import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Image,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { Audio } from 'expo-av';
import { SalesReportInput } from '../../types';
import ApiService from '../../services/api';

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  uri: string;
  name: string;
}

const CreateSalesReportScreen: React.FC = () => {
  const navigation = useNavigation();

  const [formData, setFormData] = useState<Partial<SalesReportInput>>({
    need: '',
    articleRef: '',
    comment: '',
    visitDate: new Date(),
    denomination: '',
    name: '',
    visitPurpose: '',
    complaint: '',
    city: '',
    images: [],
  });

  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [showMediaModal, setShowMediaModal] = useState(false);

  const updateFormData = (field: keyof SalesReportInput, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaLibraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    const { status: audioStatus } = await Audio.requestPermissionsAsync();

    if (cameraStatus !== 'granted' || mediaLibraryStatus !== 'granted' || audioStatus !== 'granted') {
      Alert.alert(
        'Permissions requises',
        'Cette application a besoin d\'accéder à votre caméra, galerie et microphone pour fonctionner correctement.'
      );
      return false;
    }
    return true;
  };

  const takePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const asset = result.assets[0];
      const mediaItem: MediaItem = {
        id: Date.now().toString(),
        type: 'image',
        uri: asset.uri,
        name: `photo_${Date.now()}.jpg`,
      };
      setMediaItems(prev => [...prev, mediaItem]);
      setShowMediaModal(false);
    }
  };

  const pickImage = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: true,
      quality: 0.8,
    });

    if (!result.canceled) {
      const newMediaItems = result.assets.map((asset, index) => ({
        id: `${Date.now()}_${index}`,
        type: 'image' as const,
        uri: asset.uri,
        name: `image_${Date.now()}_${index}.jpg`,
      }));
      setMediaItems(prev => [...prev, ...newMediaItems]);
      setShowMediaModal(false);
    }
  };

  const recordVideo = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Videos,
      allowsEditing: true,
      quality: ImagePicker.UIImagePickerControllerQualityType.Medium,
      videoMaxDuration: 60, // 1 minute max
    });

    if (!result.canceled && result.assets[0]) {
      const asset = result.assets[0];
      const mediaItem: MediaItem = {
        id: Date.now().toString(),
        type: 'video',
        uri: asset.uri,
        name: `video_${Date.now()}.mp4`,
      };
      setMediaItems(prev => [...prev, mediaItem]);
      setShowMediaModal(false);
    }
  };

  const startRecording = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);
      setIsRecording(true);
    } catch (err) {
      console.error('Failed to start recording', err);
      Alert.alert('Erreur', 'Impossible de démarrer l\'enregistrement audio');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    setIsRecording(false);
    await recording.stopAndUnloadAsync();
    const uri = recording.getURI();

    if (uri) {
      const mediaItem: MediaItem = {
        id: Date.now().toString(),
        type: 'audio',
        uri,
        name: `audio_${Date.now()}.m4a`,
      };
      setMediaItems(prev => [...prev, mediaItem]);
    }

    setRecording(null);
    setShowMediaModal(false);
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const mediaItem: MediaItem = {
          id: Date.now().toString(),
          type: 'document',
          uri: asset.uri,
          name: asset.name || `document_${Date.now()}.pdf`,
        };
        setMediaItems(prev => [...prev, mediaItem]);
        setShowMediaModal(false);
      }
    } catch (err) {
      console.error('Error picking document:', err);
      Alert.alert('Erreur', 'Impossible de sélectionner le document');
    }
  };

  const removeMediaItem = (id: string) => {
    setMediaItems(prev => prev.filter(item => item.id !== id));
  };

  const uploadMediaItems = async (): Promise<{ images: string[]; videoUrl?: string; audioUrl?: string; pdfUrl?: string }> => {
    const result = {
      images: [] as string[],
      videoUrl: undefined as string | undefined,
      audioUrl: undefined as string | undefined,
      pdfUrl: undefined as string | undefined,
    };

    for (const item of mediaItems) {
      try {
        const uploadResult = await ApiService.uploadFile(
          {
            uri: item.uri,
            type: item.type === 'image' ? 'image/jpeg' :
                  item.type === 'video' ? 'video/mp4' :
                  item.type === 'audio' ? 'audio/m4a' : 'application/pdf',
            name: item.name,
          },
          item.type
        );

        if (item.type === 'image') {
          result.images.push(uploadResult.url);
        } else if (item.type === 'video') {
          result.videoUrl = uploadResult.url;
        } else if (item.type === 'audio') {
          result.audioUrl = uploadResult.url;
        } else if (item.type === 'document') {
          result.pdfUrl = uploadResult.url;
        }
      } catch (error) {
        console.error(`Error uploading ${item.type}:`, error);
        // Continue with other uploads even if one fails
      }
    }

    return result;
  };

  const handleSubmit = async () => {
    // Validate required fields
    if (!formData.need || !formData.denomination || !formData.name || !formData.visitPurpose || !formData.city) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      setIsLoading(true);

      // Upload media files first
      const mediaUrls = await uploadMediaItems();

      // Create the report data
      const reportData: SalesReportInput = {
        need: formData.need!,
        articleRef: formData.articleRef,
        comment: formData.comment,
        visitDate: formData.visitDate!,
        denomination: formData.denomination!,
        name: formData.name!,
        visitPurpose: formData.visitPurpose!,
        complaint: formData.complaint,
        city: formData.city!,
        images: mediaUrls.images,
        videoUrl: mediaUrls.videoUrl,
        audioUrl: mediaUrls.audioUrl,
        pdfUrl: mediaUrls.pdfUrl,
      };

      await ApiService.createSalesReport(reportData);

      Alert.alert(
        'Succès',
        'Le rapport de visite a été créé avec succès',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          }
        ]
      );
    } catch (error) {
      console.error('Error creating sales report:', error);
      Alert.alert('Erreur', 'Impossible de créer le rapport de visite');
    } finally {
      setIsLoading(false);
    }
  };

  const renderMediaItem = ({ item }: { item: MediaItem }) => (
    <View style={styles.mediaItem}>
      <View style={styles.mediaPreview}>
        {item.type === 'image' ? (
          <Image source={{ uri: item.uri }} style={styles.mediaImage} />
        ) : (
          <View style={styles.mediaPlaceholder}>
            <Ionicons
              name={
                item.type === 'video' ? 'videocam' :
                item.type === 'audio' ? 'mic' : 'document'
              }
              size={24}
              color="#666"
            />
          </View>
        )}
      </View>
      <Text style={styles.mediaName} numberOfLines={1}>{item.name}</Text>
      <TouchableOpacity
        style={styles.removeMediaButton}
        onPress={() => removeMediaItem(item.id)}
      >
        <Ionicons name="close-circle" size={20} color="#F44336" />
      </TouchableOpacity>
    </View>
  );

  const MediaOption: React.FC<{
    icon: keyof typeof Ionicons.glyphMap;
    title: string;
    onPress: () => void;
  }> = ({ icon, title, onPress }) => (
    <TouchableOpacity style={styles.mediaOption} onPress={onPress}>
      <Ionicons name={icon} size={32} color="#006db7" />
      <Text style={styles.mediaOptionText}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations de base</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Besoin identifié *</Text>
            <TextInput
              style={styles.input}
              value={formData.need}
              onChangeText={(text) => updateFormData('need', text)}
              placeholder="Décrivez le besoin identifié..."
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Référence article</Text>
            <TextInput
              style={styles.input}
              value={formData.articleRef}
              onChangeText={(text) => updateFormData('articleRef', text)}
              placeholder="Référence de l'article..."
            />
          </View>
        </View>

        {/* Client Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations client</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Dénomination *</Text>
            <TextInput
              style={styles.input}
              value={formData.denomination}
              onChangeText={(text) => updateFormData('denomination', text)}
              placeholder="Nom de l'entreprise..."
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Contact *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(text) => updateFormData('name', text)}
              placeholder="Nom du contact..."
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Ville *</Text>
            <TextInput
              style={styles.input}
              value={formData.city}
              onChangeText={(text) => updateFormData('city', text)}
              placeholder="Ville de la visite..."
            />
          </View>
        </View>

        {/* Visit Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Détails de la visite</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Objet de la visite *</Text>
            <TextInput
              style={styles.input}
              value={formData.visitPurpose}
              onChangeText={(text) => updateFormData('visitPurpose', text)}
              placeholder="Objet de la visite..."
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Commentaires</Text>
            <TextInput
              style={styles.input}
              value={formData.comment}
              onChangeText={(text) => updateFormData('comment', text)}
              placeholder="Commentaires additionnels..."
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Réclamation</Text>
            <TextInput
              style={styles.input}
              value={formData.complaint}
              onChangeText={(text) => updateFormData('complaint', text)}
              placeholder="Réclamation éventuelle..."
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        {/* Media Attachments */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Pièces jointes ({mediaItems.length})</Text>
            <TouchableOpacity
              style={styles.addMediaButton}
              onPress={() => setShowMediaModal(true)}
            >
              <Ionicons name="add" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          {mediaItems.length > 0 ? (
            <FlatList
              data={mediaItems}
              renderItem={renderMediaItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.mediaList}
            />
          ) : (
            <View style={styles.emptyMedia}>
              <Ionicons name="attach-outline" size={48} color="#ccc" />
              <Text style={styles.emptyMediaText}>Aucune pièce jointe</Text>
              <Text style={styles.emptyMediaSubtext}>Appuyez sur + pour ajouter des fichiers</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name="checkmark" size={20} color="#fff" />
              <Text style={styles.submitButtonText}>Créer le rapport</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Media Selection Modal */}
      <Modal
        visible={showMediaModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Ajouter une pièce jointe</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowMediaModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.mediaOptionsContainer}>
            <MediaOption
              icon="camera"
              title="Prendre une photo"
              onPress={takePhoto}
            />

            <MediaOption
              icon="images"
              title="Galerie photos"
              onPress={pickImage}
            />

            <MediaOption
              icon="videocam"
              title="Enregistrer une vidéo"
              onPress={recordVideo}
            />

            <MediaOption
              icon={isRecording ? "stop" : "mic"}
              title={isRecording ? "Arrêter l'enregistrement" : "Enregistrer audio"}
              onPress={isRecording ? stopRecording : startRecording}
            />

            <MediaOption
              icon="document"
              title="Sélectionner un document"
              onPress={pickDocument}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 8,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    textAlignVertical: 'top',
  },
  addMediaButton: {
    backgroundColor: '#006db7',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaList: {
    marginTop: 16,
  },
  mediaItem: {
    marginRight: 12,
    alignItems: 'center',
    width: 80,
  },
  mediaPreview: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 8,
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  mediaPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaName: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  removeMediaButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#fff',
    borderRadius: 10,
  },
  emptyMedia: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyMediaText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptyMediaSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  submitButton: {
    backgroundColor: '#006db7',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCloseButton: {
    padding: 8,
  },
  mediaOptionsContainer: {
    padding: 16,
  },
  mediaOption: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mediaOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 16,
  },
});

export default CreateSalesReportScreen;
