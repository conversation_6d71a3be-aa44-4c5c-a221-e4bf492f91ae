const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Set environment variable for SQLite
process.env.DATABASE_URL = 'file:./dev.db';

const prisma = new PrismaClient();

async function setupDatabase() {
  try {
    console.log('🗄️ Setting up SQLite database...');

    // Create a test admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const adminUser = await prisma.user.create({
      data: {
        id: 'admin-user-1',
        email: '<EMAIL>',
        username: 'admin',
        password: hashedPassword,
        firstname: 'Admin',
        lastname: '<PERSON><PERSON><PERSON>',
        telephone: '+33123456789',
        role: 'ADMIN',
      },
    });

    console.log('✅ Admin user created:', adminUser.email);

    // Create admin profile
    const adminProfile = await prisma.admin.create({
      data: {
        id: 'admin-profile-1',
        userId: adminUser.id,
      },
    });

    console.log('✅ Admin profile created');

    // Create a test commercial user
    const commercialUser = await prisma.user.create({
      data: {
        id: 'commercial-user-1',
        email: '<EMAIL>',
        username: 'commercial',
        password: hashedPassword,
        firstname: 'Jean',
        lastname: 'Dupont',
        telephone: '+33123456790',
        role: 'COMMERCIAL',
      },
    });

    console.log('✅ Commercial user created:', commercialUser.email);

    // Create commercial profile
    const commercialProfile = await prisma.commercial.create({
      data: {
        id: 'commercial-profile-1',
        userId: commercialUser.id,
      },
    });

    console.log('✅ Commercial profile created');

    // Create a test client user
    const clientUser = await prisma.user.create({
      data: {
        id: 'client-user-1',
        email: '<EMAIL>',
        username: 'client',
        password: hashedPassword,
        firstname: 'Marie',
        lastname: 'Martin',
        telephone: '+33123456791',
        role: 'CLIENT',
      },
    });

    console.log('✅ Client user created:', clientUser.email);

    // Create client profile
    const clientProfile = await prisma.client.create({
      data: {
        id: 'client-profile-1',
        userId: clientUser.id,
        company_name: 'Entreprise Martin',
      },
    });

    console.log('✅ Client profile created');

    // Create some test categories
    const categories = await Promise.all([
      prisma.category.create({
        data: {
          id: 'cat-1',
          name: 'Éclairage LED',
          description: 'Solutions d\'éclairage LED pour tous types d\'applications',
        },
      }),
      prisma.category.create({
        data: {
          id: 'cat-2',
          name: 'Panneaux Électriques',
          description: 'Panneaux et armoires électriques industriels',
        },
      }),
      prisma.category.create({
        data: {
          id: 'cat-3',
          name: 'Câblage',
          description: 'Câbles et accessoires de câblage électrique',
        },
      }),
    ]);

    console.log('✅ Categories created:', categories.length);

    // Create some test brands
    const brands = await Promise.all([
      prisma.brand.create({
        data: {
          id: 'brand-1',
          name: 'Schneider Electric',
        },
      }),
      prisma.brand.create({
        data: {
          id: 'brand-2',
          name: 'Legrand',
        },
      }),
      prisma.brand.create({
        data: {
          id: 'brand-3',
          name: 'ABB',
        },
      }),
    ]);

    console.log('✅ Brands created:', brands.length);

    // Create some test products
    const products = await Promise.all([
      prisma.product.create({
        data: {
          id: 'prod-1',
          reference: 'LED-001',
          name: 'Ampoule LED 10W',
          description: 'Ampoule LED haute efficacité 10W, blanc chaud',
          characteristics: 'Puissance: 10W, Température: 3000K, Durée de vie: 25000h',
          categoryId: categories[0].id,
          brandId: brands[0].id,
        },
      }),
      prisma.product.create({
        data: {
          id: 'prod-2',
          reference: 'PANEL-001',
          name: 'Panneau Électrique 12 modules',
          description: 'Panneau électrique modulaire 12 modules avec porte',
          characteristics: 'Modules: 12, Protection: IP40, Matériau: Plastique ABS',
          categoryId: categories[1].id,
          brandId: brands[1].id,
        },
      }),
      prisma.product.create({
        data: {
          id: 'prod-3',
          reference: 'CABLE-001',
          name: 'Câble H07V-U 2.5mm²',
          description: 'Câble rigide H07V-U section 2.5mm² bleu',
          characteristics: 'Section: 2.5mm², Type: H07V-U, Couleur: Bleu, Longueur: 100m',
          categoryId: categories[2].id,
          brandId: brands[2].id,
        },
      }),
    ]);

    console.log('✅ Products created:', products.length);

    // Create a test quote
    const quote = await prisma.quote.create({
      data: {
        id: 'quote-1',
        quoteNumber: 'DEV-2024-001',
        clientId: clientProfile.id,
        status: 'PENDING',
        totalAmount: 150.50,
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        notes: 'Devis de test pour développement',
        createdByAdminId: adminProfile.id,
      },
    });

    console.log('✅ Quote created:', quote.quoteNumber);

    // Create quote items
    const quoteItems = await Promise.all([
      prisma.quoteitem.create({
        data: {
          id: 'qi-1',
          quoteId: quote.id,
          productId: products[0].id,
          quantity: 5,
          unitPrice: 15.50,
        },
      }),
      prisma.quoteitem.create({
        data: {
          id: 'qi-2',
          quoteId: quote.id,
          productId: products[1].id,
          quantity: 2,
          unitPrice: 35.00,
        },
      }),
    ]);

    console.log('✅ Quote items created:', quoteItems.length);

    // Create a test sales report
    const salesReport = await prisma.salesreport.create({
      data: {
        id: 'sr-1',
        commercialId: commercialProfile.id,
        need: 'Client recherche des solutions d\'éclairage LED pour son entrepôt',
        articleRef: 'LED-001',
        comment: 'Client très intéressé, demande un devis détaillé',
        visitDate: new Date(),
        denomination: 'Entreprise Martin',
        name: 'Marie Martin',
        visitPurpose: 'Présentation des solutions LED et prise de mesures',
        complaint: null,
        city: 'Paris',
        isCompleted: true,
      },
    });

    console.log('✅ Sales report created');

    // Create a chat conversation
    const conversation = await prisma.chatconversation.create({
      data: {
        id: 'conv-1',
        adminId: adminProfile.id,
        commercialId: commercialProfile.id,
        lastMessage: 'Bonjour, j\'ai besoin d\'aide pour le devis client Martin',
        lastMessageAt: new Date(),
      },
    });

    console.log('✅ Chat conversation created');

    // Create a chat message
    const message = await prisma.chatmessage.create({
      data: {
        id: 'msg-1',
        conversationId: conversation.id,
        senderId: commercialProfile.id,
        senderType: 'COMMERCIAL',
        content: 'Bonjour, j\'ai besoin d\'aide pour le devis client Martin',
        messageType: 'TEXT',
      },
    });

    console.log('✅ Chat message created');

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Test Accounts Created:');
    console.log('👤 Admin: <EMAIL> / admin123');
    console.log('👤 Commercial: <EMAIL> / admin123');
    console.log('👤 Client: <EMAIL> / admin123');
    console.log('\n🗄️ Database file: ./dev.db');
    console.log('🌐 You can now start the application and login with these accounts');

  } catch (error) {
    console.error('❌ Error setting up database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

setupDatabase()
  .catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
