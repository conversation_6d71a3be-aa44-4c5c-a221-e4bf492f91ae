import { NextRequest, NextResponse } from 'next/server';
import { saveFile, saveBase64Image } from '@/lib/upload';

// POST /api/upload - Upload a file
export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File | null;
    const base64 = formData.get('base64') as string | null;
    const directory = formData.get('directory') as string || 'uploads';
    
    if (!file && !base64) {
      return NextResponse.json(
        { error: 'No file or base64 data provided' },
        { status: 400 }
      );
    }
    
    let fileUrl: string;
    
    if (file) {
      fileUrl = await saveFile(file, directory);
    } else if (base64) {
      fileUrl = saveBase64Image(base64, directory);
    } else {
      return NextResponse.json(
        { error: 'Invalid file data' },
        { status: 400 }
      );
    }
    
    return NextResponse.json({ url: fileUrl }, { status: 201 });
  } catch (error: any) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to upload file' },
      { status: 500 }
    );
  }
}
