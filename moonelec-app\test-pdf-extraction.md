# 🧪 PDF Extraction Test Guide

## ✅ Current Status: Test Mode Active

The PDF extraction is now working in **Test Mode** which means:
- ✅ **No AI required** - Works without Ollama or OpenAI
- ✅ **Simple text parsing** - Extracts basic product info
- ✅ **Ready for testing** - You can upload PDFs immediately

## 🚀 How to Test PDF Extraction

### 1. **Access the Web App**
- Go to: http://localhost:3000
- Login as ADMIN (username: `hicham.ezzamzami`, password: `123456`)

### 2. **Navigate to PDF Import**
- Go to: **Admin** → **Products** → **Import Products**
- Or directly: http://localhost:3000/admin/products/import

### 3. **Upload a PDF**
- Click "Choose File" and select any PDF with text
- Click "Extract Products"
- The system will extract basic product information

### 4. **What Test Mode Does**
- Parses text from PDF (no AI needed)
- Creates basic product entries
- Extracts simple references and names
- Adds basic characteristics

## 🔧 Switching to Real AI

### Option 1: Use Ollama (Free, Local)
1. **Install Ollama**: https://ollama.ai/download
2. **Pull a model**: `ollama pull llama3.2:3b`
3. **Disable test mode** in `.env`:
   ```env
   AI_TEST_MODE="false"
   ```
4. **Restart the server**

### Option 2: Use OpenAI (Paid)
1. **Get OpenAI API key**: https://platform.openai.com/api-keys
2. **Update `.env`**:
   ```env
   OPENAI_API_KEY="your-actual-api-key"
   AI_TEST_MODE="false"
   ```
3. **Restart the server**

## 🧪 Test Mode vs Real AI

| Feature | Test Mode | Ollama | OpenAI |
|---------|-----------|--------|--------|
| Cost | Free | Free | Paid |
| Setup | None | Install required | API key required |
| Quality | Basic | Good | Excellent |
| Speed | Fast | Medium | Fast |
| Offline | Yes | Yes | No |

## 🎯 Expected Results in Test Mode

When you upload a PDF, you should see:
- ✅ **Product Name**: First line of text found
- ✅ **Reference**: Auto-generated or extracted pattern
- ✅ **Description**: "Produit extrait automatiquement..."
- ✅ **Characteristics**: Basic metadata about extraction

## 🔍 Troubleshooting

### PDF Upload Issues
- Make sure PDF contains readable text (not just images)
- Try a simple text-based PDF first
- Check browser console for errors

### Authentication Issues
- Make sure you're logged in as ADMIN
- Try refreshing the page and logging in again

### Server Issues
- Check server logs in terminal
- Restart the server: `npm run dev`
- Check if port 3000 is available

## 🎉 Success Indicators

You'll know it's working when you see:
1. ✅ **File upload succeeds**
2. ✅ **"Extraction successful" message**
3. ✅ **Product data appears in form**
4. ✅ **Server logs show "Test Mode extraction"**

## 📝 Next Steps

Once PDF extraction is working in test mode:
1. **Test with different PDFs** to see extraction quality
2. **Install Ollama** for better AI extraction
3. **Create products** from extracted data
4. **Test mobile app** integration

The PDF extraction is now ready for testing! 🚀
