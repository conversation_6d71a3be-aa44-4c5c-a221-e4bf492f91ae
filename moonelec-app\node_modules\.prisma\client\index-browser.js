
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AdminScalarFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.BrandScalarFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image'
};

exports.Prisma.ClientScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  company_name: 'company_name'
};

exports.Prisma.CommercialScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  profile_photo: 'profile_photo'
};

exports.Prisma.CommercialclientScalarFieldEnum = {
  id: 'id',
  commercialId: 'commercialId',
  clientId: 'clientId',
  assignedAt: 'assignedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  type: 'type',
  message: 'message',
  isRead: 'isRead',
  createdAt: 'createdAt',
  adminId: 'adminId',
  quoteId: 'quoteId',
  salesReportId: 'salesReportId'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  orderNumber: 'orderNumber',
  status: 'status',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  quoteId: 'quoteId'
};

exports.Prisma.OrderitemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  reference: 'reference',
  name: 'name',
  description: 'description',
  characteristics: 'characteristics',
  mainImage: 'mainImage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  categoryId: 'categoryId',
  brandId: 'brandId'
};

exports.Prisma.ProductimageScalarFieldEnum = {
  id: 'id',
  url: 'url',
  alt: 'alt',
  order: 'order',
  productId: 'productId',
  createdAt: 'createdAt'
};

exports.Prisma.QuoteScalarFieldEnum = {
  id: 'id',
  quoteNumber: 'quoteNumber',
  clientId: 'clientId',
  status: 'status',
  totalAmount: 'totalAmount',
  validUntil: 'validUntil',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdByAdminId: 'createdByAdminId',
  pdfUrl: 'pdfUrl'
};

exports.Prisma.QuoteitemScalarFieldEnum = {
  id: 'id',
  quoteId: 'quoteId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  password: 'password',
  lastname: 'lastname',
  firstname: 'firstname',
  telephone: 'telephone',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SalesreportScalarFieldEnum = {
  id: 'id',
  commercialId: 'commercialId',
  need: 'need',
  articleRef: 'articleRef',
  comment: 'comment',
  visitDate: 'visitDate',
  denomination: 'denomination',
  images: 'images',
  name: 'name',
  visitPurpose: 'visitPurpose',
  complaint: 'complaint',
  city: 'city',
  videoUrl: 'videoUrl',
  audioUrl: 'audioUrl',
  pdfUrl: 'pdfUrl',
  submittedAt: 'submittedAt',
  lastReminder: 'lastReminder',
  isCompleted: 'isCompleted'
};

exports.Prisma.ChatconversationScalarFieldEnum = {
  id: 'id',
  adminId: 'adminId',
  commercialId: 'commercialId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastMessage: 'lastMessage',
  lastMessageAt: 'lastMessageAt',
  isActive: 'isActive'
};

exports.Prisma.ChatmessageScalarFieldEnum = {
  id: 'id',
  conversationId: 'conversationId',
  senderId: 'senderId',
  senderType: 'senderType',
  content: 'content',
  messageType: 'messageType',
  fileUrl: 'fileUrl',
  fileName: 'fileName',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.ChatfileScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  fileUrl: 'fileUrl',
  fileSize: 'fileSize',
  fileType: 'fileType',
  uploadedById: 'uploadedById',
  conversationId: 'conversationId',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.adminOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.brandOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image'
};

exports.Prisma.categoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image'
};

exports.Prisma.clientOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  company_name: 'company_name'
};

exports.Prisma.commercialOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  profile_photo: 'profile_photo'
};

exports.Prisma.commercialclientOrderByRelevanceFieldEnum = {
  id: 'id',
  commercialId: 'commercialId',
  clientId: 'clientId'
};

exports.Prisma.notificationOrderByRelevanceFieldEnum = {
  id: 'id',
  message: 'message',
  adminId: 'adminId',
  quoteId: 'quoteId',
  salesReportId: 'salesReportId'
};

exports.Prisma.orderOrderByRelevanceFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  orderNumber: 'orderNumber',
  quoteId: 'quoteId'
};

exports.Prisma.orderitemOrderByRelevanceFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId'
};

exports.Prisma.productOrderByRelevanceFieldEnum = {
  id: 'id',
  reference: 'reference',
  name: 'name',
  description: 'description',
  characteristics: 'characteristics',
  mainImage: 'mainImage',
  categoryId: 'categoryId',
  brandId: 'brandId'
};

exports.Prisma.productimageOrderByRelevanceFieldEnum = {
  id: 'id',
  url: 'url',
  alt: 'alt',
  productId: 'productId'
};

exports.Prisma.quoteOrderByRelevanceFieldEnum = {
  id: 'id',
  quoteNumber: 'quoteNumber',
  clientId: 'clientId',
  notes: 'notes',
  createdByAdminId: 'createdByAdminId',
  pdfUrl: 'pdfUrl'
};

exports.Prisma.quoteitemOrderByRelevanceFieldEnum = {
  id: 'id',
  quoteId: 'quoteId',
  productId: 'productId'
};

exports.Prisma.userOrderByRelevanceFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  password: 'password',
  lastname: 'lastname',
  firstname: 'firstname',
  telephone: 'telephone'
};

exports.Prisma.salesreportOrderByRelevanceFieldEnum = {
  id: 'id',
  commercialId: 'commercialId',
  need: 'need',
  articleRef: 'articleRef',
  comment: 'comment',
  denomination: 'denomination',
  images: 'images',
  name: 'name',
  visitPurpose: 'visitPurpose',
  complaint: 'complaint',
  city: 'city',
  videoUrl: 'videoUrl',
  audioUrl: 'audioUrl',
  pdfUrl: 'pdfUrl'
};

exports.Prisma.chatconversationOrderByRelevanceFieldEnum = {
  id: 'id',
  adminId: 'adminId',
  commercialId: 'commercialId',
  lastMessage: 'lastMessage'
};

exports.Prisma.chatmessageOrderByRelevanceFieldEnum = {
  id: 'id',
  conversationId: 'conversationId',
  senderId: 'senderId',
  content: 'content',
  fileUrl: 'fileUrl',
  fileName: 'fileName'
};

exports.Prisma.chatfileOrderByRelevanceFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  fileUrl: 'fileUrl',
  fileType: 'fileType',
  uploadedById: 'uploadedById',
  conversationId: 'conversationId'
};
exports.notification_type = exports.$Enums.notification_type = {
  QUOTE_REQUESTED: 'QUOTE_REQUESTED',
  QUOTE_APPROVED: 'QUOTE_APPROVED',
  QUOTE_REJECTED: 'QUOTE_REJECTED',
  ORDER_PLACED: 'ORDER_PLACED',
  ORDER_SHIPPED: 'ORDER_SHIPPED',
  REPORT_REMINDER: 'REPORT_REMINDER',
  REPORT_SUBMITTED: 'REPORT_SUBMITTED'
};

exports.order_status = exports.$Enums.order_status = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
};

exports.quote_status = exports.$Enums.quote_status = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED',
  CONVERTED: 'CONVERTED'
};

exports.user_role = exports.$Enums.user_role = {
  CLIENT: 'CLIENT',
  COMMERCIAL: 'COMMERCIAL',
  ADMIN: 'ADMIN'
};

exports.chat_sender_type = exports.$Enums.chat_sender_type = {
  ADMIN: 'ADMIN',
  COMMERCIAL: 'COMMERCIAL'
};

exports.chat_message_type = exports.$Enums.chat_message_type = {
  TEXT: 'TEXT',
  FILE: 'FILE',
  IMAGE: 'IMAGE'
};

exports.Prisma.ModelName = {
  admin: 'admin',
  brand: 'brand',
  category: 'category',
  client: 'client',
  commercial: 'commercial',
  commercialclient: 'commercialclient',
  notification: 'notification',
  order: 'order',
  orderitem: 'orderitem',
  product: 'product',
  productimage: 'productimage',
  quote: 'quote',
  quoteitem: 'quoteitem',
  user: 'user',
  salesreport: 'salesreport',
  chatconversation: 'chatconversation',
  chatmessage: 'chatmessage',
  chatfile: 'chatfile'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
