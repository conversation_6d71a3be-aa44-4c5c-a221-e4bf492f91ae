import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { saveFile } from '@/lib/upload';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const conversationId = formData.get('conversationId') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!conversationId) {
      return NextResponse.json({ error: 'Conversation ID required' }, { status: 400 });
    }

    console.log('📎 Chat upload - User:', { id: user.id, role: user.role });
    console.log('📎 Chat upload - Conversation ID:', conversationId);

    // Verify user has access to this conversation
    // First, get the user's admin or commercial profile ID
    let userProfileId: string | null = null;

    if (user.role === 'ADMIN') {
      const adminProfile = await prisma.admin.findUnique({
        where: { userId: user.id },
        select: { id: true }
      });
      userProfileId = adminProfile?.id || null;
      console.log('📎 Chat upload - Admin profile ID:', userProfileId);
    } else if (user.role === 'COMMERCIAL') {
      const commercialProfile = await prisma.commercial.findUnique({
        where: { userId: user.id },
        select: { id: true }
      });
      userProfileId = commercialProfile?.id || null;
      console.log('📎 Chat upload - Commercial profile ID:', userProfileId);
    }

    // Now check conversation access
    let conversation;

    if (user.role === 'ADMIN') {
      // ADMIN can access any conversation, but let's verify it exists
      conversation = await prisma.chatconversation.findUnique({
        where: { id: conversationId },
        select: { id: true, adminId: true, commercialId: true }
      });
      console.log('📎 Chat upload - ADMIN access granted to conversation:', !!conversation);
    } else {
      // COMMERCIAL users can only access conversations they're part of
      conversation = await prisma.chatconversation.findFirst({
        where: {
          id: conversationId,
          commercialId: userProfileId || 'invalid'
        }
      });
      console.log('📎 Chat upload - COMMERCIAL access check:', !!conversation);
    }

    if (!conversation) {
      // Try to find the conversation without user restriction for debugging
      const anyConversation = await prisma.chatconversation.findUnique({
        where: { id: conversationId },
        select: { id: true, adminId: true, commercialId: true }
      });

      console.log('📎 Chat upload - Any conversation:', anyConversation);
      console.log('📎 Chat upload - User access check failed for user:', user.id);

      return NextResponse.json({
        error: 'Conversation not found or access denied',
        debug: {
          conversationExists: !!anyConversation,
          userId: user.id,
          userRole: user.role,
          userProfileId: userProfileId,
          expectedAdminId: anyConversation?.adminId,
          expectedCommercialId: anyConversation?.commercialId,
          accessMethod: user.role === 'ADMIN' ? 'admin-full-access' : 'commercial-participant-only'
        }
      }, { status: 403 });
    }

    try {
      // Upload file to chat directory
      const fileUrl = await saveFile(file, 'chat');

      // Create file metadata record
      const fileRecord = await prisma.chatfile.create({
        data: {
          fileName: file.name,
          fileUrl,
          fileSize: file.size,
          fileType: file.type,
          uploadedById: user.id,
          conversationId: conversationId
        }
      });

      return NextResponse.json({
        success: true,
        file: {
          id: fileRecord.id,
          fileName: file.name,
          fileUrl,
          fileSize: file.size,
          fileType: file.type,
          uploadedAt: fileRecord.createdAt,
          uploadedBy: {
            id: user.id,
            name: user.firstname + ' ' + user.lastname
          }
        }
      });

    } catch (uploadError: any) {
      console.error('File upload error:', uploadError);
      return NextResponse.json({
        error: uploadError.message || 'Failed to upload file'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Chat file upload error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
