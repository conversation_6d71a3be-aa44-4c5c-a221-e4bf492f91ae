import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { saveFile } from '@/lib/upload';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const conversationId = formData.get('conversationId') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!conversationId) {
      return NextResponse.json({ error: 'Conversation ID required' }, { status: 400 });
    }

    // Verify user has access to this conversation
    const conversation = await prisma.chatconversation.findFirst({
      where: {
        id: conversationId,
        OR: [
          { adminId: user.id },
          { commercialId: user.id }
        ]
      }
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found or access denied' }, { status: 403 });
    }

    try {
      // Upload file to chat directory
      const fileUrl = await saveFile(file, 'chat');

      // Create file metadata record
      const fileRecord = await prisma.chatfile.create({
        data: {
          fileName: file.name,
          fileUrl,
          fileSize: file.size,
          fileType: file.type,
          uploadedById: user.id,
          conversationId: conversationId
        }
      });

      return NextResponse.json({
        success: true,
        file: {
          id: fileRecord.id,
          fileName: file.name,
          fileUrl,
          fileSize: file.size,
          fileType: file.type,
          uploadedAt: fileRecord.createdAt,
          uploadedBy: {
            id: user.id,
            name: user.firstname + ' ' + user.lastname
          }
        }
      });

    } catch (uploadError: any) {
      console.error('File upload error:', uploadError);
      return NextResponse.json({
        error: uploadError.message || 'Failed to upload file'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Chat file upload error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
