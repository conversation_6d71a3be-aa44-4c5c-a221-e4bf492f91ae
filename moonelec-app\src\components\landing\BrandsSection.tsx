'use client';

import { useRef, useState, useEffect } from 'react';
import Image from 'next/image';
// Suppression de framer-motion pour éviter les problèmes
// import { motion, useInView, AnimatePresence } from 'framer-motion';
import Section from '@/components/ui/Section';
import { FaChevronLeft, FaChevronRight, FaLightbulb } from 'react-icons/fa';
import { useMediaQuery } from '@/hooks/useMediaQuery';

// Interface pour les marques
interface Brand {
  id: string;
  name: string;
  image: string;
}

// Nombre de marques à afficher par page
const BRANDS_PER_PAGE = 6;

export default function BrandsSection() {
  const ref = useRef(null);
  // Remplacer useInView par un état simple
  const [isInView, setIsInView] = useState(false);

  // Utiliser IntersectionObserver au lieu de useInView
  useEffect(() => {
    if (ref.current && typeof IntersectionObserver !== 'undefined') {
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.2 }
      );

      observer.observe(ref.current);
      return () => observer.disconnect();
    } else {
      // Fallback si IntersectionObserver n'est pas disponible
      setIsInView(true);
    }
  }, []);
  const [currentPage, setCurrentPage] = useState(0);
  const [activeCards, setActiveCards] = useState<Record<number, boolean>>({});
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);

  // Charger les marques depuis l'API
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        // Charger les données depuis la base de données
        const response = await fetch('/api/brands');
        if (!response.ok) {
          throw new Error('Failed to fetch brands');
        }
        const data = await response.json();
        setBrands(data.brands);
        setTotalPages(Math.ceil(data.brands.length / BRANDS_PER_PAGE));
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching brands:', error);
        setIsLoading(false);
      }
    };

    fetchBrands();
  }, []);

  // Charger l'état des ampoules depuis localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem('moonelec-bulbs-state');
        if (savedState) {
          setActiveCards(JSON.parse(savedState));
        } else {
          // Si pas d'état sauvegardé, initialiser avec quelques ampoules allumées
          const initialState: Record<number, boolean> = {};
          brands.forEach((_, index) => {
            // 30% de chance d'être allumé par défaut
            initialState[index] = Math.random() > 0.7;
          });
          setActiveCards(initialState);
        }
      } catch (error) {
        console.error('Error loading bulbs state:', error);
      }
    }
  }, [brands]);

  const handlePrevPage = () => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  const toggleCard = (cardIndex: number) => {
    setActiveCards(prev => {
      const newState = {
        ...prev,
        [cardIndex]: !prev[cardIndex]
      };

      // Sauvegarder l'état dans localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('moonelec-bulbs-state', JSON.stringify(newState));
      }

      return newState;
    });
  };

  // Obtenir les marques pour la page actuelle
  const currentBrands = brands.slice(
    currentPage * BRANDS_PER_PAGE,
    (currentPage + 1) * BRANDS_PER_PAGE
  );

  if (isLoading) {
    return (
      <Section
        id="brands"
        title="Nos Marques Partenaires"
        titleHighlight="Marques"
        subtitle="Découvrez les marques de confiance avec lesquelles nous travaillons pour vous offrir des produits électriques de haute qualité."
        ref={ref}
        className="bg-black text-white"
      >
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </Section>
    );
  }

  if (brands.length === 0) {
    return (
      <Section
        id="brands"
        title="Nos Marques Partenaires"
        titleHighlight="Marques"
        subtitle="Découvrez les marques de confiance avec lesquelles nous travaillons pour vous offrir des produits électriques de haute qualité."
        ref={ref}
        className="bg-black text-white"
      >
        <div className="flex justify-center items-center py-20">
          <p className="text-gray-400">Aucune marque partenaire n'est disponible pour le moment.</p>
        </div>
      </Section>
    );
  }

  return (
    <Section
      id="brands"
      title="Nos Marques Partenaires"
      titleHighlight="Marques"
      subtitle="Découvrez les marques de confiance avec lesquelles nous travaillons pour vous offrir des produits électriques de haute qualité."
      ref={ref}
      className="bg-black text-white"
    >
      <div className="relative">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-8 py-12">
          {currentBrands.map((brand, index) => {
            const cardIndex = currentPage * BRANDS_PER_PAGE + index;
            const isActive = activeCards[cardIndex] || false;
            const isHovered = hoveredCard === cardIndex;
            const isLightOn = isActive || isHovered;

            return (
              <div
                key={`${brand.id}-${index}`}
                className="relative mt-12"
                onMouseEnter={() => setHoveredCard(cardIndex)}
                onMouseLeave={() => setHoveredCard(null)}>
                {/* Hanging Wire */}
                <div className="absolute -top-16 left-1/2 -translate-x-1/2 w-[1px] h-8 bg-gray-500 z-10"></div>

                <div className="relative">
                  {/* Lightbulb with Switch */}
                  <div
                    className={`absolute -top-8 left-1/2 -translate-x-1/2 z-20 cursor-pointer animate-fade-in hover:scale-110 transition-transform duration-300 delay-${index * 100}`}
                    onClick={() => toggleCard(cardIndex)}
                  >
                    <div
                      className={`text-4xl transform rotate-180 ${isLightOn ? 'text-yellow-400 animate-pulse' : 'text-gray-400'}`}
                      style={{
                        textShadow: isLightOn ? "0 0 10px rgba(255, 214, 0, 0.5)" : "none"
                      }}
                    >
                      <FaLightbulb />
                    </div>

                    {/* Light Beam Effect - Trapezoid with Fade Out */}
                    {isLightOn && (
                      <div
                        className="animate-light-beam"
                        style={{
                          position: 'absolute',
                          top: '100%',
                          left: '50%',
                          width: '0',
                          height: '0',
                          borderLeft: '30px solid transparent',
                          borderRight: '30px solid transparent',
                          borderTop: '0',
                          borderBottom: '180px solid rgba(255, 255, 255, 0.8)',
                          zIndex: 5,
                          transformOrigin: 'top'
                        }}
                      />
                    )}
                  </div>
                </div>

                {/* Brand Card */}
                <div
                  className={`relative flex items-center justify-center p-6 rounded-lg shadow-md h-48 border border-gray-800 overflow-hidden animate-fade-in delay-${index * 100} ${
                    isLightOn ? 'bg-white' : 'bg-black'
                  }`}
                >
                  {/* Brand Logo */}
                  <div
                    className="relative w-full h-full z-10 hover:scale-105 transition-transform duration-300"
                  >
                    <div className="w-full h-full flex items-center justify-center">
                      {brand.image ? (
                        <Image
                          src={brand.image.startsWith('/') ? brand.image : `/${brand.image}`}
                          alt={brand.name}
                          width={150}
                          height={150}
                          className="max-h-full max-w-full object-contain"
                        />
                      ) : (
                        <div className="text-center text-gray-500">
                          <p className="font-bold">{brand.name}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Navigation Controls Container */}
        <div className="mt-16 flex flex-col items-center space-y-6 relative">
          {/* Hanging Bulbs for Navigation */}
          <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 flex items-center space-x-8">
            <div className="relative">
              <div className="w-[1px] h-8 bg-gray-500"></div>
              <div className="text-yellow-400 text-2xl transform rotate-180 absolute -bottom-6 left-1/2 -translate-x-1/2">
                <FaLightbulb />
              </div>
            </div>
          </div>
          {/* Navigation Buttons - Centered */}
          <div className="flex justify-center space-x-8">
            <button
              onClick={handlePrevPage}
              className="w-12 h-12 rounded-full bg-gray-800 shadow-lg flex items-center justify-center text-white hover:bg-yellow-500 hover:text-white hover:scale-110 active:scale-95 transition-all z-10 border border-gray-700"
              aria-label="Page précédente"
            >
              <FaChevronLeft />
            </button>

            <button
              onClick={handleNextPage}
              className="w-12 h-12 rounded-full bg-gray-800 shadow-lg flex items-center justify-center text-white hover:bg-yellow-500 hover:text-white hover:scale-110 active:scale-95 transition-all z-10 border border-gray-700"
              aria-label="Page suivante"
            >
              <FaChevronRight />
            </button>
          </div>

          {/* Page Counter */}
          <div className="text-sm text-gray-400">
            Page {currentPage + 1} sur {totalPages}
          </div>

          {/* Pagination Indicators */}
          <div className="flex justify-center space-x-3">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={`w-4 h-4 rounded-full transition-all hover:scale-110 active:scale-90 ${
                  index === currentPage
                    ? 'bg-yellow-400 scale-125'
                    : 'bg-gray-700 hover:bg-yellow-400/50'
                }`}
                aria-label={`Aller à la page ${index + 1}`}
              />
            ))}
          </div>

          {/* Turn On/Off All Button */}
          <div className="mt-2">
            <button
              onClick={() => {
                const allCardIndices = currentBrands.map((_, idx) => currentPage * BRANDS_PER_PAGE + idx);
                const allOn = allCardIndices.every(idx => activeCards[idx]);

                const newState = {...activeCards};
                allCardIndices.forEach(idx => {
                  newState[idx] = !allOn;
                });

                setActiveCards(newState);

                // Sauvegarder l'état dans localStorage
                if (typeof window !== 'undefined') {
                  localStorage.setItem('moonelec-bulbs-state', JSON.stringify(newState));
                }
              }}
              className="px-6 py-3 bg-gray-800 text-white rounded-full hover:bg-yellow-500 hover:scale-105 active:scale-95 transition-all"
            >
              {currentBrands.every((_, idx) => activeCards[currentPage * BRANDS_PER_PAGE + idx])
                ? "Éteindre toutes les ampoules"
                : "Allumer toutes les ampoules"}
            </button>
          </div>
        </div>
      </div>
    </Section>
  );
}
