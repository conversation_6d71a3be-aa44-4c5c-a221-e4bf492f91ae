{"version": 3, "file": "ExpoMediaLibrary.web.js", "sourceRoot": "", "sources": ["../src/ExpoMediaLibrary.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAIzE,MAAM,oBAAoB,GAAuB;IAC/C,MAAM,EAAE,gBAAgB,CAAC,YAAY;IACrC,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,KAAK;IACd,OAAO,EAAE,OAAO;CACjB,CAAC;AAEF,eAAe;IACb,IAAI,oBAAoB;QACtB,OAAO,uBAAuB,CAAC;IACjC,CAAC;IACD,IAAI,SAAS;QACX,OAAO;YACL,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IACD,IAAI,MAAM;QACR,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,WAAW;YACtB,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,QAAQ;YAChB,YAAY,EAAE,cAAc;YAC5B,gBAAgB,EAAE,kBAAkB;YACpC,QAAQ,EAAE,UAAU;SACrB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,UAAmB;QAC3C,OAAO,oBAAoB,CAAC;IAC9B,CAAC;IACD,KAAK,CAAC,uBAAuB,CAAC,UAAmB;QAC/C,OAAO,oBAAoB,CAAC;IAC9B,CAAC;CACF,CAAC", "sourcesContent": ["import { PermissionResponse, PermissionStatus } from 'expo-modules-core';\n\nimport { MediaTypeObject, SortByObject } from './MediaLibrary';\n\nconst noPermissionResponse: PermissionResponse = {\n  status: PermissionStatus.UNDETERMINED,\n  canAskAgain: true,\n  granted: false,\n  expires: 'never',\n};\n\nexport default {\n  get CHANGE_LISTENER_NAME(): string {\n    return 'mediaLibraryDidChange';\n  },\n  get MediaType(): MediaTypeObject {\n    return {\n      audio: 'audio',\n      photo: 'photo',\n      video: 'video',\n      unknown: 'unknown',\n    };\n  },\n  get SortBy(): SortByObject {\n    return {\n      default: 'default',\n      mediaType: 'mediaType',\n      width: 'width',\n      height: 'height',\n      creationTime: 'creationTime',\n      modificationTime: 'modificationTime',\n      duration: 'duration',\n    };\n  },\n\n  async getPermissionsAsync(_writeOnly: boolean): Promise<PermissionResponse> {\n    return noPermissionResponse;\n  },\n  async requestPermissionsAsync(_writeOnly: boolean): Promise<PermissionResponse> {\n    return noPermissionResponse;\n  },\n};\n"]}