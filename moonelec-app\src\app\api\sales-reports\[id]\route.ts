import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { 
  getSalesReportById, 
  updateSalesReport, 
  deleteSalesReport 
} from '@/lib/salesReports';
import { extractId } from '@/lib/route-utils';

// GET /api/sales-reports/[id] - Get a sales report by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins and commercials can access sales reports
    if (session.user.role !== 'ADMIN' && session.user.role !== 'COMMERCIAL') {
      return NextResponse.json(
        { error: 'Only administrators and sales team can access sales reports' },
        { status: 403 }
      );
    }

    const id = extractId(params.id);
    const report = await getSalesReportById(id);

    if (!report) {
      return NextResponse.json(
        { error: 'Sales report not found' },
        { status: 404 }
      );
    }

    // If the user is a commercial, they can only access their own reports
    if (
      session.user.role === 'COMMERCIAL' &&
      report.commercialId !== session.user.commercialId
    ) {
      return NextResponse.json(
        { error: 'You can only access your own sales reports' },
        { status: 403 }
      );
    }

    return NextResponse.json({ report });
  } catch (error: any) {
    console.error('Error fetching sales report:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch sales report' },
      { status: 500 }
    );
  }
}

// PATCH /api/sales-reports/[id] - Update a sales report
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = extractId(params.id);
    const report = await getSalesReportById(id);

    if (!report) {
      return NextResponse.json(
        { error: 'Sales report not found' },
        { status: 404 }
      );
    }

    // Only the commercial who created the report or an admin can update it
    if (
      session.user.role !== 'ADMIN' &&
      (session.user.role !== 'COMMERCIAL' || report.commercialId !== session.user.commercialId)
    ) {
      return NextResponse.json(
        { error: 'You can only update your own sales reports' },
        { status: 403 }
      );
    }

    const data = await request.json();
    const updatedReport = await updateSalesReport(id, data);

    return NextResponse.json({ success: true, report: updatedReport });
  } catch (error: any) {
    console.error('Error updating sales report:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update sales report' },
      { status: 500 }
    );
  }
}

// DELETE /api/sales-reports/[id] - Delete a sales report
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = extractId(params.id);
    const report = await getSalesReportById(id);

    if (!report) {
      return NextResponse.json(
        { error: 'Sales report not found' },
        { status: 404 }
      );
    }

    // Only the commercial who created the report or an admin can delete it
    if (
      session.user.role !== 'ADMIN' &&
      (session.user.role !== 'COMMERCIAL' || report.commercialId !== session.user.commercialId)
    ) {
      return NextResponse.json(
        { error: 'You can only delete your own sales reports' },
        { status: 403 }
      );
    }

    await deleteSalesReport(id);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting sales report:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete sales report' },
      { status: 500 }
    );
  }
}
