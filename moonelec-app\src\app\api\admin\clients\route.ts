import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// GET /api/admin/clients - Get all clients with filtering and pagination
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = req.nextUrl.searchParams;
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const search = searchParams.get('search') || '';
    const city = searchParams.get('city') || '';
    const company = searchParams.get('company') || '';

    // Build where clause
    const where: any = {
      user: {
        role: 'CLIENT',
      },
    };

    // Add search filter
    if (search) {
      where.OR = [
        {
          user: {
            firstname: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            lastname: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          company: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Add city filter
    if (city) {
      where.city = {
        contains: city,
        mode: 'insensitive',
      };
    }

    // Add company filter
    if (company) {
      where.company = {
        contains: company,
        mode: 'insensitive',
      };
    }

    // Get clients with pagination
    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
              telephone: true,
              createdAt: true,
            },
          },
        },
        skip,
        take,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.client.count({ where }),
    ]);

    return NextResponse.json({
      clients,
      total,
      page: Math.floor(skip / take) + 1,
      totalPages: Math.ceil(total / take),
    });
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/clients - Create a new client
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const {
      firstname,
      lastname,
      email,
      telephone,
      password,
      company,
      address,
      city,
      postalCode,
      country,
    } = body;

    // Validate required fields
    if (!firstname || !lastname || !email || !password) {
      return NextResponse.json(
        { message: 'Les champs prénom, nom, email et mot de passe sont requis' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: 'Un utilisateur avec cet email existe déjà' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user and client in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          id: uuidv4(),
          firstname,
          lastname,
          email,
          telephone: telephone || null,
          password: hashedPassword,
          role: 'CLIENT',
        },
      });

      // Create client profile
      const client = await tx.client.create({
        data: {
          id: uuidv4(),
          userId: user.id,
          company: company || null,
          address: address || null,
          city: city || null,
          postalCode: postalCode || null,
          country: country || 'France',
        },
        include: {
          user: {
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
              telephone: true,
              createdAt: true,
            },
          },
        },
      });

      return client;
    });

    return NextResponse.json({
      message: 'Client créé avec succès',
      client: result,
    });
  } catch (error) {
    console.error('Error creating client:', error);
    return NextResponse.json(
      { message: 'Erreur lors de la création du client' },
      { status: 500 }
    );
  }
}
