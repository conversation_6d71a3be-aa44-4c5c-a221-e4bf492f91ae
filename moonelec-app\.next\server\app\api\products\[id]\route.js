/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/[id]/route";
exports.ids = ["app/api/products/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/[id]/route.ts */ \"(rsc)/./src/app/api/products/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/[id]/route\",\n        pathname: \"/api/products/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\products\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/products/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_products__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/products */ \"(rsc)/./src/lib/products.ts\");\n\n\n// GET /api/products/[id] - Get a single product by ID\nasync function GET(req, { params }) {\n    try {\n        const product = await (0,_lib_products__WEBPACK_IMPORTED_MODULE_1__.getProductById)(params.id);\n        if (!product) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Product not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(product);\n    } catch (error) {\n        console.error('Error fetching product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch product'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/products/[id] - Update a product\nasync function PATCH(req, { params }) {\n    try {\n        const body = await req.json();\n        const { reference, name, description, characteristics, mainImage, categoryId, brandId } = body;\n        // Validate that at least one field is provided\n        if (!reference && !name && !description && !characteristics && !mainImage && categoryId === undefined && brandId === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'At least one field must be provided for update'\n            }, {\n                status: 400\n            });\n        }\n        // Update the product\n        const product = await (0,_lib_products__WEBPACK_IMPORTED_MODULE_1__.updateProduct)(params.id, {\n            reference,\n            name,\n            description,\n            characteristics,\n            mainImage,\n            categoryId,\n            brandId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(product);\n    } catch (error) {\n        console.error('Error updating product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to update product'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/products/[id] - Delete a product\nasync function DELETE(req, { params }) {\n    try {\n        await (0,_lib_products__WEBPACK_IMPORTED_MODULE_1__.deleteProduct)(params.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to delete product'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/products.ts":
/*!*****************************!*\
  !*** ./src/lib/products.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addProductImages: () => (/* binding */ addProductImages),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteAllProductImages: () => (/* binding */ deleteAllProductImages),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   deleteProductImage: () => (/* binding */ deleteProductImage),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductByReference: () => (/* binding */ getProductByReference),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct),\n/* harmony export */   updateProductImage: () => (/* binding */ updateProductImage)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// Get all products with optional filtering\nasync function getProducts(options) {\n    const { categoryId, brandId, search, skip = 0, take = 50 } = options || {};\n    const where = {\n        ...categoryId ? {\n            categoryId\n        } : {},\n        ...brandId ? {\n            brandId\n        } : {},\n        ...search ? {\n            OR: [\n                {\n                    name: {\n                        contains: search\n                    }\n                },\n                {\n                    reference: {\n                        contains: search\n                    }\n                },\n                {\n                    description: {\n                        contains: search\n                    }\n                }\n            ]\n        } : {}\n    };\n    const [rawProducts, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findMany({\n            where,\n            include: {\n                category: true,\n                brand: true,\n                productimage: {\n                    orderBy: {\n                        order: 'asc'\n                    }\n                }\n            },\n            skip,\n            take,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.count({\n            where\n        })\n    ]);\n    // Parse characteristics for all products\n    const products = rawProducts.map((product)=>{\n        let parsedCharacteristics = {};\n        try {\n            parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};\n        } catch (error) {\n            console.error('Error parsing characteristics for product', product.id, ':', error);\n            parsedCharacteristics = {};\n        }\n        return {\n            ...product,\n            characteristics: parsedCharacteristics\n        };\n    });\n    return {\n        products,\n        total\n    };\n}\n// Get a single product by ID\nasync function getProductById(id) {\n    const product = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    // Parse characteristics JSON string back to object\n    let parsedCharacteristics = {};\n    try {\n        parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};\n    } catch (error) {\n        console.error('Error parsing characteristics for product', id, ':', error);\n        parsedCharacteristics = {};\n    }\n    return {\n        ...product,\n        characteristics: parsedCharacteristics\n    };\n}\n// Get a single product by reference\nasync function getProductByReference(reference) {\n    const product = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findUnique({\n        where: {\n            reference\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    // Parse characteristics JSON string back to object\n    let parsedCharacteristics = {};\n    try {\n        parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};\n    } catch (error) {\n        console.error('Error parsing characteristics for product', reference, ':', error);\n        parsedCharacteristics = {};\n    }\n    return {\n        ...product,\n        characteristics: parsedCharacteristics\n    };\n}\n// Create a new product\nasync function createProduct(data) {\n    const { images, ...productData } = data;\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // Check if reference already exists\n        const existingProduct = await tx.product.findUnique({\n            where: {\n                reference: productData.reference\n            }\n        });\n        if (existingProduct) {\n            throw new Error(`Un produit avec la référence ${productData.reference} existe déjà`);\n        }\n        // Create the product\n        const product = await tx.product.create({\n            data: {\n                id: crypto.randomUUID(),\n                reference: productData.reference,\n                name: productData.name,\n                description: productData.description,\n                characteristics: JSON.stringify(productData.characteristics),\n                mainImage: productData.mainImage,\n                categoryId: productData.categoryId,\n                brandId: productData.brandId,\n                updatedAt: new Date()\n            }\n        });\n        // Add images if provided\n        if (images && images.length > 0) {\n            await tx.productimage.createMany({\n                data: images.map((image, index)=>({\n                        id: crypto.randomUUID(),\n                        url: image.url,\n                        alt: image.alt || product.name,\n                        order: image.order || index,\n                        productId: product.id\n                    }))\n            });\n        }\n        return product;\n    });\n}\n// Update an existing product\nasync function updateProduct(id, data) {\n    // If reference is being updated, check if it already exists\n    if (data.reference) {\n        const existingProduct = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findFirst({\n            where: {\n                reference: data.reference,\n                id: {\n                    not: id\n                }\n            }\n        });\n        if (existingProduct) {\n            throw new Error(`Un produit avec la référence ${data.reference} existe déjà`);\n        }\n    }\n    const updatedProduct = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.update({\n        where: {\n            id\n        },\n        data: {\n            ...data,\n            characteristics: data.characteristics ? JSON.stringify(data.characteristics) : undefined,\n            updatedAt: new Date()\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n    // Parse characteristics JSON string back to object\n    let parsedCharacteristics = {};\n    try {\n        parsedCharacteristics = updatedProduct.characteristics ? JSON.parse(updatedProduct.characteristics) : {};\n    } catch (error) {\n        console.error('Error parsing characteristics for updated product', id, ':', error);\n        parsedCharacteristics = {};\n    }\n    return {\n        ...updatedProduct,\n        characteristics: parsedCharacteristics\n    };\n}\n// Delete a product\nasync function deleteProduct(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // 1. Supprimer d'abord les références dans QuoteItem\n        await tx.quoteItem.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 2. Supprimer les références dans OrderItem\n        await tx.orderItem.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 3. Supprimer les images du produit\n        await tx.productimage.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 4. Enfin, supprimer le produit lui-même\n        return tx.product.delete({\n            where: {\n                id\n            }\n        });\n    });\n}\n// Add images to a product\nasync function addProductImages(productId, images) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.createMany({\n        data: images.map((image, index)=>({\n                id: crypto.randomUUID(),\n                url: image.url,\n                alt: image.alt,\n                order: image.order || index,\n                productId\n            }))\n    });\n}\n// Update a product image\nasync function updateProductImage(id, data) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.update({\n        where: {\n            id\n        },\n        data\n    });\n}\n// Delete a product image\nasync function deleteProductImage(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.delete({\n        where: {\n            id\n        }\n    });\n}\n// Delete all images for a product\nasync function deleteAllProductImages(productId) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.deleteMany({\n        where: {\n            productId\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/products.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();