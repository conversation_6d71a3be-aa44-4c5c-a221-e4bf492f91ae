/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/[id]/route";
exports.ids = ["app/api/products/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/[id]/route.ts */ \"(rsc)/./src/app/api/products/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/[id]/route\",\n        pathname: \"/api/products/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\products\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZwcm9kdWN0cyUyRiU1QmlkJTVEJTJGcm91dGUmcGFnZT0lMkZhcGklMkZwcm9kdWN0cyUyRiU1QmlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGcHJvZHVjdHMlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNBc3VzJTVDRGVza3RvcCU1Q1Byb2plY3RzJTVDTW9vbmVsZWNBcHAlNUNtb29uZWxlYy1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q0FzdXMlNUNEZXNrdG9wJTVDUHJvamVjdHMlNUNNb29uZWxlY0FwcCU1Q21vb25lbGVjLWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDc0Q7QUFDbkk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXEFzdXNcXFxcRGVza3RvcFxcXFxQcm9qZWN0c1xcXFxNb29uZWxlY0FwcFxcXFxtb29uZWxlYy1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxccHJvZHVjdHNcXFxcW2lkXVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvcHJvZHVjdHMvW2lkXS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3Byb2R1Y3RzL1tpZF1cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3Byb2R1Y3RzL1tpZF0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxBc3VzXFxcXERlc2t0b3BcXFxcUHJvamVjdHNcXFxcTW9vbmVsZWNBcHBcXFxcbW9vbmVsZWMtYXBwXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHByb2R1Y3RzXFxcXFtpZF1cXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/products/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_products__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/products */ \"(rsc)/./src/lib/products.ts\");\n\n\n// GET /api/products/[id] - Get a single product by ID\nasync function GET(req, { params }) {\n    try {\n        const product = await (0,_lib_products__WEBPACK_IMPORTED_MODULE_1__.getProductById)(params.id);\n        if (!product) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Product not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(product);\n    } catch (error) {\n        console.error('Error fetching product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch product'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/products/[id] - Update a product\nasync function PATCH(req, { params }) {\n    try {\n        const body = await req.json();\n        const { reference, name, description, characteristics, mainImage, categoryId, brandId } = body;\n        // Validate that at least one field is provided\n        if (!reference && !name && !description && !characteristics && !mainImage && categoryId === undefined && brandId === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'At least one field must be provided for update'\n            }, {\n                status: 400\n            });\n        }\n        // Update the product\n        const product = await (0,_lib_products__WEBPACK_IMPORTED_MODULE_1__.updateProduct)(params.id, {\n            reference,\n            name,\n            description,\n            characteristics,\n            mainImage,\n            categoryId,\n            brandId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(product);\n    } catch (error) {\n        console.error('Error updating product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to update product'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/products/[id] - Delete a product\nasync function DELETE(req, { params }) {\n    try {\n        await (0,_lib_products__WEBPACK_IMPORTED_MODULE_1__.deleteProduct)(params.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to delete product'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/products.ts":
/*!*****************************!*\
  !*** ./src/lib/products.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addProductImages: () => (/* binding */ addProductImages),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteAllProductImages: () => (/* binding */ deleteAllProductImages),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   deleteProductImage: () => (/* binding */ deleteProductImage),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductByReference: () => (/* binding */ getProductByReference),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct),\n/* harmony export */   updateProductImage: () => (/* binding */ updateProductImage)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// Get all products with optional filtering\nasync function getProducts(options) {\n    const { categoryId, brandId, search, skip = 0, take = 50 } = options || {};\n    const where = {\n        ...categoryId ? {\n            categoryId\n        } : {},\n        ...brandId ? {\n            brandId\n        } : {},\n        ...search ? {\n            OR: [\n                {\n                    name: {\n                        contains: search\n                    }\n                },\n                {\n                    reference: {\n                        contains: search\n                    }\n                },\n                {\n                    description: {\n                        contains: search\n                    }\n                }\n            ]\n        } : {}\n    };\n    const [products, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findMany({\n            where,\n            include: {\n                category: true,\n                brand: true,\n                productimage: {\n                    orderBy: {\n                        order: 'asc'\n                    }\n                }\n            },\n            skip,\n            take,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.count({\n            where\n        })\n    ]);\n    return {\n        products,\n        total\n    };\n}\n// Get a single product by ID\nasync function getProductById(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n}\n// Get a single product by reference\nasync function getProductByReference(reference) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findUnique({\n        where: {\n            reference\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n}\n// Create a new product\nasync function createProduct(data) {\n    const { images, ...productData } = data;\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // Check if reference already exists\n        const existingProduct = await tx.product.findUnique({\n            where: {\n                reference: productData.reference\n            }\n        });\n        if (existingProduct) {\n            throw new Error(`Un produit avec la référence ${productData.reference} existe déjà`);\n        }\n        // Create the product\n        const product = await tx.product.create({\n            data: {\n                id: crypto.randomUUID(),\n                reference: productData.reference,\n                name: productData.name,\n                description: productData.description,\n                characteristics: JSON.stringify(productData.characteristics),\n                mainImage: productData.mainImage,\n                categoryId: productData.categoryId,\n                brandId: productData.brandId,\n                updatedAt: new Date()\n            }\n        });\n        // Add images if provided\n        if (images && images.length > 0) {\n            await tx.productimage.createMany({\n                data: images.map((image, index)=>({\n                        id: crypto.randomUUID(),\n                        url: image.url,\n                        alt: image.alt || product.name,\n                        order: image.order || index,\n                        productId: product.id\n                    }))\n            });\n        }\n        return product;\n    });\n}\n// Update an existing product\nasync function updateProduct(id, data) {\n    // If reference is being updated, check if it already exists\n    if (data.reference) {\n        const existingProduct = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findFirst({\n            where: {\n                reference: data.reference,\n                id: {\n                    not: id\n                }\n            }\n        });\n        if (existingProduct) {\n            throw new Error(`Un produit avec la référence ${data.reference} existe déjà`);\n        }\n    }\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.update({\n        where: {\n            id\n        },\n        data: {\n            ...data,\n            characteristics: data.characteristics ? JSON.stringify(data.characteristics) : undefined,\n            updatedAt: new Date()\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n}\n// Delete a product\nasync function deleteProduct(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // 1. Supprimer d'abord les références dans QuoteItem\n        await tx.quoteItem.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 2. Supprimer les références dans OrderItem\n        await tx.orderItem.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 3. Supprimer les images du produit\n        await tx.productimage.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 4. Enfin, supprimer le produit lui-même\n        return tx.product.delete({\n            where: {\n                id\n            }\n        });\n    });\n}\n// Add images to a product\nasync function addProductImages(productId, images) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.createMany({\n        data: images.map((image, index)=>({\n                id: crypto.randomUUID(),\n                url: image.url,\n                alt: image.alt,\n                order: image.order || index,\n                productId\n            }))\n    });\n}\n// Update a product image\nasync function updateProductImage(id, data) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.update({\n        where: {\n            id\n        },\n        data\n    });\n}\n// Delete a product image\nasync function deleteProductImage(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.delete({\n        where: {\n            id\n        }\n    });\n}\n// Delete all images for a product\nasync function deleteAllProductImages(productId) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.deleteMany({\n        where: {\n            productId\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/products.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();