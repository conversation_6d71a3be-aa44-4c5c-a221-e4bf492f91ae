'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';
import { HomeIcon, BuildingIcon, FactoryIcon, ToolsIcon, LightbulbIcon } from '@/components/icons/ElectricalIcons';

export default function ServicesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });

  const services = [
    {
      icon: <HomeIcon size={28} />,
      title: "Matériel Résidentiel",
      description: "Solutions complètes pour les installations électriques résidentielles, de l'éclairage aux systèmes de sécurité.",
      color: "var(--primary)"
    },
    {
      icon: <BuildingIcon size={28} />,
      title: "Matériel Tertiaire",
      description: "Équipements adaptés aux besoins des bureaux, commerces et bâtiments publics avec une attention particulière à l'efficacité énergétique.",
      color: "var(--secondary)"
    },
    {
      icon: <FactoryIcon size={28} />,
      title: "Matériel Industriel",
      description: "Solutions robustes et fiables pour les environnements industriels exigeants, conformes aux normes les plus strictes.",
      color: "var(--primary)"
    },
    {
      icon: <ToolsIcon size={28} />,
      title: "Conseil Technique",
      description: "Accompagnement personnalisé par nos experts pour vous aider à choisir les solutions les plus adaptées à vos projets.",
      color: "var(--secondary)"
    },
    {
      icon: <LightbulbIcon size={28} />,
      title: "Solutions Innovantes",
      description: "Produits à la pointe de la technologie pour créer les espaces connectés de demain, économes en énergie et intelligents.",
      color: "var(--primary)"
    }
  ];

  return (
    <Section
      id="services"
      title="Nos Services"
      titleHighlight="Services"
      subtitle="Découvrez notre gamme complète de services et solutions pour tous vos besoins en matériel électrique, du résidentiel à l'industriel."
      ref={ref}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {services.map((service, index) => (
          <ServiceCard
            key={index}
            icon={service.icon}
            title={service.title}
            description={service.description}
            color={service.color}
            index={index}
            isInView={isInView}
          />
        ))}
      </div>
    </Section>
  );
}

function ServiceCard({
  icon,
  title,
  description,
  color,
  index,
  isInView
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: string;
  index: number;
  isInView: boolean;
}) {
  return (
    <Card
      animate
      delay={0.1 * index}
      hoverEffect
      className="overflow-hidden"
    >
      <div className="p-6">
        <div
          className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl mb-6"
          style={{ backgroundColor: color }}
        >
          {icon}
        </div>
        <h3 className="text-xl font-semibold mb-3 text-text-primary">{title}</h3>
        <p className="text-text-secondary">{description}</p>
      </div>
      <div className="h-2" style={{ backgroundColor: color }}></div>
    </Card>
  );
}
