import { getSession } from 'next-auth/react';
import { handleError, handleApiError, handleLoading, dismissLoading } from './error-handler';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

class ApiService {
  private async getAuthHeaders() {
    const session = await getSession();
    return {
      'Content-Type': 'application/json',
      ...(session?.accessToken && {
        Authorization: `Bearer ${session.accessToken}`,
      }),
    };
  }

  async get(endpoint: string, params?: Record<string, any>, showLoading = false) {
    const loadingToast = showLoading ? handleLoading('Chargement des données...') : null;
    
    try {
      const url = new URL(`${API_BASE_URL}${endpoint}`);
      if (params) {
        Object.keys(params).forEach(key => {
          if (params[key] !== undefined && params[key] !== null) {
            url.searchParams.append(key, params[key].toString());
          }
        });
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: await this.getAuthHeaders(),
      });

      if (loadingToast) dismissLoading(loadingToast);

      if (!response.ok) {
        await handleApiError(response, `Récupération des données (${endpoint})`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (loadingToast) dismissLoading(loadingToast);
      handleError(error, `Récupération des données (${endpoint})`);
      throw error;
    }
  }

  async post(endpoint: string, data?: any, showLoading = false) {
    const loadingToast = showLoading ? handleLoading('Enregistrement en cours...') : null;
    
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (loadingToast) dismissLoading(loadingToast);

      if (!response.ok) {
        await handleApiError(response, `Création (${endpoint})`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (loadingToast) dismissLoading(loadingToast);
      handleError(error, `Création (${endpoint})`);
      throw error;
    }
  }

  async put(endpoint: string, data?: any, showLoading = false) {
    const loadingToast = showLoading ? handleLoading('Mise à jour en cours...') : null;
    
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'PUT',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (loadingToast) dismissLoading(loadingToast);

      if (!response.ok) {
        await handleApiError(response, `Mise à jour (${endpoint})`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (loadingToast) dismissLoading(loadingToast);
      handleError(error, `Mise à jour (${endpoint})`);
      throw error;
    }
  }

  async delete(endpoint: string, showLoading = false) {
    const loadingToast = showLoading ? handleLoading('Suppression en cours...') : null;
    
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'DELETE',
        headers: await this.getAuthHeaders(),
      });

      if (loadingToast) dismissLoading(loadingToast);

      if (!response.ok) {
        await handleApiError(response, `Suppression (${endpoint})`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (loadingToast) dismissLoading(loadingToast);
      handleError(error, `Suppression (${endpoint})`);
      throw error;
    }
  }

  async uploadFile(endpoint: string, file: File, additionalData?: Record<string, any>, showLoading = true) {
    const loadingToast = showLoading ? handleLoading('Téléchargement du fichier...') : null;
    
    try {
      const session = await getSession();
      const formData = new FormData();
      formData.append('file', file);

      if (additionalData) {
        Object.keys(additionalData).forEach(key => {
          formData.append(key, additionalData[key]);
        });
      }

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          ...(session?.accessToken && {
            Authorization: `Bearer ${session.accessToken}`,
          }),
        },
        body: formData,
      });

      if (loadingToast) dismissLoading(loadingToast);

      if (!response.ok) {
        await handleApiError(response, `Téléchargement de fichier (${endpoint})`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (loadingToast) dismissLoading(loadingToast);
      handleError(error, `Téléchargement de fichier (${endpoint})`);
      throw error;
    }
  }

  // Patch method for partial updates
  async patch(endpoint: string, data?: any, showLoading = false) {
    const loadingToast = showLoading ? handleLoading('Modification en cours...') : null;
    
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'PATCH',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (loadingToast) dismissLoading(loadingToast);

      if (!response.ok) {
        await handleApiError(response, `Modification (${endpoint})`);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      if (loadingToast) dismissLoading(loadingToast);
      handleError(error, `Modification (${endpoint})`);
      throw error;
    }
  }
}

export default new ApiService();
