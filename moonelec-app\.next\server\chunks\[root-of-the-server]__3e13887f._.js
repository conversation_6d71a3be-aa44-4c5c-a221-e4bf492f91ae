module.exports = {

"[project]/.next-internal/server/app/api/quotes/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatDate": (()=>formatDate),
    "formatPrice": (()=>formatPrice),
    "generateOrderNumber": (()=>generateOrderNumber),
    "generateQuoteNumber": (()=>generateQuoteNumber),
    "getDefaultQuoteExpiryDate": (()=>getDefaultQuoteExpiryDate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
async function generateQuoteNumber() {
    const currentYear = new Date().getFullYear();
    // Trouver le dernier devis de l'année en cours
    const lastQuote = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findFirst({
        where: {
            quoteNumber: {
                startsWith: `Q-${currentYear}-`
            }
        },
        orderBy: {
            quoteNumber: 'desc'
        }
    });
    let sequentialNumber = 1;
    if (lastQuote) {
        // Extraire le numéro séquentiel du dernier devis
        const match = lastQuote.quoteNumber.match(/Q-\d{4}-(\d+)/);
        if (match && match[1]) {
            sequentialNumber = parseInt(match[1], 10) + 1;
        }
    }
    // Formater le numéro séquentiel avec des zéros en préfixe
    const formattedNumber = sequentialNumber.toString().padStart(4, '0');
    return `Q-${currentYear}-${formattedNumber}`;
}
async function generateOrderNumber() {
    const currentYear = new Date().getFullYear();
    // Trouver la dernière commande de l'année en cours
    const lastOrder = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.findFirst({
        where: {
            orderNumber: {
                startsWith: `O-${currentYear}-`
            }
        },
        orderBy: {
            orderNumber: 'desc'
        }
    });
    let sequentialNumber = 1;
    if (lastOrder) {
        // Extraire le numéro séquentiel de la dernière commande
        const match = lastOrder.orderNumber.match(/O-\d{4}-(\d+)/);
        if (match && match[1]) {
            sequentialNumber = parseInt(match[1], 10) + 1;
        }
    }
    // Formater le numéro séquentiel avec des zéros en préfixe
    const formattedNumber = sequentialNumber.toString().padStart(4, '0');
    return `O-${currentYear}-${formattedNumber}`;
}
function formatPrice(price) {
    return new Intl.NumberFormat('fr-MA', {
        style: 'currency',
        currency: 'MAD',
        minimumFractionDigits: 2
    }).format(price);
}
function formatDate(date) {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('fr-MA', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    }).format(dateObj);
}
function getDefaultQuoteExpiryDate() {
    const date = new Date();
    date.setDate(date.getDate() + 30);
    return date;
}
}}),
"[project]/src/lib/notifications.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createNotificationForAdmin": (()=>createNotificationForAdmin),
    "createNotificationForAllAdmins": (()=>createNotificationForAllAdmins),
    "getUnreadNotificationsForAdmin": (()=>getUnreadNotificationsForAdmin),
    "markAllNotificationsAsRead": (()=>markAllNotificationsAsRead),
    "markNotificationAsRead": (()=>markNotificationAsRead)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
async function createNotificationForAllAdmins(type, message, quoteId) {
    try {
        // Récupérer tous les administrateurs
        const admins = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].admin.findMany();
        // Créer une notification pour chaque administrateur
        const notifications = await Promise.all(admins.map((admin)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].notification.create({
                data: {
                    type,
                    message,
                    adminId: admin.id,
                    ...quoteId && {
                        quoteId
                    }
                }
            })));
        // Dans une application réelle, vous récupéreriez les abonnements push de chaque administrateur
        // et vous enverriez une notification push à chacun d'eux
        // Exemple :
        /*
    for (const admin of admins) {
      // Récupérer les abonnements push de l'administrateur
      const pushSubscriptions = await prisma.pushSubscription.findMany({
        where: { adminId: admin.id },
      });

      // Envoyer une notification push à chaque abonnement
      for (const subscription of pushSubscriptions) {
        try {
          await sendPushNotification(
            JSON.parse(subscription.subscription),
            {
              id: notifications.find(n => n.adminId === admin.id)?.id,
              type,
              message,
              quoteId,
            }
          );
        } catch (error) {
          console.error(`Erreur lors de l'envoi de la notification push à l'administrateur ${admin.id}:`, error);
        }
      }
    }
    */ return notifications;
    } catch (error) {
        console.error('Erreur lors de la création des notifications:', error);
        throw error;
    }
}
async function createNotificationForAdmin(adminId, type, message, quoteId) {
    try {
        const notification = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].notification.create({
            data: {
                type,
                message,
                adminId,
                ...quoteId && {
                    quoteId
                }
            }
        });
        return notification;
    } catch (error) {
        console.error('Erreur lors de la création de la notification:', error);
        throw error;
    }
}
async function getUnreadNotificationsForAdmin(adminId) {
    try {
        const notifications = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].notification.findMany({
            where: {
                adminId,
                isRead: false
            },
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                quote: {
                    select: {
                        quoteNumber: true,
                        client: {
                            include: {
                                user: {
                                    select: {
                                        firstname: true,
                                        lastname: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        return notifications;
    } catch (error) {
        console.error('Erreur lors de la récupération des notifications:', error);
        throw error;
    }
}
async function markNotificationAsRead(notificationId) {
    try {
        const notification = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].notification.update({
            where: {
                id: notificationId
            },
            data: {
                isRead: true
            }
        });
        return notification;
    } catch (error) {
        console.error('Erreur lors de la mise à jour de la notification:', error);
        throw error;
    }
}
async function markAllNotificationsAsRead(adminId) {
    try {
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].notification.updateMany({
            where: {
                adminId,
                isRead: false
            },
            data: {
                isRead: true
            }
        });
        return result;
    } catch (error) {
        console.error('Erreur lors de la mise à jour des notifications:', error);
        throw error;
    }
}
}}),
"[project]/src/lib/quotes.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "convertQuoteToOrder": (()=>convertQuoteToOrder),
    "createQuote": (()=>createQuote),
    "deleteQuote": (()=>deleteQuote),
    "getClientQuotes": (()=>getClientQuotes),
    "getQuoteById": (()=>getQuoteById),
    "getQuotes": (()=>getQuotes),
    "updateQuote": (()=>updateQuote)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notifications$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/notifications.ts [app-route] (ecmascript)");
;
;
;
;
async function getQuotes(options) {
    const { clientId, status, search, skip = 0, take = 50 } = options || {};
    const where = {
        ...clientId ? {
            clientId
        } : {},
        ...status ? {
            status
        } : {},
        ...search ? {
            OR: [
                {
                    quoteNumber: {
                        contains: search
                    }
                },
                {
                    client: {
                        user: {
                            firstname: {
                                contains: search
                            }
                        }
                    }
                },
                {
                    client: {
                        user: {
                            lastname: {
                                contains: search
                            }
                        }
                    }
                },
                {
                    client: {
                        company_name: {
                            contains: search
                        }
                    }
                }
            ]
        } : {}
    };
    const [quotes, total] = await Promise.all([
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where,
            include: {
                client: {
                    include: {
                        user: {
                            select: {
                                firstname: true,
                                lastname: true,
                                email: true,
                                telephone: true
                            }
                        }
                    }
                },
                quoteitem: {
                    include: {
                        product: true
                    }
                }
            },
            skip,
            take,
            orderBy: {
                createdAt: 'desc'
            }
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.count({
            where
        })
    ]);
    return {
        quotes,
        total
    };
}
async function getQuoteById(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findUnique({
        where: {
            id
        },
        include: {
            client: {
                include: {
                    user: {
                        select: {
                            id: true,
                            firstname: true,
                            lastname: true,
                            email: true,
                            telephone: true,
                            role: true
                        }
                    }
                }
            },
            quoteitem: {
                include: {
                    product: {
                        include: {
                            category: true,
                            brand: true,
                            productimage: true
                        }
                    }
                }
            }
        }
    });
}
async function createQuote(data) {
    const { clientId, notes, validUntil, items, createdByAdminId } = data;
    // Générer un numéro de devis unique
    const quoteNumber = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateQuoteNumber"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer le devis
        const quote = await tx.quote.create({
            data: {
                quoteNumber,
                clientId,
                status: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["QuoteStatus"].DRAFT,
                notes,
                validUntil,
                createdByAdminId
            }
        });
        // Ajouter les produits au devis
        for (const item of items){
            await tx.quoteItem.create({
                data: {
                    quoteId: quote.id,
                    productId: item.productId,
                    quantity: item.quantity,
                    unitPrice: 0
                }
            });
        }
        // Récupérer les informations du client pour la notification
        const client = await tx.client.findUnique({
            where: {
                id: clientId
            },
            include: {
                user: {
                    select: {
                        firstname: true,
                        lastname: true
                    }
                }
            }
        });
        // Créer une notification pour tous les administrateurs
        let notificationMessage = '';
        if (createdByAdminId) {
            // Si le devis a été créé par un administrateur
            const admin = await tx.admin.findUnique({
                where: {
                    id: createdByAdminId
                },
                include: {
                    user: {
                        select: {
                            firstname: true,
                            lastname: true
                        }
                    }
                }
            });
            if (admin && admin.user) {
                notificationMessage = `L'administrateur ${admin.user.firstname} ${admin.user.lastname} a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;
            } else {
                notificationMessage = `Un administrateur a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;
            }
        } else {
            // Si le devis a été créé par un client
            notificationMessage = `${client?.user.firstname} ${client?.user.lastname} a demandé un devis`;
        }
        // Créer la notification en dehors de la transaction pour éviter les problèmes
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notifications$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createNotificationForAllAdmins"])(__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["NotificationType"].QUOTE_REQUESTED, notificationMessage, quote.id);
        } catch (error) {
            console.error('Erreur lors de la création des notifications:', error);
        // Ne pas bloquer la création du devis si la notification échoue
        }
        return quote;
    });
}
async function updateQuote(id, data) {
    const { status, notes, validUntil, totalAmount, pdfUrl, items } = data;
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Préparer les données à mettre à jour
        const updateData = {};
        if (status !== undefined) updateData.status = status;
        if (notes !== undefined) updateData.notes = notes;
        if (totalAmount !== undefined) updateData.totalAmount = totalAmount;
        if (pdfUrl !== undefined) updateData.pdfUrl = pdfUrl;
        // Traiter la date de validité
        if (validUntil !== undefined) {
            if (validUntil === null) {
                updateData.validUntil = null;
            } else if (typeof validUntil === 'string') {
                // Si c'est une chaîne de caractères, s'assurer qu'elle est au format ISO
                if (validUntil.includes('T')) {
                    // Déjà au format ISO complet
                    updateData.validUntil = new Date(validUntil);
                } else {
                    // Ajouter l'heure (minuit) pour compléter le format ISO
                    updateData.validUntil = new Date(`${validUntil}T00:00:00.000Z`);
                }
            } else if (validUntil instanceof Date) {
                updateData.validUntil = validUntil;
            }
        }
        // Mettre à jour le devis
        const quote = await tx.quote.update({
            where: {
                id
            },
            data: updateData
        });
        // Mettre à jour les produits du devis si fournis
        if (items && items.length > 0) {
            // Récupérer les items existants
            const existingItems = await tx.quoteItem.findMany({
                where: {
                    quoteId: id
                }
            });
            // Créer un map des items existants pour un accès rapide
            const existingItemsMap = new Map(existingItems.map((item)=>[
                    item.productId,
                    item
                ]));
            // Traiter chaque item
            for (const item of items){
                const existingItem = item.id ? existingItems.find((i)=>i.id === item.id) : existingItemsMap.get(item.productId);
                if (existingItem) {
                    // Mettre à jour l'item existant
                    await tx.quoteItem.update({
                        where: {
                            id: existingItem.id
                        },
                        data: {
                            quantity: item.quantity,
                            ...item.unitPrice !== undefined && {
                                unitPrice: item.unitPrice
                            }
                        }
                    });
                } else {
                    // Créer un nouvel item
                    await tx.quoteItem.create({
                        data: {
                            quoteId: id,
                            productId: item.productId,
                            quantity: item.quantity,
                            unitPrice: item.unitPrice || 0
                        }
                    });
                }
            }
            // Supprimer les items qui ne sont plus dans la liste
            const newProductIds = items.map((item)=>item.productId);
            const itemsToDelete = existingItems.filter((item)=>!newProductIds.includes(item.productId));
            for (const item of itemsToDelete){
                await tx.quoteItem.delete({
                    where: {
                        id: item.id
                    }
                });
            }
        }
        return quote;
    });
}
async function deleteQuote(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Supprimer d'abord les items du devis
        await tx.quoteItem.deleteMany({
            where: {
                quoteId: id
            }
        });
        // Supprimer le devis
        return tx.quote.delete({
            where: {
                id
            }
        });
    });
}
async function convertQuoteToOrder(id) {
    const quote = await getQuoteById(id);
    if (!quote) {
        throw new Error('Quote not found');
    }
    if (quote.status !== __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["QuoteStatus"].APPROVED) {
        throw new Error('Only approved quotes can be converted to orders');
    }
// Logique pour créer une commande à partir du devis
// À implémenter selon les besoins
}
async function getClientQuotes(clientId, options) {
    const { status, skip = 0, take = 50 } = options || {};
    const where = {
        clientId,
        ...status ? {
            status
        } : {}
    };
    const [quotes, total] = await Promise.all([
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where,
            include: {
                quoteitem: {
                    include: {
                        product: true
                    }
                }
            },
            skip,
            take,
            orderBy: {
                createdAt: 'desc'
            }
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.count({
            where
        })
    ]);
    return {
        quotes,
        total
    };
}
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminUser": (()=>createAdminUser),
    "createClientUser": (()=>createClientUser),
    "createCommercialUser": (()=>createCommercialUser),
    "findUserByEmail": (()=>findUserByEmail),
    "findUserByUsername": (()=>findUserByUsername),
    "hashPassword": (()=>hashPassword),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
async function hashPassword(password) {
    const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(10);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, salt);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
async function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].CLIENT
            }
        });
        // Créer le profil client associé
        const client = await tx.client.create({
            data: {
                userId: user.id,
                company_name
            }
        });
        return {
            user,
            client
        };
    });
}
async function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].COMMERCIAL
            }
        });
        // Créer le profil commercial associé
        const commercial = await tx.commercial.create({
            data: {
                userId: user.id,
                profile_photo
            }
        });
        return {
            user,
            commercial
        };
    });
}
async function createAdminUser({ email, username, password, lastname, firstname, telephone }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].ADMIN
            }
        });
        // Créer le profil admin associé
        const admin = await tx.admin.create({
            data: {
                userId: user.id
            }
        });
        return {
            user,
            admin
        };
    });
}
async function findUserByUsername(username) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            username
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
async function findUserByEmail(email) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            email
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
}}),
"[project]/src/lib/auth-options.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'Credentials',
            credentials: {
                username: {
                    label: "Nom d'utilisateur",
                    type: 'text'
                },
                password: {
                    label: 'Mot de passe',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.username || !credentials?.password) {
                    return null;
                }
                try {
                    // Rechercher l'utilisateur par nom d'utilisateur
                    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findUserByUsername"])(credentials.username);
                    // Vérifier si l'utilisateur existe
                    if (!user) {
                        return null;
                    }
                    // Vérifier le mot de passe
                    const isPasswordValid = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyPassword"])(credentials.password, user.password);
                    if (!isPasswordValid) {
                        return null;
                    }
                    // Retourner les données de l'utilisateur sans le mot de passe
                    return {
                        id: user.id,
                        email: user.email,
                        username: user.username,
                        name: `${user.firstname} ${user.lastname}`,
                        firstname: user.firstname,
                        lastname: user.lastname,
                        role: user.role,
                        clientId: user.client?.id,
                        commercialId: user.commercial?.id,
                        adminId: user.admin?.id
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.username = user.username;
                token.role = user.role;
                token.firstname = user.firstname;
                token.lastname = user.lastname;
                token.clientId = user.clientId;
                token.commercialId = user.commercialId;
                token.adminId = user.adminId;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.id;
                session.user.username = token.username;
                session.user.role = token.role;
                session.user.firstname = token.firstname;
                session.user.lastname = token.lastname;
                session.user.clientId = token.clientId;
                session.user.commercialId = token.commercialId;
                session.user.adminId = token.adminId;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signOut: '/auth/signout',
        error: '/auth/error'
    },
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[project]/src/lib/mobile-auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMobileUserFromRequest": (()=>getMobileUserFromRequest),
    "isMobileRequest": (()=>isMobileRequest),
    "verifyMobileToken": (()=>verifyMobileToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
async function verifyMobileToken(request) {
    try {
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('🔐 No valid authorization header found');
            return null;
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        console.log('🔐 Mobile token received (length):', token.length);
        // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET
        let decoded;
        try {
            const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
            console.log('🔐 Trying JWT_SECRET for token verification');
            decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, jwtSecret);
            console.log('🔐 Token decoded successfully with JWT_SECRET:', {
                userId: decoded.userId,
                role: decoded.role
            });
        } catch (jwtError) {
            console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');
            try {
                const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';
                decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, nextAuthSecret);
                console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', {
                    userId: decoded.userId,
                    role: decoded.role
                });
            } catch (nextAuthError) {
                console.error('🔐 Both JWT secrets failed:', {
                    jwtError: jwtError.message,
                    nextAuthError: nextAuthError.message
                });
                throw new Error('Invalid token signature');
            }
        }
        // Get fresh user data from database
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: decoded.userId
            },
            select: {
                id: true,
                username: true,
                email: true,
                firstname: true,
                lastname: true,
                role: true,
                isActive: true
            }
        });
        if (!user || !user.isActive) {
            console.log('🔐 User not found or inactive:', {
                found: !!user,
                active: user?.isActive
            });
            return null;
        }
        console.log('🔐 Mobile user authenticated successfully:', {
            id: user.id,
            role: user.role
        });
        return user;
    } catch (error) {
        console.error('Mobile token verification error:', error);
        return null;
    }
}
function isMobileRequest(request) {
    const userAgent = request.headers.get('user-agent') || '';
    const authHeader = request.headers.get('authorization');
    // Check if it's a mobile request with JWT token
    return authHeader?.startsWith('Bearer ') || userAgent.includes('Expo') || userAgent.includes('ReactNative');
}
async function getMobileUserFromRequest(request) {
    // Always try to verify mobile token if Authorization header is present
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
        return await verifyMobileToken(request);
    }
    return null;
}
}}),
"[project]/src/app/api/quotes/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$quotes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/quotes.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-options.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mobile-auth.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
async function GET(req) {
    try {
        console.log('📋 Quotes API - Checking authentication...');
        // Check for mobile authentication first
        const mobileUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMobileUserFromRequest"])(req);
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        console.log('📋 Quotes API - Auth results:', {
            mobileUser: mobileUser ? {
                id: mobileUser.id,
                role: mobileUser.role
            } : null,
            session: session ? {
                id: session.user?.id,
                role: session.user?.role
            } : null
        });
        if (!mobileUser && !session) {
            console.log('📋 Quotes API - No authentication found');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Use mobile user data if available, otherwise use session
        const user = mobileUser || session?.user;
        console.log('📋 Quotes API - Using user:', {
            id: user?.id,
            role: user?.role
        });
        const searchParams = req.nextUrl.searchParams;
        const search = searchParams.get('search') || undefined;
        const status = searchParams.get('status');
        const page = searchParams.has('page') ? parseInt(searchParams.get('page')) : 1;
        const limit = searchParams.has('limit') ? parseInt(searchParams.get('limit')) : 10;
        const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')) : (page - 1) * limit;
        const take = searchParams.has('take') ? parseInt(searchParams.get('take')) : limit;
        // Filtrer par client si l'utilisateur est un client
        let clientId = undefined;
        if (user?.role === 'CLIENT' && user?.clientId) {
            clientId = user.clientId;
        } else if (searchParams.has('clientId')) {
            clientId = searchParams.get('clientId') || undefined;
        }
        // Utiliser la fonction existante pour récupérer les devis
        const { quotes, total } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$quotes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getQuotes"])({
            clientId,
            status,
            search,
            skip,
            take
        });
        // Pour le tableau de bord, nous avons besoin d'informations supplémentaires
        // Si le paramètre 'dashboard' est présent, enrichir les données
        if (searchParams.get('dashboard') === 'true') {
            // Récupérer les informations détaillées pour chaque devis
            const enrichedQuotes = await Promise.all(quotes.map(async (quote)=>{
                // Récupérer les informations du client
                const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].client.findUnique({
                    where: {
                        id: quote.clientId
                    },
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstname: true,
                                lastname: true,
                                email: true,
                                telephone: true
                            }
                        }
                    }
                });
                // Récupérer les éléments du devis avec les informations des produits
                const items = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quoteitem.findMany({
                    where: {
                        quoteId: quote.id
                    },
                    include: {
                        product: {
                            select: {
                                name: true,
                                reference: true
                            }
                        }
                    }
                });
                return {
                    ...quote,
                    client,
                    items
                };
            }));
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                quotes: enrichedQuotes,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            quotes,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('Error fetching quotes:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to fetch quotes'
        }, {
            status: 500
        });
    }
}
async function POST(req) {
    try {
        // Check for mobile authentication first
        const mobileUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMobileUserFromRequest"])(req);
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!mobileUser && !session) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Use mobile user data if available, otherwise use session
        const user = mobileUser || session?.user;
        const body = await req.json();
        const { items, notes } = body;
        // Vérifier que les items sont fournis
        if (!items || !Array.isArray(items) || items.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Items are required and must be a non-empty array'
            }, {
                status: 400
            });
        }
        // Déterminer l'ID du client
        let clientId;
        let createdByAdminId = undefined;
        if (user?.role === 'CLIENT') {
            // Si l'utilisateur est un client, utiliser son ID client
            if (!user.clientId) {
                console.error('Client ID not found in user:', user);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Client ID not found in user data. Please contact support.'
                }, {
                    status: 400
                });
            }
            clientId = user.clientId;
        } else if (user?.role === 'ADMIN') {
            // Si l'utilisateur est un admin
            if (body.clientId) {
                // Si un clientId est fourni, l'utiliser
                clientId = body.clientId;
            } else if (user.clientId) {
                // Si l'admin a aussi un compte client, utiliser cet ID
                clientId = user.clientId;
            } else {
                // Créer un devis pour le premier client trouvé (pour les tests)
                const firstClient = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].client.findFirst();
                if (!firstClient) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: 'No client found in the system. Please create a client first.'
                    }, {
                        status: 400
                    });
                }
                clientId = firstClient.id;
            }
            // Enregistrer l'ID de l'admin qui crée le devis
            createdByAdminId = user.adminId;
        } else {
            // Les commerciaux ne peuvent pas créer de devis directement
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized to create quotes'
            }, {
                status: 403
            });
        }
        // Créer le devis
        const quote = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$quotes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createQuote"])({
            clientId,
            notes,
            validUntil: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDefaultQuoteExpiryDate"])(),
            items: items.map((item)=>({
                    productId: item.productId,
                    quantity: item.quantity
                })),
            createdByAdminId
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            quote
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating quote:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to create quote'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3e13887f._.js.map