{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useFileUpload.ts"], "sourcesContent": ["import { useState } from 'react';\n\ninterface UploadOptions {\n  directory?: string;\n  onSuccess?: (url: string) => void;\n  onError?: (error: Error) => void;\n}\n\nexport function useFileUpload() {\n  const [isUploading, setIsUploading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [error, setError] = useState<Error | null>(null);\n\n  const uploadFile = async (file: File, options: UploadOptions = {}) => {\n    const { directory = 'uploads', onSuccess, onError } = options;\n    \n    setIsUploading(true);\n    setProgress(0);\n    setError(null);\n    \n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('directory', directory);\n      \n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setProgress((prev) => {\n          const newProgress = prev + Math.random() * 10;\n          return newProgress > 90 ? 90 : newProgress;\n        });\n      }, 200);\n      \n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n      \n      clearInterval(progressInterval);\n      \n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to upload file');\n      }\n      \n      const data = await response.json();\n      setProgress(100);\n      \n      if (onSuccess) {\n        onSuccess(data.url);\n      }\n      \n      return data.url;\n    } catch (err: any) {\n      setError(err);\n      \n      if (onError) {\n        onError(err);\n      }\n      \n      throw err;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const uploadBase64 = async (base64Data: string, options: UploadOptions = {}) => {\n    const { directory = 'uploads', onSuccess, onError } = options;\n    \n    setIsUploading(true);\n    setProgress(0);\n    setError(null);\n    \n    try {\n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setProgress((prev) => {\n          const newProgress = prev + Math.random() * 10;\n          return newProgress > 90 ? 90 : newProgress;\n        });\n      }, 200);\n      \n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          base64: base64Data,\n          directory,\n        }),\n      });\n      \n      clearInterval(progressInterval);\n      \n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to upload file');\n      }\n      \n      const data = await response.json();\n      setProgress(100);\n      \n      if (onSuccess) {\n        onSuccess(data.url);\n      }\n      \n      return data.url;\n    } catch (err: any) {\n      setError(err);\n      \n      if (onError) {\n        onError(err);\n      }\n      \n      throw err;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return {\n    uploadFile,\n    uploadBase64,\n    isUploading,\n    progress,\n    error,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,aAAa,OAAO,MAAY,UAAyB,CAAC,CAAC;QAC/D,MAAM,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEtD,eAAe;QACf,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,aAAa;YAE7B,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,YAAY,CAAC;oBACX,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK;oBAC3C,OAAO,cAAc,KAAK,KAAK;gBACjC;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,cAAc;YAEd,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;YAEZ,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;YACpB;YAEA,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS;YAET,IAAI,SAAS;gBACX,QAAQ;YACV;YAEA,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,OAAO,YAAoB,UAAyB,CAAC,CAAC;QACzE,MAAM,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEtD,eAAe;QACf,YAAY;QACZ,SAAS;QAET,IAAI;YACF,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,YAAY,CAAC;oBACX,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK;oBAC3C,OAAO,cAAc,KAAK,KAAK;gBACjC;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;gBACF;YACF;YAEA,cAAc;YAEd,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;YAEZ,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;YACpB;YAEA,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS;YAET,IAAI,SAAS;gBACX,QAAQ;YACV;YAEA,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { FaUpload, FaImage, FaTrash, FaSpinner } from 'react-icons/fa';\nimport { useFileUpload } from '@/hooks/useFileUpload';\n\ninterface ImageUploadProps {\n  initialImage?: string;\n  onImageChange: (imageUrl: string | null) => void;\n  directory?: string;\n  className?: string;\n  aspectRatio?: 'square' | '16/9' | '4/3' | 'auto';\n  maxSizeMB?: number;\n  label?: string;\n  required?: boolean;\n}\n\nexport default function ImageUpload({\n  initialImage,\n  onImageChange,\n  directory = 'uploads',\n  className = '',\n  aspectRatio = 'square',\n  maxSizeMB = 5,\n  label = 'Image',\n  required = false,\n}: ImageUploadProps) {\n  const [imageUrl, setImageUrl] = useState<string | null>(initialImage || null);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { uploadFile, isUploading, progress } = useFileUpload();\n\n  const aspectRatioClass = {\n    square: 'aspect-square',\n    '16/9': 'aspect-video',\n    '4/3': 'aspect-[4/3]',\n    'auto': '',\n  }[aspectRatio];\n\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n\n    const file = files[0];\n    \n    // Check file size\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSizeMB) {\n      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);\n      return;\n    }\n    \n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image');\n      return;\n    }\n    \n    setError(null);\n    \n    try {\n      const url = await uploadFile(file, {\n        directory,\n        onSuccess: (url) => {\n          setImageUrl(url);\n          onImageChange(url);\n        },\n        onError: (err) => {\n          setError(err.message);\n        },\n      });\n    } catch (err) {\n      // Error is handled by the hook\n    }\n  };\n\n  const handleRemoveImage = () => {\n    setImageUrl(null);\n    onImageChange(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    \n    const files = e.dataTransfer.files;\n    if (!files || files.length === 0) return;\n    \n    const file = files[0];\n    \n    // Check file size\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSizeMB) {\n      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);\n      return;\n    }\n    \n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image');\n      return;\n    }\n    \n    setError(null);\n    \n    try {\n      const url = await uploadFile(file, {\n        directory,\n        onSuccess: (url) => {\n          setImageUrl(url);\n          onImageChange(url);\n        },\n        onError: (err) => {\n          setError(err.message);\n        },\n      });\n    } catch (err) {\n      // Error is handled by the hook\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n  };\n\n  return (\n    <div className={className}>\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n        {label} {required && <span className=\"text-red-500\">*</span>}\n      </label>\n      \n      <div\n        className={`relative border-2 border-dashed rounded-lg ${\n          error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n        } ${aspectRatioClass} overflow-hidden`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n      >\n        {imageUrl ? (\n          <div className=\"relative w-full h-full\">\n            <Image\n              src={imageUrl}\n              alt=\"Uploaded image\"\n              fill\n              style={{ objectFit: 'cover' }}\n              className=\"w-full h-full\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center\">\n              <motion.button\n                type=\"button\"\n                onClick={handleRemoveImage}\n                className=\"p-2 bg-red-600 text-white rounded-full\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <FaTrash />\n              </motion.button>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex flex-col items-center justify-center p-6 h-full\">\n            {isUploading ? (\n              <div className=\"text-center\">\n                <FaSpinner className=\"animate-spin text-3xl text-primary mx-auto mb-2\" />\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Téléchargement en cours... {Math.round(progress)}%\n                </p>\n              </div>\n            ) : (\n              <>\n                <FaImage className=\"text-3xl text-gray-400 mb-2\" />\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 text-center mb-2\">\n                  Glissez-déposez une image ici ou cliquez pour sélectionner\n                </p>\n                <p className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n                  Formats acceptés: JPG, PNG, GIF, WebP (max {maxSizeMB} MB)\n                </p>\n              </>\n            )}\n          </div>\n        )}\n        \n        <input\n          type=\"file\"\n          ref={fileInputRef}\n          onChange={handleFileChange}\n          accept=\"image/*\"\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n          disabled={isUploading}\n        />\n      </div>\n      \n      {error && (\n        <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAmBe,SAAS,YAAY,EAClC,YAAY,EACZ,aAAa,EACb,YAAY,SAAS,EACrB,YAAY,EAAE,EACd,cAAc,QAAQ,EACtB,YAAY,CAAC,EACb,QAAQ,OAAO,EACf,WAAW,KAAK,EACC;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,gBAAgB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAE1D,MAAM,mBAAmB;QACvB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,CAAC,CAAC,YAAY;IAEd,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,WAAW;YAC1B,SAAS,CAAC,0CAA0C,EAAE,UAAU,GAAG,CAAC;YACpE;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,MAAM;gBACjC;gBACA,WAAW,CAAC;oBACV,YAAY;oBACZ,cAAc;gBAChB;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;QACZ,+BAA+B;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,WAAW;YAC1B,SAAS,CAAC,0CAA0C,EAAE,UAAU,GAAG,CAAC;YACpE;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,MAAM;gBACjC;gBACA,WAAW,CAAC;oBACV,YAAY;oBACZ,cAAc;gBAChB;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;QACZ,+BAA+B;QACjC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,WAAW,CAAC,2CAA2C,EACrD,QAAQ,mBAAmB,uCAC5B,CAAC,EAAE,iBAAiB,gBAAgB,CAAC;gBACtC,QAAQ;gBACR,YAAY;;oBAEX,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,OAAO;oCAAE,WAAW;gCAAQ;gCAC5B,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEvB,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;6CAKd,8OAAC;wBAAI,WAAU;kCACZ,4BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;;wCAA2C;wCAC1B,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;iDAIrD;;8CACE,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAA4D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;;wCAAuD;wCACtB;wCAAU;;;;;;;;;;;;;;kCAOhE,8OAAC;wBACC,MAAK;wBACL,KAAK;wBACL,UAAU;wBACV,QAAO;wBACP,WAAU;wBACV,UAAU;;;;;;;;;;;;YAIb,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/commercials/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaPlus, FaEdit, FaTrash, Fa<PERSON><PERSON>ch, <PERSON>a<PERSON><PERSON><PERSON>, FaEx<PERSON><PERSON><PERSON><PERSON>, FaUser<PERSON>ie, FaPhone, FaEnvelope, <PERSON>a<PERSON>ser } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport ImageUpload from '@/components/ui/ImageUpload';\n\ninterface Commercial {\n  id: string;\n  user: {\n    id: string;\n    email: string;\n    username: string;\n    firstname: string;\n    lastname: string;\n    telephone?: string;\n    role: string;\n  };\n  profile_photo?: string;\n  commercialclient: any[];\n}\n\nexport default function AdminCommercialsPage() {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isDeleting, setIsDeleting] = useState<string | null>(null);\n  const [commercials, setCommercials] = useState<Commercial[]>([]);\n  const [error, setError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Form state\n  const [showForm, setShowForm] = useState(false);\n  const [editingCommercial, setEditingCommercial] = useState<Commercial | null>(null);\n  const [formData, setFormData] = useState({\n    email: '',\n    username: '',\n    password: '',\n    firstname: '',\n    lastname: '',\n    telephone: '',\n    profile_photo: '',\n  });\n  const [formErrors, setFormErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    fetchCommercials();\n  }, []);\n\n  const fetchCommercials = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/commercials');\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch commercials');\n      }\n\n      const data = await response.json();\n      setCommercials(data.commercials);\n    } catch (err: any) {\n      console.error('Error fetching commercials:', err);\n      setError(err.message || 'An error occurred while fetching commercials');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error for this field\n    if (formErrors[name]) {\n      setFormErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[name];\n        return newErrors;\n      });\n    }\n  };\n\n  const handleImageChange = (imageUrl: string | null) => {\n    setFormData(prev => ({ ...prev, profile_photo: imageUrl || '' }));\n  };\n\n  const validateForm = () => {\n    const errors: Record<string, string> = {};\n\n    if (!formData.email.trim()) {\n      errors.email = 'L\\'email est requis';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      errors.email = 'L\\'email n\\'est pas valide';\n    }\n\n    if (!editingCommercial && !formData.username.trim()) {\n      errors.username = 'Le nom d\\'utilisateur est requis';\n    }\n\n    if (!editingCommercial && !formData.password.trim()) {\n      errors.password = 'Le mot de passe est requis';\n    } else if (!editingCommercial && formData.password.length < 6) {\n      errors.password = 'Le mot de passe doit contenir au moins 6 caractères';\n    }\n\n    if (!formData.firstname.trim()) {\n      errors.firstname = 'Le prénom est requis';\n    }\n\n    if (!formData.lastname.trim()) {\n      errors.lastname = 'Le nom est requis';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    setError(null);\n    setSuccessMessage(null);\n\n    try {\n      if (editingCommercial) {\n        // Update existing commercial\n        const response = await fetch(`/api/commercials/${editingCommercial.id}`, {\n          method: 'PATCH',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email: formData.email,\n            firstname: formData.firstname,\n            lastname: formData.lastname,\n            telephone: formData.telephone || undefined,\n            profile_photo: formData.profile_photo || undefined,\n          }),\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to update commercial');\n        }\n      } else {\n        // Create new commercial\n        const response = await fetch('/api/commercials', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email: formData.email,\n            username: formData.username,\n            password: formData.password,\n            firstname: formData.firstname,\n            lastname: formData.lastname,\n            telephone: formData.telephone || undefined,\n            profile_photo: formData.profile_photo || undefined,\n          }),\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to create commercial');\n        }\n      }\n\n      // Refresh the commercial list\n      await fetchCommercials();\n\n      // Reset form\n      setFormData({\n        email: '',\n        username: '',\n        password: '',\n        firstname: '',\n        lastname: '',\n        telephone: '',\n        profile_photo: '',\n      });\n\n      setShowForm(false);\n      setEditingCommercial(null);\n      setSuccessMessage(`Commercial ${editingCommercial ? 'modifié' : 'créé'} avec succès`);\n\n      // Hide success message after 3 seconds\n      setTimeout(() => {\n        setSuccessMessage(null);\n      }, 3000);\n    } catch (err: any) {\n      console.error(`Error ${editingCommercial ? 'updating' : 'creating'} commercial:`, err);\n      setError(err.message || `Une erreur est survenue lors de la ${editingCommercial ? 'modification' : 'création'} du commercial`);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleEdit = (commercial: Commercial) => {\n    setEditingCommercial(commercial);\n    setFormData({\n      email: commercial.user.email,\n      username: commercial.user.username,\n      password: '', // We don't show or edit the password\n      firstname: commercial.user.firstname,\n      lastname: commercial.user.lastname,\n      telephone: commercial.user.telephone || '',\n      profile_photo: commercial.profile_photo || '',\n    });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (commercialId: string) => {\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce commercial ? Cette action est irréversible.')) {\n      return;\n    }\n\n    try {\n      setIsDeleting(commercialId);\n      setError(null);\n\n      const response = await fetch(`/api/commercials/${commercialId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to delete commercial');\n      }\n\n      // Refresh the commercial list\n      await fetchCommercials();\n\n      setSuccessMessage('Commercial supprimé avec succès');\n\n      // Hide success message after 3 seconds\n      setTimeout(() => {\n        setSuccessMessage(null);\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error deleting commercial:', err);\n      setError(err.message || 'Une erreur est survenue lors de la suppression du commercial');\n    } finally {\n      setIsDeleting(null);\n    }\n  };\n\n  const handleCancel = () => {\n    setShowForm(false);\n    setEditingCommercial(null);\n    setFormData({\n      email: '',\n      username: '',\n      password: '',\n      firstname: '',\n      lastname: '',\n      telephone: '',\n      profile_photo: '',\n    });\n    setFormErrors({});\n  };\n\n  const filteredCommercials = commercials.filter(commercial => {\n    const searchTermLower = searchTerm.toLowerCase();\n    return (\n      commercial.user.firstname.toLowerCase().includes(searchTermLower) ||\n      commercial.user.lastname.toLowerCase().includes(searchTermLower) ||\n      commercial.user.email.toLowerCase().includes(searchTermLower) ||\n      commercial.user.username.toLowerCase().includes(searchTermLower)\n    );\n  });\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0\">\n            Gestion des Commerciaux\n          </h1>\n          {!showForm && (\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowForm(true)}\n              className=\"flex items-center justify-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors\"\n            >\n              <FaPlus />\n              <span>Ajouter un commercial</span>\n            </motion.button>\n          )}\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center\">\n            <FaExclamationTriangle className=\"mr-2\" />\n            <span>{error}</span>\n          </div>\n        )}\n\n        {successMessage && (\n          <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md\">\n            {successMessage}\n          </div>\n        )}\n\n        {/* Commercial Form */}\n        {showForm && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white mb-4\">\n              {editingCommercial ? 'Modifier le commercial' : 'Ajouter un commercial'}\n            </h2>\n\n            <form onSubmit={handleSubmit}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"firstname\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Prénom <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"firstname\"\n                    name=\"firstname\"\n                    value={formData.firstname}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      formErrors.firstname ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  />\n                  {formErrors.firstname && (\n                    <p className=\"mt-1 text-sm text-red-500\">{formErrors.firstname}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label htmlFor=\"lastname\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Nom <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"lastname\"\n                    name=\"lastname\"\n                    value={formData.lastname}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      formErrors.lastname ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  />\n                  {formErrors.lastname && (\n                    <p className=\"mt-1 text-sm text-red-500\">{formErrors.lastname}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Email <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      formErrors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  />\n                  {formErrors.email && (\n                    <p className=\"mt-1 text-sm text-red-500\">{formErrors.email}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label htmlFor=\"telephone\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Téléphone\n                  </label>\n                  <input\n                    type=\"tel\"\n                    id=\"telephone\"\n                    name=\"telephone\"\n                    value={formData.telephone}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                  />\n                </div>\n\n                {!editingCommercial && (\n                  <>\n                    <div>\n                      <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        Nom d'utilisateur <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"username\"\n                        name=\"username\"\n                        value={formData.username}\n                        onChange={handleInputChange}\n                        className={`w-full px-4 py-2 border ${\n                          formErrors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                        } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                      />\n                      {formErrors.username && (\n                        <p className=\"mt-1 text-sm text-red-500\">{formErrors.username}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        Mot de passe <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"password\"\n                        id=\"password\"\n                        name=\"password\"\n                        value={formData.password}\n                        onChange={handleInputChange}\n                        className={`w-full px-4 py-2 border ${\n                          formErrors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                        } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                      />\n                      {formErrors.password && (\n                        <p className=\"mt-1 text-sm text-red-500\">{formErrors.password}</p>\n                      )}\n                    </div>\n                  </>\n                )}\n\n                <div className={!editingCommercial ? 'md:col-span-2' : ''}>\n                  <ImageUpload\n                    initialImage={formData.profile_photo}\n                    onImageChange={handleImageChange}\n                    directory=\"commercials\"\n                    label=\"Photo de profil\"\n                    aspectRatio=\"square\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <motion.button\n                  type=\"button\"\n                  onClick={handleCancel}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  Annuler\n                </motion.button>\n\n                <motion.button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className={`px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center ${\n                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\n                  }`}\n                >\n                  {isSubmitting ? (\n                    <>\n                      <FaSpinner className=\"animate-spin mr-2\" />\n                      {editingCommercial ? 'Modification...' : 'Création...'}\n                    </>\n                  ) : (\n                    <>{editingCommercial ? 'Modifier' : 'Créer'}</>\n                  )}\n                </motion.button>\n              </div>\n            </form>\n          </div>\n        )}\n\n        {/* Search Bar */}\n        <div className=\"mb-6\">\n          <div className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher un commercial...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full px-4 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n            />\n            <FaSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n          </div>\n        </div>\n\n        {/* Commercials List */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\">\n          {isLoading && commercials.length === 0 ? (\n            <div className=\"flex items-center justify-center py-12\">\n              <FaSpinner className=\"animate-spin text-4xl text-primary\" />\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Commercial\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Contact\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Clients\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                  {filteredCommercials.length > 0 ? (\n                    filteredCommercials.map((commercial) => (\n                      <tr key={commercial.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10 relative rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700\">\n                              {commercial.profile_photo ? (\n                                <Image\n                                  src={commercial.profile_photo}\n                                  alt={`${commercial.user.firstname} ${commercial.user.lastname}`}\n                                  fill\n                                  style={{ objectFit: 'cover' }}\n                                />\n                              ) : (\n                                <div className=\"h-full w-full flex items-center justify-center text-gray-400\">\n                                  <FaUserTie size={20} />\n                                </div>\n                              )}\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                                {commercial.user.firstname} {commercial.user.lastname}\n                              </div>\n                              <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                                @{commercial.user.username}\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900 dark:text-white flex items-center\">\n                            <FaEnvelope className=\"mr-2 text-gray-400\" />\n                            {commercial.user.email}\n                          </div>\n                          {commercial.user.telephone && (\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1\">\n                              <FaPhone className=\"mr-2 text-gray-400\" />\n                              {commercial.user.telephone}\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                          {commercial.commercialclient.length}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleEdit(commercial)}\n                              className=\"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300\"\n                            >\n                              <FaEdit />\n                            </button>\n                            <button\n                              onClick={() => handleDelete(commercial.id)}\n                              disabled={isDeleting === commercial.id}\n                              className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 ${\n                                isDeleting === commercial.id ? 'opacity-50 cursor-not-allowed' : ''\n                              }`}\n                            >\n                              {isDeleting === commercial.id ? <FaSpinner className=\"animate-spin\" /> : <FaTrash />}\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan={4} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                        {isLoading ? 'Chargement...' : 'Aucun commercial trouvé'}\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AA0Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;QACV,WAAW;QACX,eAAe;IACjB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe,KAAK,WAAW;QACjC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,6BAA6B;QAC7B,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA;gBACZ,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,KAAK;gBACtB,OAAO;YACT;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe,YAAY;YAAG,CAAC;IACjE;IAEA,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,qBAAqB,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YACnD,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,qBAAqB,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YACnD,OAAO,QAAQ,GAAG;QACpB,OAAO,IAAI,CAAC,qBAAqB,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7D,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO,SAAS,GAAG;QACrB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,OAAO,QAAQ,GAAG;QACpB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,kBAAkB;QAElB,IAAI;YACF,IAAI,mBAAmB;gBACrB,6BAA6B;gBAC7B,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,EAAE,EAAE;oBACvE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,OAAO,SAAS,KAAK;wBACrB,WAAW,SAAS,SAAS;wBAC7B,UAAU,SAAS,QAAQ;wBAC3B,WAAW,SAAS,SAAS,IAAI;wBACjC,eAAe,SAAS,aAAa,IAAI;oBAC3C;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;YACF,OAAO;gBACL,wBAAwB;gBACxB,MAAM,WAAW,MAAM,MAAM,oBAAoB;oBAC/C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,OAAO,SAAS,KAAK;wBACrB,UAAU,SAAS,QAAQ;wBAC3B,UAAU,SAAS,QAAQ;wBAC3B,WAAW,SAAS,SAAS;wBAC7B,UAAU,SAAS,QAAQ;wBAC3B,WAAW,SAAS,SAAS,IAAI;wBACjC,eAAe,SAAS,aAAa,IAAI;oBAC3C;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;YACF;YAEA,8BAA8B;YAC9B,MAAM;YAEN,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,eAAe;YACjB;YAEA,YAAY;YACZ,qBAAqB;YACrB,kBAAkB,CAAC,WAAW,EAAE,oBAAoB,YAAY,OAAO,YAAY,CAAC;YAEpF,uCAAuC;YACvC,WAAW;gBACT,kBAAkB;YACpB,GAAG;QACL,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,oBAAoB,aAAa,WAAW,YAAY,CAAC,EAAE;YAClF,SAAS,IAAI,OAAO,IAAI,CAAC,mCAAmC,EAAE,oBAAoB,iBAAiB,WAAW,cAAc,CAAC;QAC/H,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,qBAAqB;QACrB,YAAY;YACV,OAAO,WAAW,IAAI,CAAC,KAAK;YAC5B,UAAU,WAAW,IAAI,CAAC,QAAQ;YAClC,UAAU;YACV,WAAW,WAAW,IAAI,CAAC,SAAS;YACpC,UAAU,WAAW,IAAI,CAAC,QAAQ;YAClC,WAAW,WAAW,IAAI,CAAC,SAAS,IAAI;YACxC,eAAe,WAAW,aAAa,IAAI;QAC7C;QACA,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,sFAAsF;YACjG;QACF;QAEA,IAAI;YACF,cAAc;YACd,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,cAAc,EAAE;gBAC/D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,8BAA8B;YAC9B,MAAM;YAEN,kBAAkB;YAElB,uCAAuC;YACvC,WAAW;gBACT,kBAAkB;YACpB,GAAG;QACL,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,qBAAqB;QACrB,YAAY;YACV,OAAO;YACP,UAAU;YACV,UAAU;YACV,WAAW;YACX,UAAU;YACV,WAAW;YACX,eAAe;QACjB;QACA,cAAc,CAAC;IACjB;IAEA,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,MAAM,kBAAkB,WAAW,WAAW;QAC9C,OACE,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACjD,WAAW,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAChD,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAC7C,WAAW,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEpD;IAEA,qBACE,8OAAC,wIAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgE;;;;;;wBAG7E,CAAC,0BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS,IAAM,YAAY;4BAC3B,WAAU;;8CAEV,8OAAC,8IAAA,CAAA,SAAM;;;;;8CACP,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;gBAMX,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,wBAAqB;4BAAC,WAAU;;;;;;sCACjC,8OAAC;sCAAM;;;;;;;;;;;;gBAIV,gCACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;gBAKJ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,oBAAoB,2BAA2B;;;;;;sCAGlD,8OAAC;4BAAK,UAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;;wDAAkE;sEAC9F,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAExC,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,SAAS,GAAG,mBAAmB,uCAC3C,wHAAwH,CAAC;;;;;;gDAE3H,WAAW,SAAS,kBACnB,8OAAC;oDAAE,WAAU;8DAA6B,WAAW,SAAS;;;;;;;;;;;;sDAIlE,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;;wDAAkE;sEAChG,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAErC,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,QAAQ,GAAG,mBAAmB,uCAC1C,wHAAwH,CAAC;;;;;;gDAE3H,WAAW,QAAQ,kBAClB,8OAAC;oDAAE,WAAU;8DAA6B,WAAW,QAAQ;;;;;;;;;;;;sDAIjE,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;;wDAAkE;sEAC3F,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEvC,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,KAAK,GAAG,mBAAmB,uCACvC,wHAAwH,CAAC;;;;;;gDAE3H,WAAW,KAAK,kBACf,8OAAC;oDAAE,WAAU;8DAA6B,WAAW,KAAK;;;;;;;;;;;;sDAI9D,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAAkE;;;;;;8DAGvG,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,WAAU;;;;;;;;;;;;wCAIb,CAAC,mCACA;;8DACE,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;;gEAAkE;8EAClF,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEnD,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,QAAQ,GAAG,mBAAmB,uCAC1C,wHAAwH,CAAC;;;;;;wDAE3H,WAAW,QAAQ,kBAClB,8OAAC;4DAAE,WAAU;sEAA6B,WAAW,QAAQ;;;;;;;;;;;;8DAIjE,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;;gEAAkE;8EACvF,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE9C,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,QAAQ,GAAG,mBAAmB,uCAC1C,wHAAwH,CAAC;;;;;;wDAE3H,WAAW,QAAQ,kBAClB,8OAAC;4DAAE,WAAU;sEAA6B,WAAW,QAAQ;;;;;;;;;;;;;;sDAMrE,8OAAC;4CAAI,WAAW,CAAC,oBAAoB,kBAAkB;sDACrD,cAAA,8OAAC,uIAAA,CAAA,UAAW;gDACV,cAAc,SAAS,aAAa;gDACpC,eAAe;gDACf,WAAU;gDACV,OAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDACX;;;;;;sDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,UAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAW,CAAC,qGAAqG,EAC/G,eAAe,kCAAkC,IACjD;sDAED,6BACC;;kEACE,8OAAC,8IAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,oBAAoB,oBAAoB;;6EAG3C;0DAAG,oBAAoB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;8BAShD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,8OAAC,8IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKxB,8OAAC;oBAAI,WAAU;8BACZ,aAAa,YAAY,MAAM,KAAK,kBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;6CAGvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,8OAAC;oCAAM,WAAU;8CACd,oBAAoB,MAAM,GAAG,IAC5B,oBAAoB,GAAG,CAAC,CAAC,2BACvB,8OAAC;4CAAuB,WAAU;;8DAChC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,WAAW,aAAa,iBACvB,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK,WAAW,aAAa;oEAC7B,KAAK,GAAG,WAAW,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,QAAQ,EAAE;oEAC/D,IAAI;oEACJ,OAAO;wEAAE,WAAW;oEAAQ;;;;;yFAG9B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;wEAAC,MAAM;;;;;;;;;;;;;;;;0EAIvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,WAAW,IAAI,CAAC,SAAS;4EAAC;4EAAE,WAAW,IAAI,CAAC,QAAQ;;;;;;;kFAEvD,8OAAC;wEAAI,WAAU;;4EAA2C;4EACtD,WAAW,IAAI,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8DAKlC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEACrB,WAAW,IAAI,CAAC,KAAK;;;;;;;wDAEvB,WAAW,IAAI,CAAC,SAAS,kBACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAClB,WAAW,IAAI,CAAC,SAAS;;;;;;;;;;;;;8DAIhC,8OAAC;oDAAG,WAAU;8DACX,WAAW,gBAAgB,CAAC,MAAM;;;;;;8DAErC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,WAAW;gEAC1B,WAAU;0EAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;0EAET,8OAAC;gEACC,SAAS,IAAM,aAAa,WAAW,EAAE;gEACzC,UAAU,eAAe,WAAW,EAAE;gEACtC,WAAW,CAAC,0EAA0E,EACpF,eAAe,WAAW,EAAE,GAAG,kCAAkC,IACjE;0EAED,eAAe,WAAW,EAAE,iBAAG,8OAAC,8IAAA,CAAA,YAAS;oEAAC,WAAU;;;;;yFAAoB,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;2CAzDhF,WAAW,EAAE;;;;kEAgExB,8OAAC;kDACC,cAAA,8OAAC;4CAAG,SAAS;4CAAG,WAAU;sDACvB,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD", "debugId": null}}]}