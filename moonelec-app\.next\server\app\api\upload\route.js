/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload/route";
exports.ids = ["app/api/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload/route.ts */ \"(rsc)/./src/app/api/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload/route\",\n        pathname: \"/api/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/upload/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/upload/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_upload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/upload */ \"(rsc)/./src/lib/upload.ts\");\n\n\n// POST /api/upload - Upload a file\nasync function POST(req) {\n    try {\n        const formData = await req.formData();\n        const file = formData.get('file');\n        const base64 = formData.get('base64');\n        const directory = formData.get('directory') || 'uploads';\n        if (!file && !base64) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file or base64 data provided'\n            }, {\n                status: 400\n            });\n        }\n        let fileUrl;\n        if (file) {\n            fileUrl = await (0,_lib_upload__WEBPACK_IMPORTED_MODULE_1__.saveFile)(file, directory);\n        } else if (base64) {\n            fileUrl = (0,_lib_upload__WEBPACK_IMPORTED_MODULE_1__.saveBase64Image)(base64, directory);\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid file data'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            url: fileUrl\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error uploading file:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to upload file'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/upload.ts":
/*!***************************!*\
  !*** ./src/lib/upload.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteFile: () => (/* binding */ deleteFile),\n/* harmony export */   saveBase64Image: () => (/* binding */ saveBase64Image),\n/* harmony export */   saveFile: () => (/* binding */ saveFile)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// Security configuration\nconst SECURITY_CONFIG = {\n    maxFileSize: 25 * 1024 * 1024,\n    allowedMimeTypes: [\n        // Images\n        'image/jpeg',\n        'image/jpg',\n        'image/png',\n        'image/gif',\n        'image/webp',\n        'image/svg+xml',\n        // Documents\n        'application/pdf',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'application/vnd.ms-excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-powerpoint',\n        'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n        // Text files\n        'text/plain',\n        'text/csv',\n        'text/xml',\n        'application/json',\n        // Archives (limited)\n        'application/zip'\n    ],\n    dangerousExtensions: [\n        'exe',\n        'bat',\n        'cmd',\n        'com',\n        'pif',\n        'scr',\n        'vbs',\n        'js',\n        'jar',\n        'php',\n        'asp',\n        'jsp',\n        'sh'\n    ],\n    suspiciousPatterns: [\n        Buffer.from('MZ'),\n        Buffer.from('#!/')\n    ]\n};\n// Security validation functions\nconst validateFileSize = (file)=>{\n    if (file.size > SECURITY_CONFIG.maxFileSize) {\n        throw new Error(`File too large. Maximum size is ${SECURITY_CONFIG.maxFileSize / (1024 * 1024)}MB`);\n    }\n};\nconst validateFileType = (file)=>{\n    if (!SECURITY_CONFIG.allowedMimeTypes.includes(file.type)) {\n        throw new Error('File type not allowed. Allowed types: images, PDF, Office documents, text files');\n    }\n};\nconst validateFileExtension = (fileName)=>{\n    const extension = path__WEBPACK_IMPORTED_MODULE_1___default().extname(fileName).toLowerCase().substring(1);\n    if (SECURITY_CONFIG.dangerousExtensions.includes(extension)) {\n        throw new Error('File extension not allowed for security reasons');\n    }\n};\nconst scanForMalware = (buffer)=>{\n    for (const pattern of SECURITY_CONFIG.suspiciousPatterns){\n        if (buffer.indexOf(pattern) === 0) {\n            throw new Error('File contains suspicious content and cannot be uploaded');\n        }\n    }\n};\n// Ensure upload directory exists\nconst createUploadDir = (dir)=>{\n    const uploadDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'public', dir);\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(uploadDir)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(uploadDir, {\n            recursive: true\n        });\n    }\n    return uploadDir;\n};\n// Save a file to the public directory with security validation\nconst saveFile = async (file, directory = 'uploads')=>{\n    // Security validations\n    validateFileSize(file);\n    validateFileType(file);\n    validateFileExtension(file.name);\n    const uploadDir = createUploadDir(directory);\n    // Convert file to buffer for malware scanning\n    const buffer = Buffer.from(await file.arrayBuffer());\n    scanForMalware(buffer);\n    // Generate a unique filename\n    const fileExtension = path__WEBPACK_IMPORTED_MODULE_1___default().extname(file.name);\n    const fileName = `${(0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()}${fileExtension}`;\n    const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadDir, fileName);\n    // Write file to disk\n    fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, buffer);\n    // Return the public URL\n    return `/${directory}/${fileName}`;\n};\n// Save a base64 image to the public directory\nconst saveBase64Image = (base64Data, directory = 'uploads')=>{\n    // Create directory if it doesn't exist\n    const uploadDir = createUploadDir(directory);\n    // Extract the file extension from the base64 data\n    const matches = base64Data.match(/^data:image\\/([a-zA-Z]+);base64,/);\n    if (!matches || matches.length !== 2) {\n        throw new Error('Invalid base64 image format');\n    }\n    const fileExtension = matches[1];\n    const base64Image = base64Data.replace(/^data:image\\/[a-zA-Z]+;base64,/, '');\n    // Generate a unique filename\n    const fileName = `${(0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()}.${fileExtension}`;\n    const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadDir, fileName);\n    // Write file to disk\n    fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, base64Image, 'base64');\n    // Return the public URL\n    return `/${directory}/${fileName}`;\n};\n// Delete a file from the public directory\nconst deleteFile = (fileUrl)=>{\n    try {\n        // Extract the file path from the URL\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'public', fileUrl);\n        // Check if file exists\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(filePath)) {\n            // Delete the file\n            fs__WEBPACK_IMPORTED_MODULE_0___default().unlinkSync(filePath);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error('Error deleting file:', error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/upload.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();