import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

// Composant d'ampoule clignotante
export const BlinkingBulb: React.FC<{
  size?: number;
  color?: string;
  position?: { top?: string; bottom?: string; left?: string; right?: string };
  delay?: number;
}> = ({ size = 24, color = '#FFD700', position = {}, delay = 0 }) => {
  const blinkAnim = useRef(new Animated.Value(0.3)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const blinkLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(blinkAnim, {
          toValue: 1,
          duration: 800 + Math.random() * 400,
          useNativeDriver: true,
        }),
        Animated.timing(blinkAnim, {
          toValue: 0.3,
          duration: 200 + Math.random() * 200,
          useNativeDriver: true,
        }),
      ])
    );

    const glowLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    setTimeout(() => {
      blinkLoop.start();
      glowLoop.start();
    }, delay);

    return () => {
      blinkLoop.stop();
      glowLoop.stop();
    };
  }, [delay]);

  return (
    <Animated.View
      style={[
        styles.bulbContainer,
        position,
        {
          opacity: blinkAnim,
        },
      ]}
    >
      {/* Effet de glow */}
      <Animated.View
        style={[
          styles.bulbGlow,
          {
            width: size * 2,
            height: size * 2,
            borderRadius: size,
            backgroundColor: color,
            opacity: glowAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 0.3],
            }),
          },
        ]}
      />
      
      {/* Ampoule */}
      <Ionicons name="bulb" size={size} color={color} />
    </Animated.View>
  );
};

// Composant d'éclair électrique
export const ElectricBolt: React.FC<{
  size?: number;
  color?: string;
  position?: { top?: string; bottom?: string; left?: string; right?: string };
  delay?: number;
}> = ({ size = 20, color = '#00BFFF', position = {}, delay = 0 }) => {
  const boltAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    const boltLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(boltAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(boltAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.delay(1000 + Math.random() * 2000),
      ])
    );

    const scaleLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ])
    );

    setTimeout(() => {
      boltLoop.start();
      scaleLoop.start();
    }, delay);

    return () => {
      boltLoop.stop();
      scaleLoop.stop();
    };
  }, [delay]);

  return (
    <Animated.View
      style={[
        styles.boltContainer,
        position,
        {
          opacity: boltAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <Ionicons name="flash" size={size} color={color} />
    </Animated.View>
  );
};

// Composant de panneau électrique avec indicateurs
export const ElectricPanel: React.FC<{
  size?: number;
  position?: { top?: string; bottom?: string; left?: string; right?: string };
}> = ({ size = 32, position = {} }) => {
  const panelAnim = useRef(new Animated.Value(0)).current;
  const indicator1 = useRef(new Animated.Value(0)).current;
  const indicator2 = useRef(new Animated.Value(0)).current;
  const indicator3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation du panneau principal
    Animated.timing(panelAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Animations des indicateurs
    const indicators = [indicator1, indicator2, indicator3];
    indicators.forEach((indicator, index) => {
      const loop = Animated.loop(
        Animated.sequence([
          Animated.timing(indicator, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(indicator, {
            toValue: 0.3,
            duration: 700,
            useNativeDriver: true,
          }),
        ])
      );

      setTimeout(() => loop.start(), index * 200);
    });
  }, []);

  return (
    <Animated.View
      style={[
        styles.panelContainer,
        position,
        {
          opacity: panelAnim,
        },
      ]}
    >
      {/* Panneau principal */}
      <Ionicons name="grid-outline" size={size} color="#006db7" />
      
      {/* Indicateurs LED */}
      <View style={styles.indicatorsContainer}>
        <Animated.View
          style={[
            styles.indicator,
            { opacity: indicator1, backgroundColor: '#00FF00' },
          ]}
        />
        <Animated.View
          style={[
            styles.indicator,
            { opacity: indicator2, backgroundColor: '#FFD700' },
          ]}
        />
        <Animated.View
          style={[
            styles.indicator,
            { opacity: indicator3, backgroundColor: '#FF4500' },
          ]}
        />
      </View>
    </Animated.View>
  );
};

// Composant d'arrière-plan électrique complet
export const ElectricBackgroundAdvanced: React.FC = () => {
  return (
    <View style={styles.backgroundContainer}>
      {/* Ampoules clignotantes */}
      <BlinkingBulb
        size={28}
        color="#FFD700"
        position={{ top: '15%', right: '10%' }}
        delay={0}
      />
      <BlinkingBulb
        size={24}
        color="#FFA500"
        position={{ top: '35%', left: '8%' }}
        delay={500}
      />
      <BlinkingBulb
        size={20}
        color="#FFFF00"
        position={{ bottom: '30%', right: '15%' }}
        delay={1000}
      />

      {/* Éclairs électriques */}
      <ElectricBolt
        size={22}
        color="#00BFFF"
        position={{ top: '25%', right: '25%' }}
        delay={200}
      />
      <ElectricBolt
        size={18}
        color="#87CEEB"
        position={{ bottom: '40%', left: '20%' }}
        delay={800}
      />

      {/* Panneaux électriques */}
      <ElectricPanel
        size={30}
        position={{ top: '50%', left: '5%' }}
      />
      <ElectricPanel
        size={26}
        position={{ bottom: '20%', right: '8%' }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  bulbContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bulbGlow: {
    position: 'absolute',
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 15,
    elevation: 10,
  },
  boltContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  panelContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicatorsContainer: {
    position: 'absolute',
    top: -5,
    right: -8,
    flexDirection: 'column',
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginVertical: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 3,
  },
});

export default ElectricBackgroundAdvanced;
