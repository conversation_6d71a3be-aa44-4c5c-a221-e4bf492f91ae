'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { FaBell } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/hooks/useAuth';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { registerServiceWorker, requestNotificationPermission, subscribeToPushNotifications } from '@/lib/serviceWorker';

interface Notification {
  id: string;
  type: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  quoteId?: string;
  quote?: {
    quoteNumber: string;
  };
}

export default function NotificationBell() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // <PERSON>rmer le dropdown quand on clique en dehors
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Récupérer les notifications
  const fetchNotifications = useCallback(async () => {
    if (!user || user.role !== 'ADMIN') return;

    try {
      setIsLoading(true);
      const response = await fetch('/api/notifications');

      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      setNotifications(data.notifications || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user, setNotifications, setIsLoading]);

  // Récupérer les notifications au chargement et toutes les 30 secondes
  useEffect(() => {
    if (user && user.role === 'ADMIN') {
      fetchNotifications();

      const interval = setInterval(fetchNotifications, 30000);

      return () => clearInterval(interval);
    }
  }, [user, fetchNotifications]);

  // Marquer une notification comme lue
  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationId }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      // Mettre à jour l'état local
      setNotifications(notifications.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Marquer toutes les notifications comme lues
  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ markAll: true }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      // Mettre à jour l'état local
      setNotifications([]);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Afficher une notification dans le navigateur
  const showBrowserNotification = useCallback((notification: Notification) => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      if (Notification.permission === 'granted') {
        // Créer la notification avec plus d'options
        const browserNotif = new Notification('Moonelec - Nouvelle demande de devis', {
          body: notification.message,
          icon: '/images/logo/logo-moonelec.png',
          tag: notification.id, // Évite les doublons
          requireInteraction: true, // La notification reste jusqu'à ce que l'utilisateur interagisse avec
          vibrate: [200, 100, 200], // Vibration pour les appareils mobiles
        });

        // Ajouter un gestionnaire d'événements pour le clic sur la notification
        browserNotif.onclick = () => {
          // Mettre le focus sur la fenêtre et fermer la notification
          window.focus();
          browserNotif.close();

          // Si la notification concerne un devis, rediriger vers la page du devis
          if (notification.quoteId) {
            window.location.href = `/admin/quotes/${notification.quoteId}`;
          }
        };
      } else if (Notification.permission !== 'denied') {
        // Demander la permission si elle n'a pas été refusée
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            showBrowserNotification(notification);
          }
        });
      }
    }
  }, []);

  // Initialiser le service worker et demander les permissions de notification
  useEffect(() => {
    if (user && user.role === 'ADMIN') {
      // Enregistrer le service worker
      registerServiceWorker();

      // Demander la permission pour les notifications
      const setupNotifications = async () => {
        const permissionGranted = await requestNotificationPermission();

        if (permissionGranted) {
          // S'abonner aux notifications push
          await subscribeToPushNotifications();
        }
      };

      setupNotifications();
    }
  }, [user]);

  // Vérifier périodiquement les nouvelles notifications et afficher des notifications dans le navigateur
  useEffect(() => {
    // Fonction pour vérifier les nouvelles notifications
    const checkForNewNotifications = async () => {
      if (!user || user.role !== 'ADMIN') return;

      try {
        const prevNotificationsCount = notifications.length;
        await fetchNotifications();

        // Si de nouvelles notifications sont arrivées, afficher la plus récente
        if (notifications.length > prevNotificationsCount && notifications.length > 0) {
          showBrowserNotification(notifications[0]);
        }
      } catch (error) {
        console.error('Error checking for new notifications:', error);
      }
    };

    // Vérifier immédiatement au chargement
    checkForNewNotifications();

    // Configurer une vérification périodique (toutes les 30 secondes)
    const interval = setInterval(checkForNewNotifications, 30000);

    return () => clearInterval(interval);
  }, [user, fetchNotifications, notifications.length, showBrowserNotification]);

  // Afficher une notification dans le navigateur quand une nouvelle notification arrive
  useEffect(() => {
    if (notifications.length > 0) {
      const latestNotification = notifications[0];
      showBrowserNotification(latestNotification);
    }
  }, [notifications, showBrowserNotification]);

  // Ne pas afficher le composant si l'utilisateur n'est pas un administrateur
  if (!user || user.role !== 'ADMIN') {
    return null;
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white focus:outline-none"
        aria-label="Notifications"
      >
        <FaBell className="text-xl" />
        {notifications.length > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {notifications.length}
          </span>
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden"
          >
            <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">
                Notifications
              </h3>
              {notifications.length > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Tout marquer comme lu
                </button>
              )}
            </div>

            <div className="max-h-96 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Chargement...
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Aucune notification
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className="p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <p className="text-sm text-gray-800 dark:text-gray-200">
                          {notification.message}
                        </p>
                        {notification.quote && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Devis: {notification.quote.quoteNumber}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {formatDistanceToNow(new Date(notification.createdAt), {
                            addSuffix: true,
                            locale: fr,
                          })}
                        </p>
                      </div>
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="text-xs text-blue-600 dark:text-blue-400 hover:underline ml-2"
                      >
                        Marquer comme lu
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
