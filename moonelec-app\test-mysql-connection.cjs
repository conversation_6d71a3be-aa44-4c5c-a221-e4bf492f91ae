const mysql = require('mysql2/promise');

async function testMySQLConnection() {
  try {
    console.log('🔍 Testing MySQL connection...');
    
    // Test different possible configurations
    const configs = [
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: '',
        database: 'moonelec_db'
      },
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'root',
        database: 'moonelec_db'
      },
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: '',
        // No database specified - just test connection
      }
    ];

    for (let i = 0; i < configs.length; i++) {
      const config = configs[i];
      console.log(`\n${i + 1}. Testing config:`, {
        host: config.host,
        port: config.port,
        user: config.user,
        hasPassword: !!config.password,
        database: config.database || 'none'
      });

      try {
        const connection = await mysql.createConnection(config);
        console.log('✅ Connection successful!');
        
        // Test if we can query
        if (config.database) {
          try {
            const [tables] = await connection.execute('SHOW TABLES');
            console.log('✅ Database accessible, tables found:', tables.length);
            
            // Check for specific tables
            const tableNames = tables.map(row => Object.values(row)[0]);
            console.log('📋 Tables:', tableNames);
            
            // Check if users table exists and has data
            if (tableNames.includes('user')) {
              const [users] = await connection.execute('SELECT id, username, email, role FROM user LIMIT 5');
              console.log('👥 Users found:', users.length);
              users.forEach(user => {
                console.log(`  - ${user.username} (${user.email}) - ${user.role}`);
              });
            }
            
            // Check brands
            if (tableNames.includes('brand')) {
              const [brands] = await connection.execute('SELECT id, name FROM brand LIMIT 5');
              console.log('🏷️ Brands found:', brands.length);
              brands.forEach(brand => {
                console.log(`  - ${brand.name}`);
              });
            }
            
          } catch (dbError) {
            console.log('❌ Database query failed:', dbError.message);
          }
        } else {
          // Just test basic connection
          const [result] = await connection.execute('SELECT 1 as test');
          console.log('✅ Basic query successful:', result);
        }
        
        await connection.end();
        console.log('✅ This configuration works!');
        
        // If we found a working config, use it
        if (config.database) {
          console.log('\n🎯 RECOMMENDED DATABASE_URL:');
          const password = config.password ? `:${config.password}` : '';
          console.log(`DATABASE_URL="mysql://${config.user}${password}@${config.host}:${config.port}/${config.database}"`);
        }
        
        return; // Exit on first successful connection
        
      } catch (error) {
        console.log('❌ Connection failed:', error.message);
      }
    }
    
    console.log('\n❌ All connection attempts failed');
    console.log('💡 Please check:');
    console.log('  - MySQL server is running');
    console.log('  - Database "moonelec_db" exists');
    console.log('  - User credentials are correct');
    console.log('  - Port 3306 is accessible');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMySQLConnection();
