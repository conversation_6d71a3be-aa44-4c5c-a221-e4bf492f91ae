import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { RootStackParamList, BottomTabParamList, UserRole } from '../types';

// Import screens
import LoginScreen from '../screens/auth/LoginScreen';
import HomeScreen from '../screens/HomeScreen';
import ProductsScreen from '../screens/products/ProductsScreen';
import ProductDetailScreen from '../screens/products/ProductDetailScreen';
import CategoriesScreen from '../screens/categories/CategoriesScreen';
import CategoryProductsScreen from '../screens/categories/CategoryProductsScreen';
import QuotesScreen from '../screens/quotes/QuotesScreen';
import QuoteDetailScreen from '../screens/quotes/QuoteDetailScreen';
import CreateQuoteScreen from '../screens/quotes/CreateQuoteScreen';
import SalesReportsScreen from '../screens/sales-reports/SalesReportsScreen';
import SalesReportDetailScreen from '../screens/sales-reports/SalesReportDetailScreen';
import CreateSalesReportScreen from '../screens/sales-reports/CreateSalesReportScreen';
import ChatScreen from '../screens/chat/ChatScreen';
import ChatConversationScreen from '../screens/chat/ChatConversationScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LoadingScreen from '../screens/LoadingScreen';
import ManageCategoriesScreen from '../screens/admin/ManageCategoriesScreen';
import ManageProductsScreen from '../screens/admin/ManageProductsScreen';
import CommercialReportsScreen from '../screens/admin/CommercialReportsScreen';
import TestConnectionScreen from '../screens/TestConnectionScreen';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<BottomTabParamList>();

const TabNavigator: React.FC = () => {
  const { user } = useAuth();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Products') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Quotes') {
            iconName = focused ? 'document-text' : 'document-text-outline';
          } else if (route.name === 'Reports') {
            iconName = focused ? 'clipboard' : 'clipboard-outline';
          } else if (route.name === 'Chat') {
            iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#006db7',
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: '#006db7',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ title: 'Accueil' }}
      />
      <Tab.Screen
        name="Products"
        component={ProductsScreen}
        options={{ title: 'Produits' }}
      />
      <Tab.Screen
        name="Quotes"
        component={QuotesScreen}
        options={{ title: 'Devis' }}
      />
      {(user?.role === UserRole.COMMERCIAL || user?.role === UserRole.ADMIN) && (
        <Tab.Screen
          name="Reports"
          component={user?.role === UserRole.ADMIN ? CommercialReportsScreen : SalesReportsScreen}
          options={{ title: 'Rapports' }}
        />
      )}
      {(user?.role === UserRole.ADMIN || user?.role === UserRole.COMMERCIAL) && (
        <Tab.Screen
          name="Chat"
          component={ChatScreen}
          options={{ title: 'Chat' }}
        />
      )}
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'Profil' }}
      />
    </Tab.Navigator>
  );
};

const AppNavigator: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <>
            <Stack.Screen name="Main" component={TabNavigator} />
            <Stack.Screen
              name="ProductDetail"
              component={ProductDetailScreen}
              options={{
                headerShown: true,
                title: 'Détail du produit',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="QuoteDetail"
              component={QuoteDetailScreen}
              options={{
                headerShown: true,
                title: 'Détail du devis',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="CreateQuote"
              component={CreateQuoteScreen}
              options={{
                headerShown: true,
                title: 'Nouveau devis',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="Categories"
              component={CategoriesScreen}
              options={{
                headerShown: true,
                title: 'Catégories',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="CategoryProducts"
              component={CategoryProductsScreen}
              options={{
                headerShown: true,
                title: 'Produits',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="SalesReportDetail"
              component={SalesReportDetailScreen}
              options={{
                headerShown: true,
                title: 'Détail du rapport',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="CreateSalesReport"
              component={CreateSalesReportScreen}
              options={{
                headerShown: true,
                title: 'Nouveau rapport',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="ChatConversation"
              component={ChatConversationScreen}
              options={{
                headerShown: true,
                title: 'Conversation',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="ManageCategories"
              component={ManageCategoriesScreen}
              options={{
                headerShown: true,
                title: 'Gestion des catégories',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="ManageProducts"
              component={ManageProductsScreen}
              options={{
                headerShown: true,
                title: 'Gestion des produits',
                headerStyle: { backgroundColor: '#006db7' },
                headerTintColor: '#fff'
              }}
            />
            <Stack.Screen
              name="TestConnection"
              component={TestConnectionScreen}
              options={{
                headerShown: false,
              }}
            />
          </>
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
