#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Démarrage de Moonelec Mobile...\n');

// Vérifier que nous sommes dans le bon répertoire
if (!fs.existsSync('package.json')) {
  console.error('❌ Erreur: package.json non trouvé. Assurez-vous d\'être dans le répertoire moonelec-mobile');
  process.exit(1);
}

// Vérifier que node_modules existe
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installation des dépendances...');
  const install = spawn('npm', ['install'], { stdio: 'inherit' });
  install.on('close', (code) => {
    if (code === 0) {
      startExpo();
    } else {
      console.error('❌ Erreur lors de l\'installation des dépendances');
      process.exit(1);
    }
  });
} else {
  startExpo();
}

function startExpo() {
  console.log('🎯 Démarrage d\'Expo...\n');
  
  // Afficher les informations importantes
  console.log('📱 Moonelec Mobile - Application de Distribution Électrique');
  console.log('🎨 Design professionnel avec animations');
  console.log('⚡ Thème électrique avec icônes spécialisées');
  console.log('🔐 Authentification robuste (JWT + NextAuth)');
  console.log('');
  console.log('📋 Fonctionnalités:');
  console.log('   ✅ Catalogue produits avec recherche');
  console.log('   ✅ Navigation par catégories');
  console.log('   ✅ Création et gestion de devis');
  console.log('   ✅ Rapports de visite (commerciaux)');
  console.log('   ✅ Multimédia (photos, vidéos, audio)');
  console.log('');
  console.log('🎯 Comptes de test:');
  console.log('   Admin: admin / admin123');
  console.log('   Commercial: commercial / commercial123');
  console.log('   Client: client / client123');
  console.log('');
  console.log('⚠️  Important: Vérifiez l\'URL API dans src/services/api.ts');
  console.log('   Actuellement: http://localhost:3000/api');
  console.log('');
  
  // Démarrer Expo
  const expo = spawn('npx', ['expo', 'start'], { 
    stdio: 'inherit',
    shell: true 
  });
  
  expo.on('error', (error) => {
    console.error('❌ Erreur lors du démarrage d\'Expo:', error.message);
    console.log('');
    console.log('🔧 Solutions possibles:');
    console.log('   1. Installez Expo CLI: npm install -g @expo/cli');
    console.log('   2. Nettoyez le cache: npx expo start --clear');
    console.log('   3. Réinstallez les dépendances: rm -rf node_modules && npm install');
  });
  
  expo.on('close', (code) => {
    if (code !== 0) {
      console.log(`\n❌ Expo s'est arrêté avec le code ${code}`);
    }
  });
}

// Gestion des signaux pour un arrêt propre
process.on('SIGINT', () => {
  console.log('\n👋 Arrêt de l\'application...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Arrêt de l\'application...');
  process.exit(0);
});
