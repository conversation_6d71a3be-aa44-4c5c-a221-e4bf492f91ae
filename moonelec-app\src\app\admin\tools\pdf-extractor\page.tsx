'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import PdfExtractor from '@/components/admin/PdfExtractor';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
  category?: string;
  brand?: string;
  price?: number;
}

export default function PdfExtractorPage() {
  const { user } = useAuth();
  const [extractedData, setExtractedData] = useState<ExtractedProductData | null>(null);
  const [extractionHistory, setExtractionHistory] = useState<ExtractedProductData[]>([]);

  const handleDataExtracted = (data: ExtractedProductData) => {
    setExtractedData(data);
    setExtractionHistory(prev => [data, ...prev.slice(0, 4)]); // Keep last 5 extractions
  };

  const createProductFromData = async (data: ExtractedProductData) => {
    try {
      // Convertir les caractéristiques en objet
      const characteristicsObject = data.characteristics || {};

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reference: Array.isArray(data.reference) ? data.reference[0] : data.reference,
          name: data.productName,
          description: data.description,
          characteristics: characteristicsObject,
          mainImage: null,
          categoryId: null,
          brandId: null,
          images: [],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      const product = await response.json();
      
      // Rediriger vers la page d'édition du produit
      window.location.href = `/admin/products/${product.id}/edit`;
    } catch (error) {
      console.error('Error creating product:', error);
      alert('Erreur lors de la création du produit: ' + (error as Error).message);
    }
  };

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🤖 Extracteur PDF IA</h1>
            <p className="text-gray-600 mt-2">
              Extrayez automatiquement les données produit depuis des fiches techniques PDF
            </p>
          </div>
          <Link href="/admin/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
            >
              Retour aux produits
            </motion.button>
          </Link>
        </div>

        {/* Extracteur principal */}
        <div className="mb-8">
          <PdfExtractor onDataExtracted={handleDataExtracted} />
        </div>

        {/* Données extraites actuelles */}
        {extractedData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8 bg-white rounded-lg shadow-lg p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">📋 Dernière extraction</h2>
              <div className="flex space-x-3">
                <button
                  onClick={() => createProductFromData(extractedData)}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  ✅ Créer le produit
                </button>
                <Link href="/admin/products/new">
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    ✏️ Éditer avant création
                  </button>
                </Link>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Informations générales</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex">
                    <span className="font-medium w-24">Nom:</span>
                    <span className="text-gray-700">{extractedData.productName}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium w-24">Référence:</span>
                    <span className="text-gray-700">
                      {Array.isArray(extractedData.reference) 
                        ? extractedData.reference.join(', ')
                        : extractedData.reference}
                    </span>
                  </div>
                  {extractedData.category && (
                    <div className="flex">
                      <span className="font-medium w-24">Catégorie:</span>
                      <span className="text-gray-700">{extractedData.category}</span>
                    </div>
                  )}
                  {extractedData.brand && (
                    <div className="flex">
                      <span className="font-medium w-24">Marque:</span>
                      <span className="text-gray-700">{extractedData.brand}</span>
                    </div>
                  )}
                  {extractedData.price && (
                    <div className="flex">
                      <span className="font-medium w-24">Prix:</span>
                      <span className="text-gray-700">{extractedData.price}€</span>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Caractéristiques techniques</h3>
                <div className="space-y-1 text-sm">
                  {Object.entries(extractedData.characteristics || {}).map(([key, value]) => (
                    <div key={key} className="flex">
                      <span className="font-medium w-32">{key}:</span>
                      <span className="text-gray-700">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-4">
              <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
              <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded border">
                {extractedData.description}
              </p>
            </div>
          </motion.div>
        )}

        {/* Historique des extractions */}
        {extractionHistory.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-lg p-6"
          >
            <h2 className="text-xl font-bold text-gray-900 mb-4">📚 Historique des extractions</h2>
            <div className="space-y-4">
              {extractionHistory.map((data, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">{data.productName}</h3>
                      <p className="text-sm text-gray-600">
                        Ref: {Array.isArray(data.reference) ? data.reference.join(', ') : data.reference}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setExtractedData(data)}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      >
                        Voir
                      </button>
                      <button
                        onClick={() => createProductFromData(data)}
                        className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                      >
                        Créer
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Instructions d'utilisation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-8 bg-blue-50 rounded-lg p-6"
        >
          <h2 className="text-lg font-bold text-blue-900 mb-3">💡 Comment utiliser l'extracteur PDF</h2>
          <div className="space-y-2 text-sm text-blue-800">
            <p>• <strong>Formats supportés:</strong> Fichiers PDF uniquement (max 10MB)</p>
            <p>• <strong>Contenu optimal:</strong> Fiches techniques avec texte lisible (pas d'images scannées)</p>
            <p>• <strong>Données extraites:</strong> Nom, référence, description, caractéristiques techniques</p>
            <p>• <strong>IA utilisée:</strong> OpenAI GPT-3.5 pour l'analyse intelligente du contenu</p>
            <p>• <strong>Après extraction:</strong> Vérifiez et modifiez les données avant de créer le produit</p>
          </div>
        </motion.div>
      </div>
    </RouteGuard>
  );
}
