'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaList,
  FaStar,
  FaHeart,
  FaShoppingCart,
  FaEye,
  FaChevronDown,
  FaTimes
} from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';

interface Product {
  id: string;
  name: string;
  reference: string;
  description: string;
  price: number;
  mainImage: string;
  category: {
    id: string;
    name: string;
  };
  brand: {
    id: string;
    name: string;
  };
  rating?: number;
  isNew?: boolean;
  isOnSale?: boolean;
  originalPrice?: number;
}

interface ModernProductsPageProps {
  initialProducts: Product[];
  categories: Array<{ id: string; name: string; }>;
  brands: Array<{ id: string; name: string; }>;
  searchParams: {
    search?: string;
    category?: string;
    brand?: string;
    page?: string;
  };
}

export default function ModernProductsPage({ 
  initialProducts, 
  categories, 
  brands, 
  searchParams 
}: ModernProductsPageProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [priceRange, setPriceRange] = useState([0, 10000]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState(searchParams.search || '');

  const sortOptions = [
    { value: 'name', label: 'Nom A-Z' },
    { value: 'name-desc', label: 'Nom Z-A' },
    { value: 'price', label: 'Prix croissant' },
    { value: 'price-desc', label: 'Prix décroissant' },
    { value: 'newest', label: 'Plus récents' },
    { value: 'rating', label: 'Mieux notés' }
  ];

  const ProductCard = ({ product }: { product: Product }) => (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
    >
      {/* Product Image */}
      <div className="relative aspect-square bg-light-gray overflow-hidden">
        {product.mainImage ? (
          <Image
            src={product.mainImage}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-200">
            <div className="w-20 h-20 bg-gray-300 rounded-lg"></div>
          </div>
        )}
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col space-y-2">
          {product.isNew && (
            <span className="px-2 py-1 bg-electric-blue text-white text-xs font-semibold rounded">
              NOUVEAU
            </span>
          )}
          {product.isOnSale && (
            <span className="px-2 py-1 bg-moonelec-red text-white text-xs font-semibold rounded">
              PROMO
            </span>
          )}
        </div>

        {/* Quick Actions */}
        <div className="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-moonelec-red rounded-full shadow-md transition-colors">
            <FaHeart className="text-sm" />
          </button>
          <Link
            href={`/products/${product.id}`}
            className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-electric-blue rounded-full shadow-md transition-colors"
          >
            <FaEye className="text-sm" />
          </Link>
        </div>

        {/* Add to Cart Overlay */}
        <div className="absolute inset-x-0 bottom-0 p-4 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button className="w-full btn btn-primary text-sm py-2">
            <FaShoppingCart className="mr-2" />
            Ajouter au Panier
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Category & Brand */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
          <span className="bg-gray-100 px-2 py-1 rounded">{product.category.name}</span>
          <span className="font-medium">{product.brand.name}</span>
        </div>

        {/* Product Name */}
        <h3 className="font-semibold text-charcoal mb-2 line-clamp-2 group-hover:text-moonelec-red transition-colors">
          {product.name}
        </h3>

        {/* Reference */}
        <p className="text-sm text-gray-500 mb-2">Réf: {product.reference}</p>

        {/* Rating */}
        {product.rating && (
          <div className="flex items-center space-x-1 mb-3">
            {[...Array(5)].map((_, i) => (
              <FaStar
                key={i}
                className={`text-xs ${
                  i < product.rating! ? 'text-yellow-400' : 'text-gray-300'
                }`}
              />
            ))}
            <span className="text-xs text-gray-500 ml-1">({product.rating})</span>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-moonelec-red">
              {product.price.toLocaleString('fr-FR')} MAD
            </span>
            {product.originalPrice && (
              <span className="text-sm text-gray-500 line-through">
                {product.originalPrice.toLocaleString('fr-FR')} MAD
              </span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );

  const ProductListItem = ({ product }: { product: Product }) => (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100"
    >
      <div className="flex">
        {/* Product Image */}
        <div className="relative w-48 h-48 bg-light-gray flex-shrink-0">
          {product.mainImage ? (
            <Image
              src={product.mainImage}
              alt={product.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <div className="w-20 h-20 bg-gray-300 rounded-lg"></div>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="flex-1 p-6">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xs bg-gray-100 px-2 py-1 rounded">{product.category.name}</span>
                <span className="text-xs text-gray-500">{product.brand.name}</span>
              </div>
              
              <h3 className="text-xl font-semibold text-charcoal mb-2 group-hover:text-moonelec-red transition-colors">
                {product.name}
              </h3>
              
              <p className="text-sm text-gray-500 mb-2">Réf: {product.reference}</p>
              
              <p className="text-gray-600 mb-4 line-clamp-2">{product.description}</p>
              
              {product.rating && (
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <FaStar
                      key={i}
                      className={`text-sm ${
                        i < product.rating! ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    />
                  ))}
                  <span className="text-sm text-gray-500 ml-2">({product.rating})</span>
                </div>
              )}
            </div>

            <div className="text-right">
              <div className="mb-4">
                <span className="text-2xl font-bold text-moonelec-red">
                  {product.price.toLocaleString('fr-FR')} MAD
                </span>
                {product.originalPrice && (
                  <div className="text-sm text-gray-500 line-through">
                    {product.originalPrice.toLocaleString('fr-FR')} MAD
                  </div>
                )}
              </div>
              
              <div className="flex space-x-2">
                <button className="p-2 border border-gray-200 hover:border-moonelec-red text-gray-600 hover:text-moonelec-red rounded-lg transition-colors">
                  <FaHeart />
                </button>
                <Link
                  href={`/products/${product.id}`}
                  className="p-2 border border-gray-200 hover:border-electric-blue text-gray-600 hover:text-electric-blue rounded-lg transition-colors"
                >
                  <FaEye />
                </Link>
                <button className="btn btn-primary px-6">
                  <FaShoppingCart className="mr-2" />
                  Ajouter
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-light-gray">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            {/* Search */}
            <div className="flex-1 max-w-2xl">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Rechercher des produits..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent"
                />
                <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              {/* Sort */}
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="appearance-none bg-white border border-gray-200 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-electric-blue"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
              </div>

              {/* View Mode */}
              <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${
                    viewMode === 'grid'
                      ? 'bg-electric-blue text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  } transition-colors`}
                >
                  <FaTh />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${
                    viewMode === 'list'
                      ? 'bg-electric-blue text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  } transition-colors`}
                >
                  <FaList />
                </button>
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn btn-secondary flex items-center space-x-2"
              >
                <FaFilter />
                <span>Filtres</span>
              </button>
            </div>
          </div>

          {/* Active Filters */}
          {(selectedCategories.length > 0 || selectedBrands.length > 0) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {selectedCategories.map((categoryId) => {
                const category = categories.find(c => c.id === categoryId);
                return (
                  <span
                    key={categoryId}
                    className="inline-flex items-center space-x-2 bg-electric-blue/10 text-electric-blue px-3 py-1 rounded-full text-sm"
                  >
                    <span>{category?.name}</span>
                    <button
                      onClick={() => setSelectedCategories(prev => prev.filter(id => id !== categoryId))}
                      className="hover:text-electric-blue-dark"
                    >
                      <FaTimes className="text-xs" />
                    </button>
                  </span>
                );
              })}
              {selectedBrands.map((brandId) => {
                const brand = brands.find(b => b.id === brandId);
                return (
                  <span
                    key={brandId}
                    className="inline-flex items-center space-x-2 bg-moonelec-red/10 text-moonelec-red px-3 py-1 rounded-full text-sm"
                  >
                    <span>{brand?.name}</span>
                    <button
                      onClick={() => setSelectedBrands(prev => prev.filter(id => id !== brandId))}
                      className="hover:text-moonelec-red-dark"
                    >
                      <FaTimes className="text-xs" />
                    </button>
                  </span>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, x: -300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -300 }}
                className="w-80 bg-white rounded-xl shadow-lg p-6 h-fit sticky top-8"
              >
                <h3 className="text-lg font-semibold text-charcoal mb-6">Filtres</h3>
                
                {/* Categories */}
                <div className="mb-6">
                  <h4 className="font-medium text-charcoal mb-3">Catégories</h4>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedCategories.includes(category.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedCategories(prev => [...prev, category.id]);
                            } else {
                              setSelectedCategories(prev => prev.filter(id => id !== category.id));
                            }
                          }}
                          className="rounded border-gray-300 text-electric-blue focus:ring-electric-blue"
                        />
                        <span className="text-sm text-gray-700">{category.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Brands */}
                <div className="mb-6">
                  <h4 className="font-medium text-charcoal mb-3">Marques</h4>
                  <div className="space-y-2">
                    {brands.map((brand) => (
                      <label key={brand.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedBrands.includes(brand.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedBrands(prev => [...prev, brand.id]);
                            } else {
                              setSelectedBrands(prev => prev.filter(id => id !== brand.id));
                            }
                          }}
                          className="rounded border-gray-300 text-moonelec-red focus:ring-moonelec-red"
                        />
                        <span className="text-sm text-gray-700">{brand.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Clear Filters */}
                <button
                  onClick={() => {
                    setSelectedCategories([]);
                    setSelectedBrands([]);
                    setPriceRange([0, 10000]);
                  }}
                  className="w-full btn btn-secondary text-sm"
                >
                  Effacer les Filtres
                </button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Products Grid/List */}
          <div className="flex-1">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(12)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl p-4 animate-pulse">
                    <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-4"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                  : 'space-y-6'
              }>
                <AnimatePresence>
                  {products.map((product) => (
                    viewMode === 'grid' ? (
                      <ProductCard key={product.id} product={product} />
                    ) : (
                      <ProductListItem key={product.id} product={product} />
                    )
                  ))}
                </AnimatePresence>
              </div>
            )}

            {/* No Results */}
            {!isLoading && products.length === 0 && (
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <FaSearch className="text-gray-400 text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-charcoal mb-2">Aucun produit trouvé</h3>
                <p className="text-gray-600">Essayez de modifier vos critères de recherche</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
