import { NextRequest, NextResponse } from 'next/server';
import { getCommercialById, updateCommercial, deleteCommercial } from '@/lib/commercials';

// GET /api/commercials/[id] - Récupérer un commercial par son ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const commercial = await getCommercialById(id);

    if (!commercial) {
      return NextResponse.json(
        { error: 'Commercial not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(commercial);
  } catch (error: any) {
    console.error('Error fetching commercial:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch commercial' },
      { status: 500 }
    );
  }
}

// PATCH /api/commercials/[id] - Mettre à jour un commercial
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await req.json();
    const { email, firstname, lastname, telephone, profile_photo } = body;

    // Vérifier qu'au moins un champ est fourni
    if (!email && !firstname && !lastname && !telephone && profile_photo === undefined) {
      return NextResponse.json(
        { error: 'At least one field must be provided for update' },
        { status: 400 }
      );
    }

    // Mettre à jour le commercial
    const commercial = await updateCommercial(id, {
      email,
      firstname,
      lastname,
      telephone,
      profile_photo,
    });

    return NextResponse.json(commercial);
  } catch (error: any) {
    console.error('Error updating commercial:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update commercial' },
      { status: 500 }
    );
  }
}

// DELETE /api/commercials/[id] - Supprimer un commercial
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    await deleteCommercial(id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting commercial:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete commercial' },
      { status: 500 }
    );
  }
}
