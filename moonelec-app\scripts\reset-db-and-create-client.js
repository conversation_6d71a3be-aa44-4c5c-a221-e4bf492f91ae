import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Vérification de l\'existence de l\'administrateur...');
    
    // Vérifier si l'administrateur existe
    const adminUser = await prisma.user.findUnique({
      where: {
        username: 'hicham.ezzamzami',
      },
      include: {
        admin: true,
      },
    });

    if (!adminUser) {
      console.log('L\'administrateur n\'existe pas. Veuillez d\'abord créer un compte administrateur.');
      return;
    }

    console.log('Administrateur trouvé:', adminUser.firstname, adminUser.lastname);

    // Supprimer toutes les données sauf l'administrateur
    console.log('Suppression des données existantes...');

    // 1. Supprimer tous les devis et leurs éléments
    await prisma.quoteItem.deleteMany({});
    await prisma.quote.deleteMany({});

    // 2. Supprimer toutes les commandes et leurs éléments
    await prisma.orderItem.deleteMany({});
    await prisma.order.deleteMany({});

    // 3. Supprimer toutes les images de produits
    await prisma.productImage.deleteMany({});

    // 4. Supprimer tous les produits
    await prisma.product.deleteMany({});

    // 5. Supprimer toutes les catégories
    await prisma.category.deleteMany({});

    // 6. Supprimer toutes les marques
    await prisma.brand.deleteMany({});

    // 7. Supprimer les relations entre commerciaux et clients
    await prisma.commercialClient.deleteMany({});

    // 8. Supprimer tous les clients sauf celui lié à l'administrateur (s'il existe)
    await prisma.client.deleteMany({
      where: {
        NOT: {
          userId: adminUser.id,
        },
      },
    });

    // 9. Supprimer tous les commerciaux
    await prisma.commercial.deleteMany({});

    // 10. Supprimer tous les utilisateurs sauf l'administrateur
    await prisma.user.deleteMany({
      where: {
        NOT: {
          id: adminUser.id,
        },
      },
    });

    console.log('Données supprimées avec succès.');

    // Créer un compte client de test
    console.log('Création d\'un compte client de test...');

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash('123456', 10);

    // Créer l'utilisateur client
    const clientUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'client.test',
        password: hashedPassword,
        firstname: 'Client',
        lastname: 'Test',
        telephone: '+212600000001',
        role: 'CLIENT',
        client: {
          create: {
            company_name: 'Entreprise Test',
          },
        },
      },
      include: {
        client: true,
      },
    });

    console.log('Compte client créé avec succès:');
    console.log('- Email:', clientUser.email);
    console.log('- Nom d\'utilisateur:', clientUser.username);
    console.log('- Mot de passe: 123456');
    console.log('- Nom complet:', clientUser.firstname, clientUser.lastname);
    console.log('- Entreprise:', clientUser.client.company_name);

    // Créer quelques catégories de base
    console.log('Création de catégories de base...');
    
    const categories = await Promise.all([
      prisma.category.create({
        data: {
          name: 'Éclairage',
          description: 'Produits d\'éclairage pour intérieur et extérieur',
          image: '/categories/eclairage.jpg',
        },
      }),
      prisma.category.create({
        data: {
          name: 'Câblage',
          description: 'Câbles électriques et accessoires',
          image: '/categories/cablage.jpg',
        },
      }),
      prisma.category.create({
        data: {
          name: 'Appareillage',
          description: 'Interrupteurs, prises et autres appareillages',
          image: '/categories/appareillage.jpg',
        },
      }),
    ]);

    console.log('Catégories créées:', categories.map(c => c.name).join(', '));

    // Créer quelques marques de base
    console.log('Création de marques de base...');
    
    const brands = await Promise.all([
      prisma.brand.create({
        data: {
          name: 'Schneider Electric',
          image: '/marques/schneider.png',
        },
      }),
      prisma.brand.create({
        data: {
          name: 'Legrand',
          image: '/marques/legrand.png',
        },
      }),
      prisma.brand.create({
        data: {
          name: 'Philips',
          image: '/marques/philips.png',
        },
      }),
    ]);

    console.log('Marques créées:', brands.map(b => b.name).join(', '));

    // Créer quelques produits de base
    console.log('Création de produits de base...');
    
    const products = await Promise.all([
      prisma.product.create({
        data: {
          reference: 'LED-A60-10W',
          name: 'Ampoule LED A60 10W E27 Blanc Chaud 2700K',
          description: 'Ampoule LED A60 de 10W avec culot E27, offrant une lumière blanc chaud à 2700K. Idéale pour un éclairage général dans les espaces résidentiels.',
          characteristics: {
            puissance: '10W',
            culot: 'E27',
            temperature: '2700K',
            flux_lumineux: '806 lumens',
            duree_vie: '15000 heures',
          },
          mainImage: '/products/ampoule-led.jpg',
          categoryId: categories[0].id, // Éclairage
          brandId: brands[2].id, // Philips
        },
      }),
      prisma.product.create({
        data: {
          reference: 'CABLE-3G1.5',
          name: 'Câble électrique 3G1.5 mm² - 100m',
          description: 'Câble électrique 3G1.5 mm² pour installations domestiques. Rouleau de 100 mètres.',
          characteristics: {
            section: '3G1.5 mm²',
            longueur: '100m',
            tension: '450/750V',
            materiau: 'Cuivre',
            norme: 'NF C 32-102',
          },
          mainImage: '/products/cable-electrique.jpg',
          categoryId: categories[1].id, // Câblage
          brandId: brands[0].id, // Schneider Electric
        },
      }),
      prisma.product.create({
        data: {
          reference: 'PRISE-2P-T',
          name: 'Prise de courant 2P+T Blanc',
          description: 'Prise de courant 2P+T blanche pour installation domestique.',
          characteristics: {
            type: '2P+T',
            couleur: 'Blanc',
            intensite: '16A',
            tension: '250V',
            indice_protection: 'IP21',
          },
          mainImage: '/products/prise-courant.jpg',
          categoryId: categories[2].id, // Appareillage
          brandId: brands[1].id, // Legrand
        },
      }),
    ]);

    console.log('Produits créés:', products.map(p => p.reference).join(', '));

    // Créer un devis de test pour le client
    console.log('Création d\'un devis de test...');
    
    const quote = await prisma.quote.create({
      data: {
        quoteNumber: 'Q-2023-001',
        clientId: clientUser.client.id,
        status: 'PENDING',
        totalAmount: 3500,
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
        notes: 'Devis de test pour démonstration',
        createdByAdminId: adminUser.admin.id,
        quoteItems: {
          create: [
            {
              productId: products[0].id, // Ampoule LED
              quantity: 50,
              unitPrice: 35,
            },
            {
              productId: products[1].id, // Câble électrique
              quantity: 2,
              unitPrice: 850,
            },
          ],
        },
      },
      include: {
        quoteItems: true,
      },
    });

    console.log('Devis créé avec succès:', quote.quoteNumber);
    console.log('Montant total:', quote.totalAmount, 'MAD');
    console.log('Nombre d\'articles:', quote.quoteItems.length);

    console.log('\nRéinitialisation de la base de données terminée avec succès!');
    console.log('\nInformations de connexion pour le client de test:');
    console.log('- Nom d\'utilisateur: client.test');
    console.log('- Mot de passe: 123456');

  } catch (error) {
    console.error('Erreur lors de la réinitialisation de la base de données:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
