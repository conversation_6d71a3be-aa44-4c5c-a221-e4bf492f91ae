{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|public/).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hvnTpWLaEzuVkzjdbHOe2qlbVBbeVIRJyR/dQh8wvdg=", "__NEXT_PREVIEW_MODE_ID": "ab4bd32cb5492447916329b940b72540", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c6b27823f16c8e612e8c7bdf1231b262807f60ce1e80e2d05d63c1d6cc00b4f8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f49552e50a35d2edf74c9e955c9f98d46a1f493344a60aaf89a5281d6152cf22"}}}, "instrumentation": null, "functions": {}}