'use client';

import React, { useState } from 'react';
import { handleError, handleSuccess, handleWarning, handleLoading, dismissLoading } from '@/lib/error-handler';
import ApiService from '@/lib/api';

// Example component showing how to use French error handling in web app
const ErrorHandlingExample: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  // Example: API call with error handling
  const handleApiCall = async () => {
    const loadingToast = handleLoading('Chargement des produits...');
    setIsLoading(true);

    try {
      // This will automatically show French error messages if it fails
      const products = await ApiService.get('/products', {}, true);
      
      dismissLoading(loadingToast);
      handleSuccess('SAVE_SUCCESS', 'Produits chargés avec succès !');
      console.log('Products loaded:', products);
    } catch (error) {
      dismissLoading(loadingToast);
      // Error is already handled by ApiService with French messages
      console.error('Failed to load products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Example: File upload with error handling
  const handleFileUpload = async (file: File) => {
    try {
      // Validate file before upload
      if (!file) {
        handleError({ message: 'NO_FILE_SELECTED' }, 'Téléchargement de fichier');
        return;
      }

      if (file.size > 25 * 1024 * 1024) {
        handleError({ message: 'FILE_TOO_LARGE' }, 'Téléchargement de fichier');
        return;
      }

      // Upload with automatic error handling
      const result = await ApiService.uploadFile('/upload', file, {}, true);
      handleSuccess('UPLOAD_SUCCESS', 'Fichier téléchargé avec succès !');
      console.log('Upload result:', result);
    } catch (error) {
      // Error is already handled by ApiService
      console.error('Upload failed:', error);
    }
  };

  // Example: Form validation with French errors
  const handleFormSubmit = async (formData: any) => {
    try {
      // Validate required fields
      if (!formData.email) {
        handleError({ message: 'REQUIRED_FIELD' }, 'Validation du formulaire', 'L\'email est obligatoire.');
        return;
      }

      if (!formData.email.includes('@')) {
        handleError({ message: 'INVALID_EMAIL' }, 'Validation du formulaire');
        return;
      }

      if (formData.password && formData.password.length < 8) {
        handleError({ message: 'PASSWORD_TOO_SHORT' }, 'Validation du formulaire');
        return;
      }

      // Submit form
      const result = await ApiService.post('/users', formData, true);
      handleSuccess('SAVE_SUCCESS', 'Utilisateur créé avec succès !');
      console.log('User created:', result);
    } catch (error) {
      // Error is already handled by ApiService
      console.error('Form submission failed:', error);
    }
  };

  // Example: Delete with confirmation
  const handleDelete = async (itemId: string) => {
    // Show warning first
    handleWarning('DELETE_CONFIRMATION');
    
    // In a real app, you'd use a proper confirmation dialog
    const confirmed = window.confirm('Êtes-vous sûr de vouloir supprimer cet élément ?');
    
    if (confirmed) {
      try {
        await ApiService.delete(`/items/${itemId}`, true);
        handleSuccess('DELETE_SUCCESS', 'Élément supprimé avec succès !');
      } catch (error) {
        // Error is already handled by ApiService
        console.error('Delete failed:', error);
      }
    }
  };

  // Example: Network error simulation
  const simulateNetworkError = () => {
    const networkError = {
      message: 'Failed to fetch',
      code: 'NETWORK_ERROR'
    };
    handleError(networkError, 'Test de réseau');
  };

  // Example: Different error types
  const simulateErrors = () => {
    // Unauthorized error
    setTimeout(() => {
      handleError({ status: 401 }, 'Test d\'authentification');
    }, 1000);

    // Validation error
    setTimeout(() => {
      handleError({ status: 422, message: 'Validation failed' }, 'Test de validation');
    }, 2000);

    // Server error
    setTimeout(() => {
      handleError({ status: 500 }, 'Test du serveur');
    }, 3000);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Exemples de Gestion d'Erreurs en Français
      </h2>

      <div className="space-y-4">
        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Appels API</h3>
          <div className="space-x-3">
            <button
              onClick={handleApiCall}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isLoading ? 'Chargement...' : 'Charger les Produits'}
            </button>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Téléchargement de Fichier</h3>
          <input
            type="file"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) handleFileUpload(file);
            }}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Validation de Formulaire</h3>
          <button
            onClick={() => handleFormSubmit({ email: '', password: '123' })}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Tester la Validation
          </button>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Suppression</h3>
          <button
            onClick={() => handleDelete('test-item-123')}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Supprimer un Élément
          </button>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Tests d'Erreurs</h3>
          <div className="space-x-3">
            <button
              onClick={simulateNetworkError}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Erreur Réseau
            </button>
            <button
              onClick={simulateErrors}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Différents Types d'Erreurs
            </button>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Messages de Succès</h3>
          <div className="space-x-3">
            <button
              onClick={() => handleSuccess('LOGIN_SUCCESS')}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Connexion Réussie
            </button>
            <button
              onClick={() => handleSuccess('SAVE_SUCCESS', 'Données sauvegardées avec succès !')}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Sauvegarde Réussie
            </button>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">Comment utiliser :</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <code>handleError(error, context, customMessage)</code> - Affiche une erreur en français</li>
          <li>• <code>handleSuccess(messageKey, customMessage)</code> - Affiche un message de succès</li>
          <li>• <code>handleWarning(messageKey, customMessage)</code> - Affiche un avertissement</li>
          <li>• <code>handleLoading(message)</code> - Affiche un indicateur de chargement</li>
          <li>• <code>ApiService.get/post/put/delete(endpoint, data, showLoading)</code> - API avec gestion d'erreurs automatique</li>
        </ul>
      </div>
    </div>
  );
};

export default ErrorHandlingExample;
