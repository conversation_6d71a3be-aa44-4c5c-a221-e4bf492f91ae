import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    // Créer des catégories
    const categories = await Promise.all([
      prisma.category.create({
        data: {
          name: 'Éclairage',
          description: 'Produits d\'éclairage pour intérieur et extérieur',
          image: '/images/categories/eclairage.jpg',
        },
      }),
      prisma.category.create({
        data: {
          name: 'Appareillage',
          description: 'Interrupteurs, prises et autres appareillages électriques',
          image: '/images/categories/appareillage.jpg',
        },
      }),
      prisma.category.create({
        data: {
          name: 'Câblage',
          description: 'Câbles et accessoires pour installations électriques',
          image: '/images/categories/cablage.jpg',
        },
      }),
    ]);

    console.log('Catégories créées:', categories);

    // Créer des marques
    const brands = await Promise.all([
      prisma.brand.create({
        data: {
          name: 'Schneider Electric',
          image: '/images/brands/schneider.png',
        },
      }),
      prisma.brand.create({
        data: {
          name: 'Legrand',
          image: '/images/brands/legrand.png',
        },
      }),
      prisma.brand.create({
        data: {
          name: 'Philips',
          image: '/images/brands/philips.png',
        },
      }),
    ]);

    console.log('Marques créées:', brands);

    // Créer des produits
    const products = await Promise.all([
      prisma.product.create({
        data: {
          reference: 'LED-A60-10W',
          name: 'Ampoule LED A60 10W E27 Blanc Chaud 2700K',
          description: 'Ampoule LED A60 de 10W avec culot E27, offrant une lumière blanc chaud à 2700K. Idéale pour l\'éclairage général des pièces à vivre.',
          characteristics: {
            puissance: '10W',
            culot: 'E27',
            temperature_couleur: '2700K',
            flux_lumineux: '806 lumens',
            duree_vie: '15000 heures',
            angle_faisceau: '220°',
            dimmable: false,
          },
          mainImage: '/images/products/ampoule-led.jpg',
          categoryId: categories[0].id, // Éclairage
          brandId: brands[2].id, // Philips
        },
      }),
      prisma.product.create({
        data: {
          reference: 'SPOT-GU10-5W',
          name: 'Spot LED GU10 5W Blanc Neutre 4000K',
          description: 'Spot LED GU10 de 5W offrant une lumière blanc neutre à 4000K. Parfait pour l\'éclairage d\'accentuation dans les cuisines et salles de bain.',
          characteristics: {
            puissance: '5W',
            culot: 'GU10',
            temperature_couleur: '4000K',
            flux_lumineux: '350 lumens',
            duree_vie: '25000 heures',
            angle_faisceau: '36°',
            dimmable: true,
          },
          mainImage: '/images/products/spot-led.jpg',
          categoryId: categories[0].id, // Éclairage
          brandId: brands[2].id, // Philips
        },
      }),
      prisma.product.create({
        data: {
          reference: 'PRISE-2P-T',
          name: 'Prise de courant 2P+T Blanc',
          description: 'Prise de courant 2P+T blanche, 16A 250V, avec système de connexion rapide. Installation simple et sécurisée.',
          characteristics: {
            type: 'Prise de courant',
            poles: '2P+T',
            intensite: '16A',
            tension: '250V',
            couleur: 'Blanc',
            connexion_rapide: true,
            norme: 'NF C 61-314',
          },
          mainImage: '/images/products/prise-courant.jpg',
          categoryId: categories[1].id, // Appareillage
          brandId: brands[1].id, // Legrand
        },
      }),
      prisma.product.create({
        data: {
          reference: 'DISJ-16A',
          name: 'Disjoncteur magnétothermique 16A',
          description: 'Disjoncteur magnétothermique 1P+N 16A courbe C, pour protection des circuits d\'éclairage et prises de courant.',
          characteristics: {
            type: 'Disjoncteur magnétothermique',
            poles: '1P+N',
            intensite: '16A',
            courbe: 'C',
            pouvoir_coupure: '4500A',
            largeur_modules: '1',
            norme: 'NF EN 60898',
          },
          mainImage: '/images/products/disjoncteur.jpg',
          categoryId: categories[1].id, // Appareillage
          brandId: brands[0].id, // Schneider Electric
        },
      }),
      prisma.product.create({
        data: {
          reference: 'CABLE-3G1.5',
          name: 'Câble électrique H07RN-F 3G1.5mm²',
          description: 'Câble électrique souple H07RN-F 3G1.5mm², pour alimentation d\'appareils mobiles en intérieur et extérieur.',
          characteristics: {
            type: 'Câble souple',
            reference: 'H07RN-F',
            section: '3G1.5mm²',
            conducteurs: '3 (Phase+Neutre+Terre)',
            tension: '450/750V',
            temperature_max: '90°C',
            resistance_huile: true,
            usage_exterieur: true,
          },
          mainImage: '/images/products/cable-electrique.jpg',
          categoryId: categories[2].id, // Câblage
          brandId: null, // Pas de marque spécifique
        },
      }),
    ]);

    console.log('Produits créés:', products);

    console.log('Données d\'exemple créées avec succès !');
  } catch (error) {
    console.error('Erreur lors de la création des données d\'exemple:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
