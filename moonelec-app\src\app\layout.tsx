import type { Metadata } from "next";
import "./globals.css";
import AuthProvider from "@/context/AuthContext";
import { CartProvider } from "@/context/CartContext";
import { ThemeProvider } from "@/context/ThemeContext";
import ToastProvider from "@/components/ui/ToastProvider";
import { runStartupTasks } from '@/lib/startupTasks';

// Run startup tasks (only on server)
if (typeof window === 'undefined') {
  runStartupTasks();
}

export const metadata: Metadata = {
  title: "Moonelec - Distribution de Matériel Électrique",
  description: "Moonelec, spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits dans les secteurs résidentiel, tertiaire et industriel.",
  manifest: "/manifest.json",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr">
      <body
        className="antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300 font-sans"
        suppressHydrationWarning
      >
        <ThemeProvider>
          <AuthProvider>
            <CartProvider>
              <ToastProvider />
              {children}
            </CartProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
