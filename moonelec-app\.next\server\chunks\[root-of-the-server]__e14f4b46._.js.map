{"version": 3, "sources": [], "sections": [{"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/node-domexception/index.js"], "sourcesContent": ["/*! node-domexception. MIT License. Jimmy <PERSON> <https://jimmy.warting.se/opensource> */\n\nif (!globalThis.DOMException) {\n  try {\n    const { MessageChannel } = require('worker_threads'),\n    port = new MessageChannel().port1,\n    ab = new ArrayBuffer()\n    port.postMessage(ab, [ab, ab])\n  } catch (err) {\n    err.constructor.name === 'DOMException' && (\n      globalThis.DOMException = err.constructor\n    )\n  }\n}\n\nmodule.exports = globalThis.DOMException\n"], "names": [], "mappings": "AAAA,wFAAwF,GAExF,IAAI,CAAC,WAAW,YAAY,EAAE;IAC5B,IAAI;QACF,MAAM,EAAE,cAAc,EAAE,2FACxB,OAAO,IAAI,iBAAiB,KAAK,EACjC,KAAK,IAAI;QACT,KAAK,WAAW,CAAC,IAAI;YAAC;YAAI;SAAG;IAC/B,EAAE,OAAO,KAAK;QACZ,IAAI,WAAW,CAAC,IAAI,KAAK,kBAAkB,CACzC,WAAW,YAAY,GAAG,IAAI,WAAW,AAC3C;IACF;AACF;AAEA,OAAO,OAAO,GAAG,WAAW,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/formdata-node/lib/esm/isPlainObject.js"], "sourcesContent": ["const getType = (value) => (Object.prototype.toString.call(value).slice(8, -1).toLowerCase());\nfunction isPlainObject(value) {\n    if (getType(value) !== \"object\") {\n        return false;\n    }\n    const pp = Object.getPrototypeOf(value);\n    if (pp === null || pp === undefined) {\n        return true;\n    }\n    const Ctor = pp.constructor && pp.constructor.toString();\n    return Ctor === Object.toString();\n}\nexport default isPlainObject;\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU,CAAC,QAAW,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,WAAW;AAC1F,SAAS,cAAc,KAAK;IACxB,IAAI,QAAQ,WAAW,UAAU;QAC7B,OAAO;IACX;IACA,MAAM,KAAK,OAAO,cAAc,CAAC;IACjC,IAAI,OAAO,QAAQ,OAAO,WAAW;QACjC,OAAO;IACX;IACA,MAAM,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,QAAQ;IACtD,OAAO,SAAS,OAAO,QAAQ;AACnC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/formdata-node/lib/esm/fileFromPath.js"], "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _FileFromPath_path, _FileFromPath_start;\nimport { statSync, createReadStream, promises as fs } from \"fs\";\nimport { basename } from \"path\";\nimport DOMException from \"node-domexception\";\nimport { File } from \"./File.js\";\nimport isPlainObject from \"./isPlainObject.js\";\nexport * from \"./isFile.js\";\nconst MESSAGE = \"The requested file could not be read, \"\n    + \"typically due to permission problems that have occurred after a reference \"\n    + \"to a file was acquired.\";\nclass FileFromPath {\n    constructor(input) {\n        _FileFromPath_path.set(this, void 0);\n        _FileFromPath_start.set(this, void 0);\n        __classPrivateFieldSet(this, _FileFromPath_path, input.path, \"f\");\n        __classPrivateFieldSet(this, _FileFromPath_start, input.start || 0, \"f\");\n        this.name = basename(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"));\n        this.size = input.size;\n        this.lastModified = input.lastModified;\n    }\n    slice(start, end) {\n        return new FileFromPath({\n            path: __classPrivateFieldGet(this, _FileFromPath_path, \"f\"),\n            lastModified: this.lastModified,\n            size: end - start,\n            start\n        });\n    }\n    async *stream() {\n        const { mtimeMs } = await fs.stat(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"));\n        if (mtimeMs > this.lastModified) {\n            throw new DOMException(MESSAGE, \"NotReadableError\");\n        }\n        if (this.size) {\n            yield* createReadStream(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"), {\n                start: __classPrivateFieldGet(this, _FileFromPath_start, \"f\"),\n                end: __classPrivateFieldGet(this, _FileFromPath_start, \"f\") + this.size - 1\n            });\n        }\n    }\n    get [(_FileFromPath_path = new WeakMap(), _FileFromPath_start = new WeakMap(), Symbol.toStringTag)]() {\n        return \"File\";\n    }\n}\nfunction createFileFromPath(path, { mtimeMs, size }, filenameOrOptions, options = {}) {\n    let filename;\n    if (isPlainObject(filenameOrOptions)) {\n        [options, filename] = [filenameOrOptions, undefined];\n    }\n    else {\n        filename = filenameOrOptions;\n    }\n    const file = new FileFromPath({ path, size, lastModified: mtimeMs });\n    if (!filename) {\n        filename = file.name;\n    }\n    return new File([file], filename, {\n        ...options, lastModified: file.lastModified\n    });\n}\nexport function fileFromPathSync(path, filenameOrOptions, options = {}) {\n    const stats = statSync(path);\n    return createFileFromPath(path, stats, filenameOrOptions, options);\n}\nexport async function fileFromPath(path, filenameOrOptions, options) {\n    const stats = await fs.stat(path);\n    return createFileFromPath(path, stats, filenameOrOptions, options);\n}\n"], "names": [], "mappings": ";;;;AAYA;AACA;AACA;AACA;AACA;AACA;AAjBA,IAAI,yBAAyB,AAAC,IAAI,IAAI,IAAI,CAAC,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC3G,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACA,IAAI,yBAAyB,AAAC,IAAI,IAAI,IAAI,CAAC,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpG,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AACA,IAAI,oBAAoB;;;;;;;AAOxB,MAAM,UAAU,2CACV,+EACA;AACN,MAAM;IACF,YAAY,KAAK,CAAE;QACf,mBAAmB,GAAG,CAAC,IAAI,EAAE,KAAK;QAClC,oBAAoB,GAAG,CAAC,IAAI,EAAE,KAAK;QACnC,uBAAuB,IAAI,EAAE,oBAAoB,MAAM,IAAI,EAAE;QAC7D,uBAAuB,IAAI,EAAE,qBAAqB,MAAM,KAAK,IAAI,GAAG;QACpE,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,iGAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,IAAI,EAAE,oBAAoB;QACtE,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;QACtB,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;IAC1C;IACA,MAAM,KAAK,EAAE,GAAG,EAAE;QACd,OAAO,IAAI,aAAa;YACpB,MAAM,uBAAuB,IAAI,EAAE,oBAAoB;YACvD,cAAc,IAAI,CAAC,YAAY;YAC/B,MAAM,MAAM;YACZ;QACJ;IACJ;IACA,OAAO,SAAS;QACZ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,6FAAA,CAAA,WAAE,CAAC,IAAI,CAAC,uBAAuB,IAAI,EAAE,oBAAoB;QACnF,IAAI,UAAU,IAAI,CAAC,YAAY,EAAE;YAC7B,MAAM,IAAI,+IAAA,CAAA,UAAY,CAAC,SAAS;QACpC;QACA,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,OAAO,CAAA,GAAA,6FAAA,CAAA,mBAAgB,AAAD,EAAE,uBAAuB,IAAI,EAAE,oBAAoB,MAAM;gBAC3E,OAAO,uBAAuB,IAAI,EAAE,qBAAqB;gBACzD,KAAK,uBAAuB,IAAI,EAAE,qBAAqB,OAAO,IAAI,CAAC,IAAI,GAAG;YAC9E;QACJ;IACJ;IACA,IAAI,CAAC,CAAC,qBAAqB,IAAI,WAAW,sBAAsB,IAAI,WAAW,OAAO,WAAW,EAAE,GAAG;QAClG,OAAO;IACX;AACJ;AACA,SAAS,mBAAmB,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;IAChF,IAAI;IACJ,IAAI,CAAA,GAAA,iKAAA,CAAA,UAAa,AAAD,EAAE,oBAAoB;QAClC,CAAC,SAAS,SAAS,GAAG;YAAC;YAAmB;SAAU;IACxD,OACK;QACD,WAAW;IACf;IACA,MAAM,OAAO,IAAI,aAAa;QAAE;QAAM;QAAM,cAAc;IAAQ;IAClE,IAAI,CAAC,UAAU;QACX,WAAW,KAAK,IAAI;IACxB;IACA,OAAO,IAAI,wJAAA,CAAA,OAAI,CAAC;QAAC;KAAK,EAAE,UAAU;QAC9B,GAAG,OAAO;QAAE,cAAc,KAAK,YAAY;IAC/C;AACJ;AACO,SAAS,iBAAiB,IAAI,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;IAClE,MAAM,QAAQ,CAAA,GAAA,6FAAA,CAAA,WAAQ,AAAD,EAAE;IACvB,OAAO,mBAAmB,MAAM,OAAO,mBAAmB;AAC9D;AACO,eAAe,aAAa,IAAI,EAAE,iBAAiB,EAAE,OAAO;IAC/D,MAAM,QAAQ,MAAM,6FAAA,CAAA,WAAE,CAAC,IAAI,CAAC;IAC5B,OAAO,mBAAmB,MAAM,OAAO,mBAAmB;AAC9D", "ignoreList": [0], "debugId": null}}]}