// Script pour tester l'extraction de données à partir d'un PDF
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le chemin du répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Chemin vers le PDF de test
const pdfPath = path.join(process.cwd(), 'public', 'test.pdf');

// Fonction pour extraire le texte d'un PDF
async function extractTextFromPdf(pdfPath) {
  try {
    // Lire le fichier PDF
    const data = fs.readFileSync(pdfPath);

    // Convertir le PDF en texte en utilisant une méthode simple
    // Cette méthode n'est pas parfaite mais peut donner une idée du contenu
    const text = extractTextFromBuffer(data);

    return text;
  } catch (error) {
    console.error('Erreur lors de l\'extraction du texte du PDF:', error);
    return '';
  }
}

// Fonction pour extraire le texte d'un buffer
function extractTextFromBuffer(buffer) {
  // Convertir le buffer en chaîne de caractères
  const text = buffer.toString('utf-8');

  // Extraire les chaînes de caractères visibles
  let extractedText = '';
  let inString = false;
  let currentString = '';

  for (let i = 0; i < text.length; i++) {
    const char = text[i];

    // Chercher les séquences qui commencent par '(' et finissent par ')'
    if (char === '(' && !inString) {
      inString = true;
      currentString = '';
    } else if (char === ')' && inString) {
      inString = false;
      if (currentString.length > 1) {
        extractedText += currentString + ' ';
      }
    } else if (inString) {
      // Ignorer les caractères de contrôle
      if (char >= ' ' && char <= '~') {
        currentString += char;
      }
    }
  }

  return extractedText;
}

// Fonction pour analyser le texte et extraire les informations structurées
function parseText(text) {
  // Diviser le texte en lignes
  const lines = text.split('\n').map(line => line.trim()).filter(Boolean);

  // Recherche du nom du produit
  let productName = '';
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('Fiche produit')) {
      // Le nom du produit est généralement avant ou après "Fiche produit"
      const parts = lines[i].split('Fiche produit');
      if (parts[0].trim()) {
        productName = parts[0].trim();
      } else if (parts.length > 1 && parts[1].trim()) {
        productName = parts[1].trim();
      }
      break;
    }
  }

  // Si on n'a pas trouvé le nom avec "Fiche produit", prendre la première ligne non vide
  if (!productName && lines.length > 0) {
    productName = lines[0];
  }

  // Recherche de la référence
  let reference = '';
  const refPatterns = [
    /Réf\.?\s*[:.]?\s*([A-Za-z0-9\-]+)/i,
    /Référence\s*[:.]?\s*([A-Za-z0-9\-]+)/i,
    /Reference\s*[:.]?\s*([A-Za-z0-9\-]+)/i,
    /REF\s*[:.]?\s*([A-Za-z0-9\-]+)/i,
    /Ref\s*[:.]?\s*([A-Za-z0-9\-]+)/i
  ];

  for (const pattern of refPatterns) {
    const match = text.match(pattern);
    if (match) {
      reference = match[1];
      break;
    }
  }

  // Recherche de la description
  let description = '';
  let descriptionIdx = -1;

  // Chercher une section "DESCRIPTION DU PRODUIT" ou similaire
  for (let i = 0; i < lines.length; i++) {
    if (/description du produit/i.test(lines[i])) {
      descriptionIdx = i + 1;
      break;
    }
  }

  // Si on a trouvé une section description, extraire la description
  if (descriptionIdx > 0) {
    let endIdx = descriptionIdx;
    for (let i = descriptionIdx; i < lines.length; i++) {
      if (/caractéristiques techniques|dessin technique/i.test(lines[i])) {
        endIdx = i;
        break;
      }
    }

    if (endIdx > descriptionIdx) {
      description = lines.slice(descriptionIdx, endIdx).join(' ');
    } else {
      // Si on n'a pas trouvé la fin, prendre les 5 lignes suivantes
      endIdx = Math.min(descriptionIdx + 5, lines.length);
      description = lines.slice(descriptionIdx, endIdx).join(' ');
    }
  }

  // Recherche des caractéristiques techniques
  const characteristics = {};
  let tableIdx = -1;

  // Chercher une section "CARACTÉRISTIQUES TECHNIQUES" ou similaire
  for (let i = 0; i < lines.length; i++) {
    if (/caractéristiques techniques/i.test(lines[i])) {
      tableIdx = i + 1;
      break;
    }
  }

  // Si on a trouvé une section caractéristiques, extraire les caractéristiques
  if (tableIdx > 0) {
    for (let i = tableIdx; i < lines.length; i++) {
      const line = lines[i];

      // Si on trouve une nouvelle section, arrêter l'extraction
      if (/dessin technique|dimensions|garantie/i.test(line) && i > tableIdx + 1) {
        break;
      }

      // Chercher les lignes avec un séparateur (:)
      if (line.includes(':')) {
        const parts = line.split(':', 2);
        if (parts.length === 2) {
          const key = parts[0].trim();
          const value = parts[1].trim();
          characteristics[key] = value;
        }
      }
    }
  }

  return {
    productName,
    reference,
    description,
    characteristics
  };
}

// Fonction principale
async function main() {
  try {
    console.log('Extraction du texte du PDF...');
    const text = await extractTextFromPdf(pdfPath);

    console.log('\nTexte extrait:');
    console.log('=============');
    console.log(text.substring(0, 1000) + '...');

    // Écrire le texte extrait dans un fichier pour analyse
    fs.writeFileSync('extracted-text.txt', text);
    console.log('\nLe texte complet a été écrit dans le fichier extracted-text.txt');

    console.log('\nAnalyse du texte...');
    const result = parseText(text);

    console.log('\nRésultat:');
    console.log('========');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Erreur:', error);
  }
}

// Exécuter la fonction principale
main();
