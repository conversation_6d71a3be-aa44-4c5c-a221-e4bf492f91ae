{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/DashboardCard.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaArrowUp, FaArrowDown } from 'react-icons/fa';\n\ninterface DashboardCardProps {\n  title: string;\n  value: number;\n  icon: ReactNode;\n  change: number;\n  period: string;\n  isCurrency?: boolean;\n}\n\nexport default function DashboardCard({\n  title,\n  value,\n  icon,\n  change,\n  period,\n  isCurrency = false,\n}: DashboardCardProps) {\n  const isPositive = change >= 0;\n  \n  // Formatter pour les nombres et les devises\n  const formattedValue = isCurrency\n    ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(value)\n    : new Intl.NumberFormat('fr-FR').format(value);\n  \n  return (\n    <motion.div\n      whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}\n      transition={{ duration: 0.2 }}\n      className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\"\n    >\n      <div className=\"p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-medium text-gray-600 dark:text-gray-300\">{title}</h3>\n          <div className=\"text-2xl\">{icon}</div>\n        </div>\n        \n        <div className=\"flex items-end justify-between\">\n          <div>\n            <p className=\"text-2xl font-bold text-gray-800 dark:text-white mb-1\">\n              {formattedValue}\n            </p>\n            <div className=\"flex items-center\">\n              <span\n                className={`flex items-center text-sm ${\n                  isPositive ? 'text-green-500' : 'text-red-500'\n                }`}\n              >\n                {isPositive ? <FaArrowUp className=\"mr-1\" /> : <FaArrowDown className=\"mr-1\" />}\n                {Math.abs(change)}%\n              </span>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400 ml-2\">\n                {period}\n              </span>\n            </div>\n          </div>\n          \n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ type: 'spring', stiffness: 200, damping: 10 }}\n            className={`w-12 h-12 rounded-full flex items-center justify-center ${\n              isPositive ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'\n            }`}\n          >\n            {isPositive ? (\n              <FaArrowUp className=\"text-green-500\" />\n            ) : (\n              <FaArrowDown className=\"text-red-500\" />\n            )}\n          </motion.div>\n        </div>\n      </div>\n      \n      <div\n        className={`h-1 ${\n          isPositive ? 'bg-green-500' : 'bg-red-500'\n        }`}\n      ></div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAee,SAAS,cAAc,EACpC,KAAK,EACL,KAAK,EACL,IAAI,EACJ,MAAM,EACN,MAAM,EACN,aAAa,KAAK,EACC;IACnB,MAAM,aAAa,UAAU;IAE7B,4CAA4C;IAC5C,MAAM,iBAAiB,aACnB,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY,UAAU;IAAM,GAAG,MAAM,CAAC,SAC9E,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAE1C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;YAAG,WAAW;QAA4E;QAC5G,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CACtE,8OAAC;gCAAI,WAAU;0CAAY;;;;;;;;;;;;kCAG7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDACV;;;;;;kDAEH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,0BAA0B,EACpC,aAAa,mBAAmB,gBAChC;;oDAED,2BAAa,8OAAC,8IAAA,CAAA,YAAS;wDAAC,WAAU;;;;;6EAAY,8OAAC,8IAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACrE,KAAK,GAAG,CAAC;oDAAQ;;;;;;;0DAEpB,8OAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;0CAKP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;gCAC1D,WAAW,CAAC,wDAAwD,EAClE,aAAa,sCAAsC,iCACnD;0CAED,2BACC,8OAAC,8IAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,8OAAC,8IAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBACC,WAAW,CAAC,IAAI,EACd,aAAa,iBAAiB,cAC9B;;;;;;;;;;;;AAIV", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/DashboardChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nexport default function DashboardChart() {\n  const [mounted, setMounted] = useState(false);\n  \n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  \n  // Données simulées pour le graphique\n  const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];\n  const currentYear = [\n    150000, 180000, 210000, 240000, 270000, 300000, 330000, 360000, 390000, 420000, 450000, 480000\n  ];\n  const previousYear = [\n    120000, 140000, 160000, 180000, 200000, 220000, 240000, 260000, 280000, 300000, 320000, 340000\n  ];\n  \n  // Trouver la valeur maximale pour l'échelle\n  const maxValue = Math.max(...currentYear, ...previousYear);\n  \n  // Calculer la hauteur des barres en pourcentage\n  const getBarHeight = (value: number) => {\n    return (value / maxValue) * 100;\n  };\n  \n  if (!mounted) {\n    return <div className=\"h-80 bg-gray-100 dark:bg-gray-700 animate-pulse rounded-lg\"></div>;\n  }\n  \n  return (\n    <div className=\"h-80\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex space-x-4\">\n          <div className=\"flex items-center\">\n            <span className=\"w-3 h-3 bg-primary rounded-full mr-2\"></span>\n            <span className=\"text-sm text-gray-600 dark:text-gray-300\">2023</span>\n          </div>\n          <div className=\"flex items-center\">\n            <span className=\"w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full mr-2\"></span>\n            <span className=\"text-sm text-gray-600 dark:text-gray-300\">2022</span>\n          </div>\n        </div>\n        \n        <select className=\"bg-gray-100 dark:bg-gray-700 border-none rounded-md text-sm text-gray-600 dark:text-gray-300 py-1 px-3 focus:outline-none focus:ring-2 focus:ring-primary\">\n          <option value=\"year\">Annuel</option>\n          <option value=\"month\">Mensuel</option>\n          <option value=\"week\">Hebdomadaire</option>\n        </select>\n      </div>\n      \n      <div className=\"relative h-64\">\n        {/* Axe Y */}\n        <div className=\"absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400\">\n          {[0, 25, 50, 75, 100].reverse().map((value, index) => (\n            <div key={index} className=\"flex items-center\">\n              <span className=\"mr-2\">{(maxValue * value / 100).toLocaleString('fr-FR')} MAD</span>\n              <div className=\"w-full border-b border-gray-200 dark:border-gray-700\"></div>\n            </div>\n          ))}\n        </div>\n        \n        {/* Graphique */}\n        <div className=\"ml-24 h-full flex items-end\">\n          <div className=\"flex-1 flex justify-around items-end h-full\">\n            {months.map((month, index) => (\n              <div key={index} className=\"flex flex-col items-center group\">\n                <div className=\"relative w-12 flex justify-center\">\n                  {/* Barre de l'année précédente */}\n                  <motion.div\n                    initial={{ height: 0 }}\n                    animate={{ height: `${getBarHeight(previousYear[index])}%` }}\n                    transition={{ duration: 0.5, delay: index * 0.05 }}\n                    className=\"absolute bottom-0 w-4 bg-gray-300 dark:bg-gray-600 rounded-t-sm\"\n                  ></motion.div>\n                  \n                  {/* Barre de l'année courante */}\n                  <motion.div\n                    initial={{ height: 0 }}\n                    animate={{ height: `${getBarHeight(currentYear[index])}%` }}\n                    transition={{ duration: 0.5, delay: index * 0.05 + 0.2 }}\n                    className=\"absolute bottom-0 w-4 bg-primary rounded-t-sm ml-5\"\n                  ></motion.div>\n                  \n                  {/* Tooltip */}\n                  <div className=\"absolute bottom-full mb-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-800 text-white text-xs rounded py-1 px-2 pointer-events-none\">\n                    <div>2022: {previousYear[index].toLocaleString('fr-FR')} MAD</div>\n                    <div>2023: {currentYear[index].toLocaleString('fr-FR')} MAD</div>\n                  </div>\n                </div>\n                \n                <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">{month}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,SAAS;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAO;QAAO;QAAO;KAAM;IACtG,MAAM,cAAc;QAClB;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KACzF;IACD,MAAM,eAAe;QACnB;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KACzF;IAED,4CAA4C;IAC5C,MAAM,WAAW,KAAK,GAAG,IAAI,gBAAgB;IAE7C,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,OAAO,AAAC,QAAQ,WAAY;IAC9B;IAEA,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,WAAU;;;;;;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAE7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;kCAI/D,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,8OAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;;;;;;;0BAIzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAI;4BAAI;4BAAI;yBAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC1C,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAK,WAAU;;4CAAQ,CAAC,WAAW,QAAQ,GAAG,EAAE,cAAc,CAAC;4CAAS;;;;;;;kDACzE,8OAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;kCAQd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,QAAQ;oDAAE;oDACrB,SAAS;wDAAE,QAAQ,GAAG,aAAa,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;oDAAC;oDAC3D,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAK;oDACjD,WAAU;;;;;;8DAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,QAAQ;oDAAE;oDACrB,SAAS;wDAAE,QAAQ,GAAG,aAAa,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;oDAAC;oDAC1D,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ,OAAO;oDAAI;oDACvD,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAI;gEAAO,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC;gEAAS;;;;;;;sEACxD,8OAAC;;gEAAI;gEAAO,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;;;;;;;sDAI3D,8OAAC;4CAAI,WAAU;sDAAiD;;;;;;;mCAzBxD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCxB", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/RecentActivityCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaShoppingCart, FaUser, FaBox, FaUserTie, FaEllipsisH } from 'react-icons/fa';\n\nexport default function RecentActivityCard() {\n  // Données simulées pour les activités récentes\n  const activities = [\n    {\n      id: 1,\n      type: 'order',\n      title: 'Nouvelle commande #12345',\n      description: 'Client: Société ABC',\n      time: 'Il y a 5 minutes',\n      icon: <FaShoppingCart />,\n      iconBg: 'bg-blue-100 dark:bg-blue-900/20',\n      iconColor: 'text-blue-500',\n    },\n    {\n      id: 2,\n      type: 'client',\n      title: 'Nouveau client inscrit',\n      description: 'Entreprise XYZ',\n      time: 'Il y a 30 minutes',\n      icon: <FaUser />,\n      iconBg: 'bg-green-100 dark:bg-green-900/20',\n      iconColor: 'text-green-500',\n    },\n    {\n      id: 3,\n      type: 'product',\n      title: 'Mise à jour du stock',\n      description: '15 produits ajoutés',\n      time: 'Il y a 2 heures',\n      icon: <FaBox />,\n      iconBg: 'bg-purple-100 dark:bg-purple-900/20',\n      iconColor: 'text-purple-500',\n    },\n    {\n      id: 4,\n      type: 'commercial',\n      title: 'Nouveau commercial',\n      description: 'Ahmed Benani a rejoint l\\'équipe',\n      time: 'Il y a 5 heures',\n      icon: <FaUserTie />,\n      iconBg: 'bg-yellow-100 dark:bg-yellow-900/20',\n      iconColor: 'text-yellow-500',\n    },\n    {\n      id: 5,\n      type: 'order',\n      title: 'Commande #12340 expédiée',\n      description: 'Client: Entreprise DEF',\n      time: 'Il y a 8 heures',\n      icon: <FaShoppingCart />,\n      iconBg: 'bg-blue-100 dark:bg-blue-900/20',\n      iconColor: 'text-blue-500',\n    },\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md h-full\">\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center\">\n        <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white\">\n          Activités récentes\n        </h2>\n        <button className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\">\n          <FaEllipsisH />\n        </button>\n      </div>\n      \n      <div className=\"p-6\">\n        <ul className=\"space-y-6\">\n          {activities.map((activity, index) => (\n            <motion.li\n              key={activity.id}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n              className=\"flex\"\n            >\n              <div className=\"relative\">\n                <div className={`w-10 h-10 rounded-full ${activity.iconBg} flex items-center justify-center ${activity.iconColor}`}>\n                  {activity.icon}\n                </div>\n                {index < activities.length - 1 && (\n                  <div className=\"absolute top-10 left-1/2 w-px h-6 bg-gray-200 dark:bg-gray-700 transform -translate-x-1/2\"></div>\n                )}\n              </div>\n              \n              <div className=\"ml-4 flex-1\">\n                <div className=\"flex justify-between\">\n                  <h3 className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                    {activity.title}\n                  </h3>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {activity.time}\n                  </span>\n                </div>\n                <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-1\">\n                  {activity.description}\n                </p>\n              </div>\n            </motion.li>\n          ))}\n        </ul>\n      </div>\n      \n      <div className=\"px-6 py-3 border-t border-gray-200 dark:border-gray-700 text-center\">\n        <button className=\"text-primary hover:underline text-sm font-medium\">\n          Voir toutes les activités\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,+CAA+C;IAC/C,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBAAM,8OAAC,8IAAA,CAAA,iBAAc;;;;;YACrB,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBAAM,8OAAC,8IAAA,CAAA,SAAM;;;;;YACb,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBAAM,8OAAC,8IAAA,CAAA,QAAK;;;;;YACZ,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;Y<PERSON><PERSON>,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBAAM,8OAAC,8IAAA,CAAA,YAAS;;;;;YAChB,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,oBAAM,8OAAC,8IAAA,CAAA,iBAAc;;;;;YACrB,QAAQ;YACR,WAAW;QACb;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC,8IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BAER,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,kCAAkC,EAAE,SAAS,SAAS,EAAE;sDAC/G,SAAS,IAAI;;;;;;wCAEf,QAAQ,WAAW,MAAM,GAAG,mBAC3B,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,SAAS,KAAK;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DACb,SAAS,IAAI;;;;;;;;;;;;sDAGlB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;;;;;;;;2BAzBpB,SAAS,EAAE;;;;;;;;;;;;;;;0BAiCxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAO,WAAU;8BAAmD;;;;;;;;;;;;;;;;;AAM7E", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/CommercialPerformanceChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaUserTie } from 'react-icons/fa';\n\ninterface CommercialData {\n  id: string;\n  name: string;\n  sales: number;\n  clients: number;\n  color: string;\n}\n\nexport default function CommercialPerformanceChart() {\n  const [mounted, setMounted] = useState(false);\n  \n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  \n  // Données simulées pour le graphique\n  const commercials: CommercialData[] = [\n    { id: '1', name: '<PERSON>', sales: 450000, clients: 24, color: '#4F46E5' },\n    { id: '2', name: '<PERSON><PERSON>', sales: 380000, clients: 18, color: '#10B981' },\n    { id: '3', name: '<PERSON><PERSON>', sales: 320000, clients: 15, color: '#F59E0B' },\n    { id: '4', name: '<PERSON>', sales: 290000, clients: 12, color: '#EF4444' },\n    { id: '5', name: '<PERSON>', sales: 250000, clients: 10, color: '#8B5CF6' },\n  ];\n  \n  // Trouver la valeur maximale pour l'échelle\n  const maxSales = Math.max(...commercials.map(c => c.sales));\n  \n  // Calculer la largeur des barres en pourcentage\n  const getBarWidth = (value: number) => {\n    return (value / maxSales) * 100;\n  };\n  \n  if (!mounted) {\n    return <div className=\"h-80 bg-gray-100 dark:bg-gray-700 animate-pulse rounded-lg\"></div>;\n  }\n  \n  return (\n    <div className=\"h-80 overflow-y-auto\">\n      <div className=\"space-y-6\">\n        {commercials.map((commercial, index) => (\n          <div key={commercial.id} className=\"relative\">\n            <div className=\"flex items-center mb-2\">\n              <div className=\"w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mr-3\">\n                <FaUserTie style={{ color: commercial.color }} />\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-800 dark:text-white\">{commercial.name}</h3>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">{commercial.clients} clients</p>\n              </div>\n              <div className=\"ml-auto text-right\">\n                <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                  {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(commercial.sales)}\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\">\n              <motion.div\n                initial={{ width: 0 }}\n                animate={{ width: `${getBarWidth(commercial.sales)}%` }}\n                transition={{ duration: 1, delay: index * 0.1 }}\n                className=\"h-full rounded-full\"\n                style={{ backgroundColor: commercial.color }}\n              ></motion.div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAce,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,cAAgC;QACpC;YAAE,IAAI;YAAK,MAAM;YAAgB,OAAO;YAAQ,SAAS;YAAI,OAAO;QAAU;QAC9E;YAAE,IAAI;YAAK,MAAM;YAAiB,OAAO;YAAQ,SAAS;YAAI,OAAO;QAAU;QAC/E;YAAE,IAAI;YAAK,MAAM;YAAiB,OAAO;YAAQ,SAAS;YAAI,OAAO;QAAU;QAC/E;YAAE,IAAI;YAAK,MAAM;YAAc,OAAO;YAAQ,SAAS;YAAI,OAAO;QAAU;QAC5E;YAAE,IAAI;YAAK,MAAM;YAAmB,OAAO;YAAQ,SAAS;YAAI,OAAO;QAAU;KAClF;IAED,4CAA4C;IAC5C,MAAM,WAAW,KAAK,GAAG,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAEzD,gDAAgD;IAChD,MAAM,cAAc,CAAC;QACnB,OAAO,AAAC,QAAQ,WAAY;IAC9B;IAEA,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,WAAU;;;;;;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;oBAAwB,WAAU;;sCACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;wCAAC,OAAO;4CAAE,OAAO,WAAW,KAAK;wCAAC;;;;;;;;;;;8CAE9C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqD,WAAW,IAAI;;;;;;sDAClF,8OAAC;4CAAE,WAAU;;gDAA4C,WAAW,OAAO;gDAAC;;;;;;;;;;;;;8CAE9E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,IAAI,KAAK,YAAY,CAAC,SAAS;4CAAE,OAAO;4CAAY,UAAU;wCAAM,GAAG,MAAM,CAAC,WAAW,KAAK;;;;;;;;;;;;;;;;;sCAKrG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO,GAAG,YAAY,WAAW,KAAK,EAAE,CAAC,CAAC;gCAAC;gCACtD,YAAY;oCAAE,UAAU;oCAAG,OAAO,QAAQ;gCAAI;gCAC9C,WAAU;gCACV,OAAO;oCAAE,iBAAiB,WAAW,KAAK;gCAAC;;;;;;;;;;;;mBAtBvC,WAAW,EAAE;;;;;;;;;;;;;;;AA8BjC", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/ProductStatsChart.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBox } from 'react-icons/fa';\n\ninterface CategoryData {\n  id: string;\n  name: string;\n  count: number;\n  percentage: number;\n  color: string;\n}\n\nexport default function ProductStatsChart() {\n  const [mounted, setMounted] = useState(false);\n  \n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  \n  // Données simulées pour le graphique\n  const categories: CategoryData[] = [\n    { id: '1', name: 'Éclairage', count: 1250, percentage: 35, color: '#4F46E5' },\n    { id: '2', name: 'Câblage', count: 850, percentage: 24, color: '#10B981' },\n    { id: '3', name: 'Appareillage', count: 650, percentage: 18, color: '#F59E0B' },\n    { id: '4', name: 'Domotique', count: 450, percentage: 13, color: '#EF4444' },\n    { id: '5', name: 'Autres', count: 350, percentage: 10, color: '#8B5CF6' },\n  ];\n  \n  if (!mounted) {\n    return <div className=\"h-80 bg-gray-100 dark:bg-gray-700 animate-pulse rounded-lg\"></div>;\n  }\n  \n  return (\n    <div className=\"h-80\">\n      <div className=\"flex h-full\">\n        {/* Graphique circulaire */}\n        <div className=\"w-1/2 relative flex items-center justify-center\">\n          <svg width=\"160\" height=\"160\" viewBox=\"0 0 160 160\" className=\"transform -rotate-90\">\n            {categories.map((category, index) => {\n              // Calculer les angles pour chaque segment\n              const previousPercentages = categories\n                .slice(0, index)\n                .reduce((sum, cat) => sum + cat.percentage, 0);\n              const startAngle = (previousPercentages / 100) * 360;\n              const endAngle = ((previousPercentages + category.percentage) / 100) * 360;\n              \n              // Convertir les angles en coordonnées pour le chemin SVG\n              const startX = 80 + 70 * Math.cos((startAngle * Math.PI) / 180);\n              const startY = 80 + 70 * Math.sin((startAngle * Math.PI) / 180);\n              const endX = 80 + 70 * Math.cos((endAngle * Math.PI) / 180);\n              const endY = 80 + 70 * Math.sin((endAngle * Math.PI) / 180);\n              \n              // Déterminer si l'arc doit être dessiné dans le sens des aiguilles d'une montre\n              const largeArcFlag = category.percentage > 50 ? 1 : 0;\n              \n              // Créer le chemin SVG pour le segment\n              const path = `\n                M 80 80\n                L ${startX} ${startY}\n                A 70 70 0 ${largeArcFlag} 1 ${endX} ${endY}\n                Z\n              `;\n              \n              return (\n                <motion.path\n                  key={category.id}\n                  d={path}\n                  fill={category.color}\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                />\n              );\n            })}\n            <circle cx=\"80\" cy=\"80\" r=\"50\" fill=\"white\" className=\"dark:fill-gray-800\" />\n          </svg>\n          \n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-gray-800 dark:text-white\">\n                {categories.reduce((sum, cat) => sum + cat.count, 0)}\n              </p>\n              <p className=\"text-xs text-gray-500 dark:text-gray-400\">Produits</p>\n            </div>\n          </div>\n        </div>\n        \n        {/* Légende */}\n        <div className=\"w-1/2 flex flex-col justify-center space-y-3\">\n          {categories.map((category, index) => (\n            <motion.div\n              key={category.id}\n              className=\"flex items-center\"\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n            >\n              <div\n                className=\"w-3 h-3 rounded-full mr-2\"\n                style={{ backgroundColor: category.color }}\n              ></div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                  {category.name}\n                </p>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                  {category.count}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {category.percentage}%\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAce,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,aAA6B;QACjC;YAAE,IAAI;YAAK,MAAM;YAAa,OAAO;YAAM,YAAY;YAAI,OAAO;QAAU;QAC5E;YAAE,IAAI;YAAK,MAAM;YAAW,OAAO;YAAK,YAAY;YAAI,OAAO;QAAU;QACzE;YAAE,IAAI;YAAK,MAAM;YAAgB,OAAO;YAAK,YAAY;YAAI,OAAO;QAAU;QAC9E;YAAE,IAAI;YAAK,MAAM;YAAa,OAAO;YAAK,YAAY;YAAI,OAAO;QAAU;QAC3E;YAAE,IAAI;YAAK,MAAM;YAAU,OAAO;YAAK,YAAY;YAAI,OAAO;QAAU;KACzE;IAED,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,WAAU;;;;;;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,OAAM;4BAAM,QAAO;4BAAM,SAAQ;4BAAc,WAAU;;gCAC3D,WAAW,GAAG,CAAC,CAAC,UAAU;oCACzB,0CAA0C;oCAC1C,MAAM,sBAAsB,WACzB,KAAK,CAAC,GAAG,OACT,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,UAAU,EAAE;oCAC9C,MAAM,aAAa,AAAC,sBAAsB,MAAO;oCACjD,MAAM,WAAW,AAAC,CAAC,sBAAsB,SAAS,UAAU,IAAI,MAAO;oCAEvE,yDAAyD;oCACzD,MAAM,SAAS,KAAK,KAAK,KAAK,GAAG,CAAC,AAAC,aAAa,KAAK,EAAE,GAAI;oCAC3D,MAAM,SAAS,KAAK,KAAK,KAAK,GAAG,CAAC,AAAC,aAAa,KAAK,EAAE,GAAI;oCAC3D,MAAM,OAAO,KAAK,KAAK,KAAK,GAAG,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;oCACvD,MAAM,OAAO,KAAK,KAAK,KAAK,GAAG,CAAC,AAAC,WAAW,KAAK,EAAE,GAAI;oCAEvD,gFAAgF;oCAChF,MAAM,eAAe,SAAS,UAAU,GAAG,KAAK,IAAI;oCAEpD,sCAAsC;oCACtC,MAAM,OAAO,CAAC;;kBAEV,EAAE,OAAO,CAAC,EAAE,OAAO;0BACX,EAAE,aAAa,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;;cAE7C,CAAC;oCAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCAEV,GAAG;wCACH,MAAM,SAAS,KAAK;wCACpB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;uCAL3C,SAAS,EAAE;;;;;gCAQtB;8CACA,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,MAAK;oCAAQ,WAAU;;;;;;;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,KAAK,EAAE;;;;;;kDAEpD,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;8BAM9D,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;;8CAEhD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,SAAS,KAAK;oCAAC;;;;;;8CAE3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,SAAS,IAAI;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,SAAS,KAAK;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;;gDACV,SAAS,UAAU;gDAAC;;;;;;;;;;;;;;2BApBpB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AA6B9B", "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/ClientOrdersCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaFileAlt, FaUser, FaCalendarAlt, FaSpinner, FaEye, FaFileDownload, FaExclamationTriangle } from 'react-icons/fa';\nimport Link from 'next/link';\n\ninterface Quote {\n  id: string;\n  createdAt: string;\n  status: 'pending' | 'processing' | 'completed' | 'cancelled';\n  client: {\n    id: string;\n    firstname: string;\n    lastname: string;\n    email: string;\n  };\n  items: {\n    id: string;\n    productId: string;\n    quantity: number;\n    product: {\n      name: string;\n      reference: string;\n    };\n  }[];\n  notes: string | null;\n}\n\nexport default function ClientOrdersCard() {\n  const [quotes, setQuotes] = useState<Quote[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const quotesPerPage = 5;\n\n  useEffect(() => {\n    const fetchQuotes = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n\n        const response = await fetch(`/api/quotes?page=${currentPage}&limit=${quotesPerPage}`);\n        \n        if (!response.ok) {\n          throw new Error('Failed to fetch quotes');\n        }\n\n        const data = await response.json();\n        setQuotes(data.quotes);\n        setTotalPages(Math.ceil(data.total / quotesPerPage));\n      } catch (err: any) {\n        console.error('Error fetching quotes:', err);\n        setError(err.message || 'An error occurred while fetching quotes');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchQuotes();\n  }, [currentPage]);\n\n  const getStatusBadgeClass = (status: Quote['status']) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n      case 'processing':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n      case 'completed':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n    }\n  };\n\n  const getStatusLabel = (status: Quote['status']) => {\n    switch (status) {\n      case 'pending':\n        return 'En attente';\n      case 'processing':\n        return 'En traitement';\n      case 'completed':\n        return 'Terminé';\n      case 'cancelled':\n        return 'Annulé';\n      default:\n        return status;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return new Intl.DateTimeFormat('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n      <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center\">\n        <FaFileAlt className=\"mr-2 text-primary\" />\n        Demandes de devis clients\n      </h2>\n\n      {isLoading ? (\n        <div className=\"flex justify-center items-center py-8\">\n          <FaSpinner className=\"animate-spin text-primary text-2xl\" />\n          <span className=\"ml-2 text-gray-600 dark:text-gray-300\">Chargement des demandes...</span>\n        </div>\n      ) : error ? (\n        <div className=\"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-4 rounded-lg flex items-center\">\n          <FaExclamationTriangle className=\"mr-2\" />\n          <span>{error}</span>\n        </div>\n      ) : quotes.length === 0 ? (\n        <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n          <FaFileAlt className=\"mx-auto text-4xl mb-2 opacity-30\" />\n          <p>Aucune demande de devis pour le moment</p>\n        </div>\n      ) : (\n        <>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n              <thead>\n                <tr>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Client\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Date\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Produits\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Statut\n                  </th>\n                  <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n                {quotes.map((quote) => (\n                  <tr key={quote.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\">\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center\">\n                          <FaUser className=\"text-primary\" />\n                        </div>\n                        <div className=\"ml-3\">\n                          <div className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                            {quote.client.firstname} {quote.client.lastname}\n                          </div>\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                            {quote.client.email}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-700 dark:text-gray-300 flex items-center\">\n                        <FaCalendarAlt className=\"mr-1 text-gray-400\" />\n                        {formatDate(quote.createdAt)}\n                      </div>\n                    </td>\n                    <td className=\"px-4 py-3\">\n                      <div className=\"text-sm text-gray-700 dark:text-gray-300\">\n                        {quote.items.length} produit{quote.items.length > 1 ? 's' : ''}\n                      </div>\n                      <div className=\"text-xs text-gray-500 dark:text-gray-400 truncate max-w-[200px]\">\n                        {quote.items.map(item => item.product.name).join(', ')}\n                      </div>\n                    </td>\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\n                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(quote.status)}`}>\n                        {getStatusLabel(quote.status)}\n                      </span>\n                    </td>\n                    <td className=\"px-4 py-3 whitespace-nowrap text-right text-sm font-medium\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <Link href={`/admin/quotes/${quote.id}`}>\n                          <button className=\"p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\" title=\"Voir les détails\">\n                            <FaEye />\n                          </button>\n                        </Link>\n                        <button className=\"p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300\" title=\"Générer un PDF\">\n                          <FaFileDownload />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"flex justify-center mt-4 space-x-1\">\n              {Array.from({ length: totalPages }).map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setCurrentPage(index + 1)}\n                  className={`px-3 py-1 rounded-md text-sm ${\n                    currentPage === index + 1\n                      ? 'bg-primary text-white'\n                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\n                  }`}\n                >\n                  {index + 1}\n                </button>\n              ))}\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AA6Be,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,gBAAgB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,YAAY,OAAO,EAAE,eAAe;gBAErF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,MAAM;gBACrB,cAAc,KAAK,IAAI,CAAC,KAAK,KAAK,GAAG;YACvC,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS,IAAI,OAAO,IAAI;YAC1B,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,8IAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAsB;;;;;;;YAI5C,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;uBAExD,sBACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,wBAAqB;wBAAC,WAAU;;;;;;kCACjC,8OAAC;kCAAM;;;;;;;;;;;uBAEP,OAAO,MAAM,KAAK,kBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;kCAAE;;;;;;;;;;;qCAGL;;kCACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;8CACC,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAqG;;;;;;;;;;;;;;;;;8CAKvH,8OAAC;oCAAM,WAAU;8CACd,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,MAAM,MAAM,CAAC,SAAS;4EAAC;4EAAE,MAAM,MAAM,CAAC,QAAQ;;;;;;;kFAEjD,8OAAC;wEAAI,WAAU;kFACZ,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAK3B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DACxB,WAAW,MAAM,SAAS;;;;;;;;;;;;8DAG/B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;gEACZ,MAAM,KAAK,CAAC,MAAM;gEAAC;gEAAS,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI,MAAM;;;;;;;sEAE9D,8OAAC;4DAAI,WAAU;sEACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;;;;;;;;;;;;8DAGrD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,+BAA+B,EAAE,oBAAoB,MAAM,MAAM,GAAG;kEACnF,eAAe,MAAM,MAAM;;;;;;;;;;;8DAGhC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;0EACrC,cAAA,8OAAC;oEAAO,WAAU;oEAAoF,OAAM;8EAC1G,cAAA,8OAAC,8IAAA,CAAA,QAAK;;;;;;;;;;;;;;;0EAGV,8OAAC;gEAAO,WAAU;gEAAwF,OAAM;0EAC9G,cAAA,8OAAC,8IAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;2CA3Cd,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;oBAsDxB,aAAa,mBACZ,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAW,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC1C,8OAAC;gCAEC,SAAS,IAAM,eAAe,QAAQ;gCACtC,WAAW,CAAC,6BAA6B,EACvC,gBAAgB,QAAQ,IACpB,0BACA,0GACJ;0CAED,QAAQ;+BARJ;;;;;;;;;;;;;;;;;;AAiBvB", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaUsers, FaShoppingCart, FaBox, FaChartLine, FaUserTie, FaBuilding, FaTag, FaCopyright } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport DashboardCard from '@/components/admin/DashboardCard';\nimport DashboardChart from '@/components/admin/DashboardChart';\nimport RecentActivityCard from '@/components/admin/RecentActivityCard';\nimport CommercialPerformanceChart from '@/components/admin/CommercialPerformanceChart';\nimport ProductStatsChart from '@/components/admin/ProductStatsChart';\nimport ClientOrdersCard from '@/components/admin/ClientOrdersCard';\n\nexport default function AdminDashboard() {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [dashboardData, setDashboardData] = useState({\n    totalClients: 0,\n    totalOrders: 0,\n    totalProducts: 0,\n    totalRevenue: 0,\n    totalCommercials: 0,\n    totalSuppliers: 0,\n    totalCategories: 0,\n    totalBrands: 0\n  });\n\n  useEffect(() => {\n    // Charger les données réelles depuis l'API\n    const fetchDashboardData = async () => {\n      try {\n        const response = await fetch('/api/admin/dashboard');\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch dashboard data');\n        }\n\n        const data = await response.json();\n        setDashboardData(data);\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        // En cas d'erreur, on garde les valeurs par défaut\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, []);\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { type: 'spring', stiffness: 100 }\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n        className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\"\n      >\n        <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n          Bienvenue, {user?.firstname} {user?.lastname}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-300 mt-1\">\n          Voici un aperçu de l'activité de Moonelec\n        </p>\n      </motion.div>\n\n      {/* Dashboard Cards */}\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n      >\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Clients\"\n            value={dashboardData.totalClients}\n            icon={<FaUsers className=\"text-blue-500\" />}\n            change={+12}\n            period=\"ce mois\"\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Commandes\"\n            value={dashboardData.totalOrders}\n            icon={<FaShoppingCart className=\"text-green-500\" />}\n            change={+24}\n            period=\"ce mois\"\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Produits\"\n            value={dashboardData.totalProducts}\n            icon={<FaBox className=\"text-purple-500\" />}\n            change={+85}\n            period=\"ce mois\"\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Chiffre d'affaires\"\n            value={dashboardData.totalRevenue}\n            icon={<FaChartLine className=\"text-yellow-500\" />}\n            change={+8.5}\n            period=\"ce mois\"\n            isCurrency\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Commerciaux\"\n            value={dashboardData.totalCommercials}\n            icon={<FaUserTie className=\"text-red-500\" />}\n            change={+2}\n            period=\"ce mois\"\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Fournisseurs\"\n            value={dashboardData.totalSuppliers}\n            icon={<FaBuilding className=\"text-indigo-500\" />}\n            change={+3}\n            period=\"ce mois\"\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Catégories\"\n            value={dashboardData.totalCategories}\n            icon={<FaTag className=\"text-pink-500\" />}\n            change={+2}\n            period=\"ce mois\"\n          />\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <DashboardCard\n            title=\"Marques\"\n            value={dashboardData.totalBrands}\n            icon={<FaCopyright className=\"text-cyan-500\" />}\n            change={+4}\n            period=\"ce mois\"\n          />\n        </motion.div>\n      </motion.div>\n\n      {/* Charts and Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          className=\"lg:col-span-2\"\n        >\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white mb-4\">\n              Évolution des ventes\n            </h2>\n            <DashboardChart />\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n        >\n          <RecentActivityCard />\n        </motion.div>\n      </div>\n\n      {/* Client Orders Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.5 }}\n        className=\"mb-6\"\n      >\n        <ClientOrdersCard />\n      </motion.div>\n\n      {/* Additional Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n        >\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white mb-4\">\n              Performance des commerciaux\n            </h2>\n            <CommercialPerformanceChart />\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.7 }}\n        >\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white mb-4\">\n              Répartition des produits par catégorie\n            </h2>\n            <ProductStatsChart />\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,cAAc;QACd,aAAa;QACb,eAAe;QACf,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,iBAAiB;QACjB,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,MAAM,qBAAqB;YACzB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,mDAAmD;YACrD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,MAAM;gBAAU,WAAW;YAAI;QAC/C;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;;4BAAmD;4BACnD,MAAM;4BAAU;4BAAE,MAAM;;;;;;;kCAEtC,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,YAAY;4BACjC,oBAAM,8OAAC,8IAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BACzB,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;kCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,WAAW;4BAChC,oBAAM,8OAAC,8IAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;4BAChC,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;kCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,aAAa;4BAClC,oBAAM,8OAAC,8IAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BACvB,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;kCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,YAAY;4BACjC,oBAAM,8OAAC,8IAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAC7B,QAAQ,CAAC;4BACT,QAAO;4BACP,UAAU;;;;;;;;;;;kCAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,gBAAgB;4BACrC,oBAAM,8OAAC,8IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAC3B,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;kCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,cAAc;4BACnC,oBAAM,8OAAC,8IAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAC5B,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;kCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,eAAe;4BACpC,oBAAM,8OAAC,8IAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BACvB,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;kCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,4IAAA,CAAA,UAAa;4BACZ,OAAM;4BACN,OAAO,cAAc,WAAW;4BAChC,oBAAM,8OAAC,8IAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAC7B,QAAQ,CAAC;4BACT,QAAO;;;;;;;;;;;;;;;;;0BAMb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC,6IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAInB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,iJAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;0BAKvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC,+IAAA,CAAA,UAAgB;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC,yJAAA,CAAA,UAA0B;;;;;;;;;;;;;;;;kCAI/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC,gJAAA,CAAA,UAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}]}