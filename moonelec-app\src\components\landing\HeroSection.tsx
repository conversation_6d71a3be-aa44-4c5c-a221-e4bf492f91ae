'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { FaArrowRight } from 'react-icons/fa';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';
import { LightbulbIcon, PlugIcon, BoltIcon } from '@/components/icons/ElectricalIcons';

export default function HeroSection() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center pt-20 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-primary/10 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-primary/5 to-transparent"></div>

        {/* Circuit Lines */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          {[
            { path: "M0,50 L100,50" },
            { path: "M20,0 L20,100" },
            { path: "M40,0 L40,100" },
            { path: "M60,0 L60,100" },
            { path: "M80,0 L80,100" },
            { path: "M0,25 L100,25" },
            { path: "M0,75 L100,75" }
          ].map((line, index) => (
            <path
              key={index}
              d={line.path}
              stroke="rgba(0, 109, 183, 0.1)"
              strokeWidth="0.2"
              fill="none"
              className="animate-fade-in"
            />
          ))}
        </svg>
      </div>

      <Container className="z-10 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="animate-slide-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-6 text-text-primary text-shadow">
              <span className="text-gradient">Moonelec</span>, Votre Partenaire en Matériel Électrique
            </h1>
            <p className="text-lg md:text-xl font-body text-text-secondary mb-8 leading-relaxed">
              Spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits pour tous vos projets résidentiels, tertiaires et industriels.
            </p>

            <div className="flex flex-wrap gap-4 mb-12">
              <Button
                variant="primary"
                icon={<FaArrowRight />}
                onClick={() => {
                  document.getElementById('products')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Découvrir nos produits
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Nous contacter
              </Button>
            </div>

            {/* Feature Icons */}
            <div className="grid grid-cols-3 gap-4">
              <FeatureIcon
                icon={<LightbulbIcon size={28} />}
                text="Éclairage"
                delay="delay-100"
              />
              <FeatureIcon
                icon={<PlugIcon size={28} />}
                text="Connectivité"
                delay="delay-200"
              />
              <FeatureIcon
                icon={<BoltIcon size={28} />}
                text="Énergie"
                delay="delay-300"
              />
            </div>
          </div>

          {/* Hero Image */}
          <div className="animate-slide-right relative">
            <div className="relative w-full h-[400px] md:h-[500px] rounded-lg overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?q=80&w=2069&auto=format&fit=crop"
                alt="Matériel électrique professionnel"
                fill
                style={{ objectFit: 'cover' }}
                className="rounded-lg"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-primary/40 to-transparent rounded-lg"></div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-6 -right-6 bg-white dark:bg-[#1a1a1a] p-4 rounded-lg shadow-lg animate-slide-up delay-500">
              <p className="font-display font-bold text-primary">+300 000</p>
              <p className="text-sm font-body">Références produits</p>
            </div>

            <div className="absolute -bottom-6 -left-6 bg-white dark:bg-[#1a1a1a] p-4 rounded-lg shadow-lg animate-slide-up delay-700">
              <p className="font-display font-bold text-secondary">Depuis 1990</p>
              <p className="text-sm font-body">À votre service</p>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
}

function FeatureIcon({
  icon,
  text,
  delay
}: {
  icon: React.ReactNode;
  text: string;
  delay: string;
}) {
  return (
    <div className={`flex flex-col items-center text-center animate-slide-up ${delay}`}>
      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-2">
        {icon}
      </div>
      <p className="text-sm font-body font-medium">{text}</p>
    </div>
  );
}
