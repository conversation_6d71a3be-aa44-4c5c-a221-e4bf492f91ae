import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ElectricIconProps {
  name: 'bulb' | 'lamp' | 'panel' | 'lightning' | 'flash' | 'power';
  size?: number;
  color?: string;
  style?: any;
}

export const ElectricIcon: React.FC<ElectricIconProps> = ({ 
  name, 
  size = 24, 
  color = '#006db7',
  style 
}) => {
  const getIconName = (iconName: string) => {
    switch (iconName) {
      case 'bulb':
        return 'bulb-outline';
      case 'lamp':
        return 'flashlight-outline';
      case 'panel':
        return 'grid-outline';
      case 'lightning':
        return 'flash-outline';
      case 'flash':
        return 'flash';
      case 'power':
        return 'power-outline';
      default:
        return 'bulb-outline';
    }
  };

  return (
    <View style={[styles.container, style]}>
      <Ionicons 
        name={getIconName(name) as any} 
        size={size} 
        color={color} 
      />
    </View>
  );
};

// Composant pour afficher plusieurs icônes électriques en arrière-plan
export const ElectricBackground: React.FC = () => {
  return (
    <View style={styles.backgroundContainer}>
      <ElectricIcon 
        name="bulb" 
        size={40} 
        color="rgba(0, 109, 183, 0.1)" 
        style={styles.icon1} 
      />
      <ElectricIcon 
        name="lightning" 
        size={30} 
        color="rgba(237, 28, 36, 0.1)" 
        style={styles.icon2} 
      />
      <ElectricIcon 
        name="panel" 
        size={35} 
        color="rgba(0, 109, 183, 0.08)" 
        style={styles.icon3} 
      />
      <ElectricIcon 
        name="lamp" 
        size={25} 
        color="rgba(237, 28, 36, 0.08)" 
        style={styles.icon4} 
      />
      <ElectricIcon 
        name="flash" 
        size={20} 
        color="rgba(0, 109, 183, 0.06)" 
        style={styles.icon5} 
      />
      <ElectricIcon 
        name="power" 
        size={28} 
        color="rgba(237, 28, 36, 0.06)" 
        style={styles.icon6} 
      />
    </View>
  );
};

// Composant pour les icônes de catégories
export const CategoryIcon: React.FC<{ category: string; size?: number; color?: string }> = ({ 
  category, 
  size = 24, 
  color = '#006db7' 
}) => {
  const getIconForCategory = (cat: string) => {
    const lowerCat = cat.toLowerCase();
    if (lowerCat.includes('éclairage') || lowerCat.includes('eclairage') || lowerCat.includes('lampe')) {
      return 'bulb';
    } else if (lowerCat.includes('panneau') || lowerCat.includes('tableau')) {
      return 'panel';
    } else if (lowerCat.includes('flash') || lowerCat.includes('urgence')) {
      return 'lightning';
    } else if (lowerCat.includes('projecteur') || lowerCat.includes('spot')) {
      return 'lamp';
    } else if (lowerCat.includes('alimentation') || lowerCat.includes('power')) {
      return 'power';
    } else {
      return 'flash';
    }
  };

  return (
    <ElectricIcon 
      name={getIconForCategory(category)} 
      size={size} 
      color={color} 
    />
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  icon1: {
    position: 'absolute',
    top: '10%',
    right: '15%',
  },
  icon2: {
    position: 'absolute',
    top: '25%',
    left: '10%',
  },
  icon3: {
    position: 'absolute',
    top: '45%',
    right: '20%',
  },
  icon4: {
    position: 'absolute',
    top: '60%',
    left: '15%',
  },
  icon5: {
    position: 'absolute',
    top: '75%',
    right: '10%',
  },
  icon6: {
    position: 'absolute',
    top: '35%',
    left: '80%',
  },
});

export default ElectricIcon;
