// User Types - Based on the existing database schema
export interface User {
  id: string;
  email: string;
  username: string;
  firstname: string;
  lastname: string;
  role: UserRole;
  clientId?: string;
  commercialId?: string;
  adminId?: string;
}

export enum UserRole {
  CLIENT = 'CLIENT',
  COMMERCIAL = 'COMMERCIAL',
  ADMIN = 'ADMIN'
}

// Product Types - Based on existing API
export interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  characteristics: Record<string, any>;
  mainImage?: string;
  images: string[];
  categoryId?: string;
  brandId?: string;
  createdAt: string;
  updatedAt: string;
  category?: Category;
  brand?: Brand;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
  parent?: Category;
  children?: Category[];
}

export interface Brand {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  createdAt: string;
  updatedAt: string;
}

// Quote Types - Based on existing API
export interface Quote {
  id: string;
  clientId: string;
  status: QuoteStatus;
  validUntil: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: QuoteItem[];
  client?: any;
  totalAmount?: number;
}

export enum QuoteStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED'
}

export interface QuoteItem {
  id: string;
  quoteId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  product: Product;
}

// Sales Report Types - Based on existing API
export interface SalesReport {
  id: string;
  commercialId: string;
  need: string;
  articleRef?: string;
  comment?: string;
  visitDate: string;
  denomination: string;
  name: string;
  visitPurpose: string;
  complaint?: string;
  city: string;
  images: string[];
  videoUrl?: string;
  audioUrl?: string;
  pdfUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SalesReportInput {
  need: string;
  articleRef?: string;
  comment?: string;
  visitDate: Date;
  denomination: string;
  name: string;
  visitPurpose: string;
  complaint?: string;
  city: string;
  images: string[];
  videoUrl?: string;
  audioUrl?: string;
  pdfUrl?: string;
}

// API Response Types
export interface ProductsResponse {
  products: Product[];
  total: number;
}

export interface QuotesResponse {
  quotes: Quote[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SalesReportsResponse {
  reports: SalesReport[];
  total: number;
  skip: number;
  take: number;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Home: undefined;
  Products: undefined;
  ProductDetail: { productId: string };
  Categories: { parentId?: string; parentName?: string };
  CategoryProducts: { categoryId: string; categoryName: string };
  Quotes: undefined;
  QuoteDetail: { quoteId: string };
  CreateQuote: { preselectedProduct?: { productId: string; quantity: number } };
  SalesReports: undefined;
  SalesReportDetail: { reportId: string };
  CreateSalesReport: undefined;
  Chat: undefined;
  ChatConversation: { conversationId: string; conversationName: string };
  Profile: undefined;
};

export type BottomTabParamList = {
  Home: undefined;
  Products: undefined;
  Quotes: undefined;
  Reports: undefined;
  Chat: undefined;
  Profile: undefined;
};

// Form Types
export interface LoginForm {
  username: string;
  password: string;
}

// Chat Types
export interface ChatUser {
  id: string;
  userId: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'COMMERCIAL';
  profilePhoto?: string;
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: 'ADMIN' | 'COMMERCIAL';
  content: string;
  messageType: 'TEXT' | 'FILE' | 'IMAGE';
  fileUrl?: string;
  fileName?: string;
  isRead: boolean;
  createdAt: string;
}

export interface ChatConversation {
  id: string;
  adminId: string;
  commercialId: string;
  admin: {
    user: {
      id: string;
      firstname: string;
      lastname: string;
      email: string;
    };
  };
  commercial: {
    user: {
      id: string;
      firstname: string;
      lastname: string;
      email: string;
    };
  };
  lastMessage?: string;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}
