import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Quote, User } from '../../types';
import ApiService from '../../services/api';
import WebApiService from '../../services/webApi';
import { useAuth } from '../../contexts/AuthContext';
import LoadingAnimation from '../../components/LoadingAnimation';

interface CommercialStats {
  userId: string;
  user: User;
  totalQuotes: number;
  pendingQuotes: number;
  acceptedQuotes: number;
  rejectedQuotes: number;
  totalValue: number;
  lastActivity: string;
}

const CommercialReportsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [commercialStats, setCommercialStats] = useState<CommercialStats[]>([]);
  const [recentQuotes, setRecentQuotes] = useState<Quote[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');

  useEffect(() => {
    if (user?.role !== 'ADMIN') {
      Alert.alert('Accès refusé', 'Cette fonctionnalité est réservée aux administrateurs');
      navigation.goBack();
      return;
    }
    loadReports();
  }, [user, selectedPeriod]);

  const loadReports = async () => {
    try {
      setIsLoading(true);
      console.log('📊 Loading commercial reports with WebAPI...');

      // Try WebAPI first
      try {
        const quotesResponse = await WebApiService.getQuotes({
          dashboard: true,
          limit: 50
        });

        const quotes = quotesResponse.quotes || [];
        setRecentQuotes(quotes);
        console.log('✅ Reports loaded with WebAPI:', quotes.length, 'quotes');
      } catch (webApiError) {
        console.log('⚠️ WebAPI failed, falling back to original API:', webApiError);

        const quotesResponse = await ApiService.getQuotes({
          limit: 50
        });

        const quotes = quotesResponse.quotes || [];
        setRecentQuotes(quotes);
        console.log('✅ Reports loaded with fallback API');
      }

      // Calculer les statistiques par commercial
      const statsMap = new Map<string, CommercialStats>();

      quotes.forEach((quote: any) => {
        if (quote.createdByAdminId) {
          const userId = quote.createdByAdminId;

          if (!statsMap.has(userId)) {
            statsMap.set(userId, {
              userId,
              user: { id: userId, firstname: 'Admin', lastname: '', email: '', role: 'ADMIN' },
              totalQuotes: 0,
              pendingQuotes: 0,
              acceptedQuotes: 0,
              rejectedQuotes: 0,
              totalValue: 0,
              lastActivity: quote.createdAt,
            });
          }

          const stats = statsMap.get(userId)!;
          stats.totalQuotes++;
          stats.totalValue += quote.totalPrice || 0;

          switch (quote.status) {
            case 'PENDING':
              stats.pendingQuotes++;
              break;
            case 'ACCEPTED':
              stats.acceptedQuotes++;
              break;
            case 'REJECTED':
              stats.rejectedQuotes++;
              break;
          }

          if (new Date(quote.createdAt) > new Date(stats.lastActivity)) {
            stats.lastActivity = quote.createdAt;
          }
        }
      });

      setCommercialStats(Array.from(statsMap.values()));
    } catch (error) {
      console.error('Error loading reports:', error);
      Alert.alert('Erreur', 'Impossible de charger les rapports');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReports();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return '#f39c12';
      case 'ACCEPTED':
        return '#27ae60';
      case 'REJECTED':
        return '#e74c3c';
      default:
        return '#95a5a6';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'En attente';
      case 'ACCEPTED':
        return 'Accepté';
      case 'REJECTED':
        return 'Refusé';
      default:
        return status;
    }
  };

  const renderCommercialStats = ({ item }: { item: CommercialStats }) => (
    <View style={styles.statsCard}>
      <View style={styles.statsHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            👤 {item.user.firstname} {item.user.lastname}
          </Text>
          <Text style={styles.userEmail}>{item.user.email}</Text>
        </View>
        <View style={styles.totalValue}>
          <Text style={styles.totalValueText}>{formatCurrency(item.totalValue)}</Text>
        </View>
      </View>

      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{item.totalQuotes}</Text>
          <Text style={styles.statLabel}>Total devis</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: '#f39c12' }]}>{item.pendingQuotes}</Text>
          <Text style={styles.statLabel}>En attente</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: '#27ae60' }]}>{item.acceptedQuotes}</Text>
          <Text style={styles.statLabel}>Acceptés</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: '#e74c3c' }]}>{item.rejectedQuotes}</Text>
          <Text style={styles.statLabel}>Refusés</Text>
        </View>
      </View>

      <Text style={styles.lastActivity}>
        Dernière activité: {formatDate(item.lastActivity)}
      </Text>
    </View>
  );

  const renderRecentQuote = ({ item }: { item: Quote }) => (
    <View style={styles.quoteCard}>
      <View style={styles.quoteHeader}>
        <Text style={styles.quoteId}>Devis #{item.id.slice(-8)}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>

      <Text style={styles.quoteClient}>
        👤 {(item as any).client?.user?.firstname} {(item as any).client?.user?.lastname}
      </Text>

      <View style={styles.quoteFooter}>
        <Text style={styles.quoteDate}>{formatDate(item.createdAt)}</Text>
        <Text style={styles.quoteValue}>{formatCurrency(item.totalPrice || 0)}</Text>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingAnimation size={60} color="#006db7" />
        <Text style={styles.loadingText}>Chargement des rapports...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>📊 Rapports Commerciaux</Text>
      </View>

      <ScrollView
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {(['week', 'month', 'quarter'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === period && styles.periodButtonTextActive,
                ]}
              >
                {period === 'week' ? 'Semaine' : period === 'month' ? 'Mois' : 'Trimestre'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Statistiques globales */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📈 Vue d'ensemble</Text>
          <View style={styles.overviewGrid}>
            <View style={styles.overviewCard}>
              <Text style={styles.overviewNumber}>{recentQuotes.length}</Text>
              <Text style={styles.overviewLabel}>Total devis</Text>
            </View>
            <View style={styles.overviewCard}>
              <Text style={styles.overviewNumber}>
                {recentQuotes.filter(q => q.status === 'PENDING').length}
              </Text>
              <Text style={styles.overviewLabel}>En attente</Text>
            </View>
            <View style={styles.overviewCard}>
              <Text style={styles.overviewNumber}>
                {formatCurrency(recentQuotes.reduce((sum, q) => sum + (q.totalPrice || 0), 0))}
              </Text>
              <Text style={styles.overviewLabel}>Valeur totale</Text>
            </View>
          </View>
        </View>

        {/* Statistiques par commercial */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👥 Performance par commercial</Text>
          {commercialStats.length > 0 ? (
            <FlatList
              data={commercialStats}
              renderItem={renderCommercialStats}
              keyExtractor={(item) => item.userId}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>Aucune activité commerciale</Text>
            </View>
          )}
        </View>

        {/* Devis récents */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📋 Devis récents</Text>
          {recentQuotes.length > 0 ? (
            <FlatList
              data={recentQuotes.slice(0, 10)}
              renderItem={renderRecentQuote}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>Aucun devis récent</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  periodButtonActive: {
    backgroundColor: '#006db7',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: '#fff',
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  overviewGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  overviewCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  overviewNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#006db7',
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  statsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  totalValue: {
    alignItems: 'flex-end',
  },
  totalValueText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  lastActivity: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  quoteCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  quoteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  quoteId: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  quoteClient: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  quoteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quoteDate: {
    fontSize: 12,
    color: '#999',
  },
  quoteValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#006db7',
  },
  emptyState: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});

export default CommercialReportsScreen;
