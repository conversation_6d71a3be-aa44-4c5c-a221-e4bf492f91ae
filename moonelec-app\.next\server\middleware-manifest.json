{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/mobpY1jJQR827egClFDoUlqDmZhLjxU8IMegj93L/8=", "__NEXT_PREVIEW_MODE_ID": "7408bb51a25cb48dee33338068bfc6eb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "76e14fd3782d896f39b435db279835e26702198571a94ca9121d86f7796e5fb6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "62a47a9e258b65ad85b9e7fc78f14afcaff083f05ebfe3ac8e4dfcee6cfdcfc8"}}}, "sortedMiddleware": ["/"], "functions": {}}