{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_27cf6c7d._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_a35b62f3.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/mobpY1jJQR827egClFDoUlqDmZhLjxU8IMegj93L/8=", "__NEXT_PREVIEW_MODE_ID": "2b072383cbf9bf01009c01d8e7694566", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "eba77934d8ae43ddf405b58a116c3600ccaf88bb8b1f170d8c805036e36a0841", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "07c39a77023799d84e9a2cb69f367d31ec038ff760a13e798e4f71260a8ca96f"}}}, "sortedMiddleware": ["/"], "functions": {}}