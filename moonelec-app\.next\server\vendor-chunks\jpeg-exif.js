"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jpeg-exif";
exports.ids = ["vendor-chunks/jpeg-exif"];
exports.modules = {

/***/ "(rsc)/./node_modules/jpeg-exif/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/jpeg-exif/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _fs = __webpack_require__(/*! fs */ \"fs\");\n\nvar _fs2 = _interopRequireDefault(_fs);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar tags = __webpack_require__(/*! ./tags.json */ \"(rsc)/./node_modules/jpeg-exif/lib/tags.json\");\n\n/*\n unsignedByte,\n asciiStrings,\n unsignedShort,\n unsignedLong,\n unsignedRational,\n signedByte,\n undefined,\n signedShort,\n signedLong,\n signedRational,\n singleFloat,\n doubleFloat\n */\nvar bytes = [0, 1, 1, 2, 4, 8, 1, 1, 2, 4, 8, 4, 8];\nvar SOIMarkerLength = 2;\nvar JPEGSOIMarker = 0xffd8;\nvar TIFFINTEL = 0x4949;\nvar TIFFMOTOROLA = 0x4d4d;\nvar APPMarkerLength = 2;\nvar APPMarkerBegin = 0xffe0;\nvar APPMarkerEnd = 0xffef;\nvar data = void 0;\n/**\n * @param buffer {Buffer}\n * @returns {Boolean}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var isImage = isValid(content);\n * console.log(isImage);\n */\nvar isValid = function isValid(buffer) {\n  try {\n    var SOIMarker = buffer.readUInt16BE(0);\n    return SOIMarker === JPEGSOIMarker;\n  } catch (e) {\n    throw new Error('Unsupport file format.');\n  }\n};\n/**\n * @param buffer {Buffer}\n * @returns {Boolean}\n * @example\n */\nvar isTiff = function isTiff(buffer) {\n  try {\n    var SOIMarker = buffer.readUInt16BE(0);\n    return SOIMarker === TIFFINTEL || SOIMarker === TIFFMOTOROLA;\n  } catch (e) {\n    throw new Error('Unsupport file format.');\n  }\n};\n/**\n * @param buffer {Buffer}\n * @returns {Number}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var APPNumber = checkAPPn(content);\n * console.log(APPNumber);\n */\nvar checkAPPn = function checkAPPn(buffer) {\n  try {\n    var APPMarkerTag = buffer.readUInt16BE(0);\n    var isInRange = APPMarkerTag >= APPMarkerBegin && APPMarkerTag <= APPMarkerEnd;\n    return isInRange ? APPMarkerTag - APPMarkerBegin : false;\n  } catch (e) {\n    throw new Error('Invalid APP Tag.');\n  }\n};\n/**\n * @param buffer {Buffer}\n * @param tagCollection {Object}\n * @param order {Boolean}\n * @param offset {Number}\n * @returns {Object}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var exifFragments = IFDHandler(content, 0, true, 8);\n * console.log(exifFragments.value);\n */\nvar IFDHandler = function IFDHandler(buffer, tagCollection, order, offset) {\n  var entriesNumber = order ? buffer.readUInt16BE(0) : buffer.readUInt16LE(0);\n\n  if (entriesNumber === 0) {\n    return {};\n  }\n\n  var entriesNumberLength = 2;\n  var entries = buffer.slice(entriesNumberLength);\n  var entryLength = 12;\n  // let nextIFDPointerBegin = entriesNumberLength + entryLength * entriesNumber;\n  // let bigNextIFDPointer= buffer.readUInt32BE(nextIFDPointerBegin) ;\n  // let littleNextIFDPointer= buffer.readUInt32LE(nextIFDPointerBegin);\n  // let nextIFDPointer = order ?bigNextIFDPointer:littleNextIFDPointer;\n  var exif = {};\n  var entryCount = 0;\n\n  for (entryCount; entryCount < entriesNumber; entryCount += 1) {\n    var entryBegin = entryCount * entryLength;\n    var entry = entries.slice(entryBegin, entryBegin + entryLength);\n    var tagBegin = 0;\n    var tagLength = 2;\n    var dataFormatBegin = tagBegin + tagLength;\n    var dataFormatLength = 2;\n    var componentsBegin = dataFormatBegin + dataFormatLength;\n    var componentsNumberLength = 4;\n    var dataValueBegin = componentsBegin + componentsNumberLength;\n    var dataValueLength = 4;\n    var tagAddress = entry.slice(tagBegin, dataFormatBegin);\n    var tagNumber = order ? tagAddress.toString('hex') : tagAddress.reverse().toString('hex');\n    var tagName = tagCollection[tagNumber];\n    var bigDataFormat = entry.readUInt16BE(dataFormatBegin);\n    var littleDataFormat = entry.readUInt16LE(dataFormatBegin);\n    var dataFormat = order ? bigDataFormat : littleDataFormat;\n    var componentsByte = bytes[dataFormat];\n    var bigComponentsNumber = entry.readUInt32BE(componentsBegin);\n    var littleComponentNumber = entry.readUInt32LE(componentsBegin);\n    var componentsNumber = order ? bigComponentsNumber : littleComponentNumber;\n    var dataLength = componentsNumber * componentsByte;\n    var dataValue = entry.slice(dataValueBegin, dataValueBegin + dataValueLength);\n\n    if (dataLength > 4) {\n      var dataOffset = (order ? dataValue.readUInt32BE(0) : dataValue.readUInt32LE(0)) - offset;\n      dataValue = buffer.slice(dataOffset, dataOffset + dataLength);\n    }\n\n    var tagValue = void 0;\n\n    if (tagName) {\n      switch (dataFormat) {\n        case 1:\n          tagValue = dataValue.readUInt8(0);\n          break;\n        case 2:\n          tagValue = dataValue.toString('ascii').replace(/\\0+$/, '');\n          break;\n        case 3:\n          tagValue = order ? dataValue.readUInt16BE(0) : dataValue.readUInt16LE(0);\n          break;\n        case 4:\n          tagValue = order ? dataValue.readUInt32BE(0) : dataValue.readUInt32LE(0);\n          break;\n        case 5:\n          tagValue = [];\n\n          for (var i = 0; i < dataValue.length; i += 8) {\n            var bigTagValue = dataValue.readUInt32BE(i) / dataValue.readUInt32BE(i + 4);\n            var littleTagValue = dataValue.readUInt32LE(i) / dataValue.readUInt32LE(i + 4);\n            tagValue.push(order ? bigTagValue : littleTagValue);\n          }\n\n          break;\n        case 7:\n          switch (tagName) {\n            case 'ExifVersion':\n              tagValue = dataValue.toString();\n              break;\n            case 'FlashPixVersion':\n              tagValue = dataValue.toString();\n              break;\n            case 'SceneType':\n              tagValue = dataValue.readUInt8(0);\n              break;\n            default:\n              tagValue = '0x' + dataValue.toString('hex', 0, 15);\n              break;\n          }\n          break;\n        case 10:\n          {\n            var bigOrder = dataValue.readInt32BE(0) / dataValue.readInt32BE(4);\n            var littleOrder = dataValue.readInt32LE(0) / dataValue.readInt32LE(4);\n            tagValue = order ? bigOrder : littleOrder;\n            break;\n          }\n        default:\n          tagValue = '0x' + dataValue.toString('hex');\n          break;\n      }\n      exif[tagName] = tagValue;\n    }\n    /*\n     else {\n     console.log(`Unkown Tag [0x${tagNumber}].`);\n     }\n     */\n  }\n  return exif;\n};\n\n/**\n * @param buf {Buffer}\n * @returns {Undefined}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var exifFragments = EXIFHandler(content);\n */\nvar EXIFHandler = function EXIFHandler(buf) {\n  var pad = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n  var buffer = buf;\n\n  if (pad) {\n    buffer = buf.slice(APPMarkerLength);\n    var length = buffer.readUInt16BE(0);\n    buffer = buffer.slice(0, length);\n    var lengthLength = 2;\n    buffer = buffer.slice(lengthLength);\n    var identifierLength = 5;\n    buffer = buffer.slice(identifierLength);\n    var padLength = 1;\n    buffer = buffer.slice(padLength);\n  }\n\n  var byteOrderLength = 2;\n  var byteOrder = buffer.toString('ascii', 0, byteOrderLength) === 'MM';\n  var fortyTwoLength = 2;\n  var fortyTwoEnd = byteOrderLength + fortyTwoLength;\n  var big42 = buffer.readUInt32BE(fortyTwoEnd);\n  var little42 = buffer.readUInt32LE(fortyTwoEnd);\n  var offsetOfIFD = byteOrder ? big42 : little42;\n\n  buffer = buffer.slice(offsetOfIFD);\n\n  if (buffer.length > 0) {\n    data = IFDHandler(buffer, tags.ifd, byteOrder, offsetOfIFD);\n\n    if (data.ExifIFDPointer) {\n      buffer = buffer.slice(data.ExifIFDPointer - offsetOfIFD);\n      data.SubExif = IFDHandler(buffer, tags.ifd, byteOrder, data.ExifIFDPointer);\n    }\n\n    if (data.GPSInfoIFDPointer) {\n      var gps = data.GPSInfoIFDPointer;\n      buffer = buffer.slice(data.ExifIFDPointer ? gps - data.ExifIFDPointer : gps - offsetOfIFD);\n      data.GPSInfo = IFDHandler(buffer, tags.gps, byteOrder, gps);\n    }\n  }\n};\n\n/**\n * @param buffer {Buffer}\n * @returns {Undefined}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var exifFragments = APPnHandler(content);\n */\nvar APPnHandler = function APPnHandler(buffer) {\n  var APPMarkerTag = checkAPPn(buffer);\n\n  if (APPMarkerTag !== false) {\n    // APP0 is 0, and 0==false\n    var length = buffer.readUInt16BE(APPMarkerLength);\n\n    switch (APPMarkerTag) {\n      case 1:\n        // EXIF\n        EXIFHandler(buffer);\n        break;\n      default:\n        APPnHandler(buffer.slice(APPMarkerLength + length));\n        break;\n    }\n  }\n};\n\n/**\n * @param buffer {Buffer}\n * @returns {Object}\n * @example\n */\nvar fromBuffer = function fromBuffer(buffer) {\n  if (!buffer) {\n    throw new Error('buffer not found');\n  }\n\n  data = undefined;\n\n  if (isValid(buffer)) {\n    buffer = buffer.slice(SOIMarkerLength);\n    data = {};\n    APPnHandler(buffer);\n  } else if (isTiff(buffer)) {\n    data = {};\n    EXIFHandler(buffer, false);\n  }\n\n  return data;\n};\n\n/**\n * @param file {String}\n * @returns {Object}\n * @example\n * var exif = sync(\"~/Picture/IMG_1981.JPG\");\n * console.log(exif.createTime);\n */\nvar sync = function sync(file) {\n  if (!file) {\n    throw new Error('File not found');\n  }\n\n  var buffer = _fs2.default.readFileSync(file);\n\n  return fromBuffer(buffer);\n};\n\n/**\n * @param file {String}\n * @param callback {Function}\n * @example\n * async(\"~/Picture/IMG_0707.JPG\", (err, data) => {\n *     if(err) {\n *         console.log(err);\n *     }\n *     if(data) {\n *         console.log(data.ExifOffset.createTime);\n *     }\n * }\n */\nvar async = function async(file, callback) {\n  data = undefined;\n\n  new Promise(function (resolve, reject) {\n    if (!file) {\n      reject(new Error('❓File not found.'));\n    }\n\n    _fs2.default.readFile(file, function (err, buffer) {\n      if (err) {\n        reject(err);\n      } else {\n        try {\n          if (isValid(buffer)) {\n            var buf = buffer.slice(SOIMarkerLength);\n\n            data = {};\n\n            APPnHandler(buf);\n            resolve(data);\n          } else if (isTiff(buffer)) {\n            data = {};\n\n            EXIFHandler(buffer, false);\n            resolve(data);\n          } else {\n            reject(new Error('😱Unsupport file type.'));\n          }\n        } catch (e) {\n          reject(e);\n        }\n      }\n    });\n  }, function (error) {\n    callback(error, undefined);\n  }).then(function (d) {\n    callback(undefined, d);\n  }).catch(function (error) {\n    callback(error, undefined);\n  });\n};\n\nexports.fromBuffer = fromBuffer;\nexports.parse = async;\nexports.parseSync = sync;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jpeg-exif/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jpeg-exif/lib/tags.json":
/*!**********************************************!*\
  !*** ./node_modules/jpeg-exif/lib/tags.json ***!
  \**********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"ifd":{"8298":"Copyright","8769":"ExifIFDPointer","8822":"ExposureProgram","8824":"SpectralSensitivity","8825":"GPSInfoIFDPointer","8827":"PhotographicSensitivity","8828":"OECF","8830":"SensitivityType","8831":"StandardOutputSensitivity","8832":"RecommendedExposureIndex","8833":"ISOSpeed","8834":"ISOSpeedLatitudeyyy","8835":"ISOSpeedLatitudezzz","9000":"ExifVersion","9003":"DateTimeOriginal","9004":"DateTimeDigitized","9101":"ComponentsConfiguration","9102":"CompressedBitsPerPixel","9201":"ShutterSpeedValue","9202":"ApertureValue","9203":"BrightnessValue","9204":"ExposureBiasValue","9205":"MaxApertureValue","9206":"SubjectDistance","9207":"MeteringMode","9208":"LightSource","9209":"Flash","9214":"SubjectArea","9286":"UserComment","9290":"SubSecTime","9291":"SubSecTimeOriginal","9292":"SubSecTimeDigitized","010e":"ImageDescription","010f":"Make","011a":"XResolution","011b":"YResolution","011c":"PlanarConfiguration","012d":"TransferFunction","013b":"Artist","013e":"WhitePoint","013f":"PrimaryChromaticities","0100":"ImageWidth","0101":"ImageHeight","0102":"BitsPerSample","0103":"Compression","0106":"PhotometricInterpretation","0110":"Model","0111":"StripOffsets","0112":"Orientation","0115":"SamplesPerPixel","0116":"RowsPerStrip","0117":"StripByteCounts","0128":"ResolutionUnit","0131":"Software","0132":"DateTime","0201":"JPEGInterchangeFormat","0202":"JPEGInterchangeFormatLength","0211":"YCbCrCoefficients","0212":"YCbCrSubSampling","0213":"YCbCrPositioning","0214":"ReferenceBlackWhite","829a":"ExposureTime","829d":"FNumber","920a":"FocalLength","927c":"MakerNote","a000":"FlashpixVersion","a001":"ColorSpace","a002":"PixelXDimension","a003":"PixelYDimension","a004":"RelatedSoundFile","a005":"InteroperabilityIFDPointer","a20b":"FlashEnergy","a20c":"SpatialFrequencyResponse","a20e":"FocalPlaneXResolution","a20f":"FocalPlaneYResolution","a40a":"Sharpness","a40b":"DeviceSettingDescription","a40c":"SubjectDistanceRange","a210":"FocalPlaneResolutionUnit","a214":"SubjectLocation","a215":"ExposureIndex","a217":"SensingMethod","a300":"FileSource","a301":"SceneType","a302":"CFAPattern","a401":"CustomRendered","a402":"ExposureMode","a403":"WhiteBalance","a404":"DigitalZoomRatio","a405":"FocalLengthIn35mmFilm","a406":"SceneCaptureType","a407":"GainControl","a408":"Contrast","a409":"Saturation","a420":"ImageUniqueID","a430":"CameraOwnerName","a431":"BodySerialNumber","a432":"LensSpecification","a433":"LensMake","a434":"LensModel","a435":"LensSerialNumber","a500":"Gamma"},"gps":{"0000":"GPSVersionID","0001":"GPSLatitudeRef","0002":"GPSLatitude","0003":"GPSLongitudeRef","0004":"GPSLongitude","0005":"GPSAltitudeRef","0006":"GPSAltitude","0007":"GPSTimeStamp","0008":"GPSSatellites","0009":"GPSStatus","000a":"GPSMeasureMode","000b":"GPSDOP","000c":"GPSSpeedRef","000d":"GPSSpeed","000e":"GPSTrackRef","000f":"GPSTrack","0010":"GPSImgDirectionRef","0011":"GPSImgDirection","0012":"GPSMapDatum","0013":"GPSDestLatitudeRef","0014":"GPSDestLatitude","0015":"GPSDestLongitudeRef","0016":"GPSDestLongitude","0017":"GPSDestBearingRef","0018":"GPSDestBearing","0019":"GPSDestDistanceRef","001a":"GPSDestDistance","001b":"GPSProcessingMethod","001c":"GPSAreaInformation","001d":"GPSDateStamp","001e":"GPSDifferential","001f":"GPSHPositioningError"}}');

/***/ })

};
;