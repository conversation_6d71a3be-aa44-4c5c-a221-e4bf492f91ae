{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      console.log('🔐 No valid authorization header found');\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n    console.log('🔐 Mobile token received (length):', token.length);\n\n    // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET\n    let decoded: any;\n    try {\n      const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';\n      console.log('🔐 Trying JWT_SECRET for token verification');\n      decoded = jwt.verify(token, jwtSecret) as any;\n      console.log('🔐 Token decoded successfully with JWT_SECRET:', { userId: decoded.userId, role: decoded.role });\n    } catch (jwtError) {\n      console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');\n      try {\n        const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';\n        decoded = jwt.verify(token, nextAuthSecret) as any;\n        console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', { userId: decoded.userId, role: decoded.role });\n      } catch (nextAuthError) {\n        console.error('🔐 Both JWT secrets failed:', { jwtError: jwtError.message, nextAuthError: nextAuthError.message });\n        throw new Error('Invalid token signature');\n      }\n    }\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n      }\n    });\n\n    if (!user) {\n      console.log('🔐 User not found:', { found: !!user });\n      return null;\n    }\n\n    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n\n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') ||\n         userAgent.includes('Expo') ||\n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  // Always try to verify mobile token if Authorization header is present\n  const authHeader = request.headers.get('authorization');\n  if (authHeader?.startsWith('Bearer ')) {\n    return await verifyMobileToken(request);\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QACjE,QAAQ,GAAG,CAAC,sCAAsC,MAAM,MAAM;QAE9D,kFAAkF;QAClF,IAAI;QACJ,IAAI;YACF,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI;YAC5C,QAAQ,GAAG,CAAC;YACZ,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAC5B,QAAQ,GAAG,CAAC,kDAAkD;gBAAE,QAAQ,QAAQ,MAAM;gBAAE,MAAM,QAAQ,IAAI;YAAC;QAC7G,EAAE,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,eAAe,IAAI;gBACtD,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;gBAC5B,QAAQ,GAAG,CAAC,uDAAuD;oBAAE,QAAQ,QAAQ,MAAM;oBAAE,MAAM,QAAQ,IAAI;gBAAC;YAClH,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,+BAA+B;oBAAE,UAAU,SAAS,OAAO;oBAAE,eAAe,cAAc,OAAO;gBAAC;gBAChH,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QAEA,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,sBAAsB;gBAAE,OAAO,CAAC,CAAC;YAAK;YAClD,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,8CAA8C;YAAE,IAAI,KAAK,EAAE;YAAE,MAAM,KAAK,IAAI;QAAC;QACzF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,uEAAuE;IACvE,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,YAAY,WAAW,YAAY;QACrC,OAAO,MAAM,kBAAkB;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/commercials/dashboard/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { verifyMobileToken } from '@/lib/mobile-auth';\nimport { prisma } from '@/lib/prisma';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Verify authentication\n    const user = await verifyMobileToken(request);\n    if (!user) {\n      return NextResponse.json(\n        { error: 'Non autorisé' },\n        { status: 401 }\n      );\n    }\n\n    // Check if user is a commercial\n    if (user.role !== 'COMMERCIAL') {\n      return NextResponse.json(\n        { error: 'Accès refusé - Rôle commercial requis' },\n        { status: 403 }\n      );\n    }\n\n    // Get current date for calculations\n    const now = new Date();\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);\n\n    // Get clients assigned to this commercial\n    const totalClients = await prisma.user.count({\n      where: {\n        role: 'CLIENT',\n        commercialId: user.id,\n      },\n    });\n\n    // Get new clients this month\n    const newClients = await prisma.user.count({\n      where: {\n        role: 'CLIENT',\n        commercialId: user.id,\n        createdAt: {\n          gte: startOfMonth,\n        },\n      },\n    });\n\n    // Get quotes for this commercial's clients\n    const quotes = await prisma.quote.findMany({\n      where: {\n        user: {\n          commercialId: user.id,\n        },\n      },\n      include: {\n        items: true,\n      },\n    });\n\n    // Calculate total sales (sum of all quotes)\n    const totalSales = quotes.reduce((sum, quote) => {\n      const quoteTotal = quote.items.reduce((itemSum, item) => {\n        return itemSum + (item.quantity * item.unitPrice);\n      }, 0);\n      return sum + quoteTotal;\n    }, 0);\n\n    // Calculate monthly sales (this month)\n    const monthlyQuotes = quotes.filter(quote =>\n      quote.createdAt >= startOfMonth\n    );\n    const monthlySales = monthlyQuotes.reduce((sum, quote) => {\n      const quoteTotal = quote.items.reduce((itemSum, item) => {\n        return itemSum + (item.quantity * item.unitPrice);\n      }, 0);\n      return sum + quoteTotal;\n    }, 0);\n\n    // Get pending quotes count\n    const pendingOrders = await prisma.quote.count({\n      where: {\n        user: {\n          commercialId: user.id,\n        },\n        status: 'PENDING',\n      },\n    });\n\n    // Get recent clients with their last quote\n    const recentClients = await prisma.user.findMany({\n      where: {\n        role: 'CLIENT',\n        commercialId: user.id,\n      },\n      include: {\n        quotes: {\n          orderBy: {\n            createdAt: 'desc',\n          },\n          take: 1,\n        },\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n      take: 5,\n    });\n\n    // Format recent clients data\n    const formattedRecentClients = recentClients.map(client => ({\n      id: client.id,\n      firstname: client.firstname,\n      lastname: client.lastname,\n      email: client.email,\n      company: client.company,\n      lastOrder: client.quotes[0]\n        ? client.quotes[0].createdAt.toLocaleDateString('fr-FR')\n        : null,\n      status: client.quotes.length > 0 ? 'Actif' : 'Nouveau',\n    }));\n\n    // Get recent quotes\n    const recentQuotes = await prisma.quote.findMany({\n      where: {\n        user: {\n          commercialId: user.id,\n        },\n      },\n      include: {\n        user: true,\n        items: true,\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n      take: 5,\n    });\n\n    // Format recent quotes data\n    const formattedRecentQuotes = recentQuotes.map(quote => {\n      const total = quote.items.reduce((sum, item) => {\n        return sum + (item.quantity * item.unitPrice);\n      }, 0);\n\n      return {\n        id: quote.id,\n        clientName: `${quote.user.firstname} ${quote.user.lastname}`,\n        company: quote.user.company,\n        total: total,\n        status: quote.status,\n        createdAt: quote.createdAt.toLocaleDateString('fr-FR'),\n      };\n    });\n\n    // Mock upcoming meetings (you can implement a meetings table later)\n    const upcomingMeetings = 3;\n\n    const dashboardData = {\n      totalClients,\n      newClients,\n      totalSales,\n      monthlySales,\n      pendingOrders,\n      upcomingMeetings,\n      recentClients: formattedRecentClients,\n      recentQuotes: formattedRecentQuotes,\n    };\n\n    return NextResponse.json(dashboardData);\n\n  } catch (error) {\n    console.error('Error fetching commercial dashboard data:', error);\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des données du tableau de bord' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,wBAAwB;QACxB,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;QACrC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,IAAI,KAAK,IAAI,KAAK,cAAc;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,MAAM,MAAM,IAAI;QAChB,MAAM,eAAe,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;QACjE,MAAM,mBAAmB,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG;QACzE,MAAM,iBAAiB,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;QAEnE,0CAA0C;QAC1C,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3C,OAAO;gBACL,MAAM;gBACN,cAAc,KAAK,EAAE;YACvB;QACF;QAEA,6BAA6B;QAC7B,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzC,OAAO;gBACL,MAAM;gBACN,cAAc,KAAK,EAAE;gBACrB,WAAW;oBACT,KAAK;gBACP;YACF;QACF;QAEA,2CAA2C;QAC3C,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC,OAAO;gBACL,MAAM;oBACJ,cAAc,KAAK,EAAE;gBACvB;YACF;YACA,SAAS;gBACP,OAAO;YACT;QACF;QAEA,4CAA4C;QAC5C,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK;YACrC,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS;gBAC9C,OAAO,UAAW,KAAK,QAAQ,GAAG,KAAK,SAAS;YAClD,GAAG;YACH,OAAO,MAAM;QACf,GAAG;QAEH,uCAAuC;QACvC,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAClC,MAAM,SAAS,IAAI;QAErB,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK;YAC9C,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS;gBAC9C,OAAO,UAAW,KAAK,QAAQ,GAAG,KAAK,SAAS;YAClD,GAAG;YACH,OAAO,MAAM;QACf,GAAG;QAEH,2BAA2B;QAC3B,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAC7C,OAAO;gBACL,MAAM;oBACJ,cAAc,KAAK,EAAE;gBACvB;gBACA,QAAQ;YACV;QACF;QAEA,2CAA2C;QAC3C,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL,MAAM;gBACN,cAAc,KAAK,EAAE;YACvB;YACA,SAAS;gBACP,QAAQ;oBACN,SAAS;wBACP,WAAW;oBACb;oBACA,MAAM;gBACR;YACF;YACA,SAAS;gBACP,WAAW;YACb;YACA,MAAM;QACR;QAEA,6BAA6B;QAC7B,MAAM,yBAAyB,cAAc,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC1D,IAAI,OAAO,EAAE;gBACb,WAAW,OAAO,SAAS;gBAC3B,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,OAAO;gBACvB,WAAW,OAAO,MAAM,CAAC,EAAE,GACvB,OAAO,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,kBAAkB,CAAC,WAC9C;gBACJ,QAAQ,OAAO,MAAM,CAAC,MAAM,GAAG,IAAI,UAAU;YAC/C,CAAC;QAED,oBAAoB;QACpB,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL,MAAM;oBACJ,cAAc,KAAK,EAAE;gBACvB;YACF;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;YACT;YACA,SAAS;gBACP,WAAW;YACb;YACA,MAAM;QACR;QAEA,4BAA4B;QAC5B,MAAM,wBAAwB,aAAa,GAAG,CAAC,CAAA;YAC7C,MAAM,QAAQ,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;gBACrC,OAAO,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS;YAC9C,GAAG;YAEH,OAAO;gBACL,IAAI,MAAM,EAAE;gBACZ,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE;gBAC5D,SAAS,MAAM,IAAI,CAAC,OAAO;gBAC3B,OAAO;gBACP,QAAQ,MAAM,MAAM;gBACpB,WAAW,MAAM,SAAS,CAAC,kBAAkB,CAAC;YAChD;QACF;QAEA,oEAAoE;QACpE,MAAM,mBAAmB;QAEzB,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA,eAAe;YACf,cAAc;QAChB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgE,GACzE;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}