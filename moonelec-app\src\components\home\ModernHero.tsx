'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { FaArrowRight, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

interface HeroSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage: string;
  backgroundGradient: string;
}

const heroSlides: HeroSlide[] = [
  {
    id: 1,
    title: "L'Électricité Qui Définit Votre Espace",
    subtitle: "Solutions Électriques Modernes",
    description: "Votre confort et votre design esthétique sont notre priorité. Découvrez notre gamme complète de produits électriques de haute qualité.",
    ctaText: "DÉCOUVRIR MAINTENANT",
    ctaLink: "/products",
    backgroundImage: "/images/hero/placeholder.svg",
    backgroundGradient: "from-charcoal/80 via-charcoal/60 to-transparent"
  },
  {
    id: 2,
    title: "Éclairage LED Intelligent",
    subtitle: "Technologie d'Avenir",
    description: "Transformez votre environnement avec nos solutions d'éclairage LED intelligentes et économes en énergie.",
    ctaText: "EXPLORER LED",
    ctaLink: "/products?category=eclairage",
    backgroundImage: "/images/hero/placeholder.svg",
    backgroundGradient: "from-electric-blue/80 via-electric-blue/60 to-transparent"
  },
  {
    id: 3,
    title: "Solutions Industrielles",
    subtitle: "Performance & Fiabilité",
    description: "Des solutions électriques robustes pour vos projets industriels les plus exigeants.",
    ctaText: "VOIR SOLUTIONS",
    ctaLink: "/solutions/industrial",
    backgroundImage: "/images/hero/placeholder.svg",
    backgroundGradient: "from-moonelec-red/80 via-moonelec-red/60 to-transparent"
  },
  {
    id: 4,
    title: "Maison Connectée",
    subtitle: "Domotique & Confort",
    description: "Créez votre maison intelligente avec nos systèmes de domotique avancés et intuitifs.",
    ctaText: "DÉCOUVRIR SMART HOME",
    ctaLink: "/solutions/smart-home",
    backgroundImage: "/images/hero/placeholder.svg",
    backgroundGradient: "from-charcoal/80 via-charcoal/60 to-transparent"
  }
];

export default function ModernHero() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const currentSlideData = heroSlides[currentSlide];

  return (
    <section 
      className="relative h-screen min-h-[600px] overflow-hidden"
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* Background Slides */}
      <div className="absolute inset-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
            className="absolute inset-0"
          >
            {/* Background Image */}
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: `url(${currentSlideData.backgroundImage})`,
              }}
            />
            
            {/* Gradient Overlay */}
            <div className={`absolute inset-0 bg-gradient-to-r ${currentSlideData.backgroundGradient}`} />
            
            {/* Pattern Overlay */}
            <div className="absolute inset-0 bg-grid-white/[0.05]" />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                {/* Subtitle */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="mb-4"
                >
                  <span className="inline-block px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white text-sm font-medium border border-white/20">
                    {currentSlideData.subtitle}
                  </span>
                </motion.div>

                {/* Main Title */}
                <motion.h1
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight"
                >
                  {currentSlideData.title.split(' ').map((word, index) => (
                    <motion.span
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className="inline-block mr-3"
                    >
                      {word}
                    </motion.span>
                  ))}
                </motion.h1>

                {/* Description */}
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-xl text-white/90 mb-8 max-w-2xl leading-relaxed"
                >
                  {currentSlideData.description}
                </motion.p>

                {/* CTA Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <Link
                    href={currentSlideData.ctaLink}
                    className="group inline-flex items-center space-x-3 bg-moonelec-red hover:bg-moonelec-red-dark text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                  >
                    <span className="text-lg">{currentSlideData.ctaText}</span>
                    <FaArrowRight className="text-lg group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex items-center space-x-4">
          {/* Previous Button */}
          <button
            onClick={prevSlide}
            className="p-3 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white rounded-full transition-all duration-300 hover:scale-110"
          >
            <FaChevronLeft />
          </button>

          {/* Slide Indicators */}
          <div className="flex space-x-2">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/70'
                }`}
              />
            ))}
          </div>

          {/* Next Button */}
          <button
            onClick={nextSlide}
            className="p-3 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white rounded-full transition-all duration-300 hover:scale-110"
          >
            <FaChevronRight />
          </button>
        </div>
      </div>

      {/* Slide Progress Bar */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
        <motion.div
          key={currentSlide}
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
          transition={{ duration: 5, ease: "linear" }}
          className="h-full bg-moonelec-red"
        />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-1/4 right-1/4 w-20 h-20 bg-electric-blue/20 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-1/3 right-1/5 w-32 h-32 bg-moonelec-red/20 rounded-full blur-2xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-1/6 w-16 h-16 bg-white/10 rounded-full blur-lg animate-pulse delay-500" />
    </section>
  );
}
