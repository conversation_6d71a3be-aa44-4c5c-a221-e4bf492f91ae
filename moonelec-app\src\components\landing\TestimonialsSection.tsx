'use client';

import { useRef, useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { FaQuoteLeft, FaChevronLeft, FaChevronRight, FaStar } from 'react-icons/fa';

// Sample testimonials
const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Électricien Professionnel',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    content: 'Moonelec est mon fournisseur de confiance depuis plus de 10 ans. La qualité des produits et le service client sont exceptionnels. Je recommande vivement leurs services à tous les professionnels du secteur.',
    rating: 5
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Architecte d\'Intérieur',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    content: 'J\'ai découvert Moonelec lors d\'un projet d\'éclairage complexe. Leur équipe m\'a guidée vers les solutions parfaites pour mes clients. Leur expertise en matière de design d\'éclairage est inégalée.',
    rating: 5
  },
  {
    id: 3,
    name: '<PERSON> Moreau',
    role: 'Directeur de Projet, BTP',
    image: 'https://randomuser.me/api/portraits/men/62.jpg',
    content: 'Pour nos grands projets industriels, Moonelec a toujours été capable de fournir le matériel nécessaire dans les délais, même pour les commandes les plus importantes. Un partenaire fiable et réactif.',
    rating: 4
  },
  {
    id: 4,
    name: 'Sophie Petit',
    role: 'Propriétaire de Maison Intelligente',
    image: 'https://randomuser.me/api/portraits/women/28.jpg',
    content: 'Grâce aux conseils des experts de Moonelec, j\'ai pu transformer ma maison en un espace intelligent et économe en énergie. Leur connaissance des dernières technologies est impressionnante.',
    rating: 5
  }
];

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [autoplay, setAutoplay] = useState(true);

  useEffect(() => {
    if (!autoplay) return;
    
    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [autoplay]);

  const handlePrev = () => {
    setAutoplay(false);
    setDirection(-1);
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setAutoplay(false);
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0
    })
  };

  return (
    <section id="testimonials" className="section" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ce Que Disent <span className="text-primary">Nos Clients</span>
          </h2>
          <div className="w-20 h-1 bg-secondary mx-auto mb-6"></div>
          <p className="text-text-secondary max-w-3xl mx-auto">
            Découvrez les témoignages de nos clients satisfaits qui nous font confiance pour leurs projets électriques.
          </p>
        </motion.div>

        <div className="relative max-w-4xl mx-auto">
          <div className="relative h-[400px] overflow-hidden rounded-lg bg-white dark:bg-[#1a1a1a] shadow-xl">
            <AnimatePresence initial={false} custom={direction}>
              <motion.div
                key={currentIndex}
                custom={direction}
                variants={variants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="absolute inset-0 p-8 md:p-12 flex flex-col items-center"
              >
                <FaQuoteLeft className="text-4xl text-primary/20 mb-6" />
                
                <p className="text-lg md:text-xl text-text-primary text-center mb-8 italic">
                  "{testimonials[currentIndex].content}"
                </p>
                
                <div className="flex items-center mt-auto">
                  <div className="relative w-16 h-16 rounded-full overflow-hidden mr-4">
                    <Image
                      src={testimonials[currentIndex].image}
                      alt={testimonials[currentIndex].name}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                  <div className="text-left">
                    <h4 className="font-semibold text-text-primary">
                      {testimonials[currentIndex].name}
                    </h4>
                    <p className="text-sm text-text-secondary">
                      {testimonials[currentIndex].role}
                    </p>
                    <div className="flex mt-1">
                      {[...Array(5)].map((_, i) => (
                        <FaStar 
                          key={i} 
                          className={`text-sm ${
                            i < testimonials[currentIndex].rating 
                              ? 'text-yellow-500' 
                              : 'text-gray-300'
                          }`} 
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Buttons */}
          <button
            onClick={handlePrev}
            className="absolute top-1/2 left-0 -translate-y-1/2 -translate-x-1/2 w-12 h-12 rounded-full bg-white dark:bg-[#1a1a1a] shadow-lg flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors z-10"
            aria-label="Témoignage précédent"
          >
            <FaChevronLeft />
          </button>
          
          <button
            onClick={handleNext}
            className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-1/2 w-12 h-12 rounded-full bg-white dark:bg-[#1a1a1a] shadow-lg flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors z-10"
            aria-label="Témoignage suivant"
          >
            <FaChevronRight />
          </button>

          {/* Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setAutoplay(false);
                  setDirection(index > currentIndex ? 1 : -1);
                  setCurrentIndex(index);
                }}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentIndex 
                    ? 'bg-primary' 
                    : 'bg-gray-300 dark:bg-gray-700'
                }`}
                aria-label={`Aller au témoignage ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
