import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, Dimensions, Image } from 'react-native';

const { width, height } = Dimensions.get('window');

interface WebStyleLoadingAnimationProps {
  size?: number;
}

export const WebStyleLoadingAnimation: React.FC<WebStyleLoadingAnimationProps> = ({ 
  size = 120 
}) => {
  // Animations pour reproduire l'effet de l'app web
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const shadowAnimation = useRef(new Animated.Value(0)).current;
  const pathAnimation = useRef(new Animated.Value(0)).current;
  const glowAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation séquentielle comme dans l'app web
    Animated.sequence([
      // 1. Apparition du logo
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
      
      // 2. Animation du path (simulation)
      Animated.timing(pathAnimation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();

    // Animation continue de l'ombre et du glow
    const shadowLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(shadowAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shadowAnimation, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    const glowLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnimation, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnimation, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    );

    shadowLoop.start();
    glowLoop.start();

    return () => {
      shadowLoop.stop();
      glowLoop.stop();
    };
  }, []);

  // Interpolations pour les animations
  const shadowTransform = shadowAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: ['-10px', '4px', '-10px'],
  });

  const glowOpacity = glowAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 0.8, 0.3],
  });

  const pathProgress = pathAnimation.interpolate({
    inputRange: [0, 0.33, 0.66, 1],
    outputRange: [300, 225, 182, 99],
  });

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {/* Effet de glow en arrière-plan */}
      <Animated.View
        style={[
          styles.glowBackground,
          {
            width: size * 1.5,
            height: size * 1.5,
            borderRadius: size * 0.75,
            opacity: glowOpacity,
          },
        ]}
      />

      {/* Logo principal avec animations */}
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: logoOpacity,
            transform: [
              { scale: logoScale },
              { 
                translateX: shadowAnimation.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [-2, 1, -2],
                })
              },
            ],
          },
        ]}
      >
        {/* Logo Moonelec */}
        <Image
          source={require('../../assets/logo-moonelec.png')}
          style={[styles.logo, { width: size * 0.8, height: size * 0.8 }]}
          resizeMode="contain"
        />

        {/* Overlay avec effet électrique */}
        <View style={[styles.electricOverlay, { width: size, height: size }]}>
          {/* Points électriques animés */}
          <Animated.View
            style={[
              styles.electricDot,
              {
                top: '20%',
                right: '15%',
                opacity: glowAnimation,
                transform: [{ scale: glowAnimation }],
              },
            ]}
          />
          <Animated.View
            style={[
              styles.electricDot,
              {
                bottom: '25%',
                left: '20%',
                opacity: shadowAnimation,
                transform: [{ scale: shadowAnimation }],
              },
            ]}
          />
          <Animated.View
            style={[
              styles.electricDot,
              {
                top: '50%',
                right: '10%',
                opacity: glowAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 0.3],
                }),
              },
            ]}
          />
        </View>
      </Animated.View>

      {/* Cercle de progression (simulation du path SVG) */}
      <Animated.View
        style={[
          styles.progressRing,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            opacity: logoOpacity,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              transform: [
                {
                  rotate: pathAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg'],
                  }),
                },
              ],
            },
          ]}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  glowBackground: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 109, 183, 0.1)',
    shadowColor: '#006db7',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
  },
  logoContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.7,
    shadowRadius: 10,
    elevation: 15,
  },
  logo: {
    zIndex: 2,
  },
  electricOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 3,
  },
  electricDot: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#ed1c24',
    shadowColor: '#ed1c24',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 5,
  },
  progressRing: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: 'transparent',
    borderTopColor: '#006db7',
    borderRightColor: '#006db7',
  },
  progressFill: {
    borderWidth: 1,
    borderColor: 'transparent',
    borderTopColor: 'rgba(0, 109, 183, 0.3)',
  },
});

export default WebStyleLoadingAnimation;
