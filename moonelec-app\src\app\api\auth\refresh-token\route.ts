import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';

// POST /api/auth/refresh-token - Generate a new token with the correct secret
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { username, password } = body;

    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }

    console.log('🔄 Refresh token request for:', username);

    // Find user by username or email
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: username }
        ]
      },
      include: {
        client: true,
        admin: true,
      }
    });

    if (!user) {
      console.log('❌ User not found:', username);
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Simple password check (in production, use proper hashing)
    if (user.password !== password) {
      console.log('❌ Invalid password for user:', username);
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      console.log('❌ User account is inactive:', username);
      return NextResponse.json(
        { error: 'Account is inactive' },
        { status: 401 }
      );
    }

    // Generate new JWT token using JWT_SECRET
    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
    console.log('🔄 Generating new token with JWT_SECRET');
    
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
      },
      jwtSecret,
      { expiresIn: '7d' } // Token valid for 7 days
    );

    // Prepare user data for response
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstname: user.firstname,
      lastname: user.lastname,
      role: user.role,
      clientId: user.client?.id,
      adminId: user.admin?.id,
    };

    console.log('✅ New token generated successfully for:', username);

    return NextResponse.json({
      success: true,
      message: 'Token refreshed successfully',
      token,
      user: userData,
    });

  } catch (error) {
    console.error('❌ Refresh token error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
