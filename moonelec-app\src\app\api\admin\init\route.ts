import { NextRequest, NextResponse } from 'next/server';
import { createAdminUser, findUserByUsername } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    // Vérifier si la requête contient un token de sécurité
    const { token } = await req.json();

    // Vérifier le token (simple pour cet exemple, à améliorer en production)
    if (token !== process.env.ADMIN_INIT_TOKEN && token !== 'moonelec-admin-init-token') {
      return NextResponse.json(
        { error: 'Token invalide' },
        { status: 401 }
      );
    }

    // Vérifier si l'administrateur existe déjà
    const existingAdmin = await findUserByUsername('hicham.ezzamzami');

    if (existingAdmin) {
      return NextResponse.json(
        { message: 'L\'administrateur existe déjà' },
        { status: 200 }
      );
    }

    // Créer l'administrateur par défaut
    await createAdminUser({
      email: '<EMAIL>',
      username: 'hicham.ezzamzami',
      password: '123456',
      lastname: 'E<PERSON>mzami',
      firstname: 'Hicham',
      telephone: '+212600000000',
    });

    return NextResponse.json(
      { message: 'Administrateur créé avec succès' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Erreur lors de la création de l\'administrateur:', error);
    return NextResponse.json(
      { error: 'Une erreur est survenue lors de la création de l\'administrateur' },
      { status: 500 }
    );
  }
}
