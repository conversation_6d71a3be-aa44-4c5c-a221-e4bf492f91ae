'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  FaSave, 
  FaUpload, 
  FaImage, 
  FaVideo, 
  FaFileAudio, 
  FaFilePdf, 
  FaSpinner, 
  FaTimes,
  FaCheck
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Image from 'next/image';

export default function NewReportPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [formData, setFormData] = useState({
    need: '',
    articleRef: '',
    comment: '',
    visitDate: new Date().toISOString().split('T')[0],
    denomination: '',
    name: '',
    visitPurpose: '',
    complaint: '',
    city: '',
  });
  
  // File uploads
  const [images, setImages] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  
  // Refs for file inputs
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  const audioInputRef = useRef<HTMLInputElement>(null);
  const pdfInputRef = useRef<HTMLInputElement>(null);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files);
      setImages(prev => [...prev, ...newImages]);
      
      // Create preview URLs
      const newPreviewUrls = newImages.map(file => URL.createObjectURL(file));
      setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);
    }
  };
  
  // Handle video upload
  const handleVideoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setVideoFile(e.target.files[0]);
    }
  };
  
  // Handle audio upload
  const handleAudioUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setAudioFile(e.target.files[0]);
    }
  };
  
  // Handle PDF upload
  const handlePdfUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setPdfFile(e.target.files[0]);
    }
  };
  
  // Remove image
  const removeImage = (index: number) => {
    const newImages = [...images];
    const newPreviewUrls = [...imagePreviewUrls];
    
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(newPreviewUrls[index]);
    
    newImages.splice(index, 1);
    newPreviewUrls.splice(index, 1);
    
    setImages(newImages);
    setImagePreviewUrls(newPreviewUrls);
  };
  
  // Remove video
  const removeVideo = () => {
    setVideoFile(null);
  };
  
  // Remove audio
  const removeAudio = () => {
    setAudioFile(null);
  };
  
  // Remove PDF
  const removePdf = () => {
    setPdfFile(null);
  };
  
  // Upload files
  const uploadFiles = async () => {
    if (images.length === 0 && !videoFile && !audioFile && !pdfFile) {
      return { imageUrls: [], videoUrl: '', audioUrl: '', pdfUrl: '' };
    }
    
    const formData = new FormData();
    
    // Add images
    images.forEach((image, index) => {
      formData.append(`image${index}`, image);
    });
    
    // Add video
    if (videoFile) {
      formData.append('video', videoFile);
    }
    
    // Add audio
    if (audioFile) {
      formData.append('audio', audioFile);
    }
    
    // Add PDF
    if (pdfFile) {
      formData.append('pdf', pdfFile);
    }
    
    try {
      const response = await fetch('/api/sales-reports/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload files');
      }
      
      const data = await response.json();
      
      return {
        imageUrls: data.imageUrls || [],
        videoUrl: data.videoUrl || '',
        audioUrl: data.audioUrl || '',
        pdfUrl: data.pdfUrl || '',
      };
    } catch (error) {
      console.error('Error uploading files:', error);
      throw error;
    }
  };
  
  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || user.role !== 'COMMERCIAL') {
      alert('You must be logged in as a sales team member to submit a report');
      return;
    }
    
    setIsSubmitting(true);
    setUploadProgress(10);
    
    try {
      // Upload files first
      const { imageUrls, videoUrl, audioUrl, pdfUrl } = await uploadFiles();
      setUploadProgress(50);
      
      // Save URLs
      setImageUrls(imageUrls);
      setVideoUrl(videoUrl);
      setAudioUrl(audioUrl);
      setPdfUrl(pdfUrl);
      
      // Create form data for the report
      const reportFormData = new FormData();
      
      // Add form fields
      Object.entries(formData).forEach(([key, value]) => {
        reportFormData.append(key, value);
      });
      
      // Add file URLs
      imageUrls.forEach(url => {
        reportFormData.append('imageUrls', url);
      });
      
      if (videoUrl) {
        reportFormData.append('videoUrl', videoUrl);
      }
      
      if (audioUrl) {
        reportFormData.append('audioUrl', audioUrl);
      }
      
      if (pdfUrl) {
        reportFormData.append('pdfUrl', pdfUrl);
      }
      
      setUploadProgress(75);
      
      // Submit the report
      const response = await fetch('/api/sales-reports', {
        method: 'POST',
        body: reportFormData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to submit report');
      }
      
      setUploadProgress(100);
      
      // Redirect to reports list
      router.push('/commercial/reports');
    } catch (error) {
      console.error('Error submitting report:', error);
      alert('Failed to submit report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <RouteGuard allowedRoles={['COMMERCIAL']}>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">
          Submit Daily Report
        </h1>
        
        <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          {/* Progress bar for submission */}
          {isSubmitting && (
            <div className="mb-6">
              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                <div 
                  className="bg-primary h-2.5 rounded-full transition-all duration-300" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 text-center">
                {uploadProgress < 100 ? 'Uploading...' : 'Completed!'}
              </p>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Visit Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Visit Date *
              </label>
              <input
                type="date"
                name="visitDate"
                value={formData.visitDate}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            {/* City */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City *
              </label>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                required
                placeholder="Enter city name"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            {/* Denomination */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Denomination *
              </label>
              <input
                type="text"
                name="denomination"
                value={formData.denomination}
                onChange={handleInputChange}
                required
                placeholder="Enter denomination"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="Enter name"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            {/* Article Reference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Article Reference
              </label>
              <input
                type="text"
                name="articleRef"
                value={formData.articleRef}
                onChange={handleInputChange}
                placeholder="Enter article reference (optional)"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          
          {/* Need */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Need *
            </label>
            <textarea
              name="need"
              value={formData.need}
              onChange={handleInputChange}
              required
              rows={4}
              placeholder="Describe the need"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
            ></textarea>
          </div>
          
          {/* Purpose of Visit */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Purpose of Visit *
            </label>
            <textarea
              name="visitPurpose"
              value={formData.visitPurpose}
              onChange={handleInputChange}
              required
              rows={4}
              placeholder="Describe the purpose of the visit"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
            ></textarea>
          </div>
          
          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Comment
            </label>
            <textarea
              name="comment"
              value={formData.comment}
              onChange={handleInputChange}
              rows={3}
              placeholder="Add any additional comments (optional)"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
            ></textarea>
          </div>
          
          {/* Complaint */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Complaint
            </label>
            <textarea
              name="complaint"
              value={formData.complaint}
              onChange={handleInputChange}
              rows={3}
              placeholder="Describe any complaints (optional)"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
            ></textarea>
          </div>
          
          {/* File Uploads */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Attachments</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Images
                </label>
                <div className="flex items-center">
                  <input
                    type="file"
                    ref={imageInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    multiple
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={() => imageInputRef.current?.click()}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    <FaImage />
                    <span>Add Images</span>
                  </button>
                </div>
                
                {/* Image Previews */}
                {imagePreviewUrls.length > 0 && (
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {imagePreviewUrls.map((url, index) => (
                      <div key={index} className="relative group">
                        <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                          <Image
                            src={url}
                            alt={`Image ${index + 1}`}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="w-full h-full"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <FaTimes size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Video */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Video
                </label>
                <div className="flex items-center">
                  <input
                    type="file"
                    ref={videoInputRef}
                    onChange={handleVideoUpload}
                    accept="video/*"
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={() => videoInputRef.current?.click()}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    <FaVideo />
                    <span>Add Video</span>
                  </button>
                </div>
                
                {/* Video Preview */}
                {videoFile && (
                  <div className="flex items-center justify-between mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
                    <span className="text-sm text-gray-800 dark:text-white truncate">
                      {videoFile.name}
                    </span>
                    <button
                      type="button"
                      onClick={removeVideo}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                )}
              </div>
              
              {/* Audio */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Audio
                </label>
                <div className="flex items-center">
                  <input
                    type="file"
                    ref={audioInputRef}
                    onChange={handleAudioUpload}
                    accept="audio/*"
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={() => audioInputRef.current?.click()}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    <FaFileAudio />
                    <span>Add Audio</span>
                  </button>
                </div>
                
                {/* Audio Preview */}
                {audioFile && (
                  <div className="flex items-center justify-between mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
                    <span className="text-sm text-gray-800 dark:text-white truncate">
                      {audioFile.name}
                    </span>
                    <button
                      type="button"
                      onClick={removeAudio}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                )}
              </div>
              
              {/* PDF */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  PDF Document
                </label>
                <div className="flex items-center">
                  <input
                    type="file"
                    ref={pdfInputRef}
                    onChange={handlePdfUpload}
                    accept=".pdf"
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={() => pdfInputRef.current?.click()}
                    className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    <FaFilePdf />
                    <span>Add PDF</span>
                  </button>
                </div>
                
                {/* PDF Preview */}
                {pdfFile && (
                  <div className="flex items-center justify-between mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
                    <span className="text-sm text-gray-800 dark:text-white truncate">
                      {pdfFile.name}
                    </span>
                    <button
                      type="button"
                      onClick={removePdf}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Submit Button */}
          <div className="flex justify-end">
            <motion.button
              type="submit"
              disabled={isSubmitting}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center justify-center gap-2 px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <FaSpinner className="animate-spin" />
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <FaSave />
                  <span>Submit Report</span>
                </>
              )}
            </motion.button>
          </div>
        </form>
      </div>
    </RouteGuard>
  );
}
