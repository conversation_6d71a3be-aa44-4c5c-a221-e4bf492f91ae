// Test script pour vérifier l'authentification
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAuth() {
  try {
    console.log('🔍 Testing authentication...');
    
    // Test 1: Essayer l'endpoint mobile (qui n'existe pas encore)
    try {
      const mobileResponse = await axios.post(`${BASE_URL}/auth/mobile/login`, {
        username: 'admin',
        password: 'admin123'
      });
      console.log('✅ Mobile endpoint works:', mobileResponse.data);
    } catch (error) {
      console.log('❌ Mobile endpoint not found (expected):', error.response?.status);
    }
    
    // Test 2: Essayer NextAuth CSRF
    try {
      const csrfResponse = await axios.get(`${BASE_URL}/auth/csrf`);
      console.log('✅ CSRF token:', csrfResponse.data.csrfToken);
      
      // Test 3: Essayer l'authentification NextAuth
      const authResponse = await axios.post(`${BASE_URL}/auth/callback/credentials`, {
        username: 'admin',
        password: 'admin123',
        csrfToken: csrfResponse.data.csrfToken,
        callbackUrl: '/',
        json: true
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        }
      });
      
      console.log('📱 NextAuth response status:', authResponse.status);
      console.log('📱 NextAuth response headers:', authResponse.headers);
      
    } catch (error) {
      console.log('❌ NextAuth error:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAuth();
