import bcrypt from 'bcryptjs';
import { prisma } from './prisma';
import { UserRole } from '@prisma/client';

// Fonction pour hacher un mot de passe
export async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

// Fonction pour vérifier un mot de passe
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Fonction pour créer un utilisateur client
export async function createClientUser({
  email,
  username,
  password,
  lastname,
  firstname,
  telephone,
  company_name,
}: {
  email: string;
  username: string;
  password: string;
  lastname: string;
  firstname: string;
  telephone?: string;
  company_name?: string;
}) {
  const hashedPassword = await hashPassword(password);

  return prisma.$transaction(async (tx) => {
    // Créer l'utilisateur de base
    const user = await tx.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        lastname,
        firstname,
        telephone,
        role: UserRole.CLIENT,
      },
    });

    // C<PERSON>er le profil client associé
    const client = await tx.client.create({
      data: {
        userId: user.id,
        company_name,
      },
    });

    return { user, client };
  });
}

// Fonction pour créer un utilisateur commercial
export async function createCommercialUser({
  email,
  username,
  password,
  lastname,
  firstname,
  telephone,
  profile_photo,
}: {
  email: string;
  username: string;
  password: string;
  lastname: string;
  firstname: string;
  telephone?: string;
  profile_photo?: string;
}) {
  const hashedPassword = await hashPassword(password);

  return prisma.$transaction(async (tx) => {
    // Créer l'utilisateur de base
    const user = await tx.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        lastname,
        firstname,
        telephone,
        role: UserRole.COMMERCIAL,
      },
    });

    // Créer le profil commercial associé
    const commercial = await tx.commercial.create({
      data: {
        userId: user.id,
        profile_photo,
      },
    });

    return { user, commercial };
  });
}

// Fonction pour créer un utilisateur administrateur
export async function createAdminUser({
  email,
  username,
  password,
  lastname,
  firstname,
  telephone,
}: {
  email: string;
  username: string;
  password: string;
  lastname: string;
  firstname: string;
  telephone?: string;
}) {
  const hashedPassword = await hashPassword(password);

  return prisma.$transaction(async (tx) => {
    // Créer l'utilisateur de base
    const user = await tx.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        lastname,
        firstname,
        telephone,
        role: UserRole.ADMIN,
      },
    });

    // Créer le profil admin associé
    const admin = await tx.admin.create({
      data: {
        userId: user.id,
      },
    });

    return { user, admin };
  });
}

// Fonction pour trouver un utilisateur par son nom d'utilisateur
export async function findUserByUsername(username: string) {
  return prisma.user.findUnique({
    where: { username },
    include: {
      client: true,
      commercial: true,
      admin: true,
    },
  });
}

// Fonction pour trouver un utilisateur par son email
export async function findUserByEmail(email: string) {
  return prisma.user.findUnique({
    where: { email },
    include: {
      client: true,
      commercial: true,
      admin: true,
    },
  });
}
