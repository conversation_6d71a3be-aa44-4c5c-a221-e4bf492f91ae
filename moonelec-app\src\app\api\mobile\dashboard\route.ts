import { NextRequest, NextResponse } from 'next/server';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { RealTimeDataService, DataCache } from '@/lib/realtime-data';
import { checkIPSecurity, createSecureError } from '@/middleware/security';
import { prisma } from '@/lib/prisma';

// GET /api/mobile/dashboard - Mobile-optimized dashboard data
export async function GET(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    console.log('📱 Mobile dashboard request from user:', { id: user.id, role: user.role });

    // Get query parameters for customization
    const url = new URL(request.url);
    const forceRefresh = url.searchParams.get('refresh') === 'true';
    const dataType = url.searchParams.get('type') || 'full';

    // Check cache first for performance
    const cacheKey = `mobile-dashboard-${user.role}-${dataType}`;
    let dashboardData = forceRefresh ? null : DataCache.get(cacheKey);

    if (!dashboardData) {
      console.log('📱 Fetching fresh dashboard data for mobile');
      
      // Get role-specific dashboard data
      if (user.role === 'ADMIN') {
        dashboardData = await getAdminDashboardData();
      } else if (user.role === 'COMMERCIAL') {
        dashboardData = await getCommercialDashboardData(user.id);
      } else if (user.role === 'CLIENT') {
        dashboardData = await getClientDashboardData(user.id);
      } else {
        return NextResponse.json({ error: 'Invalid user role' }, { status: 403 });
      }

      // Cache for 1 minute for mobile (shorter cache for real-time feel)
      DataCache.set(cacheKey, dashboardData, 1);
    }

    // Add mobile-specific metadata
    const response = {
      ...dashboardData,
      mobile: {
        optimized: true,
        cacheStatus: forceRefresh ? 'fresh' : 'cached',
        lastUpdated: new Date().toISOString(),
        userRole: user.role,
        userId: user.id,
      },
      performance: {
        responseTime: Date.now(),
        dataSource: 'real-time',
      }
    };

    console.log('📱 Mobile dashboard response prepared for:', user.role);
    return NextResponse.json(response);

  } catch (error) {
    console.error('Mobile dashboard error:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch dashboard data',
      mobile: {
        optimized: true,
        error: true,
        timestamp: new Date().toISOString(),
      }
    }, { status: 500 });
  }
}

// Admin dashboard data
async function getAdminDashboardData() {
  const [
    totalUsers,
    totalProducts,
    totalCategories,
    totalBrands,
    totalQuotes,
    recentActivity,
    revenueData
  ] = await Promise.all([
    prisma.user.count(),
    prisma.product.count(),
    prisma.category.count(),
    prisma.brand.count(),
    prisma.quote.count(),
    getRecentActivity(),
    RealTimeDataService.calculateRealRevenue('month')
  ]);

  return {
    overview: {
      totalUsers,
      totalProducts,
      totalCategories,
      totalBrands,
      totalQuotes,
      totalRevenue: Math.round(revenueData.totalRevenue),
    },
    revenue: {
      thisMonth: Math.round(revenueData.totalRevenue),
      quotesCount: revenueData.quotesCount,
      averageQuote: Math.round(revenueData.averageQuoteValue),
    },
    recentActivity,
    userBreakdown: await getUserBreakdown(),
  };
}

// Commercial dashboard data
async function getCommercialDashboardData(userId: string) {
  const [
    myQuotes,
    myClients,
    myRevenue,
    recentQuotes
  ] = await Promise.all([
    prisma.quote.count({ where: { createdById: userId } }),
    prisma.user.count({ where: { role: 'CLIENT', createdById: userId } }),
    calculateUserRevenue(userId),
    getRecentQuotes(userId, 5)
  ]);

  return {
    overview: {
      myQuotes,
      myClients,
      myRevenue: Math.round(myRevenue),
      conversionRate: myQuotes > 0 ? Math.round((myRevenue / myQuotes) * 100) / 100 : 0,
    },
    recentQuotes,
    performance: await getCommercialPerformance(userId),
  };
}

// Client dashboard data
async function getClientDashboardData(userId: string) {
  const [
    myQuotes,
    totalSpent,
    recentQuotes,
    favoriteProducts
  ] = await Promise.all([
    prisma.quote.count({ where: { clientId: userId } }),
    calculateClientSpending(userId),
    getRecentQuotes(userId, 5, 'client'),
    getFavoriteProducts(userId)
  ]);

  return {
    overview: {
      myQuotes,
      totalSpent: Math.round(totalSpent),
      averageQuote: myQuotes > 0 ? Math.round(totalSpent / myQuotes) : 0,
    },
    recentQuotes,
    favoriteProducts,
  };
}

// Helper functions
async function getRecentActivity() {
  const recentQuotes = await prisma.quote.findMany({
    take: 5,
    orderBy: { createdAt: 'desc' },
    include: {
      client: { select: { firstname: true, lastname: true } },
      createdBy: { select: { firstname: true, lastname: true } }
    }
  });

  return recentQuotes.map(quote => ({
    id: quote.id,
    type: 'quote',
    description: `Devis ${quote.reference}`,
    client: `${quote.client.firstname} ${quote.client.lastname}`,
    commercial: `${quote.createdBy.firstname} ${quote.createdBy.lastname}`,
    date: quote.createdAt,
    status: quote.status
  }));
}

async function getUserBreakdown() {
  const breakdown = await prisma.user.groupBy({
    by: ['role'],
    _count: { id: true }
  });

  return breakdown.reduce((acc, item) => {
    acc[item.role.toLowerCase()] = item._count.id;
    return acc;
  }, {} as Record<string, number>);
}

async function calculateUserRevenue(userId: string) {
  const quotes = await prisma.quote.findMany({
    where: { createdById: userId },
    include: { items: true }
  });

  return quotes.reduce((total, quote) => {
    const quoteTotal = quote.items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);
    return total + quoteTotal;
  }, 0);
}

async function calculateClientSpending(userId: string) {
  const quotes = await prisma.quote.findMany({
    where: { clientId: userId },
    include: { items: true }
  });

  return quotes.reduce((total, quote) => {
    const quoteTotal = quote.items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);
    return total + quoteTotal;
  }, 0);
}

async function getRecentQuotes(userId: string, limit: number, userType: 'commercial' | 'client' = 'commercial') {
  const whereClause = userType === 'commercial' 
    ? { createdById: userId }
    : { clientId: userId };

  return await prisma.quote.findMany({
    where: whereClause,
    take: limit,
    orderBy: { createdAt: 'desc' },
    include: {
      client: { select: { firstname: true, lastname: true } },
      items: { take: 3, include: { product: { select: { name: true } } } }
    }
  });
}

async function getCommercialPerformance(userId: string) {
  const thisMonth = new Date();
  thisMonth.setDate(1);
  
  const [thisMonthQuotes, lastMonthQuotes] = await Promise.all([
    prisma.quote.count({
      where: {
        createdById: userId,
        createdAt: { gte: thisMonth }
      }
    }),
    prisma.quote.count({
      where: {
        createdById: userId,
        createdAt: {
          gte: new Date(thisMonth.getFullYear(), thisMonth.getMonth() - 1, 1),
          lt: thisMonth
        }
      }
    })
  ]);

  const growth = lastMonthQuotes > 0 
    ? Math.round(((thisMonthQuotes - lastMonthQuotes) / lastMonthQuotes) * 100)
    : 0;

  return {
    thisMonth: thisMonthQuotes,
    lastMonth: lastMonthQuotes,
    growth
  };
}

async function getFavoriteProducts(userId: string) {
  const productCounts = await prisma.quoteItem.groupBy({
    by: ['productId'],
    where: {
      quote: { clientId: userId }
    },
    _count: { id: true },
    _sum: { quantity: true },
    orderBy: { _count: { id: 'desc' } },
    take: 5
  });

  const productIds = productCounts.map(p => p.productId).filter(Boolean);
  const products = await prisma.product.findMany({
    where: { id: { in: productIds as string[] } },
    select: { id: true, name: true, reference: true }
  });

  return productCounts.map(count => {
    const product = products.find(p => p.id === count.productId);
    return {
      product: product || { name: 'Unknown Product', reference: 'N/A' },
      orderCount: count._count.id,
      totalQuantity: count._sum.quantity || 0
    };
  });
}
