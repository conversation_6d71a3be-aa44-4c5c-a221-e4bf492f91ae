'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  reference: string;
  description: string;
  image: string | null;
  quantity: number;
  category?: {
    id: string;
    name: string;
  } | null;
  brand?: {
    id: string;
    name: string;
  } | null;
}

interface CartContextType {
  items: CartItem[];
  notes: string;
  addItem: (product: any, quantity: number) => void;
  updateItemQuantity: (itemId: string, quantity: number) => void;
  removeItem: (itemId: string) => void;
  clearCart: () => void;
  setNotes: (notes: string) => void;
  itemCount: number;
  isCartOpen: boolean;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [notes, setNotes] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Charger le panier depuis le localStorage au montage du composant
  useEffect(() => {
    const storedCart = localStorage.getItem('cart');
    const storedNotes = localStorage.getItem('cartNotes');

    if (storedCart) {
      try {
        setItems(JSON.parse(storedCart));
      } catch (error) {
        console.error('Error parsing cart from localStorage:', error);
        setItems([]);
      }
    }

    if (storedNotes) {
      setNotes(storedNotes);
    }

    setIsInitialized(true);
  }, []);

  // Sauvegarder le panier dans le localStorage à chaque modification
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem('cart', JSON.stringify(items));
      localStorage.setItem('cartNotes', notes);
    }
  }, [items, notes, isInitialized]);

  // Ajouter un produit au panier
  const addItem = (product: any, quantity: number) => {
    setItems(prevItems => {
      // Vérifier si le produit est déjà dans le panier
      const existingItemIndex = prevItems.findIndex(item => item.productId === product.id);

      if (existingItemIndex !== -1) {
        // Mettre à jour la quantité si le produit existe déjà
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += quantity;
        return updatedItems;
      } else {
        // Ajouter un nouveau produit au panier
        return [...prevItems, {
          id: `${product.id}_${Date.now()}`, // ID unique pour l'élément du panier
          productId: product.id,
          name: product.name,
          reference: product.reference,
          description: product.description,
          image: product.mainImage,
          quantity,
          category: product.category,
          brand: product.brand
        }];
      }
    });
  };

  // Mettre à jour la quantité d'un produit dans le panier
  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    setItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  // Supprimer un produit du panier
  const removeItem = (itemId: string) => {
    setItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  // Vider le panier
  const clearCart = () => {
    setItems([]);
    setNotes('');
  };

  // Nombre total d'articles dans le panier
  const itemCount = items.reduce((total, item) => total + item.quantity, 0);

  // Fonctions pour gérer l'ouverture et la fermeture du panneau coulissant
  const openCart = () => setIsCartOpen(true);
  const closeCart = () => setIsCartOpen(false);
  const toggleCart = () => setIsCartOpen(prev => !prev);

  // Modifier la fonction addItem pour ouvrir automatiquement le panneau
  const handleAddItem = (product: any, quantity: number) => {
    addItem(product, quantity);
    openCart(); // Ouvrir le panneau lorsqu'un produit est ajouté
  };

  return (
    <CartContext.Provider
      value={{
        items,
        notes,
        addItem: handleAddItem,
        updateItemQuantity,
        removeItem,
        clearCart,
        setNotes,
        itemCount,
        isCartOpen,
        openCart,
        closeCart,
        toggleCart
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
