'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaUsers, FaShoppingCart, FaBox, FaChartLine, FaUserTie, FaBuilding, FaTag, FaCopyright } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import DashboardCard from '@/components/admin/DashboardCard';
import DashboardChart from '@/components/admin/DashboardChart';
import RecentActivityCard from '@/components/admin/RecentActivityCard';
import CommercialPerformanceChart from '@/components/admin/CommercialPerformanceChart';
import ProductStatsChart from '@/components/admin/ProductStatsChart';
import ClientOrdersCard from '@/components/admin/ClientOrdersCard';

export default function AdminDashboard() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    totalClients: 0,
    totalOrders: 0,
    totalProducts: 0,
    totalRevenue: 0,
    totalCommercials: 0,
    totalSuppliers: 0,
    totalCategories: 0,
    totalBrands: 0
  });

  useEffect(() => {
    // Charger les données réelles depuis l'API
    const fetchDashboardData = async () => {
      try {
        const response = await fetch('/api/admin/dashboard');

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data');
        }

        const data = await response.json();
        setDashboardData(data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // En cas d'erreur, on garde les valeurs par défaut
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
      >
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          Bienvenue, {user?.firstname} {user?.lastname}
        </h1>
        <p className="text-gray-600 dark:text-gray-300 mt-1">
          Voici un aperçu de l'activité de Moonelec
        </p>
      </motion.div>

      {/* Dashboard Cards */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Clients"
            value={dashboardData.totalClients}
            icon={<FaUsers className="text-blue-500" />}
            change={+12}
            period="ce mois"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Commandes"
            value={dashboardData.totalOrders}
            icon={<FaShoppingCart className="text-green-500" />}
            change={+24}
            period="ce mois"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Produits"
            value={dashboardData.totalProducts}
            icon={<FaBox className="text-purple-500" />}
            change={+85}
            period="ce mois"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Chiffre d'affaires"
            value={dashboardData.totalRevenue}
            icon={<FaChartLine className="text-yellow-500" />}
            change={+8.5}
            period="ce mois"
            isCurrency
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Commerciaux"
            value={dashboardData.totalCommercials}
            icon={<FaUserTie className="text-red-500" />}
            change={+2}
            period="ce mois"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Fournisseurs"
            value={dashboardData.totalSuppliers}
            icon={<FaBuilding className="text-indigo-500" />}
            change={+3}
            period="ce mois"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Catégories"
            value={dashboardData.totalCategories}
            icon={<FaTag className="text-pink-500" />}
            change={+2}
            period="ce mois"
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <DashboardCard
            title="Marques"
            value={dashboardData.totalBrands}
            icon={<FaCopyright className="text-cyan-500" />}
            change={+4}
            period="ce mois"
          />
        </motion.div>
      </motion.div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="lg:col-span-2"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
              Évolution des ventes
            </h2>
            <DashboardChart />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <RecentActivityCard />
        </motion.div>
      </div>

      {/* Client Orders Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="mb-6"
      >
        <ClientOrdersCard />
      </motion.div>

      {/* Additional Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
              Performance des commerciaux
            </h2>
            <CommercialPerformanceChart />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
              Répartition des produits par catégorie
            </h2>
            <ProductStatsChart />
          </div>
        </motion.div>
      </div>
    </div>
  );
}
