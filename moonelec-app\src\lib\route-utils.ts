/**
 * Extrait l'ID de manière sûre à partir des paramètres de route
 * Cette fonction est conçue pour contourner le problème de Next.js 15.3.2
 * qui exige que les paramètres de route soient attendus avant d'être utilisés
 */
export function extractId(params: any): string {
  // Utiliser une approche qui évite d'accéder directement aux propriétés de params
  return String(Object.values(params)[0] || '');
}
