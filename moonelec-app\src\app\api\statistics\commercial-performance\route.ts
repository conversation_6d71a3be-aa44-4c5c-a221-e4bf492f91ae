import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

interface CommercialData {
  id: string;
  name: string;
  sales: number;
  clients: number;
  color: string;
}

export async function GET(request: NextRequest) {
  try {
    // Get all commercials with their performance data
    const commercials = await prisma.commercial.findMany({
      include: {
        user: {
          select: {
            firstname: true,
            lastname: true,
            email: true
          }
        },
        commercialclient: {
          include: {
            client: true
          }
        }
      }
    });

    const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];

    const commercialPerformance: CommercialData[] = await Promise.all(
      commercials.map(async (commercial, index) => {
        // Calculate sales from orders and quotes
        const [orderSales, quoteSales] = await Promise.all([
          // Get sales from orders where this commercial is involved
          prisma.order.aggregate({
            where: {
              client: {
                commercialclient: {
                  some: {
                    commercialId: commercial.id
                  }
                }
              },
              status: {
                in: ['DELIVERED', 'COMPLETED']
              }
            },
            _sum: {
              totalAmount: true
            }
          }),

          // Get sales from accepted quotes
          prisma.quote.aggregate({
            where: {
              createdByAdminId: commercial.id,
              status: 'ACCEPTED'
            },
            _sum: {
              totalAmount: true
            }
          })
        ]);

        const totalSales = (orderSales._sum.totalAmount || 0) + (quoteSales._sum.totalAmount || 0);
        const clientCount = commercial.commercialclient.length;

        return {
          id: commercial.id,
          name: `${commercial.user.firstname} ${commercial.user.lastname}`,
          sales: totalSales,
          clients: clientCount,
          color: colors[index % colors.length]
        };
      })
    );

    // Sort by sales descending
    commercialPerformance.sort((a, b) => b.sales - a.sales);

    return NextResponse.json({
      commercials: commercialPerformance,
      metadata: {
        totalCommercials: commercialPerformance.length,
        totalSales: commercialPerformance.reduce((sum, c) => sum + c.sales, 0),
        totalClients: commercialPerformance.reduce((sum, c) => sum + c.clients, 0),
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching commercial performance data:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des données de performance commerciale' },
      { status: 500 }
    );
  }
}
