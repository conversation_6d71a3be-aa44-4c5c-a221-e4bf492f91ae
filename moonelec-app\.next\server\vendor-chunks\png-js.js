/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/png-js";
exports.ids = ["vendor-chunks/png-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/png-js/png-node.js":
/*!*****************************************!*\
  !*** ./node_modules/png-js/png-node.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*\n * MIT LICENSE\n * Copyright (c) 2011 Devon Govett\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this\n * software and associated documentation files (the \"Software\"), to deal in the Software\n * without restriction, including without limitation the rights to use, copy, modify, merge,\n * publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons\n * to whom the Software is furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all copies or\n * substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING\n * BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n * DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nmodule.exports = class PNG {\n  static decode(path, fn) {\n    return fs.readFile(path, function(err, file) {\n      const png = new PNG(file);\n      return png.decode(pixels => fn(pixels));\n    });\n  }\n\n  static load(path) {\n    const file = fs.readFileSync(path);\n    return new PNG(file);\n  }\n\n  constructor(data) {\n    let i;\n    this.data = data;\n    this.pos = 8; // Skip the default header\n\n    this.palette = [];\n    this.imgData = [];\n    this.transparency = {};\n    this.text = {};\n\n    while (true) {\n      const chunkSize = this.readUInt32();\n      let section = '';\n      for (i = 0; i < 4; i++) {\n        section += String.fromCharCode(this.data[this.pos++]);\n      }\n\n      switch (section) {\n        case 'IHDR':\n          // we can grab  interesting values from here (like width, height, etc)\n          this.width = this.readUInt32();\n          this.height = this.readUInt32();\n          this.bits = this.data[this.pos++];\n          this.colorType = this.data[this.pos++];\n          this.compressionMethod = this.data[this.pos++];\n          this.filterMethod = this.data[this.pos++];\n          this.interlaceMethod = this.data[this.pos++];\n          break;\n\n        case 'PLTE':\n          this.palette = this.read(chunkSize);\n          break;\n\n        case 'IDAT':\n          for (i = 0; i < chunkSize; i++) {\n            this.imgData.push(this.data[this.pos++]);\n          }\n          break;\n\n        case 'tRNS':\n          // This chunk can only occur once and it must occur after the\n          // PLTE chunk and before the IDAT chunk.\n          this.transparency = {};\n          switch (this.colorType) {\n            case 3:\n              // Indexed color, RGB. Each byte in this chunk is an alpha for\n              // the palette index in the PLTE (\"palette\") chunk up until the\n              // last non-opaque entry. Set up an array, stretching over all\n              // palette entries which will be 0 (opaque) or 1 (transparent).\n              this.transparency.indexed = this.read(chunkSize);\n              var short = 255 - this.transparency.indexed.length;\n              if (short > 0) {\n                for (i = 0; i < short; i++) {\n                  this.transparency.indexed.push(255);\n                }\n              }\n              break;\n            case 0:\n              // Greyscale. Corresponding to entries in the PLTE chunk.\n              // Grey is two bytes, range 0 .. (2 ^ bit-depth) - 1\n              this.transparency.grayscale = this.read(chunkSize)[0];\n              break;\n            case 2:\n              // True color with proper alpha channel.\n              this.transparency.rgb = this.read(chunkSize);\n              break;\n          }\n          break;\n\n        case 'tEXt':\n          var text = this.read(chunkSize);\n          var index = text.indexOf(0);\n          var key = String.fromCharCode.apply(String, text.slice(0, index));\n          this.text[key] = String.fromCharCode.apply(\n            String,\n            text.slice(index + 1)\n          );\n          break;\n\n        case 'IEND':\n          // we've got everything we need!\n          switch (this.colorType) {\n            case 0:\n            case 3:\n            case 4:\n              this.colors = 1;\n              break;\n            case 2:\n            case 6:\n              this.colors = 3;\n              break;\n          }\n\n          this.hasAlphaChannel = [4, 6].includes(this.colorType);\n          var colors = this.colors + (this.hasAlphaChannel ? 1 : 0);\n          this.pixelBitlength = this.bits * colors;\n\n          switch (this.colors) {\n            case 1:\n              this.colorSpace = 'DeviceGray';\n              break;\n            case 3:\n              this.colorSpace = 'DeviceRGB';\n              break;\n          }\n\n          this.imgData = new Buffer(this.imgData);\n          return;\n          break;\n\n        default:\n          // unknown (or unimportant) section, skip it\n          this.pos += chunkSize;\n      }\n\n      this.pos += 4; // Skip the CRC\n\n      if (this.pos > this.data.length) {\n        throw new Error('Incomplete or corrupt PNG file');\n      }\n    }\n  }\n\n  read(bytes) {\n    const result = new Array(bytes);\n    for (let i = 0; i < bytes; i++) {\n      result[i] = this.data[this.pos++];\n    }\n    return result;\n  }\n\n  readUInt32() {\n    const b1 = this.data[this.pos++] << 24;\n    const b2 = this.data[this.pos++] << 16;\n    const b3 = this.data[this.pos++] << 8;\n    const b4 = this.data[this.pos++];\n    return b1 | b2 | b3 | b4;\n  }\n\n  readUInt16() {\n    const b1 = this.data[this.pos++] << 8;\n    const b2 = this.data[this.pos++];\n    return b1 | b2;\n  }\n\n  decodePixels(fn) {\n    return zlib.inflate(this.imgData, (err, data) => {\n      if (err) {\n        throw err;\n      }\n\n      const { width, height } = this;\n      const pixelBytes = this.pixelBitlength / 8;\n\n      const pixels = new Buffer(width * height * pixelBytes);\n      const { length } = data;\n      let pos = 0;\n\n      function pass(x0, y0, dx, dy, singlePass = false) {\n        const w = Math.ceil((width - x0) / dx);\n        const h = Math.ceil((height - y0) / dy);\n        const scanlineLength = pixelBytes * w;\n        const buffer = singlePass ? pixels : new Buffer(scanlineLength * h);\n        let row = 0;\n        let c = 0;\n        while (row < h && pos < length) {\n          var byte, col, i, left, upper;\n          switch (data[pos++]) {\n            case 0: // None\n              for (i = 0; i < scanlineLength; i++) {\n                buffer[c++] = data[pos++];\n              }\n              break;\n\n            case 1: // Sub\n              for (i = 0; i < scanlineLength; i++) {\n                byte = data[pos++];\n                left = i < pixelBytes ? 0 : buffer[c - pixelBytes];\n                buffer[c++] = (byte + left) % 256;\n              }\n              break;\n\n            case 2: // Up\n              for (i = 0; i < scanlineLength; i++) {\n                byte = data[pos++];\n                col = (i - (i % pixelBytes)) / pixelBytes;\n                upper =\n                  row &&\n                  buffer[\n                    (row - 1) * scanlineLength +\n                      col * pixelBytes +\n                      (i % pixelBytes)\n                  ];\n                buffer[c++] = (upper + byte) % 256;\n              }\n              break;\n\n            case 3: // Average\n              for (i = 0; i < scanlineLength; i++) {\n                byte = data[pos++];\n                col = (i - (i % pixelBytes)) / pixelBytes;\n                left = i < pixelBytes ? 0 : buffer[c - pixelBytes];\n                upper =\n                  row &&\n                  buffer[\n                    (row - 1) * scanlineLength +\n                      col * pixelBytes +\n                      (i % pixelBytes)\n                  ];\n                buffer[c++] = (byte + Math.floor((left + upper) / 2)) % 256;\n              }\n              break;\n\n            case 4: // Paeth\n              for (i = 0; i < scanlineLength; i++) {\n                var paeth, upperLeft;\n                byte = data[pos++];\n                col = (i - (i % pixelBytes)) / pixelBytes;\n                left = i < pixelBytes ? 0 : buffer[c - pixelBytes];\n\n                if (row === 0) {\n                  upper = upperLeft = 0;\n                } else {\n                  upper =\n                    buffer[\n                      (row - 1) * scanlineLength +\n                        col * pixelBytes +\n                        (i % pixelBytes)\n                    ];\n                  upperLeft =\n                    col &&\n                    buffer[\n                      (row - 1) * scanlineLength +\n                        (col - 1) * pixelBytes +\n                        (i % pixelBytes)\n                    ];\n                }\n\n                const p = left + upper - upperLeft;\n                const pa = Math.abs(p - left);\n                const pb = Math.abs(p - upper);\n                const pc = Math.abs(p - upperLeft);\n\n                if (pa <= pb && pa <= pc) {\n                  paeth = left;\n                } else if (pb <= pc) {\n                  paeth = upper;\n                } else {\n                  paeth = upperLeft;\n                }\n\n                buffer[c++] = (byte + paeth) % 256;\n              }\n              break;\n\n            default:\n              throw new Error(`Invalid filter algorithm: ${data[pos - 1]}`);\n          }\n\n          if (!singlePass) {\n            let pixelsPos = ((y0 + row * dy) * width + x0) * pixelBytes;\n            let bufferPos = row * scanlineLength;\n            for (i = 0; i < w; i++) {\n              for (let j = 0; j < pixelBytes; j++)\n                pixels[pixelsPos++] = buffer[bufferPos++];\n              pixelsPos += (dx - 1) * pixelBytes;\n            }\n          }\n\n          row++;\n        }\n      }\n\n      if (this.interlaceMethod === 1) {\n        /*\n          1 6 4 6 2 6 4 6\n          7 7 7 7 7 7 7 7\n          5 6 5 6 5 6 5 6\n          7 7 7 7 7 7 7 7\n          3 6 4 6 3 6 4 6\n          7 7 7 7 7 7 7 7\n          5 6 5 6 5 6 5 6\n          7 7 7 7 7 7 7 7\n        */\n        pass(0, 0, 8, 8); // 1\n        pass(4, 0, 8, 8); // 2\n        pass(0, 4, 4, 8); // 3\n        pass(2, 0, 4, 4); // 4\n        pass(0, 2, 2, 4); // 5\n        pass(1, 0, 2, 2); // 6\n        pass(0, 1, 1, 2); // 7\n      } else {\n        pass(0, 0, 1, 1, true);\n      }\n\n      return fn(pixels);\n    });\n  }\n\n  decodePalette() {\n    const { palette } = this;\n    const { length } = palette;\n    const transparency = this.transparency.indexed || [];\n    const ret = new Buffer(transparency.length + length);\n    let pos = 0;\n    let c = 0;\n\n    for (let i = 0; i < length; i += 3) {\n      var left;\n      ret[pos++] = palette[i];\n      ret[pos++] = palette[i + 1];\n      ret[pos++] = palette[i + 2];\n      ret[pos++] = (left = transparency[c++]) != null ? left : 255;\n    }\n\n    return ret;\n  }\n\n  copyToImageData(imageData, pixels) {\n    let j, k;\n    let { colors } = this;\n    let palette = null;\n    let alpha = this.hasAlphaChannel;\n\n    if (this.palette.length) {\n      palette =\n        this._decodedPalette || (this._decodedPalette = this.decodePalette());\n      colors = 4;\n      alpha = true;\n    }\n\n    const data = imageData.data || imageData;\n    const { length } = data;\n    const input = palette || pixels;\n    let i = (j = 0);\n\n    if (colors === 1) {\n      while (i < length) {\n        k = palette ? pixels[i / 4] * 4 : j;\n        const v = input[k++];\n        data[i++] = v;\n        data[i++] = v;\n        data[i++] = v;\n        data[i++] = alpha ? input[k++] : 255;\n        j = k;\n      }\n    } else {\n      while (i < length) {\n        k = palette ? pixels[i / 4] * 4 : j;\n        data[i++] = input[k++];\n        data[i++] = input[k++];\n        data[i++] = input[k++];\n        data[i++] = alpha ? input[k++] : 255;\n        j = k;\n      }\n    }\n  }\n\n  decode(fn) {\n    const ret = new Buffer(this.width * this.height * 4);\n    return this.decodePixels(pixels => {\n      this.copyToImageData(ret, pixels);\n      return fn(ret);\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/png-js/png-node.js\n");

/***/ })

};
;