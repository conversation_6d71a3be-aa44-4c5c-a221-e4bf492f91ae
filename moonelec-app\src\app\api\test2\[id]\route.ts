import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Convertir params en objet standard pour éviter les problèmes d'accès asynchrone
    const paramsObj = { ...params };
    const id = paramsObj.id;
    
    return NextResponse.json({ id });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'An error occurred' }, { status: 500 });
  }
}
