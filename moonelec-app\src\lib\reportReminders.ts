import { prisma } from './prisma';
import { v4 as uuidv4 } from 'uuid';

// Check for incomplete reports and send reminders
export async function checkIncompleteReports() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Get all commercials
  const commercials = await prisma.commercial.findMany({
    include: {
      user: true,
    },
  });
  
  const results = [];
  
  for (const commercial of commercials) {
    // Check if the commercial has submitted a report today
    const report = await prisma.salesreport.findFirst({
      where: {
        commercialId: commercial.id,
        submittedAt: {
          gte: today,
        },
        isCompleted: true,
      },
    });
    
    if (!report) {
      // Commercial hasn't submitted a report today
      // Check if we need to send a reminder (every 2 hours)
      const lastReminder = await prisma.salesreport.findFirst({
        where: {
          commercialId: commercial.id,
          lastReminder: {
            not: null,
          },
          isCompleted: false,
        },
        orderBy: {
          lastReminder: 'desc',
        },
      });
      
      const twoHoursAgo = new Date();
      twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);
      
      if (!lastReminder || (lastReminder.lastReminder && lastReminder.lastReminder < twoHoursAgo)) {
        // Create or update a reminder report
        const reportId = lastReminder ? lastReminder.id : uuidv4();
        
        const reminderReport = await prisma.salesreport.upsert({
          where: {
            id: reportId,
          },
          update: {
            lastReminder: new Date(),
          },
          create: {
            id: reportId,
            commercialId: commercial.id,
            need: '',
            visitDate: today,
            denomination: '',
            name: '',
            visitPurpose: '',
            city: '',
            lastReminder: new Date(),
            isCompleted: false,
          },
        });
        
        // Create notifications for admins
        const admins = await prisma.admin.findMany();
        
        for (const admin of admins) {
          await prisma.notification.create({
            data: {
              id: uuidv4(),
              type: 'REPORT_REMINDER',
              message: `${commercial.user.firstname} ${commercial.user.lastname} has not submitted their daily report yet.`,
              adminId: admin.id,
              salesReportId: reminderReport.id,
              isRead: false,
            },
          });
        }
        
        results.push({
          commercial,
          reminderReport,
          needsReminder: true,
        });
      } else {
        results.push({
          commercial,
          needsReminder: false,
        });
      }
    } else {
      results.push({
        commercial,
        report,
        needsReminder: false,
      });
    }
  }
  
  return results;
}

// Schedule the check to run every 2 hours
export function scheduleReportReminders() {
  // Only run in production to avoid multiple instances in development
  if (process.env.NODE_ENV === 'production') {
    // Initial check
    checkIncompleteReports();
    
    // Schedule checks every 2 hours
    setInterval(checkIncompleteReports, 2 * 60 * 60 * 1000);
  }
}
