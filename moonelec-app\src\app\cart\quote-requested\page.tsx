'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaHome, FaUser, FaFileAlt } from 'react-icons/fa';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import PageLayout from '@/components/layout/PageLayout';

export default function QuoteRequestedPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 mb-6"
          >
            <FaCheckCircle className="w-12 h-12" />
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-2xl font-bold text-gray-800 dark:text-white mb-4"
          >
            Demande de devis envoyée avec succès !
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-gray-600 dark:text-gray-300 mb-8"
          >
            Merci pour votre demande de devis. Notre équipe commerciale va étudier votre demande et vous contactera dans les plus brefs délais.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8 text-left"
          >
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Que se passe-t-il maintenant ?
            </h2>
            <ol className="space-y-4">
              <li className="flex items-start">
                <span className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary text-white mr-3">
                  1
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  Notre équipe commerciale va examiner votre demande et préparer un devis personnalisé avec les prix exacts.
                </span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary text-white mr-3">
                  2
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  Vous recevrez une notification par email lorsque votre devis sera prêt.
                </span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary text-white mr-3">
                  3
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  Vous pourrez consulter, télécharger et approuver votre devis depuis votre espace client.
                </span>
              </li>
            </ol>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex flex-col sm:flex-row justify-center gap-4"
          >
            <Link href="/">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full sm:w-auto px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center gap-2"
              >
                <FaHome />
                Retour à l'accueil
              </motion.button>
            </Link>

            {isAuthenticated && (
              <Link href="/client/quotes">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full sm:w-auto px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors flex items-center justify-center gap-2"
                >
                  <FaFileAlt />
                  Mes devis
                </motion.button>
              </Link>
            )}

            {isAuthenticated && (
              <Link href="/client/dashboard">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full sm:w-auto px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors flex items-center justify-center gap-2"
                >
                  <FaUser />
                  Mon espace client
                </motion.button>
              </Link>
            )}
          </motion.div>
        </div>
      </div>
    </div>
    </PageLayout>
  );
}
