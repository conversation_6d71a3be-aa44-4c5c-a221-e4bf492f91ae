'use client';

import { motion } from 'framer-motion';
import { FaShoppingCart, FaUser, FaBox, FaUserTie, FaEllipsisH } from 'react-icons/fa';

export default function RecentActivityCard() {
  // Données simulées pour les activités récentes
  const activities = [
    {
      id: 1,
      type: 'order',
      title: 'Nouvelle commande #12345',
      description: 'Client: Société ABC',
      time: 'Il y a 5 minutes',
      icon: <FaShoppingCart />,
      iconBg: 'bg-blue-100 dark:bg-blue-900/20',
      iconColor: 'text-blue-500',
    },
    {
      id: 2,
      type: 'client',
      title: 'Nouveau client inscrit',
      description: 'Entreprise XYZ',
      time: 'Il y a 30 minutes',
      icon: <FaUser />,
      iconBg: 'bg-green-100 dark:bg-green-900/20',
      iconColor: 'text-green-500',
    },
    {
      id: 3,
      type: 'product',
      title: 'Mise à jour du stock',
      description: '15 produits ajoutés',
      time: 'Il y a 2 heures',
      icon: <FaBox />,
      iconBg: 'bg-purple-100 dark:bg-purple-900/20',
      iconColor: 'text-purple-500',
    },
    {
      id: 4,
      type: 'commercial',
      title: 'Nouveau commercial',
      description: 'Ahmed Benani a rejoint l\'équipe',
      time: 'Il y a 5 heures',
      icon: <FaUserTie />,
      iconBg: 'bg-yellow-100 dark:bg-yellow-900/20',
      iconColor: 'text-yellow-500',
    },
    {
      id: 5,
      type: 'order',
      title: 'Commande #12340 expédiée',
      description: 'Client: Entreprise DEF',
      time: 'Il y a 8 heures',
      icon: <FaShoppingCart />,
      iconBg: 'bg-blue-100 dark:bg-blue-900/20',
      iconColor: 'text-blue-500',
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md h-full">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Activités récentes
        </h2>
        <button className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
          <FaEllipsisH />
        </button>
      </div>
      
      <div className="p-6">
        <ul className="space-y-6">
          {activities.map((activity, index) => (
            <motion.li
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex"
            >
              <div className="relative">
                <div className={`w-10 h-10 rounded-full ${activity.iconBg} flex items-center justify-center ${activity.iconColor}`}>
                  {activity.icon}
                </div>
                {index < activities.length - 1 && (
                  <div className="absolute top-10 left-1/2 w-px h-6 bg-gray-200 dark:bg-gray-700 transform -translate-x-1/2"></div>
                )}
              </div>
              
              <div className="ml-4 flex-1">
                <div className="flex justify-between">
                  <h3 className="text-sm font-medium text-gray-800 dark:text-white">
                    {activity.title}
                  </h3>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {activity.time}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  {activity.description}
                </p>
              </div>
            </motion.li>
          ))}
        </ul>
      </div>
      
      <div className="px-6 py-3 border-t border-gray-200 dark:border-gray-700 text-center">
        <button className="text-primary hover:underline text-sm font-medium">
          Voir toutes les activités
        </button>
      </div>
    </div>
  );
}
