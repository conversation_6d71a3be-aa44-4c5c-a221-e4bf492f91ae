'use client';

import { ReactNode, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { 
  FaTachometerAlt, 
  FaShoppingCart, 
  FaFileInvoice, 
  FaHeart, 
  FaUser, 
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaBell
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';

export default function ClientLayout({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const menuItems = [
    { icon: <FaTachometerAlt />, label: 'Tableau de bord', href: '/client/dashboard' },
    { icon: <FaShoppingCart />, label: 'Commandes', href: '/client/orders' },
    { icon: <FaFileInvoice />, label: 'Factures', href: '/client/invoices' },
    { icon: <FaHeart />, label: 'Favoris', href: '/client/favorites' },
    { icon: <FaUser />, label: 'Profil', href: '/client/profile' },
  ];

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  return (
    <RouteGuard allowedRoles={['CLIENT']}>
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <Link href="/">
                    <Image
                      src="/images/logo/logo-moonelec.png"
                      alt="Moonelec Logo"
                      width={150}
                      height={40}
                      className="h-10 w-auto"
                    />
                  </Link>
                </div>
              </div>
              
              <div className="hidden md:ml-6 md:flex md:items-center md:space-x-4">
                {menuItems.map((item, index) => {
                  const isActive = typeof window !== 'undefined' && window.location.pathname === item.href;
                  
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className={`px-3 py-2 rounded-md text-sm font-medium ${
                        isActive
                          ? 'bg-primary text-white'
                          : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                    >
                      <span className="flex items-center">
                        <span className="mr-2">{item.icon}</span>
                        {item.label}
                      </span>
                    </Link>
                  );
                })}
              </div>
              
              <div className="flex items-center">
                <button className="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                  <FaBell />
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </button>
                
                <div className="ml-3 relative">
                  <div className="flex items-center">
                    <button
                      onClick={() => router.push('/client/profile')}
                      className="flex items-center space-x-2 focus:outline-none"
                    >
                      <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
                        <FaUser className="w-full h-full text-gray-400 p-1" />
                      </div>
                      <span className="hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {user?.firstname}
                      </span>
                    </button>
                  </div>
                </div>
                
                <button
                  onClick={handleLogout}
                  className="ml-4 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hidden md:block"
                >
                  <FaSignOutAlt />
                </button>
                
                <div className="ml-4 md:hidden">
                  <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
                  >
                    {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden"
            >
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                {menuItems.map((item, index) => {
                  const isActive = typeof window !== 'undefined' && window.location.pathname === item.href;
                  
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className={`block px-3 py-2 rounded-md text-base font-medium ${
                        isActive
                          ? 'bg-primary text-white'
                          : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="flex items-center">
                        <span className="mr-2">{item.icon}</span>
                        {item.label}
                      </span>
                    </Link>
                  );
                })}
                
                <button
                  onClick={handleLogout}
                  className="w-full text-left block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                >
                  <span className="flex items-center">
                    <span className="mr-2"><FaSignOutAlt /></span>
                    Déconnexion
                  </span>
                </button>
              </div>
            </motion.div>
          )}
        </header>

        {/* Main Content */}
        <main className="py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </RouteGuard>
  );
}
