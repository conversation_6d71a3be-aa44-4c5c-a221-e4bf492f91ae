{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      console.log('🔐 No valid authorization header found');\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n    console.log('🔐 Mobile token received (length):', token.length);\n\n    // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET\n    let decoded: any;\n    try {\n      const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';\n      console.log('🔐 Trying JWT_SECRET for token verification');\n      decoded = jwt.verify(token, jwtSecret) as any;\n      console.log('🔐 Token decoded successfully with JWT_SECRET:', { userId: decoded.userId, role: decoded.role });\n    } catch (jwtError) {\n      console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');\n      try {\n        const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';\n        decoded = jwt.verify(token, nextAuthSecret) as any;\n        console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', { userId: decoded.userId, role: decoded.role });\n      } catch (nextAuthError) {\n        console.error('🔐 Both JWT secrets failed:', { jwtError: jwtError.message, nextAuthError: nextAuthError.message });\n        throw new Error('Invalid token signature');\n      }\n    }\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n      }\n    });\n\n    if (!user) {\n      console.log('🔐 User not found:', { found: !!user });\n      return null;\n    }\n\n    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n\n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') ||\n         userAgent.includes('Expo') ||\n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  // Always try to verify mobile token if Authorization header is present\n  const authHeader = request.headers.get('authorization');\n  if (authHeader?.startsWith('Bearer ')) {\n    return await verifyMobileToken(request);\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QACjE,QAAQ,GAAG,CAAC,sCAAsC,MAAM,MAAM;QAE9D,kFAAkF;QAClF,IAAI;QACJ,IAAI;YACF,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI;YAC5C,QAAQ,GAAG,CAAC;YACZ,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAC5B,QAAQ,GAAG,CAAC,kDAAkD;gBAAE,QAAQ,QAAQ,MAAM;gBAAE,MAAM,QAAQ,IAAI;YAAC;QAC7G,EAAE,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,eAAe,IAAI;gBACtD,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;gBAC5B,QAAQ,GAAG,CAAC,uDAAuD;oBAAE,QAAQ,QAAQ,MAAM;oBAAE,MAAM,QAAQ,IAAI;gBAAC;YAClH,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,+BAA+B;oBAAE,UAAU,SAAS,OAAO;oBAAE,eAAe,cAAc,OAAO;gBAAC;gBAChH,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QAEA,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,sBAAsB;gBAAE,OAAO,CAAC,CAAC;YAAK;YAClD,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,8CAA8C;YAAE,IAAI,KAAK,EAAE;YAAE,MAAM,KAAK,IAAI;QAAC;QACzF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,uEAAuE;IACvE,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,YAAY,WAAW,YAAY;QACrC,OAAO,MAAM,kBAAkB;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/upload.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\n// Security configuration\nconst SECURITY_CONFIG = {\n  maxFileSize: 25 * 1024 * 1024, // 25MB\n  allowedMimeTypes: [\n    // Images\n    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',\n    // Documents\n    'application/pdf', 'application/msword',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    'application/vnd.ms-excel',\n    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    'application/vnd.ms-powerpoint',\n    'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n    // Text files\n    'text/plain', 'text/csv', 'text/xml', 'application/json',\n    // Archives (limited)\n    'application/zip'\n  ],\n  dangerousExtensions: ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'jsp', 'sh'],\n  suspiciousPatterns: [\n    Buffer.from('MZ'), // PE executable header\n    Buffer.from('#!/'), // Script shebang\n  ]\n};\n\n// Security validation functions\nconst validateFileSize = (file: File): void => {\n  if (file.size > SECURITY_CONFIG.maxFileSize) {\n    throw new Error(`File too large. Maximum size is ${SECURITY_CONFIG.maxFileSize / (1024 * 1024)}MB`);\n  }\n};\n\nconst validateFileType = (file: File): void => {\n  if (!SECURITY_CONFIG.allowedMimeTypes.includes(file.type)) {\n    throw new Error('File type not allowed. Allowed types: images, PDF, Office documents, text files');\n  }\n};\n\nconst validateFileExtension = (fileName: string): void => {\n  const extension = path.extname(fileName).toLowerCase().substring(1);\n  if (SECURITY_CONFIG.dangerousExtensions.includes(extension)) {\n    throw new Error('File extension not allowed for security reasons');\n  }\n};\n\nconst scanForMalware = (buffer: Buffer): void => {\n  for (const pattern of SECURITY_CONFIG.suspiciousPatterns) {\n    if (buffer.indexOf(pattern) === 0) {\n      throw new Error('File contains suspicious content and cannot be uploaded');\n    }\n  }\n};\n\n// Ensure upload directory exists\nconst createUploadDir = (dir: string) => {\n  const uploadDir = path.join(process.cwd(), 'public', dir);\n  if (!fs.existsSync(uploadDir)) {\n    fs.mkdirSync(uploadDir, { recursive: true });\n  }\n  return uploadDir;\n};\n\n// Save a file to the public directory with security validation\nexport const saveFile = async (\n  file: File,\n  directory: string = 'uploads'\n): Promise<string> => {\n  // Security validations\n  validateFileSize(file);\n  validateFileType(file);\n  validateFileExtension(file.name);\n\n  const uploadDir = createUploadDir(directory);\n\n  // Convert file to buffer for malware scanning\n  const buffer = Buffer.from(await file.arrayBuffer());\n  scanForMalware(buffer);\n\n  // Generate a unique filename\n  const fileExtension = path.extname(file.name);\n  const fileName = `${uuidv4()}${fileExtension}`;\n  const filePath = path.join(uploadDir, fileName);\n\n  // Write file to disk\n  fs.writeFileSync(filePath, buffer);\n\n  // Return the public URL\n  return `/${directory}/${fileName}`;\n};\n\n// Save a base64 image to the public directory\nexport const saveBase64Image = (\n  base64Data: string,\n  directory: string = 'uploads'\n): string => {\n  // Create directory if it doesn't exist\n  const uploadDir = createUploadDir(directory);\n\n  // Extract the file extension from the base64 data\n  const matches = base64Data.match(/^data:image\\/([a-zA-Z]+);base64,/);\n  if (!matches || matches.length !== 2) {\n    throw new Error('Invalid base64 image format');\n  }\n\n  const fileExtension = matches[1];\n  const base64Image = base64Data.replace(/^data:image\\/[a-zA-Z]+;base64,/, '');\n\n  // Generate a unique filename\n  const fileName = `${uuidv4()}.${fileExtension}`;\n  const filePath = path.join(uploadDir, fileName);\n\n  // Write file to disk\n  fs.writeFileSync(filePath, base64Image, 'base64');\n\n  // Return the public URL\n  return `/${directory}/${fileName}`;\n};\n\n// Delete a file from the public directory\nexport const deleteFile = (fileUrl: string): boolean => {\n  try {\n    // Extract the file path from the URL\n    const filePath = path.join(process.cwd(), 'public', fileUrl);\n\n    // Check if file exists\n    if (fs.existsSync(filePath)) {\n      // Delete the file\n      fs.unlinkSync(filePath);\n      return true;\n    }\n    return false;\n  } catch (error) {\n    console.error('Error deleting file:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,yBAAyB;AACzB,MAAM,kBAAkB;IACtB,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAChB,SAAS;QACT;QAAc;QAAa;QAAa;QAAa;QAAc;QACnE,YAAY;QACZ;QAAmB;QACnB;QACA;QACA;QACA;QACA;QACA,aAAa;QACb;QAAc;QAAY;QAAY;QACtC,qBAAqB;QACrB;KACD;IACD,qBAAqB;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAM;QAAO;QAAO;QAAO;QAAO;KAAK;IAC9G,oBAAoB;QAClB,OAAO,IAAI,CAAC;QACZ,OAAO,IAAI,CAAC;KACb;AACH;AAEA,gCAAgC;AAChC,MAAM,mBAAmB,CAAC;IACxB,IAAI,KAAK,IAAI,GAAG,gBAAgB,WAAW,EAAE;QAC3C,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,gBAAgB,WAAW,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IACpG;AACF;AAEA,MAAM,mBAAmB,CAAC;IACxB,IAAI,CAAC,gBAAgB,gBAAgB,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;QACzD,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,MAAM,wBAAwB,CAAC;IAC7B,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW,GAAG,SAAS,CAAC;IACjE,IAAI,gBAAgB,mBAAmB,CAAC,QAAQ,CAAC,YAAY;QAC3D,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,KAAK,MAAM,WAAW,gBAAgB,kBAAkB,CAAE;QACxD,IAAI,OAAO,OAAO,CAAC,aAAa,GAAG;YACjC,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEA,iCAAiC;AACjC,MAAM,kBAAkB,CAAC;IACvB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;IACrD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;QAC7B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW;YAAE,WAAW;QAAK;IAC5C;IACA,OAAO;AACT;AAGO,MAAM,WAAW,OACtB,MACA,YAAoB,SAAS;IAE7B,uBAAuB;IACvB,iBAAiB;IACjB,iBAAiB;IACjB,sBAAsB,KAAK,IAAI;IAE/B,MAAM,YAAY,gBAAgB;IAElC,8CAA8C;IAC9C,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;IACjD,eAAe;IAEf,6BAA6B;IAC7B,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;IAC5C,MAAM,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,MAAM,eAAe;IAC9C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,qBAAqB;IACrB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;IAE3B,wBAAwB;IACxB,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU;AACpC;AAGO,MAAM,kBAAkB,CAC7B,YACA,YAAoB,SAAS;IAE7B,uCAAuC;IACvC,MAAM,YAAY,gBAAgB;IAElC,kDAAkD;IAClD,MAAM,UAAU,WAAW,KAAK,CAAC;IACjC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,gBAAgB,OAAO,CAAC,EAAE;IAChC,MAAM,cAAc,WAAW,OAAO,CAAC,kCAAkC;IAEzE,6BAA6B;IAC7B,MAAM,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,IAAI,CAAC,EAAE,eAAe;IAC/C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,qBAAqB;IACrB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU,aAAa;IAExC,wBAAwB;IACxB,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU;AACpC;AAGO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,qCAAqC;QACrC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QAEpD,uBAAuB;QACvB,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC3B,kBAAkB;YAClB,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YACd,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/chat/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth-options';\nimport { getMobileUserFromRequest } from '@/lib/mobile-auth';\nimport { saveFile } from '@/lib/upload';\nimport { prisma } from '@/lib/prisma';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Verify authentication\n    const mobileUser = await getMobileUserFromRequest(request);\n    const session = await getServerSession(authOptions);\n\n    if (!mobileUser && !session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const user = mobileUser || session?.user;\n    if (!user) {\n      return NextResponse.json({ error: 'User not found' }, { status: 401 });\n    }\n\n    const formData = await request.formData();\n    const file = formData.get('file') as File | null;\n    const conversationId = formData.get('conversationId') as string;\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    if (!conversationId) {\n      return NextResponse.json({ error: 'Conversation ID required' }, { status: 400 });\n    }\n\n    // Verify user has access to this conversation\n    const conversation = await prisma.chatconversation.findFirst({\n      where: {\n        id: conversationId,\n        OR: [\n          { adminId: user.id },\n          { commercialId: user.id }\n        ]\n      }\n    });\n\n    if (!conversation) {\n      return NextResponse.json({ error: 'Conversation not found or access denied' }, { status: 403 });\n    }\n\n    try {\n      // Upload file to chat directory\n      const fileUrl = await saveFile(file, 'chat');\n\n      // Create file metadata record\n      const fileRecord = await prisma.chatfile.create({\n        data: {\n          fileName: file.name,\n          fileUrl,\n          fileSize: file.size,\n          fileType: file.type,\n          uploadedById: user.id,\n          conversationId: conversationId\n        }\n      });\n\n      return NextResponse.json({\n        success: true,\n        file: {\n          id: fileRecord.id,\n          fileName: file.name,\n          fileUrl,\n          fileSize: file.size,\n          fileType: file.type,\n          uploadedAt: fileRecord.createdAt,\n          uploadedBy: {\n            id: user.id,\n            name: user.firstname + ' ' + user.lastname\n          }\n        }\n      });\n\n    } catch (uploadError: any) {\n      console.error('File upload error:', uploadError);\n      return NextResponse.json({\n        error: uploadError.message || 'Failed to upload file'\n      }, { status: 400 });\n    }\n\n  } catch (error) {\n    console.error('Chat file upload error:', error);\n    return NextResponse.json({\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,wBAAwB;QACxB,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,cAAc,SAAS;QACpC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,iBAAiB,SAAS,GAAG,CAAC;QAEpC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,8CAA8C;QAC9C,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;YAC3D,OAAO;gBACL,IAAI;gBACJ,IAAI;oBACF;wBAAE,SAAS,KAAK,EAAE;oBAAC;oBACnB;wBAAE,cAAc,KAAK,EAAE;oBAAC;iBACzB;YACH;QACF;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0C,GAAG;gBAAE,QAAQ;YAAI;QAC/F;QAEA,IAAI;YACF,gCAAgC;YAChC,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,MAAM;YAErC,8BAA8B;YAC9B,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC9C,MAAM;oBACJ,UAAU,KAAK,IAAI;oBACnB;oBACA,UAAU,KAAK,IAAI;oBACnB,UAAU,KAAK,IAAI;oBACnB,cAAc,KAAK,EAAE;oBACrB,gBAAgB;gBAClB;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,IAAI,WAAW,EAAE;oBACjB,UAAU,KAAK,IAAI;oBACnB;oBACA,UAAU,KAAK,IAAI;oBACnB,UAAU,KAAK,IAAI;oBACnB,YAAY,WAAW,SAAS;oBAChC,YAAY;wBACV,IAAI,KAAK,EAAE;wBACX,MAAM,KAAK,SAAS,GAAG,MAAM,KAAK,QAAQ;oBAC5C;gBACF;YACF;QAEF,EAAE,OAAO,aAAkB;YACzB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,YAAY,OAAO,IAAI;YAChC,GAAG;gBAAE,QAAQ;YAAI;QACnB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}