{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/extract-pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport pdf from 'pdf-parse';\nimport OpenAI from 'openai';\nimport { authOptions } from '@/lib/auth-options';\n\n// Types pour les données extraites\ninterface ExtractedProductData {\n  productName: string;\n  reference: string | string[];\n  description: string;\n  characteristics: Record<string, string>;\n}\n\n// Configuration OpenAI\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// POST /api/extract-pdf - Extract product data from PDF using AI\nexport async function POST(req: NextRequest) {\n  try {\n    // Vérification de l'authentification\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Vérification de la clé API OpenAI\n    if (!process.env.OPENAI_API_KEY) {\n      return NextResponse.json(\n        { error: 'OpenAI API key not configured' },\n        { status: 500 }\n      );\n    }\n\n    // Récupération du fichier PDF\n    const formData = await req.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No PDF file provided' },\n        { status: 400 }\n      );\n    }\n\n    // Vérification du type de fichier\n    if (file.type !== 'application/pdf') {\n      return NextResponse.json(\n        { error: 'File must be a PDF' },\n        { status: 400 }\n      );\n    }\n\n    // Vérification de la taille du fichier (max 10MB)\n    if (file.size > 10 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'File size must be less than 10MB' },\n        { status: 400 }\n      );\n    }\n\n    console.log('📄 Processing PDF:', file.name, 'Size:', file.size);\n\n    // Conversion du fichier en buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Extraction du texte du PDF\n    console.log('🔍 Extracting text from PDF...');\n    const pdfData = await pdf(buffer);\n    const extractedText = pdfData.text;\n\n    if (!extractedText || extractedText.trim().length === 0) {\n      return NextResponse.json(\n        { error: 'No text could be extracted from the PDF' },\n        { status: 400 }\n      );\n    }\n\n    console.log('📝 Extracted text length:', extractedText.length);\n\n    // Prompt pour l'IA\n    const systemPrompt = `\nYou are a product sheet parser. Extract product information from the provided text and return ONLY a valid JSON object.\n\nRequired JSON format:\n{\n  \"productName\": \"string\",\n  \"reference\": \"string or array of strings\",\n  \"description\": \"string\",\n  \"characteristics\": { \"key\": \"value\", ... }\n}\n\nRules:\n- Return ONLY the JSON object, no other text\n- Do not wrap in markdown code blocks\n- If multiple references exist, use an array\n- Include all technical specifications in characteristics\n- Use descriptive keys for characteristics\n- If information is missing, use empty string or empty object\n`;\n\n    const userPrompt = `Texte extrait du PDF :\\n\\n${extractedText}`;\n\n    // Appel à l'API OpenAI\n    console.log('🤖 Calling OpenAI API...');\n    let completion;\n    try {\n      completion = await openai.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        messages: [\n          { role: 'system', content: systemPrompt },\n          { role: 'user', content: userPrompt }\n        ],\n        max_tokens: 1000,\n        temperature: 0.1, // Faible température pour plus de précision\n      });\n    } catch (openaiError) {\n      console.error('❌ OpenAI API Error:', openaiError);\n      const errorMessage = openaiError instanceof Error ? openaiError.message : 'Unknown OpenAI error';\n      return NextResponse.json(\n        { error: `OpenAI API Error: ${errorMessage}` },\n        { status: 500 }\n      );\n    }\n\n    const aiResponse = completion.choices[0]?.message?.content;\n\n    if (!aiResponse) {\n      return NextResponse.json(\n        { error: 'No response from AI' },\n        { status: 500 }\n      );\n    }\n\n    console.log('🤖 AI Response:', aiResponse);\n\n    // Extraction du JSON de la réponse\n    let extractedData: ExtractedProductData;\n    try {\n      // Nettoyer la réponse (enlever les espaces, retours à la ligne au début/fin)\n      let cleanedResponse = aiResponse.trim();\n\n      // Si la réponse commence par ```json, on l'enlève\n      if (cleanedResponse.startsWith('```json')) {\n        cleanedResponse = cleanedResponse.replace(/^```json\\s*/, '').replace(/\\s*```$/, '');\n      }\n\n      // Si la réponse commence par ```, on l'enlève\n      if (cleanedResponse.startsWith('```')) {\n        cleanedResponse = cleanedResponse.replace(/^```\\s*/, '').replace(/\\s*```$/, '');\n      }\n\n      // Recherche du JSON dans la réponse\n      const jsonMatch = cleanedResponse.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        console.error('❌ No JSON found in AI response. Full response:', aiResponse);\n        return NextResponse.json(\n          {\n            error: 'No valid JSON found in AI response',\n            debug: {\n              aiResponse: aiResponse.substring(0, 500) + (aiResponse.length > 500 ? '...' : ''),\n              cleanedResponse: cleanedResponse.substring(0, 500) + (cleanedResponse.length > 500 ? '...' : '')\n            }\n          },\n          { status: 500 }\n        );\n      }\n\n      const jsonString = jsonMatch[0];\n      console.log('🔍 Extracted JSON string:', jsonString);\n\n      extractedData = JSON.parse(jsonString);\n\n      // Validation basique des données extraites\n      if (!extractedData.productName && !extractedData.description) {\n        throw new Error('Invalid extracted data: missing required fields');\n      }\n\n    } catch (parseError) {\n      console.error('❌ JSON parsing error:', parseError);\n      console.error('❌ AI Response that failed to parse:', aiResponse);\n      return NextResponse.json(\n        {\n          error: 'Failed to parse AI response as JSON',\n          debug: {\n            parseError: parseError instanceof Error ? parseError.message : 'Unknown error',\n            aiResponse: aiResponse.substring(0, 500) + (aiResponse.length > 500 ? '...' : '')\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // Validation des données extraites\n    if (!extractedData.productName || !extractedData.description) {\n      return NextResponse.json(\n        { error: 'Insufficient product data extracted' },\n        { status: 400 }\n      );\n    }\n\n    console.log('✅ Successfully extracted product data:', extractedData.productName);\n\n    return NextResponse.json({\n      success: true,\n      data: extractedData,\n      metadata: {\n        fileName: file.name,\n        fileSize: file.size,\n        textLength: extractedText.length,\n        extractedAt: new Date().toISOString(),\n      }\n    });\n\n  } catch (error) {\n    console.error('❌ PDF extraction error:', error);\n\n    if (error instanceof Error) {\n      // Erreurs spécifiques d'OpenAI\n      if (error.message.includes('API key')) {\n        return NextResponse.json(\n          { error: 'OpenAI API configuration error' },\n          { status: 500 }\n        );\n      }\n\n      // Erreurs de parsing PDF\n      if (error.message.includes('pdf')) {\n        return NextResponse.json(\n          { error: 'Failed to parse PDF file' },\n          { status: 400 }\n        );\n      }\n    }\n\n    return NextResponse.json(\n      { error: 'Internal server error during PDF extraction' },\n      { status: 500 }\n    );\n  }\n}\n\n// GET /api/extract-pdf - Get extraction status/info\nexport async function GET() {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    return NextResponse.json({\n      available: !!process.env.OPENAI_API_KEY,\n      supportedFormats: ['application/pdf'],\n      maxFileSize: '10MB',\n      features: [\n        'Product name extraction',\n        'Reference number extraction',\n        'Technical characteristics extraction',\n        'Category and brand detection',\n        'Price extraction'\n      ]\n    });\n\n  } catch (error) {\n    console.error('Error getting extraction info:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAUA,uBAAuB;AACvB,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,qCAAqC;QACrC,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,oCAAoC;QACpC,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,WAAW,MAAM,IAAI,QAAQ;QACnC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,sBAAsB,KAAK,IAAI,EAAE,SAAS,KAAK,IAAI;QAE/D,kCAAkC;QAClC,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,6BAA6B;QAC7B,QAAQ,GAAG,CAAC;QACZ,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAG,AAAD,EAAE;QAC1B,MAAM,gBAAgB,QAAQ,IAAI;QAElC,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,6BAA6B,cAAc,MAAM;QAE7D,mBAAmB;QACnB,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;AAkB1B,CAAC;QAEG,MAAM,aAAa,CAAC,0BAA0B,EAAE,eAAe;QAE/D,uBAAuB;QACvB,QAAQ,GAAG,CAAC;QACZ,IAAI;QACJ,IAAI;YACF,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAChD,OAAO;gBACP,UAAU;oBACR;wBAAE,MAAM;wBAAU,SAAS;oBAAa;oBACxC;wBAAE,MAAM;wBAAQ,SAAS;oBAAW;iBACrC;gBACD,YAAY;gBACZ,aAAa;YACf;QACF,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,uBAAuB,QAAQ,YAAY,OAAO,GAAG;YAC1E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,kBAAkB,EAAE,cAAc;YAAC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;QAEnD,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,mCAAmC;QACnC,IAAI;QACJ,IAAI;YACF,6EAA6E;YAC7E,IAAI,kBAAkB,WAAW,IAAI;YAErC,kDAAkD;YAClD,IAAI,gBAAgB,UAAU,CAAC,YAAY;gBACzC,kBAAkB,gBAAgB,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,WAAW;YAClF;YAEA,8CAA8C;YAC9C,IAAI,gBAAgB,UAAU,CAAC,QAAQ;gBACrC,kBAAkB,gBAAgB,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW;YAC9E;YAEA,oCAAoC;YACpC,MAAM,YAAY,gBAAgB,KAAK,CAAC;YACxC,IAAI,CAAC,WAAW;gBACd,QAAQ,KAAK,CAAC,kDAAkD;gBAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,OAAO;oBACP,OAAO;wBACL,YAAY,WAAW,SAAS,CAAC,GAAG,OAAO,CAAC,WAAW,MAAM,GAAG,MAAM,QAAQ,EAAE;wBAChF,iBAAiB,gBAAgB,SAAS,CAAC,GAAG,OAAO,CAAC,gBAAgB,MAAM,GAAG,MAAM,QAAQ,EAAE;oBACjG;gBACF,GACA;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,aAAa,SAAS,CAAC,EAAE;YAC/B,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,gBAAgB,KAAK,KAAK,CAAC;YAE3B,2CAA2C;YAC3C,IAAI,CAAC,cAAc,WAAW,IAAI,CAAC,cAAc,WAAW,EAAE;gBAC5D,MAAM,IAAI,MAAM;YAClB;QAEF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,OAAO;oBACL,YAAY,sBAAsB,QAAQ,WAAW,OAAO,GAAG;oBAC/D,YAAY,WAAW,SAAS,CAAC,GAAG,OAAO,CAAC,WAAW,MAAM,GAAG,MAAM,QAAQ,EAAE;gBAClF;YACF,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,IAAI,CAAC,cAAc,WAAW,IAAI,CAAC,cAAc,WAAW,EAAE;YAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,0CAA0C,cAAc,WAAW;QAE/E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,UAAU;gBACR,UAAU,KAAK,IAAI;gBACnB,UAAU,KAAK,IAAI;gBACnB,YAAY,cAAc,MAAM;gBAChC,aAAa,IAAI,OAAO,WAAW;YACrC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,IAAI,iBAAiB,OAAO;YAC1B,+BAA+B;YAC/B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiC,GAC1C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA2B,GACpC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8C,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YACvC,kBAAkB;gBAAC;aAAkB;YACrC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}