'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaFileAlt, FaDownload, FaEye, FaSearch, FaCheck, FaTimes } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';

// Types
interface Quote {
  id: string;
  quoteNumber: string;
  date: string;
  validUntil: string;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  totalAmount: number;
  items: number;
}

export default function ClientQuotesPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // Données simulées pour les devis
  const mockQuotes: Quote[] = [
    {
      id: '1',
      quoteNumber: 'Q-2023-001',
      date: '15/07/2023',
      validUntil: '15/08/2023',
      status: 'PENDING',
      totalAmount: 12500,
      items: 5
    },
    {
      id: '2',
      quoteNumber: 'Q-2023-002',
      date: '10/07/2023',
      validUntil: '10/08/2023',
      status: 'APPROVED',
      totalAmount: 8750,
      items: 3
    },
    {
      id: '3',
      quoteNumber: 'Q-2023-003',
      date: '05/07/2023',
      validUntil: '05/08/2023',
      status: 'DRAFT',
      totalAmount: 0,
      items: 4
    },
    {
      id: '4',
      quoteNumber: 'Q-2023-004',
      date: '01/07/2023',
      validUntil: '01/08/2023',
      status: 'CONVERTED',
      totalAmount: 15000,
      items: 6
    }
  ];

  useEffect(() => {
    // Simuler le chargement des données
    const timer = setTimeout(() => {
      setQuotes(mockQuotes);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filtrer les devis en fonction de la recherche et du statut
  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = 
      quote.quoteNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus ? quote.status === selectedStatus : true;
    
    return matchesSearch && matchesStatus;
  });

  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'CONVERTED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Fonction pour obtenir le libellé du statut en français
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'Brouillon';
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvé';
      case 'REJECTED':
        return 'Rejeté';
      case 'EXPIRED':
        return 'Expiré';
      case 'CONVERTED':
        return 'Converti';
      default:
        return status;
    }
  };

  const handleApproveQuote = async (quoteId: string) => {
    // Logique pour approuver un devis
    console.log('Approbation du devis', quoteId);
  };

  const handleRejectQuote = async (quoteId: string) => {
    // Logique pour rejeter un devis
    console.log('Rejet du devis', quoteId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['CLIENT']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            Mes Devis
          </h1>
        </div>

        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Rechercher par numéro de devis..."
              />
            </div>
            <div className="w-full md:w-64">
              <select
                value={selectedStatus || ''}
                onChange={(e) => setSelectedStatus(e.target.value || null)}
                className="block w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Tous les statuts</option>
                <option value="DRAFT">Brouillon</option>
                <option value="PENDING">En attente</option>
                <option value="APPROVED">Approuvé</option>
                <option value="REJECTED">Rejeté</option>
                <option value="EXPIRED">Expiré</option>
                <option value="CONVERTED">Converti</option>
              </select>
            </div>
          </div>
        </div>

        {/* Quotes List */}
        {filteredQuotes.length > 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      N° Devis
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Validité
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Articles
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Montant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredQuotes.map((quote) => (
                    <tr key={quote.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {quote.quoteNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {quote.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {quote.validUntil}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {quote.items}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {quote.status === 'DRAFT' ? (
                          <span className="text-gray-500 dark:text-gray-400">Non défini</span>
                        ) : (
                          new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(quote.totalAmount)
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(quote.status)}`}>
                          {getStatusLabel(quote.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link href={`/client/quotes/${quote.id}`}>
                            <span className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 cursor-pointer">
                              <FaEye title="Voir les détails" />
                            </span>
                          </Link>
                          
                          {quote.status !== 'DRAFT' && quote.status !== 'PENDING' && (
                            <button
                              onClick={() => {
                                // Logique de téléchargement
                                console.log('Téléchargement du devis', quote.id);
                              }}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            >
                              <FaDownload title="Télécharger le devis" />
                            </button>
                          )}
                          
                          {quote.status === 'PENDING' && (
                            <>
                              <button
                                onClick={() => handleApproveQuote(quote.id)}
                                className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              >
                                <FaCheck title="Approuver" />
                              </button>
                              
                              <button
                                onClick={() => handleRejectQuote(quote.id)}
                                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              >
                                <FaTimes title="Rejeter" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div className="flex justify-center mb-4">
              <FaFileAlt className="text-5xl text-gray-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              Aucun devis trouvé
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Vous n'avez pas encore de devis ou aucun devis ne correspond à vos critères de recherche.
            </p>
            <Link href="/cart">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Demander un devis
              </motion.button>
            </Link>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
