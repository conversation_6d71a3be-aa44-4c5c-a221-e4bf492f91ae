'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { FaArrowUp, FaArrowDown } from 'react-icons/fa';

interface DashboardCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  change: number;
  period: string;
  isCurrency?: boolean;
}

export default function DashboardCard({
  title,
  value,
  icon,
  change,
  period,
  isCurrency = false,
}: DashboardCardProps) {
  const isPositive = change >= 0;
  
  // Formatter pour les nombres et les devises
  const formattedValue = isCurrency
    ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(value)
    : new Intl.NumberFormat('fr-FR').format(value);
  
  return (
    <motion.div
      whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}
      transition={{ duration: 0.2 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300">{title}</h3>
          <div className="text-2xl">{icon}</div>
        </div>
        
        <div className="flex items-end justify-between">
          <div>
            <p className="text-2xl font-bold text-gray-800 dark:text-white mb-1">
              {formattedValue}
            </p>
            <div className="flex items-center">
              <span
                className={`flex items-center text-sm ${
                  isPositive ? 'text-green-500' : 'text-red-500'
                }`}
              >
                {isPositive ? <FaArrowUp className="mr-1" /> : <FaArrowDown className="mr-1" />}
                {Math.abs(change)}%
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                {period}
              </span>
            </div>
          </div>
          
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: 'spring', stiffness: 200, damping: 10 }}
            className={`w-12 h-12 rounded-full flex items-center justify-center ${
              isPositive ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'
            }`}
          >
            {isPositive ? (
              <FaArrowUp className="text-green-500" />
            ) : (
              <FaArrowDown className="text-red-500" />
            )}
          </motion.div>
        </div>
      </div>
      
      <div
        className={`h-1 ${
          isPositive ? 'bg-green-500' : 'bg-red-500'
        }`}
      ></div>
    </motion.div>
  );
}
