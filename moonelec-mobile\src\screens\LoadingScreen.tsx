import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import AnimatedLogo from '../components/AnimatedLogo';
import LoadingAnimation from '../components/LoadingAnimation';
import { ElectricBackground } from '../components/ElectricIcons';

const { width, height } = Dimensions.get('window');

const LoadingScreen: React.FC = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <View style={styles.container}>
      <ElectricBackground />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Logo principal animé */}
        <AnimatedLogo
          size={150}
          showText={true}
          animationType="bounce"
        />

        {/* Animation de chargement */}
        <View style={styles.loadingContainer}>
          <LoadingAnimation size={60} color="#006db7" />
          <Text style={styles.loadingText}>Initialisation...</Text>
        </View>

        {/* Texte de bienvenue */}
        <Animated.View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>Bienvenue dans</Text>
          <Text style={styles.appNameText}>Moonelec Mobile</Text>
          <Text style={styles.descriptionText}>
            Votre catalogue de composants électriques
          </Text>
        </Animated.View>
      </Animated.View>

      {/* Indicateur de progression */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              },
            ]}
          />
        </View>
        <Text style={styles.progressText}>Chargement des données...</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 30,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: '#006db7',
    fontWeight: '500',
  },
  welcomeContainer: {
    alignItems: 'center',
    marginTop: 20,
    paddingHorizontal: 40,
  },
  welcomeText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 5,
  },
  appNameText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#006db7',
    marginBottom: 10,
  },
  descriptionText: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
  },
  progressContainer: {
    position: 'absolute',
    bottom: 80,
    left: 40,
    right: 40,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(0, 109, 183, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#006db7',
    borderRadius: 2,
  },
  progressText: {
    marginTop: 10,
    fontSize: 12,
    color: '#666',
  },
});

export default LoadingScreen;
