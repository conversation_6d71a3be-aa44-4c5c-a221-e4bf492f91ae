/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/clients/export/route";
exports.ids = ["app/api/admin/clients/export/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_admin_clients_export_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/clients/export/route.ts */ \"(rsc)/./src/app/api/admin/clients/export/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/clients/export/route\",\n        pathname: \"/api/admin/clients/export\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/clients/export/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\admin\\\\clients\\\\export\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_admin_clients_export_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRmNsaWVudHMlMkZleHBvcnQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFkbWluJTJGY2xpZW50cyUyRmV4cG9ydCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFkbWluJTJGY2xpZW50cyUyRmV4cG9ydCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNBc3VzJTVDRGVza3RvcCU1Q1Byb2plY3RzJTVDTW9vbmVsZWNBcHAlNUNtb29uZWxlYy1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q0FzdXMlNUNEZXNrdG9wJTVDUHJvamVjdHMlNUNNb29uZWxlY0FwcCU1Q21vb25lbGVjLWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDOEQ7QUFDM0k7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXEFzdXNcXFxcRGVza3RvcFxcXFxQcm9qZWN0c1xcXFxNb29uZWxlY0FwcFxcXFxtb29uZWxlYy1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYWRtaW5cXFxcY2xpZW50c1xcXFxleHBvcnRcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FkbWluL2NsaWVudHMvZXhwb3J0L3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYWRtaW4vY2xpZW50cy9leHBvcnRcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2FkbWluL2NsaWVudHMvZXhwb3J0L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcQXN1c1xcXFxEZXNrdG9wXFxcXFByb2plY3RzXFxcXE1vb25lbGVjQXBwXFxcXG1vb25lbGVjLWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhZG1pblxcXFxjbGllbnRzXFxcXGV4cG9ydFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/clients/export/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/admin/clients/export/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./src/lib/auth-options.ts\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! xlsx */ \"(rsc)/./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var pdfkit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pdfkit */ \"(rsc)/./node_modules/pdfkit/js/pdfkit.es.js\");\n\n\n\n\n\n\n// GET /api/admin/clients/export - Export clients data\nasync function GET(req) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session?.user || session.user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const searchParams = req.nextUrl.searchParams;\n        const format = searchParams.get('format') || 'excel';\n        const search = searchParams.get('search') || '';\n        const city = searchParams.get('city') || '';\n        const company = searchParams.get('company') || '';\n        // Build where clause (same as in the main route)\n        const where = {\n            user: {\n                role: 'CLIENT'\n            }\n        };\n        if (search) {\n            where.OR = [\n                {\n                    user: {\n                        firstname: {\n                            contains: search,\n                            mode: 'insensitive'\n                        }\n                    }\n                },\n                {\n                    user: {\n                        lastname: {\n                            contains: search,\n                            mode: 'insensitive'\n                        }\n                    }\n                },\n                {\n                    user: {\n                        email: {\n                            contains: search,\n                            mode: 'insensitive'\n                        }\n                    }\n                },\n                {\n                    company: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        if (city) {\n            where.city = {\n                contains: city,\n                mode: 'insensitive'\n            };\n        }\n        if (company) {\n            where.company = {\n                contains: company,\n                mode: 'insensitive'\n            };\n        }\n        // Get all clients (no pagination for export)\n        const clients = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findMany({\n            where,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        firstname: true,\n                        lastname: true,\n                        email: true,\n                        telephone: true,\n                        createdAt: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        // Prepare data for export\n        const exportData = clients.map((client)=>({\n                'ID': client.id,\n                'Prénom': client.user.firstname,\n                'Nom': client.user.lastname,\n                'Email': client.user.email,\n                'Téléphone': client.user.telephone || '',\n                'Entreprise': client.company || '',\n                'Adresse': client.address || '',\n                'Ville': client.city || '',\n                'Code Postal': client.postalCode || '',\n                'Pays': client.country || '',\n                'Date d\\'inscription': new Date(client.user.createdAt).toLocaleDateString('fr-FR')\n            }));\n        if (format === 'excel') {\n            // Create Excel file\n            const workbook = xlsx__WEBPACK_IMPORTED_MODULE_5__.utils.book_new();\n            const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_5__.utils.json_to_sheet(exportData);\n            // Set column widths\n            const colWidths = [\n                {\n                    wch: 10\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 25\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 20\n                },\n                {\n                    wch: 30\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 10\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                }\n            ];\n            worksheet['!cols'] = colWidths;\n            xlsx__WEBPACK_IMPORTED_MODULE_5__.utils.book_append_sheet(workbook, worksheet, 'Clients');\n            const buffer = xlsx__WEBPACK_IMPORTED_MODULE_5__.write(workbook, {\n                type: 'buffer',\n                bookType: 'xlsx'\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n                headers: {\n                    'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n                    'Content-Disposition': 'attachment; filename=clients.xlsx'\n                }\n            });\n        } else if (format === 'csv') {\n            // Create CSV file\n            const workbook = xlsx__WEBPACK_IMPORTED_MODULE_5__.utils.book_new();\n            const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_5__.utils.json_to_sheet(exportData);\n            xlsx__WEBPACK_IMPORTED_MODULE_5__.utils.book_append_sheet(workbook, worksheet, 'Clients');\n            const buffer = xlsx__WEBPACK_IMPORTED_MODULE_5__.write(workbook, {\n                type: 'buffer',\n                bookType: 'csv'\n            });\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n                headers: {\n                    'Content-Type': 'text/csv',\n                    'Content-Disposition': 'attachment; filename=clients.csv'\n                }\n            });\n        } else if (format === 'pdf') {\n            // Create PDF file\n            const doc = new pdfkit__WEBPACK_IMPORTED_MODULE_4__[\"default\"]({\n                margin: 50\n            });\n            const chunks = [];\n            doc.on('data', (chunk)=>chunks.push(chunk));\n            // Add title\n            doc.fontSize(20).text('Liste des Clients', {\n                align: 'center'\n            });\n            doc.moveDown();\n            // Add export date\n            doc.fontSize(12).text(`Exporté le: ${new Date().toLocaleDateString('fr-FR')}`, {\n                align: 'right'\n            });\n            doc.moveDown();\n            // Add filter information if any\n            if (search || city || company) {\n                doc.fontSize(12).text('Filtres appliqués:');\n                if (search) doc.text(`Recherche: ${search}`);\n                if (city) doc.text(`Ville: ${city}`);\n                if (company) doc.text(`Entreprise: ${company}`);\n                doc.moveDown();\n            }\n            // Add table headers\n            const tableTop = doc.y;\n            const itemHeight = 20;\n            doc.fontSize(10);\n            doc.text('Nom', 50, tableTop, {\n                width: 80\n            });\n            doc.text('Email', 130, tableTop, {\n                width: 120\n            });\n            doc.text('Téléphone', 250, tableTop, {\n                width: 80\n            });\n            doc.text('Entreprise', 330, tableTop, {\n                width: 100\n            });\n            doc.text('Ville', 430, tableTop, {\n                width: 80\n            });\n            doc.text('Date', 510, tableTop, {\n                width: 60\n            });\n            // Add line under headers\n            doc.moveTo(50, tableTop + 15).lineTo(570, tableTop + 15).stroke();\n            // Add data rows\n            let currentY = tableTop + itemHeight;\n            clients.forEach((client, index)=>{\n                if (currentY > 750) {\n                    doc.addPage();\n                    currentY = 50;\n                }\n                const fullName = `${client.user.firstname} ${client.user.lastname}`;\n                doc.text(fullName, 50, currentY, {\n                    width: 80\n                });\n                doc.text(client.user.email, 130, currentY, {\n                    width: 120\n                });\n                doc.text(client.user.telephone || '', 250, currentY, {\n                    width: 80\n                });\n                doc.text(client.company || '', 330, currentY, {\n                    width: 100\n                });\n                doc.text(client.city || '', 430, currentY, {\n                    width: 80\n                });\n                doc.text(new Date(client.user.createdAt).toLocaleDateString('fr-FR'), 510, currentY, {\n                    width: 60\n                });\n                currentY += itemHeight;\n            });\n            // Add footer\n            doc.fontSize(8).text(`Total: ${clients.length} clients`, 50, doc.page.height - 50, {\n                align: 'center'\n            });\n            doc.end();\n            return new Promise((resolve)=>{\n                doc.on('end', ()=>{\n                    const buffer = Buffer.concat(chunks);\n                    resolve(new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n                        headers: {\n                            'Content-Type': 'application/pdf',\n                            'Content-Disposition': 'attachment; filename=clients.pdf'\n                        }\n                    }));\n                });\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Format non supporté'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error exporting clients:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Erreur lors de l\\'exportation'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/clients/export/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconsole.log('🔧 Auth options module loaded');\nconsole.log('🔧 Creating auth options...');\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                username: {\n                    label: \"Nom d'utilisateur\",\n                    type: 'text'\n                },\n                password: {\n                    label: 'Mot de passe',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                console.log('🔍 AUTHORIZE FUNCTION CALLED!');\n                console.log('🔍 Auth attempt:', {\n                    username: credentials?.username,\n                    hasPassword: !!credentials?.password\n                });\n                if (!credentials?.username || !credentials?.password) {\n                    console.log('❌ Missing credentials');\n                    return null;\n                }\n                try {\n                    // Rechercher l'utilisateur par nom d'utilisateur\n                    console.log('🔍 Looking for user:', credentials.username);\n                    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.findUserByUsername)(credentials.username);\n                    // Vérifier si l'utilisateur existe\n                    if (!user) {\n                        console.log('❌ User not found:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ User found:', {\n                        id: user.id,\n                        username: user.username,\n                        role: user.role\n                    });\n                    // Vérifier le mot de passe\n                    console.log('🔍 Verifying password...');\n                    const isPasswordValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log('❌ Invalid password for user:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ Password valid for user:', credentials.username);\n                    // Retourner les données de l'utilisateur sans le mot de passe\n                    const userObject = {\n                        id: user.id,\n                        email: user.email,\n                        username: user.username,\n                        name: `${user.firstname} ${user.lastname}`,\n                        firstname: user.firstname,\n                        lastname: user.lastname,\n                        role: user.role,\n                        clientId: user.client?.id,\n                        commercialId: user.commercial?.id,\n                        adminId: user.admin?.id\n                    };\n                    console.log('✅ Returning user object:', userObject);\n                    return userObject;\n                } catch (error) {\n                    console.error('❌ Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.username = user.username;\n                token.role = user.role;\n                token.firstname = user.firstname;\n                token.lastname = user.lastname;\n                token.clientId = user.clientId;\n                token.commercialId = user.commercialId;\n                token.adminId = user.adminId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.username = token.username;\n                session.user.role = token.role;\n                session.user.firstname = token.firstname;\n                session.user.lastname = token.lastname;\n                session.user.clientId = token.clientId;\n                session.user.commercialId = token.commercialId;\n                session.user.adminId = token.adminId;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signOut: '/auth/signout',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   createClientUser: () => (/* binding */ createClientUser),\n/* harmony export */   createCommercialUser: () => (/* binding */ createCommercialUser),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Fonction pour hacher un mot de passe\nasync function hashPassword(password) {\n    const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].genSalt(10);\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, salt);\n}\n// Fonction pour vérifier un mot de passe\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Fonction pour créer un utilisateur client\nasync function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.CLIENT\n            }\n        });\n        // Créer le profil client associé\n        const client = await tx.client.create({\n            data: {\n                userId: user.id,\n                company_name\n            }\n        });\n        return {\n            user,\n            client\n        };\n    });\n}\n// Fonction pour créer un utilisateur commercial\nasync function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.COMMERCIAL\n            }\n        });\n        // Créer le profil commercial associé\n        const commercial = await tx.commercial.create({\n            data: {\n                userId: user.id,\n                profile_photo\n            }\n        });\n        return {\n            user,\n            commercial\n        };\n    });\n}\n// Fonction pour créer un utilisateur administrateur\nasync function createAdminUser({ email, username, password, lastname, firstname, telephone }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN\n            }\n        });\n        // Créer le profil admin associé\n        const admin = await tx.admin.create({\n            data: {\n                userId: user.id\n            }\n        });\n        return {\n            user,\n            admin\n        };\n    });\n}\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nasync function findUserByUsername(username) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            username\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n// Fonction pour trouver un utilisateur par son email\nasync function findUserByEmail(email) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            email\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDSTtBQUNRO0FBRTFDLHVDQUF1QztBQUNoQyxlQUFlRyxhQUFhQyxRQUFnQjtJQUNqRCxNQUFNQyxPQUFPLE1BQU1MLHdEQUFjLENBQUM7SUFDbEMsT0FBT0EscURBQVcsQ0FBQ0ksVUFBVUM7QUFDL0I7QUFFQSx5Q0FBeUM7QUFDbEMsZUFBZUcsZUFDcEJKLFFBQWdCLEVBQ2hCSyxjQUFzQjtJQUV0QixPQUFPVCx3REFBYyxDQUFDSSxVQUFVSztBQUNsQztBQUVBLDRDQUE0QztBQUNyQyxlQUFlRSxpQkFBaUIsRUFDckNDLEtBQUssRUFDTEMsUUFBUSxFQUNSVCxRQUFRLEVBQ1JVLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxTQUFTLEVBQ1RDLFlBQVksRUFTYjtJQUNDLE1BQU1SLGlCQUFpQixNQUFNTixhQUFhQztJQUUxQyxPQUFPSCwyQ0FBTUEsQ0FBQ2lCLFlBQVksQ0FBQyxPQUFPQztRQUNoQyw4QkFBOEI7UUFDOUIsTUFBTUMsT0FBTyxNQUFNRCxHQUFHQyxJQUFJLENBQUNDLE1BQU0sQ0FBQztZQUNoQ0MsTUFBTTtnQkFDSlY7Z0JBQ0FDO2dCQUNBVCxVQUFVSztnQkFDVks7Z0JBQ0FDO2dCQUNBQztnQkFDQU8sTUFBTXJCLG9EQUFRQSxDQUFDc0IsTUFBTTtZQUN2QjtRQUNGO1FBRUEsaUNBQWlDO1FBQ2pDLE1BQU1DLFNBQVMsTUFBTU4sR0FBR00sTUFBTSxDQUFDSixNQUFNLENBQUM7WUFDcENDLE1BQU07Z0JBQ0pJLFFBQVFOLEtBQUtPLEVBQUU7Z0JBQ2ZWO1lBQ0Y7UUFDRjtRQUVBLE9BQU87WUFBRUc7WUFBTUs7UUFBTztJQUN4QjtBQUNGO0FBRUEsZ0RBQWdEO0FBQ3pDLGVBQWVHLHFCQUFxQixFQUN6Q2hCLEtBQUssRUFDTEMsUUFBUSxFQUNSVCxRQUFRLEVBQ1JVLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxTQUFTLEVBQ1RhLGFBQWEsRUFTZDtJQUNDLE1BQU1wQixpQkFBaUIsTUFBTU4sYUFBYUM7SUFFMUMsT0FBT0gsMkNBQU1BLENBQUNpQixZQUFZLENBQUMsT0FBT0M7UUFDaEMsOEJBQThCO1FBQzlCLE1BQU1DLE9BQU8sTUFBTUQsR0FBR0MsSUFBSSxDQUFDQyxNQUFNLENBQUM7WUFDaENDLE1BQU07Z0JBQ0pWO2dCQUNBQztnQkFDQVQsVUFBVUs7Z0JBQ1ZLO2dCQUNBQztnQkFDQUM7Z0JBQ0FPLE1BQU1yQixvREFBUUEsQ0FBQzRCLFVBQVU7WUFDM0I7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNQyxhQUFhLE1BQU1aLEdBQUdZLFVBQVUsQ0FBQ1YsTUFBTSxDQUFDO1lBQzVDQyxNQUFNO2dCQUNKSSxRQUFRTixLQUFLTyxFQUFFO2dCQUNmRTtZQUNGO1FBQ0Y7UUFFQSxPQUFPO1lBQUVUO1lBQU1XO1FBQVc7SUFDNUI7QUFDRjtBQUVBLG9EQUFvRDtBQUM3QyxlQUFlQyxnQkFBZ0IsRUFDcENwQixLQUFLLEVBQ0xDLFFBQVEsRUFDUlQsUUFBUSxFQUNSVSxRQUFRLEVBQ1JDLFNBQVMsRUFDVEMsU0FBUyxFQVFWO0lBQ0MsTUFBTVAsaUJBQWlCLE1BQU1OLGFBQWFDO0lBRTFDLE9BQU9ILDJDQUFNQSxDQUFDaUIsWUFBWSxDQUFDLE9BQU9DO1FBQ2hDLDhCQUE4QjtRQUM5QixNQUFNQyxPQUFPLE1BQU1ELEdBQUdDLElBQUksQ0FBQ0MsTUFBTSxDQUFDO1lBQ2hDQyxNQUFNO2dCQUNKVjtnQkFDQUM7Z0JBQ0FULFVBQVVLO2dCQUNWSztnQkFDQUM7Z0JBQ0FDO2dCQUNBTyxNQUFNckIsb0RBQVFBLENBQUMrQixLQUFLO1lBQ3RCO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTUMsUUFBUSxNQUFNZixHQUFHZSxLQUFLLENBQUNiLE1BQU0sQ0FBQztZQUNsQ0MsTUFBTTtnQkFDSkksUUFBUU4sS0FBS08sRUFBRTtZQUNqQjtRQUNGO1FBRUEsT0FBTztZQUFFUDtZQUFNYztRQUFNO0lBQ3ZCO0FBQ0Y7QUFFQSxpRUFBaUU7QUFDMUQsZUFBZUMsbUJBQW1CdEIsUUFBZ0I7SUFDdkQsT0FBT1osMkNBQU1BLENBQUNtQixJQUFJLENBQUNnQixVQUFVLENBQUM7UUFDNUJDLE9BQU87WUFBRXhCO1FBQVM7UUFDbEJ5QixTQUFTO1lBQ1BiLFFBQVE7WUFDUk0sWUFBWTtZQUNaRyxPQUFPO1FBQ1Q7SUFDRjtBQUNGO0FBRUEscURBQXFEO0FBQzlDLGVBQWVLLGdCQUFnQjNCLEtBQWE7SUFDakQsT0FBT1gsMkNBQU1BLENBQUNtQixJQUFJLENBQUNnQixVQUFVLENBQUM7UUFDNUJDLE9BQU87WUFBRXpCO1FBQU07UUFDZjBCLFNBQVM7WUFDUGIsUUFBUTtZQUNSTSxZQUFZO1lBQ1pHLE9BQU87UUFDVDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnO1xuaW1wb3J0IHsgVXNlclJvbGUgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbi8vIEZvbmN0aW9uIHBvdXIgaGFjaGVyIHVuIG1vdCBkZSBwYXNzZVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhc2hQYXNzd29yZChwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgY29uc3Qgc2FsdCA9IGF3YWl0IGJjcnlwdC5nZW5TYWx0KDEwKTtcbiAgcmV0dXJuIGJjcnlwdC5oYXNoKHBhc3N3b3JkLCBzYWx0KTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciB2w6lyaWZpZXIgdW4gbW90IGRlIHBhc3NlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5UGFzc3dvcmQoXG4gIHBhc3N3b3JkOiBzdHJpbmcsXG4gIGhhc2hlZFBhc3N3b3JkOiBzdHJpbmdcbik6IFByb21pc2U8Ym9vbGVhbj4ge1xuICByZXR1cm4gYmNyeXB0LmNvbXBhcmUocGFzc3dvcmQsIGhhc2hlZFBhc3N3b3JkKTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciBjcsOpZXIgdW4gdXRpbGlzYXRldXIgY2xpZW50XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQ2xpZW50VXNlcih7XG4gIGVtYWlsLFxuICB1c2VybmFtZSxcbiAgcGFzc3dvcmQsXG4gIGxhc3RuYW1lLFxuICBmaXJzdG5hbWUsXG4gIHRlbGVwaG9uZSxcbiAgY29tcGFueV9uYW1lLFxufToge1xuICBlbWFpbDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBsYXN0bmFtZTogc3RyaW5nO1xuICBmaXJzdG5hbWU6IHN0cmluZztcbiAgdGVsZXBob25lPzogc3RyaW5nO1xuICBjb21wYW55X25hbWU/OiBzdHJpbmc7XG59KSB7XG4gIGNvbnN0IGhhc2hlZFBhc3N3b3JkID0gYXdhaXQgaGFzaFBhc3N3b3JkKHBhc3N3b3JkKTtcblxuICByZXR1cm4gcHJpc21hLiR0cmFuc2FjdGlvbihhc3luYyAodHgpID0+IHtcbiAgICAvLyBDcsOpZXIgbCd1dGlsaXNhdGV1ciBkZSBiYXNlXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IHR4LnVzZXIuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgZW1haWwsXG4gICAgICAgIHVzZXJuYW1lLFxuICAgICAgICBwYXNzd29yZDogaGFzaGVkUGFzc3dvcmQsXG4gICAgICAgIGxhc3RuYW1lLFxuICAgICAgICBmaXJzdG5hbWUsXG4gICAgICAgIHRlbGVwaG9uZSxcbiAgICAgICAgcm9sZTogVXNlclJvbGUuQ0xJRU5ULFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIENyw6llciBsZSBwcm9maWwgY2xpZW50IGFzc29jacOpXG4gICAgY29uc3QgY2xpZW50ID0gYXdhaXQgdHguY2xpZW50LmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgICAgY29tcGFueV9uYW1lLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIHJldHVybiB7IHVzZXIsIGNsaWVudCB9O1xuICB9KTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciBjcsOpZXIgdW4gdXRpbGlzYXRldXIgY29tbWVyY2lhbFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNvbW1lcmNpYWxVc2VyKHtcbiAgZW1haWwsXG4gIHVzZXJuYW1lLFxuICBwYXNzd29yZCxcbiAgbGFzdG5hbWUsXG4gIGZpcnN0bmFtZSxcbiAgdGVsZXBob25lLFxuICBwcm9maWxlX3Bob3RvLFxufToge1xuICBlbWFpbDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBsYXN0bmFtZTogc3RyaW5nO1xuICBmaXJzdG5hbWU6IHN0cmluZztcbiAgdGVsZXBob25lPzogc3RyaW5nO1xuICBwcm9maWxlX3Bob3RvPzogc3RyaW5nO1xufSkge1xuICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZCk7XG5cbiAgcmV0dXJuIHByaXNtYS4kdHJhbnNhY3Rpb24oYXN5bmMgKHR4KSA9PiB7XG4gICAgLy8gQ3LDqWVyIGwndXRpbGlzYXRldXIgZGUgYmFzZVxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCB0eC51c2VyLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICB1c2VybmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6IGhhc2hlZFBhc3N3b3JkLFxuICAgICAgICBsYXN0bmFtZSxcbiAgICAgICAgZmlyc3RuYW1lLFxuICAgICAgICB0ZWxlcGhvbmUsXG4gICAgICAgIHJvbGU6IFVzZXJSb2xlLkNPTU1FUkNJQUwsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgLy8gQ3LDqWVyIGxlIHByb2ZpbCBjb21tZXJjaWFsIGFzc29jacOpXG4gICAgY29uc3QgY29tbWVyY2lhbCA9IGF3YWl0IHR4LmNvbW1lcmNpYWwuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgICBwcm9maWxlX3Bob3RvLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIHJldHVybiB7IHVzZXIsIGNvbW1lcmNpYWwgfTtcbiAgfSk7XG59XG5cbi8vIEZvbmN0aW9uIHBvdXIgY3LDqWVyIHVuIHV0aWxpc2F0ZXVyIGFkbWluaXN0cmF0ZXVyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQWRtaW5Vc2VyKHtcbiAgZW1haWwsXG4gIHVzZXJuYW1lLFxuICBwYXNzd29yZCxcbiAgbGFzdG5hbWUsXG4gIGZpcnN0bmFtZSxcbiAgdGVsZXBob25lLFxufToge1xuICBlbWFpbDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBsYXN0bmFtZTogc3RyaW5nO1xuICBmaXJzdG5hbWU6IHN0cmluZztcbiAgdGVsZXBob25lPzogc3RyaW5nO1xufSkge1xuICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZCk7XG5cbiAgcmV0dXJuIHByaXNtYS4kdHJhbnNhY3Rpb24oYXN5bmMgKHR4KSA9PiB7XG4gICAgLy8gQ3LDqWVyIGwndXRpbGlzYXRldXIgZGUgYmFzZVxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCB0eC51c2VyLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICB1c2VybmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6IGhhc2hlZFBhc3N3b3JkLFxuICAgICAgICBsYXN0bmFtZSxcbiAgICAgICAgZmlyc3RuYW1lLFxuICAgICAgICB0ZWxlcGhvbmUsXG4gICAgICAgIHJvbGU6IFVzZXJSb2xlLkFETUlOLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIENyw6llciBsZSBwcm9maWwgYWRtaW4gYXNzb2Npw6lcbiAgICBjb25zdCBhZG1pbiA9IGF3YWl0IHR4LmFkbWluLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICByZXR1cm4geyB1c2VyLCBhZG1pbiB9O1xuICB9KTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciB0cm91dmVyIHVuIHV0aWxpc2F0ZXVyIHBhciBzb24gbm9tIGQndXRpbGlzYXRldXJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBmaW5kVXNlckJ5VXNlcm5hbWUodXNlcm5hbWU6IHN0cmluZykge1xuICByZXR1cm4gcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgd2hlcmU6IHsgdXNlcm5hbWUgfSxcbiAgICBpbmNsdWRlOiB7XG4gICAgICBjbGllbnQ6IHRydWUsXG4gICAgICBjb21tZXJjaWFsOiB0cnVlLFxuICAgICAgYWRtaW46IHRydWUsXG4gICAgfSxcbiAgfSk7XG59XG5cbi8vIEZvbmN0aW9uIHBvdXIgdHJvdXZlciB1biB1dGlsaXNhdGV1ciBwYXIgc29uIGVtYWlsXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZmluZFVzZXJCeUVtYWlsKGVtYWlsOiBzdHJpbmcpIHtcbiAgcmV0dXJuIHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgIHdoZXJlOiB7IGVtYWlsIH0sXG4gICAgaW5jbHVkZToge1xuICAgICAgY2xpZW50OiB0cnVlLFxuICAgICAgY29tbWVyY2lhbDogdHJ1ZSxcbiAgICAgIGFkbWluOiB0cnVlLFxuICAgIH0sXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbImJjcnlwdCIsInByaXNtYSIsIlVzZXJSb2xlIiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJzYWx0IiwiZ2VuU2FsdCIsImhhc2giLCJ2ZXJpZnlQYXNzd29yZCIsImhhc2hlZFBhc3N3b3JkIiwiY29tcGFyZSIsImNyZWF0ZUNsaWVudFVzZXIiLCJlbWFpbCIsInVzZXJuYW1lIiwibGFzdG5hbWUiLCJmaXJzdG5hbWUiLCJ0ZWxlcGhvbmUiLCJjb21wYW55X25hbWUiLCIkdHJhbnNhY3Rpb24iLCJ0eCIsInVzZXIiLCJjcmVhdGUiLCJkYXRhIiwicm9sZSIsIkNMSUVOVCIsImNsaWVudCIsInVzZXJJZCIsImlkIiwiY3JlYXRlQ29tbWVyY2lhbFVzZXIiLCJwcm9maWxlX3Bob3RvIiwiQ09NTUVSQ0lBTCIsImNvbW1lcmNpYWwiLCJjcmVhdGVBZG1pblVzZXIiLCJBRE1JTiIsImFkbWluIiwiZmluZFVzZXJCeVVzZXJuYW1lIiwiZmluZFVuaXF1ZSIsIndoZXJlIiwiaW5jbHVkZSIsImZpbmRVc2VyQnlFbWFpbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/crypto-js","vendor-chunks/restructure","vendor-chunks/brotli","vendor-chunks/unicode-trie","vendor-chunks/linebreak","vendor-chunks/jpeg-exif","vendor-chunks/xlsx","vendor-chunks/unicode-properties","vendor-chunks/tslib","vendor-chunks/fontkit","vendor-chunks/tiny-inflate","vendor-chunks/png-js","vendor-chunks/pdfkit","vendor-chunks/fast-deep-equal","vendor-chunks/dfa","vendor-chunks/clone","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();