import { NextRequest, NextResponse } from 'next/server';
import { getCommercials, createCommercial } from '@/lib/commercials';
import { findUserByEmail, findUserByUsername } from '@/lib/auth';

// GET /api/commercials - Récupérer tous les commerciaux
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const search = searchParams.get('search') || undefined;
    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;
    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;

    const { commercials, total } = await getCommercials({
      search,
      skip,
      take,
    });

    return NextResponse.json({ commercials, total });
  } catch (error: any) {
    console.error('Error fetching commercials:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch commercials' },
      { status: 500 }
    );
  }
}

// POST /api/commercials - Créer un nouveau commercial
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      email,
      username,
      password,
      firstname,
      lastname,
      telephone,
      profile_photo,
    } = body;

    // Vérifier que tous les champs requis sont présents
    if (!email || !username || !password || !firstname || !lastname) {
      return NextResponse.json(
        { error: 'Email, username, password, firstname, and lastname are required' },
        { status: 400 }
      );
    }

    // Vérifier si l'email est déjà utilisé
    const existingEmail = await findUserByEmail(email);
    if (existingEmail) {
      return NextResponse.json(
        { error: 'Email is already in use' },
        { status: 400 }
      );
    }

    // Vérifier si le nom d'utilisateur est déjà utilisé
    const existingUsername = await findUserByUsername(username);
    if (existingUsername) {
      return NextResponse.json(
        { error: 'Username is already in use' },
        { status: 400 }
      );
    }

    // Créer le commercial
    const result = await createCommercial({
      email,
      username,
      password,
      firstname,
      lastname,
      telephone,
      profile_photo,
    });

    // Retourner une réponse de succès sans le mot de passe
    return NextResponse.json(
      {
        commercial: {
          id: result.commercial.id,
          user: {
            id: result.user.id,
            email: result.user.email,
            username: result.user.username,
            firstname: result.user.firstname,
            lastname: result.user.lastname,
            telephone: result.user.telephone,
            role: result.user.role,
          },
          profile_photo: result.commercial.profile_photo,
        },
      },
      { status: 201 }
    );
  } catch (error: any) {
    console.error('Error creating commercial:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create commercial' },
      { status: 500 }
    );
  }
}
