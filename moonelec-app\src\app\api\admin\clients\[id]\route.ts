import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import bcrypt from 'bcryptjs';

// GET /api/admin/clients/[id] - Get a specific client
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const client = await prisma.client.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true,
            telephone: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    if (!client) {
      return NextResponse.json(
        { message: 'Client non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json({ client });
  } catch (error) {
    console.error('Error fetching client:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/clients/[id] - Update a client
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const {
      firstname,
      lastname,
      email,
      telephone,
      password,
      company,
      address,
      city,
      postalCode,
      country,
    } = body;

    // Check if client exists
    const existingClient = await prisma.client.findUnique({
      where: { id: params.id },
      include: { user: true },
    });

    if (!existingClient) {
      return NextResponse.json(
        { message: 'Client non trouvé' },
        { status: 404 }
      );
    }

    // Check if email is already taken by another user
    if (email && email !== existingClient.user.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email },
      });

      if (emailExists) {
        return NextResponse.json(
          { message: 'Cet email est déjà utilisé par un autre utilisateur' },
          { status: 400 }
        );
      }
    }

    // Prepare user update data
    const userUpdateData: any = {};
    if (firstname) userUpdateData.firstname = firstname;
    if (lastname) userUpdateData.lastname = lastname;
    if (email) userUpdateData.email = email;
    if (telephone !== undefined) userUpdateData.telephone = telephone || null;

    // Hash new password if provided
    if (password) {
      userUpdateData.password = await bcrypt.hash(password, 12);
    }

    // Prepare client update data
    const clientUpdateData: any = {};
    if (company !== undefined) clientUpdateData.company = company || null;
    if (address !== undefined) clientUpdateData.address = address || null;
    if (city !== undefined) clientUpdateData.city = city || null;
    if (postalCode !== undefined) clientUpdateData.postalCode = postalCode || null;
    if (country) clientUpdateData.country = country;

    // Update user and client in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update user if there are changes
      if (Object.keys(userUpdateData).length > 0) {
        await tx.user.update({
          where: { id: existingClient.userId },
          data: userUpdateData,
        });
      }

      // Update client if there are changes
      if (Object.keys(clientUpdateData).length > 0) {
        await tx.client.update({
          where: { id: params.id },
          data: clientUpdateData,
        });
      }

      // Return updated client
      return tx.client.findUnique({
        where: { id: params.id },
        include: {
          user: {
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
              telephone: true,
              createdAt: true,
              updatedAt: true,
            },
          },
        },
      });
    });

    return NextResponse.json({
      message: 'Client mis à jour avec succès',
      client: result,
    });
  } catch (error) {
    console.error('Error updating client:', error);
    return NextResponse.json(
      { message: 'Erreur lors de la mise à jour du client' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/clients/[id] - Delete a client
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Check if client exists
    const existingClient = await prisma.client.findUnique({
      where: { id: params.id },
      include: { user: true },
    });

    if (!existingClient) {
      return NextResponse.json(
        { message: 'Client non trouvé' },
        { status: 404 }
      );
    }

    // Delete client and user in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete client first (due to foreign key constraint)
      await tx.client.delete({
        where: { id: params.id },
      });

      // Delete user
      await tx.user.delete({
        where: { id: existingClient.userId },
      });
    });

    return NextResponse.json({
      message: 'Client supprimé avec succès',
    });
  } catch (error) {
    console.error('Error deleting client:', error);
    return NextResponse.json(
      { message: 'Erreur lors de la suppression du client' },
      { status: 500 }
    );
  }
}
