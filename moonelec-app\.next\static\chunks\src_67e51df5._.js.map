{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\n\nexport function useAuth() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading = status === 'loading';\n  const user = session?.user;\n\n  const isClient = isAuthenticated && user?.role === 'CLIENT';\n  const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';\n  const isAdmin = isAuthenticated && user?.role === 'ADMIN';\n\n  const login = async (username: string, password: string) => {\n    const result = await signIn('credentials', {\n      username,\n      password,\n      redirect: false,\n    });\n\n    return result;\n  };\n\n  const logout = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const redirectToLogin = () => {\n    router.push('/auth/signin');\n  };\n\n  const redirectToDashboard = () => {\n    if (isAdmin) {\n      router.push('/admin/quotes');\n    } else if (isCommercial) {\n      router.push('/commercial/quotes');\n    } else if (isClient) {\n      router.push('/account/quotes');\n    } else {\n      router.push('/');\n    }\n  };\n\n  return {\n    session,\n    status,\n    user,\n    isAuthenticated,\n    isLoading,\n    isClient,\n    isCommercial,\n    isAdmin,\n    login,\n    logout,\n    redirectToLogin,\n    redirectToDashboard,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,WAAW;IACnC,MAAM,YAAY,WAAW;IAC7B,MAAM,OAAO,SAAS;IAEtB,MAAM,WAAW,mBAAmB,MAAM,SAAS;IACnD,MAAM,eAAe,mBAAmB,MAAM,SAAS;IACvD,MAAM,UAAU,mBAAmB,MAAM,SAAS;IAElD,MAAM,QAAQ,OAAO,UAAkB;QACrC,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;YACzC;YACA;YACA,UAAU;QACZ;QAEA,OAAO;IACT;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,cAAc;YACvB,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,UAAU;YACnB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzDgB;;QACoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/cart/CartSlidePanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaTimes, FaTrash, FaPlus, FaMinus, FaFileAlt, FaShoppingCart } from 'react-icons/fa';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useCart } from '@/context/CartContext';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\n\ninterface CartSlidePanelProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function CartSlidePanel({ isOpen, onClose }: CartSlidePanelProps) {\n  const { items, updateItemQuantity, removeItem, notes, setNotes } = useCart();\n  const { isAuthenticated, redirectToLogin } = useAuth();\n  const router = useRouter();\n  const panelRef = useRef<HTMLDivElement>(null);\n\n  // Fermer le panneau lorsque l'utilisateur clique en dehors\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (panelRef.current && !panelRef.current.contains(event.target as Node) && isOpen) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    \n    // Désactiver le défilement du body lorsque le panneau est ouvert\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'auto';\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.body.style.overflow = 'auto';\n    };\n  }, [isOpen, onClose]);\n\n  // Gérer la demande de devis\n  const handleRequestQuote = () => {\n    if (!isAuthenticated) {\n      redirectToLogin();\n      return;\n    }\n    \n    onClose();\n    router.push('/cart');\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Overlay */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 0.5 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black z-50\"\n            onClick={onClose}\n          />\n\n          {/* Panel */}\n          <motion.div\n            ref={panelRef}\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 300 }}\n            className=\"fixed top-0 right-0 h-full w-full sm:w-96 bg-white dark:bg-gray-800 shadow-xl z-50 overflow-y-auto\"\n          >\n            {/* Header */}\n            <div className=\"sticky top-0 bg-white dark:bg-gray-800 z-10 border-b border-gray-200 dark:border-gray-700 p-4 flex justify-between items-center\">\n              <h2 className=\"text-xl font-bold text-gray-800 dark:text-white flex items-center\">\n                <FaShoppingCart className=\"mr-2\" />\n                Votre Panier ({items.length})\n              </h2>\n              <button\n                onClick={onClose}\n                className=\"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                aria-label=\"Fermer\"\n              >\n                <FaTimes className=\"text-gray-600 dark:text-gray-300\" />\n              </button>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-4\">\n              {items.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"flex justify-center mb-4\">\n                    <FaShoppingCart className=\"text-5xl text-gray-400\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-2\">\n                    Votre panier est vide\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n                    Parcourez notre catalogue pour ajouter des produits à votre panier.\n                  </p>\n                  <Link href=\"/products\">\n                    <button\n                      onClick={onClose}\n                      className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors\"\n                    >\n                      Découvrir nos produits\n                    </button>\n                  </Link>\n                </div>\n              ) : (\n                <>\n                  {/* Cart Items */}\n                  <div className=\"divide-y divide-gray-200 dark:divide-gray-700 mb-4\">\n                    {items.map((item) => (\n                      <div key={item.id} className=\"py-4 flex items-start\">\n                        {/* Product Image */}\n                        <div className=\"w-16 h-16 relative flex-shrink-0 mr-3\">\n                          {item.image ? (\n                            <Image\n                              src={item.image}\n                              alt={item.name}\n                              fill\n                              style={{ objectFit: 'contain' }}\n                              className=\"rounded-md\"\n                            />\n                          ) : (\n                            <div className=\"w-full h-full bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center\">\n                              <span className=\"text-gray-400 text-xs\">No image</span>\n                            </div>\n                          )}\n                        </div>\n\n                        {/* Product Info */}\n                        <div className=\"flex-grow\">\n                          <h3 className=\"text-sm font-medium text-gray-800 dark:text-white mb-1 line-clamp-1\">\n                            {item.name}\n                          </h3>\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">\n                            Réf: {item.reference}\n                          </p>\n\n                          {/* Quantity Controls */}\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"flex items-center\">\n                              <button\n                                onClick={() => updateItemQuantity(item.id, item.quantity - 1)}\n                                className=\"p-1 bg-gray-100 dark:bg-gray-700 rounded-l-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                                aria-label=\"Diminuer la quantité\"\n                              >\n                                <FaMinus className=\"text-gray-600 dark:text-gray-300 text-xs\" />\n                              </button>\n                              <span className=\"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-xs font-medium\">\n                                {item.quantity}\n                              </span>\n                              <button\n                                onClick={() => updateItemQuantity(item.id, item.quantity + 1)}\n                                className=\"p-1 bg-gray-100 dark:bg-gray-700 rounded-r-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                                aria-label=\"Augmenter la quantité\"\n                              >\n                                <FaPlus className=\"text-gray-600 dark:text-gray-300 text-xs\" />\n                              </button>\n                            </div>\n\n                            <button\n                              onClick={() => removeItem(item.id)}\n                              className=\"p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors\"\n                              aria-label=\"Supprimer l'article\"\n                            >\n                              <FaTrash className=\"text-xs\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Notes */}\n                  <div className=\"mb-4\">\n                    <label htmlFor=\"cart-notes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Notes ou instructions spéciales\n                    </label>\n                    <textarea\n                      id=\"cart-notes\"\n                      rows={3}\n                      value={notes}\n                      onChange={(e) => setNotes(e.target.value)}\n                      className=\"w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                      placeholder=\"Ajoutez des notes ou des instructions spéciales pour votre devis...\"\n                    ></textarea>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"space-y-3\">\n                    <button\n                      onClick={handleRequestQuote}\n                      className=\"w-full py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center\"\n                    >\n                      <FaFileAlt className=\"mr-2\" />\n                      Demander un devis\n                    </button>\n                    \n                    <Link href=\"/cart\" className=\"block w-full\">\n                      <button\n                        onClick={onClose}\n                        className=\"w-full py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                      >\n                        Voir le panier complet\n                      </button>\n                    </Link>\n                  </div>\n                </>\n              )}\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAgBe,SAAS,eAAe,EAAE,MAAM,EAAE,OAAO,EAAuB;;IAC7E,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACzE,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;+DAAqB,CAAC;oBAC1B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAa,QAAQ;wBAClF;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YAEvC,iEAAiE;YACjE,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;4CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;mCAAG;QAAC;QAAQ;KAAQ;IAEpB,4BAA4B;IAC5B,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB;YACpB;YACA;QACF;QAEA;QACA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAI;oBACxB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,iJAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;wCAAS;wCACpB,MAAM,MAAM;wCAAC;;;;;;;8CAE9B,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,iJAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;qDAML;;kDAEE,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gDAAkB,WAAU;;kEAE3B,6LAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK,iBACT,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,KAAK,KAAK;4DACf,KAAK,KAAK,IAAI;4DACd,IAAI;4DACJ,OAAO;gEAAE,WAAW;4DAAU;4DAC9B,WAAU;;;;;iFAGZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;kEAM9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,KAAK,IAAI;;;;;;0EAEZ,6LAAC;gEAAE,WAAU;;oEAAgD;oEACrD,KAAK,SAAS;;;;;;;0EAItB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,SAAS,IAAM,mBAAmB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;gFAC3D,WAAU;gFACV,cAAW;0FAEX,cAAA,6LAAC,iJAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;;;;;;0FAErB,6LAAC;gFAAK,WAAU;0FACb,KAAK,QAAQ;;;;;;0FAEhB,6LAAC;gFACC,SAAS,IAAM,mBAAmB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;gFAC3D,WAAU;gFACV,cAAW;0FAEX,cAAA,6LAAC,iJAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;;;;;;;;;;;;kFAItB,6LAAC;wEACC,SAAS,IAAM,WAAW,KAAK,EAAE;wEACjC,WAAU;wEACV,cAAW;kFAEX,cAAA,6LAAC,iJAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;+CAtDjB,KAAK,EAAE;;;;;;;;;;kDA+DrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAAkE;;;;;;0DAGxG,6LAAC;gDACC,IAAG;gDACH,MAAM;gDACN,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,iJAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAS;;;;;;;0DAIhC,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAC3B,cAAA,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavB;GAhNwB;;QAC6C,iIAAA,CAAA,UAAO;QAC7B,0HAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/serviceWorker.ts"], "sourcesContent": ["// Fonction pour enregistrer le service worker\nexport function registerServiceWorker() {\n  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {\n    window.addEventListener('load', () => {\n      navigator.serviceWorker\n        .register('/sw.js')\n        .then((registration) => {\n          console.log('Service Worker enregistré avec succès:', registration);\n        })\n        .catch((error) => {\n          console.error('Erreur lors de l\\'enregistrement du Service Worker:', error);\n        });\n    });\n  }\n}\n\n// Fonction pour demander la permission de notification\nexport async function requestNotificationPermission() {\n  if (typeof window !== 'undefined' && 'Notification' in window) {\n    if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return Notification.permission === 'granted';\n  }\n  return false;\n}\n\n// Fonction pour s'abonner aux notifications push\nexport async function subscribeToPushNotifications() {\n  if (typeof window !== 'undefined' && 'serviceWorker' in navigator && 'PushManager' in window) {\n    try {\n      const registration = await navigator.serviceWorker.ready;\n      \n      // Vérifier si l'utilisateur est déjà abonné\n      let subscription = await registration.pushManager.getSubscription();\n      \n      if (!subscription) {\n        // Créer un nouvel abonnement\n        // Note: Dans une application réelle, vous devriez générer une clé VAPID côté serveur\n        // et l'utiliser ici. Pour cet exemple, nous utilisons une clé fictive.\n        const vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U';\n        const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);\n        \n        subscription = await registration.pushManager.subscribe({\n          userVisibleOnly: true,\n          applicationServerKey: convertedVapidKey\n        });\n        \n        // Dans une application réelle, vous enverriez cet abonnement à votre serveur\n        console.log('Nouvel abonnement push créé:', subscription);\n      } else {\n        console.log('Utilisateur déjà abonné aux notifications push');\n      }\n      \n      return subscription;\n    } catch (error) {\n      console.error('Erreur lors de l\\'abonnement aux notifications push:', error);\n      return null;\n    }\n  }\n  return null;\n}\n\n// Fonction utilitaire pour convertir une clé VAPID en Uint8Array\nfunction urlBase64ToUint8Array(base64String: string) {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');\n  \n  const rawData = window.atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n  \n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  \n  return outputArray;\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AACvC,SAAS;IACd,IAAI,aAAkB,eAAe,mBAAmB,WAAW;QACjE,OAAO,gBAAgB,CAAC,QAAQ;YAC9B,UAAU,aAAa,CACpB,QAAQ,CAAC,UACT,IAAI,CAAC,CAAC;gBACL,QAAQ,GAAG,CAAC,0CAA0C;YACxD,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,uDAAuD;YACvE;QACJ;IACF;AACF;AAGO,eAAe;IACpB,IAAI,aAAkB,eAAe,kBAAkB,QAAQ;QAC7D,IAAI,aAAa,UAAU,KAAK,aAAa,aAAa,UAAU,KAAK,UAAU;YACjF,MAAM,aAAa,MAAM,aAAa,iBAAiB;YACvD,OAAO,eAAe;QACxB;QACA,OAAO,aAAa,UAAU,KAAK;IACrC;IACA,OAAO;AACT;AAGO,eAAe;IACpB,IAAI,aAAkB,eAAe,mBAAmB,aAAa,iBAAiB,QAAQ;QAC5F,IAAI;YACF,MAAM,eAAe,MAAM,UAAU,aAAa,CAAC,KAAK;YAExD,4CAA4C;YAC5C,IAAI,eAAe,MAAM,aAAa,WAAW,CAAC,eAAe;YAEjE,IAAI,CAAC,cAAc;gBACjB,6BAA6B;gBAC7B,qFAAqF;gBACrF,uEAAuE;gBACvE,MAAM,iBAAiB;gBACvB,MAAM,oBAAoB,sBAAsB;gBAEhD,eAAe,MAAM,aAAa,WAAW,CAAC,SAAS,CAAC;oBACtD,iBAAiB;oBACjB,sBAAsB;gBACxB;gBAEA,6EAA6E;gBAC7E,QAAQ,GAAG,CAAC,gCAAgC;YAC9C,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wDAAwD;YACtE,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,iEAAiE;AACjE,SAAS,sBAAsB,YAAoB;IACjD,MAAM,UAAU,IAAI,MAAM,CAAC,CAAC,IAAK,aAAa,MAAM,GAAG,CAAE,IAAI;IAC7D,MAAM,SAAS,CAAC,eAAe,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;IAEzE,MAAM,UAAU,OAAO,IAAI,CAAC;IAC5B,MAAM,cAAc,IAAI,WAAW,QAAQ,MAAM;IAEjD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;QACvC,WAAW,CAAC,EAAE,GAAG,QAAQ,UAAU,CAAC;IACtC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/notifications/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport { FaBell } from 'react-icons/fa';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '@/hooks/useAuth';\nimport { formatDistanceToNow } from 'date-fns';\nimport { fr } from 'date-fns/locale';\nimport { registerServiceWorker, requestNotificationPermission, subscribeToPushNotifications } from '@/lib/serviceWorker';\n\ninterface Notification {\n  id: string;\n  type: string;\n  message: string;\n  isRead: boolean;\n  createdAt: string;\n  quoteId?: string;\n  quote?: {\n    quoteNumber: string;\n  };\n}\n\nexport default function NotificationBell() {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // <PERSON>rmer le dropdown quand on clique en dehors\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  // Récupérer les notifications\n  const fetchNotifications = useCallback(async () => {\n    if (!user || user.role !== 'ADMIN') return;\n\n    try {\n      setIsLoading(true);\n      const response = await fetch('/api/notifications');\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch notifications');\n      }\n\n      const data = await response.json();\n      setNotifications(data.notifications || []);\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [user, setNotifications, setIsLoading]);\n\n  // Récupérer les notifications au chargement et toutes les 30 secondes\n  useEffect(() => {\n    if (user && user.role === 'ADMIN') {\n      fetchNotifications();\n\n      const interval = setInterval(fetchNotifications, 30000);\n\n      return () => clearInterval(interval);\n    }\n  }, [user, fetchNotifications]);\n\n  // Marquer une notification comme lue\n  const markAsRead = async (notificationId: string) => {\n    try {\n      const response = await fetch('/api/notifications', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ notificationId }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to mark notification as read');\n      }\n\n      // Mettre à jour l'état local\n      setNotifications(notifications.filter(n => n.id !== notificationId));\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  // Marquer toutes les notifications comme lues\n  const markAllAsRead = async () => {\n    try {\n      const response = await fetch('/api/notifications', {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ markAll: true }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to mark all notifications as read');\n      }\n\n      // Mettre à jour l'état local\n      setNotifications([]);\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n    }\n  };\n\n  // Afficher une notification dans le navigateur\n  const showBrowserNotification = useCallback((notification: Notification) => {\n    if (typeof window !== 'undefined' && 'Notification' in window) {\n      if (Notification.permission === 'granted') {\n        // Créer la notification avec plus d'options\n        const browserNotif = new Notification('Moonelec - Nouvelle demande de devis', {\n          body: notification.message,\n          icon: '/images/logo/logo-moonelec.png',\n          tag: notification.id, // Évite les doublons\n          requireInteraction: true, // La notification reste jusqu'à ce que l'utilisateur interagisse avec\n          vibrate: [200, 100, 200], // Vibration pour les appareils mobiles\n        });\n\n        // Ajouter un gestionnaire d'événements pour le clic sur la notification\n        browserNotif.onclick = () => {\n          // Mettre le focus sur la fenêtre et fermer la notification\n          window.focus();\n          browserNotif.close();\n\n          // Si la notification concerne un devis, rediriger vers la page du devis\n          if (notification.quoteId) {\n            window.location.href = `/admin/quotes/${notification.quoteId}`;\n          }\n        };\n      } else if (Notification.permission !== 'denied') {\n        // Demander la permission si elle n'a pas été refusée\n        Notification.requestPermission().then(permission => {\n          if (permission === 'granted') {\n            showBrowserNotification(notification);\n          }\n        });\n      }\n    }\n  }, []);\n\n  // Initialiser le service worker et demander les permissions de notification\n  useEffect(() => {\n    if (user && user.role === 'ADMIN') {\n      // Enregistrer le service worker\n      registerServiceWorker();\n\n      // Demander la permission pour les notifications\n      const setupNotifications = async () => {\n        const permissionGranted = await requestNotificationPermission();\n\n        if (permissionGranted) {\n          // S'abonner aux notifications push\n          await subscribeToPushNotifications();\n        }\n      };\n\n      setupNotifications();\n    }\n  }, [user]);\n\n  // Vérifier périodiquement les nouvelles notifications et afficher des notifications dans le navigateur\n  useEffect(() => {\n    // Fonction pour vérifier les nouvelles notifications\n    const checkForNewNotifications = async () => {\n      if (!user || user.role !== 'ADMIN') return;\n\n      try {\n        const prevNotificationsCount = notifications.length;\n        await fetchNotifications();\n\n        // Si de nouvelles notifications sont arrivées, afficher la plus récente\n        if (notifications.length > prevNotificationsCount && notifications.length > 0) {\n          showBrowserNotification(notifications[0]);\n        }\n      } catch (error) {\n        console.error('Error checking for new notifications:', error);\n      }\n    };\n\n    // Vérifier immédiatement au chargement\n    checkForNewNotifications();\n\n    // Configurer une vérification périodique (toutes les 30 secondes)\n    const interval = setInterval(checkForNewNotifications, 30000);\n\n    return () => clearInterval(interval);\n  }, [user, fetchNotifications, notifications.length, showBrowserNotification]);\n\n  // Afficher une notification dans le navigateur quand une nouvelle notification arrive\n  useEffect(() => {\n    if (notifications.length > 0) {\n      const latestNotification = notifications[0];\n      showBrowserNotification(latestNotification);\n    }\n  }, [notifications, showBrowserNotification]);\n\n  // Ne pas afficher le composant si l'utilisateur n'est pas un administrateur\n  if (!user || user.role !== 'ADMIN') {\n    return null;\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white focus:outline-none\"\n        aria-label=\"Notifications\"\n      >\n        <FaBell className=\"text-xl\" />\n        {notifications.length > 0 && (\n          <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full\">\n            {notifications.length}\n          </span>\n        )}\n      </button>\n\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n            className=\"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden\"\n          >\n            <div className=\"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center\">\n              <h3 className=\"text-sm font-semibold text-gray-700 dark:text-gray-200\">\n                Notifications\n              </h3>\n              {notifications.length > 0 && (\n                <button\n                  onClick={markAllAsRead}\n                  className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n                >\n                  Tout marquer comme lu\n                </button>\n              )}\n            </div>\n\n            <div className=\"max-h-96 overflow-y-auto\">\n              {isLoading ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  Chargement...\n                </div>\n              ) : notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                  Aucune notification\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className=\"p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\"\n                  >\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-800 dark:text-gray-200\">\n                          {notification.message}\n                        </p>\n                        {notification.quote && (\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                            Devis: {notification.quote.quoteNumber}\n                          </p>\n                        )}\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                          {formatDistanceToNow(new Date(notification.createdAt), {\n                            addSuffix: true,\n                            locale: fr,\n                          })}\n                        </p>\n                      </div>\n                      <button\n                        onClick={() => markAsRead(notification.id)}\n                        className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline ml-2\"\n                      >\n                        Marquer comme lu\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,UAAU;gBACZ;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;8CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;qCAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;YAEpC,IAAI;gBACF,aAAa;gBACb,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB,KAAK,aAAa,IAAI,EAAE;YAC3C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD,SAAU;gBACR,aAAa;YACf;QACF;2DAAG;QAAC;QAAM;QAAkB;KAAa;IAEzC,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,KAAK,IAAI,KAAK,SAAS;gBACjC;gBAEA,MAAM,WAAW,YAAY,oBAAoB;gBAEjD;kDAAO,IAAM,cAAc;;YAC7B;QACF;qCAAG;QAAC;QAAM;KAAmB;IAE7B,qCAAqC;IACrC,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAe;YACxC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,6BAA6B;YAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,8CAA8C;IAC9C,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAK;YACvC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,6BAA6B;YAC7B,iBAAiB,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;IACF;IAEA,+CAA+C;IAC/C,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YAC3C,IAAI,aAAkB,eAAe,kBAAkB,QAAQ;gBAC7D,IAAI,aAAa,UAAU,KAAK,WAAW;oBACzC,4CAA4C;oBAC5C,MAAM,eAAe,IAAI,aAAa,wCAAwC;wBAC5E,MAAM,aAAa,OAAO;wBAC1B,MAAM;wBACN,KAAK,aAAa,EAAE;wBACpB,oBAAoB;wBACpB,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;oBAC1B;oBAEA,wEAAwE;oBACxE,aAAa,OAAO;iFAAG;4BACrB,2DAA2D;4BAC3D,OAAO,KAAK;4BACZ,aAAa,KAAK;4BAElB,wEAAwE;4BACxE,IAAI,aAAa,OAAO,EAAE;gCACxB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,cAAc,EAAE,aAAa,OAAO,EAAE;4BAChE;wBACF;;gBACF,OAAO,IAAI,aAAa,UAAU,KAAK,UAAU;oBAC/C,qDAAqD;oBACrD,aAAa,iBAAiB,GAAG,IAAI;iFAAC,CAAA;4BACpC,IAAI,eAAe,WAAW;gCAC5B,wBAAwB;4BAC1B;wBACF;;gBACF;YACF;QACF;gEAAG,EAAE;IAEL,4EAA4E;IAC5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,KAAK,IAAI,KAAK,SAAS;gBACjC,gCAAgC;gBAChC,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD;gBAEpB,gDAAgD;gBAChD,MAAM;qEAAqB;wBACzB,MAAM,oBAAoB,MAAM,CAAA,GAAA,8HAAA,CAAA,gCAA6B,AAAD;wBAE5D,IAAI,mBAAmB;4BACrB,mCAAmC;4BACnC,MAAM,CAAA,GAAA,8HAAA,CAAA,+BAA4B,AAAD;wBACnC;oBACF;;gBAEA;YACF;QACF;qCAAG;QAAC;KAAK;IAET,uGAAuG;IACvG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,qDAAqD;YACrD,MAAM;uEAA2B;oBAC/B,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;oBAEpC,IAAI;wBACF,MAAM,yBAAyB,cAAc,MAAM;wBACnD,MAAM;wBAEN,wEAAwE;wBACxE,IAAI,cAAc,MAAM,GAAG,0BAA0B,cAAc,MAAM,GAAG,GAAG;4BAC7E,wBAAwB,aAAa,CAAC,EAAE;wBAC1C;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yCAAyC;oBACzD;gBACF;;YAEA,uCAAuC;YACvC;YAEA,kEAAkE;YAClE,MAAM,WAAW,YAAY,0BAA0B;YAEvD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAM;QAAoB,cAAc,MAAM;QAAE;KAAwB;IAE5E,sFAAsF;IACtF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,qBAAqB,aAAa,CAAC,EAAE;gBAC3C,wBAAwB;YAC1B;QACF;qCAAG;QAAC;QAAe;KAAwB;IAE3C,4EAA4E;IAC5E,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;QAClC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;;kCAEX,6LAAC,iJAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAK,WAAU;kCACb,cAAc,MAAM;;;;;;;;;;;;0BAK3B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;gCAGtE,cAAc,MAAM,GAAG,mBACtB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAML,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;0CAAmD;;;;;uCAGhE,cAAc,MAAM,KAAK,kBAC3B,6LAAC;gCAAI,WAAU;0CAAmD;;;;;uCAIlE,cAAc,GAAG,CAAC,CAAC,6BACjB,6LAAC;oCAEC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;oDAEtB,aAAa,KAAK,kBACjB,6LAAC;wDAAE,WAAU;;4DAAgD;4DACnD,aAAa,KAAK,CAAC,WAAW;;;;;;;kEAG1C,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;4DACrD,WAAW;4DACX,QAAQ,8IAAA,CAAA,KAAE;wDACZ;;;;;;;;;;;;0DAGJ,6LAAC;gDACC,SAAS,IAAM,WAAW,aAAa,EAAE;gDACzC,WAAU;0DACX;;;;;;;;;;;;mCAvBE,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCxC;GAtRwB;;QACL,0HAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/shared/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { FaBars, FaTimes, FaUser, FaShoppingCart, FaSun, FaMoon } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useCart } from '@/context/CartContext';\nimport { useTheme } from '@/context/ThemeContext';\nimport CartSlidePanel from '@/components/cart/CartSlidePanel';\nimport NotificationBell from '@/components/notifications/NotificationBell';\n\nexport default function Navbar() {\n  const { user } = useAuth();\n  const { itemCount, isCartOpen, openCart, closeCart } = useCart();\n  const { actualTheme, toggleTheme } = useTheme();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <motion.header\n      className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${\n        isScrolled ? 'bg-white/90 dark:bg-[#0a0a0a]/90 backdrop-blur-md shadow-md py-2' : 'bg-transparent py-4'\n      }`}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <div className=\"container mx-auto px-4 flex justify-between items-center\">\n        <Link href=\"/\" className=\"flex items-center\">\n          <Image\n            src=\"/images/logo/logo-moonelec.png\"\n            alt=\"Moonelec Logo\"\n            width={180}\n            height={60}\n            className=\"mr-2\"\n            priority\n          />\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-8\">\n          <NavLink href=\"/about\">À Propos</NavLink>\n          <NavLink href=\"#services\">Services</NavLink>\n          <NavLink href=\"/products\">Produits</NavLink>\n          <NavLink href=\"#brands\">Marques</NavLink>\n          <NavLink href=\"#contact\">Contact</NavLink>\n\n          {/* Theme Toggle */}\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={toggleTheme}\n            className=\"p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n            aria-label={actualTheme === 'dark' ? 'Passer en mode clair' : 'Passer en mode sombre'}\n          >\n            {actualTheme === 'dark' ? (\n              <FaSun className=\"text-yellow-400 text-lg\" />\n            ) : (\n              <FaMoon className=\"text-blue-600 text-lg\" />\n            )}\n          </motion.button>\n\n          {!user ? (\n            <>\n              <Link href=\"/auth/signin\" className=\"btn-outline\">\n                Connexion\n              </Link>\n              <Link href=\"/auth/signup\" className=\"btn-primary\">\n                S'inscrire\n              </Link>\n            </>\n          ) : (\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/account\" className=\"flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors\">\n                <div className=\"relative w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden border border-gray-300 dark:border-gray-600\">\n                  {user.image ? (\n                    <Image\n                      src={user.image}\n                      alt={user.firstname || user.username}\n                      fill\n                      style={{ objectFit: 'cover' }}\n                    />\n                  ) : (\n                    <span className=\"text-gray-500 dark:text-gray-400 text-sm font-medium\">\n                      {user.firstname ? user.firstname.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}\n                    </span>\n                  )}\n                </div>\n                <span className=\"font-medium\">\n                  {user.firstname || user.username}\n                </span>\n              </Link>\n\n              {/* Notification Bell - Only shown for admins */}\n              <NotificationBell />\n\n              <button\n                onClick={openCart}\n                className=\"relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors\"\n                aria-label=\"Ouvrir le panier\"\n              >\n                <FaShoppingCart className=\"h-6 w-6 navbar-cart-icon\" />\n                {itemCount > 0 && (\n                  <span className=\"absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                    {itemCount > 99 ? '99+' : itemCount}\n                  </span>\n                )}\n              </button>\n            </div>\n          )}\n        </nav>\n\n        {/* Mobile Menu Button */}\n        <button\n          className=\"md:hidden text-2xl focus:outline-none\"\n          onClick={toggleMobileMenu}\n          aria-label={isMobileMenuOpen ? \"Fermer le menu\" : \"Ouvrir le menu\"}\n        >\n          {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n        </button>\n      </div>\n\n      {/* Mobile Navigation */}\n      <motion.div\n        className={`md:hidden absolute top-full left-0 right-0 bg-white dark:bg-[#0a0a0a] shadow-lg`}\n        initial={{ height: 0, opacity: 0 }}\n        animate={{\n          height: isMobileMenuOpen ? 'auto' : 0,\n          opacity: isMobileMenuOpen ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"container mx-auto px-4 py-4 flex flex-col space-y-4\">\n          <MobileNavLink href=\"/about\" onClick={() => setIsMobileMenuOpen(false)}>\n            À Propos\n          </MobileNavLink>\n          <MobileNavLink href=\"#services\" onClick={() => setIsMobileMenuOpen(false)}>\n            Services\n          </MobileNavLink>\n          <MobileNavLink href=\"/products\" onClick={() => setIsMobileMenuOpen(false)}>\n            Produits\n          </MobileNavLink>\n          <MobileNavLink href=\"#brands\" onClick={() => setIsMobileMenuOpen(false)}>\n            Marques\n          </MobileNavLink>\n          <MobileNavLink href=\"#contact\" onClick={() => setIsMobileMenuOpen(false)}>\n            Contact\n          </MobileNavLink>\n\n          {/* Mobile Theme Toggle */}\n          <button\n            onClick={toggleTheme}\n            className=\"flex items-center justify-between py-2 px-4 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors\"\n          >\n            <span>Mode {actualTheme === 'dark' ? 'Clair' : 'Sombre'}</span>\n            {actualTheme === 'dark' ? (\n              <FaSun className=\"text-yellow-400 text-lg\" />\n            ) : (\n              <FaMoon className=\"text-blue-600 text-lg\" />\n            )}\n          </button>\n\n          {!user ? (\n            <div className=\"flex flex-col space-y-2 pt-2\">\n              <Link\n                href=\"/auth/signin\"\n                className=\"btn-outline w-full text-center\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Connexion\n              </Link>\n              <Link\n                href=\"/auth/signup\"\n                className=\"btn-primary w-full text-center\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                S'inscrire\n              </Link>\n            </div>\n          ) : (\n            <div className=\"flex flex-col space-y-2 pt-2 border-t border-gray-100 dark:border-gray-800\">\n              <div className=\"flex items-center py-2\">\n                <div className=\"relative w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden border border-gray-300 dark:border-gray-600 mr-3\">\n                  {user.image ? (\n                    <Image\n                      src={user.image}\n                      alt={user.firstname || user.username}\n                      fill\n                      style={{ objectFit: 'cover' }}\n                    />\n                  ) : (\n                    <span className=\"text-gray-500 dark:text-gray-400 text-sm font-medium\">\n                      {user.firstname ? user.firstname.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}\n                    </span>\n                  )}\n                </div>\n                <span className=\"font-medium text-gray-800 dark:text-white\">\n                  {user.firstname || user.username}\n                </span>\n              </div>\n\n              <Link\n                href=\"/account\"\n                className=\"py-2 px-4 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Mon compte\n              </Link>\n\n              <button\n                className=\"w-full py-2 px-4 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors flex items-center justify-between text-left\"\n                onClick={() => {\n                  setIsMobileMenuOpen(false);\n                  openCart();\n                }}\n              >\n                <span>Mon panier</span>\n                {itemCount > 0 && (\n                  <span className=\"bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                    {itemCount > 99 ? '99+' : itemCount}\n                  </span>\n                )}\n              </button>\n            </div>\n          )}\n        </div>\n      </motion.div>\n    </motion.header>\n  );\n}\n\n// Composant CartSlidePanel rendu séparément pour éviter les problèmes de syntaxe\nfunction NavbarWithCart() {\n  const { isCartOpen, closeCart } = useCart();\n\n  return (\n    <>\n      <Navbar />\n      <CartSlidePanel isOpen={isCartOpen} onClose={closeCart} />\n    </>\n  );\n}\n\nexport { NavbarWithCart };\n\nfunction NavLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <Link\n      href={href}\n      className=\"relative font-medium text-text-primary hover:text-primary transition-colors group\"\n    >\n      {children}\n      <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full\"></span>\n    </Link>\n  );\n}\n\nfunction MobileNavLink({\n  href,\n  children,\n  onClick\n}: {\n  href: string;\n  children: React.ReactNode;\n  onClick?: () => void;\n}) {\n  return (\n    <Link\n      href={href}\n      className=\"py-2 px-4 border-b border-gray-100 dark:border-gray-800 text-text-primary hover:text-primary transition-colors\"\n      onClick={onClick}\n    >\n      {children}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAC,4DAA4D,EACtE,aAAa,qEAAqE,uBAClF;QACF,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,QAAQ;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAQ,MAAK;0CAAS;;;;;;0CACvB,6LAAC;gCAAQ,MAAK;0CAAY;;;;;;0CAC1B,6LAAC;gCAAQ,MAAK;0CAAY;;;;;;0CAC1B,6LAAC;gCAAQ,MAAK;0CAAU;;;;;;0CACxB,6LAAC;gCAAQ,MAAK;0CAAW;;;;;;0CAGzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;gCACV,cAAY,gBAAgB,SAAS,yBAAyB;0CAE7D,gBAAgB,uBACf,6LAAC,iJAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAEjB,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;4BAIrB,CAAC,qBACA;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAc;;;;;;kDAGlD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAc;;;;;;;6DAKpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;;0DAC9B,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK,iBACT,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,SAAS,IAAI,KAAK,QAAQ;oDACpC,IAAI;oDACJ,OAAO;wDAAE,WAAW;oDAAQ;;;;;yEAG9B,6LAAC;oDAAK,WAAU;8DACb,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0DAIpG,6LAAC;gDAAK,WAAU;0DACb,KAAK,SAAS,IAAI,KAAK,QAAQ;;;;;;;;;;;;kDAKpC,6LAAC,0JAAA,CAAA,UAAgB;;;;;kDAEjB,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;;0DAEX,6LAAC,iJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;4CACzB,YAAY,mBACX,6LAAC;gDAAK,WAAU;0DACb,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAStC,6LAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAY,mBAAmB,mBAAmB;kCAEjD,iCAAmB,6LAAC,iJAAA,CAAA,UAAO;;;;iDAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;0BAK7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,+EAA+E,CAAC;gBAC5F,SAAS;oBAAE,QAAQ;oBAAG,SAAS;gBAAE;gBACjC,SAAS;oBACP,QAAQ,mBAAmB,SAAS;oBACpC,SAAS,mBAAmB,IAAI;gBAClC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAc,MAAK;4BAAS,SAAS,IAAM,oBAAoB;sCAAQ;;;;;;sCAGxE,6LAAC;4BAAc,MAAK;4BAAY,SAAS,IAAM,oBAAoB;sCAAQ;;;;;;sCAG3E,6LAAC;4BAAc,MAAK;4BAAY,SAAS,IAAM,oBAAoB;sCAAQ;;;;;;sCAG3E,6LAAC;4BAAc,MAAK;4BAAU,SAAS,IAAM,oBAAoB;sCAAQ;;;;;;sCAGzE,6LAAC;4BAAc,MAAK;4BAAW,SAAS,IAAM,oBAAoB;sCAAQ;;;;;;sCAK1E,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;;wCAAK;wCAAM,gBAAgB,SAAS,UAAU;;;;;;;gCAC9C,gBAAgB,uBACf,6LAAC,iJAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAEjB,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAIrB,CAAC,qBACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;;;;;;iDAKH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,iBACT,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,SAAS,IAAI,KAAK,QAAQ;gDACpC,IAAI;gDACJ,OAAO;oDAAE,WAAW;gDAAQ;;;;;qEAG9B,6LAAC;gDAAK,WAAU;0DACb,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sDAIpG,6LAAC;4CAAK,WAAU;sDACb,KAAK,SAAS,IAAI,KAAK,QAAQ;;;;;;;;;;;;8CAIpC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;8CAID,6LAAC;oCACC,WAAU;oCACV,SAAS;wCACP,oBAAoB;wCACpB;oCACF;;sDAEA,6LAAC;sDAAK;;;;;;wCACL,YAAY,mBACX,6LAAC;4CAAK,WAAU;sDACb,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C;GA5OwB;;QACL,0HAAA,CAAA,UAAO;QAC+B,iIAAA,CAAA,UAAO;QACzB,kIAAA,CAAA,WAAQ;;;KAHvB;AA8OxB,iFAAiF;AACjF,SAAS;;IACP,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAExC,qBACE;;0BACE,6LAAC;;;;;0BACD,6LAAC,+IAAA,CAAA,UAAc;gBAAC,QAAQ;gBAAY,SAAS;;;;;;;;AAGnD;IATS;;QAC2B,iIAAA,CAAA,UAAO;;;MADlC;;AAaT,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAA+C;IAC9E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;;YAET;0BACD,6LAAC;gBAAK,WAAU;;;;;;;;;;;;AAGtB;MAVS;AAYT,SAAS,cAAc,EACrB,IAAI,EACJ,QAAQ,EACR,OAAO,EAKR;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;QACV,SAAS;kBAER;;;;;;AAGP;MAlBS", "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/shared/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { FaFacebook, FaTwitter, FaLinkedin, FaInstagram, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-[#0a1f2f] dark:bg-gray-950 text-white pt-16 pb-8 transition-colors duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center mb-4\">\n              <Image\n                src=\"/images/logo/logo-moonelec.png\"\n                alt=\"Moonelec Logo\"\n                width={180}\n                height={60}\n                className=\"mr-2\"\n              />\n            </div>\n            <p className=\"text-gray-300 dark:text-gray-400 mb-4\">\n              Spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits dans les secteurs résidentiel, tertiaire et industriel.\n            </p>\n            <div className=\"flex space-x-4\">\n              <SocialIcon icon={<FaFacebook />} href=\"https://facebook.com\" />\n              <SocialIcon icon={<FaTwitter />} href=\"https://twitter.com\" />\n              <SocialIcon icon={<FaLinkedin />} href=\"https://linkedin.com\" />\n              <SocialIcon icon={<FaInstagram />} href=\"https://instagram.com\" />\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h3 className=\"text-lg font-semibold mb-4 border-b border-blue-500 pb-2 inline-block\">\n              Liens Rapides\n            </h3>\n            <ul className=\"space-y-2\">\n              <FooterLink href=\"/about\">À Propos</FooterLink>\n              <FooterLink href=\"/#services\">Nos Services</FooterLink>\n              <FooterLink href=\"/#products\">Nos Produits</FooterLink>\n              <FooterLink href=\"/#brands\">Nos Marques</FooterLink>\n              <FooterLink href=\"/#contact\">Contact</FooterLink>\n            </ul>\n          </motion.div>\n\n          {/* Services */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h3 className=\"text-lg font-semibold mb-4 border-b border-blue-500 pb-2 inline-block\">\n              Nos Services\n            </h3>\n            <ul className=\"space-y-2\">\n              <FooterLink href=\"/services/residential\">Matériel Résidentiel</FooterLink>\n              <FooterLink href=\"/services/commercial\">Matériel Tertiaire</FooterLink>\n              <FooterLink href=\"/services/industrial\">Matériel Industriel</FooterLink>\n              <FooterLink href=\"/services/consulting\">Conseil Technique</FooterLink>\n              <FooterLink href=\"/services/installation\">Installation</FooterLink>\n            </ul>\n          </motion.div>\n\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            <h3 className=\"text-lg font-semibold mb-4 border-b border-blue-500 pb-2 inline-block\">\n              Contact\n            </h3>\n            <ul className=\"space-y-4\">\n              <li className=\"flex items-start\">\n                <FaMapMarkerAlt className=\"text-blue-400 mt-1 mr-3\" />\n                <span>Derb El Youssoufía, Rue 78, N°89, Bd El Fida - Casablanca-Maroc</span>\n              </li>\n              <li className=\"flex items-center\">\n                <FaPhone className=\"text-blue-400 mr-3\" />\n                <span>+212 522 80 80 80</span>\n              </li>\n              <li className=\"flex items-center\">\n                <FaEnvelope className=\"text-blue-400 mr-3\" />\n                <span><EMAIL></span>\n              </li>\n            </ul>\n          </motion.div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-gray-700 dark:border-gray-800 mt-12 pt-8 text-center text-gray-400 dark:text-gray-500\">\n          <p>© {currentYear} Moonelec S.A.R.L. Tous droits réservés.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n\nfunction SocialIcon({ icon, href }: { icon: React.ReactNode; href: string }) {\n  return (\n    <a\n      href={href}\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n      className=\"w-10 h-10 rounded-full bg-blue-600 dark:bg-blue-700 flex items-center justify-center hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors\"\n    >\n      {icon}\n    </a>\n  );\n}\n\nfunction FooterLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <li>\n      <Link\n        href={href}\n        className=\"text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 hover:pl-2 transition-all duration-300\"\n      >\n        {children}\n      </Link>\n    </li>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAW,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;4CAAK,MAAK;;;;;;sDACvC,6LAAC;4CAAW,oBAAM,6LAAC,iJAAA,CAAA,YAAS;;;;;4CAAK,MAAK;;;;;;sDACtC,6LAAC;4CAAW,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;4CAAK,MAAK;;;;;;sDACvC,6LAAC;4CAAW,oBAAM,6LAAC,iJAAA,CAAA,cAAW;;;;;4CAAK,MAAK;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAAwE;;;;;;8CAGtF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAW,MAAK;sDAAS;;;;;;sDAC1B,6LAAC;4CAAW,MAAK;sDAAa;;;;;;sDAC9B,6LAAC;4CAAW,MAAK;sDAAa;;;;;;sDAC9B,6LAAC;4CAAW,MAAK;sDAAW;;;;;;sDAC5B,6LAAC;4CAAW,MAAK;sDAAY;;;;;;;;;;;;;;;;;;sCAKjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAAwE;;;;;;8CAGtF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAW,MAAK;sDAAwB;;;;;;sDACzC,6LAAC;4CAAW,MAAK;sDAAuB;;;;;;sDACxC,6LAAC;4CAAW,MAAK;sDAAuB;;;;;;sDACxC,6LAAC;4CAAW,MAAK;sDAAuB;;;;;;sDACxC,6LAAC;4CAAW,MAAK;sDAAyB;;;;;;;;;;;;;;;;;;sCAK9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAAwE;;;;;;8CAGtF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iJAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;8DAC1B,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iJAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iJAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;4BAAE;4BAAG;4BAAY;;;;;;;;;;;;;;;;;;;;;;;AAK5B;KA1GwB;AA4GxB,SAAS,WAAW,EAAE,IAAI,EAAE,IAAI,EAA2C;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;kBAET;;;;;;AAGP;MAXS;AAaT,SAAS,WAAW,EAAE,IAAI,EAAE,QAAQ,EAA+C;IACjF,qBACE,6LAAC;kBACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM;YACN,WAAU;sBAET;;;;;;;;;;;AAIT;MAXS", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/shared/LoadingAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface LoadingAnimationProps {\n  isLoading?: boolean;\n  onLoadingComplete?: () => void;\n}\n\nexport default function LoadingAnimation({\n  isLoading = true,\n  onLoadingComplete\n}: LoadingAnimationProps) {\n  const [loading, setLoading] = useState(isLoading);\n  const [fadeOut, setFadeOut] = useState(false);\n\n  useEffect(() => {\n    if (isLoading) {\n      // Réduire le délai initial à 300ms\n      const timer = setTimeout(() => {\n        setFadeOut(true);\n\n        // Attendre la fin de l'animation de fade out avant de cacher complètement\n        const hideTimer = setTimeout(() => {\n          setLoading(false);\n          if (onLoadingComplete) onLoadingComplete();\n        }, 300); // Durée de l'animation de fade out réduite\n\n        return () => clearTimeout(hideTimer);\n      }, 300); // <PERSON><PERSON>lai initial réduit\n\n      return () => clearTimeout(timer);\n    } else {\n      // Si isLoading est false dès le départ, cacher immédiatement\n      setLoading(false);\n    }\n  }, [isLoading, onLoadingComplete]);\n\n  if (!loading) {\n    return null;\n  }\n\n  return (\n    <div\n      className={`fixed inset-0 z-50 flex items-center justify-center bg-[#1a1a1a] transition-opacity duration-500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`}\n    >\n          <style jsx global>{`\n            .loading-svg #ISpzKj7McrRg {\n              opacity: 0;\n              animation: opacitypath 1s ease-in-out forwards;\n            }\n\n            .loading-svg #IMb5qOBkYi8y2 {\n              opacity: 0;\n              animation: opacitypath2 1.2s ease-in-out forwards;\n            }\n\n            .loading-svg #IMb5qOBkYi8y {\n              filter: drop-shadow(3px 3px 2px rgba(255, 255, 255, 0.7));\n              animation: animationshadow 2s ease-in-out infinite;\n            }\n\n            .loading-svg #eGByPCsADLe4 {\n              transform-origin: center;\n              animation: movementAnimation 1s ease-in-out forwards;\n            }\n\n            .loading-svg #rOMyCEsDLSe1 {\n              stroke-dasharray: 300;\n              stroke-dashoffset: 300;\n              animation: animationpath 1s ease-in-out forwards;\n            }\n\n            @keyframes animationpath {\n              0% {\n                stroke-dashoffset: 300;\n              }\n              33% {\n                stroke-dashoffset: 225;\n              }\n              66% {\n                stroke-dashoffset: 182;\n              }\n              99% {\n                stroke-dashoffset: 120;\n              }\n              100% {\n                stroke-dashoffset: 99;\n              }\n            }\n\n            @keyframes opacitypath {\n              80% {\n                opacity: 0;\n              }\n              100% {\n                opacity: 1;\n              }\n            }\n\n            @keyframes opacitypath2 {\n              80% {\n                opacity: 0;\n              }\n              100% {\n                opacity: 1;\n              }\n            }\n\n            @keyframes movementAnimation {\n              0% {\n                transform: translate(-115px, 58px);\n              }\n              33.33% {\n                transform: translate(-65px, 5px);\n              }\n              66.66% {\n                transform: translate(-34px, 31px);\n              }\n              99.99% {\n                transform: translate(25px, -23px);\n              }\n              100% {\n                transform: translate(25px, -23px);\n              }\n            }\n\n            @keyframes animationshadow {\n              0% {\n                filter: drop-shadow(-10px 0px 10px rgba(255, 255, 255, 0.7));\n              }\n              50% {\n                filter: drop-shadow(4px -8px 14px rgba(255, 255, 255, 0.7));\n              }\n              100% {\n                filter: drop-shadow(-10px 0px 10px rgba(255, 255, 255, 0.7));\n              }\n            }\n          `}</style>\n\n          <svg\n            className=\"loading-svg\"\n            id=\"eGByPCsADLe1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n            viewBox=\"0 0 264.58333 264.58333\"\n            width=\"120\"\n            height=\"120\"\n          >\n            <path\n              id=\"ISpzKj7McrRg\"\n              d=\"M122.48,78.945c-19.782763,0-38.755276,7.858672-52.743802,21.847198s-21.847198,32.961039-21.847198,52.743802s7.858672,38.755276,21.847198,52.743802s32.961039,21.847198,52.743802,21.847198s38.755276-7.858672,52.743802-21.847198s21.847198-32.961039,21.847198-52.743802c0-41.195472-33.395528-74.591-74.591-74.591Zm-14.202,4.7329c-20.059262,13.206865-32.143233,35.606447-32.165,59.623c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c4.775729-.058974,9.533518-.596562,14.202-1.6047-11.659545,7.704127-25.322088,11.820586-39.297,11.84-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.085181-33.925568,24.011087-63.115508,57.26-69.858v-.0003Z\"\n              transform=\"translate(25.238-21.244)\"\n              fill=\"#006db7\"\n            />\n            <g id=\"IMb5qOBkYi8y2\">\n              <path\n                id=\"IMb5qOBkYi8y\"\n                d=\"M108.28,83.678c-33.248913,6.742492-57.174819,35.932432-57.26,69.858c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c13.974912-.019414,27.637455-4.135873,39.297-11.84-4.668482,1.008138-9.426271,1.545726-14.202,1.6047-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.021767-24.016553,12.105738-46.416135,32.165-59.623v.0003Z\"\n                transform=\"translate(25.238-21.244)\"\n                fill=\"#fff\"\n              />\n            </g>\n            <path\n              id=\"eGByPCsADLe4\"\n              d=\"M131.51,121l40.383.30632.2758,43.221\"\n              transform=\"translate(-122.494724 62.610263)\"\n              fill=\"none\"\n              stroke=\"#ed1c24\"\n              strokeWidth=\"24.133\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n            />\n            <path\n              id=\"rOMyCEsDLSe1\"\n              d=\"M29.098,200.61l53.006-53.126l30.765,31.282l58.655-57.504\"\n              transform=\"translate(25.238-21.244)\"\n              fill=\"none\"\n              stroke=\"#ed1c24\"\n              strokeWidth=\"24.133\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeDasharray=\"480\"\n            />\n          </svg>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AASe,SAAS,iBAAiB,EACvC,YAAY,IAAI,EAChB,iBAAiB,EACK;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;gBACb,mCAAmC;gBACnC,MAAM,QAAQ;wDAAW;wBACvB,WAAW;wBAEX,0EAA0E;wBAC1E,MAAM,YAAY;0EAAW;gCAC3B,WAAW;gCACX,IAAI,mBAAmB;4BACzB;yEAAG,MAAM,2CAA2C;wBAEpD;gEAAO,IAAM,aAAa;;oBAC5B;uDAAG,MAAM,uBAAuB;gBAEhC;kDAAO,IAAM,aAAa;;YAC5B,OAAO;gBACL,6DAA6D;gBAC7D,WAAW;YACb;QACF;qCAAG;QAAC;QAAW;KAAkB;IAEjC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC;kDACY,CAAC,iGAAiG,EAAE,UAAU,cAAc,eAAe;;;;;;0BAgGlJ,6LAAC;gBAEC,IAAG;gBACH,OAAM;gBACN,YAAW;gBACX,SAAQ;gBACR,OAAM;gBACN,QAAO;0DANG;;kCAQV,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;;;;;;;kCAEP,6LAAC;wBAAE,IAAG;;kCACJ,cAAA,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,WAAU;4BACV,MAAK;;;;;;;;;;;;kCAGT,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;kCAEjB,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,iBAAgB;;;;;;;;;;;;;;;;;;;AAK9B;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 2309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/layout/PageLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useState, useEffect } from 'react';\nimport { NavbarWithCart } from '@/components/shared/Navbar';\nimport Footer from '@/components/shared/Footer';\nimport LoadingAnimation from '@/components/shared/LoadingAnimation';\n\ninterface PageLayoutProps {\n  children: ReactNode;\n  showLoading?: boolean;\n}\n\nexport default function PageLayout({\n  children,\n  showLoading = true\n}: PageLayoutProps) {\n  const [isLoading, setIsLoading] = useState(showLoading);\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    if (showLoading) {\n      const timer = setTimeout(() => {\n        setIsLoading(false);\n      }, 200); // Réduire le délai à 200ms\n\n      return () => clearTimeout(timer);\n    } else {\n      // Si showLoading est false dès le départ, désactiver immédiatement le chargement\n      setIsLoading(false);\n    }\n  }, [showLoading]);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.scrollY > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900\">\n      <LoadingAnimation\n        isLoading={isLoading}\n        onLoadingComplete={() => setIsLoading(false)}\n      />\n\n      {!isLoading && (\n        <>\n          <NavbarWithCart />\n          <main className=\"flex-grow\">\n            <div className=\"animate-fade-in\">\n              {children}\n            </div>\n          </main>\n          <Footer />\n\n          {/* Scroll to top button */}\n          {isVisible && (\n            <button\n              onClick={scrollToTop}\n              className=\"fixed bottom-6 right-6 p-3 bg-primary text-white rounded-full shadow-lg hover:bg-primary-dark focus:outline-none z-50 animate-fade-in\"\n              aria-label=\"Retour en haut\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n              </svg>\n            </button>\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYe,SAAS,WAAW,EACjC,QAAQ,EACR,cAAc,IAAI,EACF;;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,aAAa;gBACf,MAAM,QAAQ;kDAAW;wBACvB,aAAa;oBACf;iDAAG,MAAM,2BAA2B;gBAEpC;4CAAO,IAAM,aAAa;;YAC5B,OAAO;gBACL,iFAAiF;gBACjF,aAAa;YACf;QACF;+BAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;yDAAmB;oBACvB,IAAI,OAAO,OAAO,GAAG,KAAK;wBACxB,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mJAAA,CAAA,UAAgB;gBACf,WAAW;gBACX,mBAAmB,IAAM,aAAa;;;;;;YAGvC,CAAC,2BACA;;kCACE,6LAAC,yIAAA,CAAA,iBAAc;;;;;kCACf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;kCAGL,6LAAC,yIAAA,CAAA,UAAM;;;;;oBAGN,2BACC,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAI,OAAM;4BAA6B,WAAU;4BAAU,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACjG,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GA1EwB;KAAA", "debugId": null}}, {"offset": {"line": 2460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/Container.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\n\ninterface ContainerProps {\n  children: ReactNode;\n  className?: string;\n  as?: keyof JSX.IntrinsicElements;\n}\n\nexport default function Container({\n  children,\n  className = '',\n  as: Component = 'div'\n}: ContainerProps) {\n  return (\n    <Component className={`container mx-auto px-4 ${className}`}>\n      {children}\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,UAAU,EAChC,QAAQ,EACR,YAAY,EAAE,EACd,IAAI,YAAY,KAAK,EACN;IACf,qBACE,6LAAC;QAAU,WAAW,CAAC,uBAAuB,EAAE,WAAW;kBACxD;;;;;;AAGP;KAVwB", "debugId": null}}, {"offset": {"line": 2488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\n\ntype ButtonVariant = 'primary' | 'secondary' | 'outline';\ntype ButtonSize = 'sm' | 'md' | 'lg';\n\ninterface ButtonProps {\n  children: ReactNode;\n  variant?: ButtonVariant;\n  size?: ButtonSize;\n  className?: string;\n  disabled?: boolean;\n  type?: 'button' | 'submit' | 'reset';\n  onClick?: () => void;\n  fullWidth?: boolean;\n  icon?: ReactNode;\n  iconPosition?: 'left' | 'right';\n}\n\nexport default function Button({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  type = 'button',\n  onClick,\n  fullWidth = false,\n  icon,\n  iconPosition = 'right'\n}: ButtonProps) {\n  // Base styles\n  const baseStyles = 'inline-flex items-center justify-center font-medium transition-all duration-300 rounded-md';\n  \n  // Variant styles\n  const variantStyles = {\n    primary: 'bg-primary text-white hover:bg-primary/90 active:bg-primary/80',\n    secondary: 'bg-secondary text-white hover:bg-secondary/90 active:bg-secondary/80',\n    outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white'\n  };\n  \n  // Size styles\n  const sizeStyles = {\n    sm: 'text-sm py-1.5 px-3',\n    md: 'text-base py-2 px-4',\n    lg: 'text-lg py-2.5 px-5'\n  };\n  \n  // Width style\n  const widthStyle = fullWidth ? 'w-full' : '';\n  \n  // Disabled style\n  const disabledStyle = disabled ? 'opacity-60 cursor-not-allowed' : 'hover:shadow-md hover:-translate-y-1';\n  \n  // Combine all styles\n  const buttonStyles = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${widthStyle} ${disabledStyle} ${className}`;\n  \n  return (\n    <motion.button\n      type={type}\n      className={buttonStyles}\n      onClick={onClick}\n      disabled={disabled}\n      whileHover={!disabled ? { scale: 1.02 } : {}}\n      whileTap={!disabled ? { scale: 0.98 } : {}}\n    >\n      {icon && iconPosition === 'left' && <span className=\"mr-2\">{icon}</span>}\n      {children}\n      {icon && iconPosition === 'right' && <span className=\"ml-2\">{icon}</span>}\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAqBe,SAAS,OAAO,EAC7B,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,QAAQ,EACf,OAAO,EACP,YAAY,KAAK,EACjB,IAAI,EACJ,eAAe,OAAO,EACV;IACZ,cAAc;IACd,MAAM,aAAa;IAEnB,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,cAAc;IACd,MAAM,aAAa,YAAY,WAAW;IAE1C,iBAAiB;IACjB,MAAM,gBAAgB,WAAW,kCAAkC;IAEnE,qBAAqB;IACrB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,cAAc,CAAC,EAAE,WAAW;IAE9H,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,MAAM;QACN,WAAW;QACX,SAAS;QACT,UAAU;QACV,YAAY,CAAC,WAAW;YAAE,OAAO;QAAK,IAAI,CAAC;QAC3C,UAAU,CAAC,WAAW;YAAE,OAAO;QAAK,IAAI,CAAC;;YAExC,QAAQ,iBAAiB,wBAAU,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAC3D;YACA,QAAQ,iBAAiB,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGnE;KApDwB", "debugId": null}}, {"offset": {"line": 2565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/icons/ElectricalIcons.tsx"], "sourcesContent": ["'use client';\n\nimport { SVGProps } from 'react';\n\ninterface IconProps extends SVGProps<SVGSVGElement> {\n  size?: number;\n}\n\nexport function LightbulbIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <path d=\"M9 18h6\" />\n      <path d=\"M10 22h4\" />\n      <path d=\"M12 2v2\" />\n      <path d=\"M12 8v2\" />\n      <path d=\"M15.5 5.5l-1.4 1.4\" />\n      <path d=\"M17 12h2\" />\n      <path d=\"M5 12h2\" />\n      <path d=\"M8.5 5.5l1.4 1.4\" />\n      <path d=\"M15.9 14.1a4 4 0 1 0-5.8 0l.8 1.2a2 2 0 0 1 .4 1.2V18h3.4v-1.5c0-.4.1-.8.4-1.2l.8-1.2Z\" />\n    </svg>\n  );\n}\n\nexport function PlugIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <path d=\"M12 22v-5\" />\n      <path d=\"M9 8V2\" />\n      <path d=\"M15 8V2\" />\n      <path d=\"M18 8v4a6 6 0 0 1-6 6 6 6 0 0 1-6-6V8Z\" />\n    </svg>\n  );\n}\n\nexport function BoltIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <path d=\"M13 2 3 14h9l-1 8 10-12h-9l1-8z\" />\n    </svg>\n  );\n}\n\nexport function HomeIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\" />\n      <polyline points=\"9 22 9 12 15 12 15 22\" />\n    </svg>\n  );\n}\n\nexport function BuildingIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <rect x=\"4\" y=\"2\" width=\"16\" height=\"20\" rx=\"2\" ry=\"2\" />\n      <path d=\"M9 22v-4h6v4\" />\n      <path d=\"M8 6h.01\" />\n      <path d=\"M16 6h.01\" />\n      <path d=\"M12 6h.01\" />\n      <path d=\"M12 10h.01\" />\n      <path d=\"M12 14h.01\" />\n      <path d=\"M16 10h.01\" />\n      <path d=\"M16 14h.01\" />\n      <path d=\"M8 10h.01\" />\n      <path d=\"M8 14h.01\" />\n    </svg>\n  );\n}\n\nexport function FactoryIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <path d=\"M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\" />\n      <path d=\"M17 18h1\" />\n      <path d=\"M12 18h1\" />\n      <path d=\"M7 18h1\" />\n    </svg>\n  );\n}\n\nexport function ToolsIcon({ size = 24, ...props }: IconProps) {\n  return (\n    <svg \n      xmlns=\"http://www.w3.org/2000/svg\" \n      width={size} \n      height={size} \n      viewBox=\"0 0 24 24\" \n      fill=\"none\" \n      stroke=\"currentColor\" \n      strokeWidth=\"2\" \n      strokeLinecap=\"round\" \n      strokeLinejoin=\"round\" \n      {...props}\n    >\n      <path d=\"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\" />\n    </svg>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAQO,SAAS,cAAc,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IAC9D,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;;0BAET,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;KAzBgB;AA2BT,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IACzD,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;;0BAET,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;MApBgB;AAsBT,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IACzD,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAjBgB;AAmBT,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IACzD,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;;0BAET,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAS,QAAO;;;;;;;;;;;;AAGvB;MAlBgB;AAoBT,SAAS,aAAa,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IAC7D,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;;0BAET,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,IAAG;;;;;;0BACnD,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;MA3BgB;AA6BT,SAAS,YAAY,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IAC5D,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;;0BAET,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;MApBgB;AAsBT,SAAS,UAAU,EAAE,OAAO,EAAE,EAAE,GAAG,OAAkB;IAC1D,qBACE,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;QACd,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAjBgB", "debugId": null}}, {"offset": {"line": 2966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/landing/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport Image from 'next/image';\nimport { FaArrowRight } from 'react-icons/fa';\nimport Container from '@/components/ui/Container';\nimport Button from '@/components/ui/Button';\nimport { LightbulbIcon, PlugIcon, BoltIcon } from '@/components/icons/ElectricalIcons';\n\nexport default function HeroSection() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center pt-20 overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-b from-primary/10 to-transparent\"></div>\n        <div className=\"absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-primary/5 to-transparent\"></div>\n\n        {/* Circuit Lines */}\n        <svg className=\"absolute inset-0 w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n          {[\n            { path: \"M0,50 L100,50\" },\n            { path: \"M20,0 L20,100\" },\n            { path: \"M40,0 L40,100\" },\n            { path: \"M60,0 L60,100\" },\n            { path: \"M80,0 L80,100\" },\n            { path: \"M0,25 L100,25\" },\n            { path: \"M0,75 L100,75\" }\n          ].map((line, index) => (\n            <path\n              key={index}\n              d={line.path}\n              stroke=\"rgba(0, 109, 183, 0.1)\"\n              strokeWidth=\"0.2\"\n              fill=\"none\"\n              className=\"animate-fade-in\"\n            />\n          ))}\n        </svg>\n      </div>\n\n      <Container className=\"z-10 py-16\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Text Content */}\n          <div className=\"animate-slide-left\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-6 text-text-primary text-shadow\">\n              <span className=\"text-gradient\">Moonelec</span>, Votre Partenaire en Matériel Électrique\n            </h1>\n            <p className=\"text-lg md:text-xl font-body text-text-secondary mb-8 leading-relaxed\">\n              Spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits pour tous vos projets résidentiels, tertiaires et industriels.\n            </p>\n\n            <div className=\"flex flex-wrap gap-4 mb-12\">\n              <Button\n                variant=\"primary\"\n                icon={<FaArrowRight />}\n                onClick={() => {\n                  document.getElementById('products')?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Découvrir nos produits\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Nous contacter\n              </Button>\n            </div>\n\n            {/* Feature Icons */}\n            <div className=\"grid grid-cols-3 gap-4\">\n              <FeatureIcon\n                icon={<LightbulbIcon size={28} />}\n                text=\"Éclairage\"\n                delay=\"delay-100\"\n              />\n              <FeatureIcon\n                icon={<PlugIcon size={28} />}\n                text=\"Connectivité\"\n                delay=\"delay-200\"\n              />\n              <FeatureIcon\n                icon={<BoltIcon size={28} />}\n                text=\"Énergie\"\n                delay=\"delay-300\"\n              />\n            </div>\n          </div>\n\n          {/* Hero Image */}\n          <div className=\"animate-slide-right relative\">\n            <div className=\"relative w-full h-[400px] md:h-[500px] rounded-lg overflow-hidden\">\n              <Image\n                src=\"https://images.unsplash.com/photo-1621905251189-08b45d6a269e?q=80&w=2069&auto=format&fit=crop\"\n                alt=\"Matériel électrique professionnel\"\n                fill\n                style={{ objectFit: 'cover' }}\n                className=\"rounded-lg\"\n                priority\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-tr from-primary/40 to-transparent rounded-lg\"></div>\n            </div>\n\n            {/* Floating Elements */}\n            <div className=\"absolute -top-6 -right-6 bg-white dark:bg-[#1a1a1a] p-4 rounded-lg shadow-lg animate-slide-up delay-500\">\n              <p className=\"font-display font-bold text-primary\">+300 000</p>\n              <p className=\"text-sm font-body\">Références produits</p>\n            </div>\n\n            <div className=\"absolute -bottom-6 -left-6 bg-white dark:bg-[#1a1a1a] p-4 rounded-lg shadow-lg animate-slide-up delay-700\">\n              <p className=\"font-display font-bold text-secondary\">Depuis 1990</p>\n              <p className=\"text-sm font-body\">À votre service</p>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </section>\n  );\n}\n\nfunction FeatureIcon({\n  icon,\n  text,\n  delay\n}: {\n  icon: React.ReactNode;\n  text: string;\n  delay: string;\n}) {\n  return (\n    <div className={`flex flex-col items-center text-center animate-slide-up ${delay}`}>\n      <div className=\"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-2\">\n        {icon}\n      </div>\n      <p className=\"text-sm font-body font-medium\">{text}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,YAAY;QACd;gCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;wBAAiC,SAAQ;wBAAc,qBAAoB;kCACvF;4BACC;gCAAE,MAAM;4BAAgB;4BACxB;gCAAE,MAAM;4BAAgB;4BACxB;gCAAE,MAAM;4BAAgB;4BACxB;gCAAE,MAAM;4BAAgB;4BACxB;gCAAE,MAAM;4BAAgB;4BACxB;gCAAE,MAAM;4BAAgB;4BACxB;gCAAE,MAAM;4BAAgB;yBACzB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;gCAEC,GAAG,KAAK,IAAI;gCACZ,QAAO;gCACP,aAAY;gCACZ,MAAK;gCACL,WAAU;+BALL;;;;;;;;;;;;;;;;0BAWb,6LAAC,wIAAA,CAAA,UAAS;gBAAC,WAAU;0BACnB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAe;;;;;;;8CAEjD,6LAAC;oCAAE,WAAU;8CAAwE;;;;;;8CAIrF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,oBAAM,6LAAC,iJAAA,CAAA,eAAY;;;;;4CACnB,SAAS;gDACP,SAAS,cAAc,CAAC,aAAa,eAAe;oDAAE,UAAU;gDAAS;4CAC3E;sDACD;;;;;;sDAGD,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,SAAS,cAAc,CAAC,YAAY,eAAe;oDAAE,UAAU;gDAAS;4CAC1E;sDACD;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,oBAAM,6LAAC,iJAAA,CAAA,gBAAa;gDAAC,MAAM;;;;;;4CAC3B,MAAK;4CACL,OAAM;;;;;;sDAER,6LAAC;4CACC,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;4CACtB,MAAK;4CACL,OAAM;;;;;;sDAER,6LAAC;4CACC,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;4CACtB,MAAK;4CACL,OAAM;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,OAAO;gDAAE,WAAW;4CAAQ;4CAC5B,WAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAoB;;;;;;;;;;;;8CAGnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GArHwB;KAAA;AAuHxB,SAAS,YAAY,EACnB,IAAI,EACJ,IAAI,EACJ,KAAK,EAKN;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,wDAAwD,EAAE,OAAO;;0BAChF,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAGpD;MAjBS", "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/Section.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useRef, forwardRef, useState, useEffect } from 'react';\n\ninterface SectionProps {\n  children: ReactNode;\n  id?: string;\n  className?: string;\n  title?: string;\n  subtitle?: string;\n  accent?: boolean;\n  titleHighlight?: string;\n}\n\nconst Section = forwardRef<HTMLElement, SectionProps>(function Section(\n  {\n    children,\n    id,\n    className = '',\n    title,\n    subtitle,\n    accent = false,\n    titleHighlight\n  },\n  forwardedRef\n) {\n  const localRef = useRef(null);\n  const ref = forwardedRef || localRef;\n  const [isInView, setIsInView] = useState(false);\n\n  // Utiliser IntersectionObserver au lieu de useInView\n  useEffect(() => {\n    if (ref && 'current' in ref && ref.current && typeof IntersectionObserver !== 'undefined') {\n      const observer = new IntersectionObserver(\n        (entries) => {\n          if (entries[0].isIntersecting) {\n            setIsInView(true);\n            observer.disconnect();\n          }\n        },\n        { threshold: 0.2 }\n      );\n\n      observer.observe(ref.current);\n      return () => observer.disconnect();\n    } else {\n      // Fallback si IntersectionObserver n'est pas disponible\n      setIsInView(true);\n    }\n  }, [ref]);\n\n  const baseStyles = 'py-16 md:py-24';\n  const accentStyles = accent ? 'bg-accent dark:bg-[#111]' : '';\n\n  return (\n    <section\n      id={id}\n      className={`${baseStyles} ${accentStyles} ${className}`}\n      ref={ref}\n    >\n      <div className=\"container mx-auto px-4\">\n        {(title || subtitle) && (\n          <div\n            className={`text-center mb-16 transition-all duration-600 ${isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}\n          >\n            {title && (\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n                {titleHighlight ? (\n                  <>\n                    {title.split(titleHighlight)[0]}\n                    <span className=\"text-primary\">{titleHighlight}</span>\n                    {title.split(titleHighlight)[1]}\n                  </>\n                ) : (\n                  title\n                )}\n              </h2>\n            )}\n            <div className=\"w-20 h-1 bg-secondary mx-auto mb-6\"></div>\n            {subtitle && (\n              <p className=\"text-text-secondary max-w-3xl mx-auto\">\n                {subtitle}\n              </p>\n            )}\n          </div>\n        )}\n\n        {children}\n      </div>\n    </section>\n  );\n});\n\nexport default Section;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAcA,MAAM,wBAAU,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAA6B,SAAS,QAC7D,EACE,QAAQ,EACR,EAAE,EACF,YAAY,EAAE,EACd,KAAK,EACL,QAAQ,EACR,SAAS,KAAK,EACd,cAAc,EACf,EACD,YAAY;;IAEZ,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,MAAM,gBAAgB;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,OAAO,aAAa,OAAO,IAAI,OAAO,IAAI,OAAO,yBAAyB,aAAa;gBACzF,MAAM,WAAW,IAAI;iDACnB,CAAC;wBACC,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE;4BAC7B,YAAY;4BACZ,SAAS,UAAU;wBACrB;oBACF;gDACA;oBAAE,WAAW;gBAAI;gBAGnB,SAAS,OAAO,CAAC,IAAI,OAAO;gBAC5B;iDAAO,IAAM,SAAS,UAAU;;YAClC,OAAO;gBACL,wDAAwD;gBACxD,YAAY;YACd;QACF;oCAAG;QAAC;KAAI;IAER,MAAM,aAAa;IACnB,MAAM,eAAe,SAAS,6BAA6B;IAE3D,qBACE,6LAAC;QACC,IAAI;QACJ,WAAW,GAAG,WAAW,CAAC,EAAE,aAAa,CAAC,EAAE,WAAW;QACvD,KAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;gBACZ,CAAC,SAAS,QAAQ,mBACjB,6LAAC;oBACC,WAAW,CAAC,8CAA8C,EAAE,WAAW,8BAA8B,2BAA2B;;wBAE/H,uBACC,6LAAC;4BAAG,WAAU;sCACX,+BACC;;oCACG,MAAM,KAAK,CAAC,eAAe,CAAC,EAAE;kDAC/B,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAC/B,MAAM,KAAK,CAAC,eAAe,CAAC,EAAE;;+CAGjC;;;;;;sCAIN,6LAAC;4BAAI,WAAU;;;;;;wBACd,0BACC,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;gBAMR;;;;;;;;;;;;AAIT;;uCAEe", "debugId": null}}, {"offset": {"line": 3465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  animate?: boolean;\n  delay?: number;\n  onClick?: () => void;\n  hoverEffect?: boolean;\n}\n\nexport default function Card({\n  children,\n  className = '',\n  animate = false,\n  delay = 0,\n  onClick,\n  hoverEffect = true\n}: CardProps) {\n  const baseStyles = 'bg-white dark:bg-[#1a1a1a] rounded-lg shadow-md overflow-hidden';\n  \n  if (animate) {\n    return (\n      <motion.div\n        className={`${baseStyles} ${className}`}\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        viewport={{ once: true, margin: '-50px' }}\n        transition={{ duration: 0.5, delay }}\n        onClick={onClick}\n        whileHover={hoverEffect ? { y: -5, transition: { duration: 0.2 } } : {}}\n      >\n        {children}\n      </motion.div>\n    );\n  }\n  \n  return (\n    <div \n      className={`${baseStyles} ${className} ${hoverEffect ? 'transition-transform hover:-translate-y-1 duration-200' : ''}`}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAce,SAAS,KAAK,EAC3B,QAAQ,EACR,YAAY,EAAE,EACd,UAAU,KAAK,EACf,QAAQ,CAAC,EACT,OAAO,EACP,cAAc,IAAI,EACR;IACV,MAAM,aAAa;IAEnB,IAAI,SAAS;QACX,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,GAAG,WAAW,CAAC,EAAE,WAAW;YACvC,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAQ;YACxC,YAAY;gBAAE,UAAU;gBAAK;YAAM;YACnC,SAAS;YACT,YAAY,cAAc;gBAAE,GAAG,CAAC;gBAAG,YAAY;oBAAE,UAAU;gBAAI;YAAE,IAAI,CAAC;sBAErE;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW,GAAG,WAAW,CAAC,EAAE,UAAU,CAAC,EAAE,cAAc,2DAA2D,IAAI;QACtH,SAAS;kBAER;;;;;;AAGP;KAlCwB", "debugId": null}}, {"offset": {"line": 3530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/landing/AboutSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef } from 'react';\nimport Image from 'next/image';\nimport { motion, useInView } from 'framer-motion';\nimport CountUp from 'react-countup';\nimport { FaHistory, FaUsers, FaWarehouse, FaGlobe } from 'react-icons/fa';\nimport Section from '@/components/ui/Section';\nimport Card from '@/components/ui/Card';\n\nexport default function AboutSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, amount: 0.3 });\n\n  return (\n    <Section\n      id=\"about\"\n      accent\n      title=\"À Propos de Moonelec\"\n      titleHighlight=\"Moonelec\"\n      subtitle=\"Découvrez l'histoire et l'expertise de Moonelec, votre partenaire de confiance dans la distribution de matériel électrique depuis plus de trois décennies.\"\n      ref={ref}\n    >\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n        {/* Image Side */}\n        <motion.div\n          initial={{ opacity: 0, x: -50 }}\n          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"relative\"\n        >\n          <div className=\"relative w-full h-[400px] rounded-lg overflow-hidden\">\n            <Image\n              src=\"https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?q=80&w=2070&auto=format&fit=crop\"\n              alt=\"Entrepôt Moonelec\"\n              fill\n              style={{ objectFit: 'cover' }}\n              className=\"rounded-lg\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-tr from-primary/30 to-transparent rounded-lg\"></div>\n          </div>\n\n          {/* Experience Badge */}\n          <motion.div\n            className=\"absolute -bottom-8 -right-8 bg-white dark:bg-[#1a1a1a] p-6 rounded-lg shadow-xl\"\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}\n            transition={{ duration: 0.5, delay: 0.6 }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-primary mb-1\">\n                {isInView && (\n                  <CountUp\n                    start={0}\n                    end={new Date().getFullYear() - 1990}\n                    duration={2.5}\n                    suffix=\" ans\"\n                  />\n                )}\n              </div>\n              <p className=\"text-sm text-text-secondary\">d'expérience</p>\n            </div>\n          </motion.div>\n        </motion.div>\n\n        {/* Text Content */}\n        <motion.div\n          initial={{ opacity: 0, x: 50 }}\n          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n        >\n          <h3 className=\"text-2xl font-semibold mb-6 text-text-primary\">\n            Notre Histoire et Notre Expertise\n          </h3>\n\n          <p className=\"text-text-secondary mb-6\">\n            La société Moonelec, est spécialisée dans la distribution de matériel électrique depuis 1990. Forte de plus de 300 000 références produits, elle offre une gamme complète de matériel électrique dans les secteurs résidentiel, tertiaire et industriel.\n          </p>\n\n          <p className=\"text-text-secondary mb-8\">\n            Ainsi elle met à disposition tout son savoir-faire et sa parfaite connaissance des produits. Afin d'accompagner de plus près ses clients dans la réalisation de leurs projets.\n          </p>\n\n          <Card className=\"p-6 mb-8 border-l-4 border-primary bg-primary/5 dark:bg-primary/10\">\n            <p className=\"text-text-secondary font-medium italic\">\n              \"Parce qu'elle sait tirer profit de son expérience tout en ayant une vision de son activité résolument tournée vers l'avenir, est sans cesse à la recherche des innovations qui feront de demain un monde connecté.\"\n            </p>\n          </Card>\n\n          {/* Stats Grid */}\n          <div className=\"grid grid-cols-2 gap-6 mt-8\">\n            <StatItem\n              icon={<FaHistory />}\n              value={1990}\n              label=\"Fondée en\"\n              delay={0.6}\n              isInView={isInView}\n            />\n            <StatItem\n              icon={<FaUsers />}\n              value={5000}\n              label=\"Clients satisfaits\"\n              delay={0.7}\n              isInView={isInView}\n              plus\n            />\n            <StatItem\n              icon={<FaWarehouse />}\n              value={300000}\n              label=\"Références produits\"\n              delay={0.8}\n              isInView={isInView}\n              plus\n            />\n            <StatItem\n              icon={<FaGlobe />}\n              value={15}\n              label=\"Pays desservis\"\n              delay={0.9}\n              isInView={isInView}\n              plus\n            />\n          </div>\n        </motion.div>\n      </div>\n    </Section>\n  );\n}\n\nfunction StatItem({\n  icon,\n  value,\n  label,\n  delay,\n  isInView,\n  plus = false\n}: {\n  icon: React.ReactNode;\n  value: number;\n  label: string;\n  delay: number;\n  isInView: boolean;\n  plus?: boolean;\n}) {\n  return (\n    <motion.div\n      className=\"flex items-start\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n      transition={{ duration: 0.5, delay }}\n    >\n      <div className=\"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary text-lg mr-4 mt-1\">\n        {icon}\n      </div>\n      <div>\n        <div className=\"text-xl font-bold text-text-primary\">\n          {isInView && (\n            <CountUp\n              start={0}\n              end={value}\n              duration={2.5}\n              separator=\" \"\n              suffix={plus ? \"+\" : \"\"}\n            />\n          )}\n        </div>\n        <p className=\"text-sm text-text-secondary\">{label}</p>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAI;IAE1D,qBACE,6LAAC,sIAAA,CAAA,UAAO;QACN,IAAG;QACH,MAAM;QACN,OAAM;QACN,gBAAe;QACf,UAAS;QACT,KAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAChE,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,OAAO;wCAAE,WAAW;oCAAQ;oCAC5B,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS,WAAW;gCAAE,OAAO;gCAAG,SAAS;4BAAE,IAAI;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,0BACC,6LAAC,qJAAA,CAAA,UAAO;4CACN,OAAO;4CACP,KAAK,IAAI,OAAO,WAAW,KAAK;4CAChC,UAAU;4CACV,QAAO;;;;;;;;;;;kDAIb,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;;;;;;;;;;;;;;;;;;8BAMjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,6LAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAI9D,6LAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAIxC,6LAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAIxC,6LAAC,mIAAA,CAAA,UAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;sCAMxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,oBAAM,6LAAC,iJAAA,CAAA,YAAS;;;;;oCAChB,OAAO;oCACP,OAAM;oCACN,OAAO;oCACP,UAAU;;;;;;8CAEZ,6LAAC;oCACC,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;oCACd,OAAO;oCACP,OAAM;oCACN,OAAO;oCACP,UAAU;oCACV,IAAI;;;;;;8CAEN,6LAAC;oCACC,oBAAM,6LAAC,iJAAA,CAAA,cAAW;;;;;oCAClB,OAAO;oCACP,OAAM;oCACN,OAAO;oCACP,UAAU;oCACV,IAAI;;;;;;8CAEN,6LAAC;oCACC,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;oCACd,OAAO;oCACP,OAAM;oCACN,OAAO;oCACP,UAAU;oCACV,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GArHwB;;QAEL,gLAAA,CAAA,YAAS;;;KAFJ;AAuHxB,SAAS,SAAS,EAChB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,OAAO,KAAK,EAQb;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;YAAK;QAAM;;0BAEnC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC,qJAAA,CAAA,UAAO;4BACN,OAAO;4BACP,KAAK;4BACL,UAAU;4BACV,WAAU;4BACV,QAAQ,OAAO,MAAM;;;;;;;;;;;kCAI3B,6LAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;AAIpD;MAzCS", "debugId": null}}, {"offset": {"line": 3911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/landing/ServicesSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport Section from '@/components/ui/Section';\nimport Card from '@/components/ui/Card';\nimport { HomeIcon, BuildingIcon, FactoryIcon, ToolsIcon, LightbulbIcon } from '@/components/icons/ElectricalIcons';\n\nexport default function ServicesSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, amount: 0.2 });\n\n  const services = [\n    {\n      icon: <HomeIcon size={28} />,\n      title: \"Matériel Résidentiel\",\n      description: \"Solutions complètes pour les installations électriques résidentielles, de l'éclairage aux systèmes de sécurité.\",\n      color: \"var(--primary)\"\n    },\n    {\n      icon: <BuildingIcon size={28} />,\n      title: \"Matériel Tertiaire\",\n      description: \"Équipements adaptés aux besoins des bureaux, commerces et bâtiments publics avec une attention particulière à l'efficacité énergétique.\",\n      color: \"var(--secondary)\"\n    },\n    {\n      icon: <FactoryIcon size={28} />,\n      title: \"Matériel Industriel\",\n      description: \"Solutions robustes et fiables pour les environnements industriels exigeants, conformes aux normes les plus strictes.\",\n      color: \"var(--primary)\"\n    },\n    {\n      icon: <ToolsIcon size={28} />,\n      title: \"Conseil Technique\",\n      description: \"Accompagnement personnalisé par nos experts pour vous aider à choisir les solutions les plus adaptées à vos projets.\",\n      color: \"var(--secondary)\"\n    },\n    {\n      icon: <LightbulbIcon size={28} />,\n      title: \"Solutions Innovantes\",\n      description: \"Produits à la pointe de la technologie pour créer les espaces connectés de demain, économes en énergie et intelligents.\",\n      color: \"var(--primary)\"\n    }\n  ];\n\n  return (\n    <Section\n      id=\"services\"\n      title=\"Nos Services\"\n      titleHighlight=\"Services\"\n      subtitle=\"Découvrez notre gamme complète de services et solutions pour tous vos besoins en matériel électrique, du résidentiel à l'industriel.\"\n      ref={ref}\n    >\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {services.map((service, index) => (\n          <ServiceCard\n            key={index}\n            icon={service.icon}\n            title={service.title}\n            description={service.description}\n            color={service.color}\n            index={index}\n            isInView={isInView}\n          />\n        ))}\n      </div>\n    </Section>\n  );\n}\n\nfunction ServiceCard({\n  icon,\n  title,\n  description,\n  color,\n  index,\n  isInView\n}: {\n  icon: React.ReactNode;\n  title: string;\n  description: string;\n  color: string;\n  index: number;\n  isInView: boolean;\n}) {\n  return (\n    <Card\n      animate\n      delay={0.1 * index}\n      hoverEffect\n      className=\"overflow-hidden\"\n    >\n      <div className=\"p-6\">\n        <div\n          className=\"w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl mb-6\"\n          style={{ backgroundColor: color }}\n        >\n          {icon}\n        </div>\n        <h3 className=\"text-xl font-semibold mb-3 text-text-primary\">{title}</h3>\n        <p className=\"text-text-secondary\">{description}</p>\n      </div>\n      <div className=\"h-2\" style={{ backgroundColor: color }}></div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAI;IAE1D,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,eAAY;gBAAC,MAAM;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,cAAW;gBAAC,MAAM;;;;;;YACzB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,gBAAa;gBAAC,MAAM;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,6LAAC,sIAAA,CAAA,UAAO;QACN,IAAG;QACH,OAAM;QACN,gBAAe;QACf,UAAS;QACT,KAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oBAEC,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,aAAa,QAAQ,WAAW;oBAChC,OAAO,QAAQ,KAAK;oBACpB,OAAO;oBACP,UAAU;mBANL;;;;;;;;;;;;;;;AAYjB;GA5DwB;;QAEL,gLAAA,CAAA,YAAS;;;KAFJ;AA8DxB,SAAS,YAAY,EACnB,IAAI,EACJ,KAAK,EACL,WAAW,EACX,KAAK,EACL,KAAK,EACL,QAAQ,EAQT;IACC,qBACE,6LAAC,mIAAA,CAAA,UAAI;QACH,OAAO;QACP,OAAO,MAAM;QACb,WAAW;QACX,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAM;kCAE/B;;;;;;kCAEH,6LAAC;wBAAG,WAAU;kCAAgD;;;;;;kCAC9D,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;0BAEtC,6LAAC;gBAAI,WAAU;gBAAM,OAAO;oBAAE,iBAAiB;gBAAM;;;;;;;;;;;;AAG3D;MAnCS", "debugId": null}}, {"offset": {"line": 4107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/landing/ProductsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { motion, useInView } from 'framer-motion';\nimport { FaArrowRight } from 'react-icons/fa';\n\n// Sample product categories\nconst productCategories = [\n  {\n    id: 'lighting',\n    name: 'É<PERSON>lair<PERSON>',\n    image: 'https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?q=80&w=2070&auto=format&fit=crop',\n    description: 'Solutions d\\'éclairage pour tous les espaces, des LED économiques aux systèmes intelligents.'\n  },\n  {\n    id: 'wiring',\n    name: 'Câblage',\n    image: 'https://images.unsplash.com/photo-1601814933824-fd0b574dd592?q=80&w=2012&auto=format&fit=crop',\n    description: 'Câbles et accessoires de haute qualité pour toutes les installations électriques.'\n  },\n  {\n    id: 'automation',\n    name: 'Domotique',\n    image: 'https://images.unsplash.com/photo-1558002038-1055907df827?q=80&w=2070&auto=format&fit=crop',\n    description: 'Systèmes intelligents pour contrôler et automatiser votre environnement.'\n  },\n  {\n    id: 'security',\n    name: 'Sécurité',\n    image: 'https://images.unsplash.com/photo-1557318041-1ce374d55ebf?q=80&w=2080&auto=format&fit=crop',\n    description: 'Équipements de sécurité électrique, alarmes et systèmes de surveillance.'\n  },\n  {\n    id: 'industrial',\n    name: 'Équipement Industriel',\n    image: 'https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?q=80&w=2070&auto=format&fit=crop',\n    description: 'Matériel robuste pour les environnements industriels exigeants.'\n  },\n  {\n    id: 'renewable',\n    name: 'Énergie Renouvelable',\n    image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?q=80&w=2072&auto=format&fit=crop',\n    description: 'Solutions pour l\\'énergie solaire, éolienne et autres sources renouvelables.'\n  }\n];\n\nexport default function ProductsSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, amount: 0.1 });\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\n\n  return (\n    <section id=\"products\" className=\"section bg-accent dark:bg-[#111]\" ref={ref}>\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            Nos <span className=\"text-primary\">Produits</span>\n          </h2>\n          <div className=\"w-20 h-1 bg-secondary mx-auto mb-6\"></div>\n          <p className=\"text-text-secondary max-w-3xl mx-auto\">\n            Découvrez notre vaste gamme de produits électriques de haute qualité pour tous vos projets, des plus simples aux plus complexes.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {productCategories.map((category, index) => (\n            <ProductCard \n              key={category.id}\n              category={category}\n              index={index}\n              isInView={isInView}\n              isActive={activeCategory === category.id}\n              onMouseEnter={() => setActiveCategory(category.id)}\n              onMouseLeave={() => setActiveCategory(null)}\n            />\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          className=\"text-center mt-16\"\n        >\n          <Link href=\"/products\" className=\"btn-primary inline-flex items-center gap-2\">\n            Voir tous nos produits <FaArrowRight />\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n\nfunction ProductCard({ \n  category, \n  index,\n  isInView,\n  isActive,\n  onMouseEnter,\n  onMouseLeave\n}: { \n  category: {\n    id: string;\n    name: string;\n    image: string;\n    description: string;\n  };\n  index: number;\n  isInView: boolean;\n  isActive: boolean;\n  onMouseEnter: () => void;\n  onMouseLeave: () => void;\n}) {\n  return (\n    <motion.div \n      className=\"rounded-lg overflow-hidden shadow-lg group relative\"\n      initial={{ opacity: 0, y: 30 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n      transition={{ duration: 0.5, delay: 0.1 * index }}\n      whileHover={{ y: -5 }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    >\n      <div className=\"relative h-64 w-full overflow-hidden\">\n        <Image\n          src={category.image}\n          alt={category.name}\n          fill\n          style={{ objectFit: 'cover' }}\n          className=\"transition-transform duration-500 group-hover:scale-110\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"></div>\n      </div>\n      \n      <div className=\"absolute bottom-0 left-0 right-0 p-6 text-white\">\n        <h3 className=\"text-xl font-semibold mb-2\">{category.name}</h3>\n        <motion.p \n          className=\"text-sm text-gray-200 mb-4\"\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ \n            opacity: isActive ? 1 : 0,\n            height: isActive ? 'auto' : 0\n          }}\n          transition={{ duration: 0.3 }}\n        >\n          {category.description}\n        </motion.p>\n        <Link \n          href={`/products/${category.id}`} \n          className=\"inline-flex items-center text-sm font-medium text-white hover:text-primary transition-colors\"\n        >\n          Découvrir <FaArrowRight className=\"ml-2\" />\n        </Link>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAQA,4BAA4B;AAC5B,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAI;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;QAAmC,KAAK;kBACvE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAsC;8CAC9C,6LAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAErC,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAKvD,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC;4BAEC,UAAU;4BACV,OAAO;4BACP,UAAU;4BACV,UAAU,mBAAmB,SAAS,EAAE;4BACxC,cAAc,IAAM,kBAAkB,SAAS,EAAE;4BACjD,cAAc,IAAM,kBAAkB;2BANjC,SAAS,EAAE;;;;;;;;;;8BAWtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAU;;4BAA6C;0CACrD,6LAAC,iJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD;GAlDwB;;QAEL,gLAAA,CAAA,YAAS;;;KAFJ;AAoDxB,SAAS,YAAY,EACnB,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,YAAY,EAab;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;YAAK,OAAO,MAAM;QAAM;QAChD,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,cAAc;QACd,cAAc;;0BAEd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,SAAS,KAAK;wBACnB,KAAK,SAAS,IAAI;wBAClB,IAAI;wBACJ,OAAO;4BAAE,WAAW;wBAAQ;wBAC5B,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8B,SAAS,IAAI;;;;;;kCACzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BACP,SAAS,WAAW,IAAI;4BACxB,QAAQ,WAAW,SAAS;wBAC9B;wBACA,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,SAAS,WAAW;;;;;;kCAEvB,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;wBAChC,WAAU;;4BACX;0CACW,6LAAC,iJAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAK5C;MA/DS", "debugId": null}}, {"offset": {"line": 4437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useMediaQuery.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useMediaQuery(query: string): boolean {\n  const [matches, setMatches] = useState(false);\n  \n  useEffect(() => {\n    const media = window.matchMedia(query);\n    \n    // Initial check\n    if (media.matches !== matches) {\n      setMatches(media.matches);\n    }\n    \n    // Add listener for changes\n    const listener = () => setMatches(media.matches);\n    media.addEventListener('change', listener);\n    \n    // Clean up\n    return () => media.removeEventListener('change', listener);\n  }, [matches, query]);\n  \n  return matches;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,SAAS,cAAc,KAAa;;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ,OAAO,UAAU,CAAC;YAEhC,gBAAgB;YAChB,IAAI,MAAM,OAAO,KAAK,SAAS;gBAC7B,WAAW,MAAM,OAAO;YAC1B;YAEA,2BAA2B;YAC3B,MAAM;oDAAW,IAAM,WAAW,MAAM,OAAO;;YAC/C,MAAM,gBAAgB,CAAC,UAAU;YAEjC,WAAW;YACX;2CAAO,IAAM,MAAM,mBAAmB,CAAC,UAAU;;QACnD;kCAAG;QAAC;QAAS;KAAM;IAEnB,OAAO;AACT;GApBgB", "debugId": null}}, {"offset": {"line": 4480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/landing/BrandsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, useEffect } from 'react';\nimport Image from 'next/image';\n// Suppression de framer-motion pour éviter les problèmes\n// import { motion, useInView, AnimatePresence } from 'framer-motion';\nimport Section from '@/components/ui/Section';\nimport { FaChevronLeft, FaChevronRight, FaLightbulb } from 'react-icons/fa';\nimport { useMediaQuery } from '@/hooks/useMediaQuery';\n\n// Interface pour les marques\ninterface Brand {\n  id: string;\n  name: string;\n  image: string;\n}\n\n// Nombre de marques à afficher par page\nconst BRANDS_PER_PAGE = 6;\n\nexport default function BrandsSection() {\n  const ref = useRef(null);\n  // Remplacer useInView par un état simple\n  const [isInView, setIsInView] = useState(false);\n\n  // Utiliser IntersectionObserver au lieu de useInView\n  useEffect(() => {\n    if (ref.current && typeof IntersectionObserver !== 'undefined') {\n      const observer = new IntersectionObserver(\n        (entries) => {\n          if (entries[0].isIntersecting) {\n            setIsInView(true);\n            observer.disconnect();\n          }\n        },\n        { threshold: 0.2 }\n      );\n\n      observer.observe(ref.current);\n      return () => observer.disconnect();\n    } else {\n      // Fallback si IntersectionObserver n'est pas disponible\n      setIsInView(true);\n    }\n  }, []);\n  const [currentPage, setCurrentPage] = useState(0);\n  const [activeCards, setActiveCards] = useState<Record<number, boolean>>({});\n  const [hoveredCard, setHoveredCard] = useState<number | null>(null);\n  const isMobile = useMediaQuery('(max-width: 768px)');\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [totalPages, setTotalPages] = useState(1);\n\n  // Charger les marques depuis l'API\n  useEffect(() => {\n    const fetchBrands = async () => {\n      try {\n        // Charger les données depuis la base de données\n        const response = await fetch('/api/brands');\n        if (!response.ok) {\n          throw new Error('Failed to fetch brands');\n        }\n        const data = await response.json();\n        setBrands(data.brands);\n        setTotalPages(Math.ceil(data.brands.length / BRANDS_PER_PAGE));\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Error fetching brands:', error);\n        setIsLoading(false);\n      }\n    };\n\n    fetchBrands();\n  }, []);\n\n  // Charger l'état des ampoules depuis localStorage\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      try {\n        const savedState = localStorage.getItem('moonelec-bulbs-state');\n        if (savedState) {\n          setActiveCards(JSON.parse(savedState));\n        } else {\n          // Si pas d'état sauvegardé, initialiser avec quelques ampoules allumées\n          const initialState: Record<number, boolean> = {};\n          brands.forEach((_, index) => {\n            // 30% de chance d'être allumé par défaut\n            initialState[index] = Math.random() > 0.7;\n          });\n          setActiveCards(initialState);\n        }\n      } catch (error) {\n        console.error('Error loading bulbs state:', error);\n      }\n    }\n  }, [brands]);\n\n  const handlePrevPage = () => {\n    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));\n  };\n\n  const handleNextPage = () => {\n    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));\n  };\n\n  const toggleCard = (cardIndex: number) => {\n    setActiveCards(prev => {\n      const newState = {\n        ...prev,\n        [cardIndex]: !prev[cardIndex]\n      };\n\n      // Sauvegarder l'état dans localStorage\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('moonelec-bulbs-state', JSON.stringify(newState));\n      }\n\n      return newState;\n    });\n  };\n\n  // Obtenir les marques pour la page actuelle\n  const currentBrands = brands.slice(\n    currentPage * BRANDS_PER_PAGE,\n    (currentPage + 1) * BRANDS_PER_PAGE\n  );\n\n  if (isLoading) {\n    return (\n      <Section\n        id=\"brands\"\n        title=\"Nos Marques Partenaires\"\n        titleHighlight=\"Marques\"\n        subtitle=\"Découvrez les marques de confiance avec lesquelles nous travaillons pour vous offrir des produits électriques de haute qualité.\"\n        ref={ref}\n        className=\"bg-black text-white\"\n      >\n        <div className=\"flex justify-center items-center py-20\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n        </div>\n      </Section>\n    );\n  }\n\n  if (brands.length === 0) {\n    return (\n      <Section\n        id=\"brands\"\n        title=\"Nos Marques Partenaires\"\n        titleHighlight=\"Marques\"\n        subtitle=\"Découvrez les marques de confiance avec lesquelles nous travaillons pour vous offrir des produits électriques de haute qualité.\"\n        ref={ref}\n        className=\"bg-black text-white\"\n      >\n        <div className=\"flex justify-center items-center py-20\">\n          <p className=\"text-gray-400\">Aucune marque partenaire n'est disponible pour le moment.</p>\n        </div>\n      </Section>\n    );\n  }\n\n  return (\n    <Section\n      id=\"brands\"\n      title=\"Nos Marques Partenaires\"\n      titleHighlight=\"Marques\"\n      subtitle=\"Découvrez les marques de confiance avec lesquelles nous travaillons pour vous offrir des produits électriques de haute qualité.\"\n      ref={ref}\n      className=\"bg-black text-white\"\n    >\n      <div className=\"relative\">\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-8 py-12\">\n          {currentBrands.map((brand, index) => {\n            const cardIndex = currentPage * BRANDS_PER_PAGE + index;\n            const isActive = activeCards[cardIndex] || false;\n            const isHovered = hoveredCard === cardIndex;\n            const isLightOn = isActive || isHovered;\n\n            return (\n              <div\n                key={`${brand.id}-${index}`}\n                className=\"relative mt-12\"\n                onMouseEnter={() => setHoveredCard(cardIndex)}\n                onMouseLeave={() => setHoveredCard(null)}>\n                {/* Hanging Wire */}\n                <div className=\"absolute -top-16 left-1/2 -translate-x-1/2 w-[1px] h-8 bg-gray-500 z-10\"></div>\n\n                <div className=\"relative\">\n                  {/* Lightbulb with Switch */}\n                  <div\n                    className={`absolute -top-8 left-1/2 -translate-x-1/2 z-20 cursor-pointer animate-fade-in hover:scale-110 transition-transform duration-300 delay-${index * 100}`}\n                    onClick={() => toggleCard(cardIndex)}\n                  >\n                    <div\n                      className={`text-4xl transform rotate-180 ${isLightOn ? 'text-yellow-400 animate-pulse' : 'text-gray-400'}`}\n                      style={{\n                        textShadow: isLightOn ? \"0 0 10px rgba(255, 214, 0, 0.5)\" : \"none\"\n                      }}\n                    >\n                      <FaLightbulb />\n                    </div>\n\n                    {/* Light Beam Effect - Trapezoid with Fade Out */}\n                    {isLightOn && (\n                      <div\n                        className=\"animate-light-beam\"\n                        style={{\n                          position: 'absolute',\n                          top: '100%',\n                          left: '50%',\n                          width: '0',\n                          height: '0',\n                          borderLeft: '30px solid transparent',\n                          borderRight: '30px solid transparent',\n                          borderTop: '0',\n                          borderBottom: '180px solid rgba(255, 255, 255, 0.8)',\n                          zIndex: 5,\n                          transformOrigin: 'top'\n                        }}\n                      />\n                    )}\n                  </div>\n                </div>\n\n                {/* Brand Card */}\n                <div\n                  className={`relative flex items-center justify-center p-6 rounded-lg shadow-md h-48 border border-gray-800 overflow-hidden animate-fade-in delay-${index * 100} ${\n                    isLightOn ? 'bg-white' : 'bg-black'\n                  }`}\n                >\n                  {/* Brand Logo */}\n                  <div\n                    className=\"relative w-full h-full z-10 hover:scale-105 transition-transform duration-300\"\n                  >\n                    <div className=\"w-full h-full flex items-center justify-center\">\n                      {brand.image ? (\n                        <Image\n                          src={brand.image.startsWith('/') ? brand.image : `/${brand.image}`}\n                          alt={brand.name}\n                          width={150}\n                          height={150}\n                          className=\"max-h-full max-w-full object-contain\"\n                        />\n                      ) : (\n                        <div className=\"text-center text-gray-500\">\n                          <p className=\"font-bold\">{brand.name}</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Navigation Controls Container */}\n        <div className=\"mt-16 flex flex-col items-center space-y-6 relative\">\n          {/* Hanging Bulbs for Navigation */}\n          <div className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 flex items-center space-x-8\">\n            <div className=\"relative\">\n              <div className=\"w-[1px] h-8 bg-gray-500\"></div>\n              <div className=\"text-yellow-400 text-2xl transform rotate-180 absolute -bottom-6 left-1/2 -translate-x-1/2\">\n                <FaLightbulb />\n              </div>\n            </div>\n          </div>\n          {/* Navigation Buttons - Centered */}\n          <div className=\"flex justify-center space-x-8\">\n            <button\n              onClick={handlePrevPage}\n              className=\"w-12 h-12 rounded-full bg-gray-800 shadow-lg flex items-center justify-center text-white hover:bg-yellow-500 hover:text-white hover:scale-110 active:scale-95 transition-all z-10 border border-gray-700\"\n              aria-label=\"Page précédente\"\n            >\n              <FaChevronLeft />\n            </button>\n\n            <button\n              onClick={handleNextPage}\n              className=\"w-12 h-12 rounded-full bg-gray-800 shadow-lg flex items-center justify-center text-white hover:bg-yellow-500 hover:text-white hover:scale-110 active:scale-95 transition-all z-10 border border-gray-700\"\n              aria-label=\"Page suivante\"\n            >\n              <FaChevronRight />\n            </button>\n          </div>\n\n          {/* Page Counter */}\n          <div className=\"text-sm text-gray-400\">\n            Page {currentPage + 1} sur {totalPages}\n          </div>\n\n          {/* Pagination Indicators */}\n          <div className=\"flex justify-center space-x-3\">\n            {Array.from({ length: totalPages }).map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentPage(index)}\n                className={`w-4 h-4 rounded-full transition-all hover:scale-110 active:scale-90 ${\n                  index === currentPage\n                    ? 'bg-yellow-400 scale-125'\n                    : 'bg-gray-700 hover:bg-yellow-400/50'\n                }`}\n                aria-label={`Aller à la page ${index + 1}`}\n              />\n            ))}\n          </div>\n\n          {/* Turn On/Off All Button */}\n          <div className=\"mt-2\">\n            <button\n              onClick={() => {\n                const allCardIndices = currentBrands.map((_, idx) => currentPage * BRANDS_PER_PAGE + idx);\n                const allOn = allCardIndices.every(idx => activeCards[idx]);\n\n                const newState = {...activeCards};\n                allCardIndices.forEach(idx => {\n                  newState[idx] = !allOn;\n                });\n\n                setActiveCards(newState);\n\n                // Sauvegarder l'état dans localStorage\n                if (typeof window !== 'undefined') {\n                  localStorage.setItem('moonelec-bulbs-state', JSON.stringify(newState));\n                }\n              }}\n              className=\"px-6 py-3 bg-gray-800 text-white rounded-full hover:bg-yellow-500 hover:scale-105 active:scale-95 transition-all\"\n            >\n              {currentBrands.every((_, idx) => activeCards[currentPage * BRANDS_PER_PAGE + idx])\n                ? \"Éteindre toutes les ampoules\"\n                : \"Allumer toutes les ampoules\"}\n            </button>\n          </div>\n        </div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,yDAAyD;AACzD,sEAAsE;AACtE;AACA;AACA;;;AARA;;;;;;AAiBA,wCAAwC;AACxC,MAAM,kBAAkB;AAET,SAAS;;IACtB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,yCAAyC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,IAAI,OAAO,IAAI,OAAO,yBAAyB,aAAa;gBAC9D,MAAM,WAAW,IAAI;+CACnB,CAAC;wBACC,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE;4BAC7B,YAAY;4BACZ,SAAS,UAAU;wBACrB;oBACF;8CACA;oBAAE,WAAW;gBAAI;gBAGnB,SAAS,OAAO,CAAC,IAAI,OAAO;gBAC5B;+CAAO,IAAM,SAAS,UAAU;;YAClC,OAAO;gBACL,wDAAwD;gBACxD,YAAY;YACd;QACF;kCAAG,EAAE;IACL,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;uDAAc;oBAClB,IAAI;wBACF,gDAAgD;wBAChD,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,UAAU,KAAK,MAAM;wBACrB,cAAc,KAAK,IAAI,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG;wBAC7C,aAAa;oBACf,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,aAAa;oBACf;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,wCAAmC;gBACjC,IAAI;oBACF,MAAM,aAAa,aAAa,OAAO,CAAC;oBACxC,IAAI,YAAY;wBACd,eAAe,KAAK,KAAK,CAAC;oBAC5B,OAAO;wBACL,wEAAwE;wBACxE,MAAM,eAAwC,CAAC;wBAC/C,OAAO,OAAO;uDAAC,CAAC,GAAG;gCACjB,yCAAyC;gCACzC,YAAY,CAAC,MAAM,GAAG,KAAK,MAAM,KAAK;4BACxC;;wBACA,eAAe;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF;QACF;kCAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB;QACrB,eAAe,CAAC,OAAU,SAAS,IAAI,aAAa,IAAI,OAAO;IACjE;IAEA,MAAM,iBAAiB;QACrB,eAAe,CAAC,OAAU,SAAS,aAAa,IAAI,IAAI,OAAO;IACjE;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe,CAAA;YACb,MAAM,WAAW;gBACf,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU;YAC/B;YAEA,uCAAuC;YACvC,wCAAmC;gBACjC,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;YAC9D;YAEA,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,OAAO,KAAK,CAChC,cAAc,iBACd,CAAC,cAAc,CAAC,IAAI;IAGtB,IAAI,WAAW;QACb,qBACE,6LAAC,sIAAA,CAAA,UAAO;YACN,IAAG;YACH,OAAM;YACN,gBAAe;YACf,UAAS;YACT,KAAK;YACL,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,6LAAC,sIAAA,CAAA,UAAO;YACN,IAAG;YACH,OAAM;YACN,gBAAe;YACf,UAAS;YACT,KAAK;YACL,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QACN,IAAG;QACH,OAAM;QACN,gBAAe;QACf,UAAS;QACT,KAAK;QACL,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,OAAO;wBACzB,MAAM,YAAY,cAAc,kBAAkB;wBAClD,MAAM,WAAW,WAAW,CAAC,UAAU,IAAI;wBAC3C,MAAM,YAAY,gBAAgB;wBAClC,MAAM,YAAY,YAAY;wBAE9B,qBACE,6LAAC;4BAEC,WAAU;4BACV,cAAc,IAAM,eAAe;4BACnC,cAAc,IAAM,eAAe;;8CAEnC,6LAAC;oCAAI,WAAU;;;;;;8CAEf,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC;wCACC,WAAW,CAAC,sIAAsI,EAAE,QAAQ,KAAK;wCACjK,SAAS,IAAM,WAAW;;0DAE1B,6LAAC;gDACC,WAAW,CAAC,8BAA8B,EAAE,YAAY,kCAAkC,iBAAiB;gDAC3G,OAAO;oDACL,YAAY,YAAY,oCAAoC;gDAC9D;0DAEA,cAAA,6LAAC,iJAAA,CAAA,cAAW;;;;;;;;;;4CAIb,2BACC,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,UAAU;oDACV,KAAK;oDACL,MAAM;oDACN,OAAO;oDACP,QAAQ;oDACR,YAAY;oDACZ,aAAa;oDACb,WAAW;oDACX,cAAc;oDACd,QAAQ;oDACR,iBAAiB;gDACnB;;;;;;;;;;;;;;;;;8CAOR,6LAAC;oCACC,WAAW,CAAC,qIAAqI,EAAE,QAAQ,IAAI,CAAC,EAC9J,YAAY,aAAa,YACzB;8CAGF,cAAA,6LAAC;wCACC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,MAAM,KAAK,iBACV,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,MAAM,KAAK,CAAC,UAAU,CAAC,OAAO,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;gDAClE,KAAK,MAAM,IAAI;gDACf,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAa,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAjEzC,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;oBAyEjC;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;sCAKlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;8CAGhB,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,iJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;gCAAwB;gCAC/B,cAAc;gCAAE;gCAAM;;;;;;;sCAI9B,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAW,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC1C,6LAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAC,oEAAoE,EAC9E,UAAU,cACN,4BACA,sCACJ;oCACF,cAAY,CAAC,gBAAgB,EAAE,QAAQ,GAAG;mCAPrC;;;;;;;;;;sCAaX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAC,GAAG,MAAQ,cAAc,kBAAkB;oCACrF,MAAM,QAAQ,eAAe,KAAK,CAAC,CAAA,MAAO,WAAW,CAAC,IAAI;oCAE1D,MAAM,WAAW;wCAAC,GAAG,WAAW;oCAAA;oCAChC,eAAe,OAAO,CAAC,CAAA;wCACrB,QAAQ,CAAC,IAAI,GAAG,CAAC;oCACnB;oCAEA,eAAe;oCAEf,uCAAuC;oCACvC,wCAAmC;wCACjC,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;oCAC9D;gCACF;gCACA,WAAU;0CAET,cAAc,KAAK,CAAC,CAAC,GAAG,MAAQ,WAAW,CAAC,cAAc,kBAAkB,IAAI,IAC7E,iCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA7TwB;;QA4BL,gIAAA,CAAA,gBAAa;;;KA5BR", "debugId": null}}, {"offset": {"line": 4976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/landing/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport { FaPhone, FaEnvelope, FaMapMarkerAlt, FaClock, FaPaperPlane } from 'react-icons/fa';\n\nexport default function ContactSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, amount: 0.2 });\n  const [formState, setFormState] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormState(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setIsSubmitted(true);\n      setFormState({\n        name: '',\n        email: '',\n        phone: '',\n        subject: '',\n        message: ''\n      });\n\n      // Reset success message after 5 seconds\n      setTimeout(() => {\n        setIsSubmitted(false);\n      }, 5000);\n    }, 1500);\n  };\n\n  return (\n    <section id=\"contact\" className=\"section bg-accent dark:bg-[#111]\" ref={ref}>\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            CONTACT <span className=\"text-primary\">INFO</span>\n          </h2>\n          <div className=\"w-20 h-1 bg-secondary mx-auto mb-6\"></div>\n          <p className=\"text-text-secondary max-w-3xl mx-auto\">\n            Nous sommes à votre service pour toute demande technique ou tarifaire\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <h3 className=\"text-2xl font-semibold mb-6 text-text-primary\">\n              Informations de Contact\n            </h3>\n\n            <div className=\"space-y-6 mb-8\">\n              <ContactItem\n                icon={<FaMapMarkerAlt />}\n                title=\"Adresse\"\n                content=\"Derb El Youssoufía, Rue 78, N°89, Bd El Fida - Casablanca-Maroc\"\n              />\n              <ContactItem\n                icon={<FaPhone />}\n                title=\"Téléphone\"\n                content=\"+212 522 80 80 80\"\n              />\n              <ContactItem\n                icon={<FaEnvelope />}\n                title=\"Email\"\n                content=\"<EMAIL>\"\n              />\n            </div>\n\n            <div className=\"bg-white dark:bg-[#1a1a1a] p-6 rounded-lg shadow-md\">\n              <h4 className=\"text-lg font-semibold mb-4 text-text-primary\">\n                CONTACT INFO\n              </h4>\n              <p className=\"text-text-secondary mb-4\">\n                Nous sommes à votre service pour toute demande technique ou tarifaire\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <div className=\"bg-white dark:bg-[#1a1a1a] p-6 md:p-8 rounded-lg shadow-lg\">\n              <h3 className=\"text-2xl font-semibold mb-6 text-text-primary\">\n                Envoyez-nous un Message\n              </h3>\n\n              {isSubmitted ? (\n                <motion.div\n                  className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 p-4 rounded-md mb-6\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <p className=\"font-medium\">Message envoyé avec succès!</p>\n                  <p className=\"text-sm mt-1\">Nous vous répondrons dans les plus brefs délais.</p>\n                </motion.div>\n              ) : (\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Nom Complet *\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        value={formState.name}\n                        onChange={handleChange}\n                        required\n                        className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                      />\n                    </div>\n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Email *\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        value={formState.email}\n                        onChange={handleChange}\n                        required\n                        className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label htmlFor=\"phone\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Téléphone\n                      </label>\n                      <input\n                        type=\"tel\"\n                        id=\"phone\"\n                        name=\"phone\"\n                        value={formState.phone}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                      />\n                    </div>\n                    <div>\n                      <label htmlFor=\"subject\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Sujet *\n                      </label>\n                      <select\n                        id=\"subject\"\n                        name=\"subject\"\n                        value={formState.subject}\n                        onChange={handleChange}\n                        required\n                        className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                      >\n                        <option value=\"\">Sélectionnez un sujet</option>\n                        <option value=\"information\">Demande d'information</option>\n                        <option value=\"quote\">Demande de devis</option>\n                        <option value=\"support\">Support technique</option>\n                        <option value=\"partnership\">Partenariat</option>\n                        <option value=\"other\">Autre</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                      Message *\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formState.message}\n                      onChange={handleChange}\n                      required\n                      rows={5}\n                      className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                    ></textarea>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    className={`btn-primary w-full flex items-center justify-center gap-2 ${\n                      isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\n                    }`}\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <span className=\"animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full\"></span>\n                        Envoi en cours...\n                      </>\n                    ) : (\n                      <>\n                        Envoyer <FaPaperPlane />\n                      </>\n                    )}\n                  </button>\n                </form>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\nfunction ContactItem({\n  icon,\n  title,\n  content\n}: {\n  icon: React.ReactNode;\n  title: string;\n  content: string;\n}) {\n  return (\n    <div className=\"flex items-start\">\n      <div className=\"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary text-lg mr-4 mt-1\">\n        {icon}\n      </div>\n      <div>\n        <h4 className=\"font-medium text-text-primary\">{title}</h4>\n        <p className=\"text-text-secondary\">{content}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAI;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,WAAW;YACT,gBAAgB;YAChB,eAAe;YACf,aAAa;gBACX,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;YAEA,wCAAwC;YACxC,WAAW;gBACT,eAAe;YACjB,GAAG;QACL,GAAG;IACL;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;QAAmC,KAAK;kBACtE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAsC;8CAC1C,6LAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAKvD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAChE,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,6LAAC;oCAAG,WAAU;8CAAgD;;;;;;8CAI9D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,oBAAM,6LAAC,iJAAA,CAAA,iBAAc;;;;;4CACrB,OAAM;4CACN,SAAQ;;;;;;sDAEV,6LAAC;4CACC,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;4CACd,OAAM;4CACN,SAAQ;;;;;;sDAEV,6LAAC;4CACC,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;4CACjB,OAAM;4CACN,SAAQ;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAG7D,6LAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;sCAO5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC/D,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAgD;;;;;;oCAI7D,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,6LAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;6DAG9B,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAAqD;;;;;;0EAGrF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,UAAU,IAAI;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAqD;;;;;;0EAGtF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,UAAU,KAAK;gEACtB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAqD;;;;;;0EAGtF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,UAAU,KAAK;gEACtB,UAAU;gEACV,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAqD;;;;;;0EAGxF,6LAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,UAAU,OAAO;gEACxB,UAAU;gEACV,QAAQ;gEACR,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,6LAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,6LAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,6LAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;;;;;;;0DAK5B,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAqD;;;;;;kEAGxF,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,UAAU,OAAO;wDACxB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAW,CAAC,0DAA0D,EACpE,eAAe,kCAAkC,IACjD;0DAED,6BACC;;sEACE,6LAAC;4DAAK,WAAU;;;;;;wDAAsF;;iFAIxG;;wDAAE;sEACQ,6LAAC,iJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY7C;GArOwB;;QAEL,gLAAA,CAAA,YAAS;;;KAFJ;AAuOxB,SAAS,YAAY,EACnB,IAAI,EACJ,KAAK,EACL,OAAO,EAKR;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;;;;;;;AAI5C;MApBS", "debugId": null}}, {"offset": {"line": 5612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport PageLayout from '@/components/layout/PageLayout';\nimport HeroSection from '@/components/landing/HeroSection';\nimport AboutSection from '@/components/landing/AboutSection';\nimport ServicesSection from '@/components/landing/ServicesSection';\nimport ProductsSection from '@/components/landing/ProductsSection';\nimport BrandsSection from '@/components/landing/BrandsSection';\nimport ContactSection from '@/components/landing/ContactSection';\n\nexport default function Home() {\n  return (\n    <PageLayout>\n      <HeroSection />\n      <AboutSection />\n      <ServicesSection />\n      <ProductsSection />\n      <BrandsSection />\n      <ContactSection />\n    </PageLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,qBACE,6LAAC,6IAAA,CAAA,UAAU;;0BACT,6LAAC,+IAAA,CAAA,UAAW;;;;;0BACZ,6LAAC,gJAAA,CAAA,UAAY;;;;;0BACb,6LAAC,mJAAA,CAAA,UAAe;;;;;0BAChB,6LAAC,mJAAA,CAAA,UAAe;;;;;0BAChB,6LAAC,iJAAA,CAAA,UAAa;;;;;0BACd,6LAAC,kJAAA,CAAA,UAAc;;;;;;;;;;;AAGrB;KAXwB", "debugId": null}}]}