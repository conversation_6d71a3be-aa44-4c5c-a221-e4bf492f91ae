{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/quotes/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aEye, FaEdit, FaTrash, FaFilePdf, FaCheck, FaTimes, FaPlus } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport Link from 'next/link';\n\n// Types\ninterface Quote {\n  id: string;\n  quoteNumber: string;\n  clientName: string;\n  companyName: string;\n  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';\n  totalAmount: number;\n  createdAt: string;\n  validUntil: string;\n}\n\nexport default function AdminQuotesPage() {\n  const router = useRouter();\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [quotes, setQuotes] = useState<Quote[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);\n\n  // Liste vide pour les devis\n  const mockQuotes: Quote[] = [];\n\n  useEffect(() => {\n    // Fonction pour récupérer les devis depuis l'API\n    const fetchQuotes = async () => {\n      try {\n        // Construire l'URL avec les paramètres de recherche et de filtrage\n        let url = '/api/quotes?dashboard=true';\n\n        if (searchTerm) {\n          url += `&search=${encodeURIComponent(searchTerm)}`;\n        }\n\n        if (selectedStatus) {\n          url += `&status=${encodeURIComponent(selectedStatus)}`;\n        }\n\n        url += `&page=${currentPage}&limit=10`;\n\n        const response = await fetch(url);\n\n        if (!response.ok) {\n          throw new Error('Erreur lors de la récupération des devis');\n        }\n\n        const data = await response.json();\n\n        // Formater les données pour correspondre à notre interface Quote\n        const formattedQuotes = data.quotes.map((quote: any) => ({\n          id: quote.id,\n          quoteNumber: quote.quoteNumber,\n          clientName: quote.client ? `${quote.client.firstname} ${quote.client.lastname}` : 'Client inconnu',\n          companyName: quote.client?.company_name || 'N/A',\n          status: quote.status,\n          totalAmount: quote.totalAmount || 0,\n          createdAt: new Date(quote.createdAt).toLocaleDateString('fr-FR'),\n          validUntil: quote.validUntil ? new Date(quote.validUntil).toLocaleDateString('fr-FR') : 'N/A'\n        }));\n\n        setQuotes(formattedQuotes);\n        setTotalPages(data.totalPages || 1);\n      } catch (error) {\n        console.error('Erreur:', error);\n        // En cas d'erreur, utiliser un tableau vide\n        setQuotes([]);\n        setTotalPages(1);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    // Appeler la fonction de récupération\n    fetchQuotes();\n  }, [searchTerm, selectedStatus, currentPage]);\n\n  // Nous n'avons plus besoin de filtrer localement car l'API s'en charge\n  // Utiliser directement les quotes récupérées de l'API\n  const paginatedQuotes = quotes;\n\n  // Fonction pour obtenir la classe de couleur en fonction du statut\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'DRAFT':\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n      case 'PENDING':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';\n      case 'APPROVED':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';\n      case 'REJECTED':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';\n      case 'EXPIRED':\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n      case 'CONVERTED':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';\n    }\n  };\n\n  // Fonction pour obtenir le libellé du statut en français\n  const getStatusLabel = (status: string) => {\n    switch (status) {\n      case 'DRAFT':\n        return 'Brouillon';\n      case 'PENDING':\n        return 'En attente';\n      case 'APPROVED':\n        return 'Approuvé';\n      case 'REJECTED':\n        return 'Rejeté';\n      case 'EXPIRED':\n        return 'Expiré';\n      case 'CONVERTED':\n        return 'Converti';\n      default:\n        return status;\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0\">\n            Gestion des Devis\n          </h1>\n          <Link href=\"/admin/quotes/new\">\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center\"\n            >\n              <FaPlus className=\"mr-2\" />\n              Nouveau Devis\n            </motion.button>\n          </Link>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6\">\n          <div className=\"flex flex-col md:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <FaSearch className=\"text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                placeholder=\"Rechercher par numéro, client ou entreprise...\"\n              />\n            </div>\n            <div className=\"w-full md:w-64\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <FaFilter className=\"text-gray-400\" />\n                </div>\n                <select\n                  value={selectedStatus || ''}\n                  onChange={(e) => setSelectedStatus(e.target.value || null)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                >\n                  <option value=\"\">Tous les statuts</option>\n                  <option value=\"DRAFT\">Brouillon</option>\n                  <option value=\"PENDING\">En attente</option>\n                  <option value=\"APPROVED\">Approuvé</option>\n                  <option value=\"REJECTED\">Rejeté</option>\n                  <option value=\"EXPIRED\">Expiré</option>\n                  <option value=\"CONVERTED\">Converti</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quotes Table */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                    N° Devis\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                    Client\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                    Date\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                    Montant\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                    Statut\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                {paginatedQuotes.length > 0 ? (\n                  paginatedQuotes.map((quote) => (\n                    <tr key={quote.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                        {quote.quoteNumber}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900 dark:text-white\">{quote.clientName}</div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">{quote.companyName}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900 dark:text-white\">{quote.createdAt}</div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          Valide jusqu'au {quote.validUntil}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                        {quote.status === 'DRAFT' ? (\n                          <span className=\"text-gray-500 dark:text-gray-400\">Non défini</span>\n                        ) : (\n                          new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(quote.totalAmount)\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(quote.status)}`}>\n                          {getStatusLabel(quote.status)}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <div className=\"flex space-x-2\">\n                          <button\n                            onClick={() => router.push(`/admin/quotes/${quote.id}`)}\n                            className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                            title=\"Voir\"\n                          >\n                            <FaEye />\n                          </button>\n\n                          {quote.status === 'DRAFT' && (\n                            <button\n                              onClick={() => router.push(`/admin/quotes/${quote.id}/edit`)}\n                              className=\"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300\"\n                              title=\"Modifier\"\n                            >\n                              <FaEdit />\n                            </button>\n                          )}\n\n                          {quote.status !== 'DRAFT' && (\n                            <button\n                              onClick={() => router.push(`/admin/quotes/${quote.id}/pdf`)}\n                              className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                              title=\"Télécharger PDF\"\n                            >\n                              <FaFilePdf />\n                            </button>\n                          )}\n\n                          {quote.status === 'PENDING' && (\n                            <>\n                              <button\n                                onClick={() => {\n                                  if (confirm('Êtes-vous sûr de vouloir approuver ce devis ?')) {\n                                    // Logique d'approbation\n                                    console.log('Approbation du devis', quote.id);\n                                  }\n                                }}\n                                className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                                title=\"Approuver\"\n                              >\n                                <FaCheck />\n                              </button>\n\n                              <button\n                                onClick={() => {\n                                  if (confirm('Êtes-vous sûr de vouloir rejeter ce devis ?')) {\n                                    // Logique de rejet\n                                    console.log('Rejet du devis', quote.id);\n                                  }\n                                }}\n                                className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                                title=\"Rejeter\"\n                              >\n                                <FaTimes />\n                              </button>\n                            </>\n                          )}\n\n                          {(quote.status === 'DRAFT' || quote.status === 'PENDING') && (\n                            <button\n                              onClick={() => {\n                                if (confirm('Êtes-vous sûr de vouloir supprimer ce devis ?')) {\n                                  // Logique de suppression\n                                  console.log('Suppression du devis', quote.id);\n                                }\n                              }}\n                              className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                              title=\"Supprimer\"\n                            >\n                              <FaTrash />\n                            </button>\n                          )}\n                        </div>\n                      </td>\n                    </tr>\n                  ))\n                ) : (\n                  <tr>\n                    <td colSpan={6} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                      Aucun devis trouvé\n                    </td>\n                  </tr>\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"flex justify-center mt-6\">\n            <nav className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n                disabled={currentPage === 1}\n                className={`px-3 py-1 rounded-md ${\n                  currentPage === 1\n                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n              >\n                Précédent\n              </button>\n\n              {Array.from({ length: totalPages }).map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setCurrentPage(index + 1)}\n                  className={`px-3 py-1 rounded-md ${\n                    currentPage === index + 1\n                      ? 'bg-primary text-white'\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                  }`}\n                >\n                  {index + 1}\n                </button>\n              ))}\n\n              <button\n                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\n                disabled={currentPage === totalPages}\n                className={`px-3 py-1 rounded-md ${\n                  currentPage === totalPages\n                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n              >\n                Suivant\n              </button>\n            </nav>\n          </div>\n        )}\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,4BAA4B;IAC5B,MAAM,aAAsB,EAAE;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,iDAAiD;YACjD,MAAM;yDAAc;oBAClB,IAAI;wBACF,mEAAmE;wBACnE,IAAI,MAAM;wBAEV,IAAI,YAAY;4BACd,OAAO,CAAC,QAAQ,EAAE,mBAAmB,aAAa;wBACpD;wBAEA,IAAI,gBAAgB;4BAClB,OAAO,CAAC,QAAQ,EAAE,mBAAmB,iBAAiB;wBACxD;wBAEA,OAAO,CAAC,MAAM,EAAE,YAAY,SAAS,CAAC;wBAEtC,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,iEAAiE;wBACjE,MAAM,kBAAkB,KAAK,MAAM,CAAC,GAAG;qFAAC,CAAC,QAAe,CAAC;oCACvD,IAAI,MAAM,EAAE;oCACZ,aAAa,MAAM,WAAW;oCAC9B,YAAY,MAAM,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,QAAQ,EAAE,GAAG;oCAClF,aAAa,MAAM,MAAM,EAAE,gBAAgB;oCAC3C,QAAQ,MAAM,MAAM;oCACpB,aAAa,MAAM,WAAW,IAAI;oCAClC,WAAW,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;oCACxD,YAAY,MAAM,UAAU,GAAG,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC,WAAW;gCAC1F,CAAC;;wBAED,UAAU;wBACV,cAAc,KAAK,UAAU,IAAI;oBACnC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,4CAA4C;wBAC5C,UAAU,EAAE;wBACZ,cAAc;oBAChB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,sCAAsC;YACtC;QACF;oCAAG;QAAC;QAAY;QAAgB;KAAY;IAE5C,uEAAuE;IACvE,sDAAsD;IACtD,MAAM,kBAAkB;IAExB,mEAAmE;IACnE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,yDAAyD;IACzD,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC,2IAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC,iJAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAOjC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAGhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CACC,OAAO,kBAAkB;4CACzB,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK,IAAI;4CACrD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,6LAAC;oCAAM,WAAU;8CACd,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,sBACnB,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAG,WAAU;8DACX,MAAM,WAAW;;;;;;8DAEpB,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;sEAAyC,MAAM,UAAU;;;;;;sEACxE,6LAAC;4DAAI,WAAU;sEAA4C,MAAM,WAAW;;;;;;;;;;;;8DAE9E,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;sEAAyC,MAAM,SAAS;;;;;;sEACvE,6LAAC;4DAAI,WAAU;;gEAA2C;gEACvC,MAAM,UAAU;;;;;;;;;;;;;8DAGrC,6LAAC;oDAAG,WAAU;8DACX,MAAM,MAAM,KAAK,wBAChB,6LAAC;wDAAK,WAAU;kEAAmC;;;;;+DAEnD,IAAI,KAAK,YAAY,CAAC,SAAS;wDAAE,OAAO;wDAAY,UAAU;oDAAM,GAAG,MAAM,CAAC,MAAM,WAAW;;;;;;8DAGnG,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,mEAAmE,EAAE,eAAe,MAAM,MAAM,GAAG;kEAClH,eAAe,MAAM,MAAM;;;;;;;;;;;8DAGhC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;gEACtD,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,iJAAA,CAAA,QAAK;;;;;;;;;;4DAGP,MAAM,MAAM,KAAK,yBAChB,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC;gEAC3D,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;4DAIV,MAAM,MAAM,KAAK,yBAChB,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC;gEAC1D,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,iJAAA,CAAA,YAAS;;;;;;;;;;4DAIb,MAAM,MAAM,KAAK,2BAChB;;kFACE,6LAAC;wEACC,SAAS;4EACP,IAAI,QAAQ,kDAAkD;gFAC5D,wBAAwB;gFACxB,QAAQ,GAAG,CAAC,wBAAwB,MAAM,EAAE;4EAC9C;wEACF;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;kFAGV,6LAAC;wEACC,SAAS;4EACP,IAAI,QAAQ,gDAAgD;gFAC1D,mBAAmB;gFACnB,QAAQ,GAAG,CAAC,kBAAkB,MAAM,EAAE;4EACxC;wEACF;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;4DAKb,CAAC,MAAM,MAAM,KAAK,WAAW,MAAM,MAAM,KAAK,SAAS,mBACtD,6LAAC;gEACC,SAAS;oEACP,IAAI,QAAQ,kDAAkD;wEAC5D,yBAAyB;wEACzB,QAAQ,GAAG,CAAC,wBAAwB,MAAM,EAAE;oEAC9C;gEACF;gEACA,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;2CAjGT,MAAM,EAAE;;;;kEAyGnB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;4CAAG,WAAU;sDAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAW9F,aAAa,mBACZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gCACxD,UAAU,gBAAgB;gCAC1B,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,IACZ,iDACA,+CACJ;0CACH;;;;;;4BAIA,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAW,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC1C,6LAAC;oCAEC,SAAS,IAAM,eAAe,QAAQ;oCACtC,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,QAAQ,IACpB,0BACA,+CACJ;8CAED,QAAQ;mCARJ;;;;;0CAYT,6LAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;gCACjE,UAAU,gBAAgB;gCAC1B,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,aACZ,iDACA,+CACJ;0CACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhXwB;;QACP,qIAAA,CAAA,YAAS;QACP,0HAAA,CAAA,UAAO;;;KAFF", "debugId": null}}]}