"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dnd-core";
exports.ids = ["vendor-chunks/dnd-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBeginDrag: () => (/* binding */ createBeginDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./local/setClientOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\nconst ResetCoordinatesAction = {\n    type: _types_js__WEBPACK_IMPORTED_MODULE_1__.INIT_COORDS,\n    payload: {\n        clientOffset: null,\n        sourceClientOffset: null\n    }\n};\nfunction createBeginDrag(manager) {\n    return function beginDrag(sourceIds = [], options = {\n        publishSource: true\n    }) {\n        const { publishSource =true , clientOffset , getSourceClientOffset ,  } = options;\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        // Initialize the coordinates using the client offset\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset));\n        verifyInvariants(sourceIds, monitor, registry);\n        // Get the draggable source\n        const sourceId = getDraggableSource(sourceIds, monitor);\n        if (sourceId == null) {\n            manager.dispatch(ResetCoordinatesAction);\n            return;\n        }\n        // Get the source client offset\n        let sourceClientOffset = null;\n        if (clientOffset) {\n            if (!getSourceClientOffset) {\n                throw new Error('getSourceClientOffset must be defined');\n            }\n            verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n            sourceClientOffset = getSourceClientOffset(sourceId);\n        }\n        // Initialize the full coordinates\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset, sourceClientOffset));\n        const source = registry.getSource(sourceId);\n        const item = source.beginDrag(monitor, sourceId);\n        // If source.beginDrag returns null, this is an indicator to cancel the drag\n        if (item == null) {\n            return undefined;\n        }\n        verifyItemIsObject(item);\n        registry.pinSource(sourceId);\n        const itemType = registry.getSourceType(sourceId);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG,\n            payload: {\n                itemType,\n                item,\n                sourceId,\n                clientOffset: clientOffset || null,\n                sourceClientOffset: sourceClientOffset || null,\n                isSourcePublic: !!publishSource\n            }\n        };\n    };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.isDragging(), 'Cannot call beginDrag while dragging.');\n    sourceIds.forEach(function(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(registry.getSource(sourceId), 'Expected sourceIds to be registered.');\n    });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof getSourceClientOffset === 'function', 'When clientOffset is provided, getSourceClientOffset must be a function.');\n}\nfunction verifyItemIsObject(item) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)((0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.isObject)(item), 'Item must be an object.');\n}\nfunction getDraggableSource(sourceIds, monitor) {\n    let sourceId = null;\n    for(let i = sourceIds.length - 1; i >= 0; i--){\n        if (monitor.canDragSource(sourceIds[i])) {\n            sourceId = sourceIds[i];\n            break;\n        }\n    }\n    return sourceId;\n}\n\n//# sourceMappingURL=beginDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/drop.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDrop: () => (/* binding */ createDrop)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nfunction createDrop(manager) {\n    return function drop(options = {}) {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyInvariants(monitor);\n        const targetIds = getDroppableTargets(monitor);\n        // Multiple actions are dispatched here, which is why this doesn't return an action\n        targetIds.forEach((targetId, index)=>{\n            const dropResult = determineDropResult(targetId, index, registry, monitor);\n            const action = {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_1__.DROP,\n                payload: {\n                    dropResult: _objectSpread({}, options, dropResult)\n                }\n            };\n            manager.dispatch(action);\n        });\n    };\n}\nfunction verifyInvariants(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call drop while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n    const target = registry.getTarget(targetId);\n    let dropResult = target ? target.drop(monitor, targetId) : undefined;\n    verifyDropResultType(dropResult);\n    if (typeof dropResult === 'undefined') {\n        dropResult = index === 0 ? {} : monitor.getDropResult();\n    }\n    return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof dropResult === 'undefined' || (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n    const targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n    targetIds.reverse();\n    return targetIds;\n}\n\n//# sourceMappingURL=drop.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEndDrag: () => (/* binding */ createEndDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\nfunction createEndDrag(manager) {\n    return function endDrag() {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyIsDragging(monitor);\n        const sourceId = monitor.getSourceId();\n        if (sourceId != null) {\n            const source = registry.getSource(sourceId, true);\n            source.endDrag(monitor, sourceId);\n            registry.unpinSource();\n        }\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG\n        };\n    };\n}\nfunction verifyIsDragging(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call endDrag while not dragging.');\n}\n\n//# sourceMappingURL=endDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2VuZERyYWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlEO0FBQ1g7QUFDL0I7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLCtDQUFRO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrREFBUztBQUNiOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFxhY3Rpb25zXFxkcmFnRHJvcFxcZW5kRHJhZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICdAcmVhY3QtZG5kL2ludmFyaWFudCc7XG5pbXBvcnQgeyBFTkRfRFJBRyB9IGZyb20gJy4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUVuZERyYWcobWFuYWdlcikge1xuICAgIHJldHVybiBmdW5jdGlvbiBlbmREcmFnKCkge1xuICAgICAgICBjb25zdCBtb25pdG9yID0gbWFuYWdlci5nZXRNb25pdG9yKCk7XG4gICAgICAgIGNvbnN0IHJlZ2lzdHJ5ID0gbWFuYWdlci5nZXRSZWdpc3RyeSgpO1xuICAgICAgICB2ZXJpZnlJc0RyYWdnaW5nKG1vbml0b3IpO1xuICAgICAgICBjb25zdCBzb3VyY2VJZCA9IG1vbml0b3IuZ2V0U291cmNlSWQoKTtcbiAgICAgICAgaWYgKHNvdXJjZUlkICE9IG51bGwpIHtcbiAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IHJlZ2lzdHJ5LmdldFNvdXJjZShzb3VyY2VJZCwgdHJ1ZSk7XG4gICAgICAgICAgICBzb3VyY2UuZW5kRHJhZyhtb25pdG9yLCBzb3VyY2VJZCk7XG4gICAgICAgICAgICByZWdpc3RyeS51bnBpblNvdXJjZSgpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlOiBFTkRfRFJBR1xuICAgICAgICB9O1xuICAgIH07XG59XG5mdW5jdGlvbiB2ZXJpZnlJc0RyYWdnaW5nKG1vbml0b3IpIHtcbiAgICBpbnZhcmlhbnQobW9uaXRvci5pc0RyYWdnaW5nKCksICdDYW5ub3QgY2FsbCBlbmREcmFnIHdoaWxlIG5vdCBkcmFnZ2luZy4nKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW5kRHJhZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/hover.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHover: () => (/* binding */ createHover)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\nfunction createHover(manager) {\n    return function hover(targetIdsArg, { clientOffset  } = {}) {\n        verifyTargetIdsIsArray(targetIdsArg);\n        const targetIds = targetIdsArg.slice(0);\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        const draggedItemType = monitor.getItemType();\n        removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n        checkInvariants(targetIds, monitor, registry);\n        hoverAllTargets(targetIds, monitor, registry);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.HOVER,\n            payload: {\n                targetIds,\n                clientOffset: clientOffset || null\n            }\n        };\n    };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call hover while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call hover after drop.');\n    for(let i = 0; i < targetIds.length; i++){\n        const targetId = targetIds[i];\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n        const target = registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, 'Expected targetIds to be registered.');\n    }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n    // Remove those targetIds that don't match the targetType.  This\n    // fixes shallow isOver which would only be non-shallow because of\n    // non-matching targets.\n    for(let i = targetIds.length - 1; i >= 0; i--){\n        const targetId = targetIds[i];\n        const targetType = registry.getTargetType(targetId);\n        if (!(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            targetIds.splice(i, 1);\n        }\n    }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n    // Finally call hover on all matching targets.\n    targetIds.forEach(function(targetId) {\n        const target = registry.getTarget(targetId);\n        target.hover(monitor, targetId);\n    });\n}\n\n//# sourceMappingURL=hover.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.DROP),\n/* harmony export */   END_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG),\n/* harmony export */   HOVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.HOVER),\n/* harmony export */   INIT_COORDS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE),\n/* harmony export */   createDragDropActions: () => (/* binding */ createDragDropActions)\n/* harmony export */ });\n/* harmony import */ var _beginDrag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./beginDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\");\n/* harmony import */ var _drop_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drop.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\");\n/* harmony import */ var _endDrag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./endDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\");\n/* harmony import */ var _hover_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hover.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\");\n/* harmony import */ var _publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./publishDragSource.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\n\n\nfunction createDragDropActions(manager) {\n    return {\n        beginDrag: (0,_beginDrag_js__WEBPACK_IMPORTED_MODULE_1__.createBeginDrag)(manager),\n        publishDragSource: (0,_publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__.createPublishDragSource)(manager),\n        hover: (0,_hover_js__WEBPACK_IMPORTED_MODULE_3__.createHover)(manager),\n        drop: (0,_drop_js__WEBPACK_IMPORTED_MODULE_4__.createDrop)(manager),\n        endDrag: (0,_endDrag_js__WEBPACK_IMPORTED_MODULE_5__.createEndDrag)(manager)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUQ7QUFDVjtBQUNNO0FBQ0o7QUFDd0I7QUFDdEM7QUFDcEI7QUFDUDtBQUNBLG1CQUFtQiw4REFBZTtBQUNsQywyQkFBMkIsOEVBQXVCO0FBQ2xELGVBQWUsc0RBQVc7QUFDMUIsY0FBYyxvREFBVTtBQUN4QixpQkFBaUIsMERBQWE7QUFDOUI7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxQcm9qZWN0c1xcTW9vbmVsZWNBcHBcXG1vb25lbGVjLWFwcFxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcYWN0aW9uc1xcZHJhZ0Ryb3BcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJlZ2luRHJhZyB9IGZyb20gJy4vYmVnaW5EcmFnLmpzJztcbmltcG9ydCB7IGNyZWF0ZURyb3AgfSBmcm9tICcuL2Ryb3AuanMnO1xuaW1wb3J0IHsgY3JlYXRlRW5kRHJhZyB9IGZyb20gJy4vZW5kRHJhZy5qcyc7XG5pbXBvcnQgeyBjcmVhdGVIb3ZlciB9IGZyb20gJy4vaG92ZXIuanMnO1xuaW1wb3J0IHsgY3JlYXRlUHVibGlzaERyYWdTb3VyY2UgfSBmcm9tICcuL3B1Ymxpc2hEcmFnU291cmNlLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZURyYWdEcm9wQWN0aW9ucyhtYW5hZ2VyKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYmVnaW5EcmFnOiBjcmVhdGVCZWdpbkRyYWcobWFuYWdlciksXG4gICAgICAgIHB1Ymxpc2hEcmFnU291cmNlOiBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZShtYW5hZ2VyKSxcbiAgICAgICAgaG92ZXI6IGNyZWF0ZUhvdmVyKG1hbmFnZXIpLFxuICAgICAgICBkcm9wOiBjcmVhdGVEcm9wKG1hbmFnZXIpLFxuICAgICAgICBlbmREcmFnOiBjcmVhdGVFbmREcmFnKG1hbmFnZXIpXG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js":
/*!******************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setClientOffset: () => (/* binding */ setClientOffset)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction setClientOffset(clientOffset, sourceClientOffset) {\n    return {\n        type: _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS,\n        payload: {\n            sourceClientOffset: sourceClientOffset || null,\n            clientOffset: clientOffset || null\n        }\n    };\n}\n\n//# sourceMappingURL=setClientOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2xvY2FsL3NldENsaWVudE9mZnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUNuQztBQUNQO0FBQ0EsY0FBYyxrREFBVztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXGFjdGlvbnNcXGRyYWdEcm9wXFxsb2NhbFxcc2V0Q2xpZW50T2Zmc2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IElOSVRfQ09PUkRTIH0gZnJvbSAnLi4vdHlwZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHNldENsaWVudE9mZnNldChjbGllbnRPZmZzZXQsIHNvdXJjZUNsaWVudE9mZnNldCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IElOSVRfQ09PUkRTLFxuICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICBzb3VyY2VDbGllbnRPZmZzZXQ6IHNvdXJjZUNsaWVudE9mZnNldCB8fCBudWxsLFxuICAgICAgICAgICAgY2xpZW50T2Zmc2V0OiBjbGllbnRPZmZzZXQgfHwgbnVsbFxuICAgICAgICB9XG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2V0Q2xpZW50T2Zmc2V0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js":
/*!**************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPublishDragSource: () => (/* binding */ createPublishDragSource)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction createPublishDragSource(manager) {\n    return function publishDragSource() {\n        const monitor = manager.getMonitor();\n        if (monitor.isDragging()) {\n            return {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE\n            };\n        }\n        return;\n    };\n}\n\n//# sourceMappingURL=publishDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3B1Ymxpc2hEcmFnU291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQzFDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXGFjdGlvbnNcXGRyYWdEcm9wXFxwdWJsaXNoRHJhZ1NvdXJjZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQVUJMSVNIX0RSQUdfU09VUkNFIH0gZnJvbSAnLi90eXBlcy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlUHVibGlzaERyYWdTb3VyY2UobWFuYWdlcikge1xuICAgIHJldHVybiBmdW5jdGlvbiBwdWJsaXNoRHJhZ1NvdXJjZSgpIHtcbiAgICAgICAgY29uc3QgbW9uaXRvciA9IG1hbmFnZXIuZ2V0TW9uaXRvcigpO1xuICAgICAgICBpZiAobW9uaXRvci5pc0RyYWdnaW5nKCkpIHtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgdHlwZTogUFVCTElTSF9EUkFHX1NPVVJDRVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHVibGlzaERyYWdTb3VyY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* binding */ BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* binding */ DROP),\n/* harmony export */   END_DRAG: () => (/* binding */ END_DRAG),\n/* harmony export */   HOVER: () => (/* binding */ HOVER),\n/* harmony export */   INIT_COORDS: () => (/* binding */ INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* binding */ PUBLISH_DRAG_SOURCE)\n/* harmony export */ });\nconst INIT_COORDS = 'dnd-core/INIT_COORDS';\nconst BEGIN_DRAG = 'dnd-core/BEGIN_DRAG';\nconst PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE';\nconst HOVER = 'dnd-core/HOVER';\nconst DROP = 'dnd-core/DROP';\nconst END_DRAG = 'dnd-core/END_DRAG';\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxQcm9qZWN0c1xcTW9vbmVsZWNBcHBcXG1vb25lbGVjLWFwcFxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcYWN0aW9uc1xcZHJhZ0Ryb3BcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBJTklUX0NPT1JEUyA9ICdkbmQtY29yZS9JTklUX0NPT1JEUyc7XG5leHBvcnQgY29uc3QgQkVHSU5fRFJBRyA9ICdkbmQtY29yZS9CRUdJTl9EUkFHJztcbmV4cG9ydCBjb25zdCBQVUJMSVNIX0RSQUdfU09VUkNFID0gJ2RuZC1jb3JlL1BVQkxJU0hfRFJBR19TT1VSQ0UnO1xuZXhwb3J0IGNvbnN0IEhPVkVSID0gJ2RuZC1jb3JlL0hPVkVSJztcbmV4cG9ydCBjb25zdCBEUk9QID0gJ2RuZC1jb3JlL0RST1AnO1xuZXhwb3J0IGNvbnN0IEVORF9EUkFHID0gJ2RuZC1jb3JlL0VORF9EUkFHJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/registry.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/registry.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_SOURCE: () => (/* binding */ ADD_SOURCE),\n/* harmony export */   ADD_TARGET: () => (/* binding */ ADD_TARGET),\n/* harmony export */   REMOVE_SOURCE: () => (/* binding */ REMOVE_SOURCE),\n/* harmony export */   REMOVE_TARGET: () => (/* binding */ REMOVE_TARGET),\n/* harmony export */   addSource: () => (/* binding */ addSource),\n/* harmony export */   addTarget: () => (/* binding */ addTarget),\n/* harmony export */   removeSource: () => (/* binding */ removeSource),\n/* harmony export */   removeTarget: () => (/* binding */ removeTarget)\n/* harmony export */ });\nconst ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nconst ADD_TARGET = 'dnd-core/ADD_TARGET';\nconst REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nconst REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nfunction addSource(sourceId) {\n    return {\n        type: ADD_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction addTarget(targetId) {\n    return {\n        type: ADD_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\nfunction removeSource(sourceId) {\n    return {\n        type: REMOVE_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction removeTarget(targetId) {\n    return {\n        type: REMOVE_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\n\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFxhY3Rpb25zXFxyZWdpc3RyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQUREX1NPVVJDRSA9ICdkbmQtY29yZS9BRERfU09VUkNFJztcbmV4cG9ydCBjb25zdCBBRERfVEFSR0VUID0gJ2RuZC1jb3JlL0FERF9UQVJHRVQnO1xuZXhwb3J0IGNvbnN0IFJFTU9WRV9TT1VSQ0UgPSAnZG5kLWNvcmUvUkVNT1ZFX1NPVVJDRSc7XG5leHBvcnQgY29uc3QgUkVNT1ZFX1RBUkdFVCA9ICdkbmQtY29yZS9SRU1PVkVfVEFSR0VUJztcbmV4cG9ydCBmdW5jdGlvbiBhZGRTb3VyY2Uoc291cmNlSWQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiBBRERfU09VUkNFLFxuICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICBzb3VyY2VJZFxuICAgICAgICB9XG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBhZGRUYXJnZXQodGFyZ2V0SWQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiBBRERfVEFSR0VULFxuICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICB0YXJnZXRJZFxuICAgICAgICB9XG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiByZW1vdmVTb3VyY2Uoc291cmNlSWQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiBSRU1PVkVfU09VUkNFLFxuICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICBzb3VyY2VJZFxuICAgICAgICB9XG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiByZW1vdmVUYXJnZXQodGFyZ2V0SWQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiBSRU1PVkVfVEFSR0VULFxuICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICB0YXJnZXRJZFxuICAgICAgICB9XG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnaXN0cnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropManagerImpl: () => (/* binding */ DragDropManagerImpl)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\");\n\nclass DragDropManagerImpl {\n    receiveBackend(backend) {\n        this.backend = backend;\n    }\n    getMonitor() {\n        return this.monitor;\n    }\n    getBackend() {\n        return this.backend;\n    }\n    getRegistry() {\n        return this.monitor.registry;\n    }\n    getActions() {\n        /* eslint-disable-next-line @typescript-eslint/no-this-alias */ const manager = this;\n        const { dispatch  } = this.store;\n        function bindActionCreator(actionCreator) {\n            return (...args)=>{\n                const action = actionCreator.apply(manager, args);\n                if (typeof action !== 'undefined') {\n                    dispatch(action);\n                }\n            };\n        }\n        const actions = (0,_actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.createDragDropActions)(this);\n        return Object.keys(actions).reduce((boundActions, key)=>{\n            const action = actions[key];\n            boundActions[key] = bindActionCreator(action);\n            return boundActions;\n        }, {});\n    }\n    dispatch(action) {\n        this.store.dispatch(action);\n    }\n    constructor(store, monitor){\n        this.isSetUp = false;\n        this.handleRefCountChange = ()=>{\n            const shouldSetUp = this.store.getState().refCount > 0;\n            if (this.backend) {\n                if (shouldSetUp && !this.isSetUp) {\n                    this.backend.setup();\n                    this.isSetUp = true;\n                } else if (!shouldSetUp && this.isSetUp) {\n                    this.backend.teardown();\n                    this.isSetUp = false;\n                }\n            }\n        };\n        this.store = store;\n        this.monitor = monitor;\n        store.subscribe(this.handleRefCountChange);\n    }\n}\n\n//# sourceMappingURL=DragDropManagerImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropMonitorImpl: () => (/* binding */ DragDropMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_coords_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/coords.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/coords.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n\n\n\n\nclass DragDropMonitorImpl {\n    subscribeToStateChange(listener, options = {}) {\n        const { handlerIds  } = options;\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof handlerIds === 'undefined' || Array.isArray(handlerIds), 'handlerIds, when specified, must be an array of strings.');\n        let prevStateId = this.store.getState().stateId;\n        const handleChange = ()=>{\n            const state = this.store.getState();\n            const currentStateId = state.stateId;\n            try {\n                const canSkipListener = currentStateId === prevStateId || currentStateId === prevStateId + 1 && !(0,_utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__.areDirty)(state.dirtyHandlerIds, handlerIds);\n                if (!canSkipListener) {\n                    listener();\n                }\n            } finally{\n                prevStateId = currentStateId;\n            }\n        };\n        return this.store.subscribe(handleChange);\n    }\n    subscribeToOffsetChange(listener) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        let previousState = this.store.getState().dragOffset;\n        const handleChange = ()=>{\n            const nextState = this.store.getState().dragOffset;\n            if (nextState === previousState) {\n                return;\n            }\n            previousState = nextState;\n            listener();\n        };\n        return this.store.subscribe(handleChange);\n    }\n    canDragSource(sourceId) {\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (this.isDragging()) {\n            return false;\n        }\n        return source.canDrag(this, sourceId);\n    }\n    canDropOnTarget(targetId) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const target = this.registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, `Expected to find a valid target. targetId=${targetId}`);\n        if (!this.isDragging() || this.didDrop()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        return (0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType) && target.canDrop(this, targetId);\n    }\n    isDragging() {\n        return Boolean(this.getItemType());\n    }\n    isDraggingSource(sourceId) {\n        // undefined on initial render\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId, true);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (!this.isDragging() || !this.isSourcePublic()) {\n            return false;\n        }\n        const sourceType = this.registry.getSourceType(sourceId);\n        const draggedItemType = this.getItemType();\n        if (sourceType !== draggedItemType) {\n            return false;\n        }\n        return source.isDragging(this, sourceId);\n    }\n    isOverTarget(targetId, options = {\n        shallow: false\n    }) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const { shallow  } = options;\n        if (!this.isDragging()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        if (draggedItemType && !(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            return false;\n        }\n        const targetIds = this.getTargetIds();\n        if (!targetIds.length) {\n            return false;\n        }\n        const index = targetIds.indexOf(targetId);\n        if (shallow) {\n            return index === targetIds.length - 1;\n        } else {\n            return index > -1;\n        }\n    }\n    getItemType() {\n        return this.store.getState().dragOperation.itemType;\n    }\n    getItem() {\n        return this.store.getState().dragOperation.item;\n    }\n    getSourceId() {\n        return this.store.getState().dragOperation.sourceId;\n    }\n    getTargetIds() {\n        return this.store.getState().dragOperation.targetIds;\n    }\n    getDropResult() {\n        return this.store.getState().dragOperation.dropResult;\n    }\n    didDrop() {\n        return this.store.getState().dragOperation.didDrop;\n    }\n    isSourcePublic() {\n        return Boolean(this.store.getState().dragOperation.isSourcePublic);\n    }\n    getInitialClientOffset() {\n        return this.store.getState().dragOffset.initialClientOffset;\n    }\n    getInitialSourceClientOffset() {\n        return this.store.getState().dragOffset.initialSourceClientOffset;\n    }\n    getClientOffset() {\n        return this.store.getState().dragOffset.clientOffset;\n    }\n    getSourceClientOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getSourceClientOffset)(this.store.getState().dragOffset);\n    }\n    getDifferenceFromInitialOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getDifferenceFromInitialOffset)(this.store.getState().dragOffset);\n    }\n    constructor(store, registry){\n        this.store = store;\n        this.registry = registry;\n    }\n}\n\n//# sourceMappingURL=DragDropMonitorImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRegistryImpl: () => (/* binding */ HandlerRegistryImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/asap */ \"(ssr)/./node_modules/@react-dnd/asap/dist/index.js\");\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _contracts_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts.js */ \"(ssr)/./node_modules/dnd-core/dist/contracts.js\");\n/* harmony import */ var _interfaces_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interfaces.js */ \"(ssr)/./node_modules/dnd-core/dist/interfaces.js\");\n/* harmony import */ var _utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getNextUniqueId.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\");\n\n\n\n\n\n\nfunction getNextHandlerId(role) {\n    const id = (0,_utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__.getNextUniqueId)().toString();\n    switch(role){\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE:\n            return `S${id}`;\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET:\n            return `T${id}`;\n        default:\n            throw new Error(`Unknown Handler Role: ${role}`);\n    }\n}\nfunction parseRoleFromHandlerId(handlerId) {\n    switch(handlerId[0]){\n        case 'S':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n        case 'T':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n        default:\n            throw new Error(`Cannot parse handler ID: ${handlerId}`);\n    }\n}\nfunction mapContainsValue(map, searchValue) {\n    const entries = map.entries();\n    let isDone = false;\n    do {\n        const { done , value: [, value] ,  } = entries.next();\n        if (value === searchValue) {\n            return true;\n        }\n        isDone = !!done;\n    }while (!isDone)\n    return false;\n}\nclass HandlerRegistryImpl {\n    addSource(type, source) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateSourceContract)(source);\n        const sourceId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE, type, source);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addSource)(sourceId));\n        return sourceId;\n    }\n    addTarget(type, target) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type, true);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateTargetContract)(target);\n        const targetId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET, type, target);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addTarget)(targetId));\n        return targetId;\n    }\n    containsHandler(handler) {\n        return mapContainsValue(this.dragSources, handler) || mapContainsValue(this.dropTargets, handler);\n    }\n    getSource(sourceId, includePinned = false) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        const isPinned = includePinned && sourceId === this.pinnedSourceId;\n        const source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId);\n        return source;\n    }\n    getTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.dropTargets.get(targetId);\n    }\n    getSourceType(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        return this.types.get(sourceId);\n    }\n    getTargetType(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.types.get(targetId);\n    }\n    isSourceId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n    }\n    isTargetId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n    }\n    removeSource(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getSource(sourceId), 'Expected an existing source.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeSource)(sourceId));\n        (0,_react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__.asap)(()=>{\n            this.dragSources.delete(sourceId);\n            this.types.delete(sourceId);\n        });\n    }\n    removeTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getTarget(targetId), 'Expected an existing target.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeTarget)(targetId));\n        this.dropTargets.delete(targetId);\n        this.types.delete(targetId);\n    }\n    pinSource(sourceId) {\n        const source = this.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(source, 'Expected an existing source.');\n        this.pinnedSourceId = sourceId;\n        this.pinnedSource = source;\n    }\n    unpinSource() {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.pinnedSource, 'No source is pinned at the time.');\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n    }\n    addHandler(role, type, handler) {\n        const id = getNextHandlerId(role);\n        this.types.set(id, type);\n        if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE) {\n            this.dragSources.set(id, handler);\n        } else if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET) {\n            this.dropTargets.set(id, handler);\n        }\n        return id;\n    }\n    constructor(store){\n        this.types = new Map();\n        this.dragSources = new Map();\n        this.dropTargets = new Map();\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n        this.store = store;\n    }\n}\n\n//# sourceMappingURL=HandlerRegistryImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9jbGFzc2VzL0hhbmRsZXJSZWdpc3RyeUltcGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1QztBQUNVO0FBQ3lDO0FBQ0s7QUFDaEQ7QUFDZTtBQUM5RDtBQUNBLGVBQWUsMEVBQWU7QUFDOUI7QUFDQSxhQUFhLHVEQUFXO0FBQ3hCLHVCQUF1QixHQUFHO0FBQzFCLGFBQWEsdURBQVc7QUFDeEIsdUJBQXVCLEdBQUc7QUFDMUI7QUFDQSxxREFBcUQsS0FBSztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHVEQUFXO0FBQzlCO0FBQ0EsbUJBQW1CLHVEQUFXO0FBQzlCO0FBQ0Esd0RBQXdELFVBQVU7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDZCQUE2QjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ087QUFDUDtBQUNBLFFBQVEsMkRBQVk7QUFDcEIsUUFBUSxxRUFBc0I7QUFDOUIseUNBQXlDLHVEQUFXO0FBQ3BELDRCQUE0QiwrREFBUztBQUNyQztBQUNBO0FBQ0E7QUFDQSxRQUFRLDJEQUFZO0FBQ3BCLFFBQVEscUVBQXNCO0FBQzlCLHlDQUF5Qyx1REFBVztBQUNwRCw0QkFBNEIsK0RBQVM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwrREFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwrREFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQSxRQUFRLCtEQUFTO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLFFBQVEsK0RBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsdURBQVc7QUFDbkM7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHVEQUFXO0FBQ25DO0FBQ0E7QUFDQSxRQUFRLCtEQUFTO0FBQ2pCLDRCQUE0QixrRUFBWTtBQUN4QyxRQUFRLHFEQUFJO0FBQ1o7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsUUFBUSwrREFBUztBQUNqQiw0QkFBNEIsa0VBQVk7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsK0RBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLCtEQUFTO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix1REFBVztBQUNoQztBQUNBLFVBQVUsa0JBQWtCLHVEQUFXO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFxjbGFzc2VzXFxIYW5kbGVyUmVnaXN0cnlJbXBsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFzYXAgfSBmcm9tICdAcmVhY3QtZG5kL2FzYXAnO1xuaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnQHJlYWN0LWRuZC9pbnZhcmlhbnQnO1xuaW1wb3J0IHsgYWRkU291cmNlLCBhZGRUYXJnZXQsIHJlbW92ZVNvdXJjZSwgcmVtb3ZlVGFyZ2V0IH0gZnJvbSAnLi4vYWN0aW9ucy9yZWdpc3RyeS5qcyc7XG5pbXBvcnQgeyB2YWxpZGF0ZVNvdXJjZUNvbnRyYWN0LCB2YWxpZGF0ZVRhcmdldENvbnRyYWN0LCB2YWxpZGF0ZVR5cGUgfSBmcm9tICcuLi9jb250cmFjdHMuanMnO1xuaW1wb3J0IHsgSGFuZGxlclJvbGUgfSBmcm9tICcuLi9pbnRlcmZhY2VzLmpzJztcbmltcG9ydCB7IGdldE5leHRVbmlxdWVJZCB9IGZyb20gJy4uL3V0aWxzL2dldE5leHRVbmlxdWVJZC5qcyc7XG5mdW5jdGlvbiBnZXROZXh0SGFuZGxlcklkKHJvbGUpIHtcbiAgICBjb25zdCBpZCA9IGdldE5leHRVbmlxdWVJZCgpLnRvU3RyaW5nKCk7XG4gICAgc3dpdGNoKHJvbGUpe1xuICAgICAgICBjYXNlIEhhbmRsZXJSb2xlLlNPVVJDRTpcbiAgICAgICAgICAgIHJldHVybiBgUyR7aWR9YDtcbiAgICAgICAgY2FzZSBIYW5kbGVyUm9sZS5UQVJHRVQ6XG4gICAgICAgICAgICByZXR1cm4gYFQke2lkfWA7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gSGFuZGxlciBSb2xlOiAke3JvbGV9YCk7XG4gICAgfVxufVxuZnVuY3Rpb24gcGFyc2VSb2xlRnJvbUhhbmRsZXJJZChoYW5kbGVySWQpIHtcbiAgICBzd2l0Y2goaGFuZGxlcklkWzBdKXtcbiAgICAgICAgY2FzZSAnUyc6XG4gICAgICAgICAgICByZXR1cm4gSGFuZGxlclJvbGUuU09VUkNFO1xuICAgICAgICBjYXNlICdUJzpcbiAgICAgICAgICAgIHJldHVybiBIYW5kbGVyUm9sZS5UQVJHRVQ7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENhbm5vdCBwYXJzZSBoYW5kbGVyIElEOiAke2hhbmRsZXJJZH1gKTtcbiAgICB9XG59XG5mdW5jdGlvbiBtYXBDb250YWluc1ZhbHVlKG1hcCwgc2VhcmNoVmFsdWUpIHtcbiAgICBjb25zdCBlbnRyaWVzID0gbWFwLmVudHJpZXMoKTtcbiAgICBsZXQgaXNEb25lID0gZmFsc2U7XG4gICAgZG8ge1xuICAgICAgICBjb25zdCB7IGRvbmUgLCB2YWx1ZTogWywgdmFsdWVdICwgIH0gPSBlbnRyaWVzLm5leHQoKTtcbiAgICAgICAgaWYgKHZhbHVlID09PSBzZWFyY2hWYWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgaXNEb25lID0gISFkb25lO1xuICAgIH13aGlsZSAoIWlzRG9uZSlcbiAgICByZXR1cm4gZmFsc2U7XG59XG5leHBvcnQgY2xhc3MgSGFuZGxlclJlZ2lzdHJ5SW1wbCB7XG4gICAgYWRkU291cmNlKHR5cGUsIHNvdXJjZSkge1xuICAgICAgICB2YWxpZGF0ZVR5cGUodHlwZSk7XG4gICAgICAgIHZhbGlkYXRlU291cmNlQ29udHJhY3Qoc291cmNlKTtcbiAgICAgICAgY29uc3Qgc291cmNlSWQgPSB0aGlzLmFkZEhhbmRsZXIoSGFuZGxlclJvbGUuU09VUkNFLCB0eXBlLCBzb3VyY2UpO1xuICAgICAgICB0aGlzLnN0b3JlLmRpc3BhdGNoKGFkZFNvdXJjZShzb3VyY2VJZCkpO1xuICAgICAgICByZXR1cm4gc291cmNlSWQ7XG4gICAgfVxuICAgIGFkZFRhcmdldCh0eXBlLCB0YXJnZXQpIHtcbiAgICAgICAgdmFsaWRhdGVUeXBlKHR5cGUsIHRydWUpO1xuICAgICAgICB2YWxpZGF0ZVRhcmdldENvbnRyYWN0KHRhcmdldCk7XG4gICAgICAgIGNvbnN0IHRhcmdldElkID0gdGhpcy5hZGRIYW5kbGVyKEhhbmRsZXJSb2xlLlRBUkdFVCwgdHlwZSwgdGFyZ2V0KTtcbiAgICAgICAgdGhpcy5zdG9yZS5kaXNwYXRjaChhZGRUYXJnZXQodGFyZ2V0SWQpKTtcbiAgICAgICAgcmV0dXJuIHRhcmdldElkO1xuICAgIH1cbiAgICBjb250YWluc0hhbmRsZXIoaGFuZGxlcikge1xuICAgICAgICByZXR1cm4gbWFwQ29udGFpbnNWYWx1ZSh0aGlzLmRyYWdTb3VyY2VzLCBoYW5kbGVyKSB8fCBtYXBDb250YWluc1ZhbHVlKHRoaXMuZHJvcFRhcmdldHMsIGhhbmRsZXIpO1xuICAgIH1cbiAgICBnZXRTb3VyY2Uoc291cmNlSWQsIGluY2x1ZGVQaW5uZWQgPSBmYWxzZSkge1xuICAgICAgICBpbnZhcmlhbnQodGhpcy5pc1NvdXJjZUlkKHNvdXJjZUlkKSwgJ0V4cGVjdGVkIGEgdmFsaWQgc291cmNlIElELicpO1xuICAgICAgICBjb25zdCBpc1Bpbm5lZCA9IGluY2x1ZGVQaW5uZWQgJiYgc291cmNlSWQgPT09IHRoaXMucGlubmVkU291cmNlSWQ7XG4gICAgICAgIGNvbnN0IHNvdXJjZSA9IGlzUGlubmVkID8gdGhpcy5waW5uZWRTb3VyY2UgOiB0aGlzLmRyYWdTb3VyY2VzLmdldChzb3VyY2VJZCk7XG4gICAgICAgIHJldHVybiBzb3VyY2U7XG4gICAgfVxuICAgIGdldFRhcmdldCh0YXJnZXRJZCkge1xuICAgICAgICBpbnZhcmlhbnQodGhpcy5pc1RhcmdldElkKHRhcmdldElkKSwgJ0V4cGVjdGVkIGEgdmFsaWQgdGFyZ2V0IElELicpO1xuICAgICAgICByZXR1cm4gdGhpcy5kcm9wVGFyZ2V0cy5nZXQodGFyZ2V0SWQpO1xuICAgIH1cbiAgICBnZXRTb3VyY2VUeXBlKHNvdXJjZUlkKSB7XG4gICAgICAgIGludmFyaWFudCh0aGlzLmlzU291cmNlSWQoc291cmNlSWQpLCAnRXhwZWN0ZWQgYSB2YWxpZCBzb3VyY2UgSUQuJyk7XG4gICAgICAgIHJldHVybiB0aGlzLnR5cGVzLmdldChzb3VyY2VJZCk7XG4gICAgfVxuICAgIGdldFRhcmdldFR5cGUodGFyZ2V0SWQpIHtcbiAgICAgICAgaW52YXJpYW50KHRoaXMuaXNUYXJnZXRJZCh0YXJnZXRJZCksICdFeHBlY3RlZCBhIHZhbGlkIHRhcmdldCBJRC4nKTtcbiAgICAgICAgcmV0dXJuIHRoaXMudHlwZXMuZ2V0KHRhcmdldElkKTtcbiAgICB9XG4gICAgaXNTb3VyY2VJZChoYW5kbGVySWQpIHtcbiAgICAgICAgY29uc3Qgcm9sZSA9IHBhcnNlUm9sZUZyb21IYW5kbGVySWQoaGFuZGxlcklkKTtcbiAgICAgICAgcmV0dXJuIHJvbGUgPT09IEhhbmRsZXJSb2xlLlNPVVJDRTtcbiAgICB9XG4gICAgaXNUYXJnZXRJZChoYW5kbGVySWQpIHtcbiAgICAgICAgY29uc3Qgcm9sZSA9IHBhcnNlUm9sZUZyb21IYW5kbGVySWQoaGFuZGxlcklkKTtcbiAgICAgICAgcmV0dXJuIHJvbGUgPT09IEhhbmRsZXJSb2xlLlRBUkdFVDtcbiAgICB9XG4gICAgcmVtb3ZlU291cmNlKHNvdXJjZUlkKSB7XG4gICAgICAgIGludmFyaWFudCh0aGlzLmdldFNvdXJjZShzb3VyY2VJZCksICdFeHBlY3RlZCBhbiBleGlzdGluZyBzb3VyY2UuJyk7XG4gICAgICAgIHRoaXMuc3RvcmUuZGlzcGF0Y2gocmVtb3ZlU291cmNlKHNvdXJjZUlkKSk7XG4gICAgICAgIGFzYXAoKCk9PntcbiAgICAgICAgICAgIHRoaXMuZHJhZ1NvdXJjZXMuZGVsZXRlKHNvdXJjZUlkKTtcbiAgICAgICAgICAgIHRoaXMudHlwZXMuZGVsZXRlKHNvdXJjZUlkKTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJlbW92ZVRhcmdldCh0YXJnZXRJZCkge1xuICAgICAgICBpbnZhcmlhbnQodGhpcy5nZXRUYXJnZXQodGFyZ2V0SWQpLCAnRXhwZWN0ZWQgYW4gZXhpc3RpbmcgdGFyZ2V0LicpO1xuICAgICAgICB0aGlzLnN0b3JlLmRpc3BhdGNoKHJlbW92ZVRhcmdldCh0YXJnZXRJZCkpO1xuICAgICAgICB0aGlzLmRyb3BUYXJnZXRzLmRlbGV0ZSh0YXJnZXRJZCk7XG4gICAgICAgIHRoaXMudHlwZXMuZGVsZXRlKHRhcmdldElkKTtcbiAgICB9XG4gICAgcGluU291cmNlKHNvdXJjZUlkKSB7XG4gICAgICAgIGNvbnN0IHNvdXJjZSA9IHRoaXMuZ2V0U291cmNlKHNvdXJjZUlkKTtcbiAgICAgICAgaW52YXJpYW50KHNvdXJjZSwgJ0V4cGVjdGVkIGFuIGV4aXN0aW5nIHNvdXJjZS4nKTtcbiAgICAgICAgdGhpcy5waW5uZWRTb3VyY2VJZCA9IHNvdXJjZUlkO1xuICAgICAgICB0aGlzLnBpbm5lZFNvdXJjZSA9IHNvdXJjZTtcbiAgICB9XG4gICAgdW5waW5Tb3VyY2UoKSB7XG4gICAgICAgIGludmFyaWFudCh0aGlzLnBpbm5lZFNvdXJjZSwgJ05vIHNvdXJjZSBpcyBwaW5uZWQgYXQgdGhlIHRpbWUuJyk7XG4gICAgICAgIHRoaXMucGlubmVkU291cmNlSWQgPSBudWxsO1xuICAgICAgICB0aGlzLnBpbm5lZFNvdXJjZSA9IG51bGw7XG4gICAgfVxuICAgIGFkZEhhbmRsZXIocm9sZSwgdHlwZSwgaGFuZGxlcikge1xuICAgICAgICBjb25zdCBpZCA9IGdldE5leHRIYW5kbGVySWQocm9sZSk7XG4gICAgICAgIHRoaXMudHlwZXMuc2V0KGlkLCB0eXBlKTtcbiAgICAgICAgaWYgKHJvbGUgPT09IEhhbmRsZXJSb2xlLlNPVVJDRSkge1xuICAgICAgICAgICAgdGhpcy5kcmFnU291cmNlcy5zZXQoaWQsIGhhbmRsZXIpO1xuICAgICAgICB9IGVsc2UgaWYgKHJvbGUgPT09IEhhbmRsZXJSb2xlLlRBUkdFVCkge1xuICAgICAgICAgICAgdGhpcy5kcm9wVGFyZ2V0cy5zZXQoaWQsIGhhbmRsZXIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBpZDtcbiAgICB9XG4gICAgY29uc3RydWN0b3Ioc3RvcmUpe1xuICAgICAgICB0aGlzLnR5cGVzID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLmRyYWdTb3VyY2VzID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLmRyb3BUYXJnZXRzID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLnBpbm5lZFNvdXJjZUlkID0gbnVsbDtcbiAgICAgICAgdGhpcy5waW5uZWRTb3VyY2UgPSBudWxsO1xuICAgICAgICB0aGlzLnN0b3JlID0gc3RvcmU7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1IYW5kbGVyUmVnaXN0cnlJbXBsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/contracts.js":
/*!*************************************************!*\
  !*** ./node_modules/dnd-core/dist/contracts.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSourceContract: () => (/* binding */ validateSourceContract),\n/* harmony export */   validateTargetContract: () => (/* binding */ validateTargetContract),\n/* harmony export */   validateType: () => (/* binding */ validateType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n\nfunction validateSourceContract(source) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.canDrag === 'function', 'Expected canDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.beginDrag === 'function', 'Expected beginDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.endDrag === 'function', 'Expected endDrag to be a function.');\n}\nfunction validateTargetContract(target) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.canDrop === 'function', 'Expected canDrop to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.hover === 'function', 'Expected hover to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.drop === 'function', 'Expected beginDrag to be a function.');\n}\nfunction validateType(type, allowArray) {\n    if (allowArray && Array.isArray(type)) {\n        type.forEach((t)=>validateType(t, false)\n        );\n        return;\n    }\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof type === 'string' || typeof type === 'symbol', allowArray ? 'Type can only be a string, a symbol, or an array of either.' : 'Type can only be a string or a symbol.');\n}\n\n//# sourceMappingURL=contracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/contracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/createDragDropManager.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDragDropManager: () => (/* binding */ createDragDropManager)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/es/redux.js\");\n/* harmony import */ var _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./classes/DragDropManagerImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\");\n/* harmony import */ var _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/DragDropMonitorImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\");\n/* harmony import */ var _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/HandlerRegistryImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\");\n/* harmony import */ var _reducers_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./reducers/index.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/index.js\");\n\n\n\n\n\nfunction createDragDropManager(backendFactory, globalContext = undefined, backendOptions = {}, debugMode = false) {\n    const store = makeStoreInstance(debugMode);\n    const monitor = new _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__.DragDropMonitorImpl(store, new _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__.HandlerRegistryImpl(store));\n    const manager = new _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__.DragDropManagerImpl(store, monitor);\n    const backend = backendFactory(manager, globalContext, backendOptions);\n    manager.receiveBackend(backend);\n    return manager;\n}\nfunction makeStoreInstance(debugMode) {\n    // TODO: if we ever make a react-native version of this,\n    // we'll need to consider how to pull off dev-tooling\n    const reduxDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__;\n    return (0,redux__WEBPACK_IMPORTED_MODULE_3__.createStore)(_reducers_index_js__WEBPACK_IMPORTED_MODULE_4__.reduce, debugMode && reduxDevTools && reduxDevTools({\n        name: 'dnd-core',\n        instanceId: 'dnd-core'\n    }));\n}\n\n//# sourceMappingURL=createDragDropManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/interfaces.js":
/*!**************************************************!*\
  !*** ./node_modules/dnd-core/dist/interfaces.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRole: () => (/* binding */ HandlerRole)\n/* harmony export */ });\nvar HandlerRole;\n(function(HandlerRole) {\n    HandlerRole[\"SOURCE\"] = \"SOURCE\";\n    HandlerRole[\"TARGET\"] = \"TARGET\";\n})(HandlerRole || (HandlerRole = {}));\n\n//# sourceMappingURL=interfaces.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9pbnRlcmZhY2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsa0NBQWtDOztBQUVuQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxQcm9qZWN0c1xcTW9vbmVsZWNBcHBcXG1vb25lbGVjLWFwcFxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcaW50ZXJmYWNlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIEhhbmRsZXJSb2xlO1xuKGZ1bmN0aW9uKEhhbmRsZXJSb2xlKSB7XG4gICAgSGFuZGxlclJvbGVbXCJTT1VSQ0VcIl0gPSBcIlNPVVJDRVwiO1xuICAgIEhhbmRsZXJSb2xlW1wiVEFSR0VUXCJdID0gXCJUQVJHRVRcIjtcbn0pKEhhbmRsZXJSb2xlIHx8IChIYW5kbGVyUm9sZSA9IHt9KSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludGVyZmFjZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/interfaces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\n\n\n\n\nfunction reduce(// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_state = _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE, action) {\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.HOVER:\n            break;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_SOURCE:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.PUBLISH_DRAG_SOURCE:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.DROP:\n        default:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.ALL;\n    }\n    const { targetIds =[] , prevTargetIds =[]  } = action.payload;\n    const result = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.xor)(targetIds, prevTargetIds);\n    const didChange = result.length > 0 || !(0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_4__.areArraysEqual)(targetIds, prevTargetIds);\n    if (!didChange) {\n        return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n    }\n    // Check the target ids at the innermost position. If they are valid, add them\n    // to the result\n    const prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1];\n    const innermostTargetId = targetIds[targetIds.length - 1];\n    if (prevInnermostTargetId !== innermostTargetId) {\n        if (prevInnermostTargetId) {\n            result.push(prevInnermostTargetId);\n        }\n        if (innermostTargetId) {\n            result.push(innermostTargetId);\n        }\n    }\n    return result;\n}\n\n//# sourceMappingURL=dirtyHandlerIds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js":
/*!***********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOffset.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\nconst initialState = {\n    initialSourceClientOffset: null,\n    initialClientOffset: null,\n    clientOffset: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return {\n                initialSourceClientOffset: payload.sourceClientOffset,\n                initialClientOffset: payload.clientOffset,\n                clientOffset: payload.clientOffset\n            };\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            if ((0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_1__.areCoordsEqual)(state.clientOffset, payload.clientOffset)) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                clientOffset: payload.clientOffset\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOperation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nconst initialState = {\n    itemType: null,\n    item: null,\n    sourceId: null,\n    targetIds: [],\n    dropResult: null,\n    didDrop: false,\n    isSourcePublic: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return _objectSpread({}, state, {\n                itemType: payload.itemType,\n                item: payload.item,\n                sourceId: payload.sourceId,\n                isSourcePublic: payload.isSourcePublic,\n                dropResult: null,\n                didDrop: false\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE:\n            return _objectSpread({}, state, {\n                isSourcePublic: true\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            return _objectSpread({}, state, {\n                targetIds: payload.targetIds\n            });\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__.REMOVE_TARGET:\n            if (state.targetIds.indexOf(payload.targetId) === -1) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                targetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.without)(state.targetIds, payload.targetId)\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return _objectSpread({}, state, {\n                dropResult: payload.dropResult,\n                didDrop: true,\n                targetIds: []\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n            return _objectSpread({}, state, {\n                itemType: null,\n                item: null,\n                sourceId: null,\n                dropResult: null,\n                didDrop: false,\n                isSourcePublic: null,\n                targetIds: []\n            });\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOperation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/index.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dirtyHandlerIds.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\");\n/* harmony import */ var _dragOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dragOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\");\n/* harmony import */ var _dragOperation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dragOperation.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\");\n/* harmony import */ var _refCount_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./refCount.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\");\n/* harmony import */ var _stateId_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./stateId.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\n\n\n\nfunction reduce(state = {}, action) {\n    return {\n        dirtyHandlerIds: (0,_dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__.reduce)(state.dirtyHandlerIds, {\n            type: action.type,\n            payload: _objectSpread({}, action.payload, {\n                prevTargetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__.get)(state, 'dragOperation.targetIds', [])\n            })\n        }),\n        dragOffset: (0,_dragOffset_js__WEBPACK_IMPORTED_MODULE_2__.reduce)(state.dragOffset, action),\n        refCount: (0,_refCount_js__WEBPACK_IMPORTED_MODULE_3__.reduce)(state.refCount, action),\n        dragOperation: (0,_dragOperation_js__WEBPACK_IMPORTED_MODULE_4__.reduce)(state.dragOperation, action),\n        stateId: (0,_stateId_js__WEBPACK_IMPORTED_MODULE_5__.reduce)(state.stateId)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/refCount.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n\nfunction reduce(state = 0, action) {\n    switch(action.type){\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_TARGET:\n            return state + 1;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TARGET:\n            return state - 1;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=refCount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9yZWZDb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4RjtBQUN2RjtBQUNQO0FBQ0EsYUFBYSw0REFBVTtBQUN2QixhQUFhLDREQUFVO0FBQ3ZCO0FBQ0EsYUFBYSwrREFBYTtBQUMxQixhQUFhLCtEQUFhO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHJlZHVjZXJzXFxyZWZDb3VudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBRERfU09VUkNFLCBBRERfVEFSR0VULCBSRU1PVkVfU09VUkNFLCBSRU1PVkVfVEFSR0VUIH0gZnJvbSAnLi4vYWN0aW9ucy9yZWdpc3RyeS5qcyc7XG5leHBvcnQgZnVuY3Rpb24gcmVkdWNlKHN0YXRlID0gMCwgYWN0aW9uKSB7XG4gICAgc3dpdGNoKGFjdGlvbi50eXBlKXtcbiAgICAgICAgY2FzZSBBRERfU09VUkNFOlxuICAgICAgICBjYXNlIEFERF9UQVJHRVQ6XG4gICAgICAgICAgICByZXR1cm4gc3RhdGUgKyAxO1xuICAgICAgICBjYXNlIFJFTU9WRV9TT1VSQ0U6XG4gICAgICAgIGNhc2UgUkVNT1ZFX1RBUkdFVDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZSAtIDE7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gc3RhdGU7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWZDb3VudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/stateId.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(state = 0) {\n    return state + 1;\n}\n\n//# sourceMappingURL=stateId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9zdGF0ZUlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHJlZHVjZXJzXFxzdGF0ZUlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiByZWR1Y2Uoc3RhdGUgPSAwKSB7XG4gICAgcmV0dXJuIHN0YXRlICsgMTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RhdGVJZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/coords.js":
/*!****************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/coords.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   getDifferenceFromInitialOffset: () => (/* binding */ getDifferenceFromInitialOffset),\n/* harmony export */   getSourceClientOffset: () => (/* binding */ getSourceClientOffset),\n/* harmony export */   subtract: () => (/* binding */ subtract)\n/* harmony export */ });\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */ function add(a, b) {\n    return {\n        x: a.x + b.x,\n        y: a.y + b.y\n    };\n}\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */ function subtract(a, b) {\n    return {\n        x: a.x - b.x,\n        y: a.y - b.y\n    };\n}\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */ function getSourceClientOffset(state) {\n    const { clientOffset , initialClientOffset , initialSourceClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n        return null;\n    }\n    return subtract(add(clientOffset, initialSourceClientOffset), initialClientOffset);\n}\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */ function getDifferenceFromInitialOffset(state) {\n    const { clientOffset , initialClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset) {\n        return null;\n    }\n    return subtract(clientOffset, initialClientOffset);\n}\n\n//# sourceMappingURL=coords.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/coords.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js":
/*!*******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/dirtiness.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL: () => (/* binding */ ALL),\n/* harmony export */   NONE: () => (/* binding */ NONE),\n/* harmony export */   areDirty: () => (/* binding */ areDirty)\n/* harmony export */ });\n/* harmony import */ var _js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\nconst NONE = [];\nconst ALL = [];\nNONE.__IS_NONE__ = true;\nALL.__IS_ALL__ = true;\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */ function areDirty(dirtyIds, handlerIds) {\n    if (dirtyIds === NONE) {\n        return false;\n    }\n    if (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n        return true;\n    }\n    const commonIds = (0,_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.intersection)(handlerIds, dirtyIds);\n    return commonIds.length > 0;\n}\n\n//# sourceMappingURL=dirtiness.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9kaXJ0aW5lc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2QztBQUN0QztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFZO0FBQ2xDO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHV0aWxzXFxkaXJ0aW5lc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW50ZXJzZWN0aW9uIH0gZnJvbSAnLi9qc191dGlscy5qcyc7XG5leHBvcnQgY29uc3QgTk9ORSA9IFtdO1xuZXhwb3J0IGNvbnN0IEFMTCA9IFtdO1xuTk9ORS5fX0lTX05PTkVfXyA9IHRydWU7XG5BTEwuX19JU19BTExfXyA9IHRydWU7XG4vKipcbiAqIERldGVybWluZXMgaWYgdGhlIGdpdmVuIGhhbmRsZXIgSURzIGFyZSBkaXJ0eSBvciBub3QuXG4gKlxuICogQHBhcmFtIGRpcnR5SWRzIFRoZSBzZXQgb2YgZGlydHkgaGFuZGxlciBpZHNcbiAqIEBwYXJhbSBoYW5kbGVySWRzIFRoZSBzZXQgb2YgaGFuZGxlciBpZHMgdG8gY2hlY2tcbiAqLyBleHBvcnQgZnVuY3Rpb24gYXJlRGlydHkoZGlydHlJZHMsIGhhbmRsZXJJZHMpIHtcbiAgICBpZiAoZGlydHlJZHMgPT09IE5PTkUpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoZGlydHlJZHMgPT09IEFMTCB8fCB0eXBlb2YgaGFuZGxlcklkcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGNvbnN0IGNvbW1vbklkcyA9IGludGVyc2VjdGlvbihoYW5kbGVySWRzLCBkaXJ0eUlkcyk7XG4gICAgcmV0dXJuIGNvbW1vbklkcy5sZW5ndGggPiAwO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1kaXJ0aW5lc3MuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/equality.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/equality.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areArraysEqual: () => (/* binding */ areArraysEqual),\n/* harmony export */   areCoordsEqual: () => (/* binding */ areCoordsEqual),\n/* harmony export */   strictEquality: () => (/* binding */ strictEquality)\n/* harmony export */ });\nconst strictEquality = (a, b)=>a === b\n;\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */ function areCoordsEqual(offsetA, offsetB) {\n    if (!offsetA && !offsetB) {\n        return true;\n    } else if (!offsetA || !offsetB) {\n        return false;\n    } else {\n        return offsetA.x === offsetB.x && offsetA.y === offsetB.y;\n    }\n}\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */ function areArraysEqual(a, b, isEqual = strictEquality) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    for(let i = 0; i < a.length; ++i){\n        if (!isEqual(a[i], b[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\n//# sourceMappingURL=equality.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9lcXVhbGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsY0FBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHV0aWxzXFxlcXVhbGl0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc3RyaWN0RXF1YWxpdHkgPSAoYSwgYik9PmEgPT09IGJcbjtcbi8qKlxuICogRGV0ZXJtaW5lIGlmIHR3byBjYXJ0ZXNpYW4gY29vcmRpbmF0ZSBvZmZzZXRzIGFyZSBlcXVhbFxuICogQHBhcmFtIG9mZnNldEFcbiAqIEBwYXJhbSBvZmZzZXRCXG4gKi8gZXhwb3J0IGZ1bmN0aW9uIGFyZUNvb3Jkc0VxdWFsKG9mZnNldEEsIG9mZnNldEIpIHtcbiAgICBpZiAoIW9mZnNldEEgJiYgIW9mZnNldEIpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBlbHNlIGlmICghb2Zmc2V0QSB8fCAhb2Zmc2V0Qikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIG9mZnNldEEueCA9PT0gb2Zmc2V0Qi54ICYmIG9mZnNldEEueSA9PT0gb2Zmc2V0Qi55O1xuICAgIH1cbn1cbi8qKlxuICogRGV0ZXJtaW5lcyBpZiB0d28gYXJyYXlzIG9mIGl0ZW1zIGFyZSBlcXVhbFxuICogQHBhcmFtIGEgVGhlIGZpcnN0IGFycmF5IG9mIGl0ZW1zXG4gKiBAcGFyYW0gYiBUaGUgc2Vjb25kIGFycmF5IG9mIGl0ZW1zXG4gKi8gZXhwb3J0IGZ1bmN0aW9uIGFyZUFycmF5c0VxdWFsKGEsIGIsIGlzRXF1YWwgPSBzdHJpY3RFcXVhbGl0eSkge1xuICAgIGlmIChhLmxlbmd0aCAhPT0gYi5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBmb3IobGV0IGkgPSAwOyBpIDwgYS5sZW5ndGg7ICsraSl7XG4gICAgICAgIGlmICghaXNFcXVhbChhW2ldLCBiW2ldKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1lcXVhbGl0eS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/equality.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/getNextUniqueId.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextUniqueId: () => (/* binding */ getNextUniqueId)\n/* harmony export */ });\nlet nextUniqueId = 0;\nfunction getNextUniqueId() {\n    return nextUniqueId++;\n}\n\n//# sourceMappingURL=getNextUniqueId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9nZXROZXh0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFx1dGlsc1xcZ2V0TmV4dFVuaXF1ZUlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBuZXh0VW5pcXVlSWQgPSAwO1xuZXhwb3J0IGZ1bmN0aW9uIGdldE5leHRVbmlxdWVJZCgpIHtcbiAgICByZXR1cm4gbmV4dFVuaXF1ZUlkKys7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldE5leHRVbmlxdWVJZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/js_utils.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   without: () => (/* binding */ without),\n/* harmony export */   xor: () => (/* binding */ xor)\n/* harmony export */ });\n// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */ function get(obj, path, defaultValue) {\n    return path.split('.').reduce((a, c)=>a && a[c] ? a[c] : defaultValue || null\n    , obj);\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter((i)=>i !== item\n    );\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isString(input) {\n    return typeof input === 'string';\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isObject(input) {\n    return typeof input === 'object';\n}\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */ function xor(itemsA, itemsB) {\n    const map = new Map();\n    const insertItem = (item)=>{\n        map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n    };\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    const result = [];\n    map.forEach((count, key)=>{\n        if (count === 1) {\n            result.push(key);\n        }\n    });\n    return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */ function intersection(itemsA, itemsB) {\n    return itemsA.filter((t)=>itemsB.indexOf(t) > -1\n    );\n}\n\n//# sourceMappingURL=js_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/matchesType.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchesType: () => (/* binding */ matchesType)\n/* harmony export */ });\nfunction matchesType(targetType, draggedItemType) {\n    if (draggedItemType === null) {\n        return targetType === null;\n    }\n    return Array.isArray(targetType) ? targetType.some((t)=>t === draggedItemType\n    ) : targetType === draggedItemType;\n}\n\n//# sourceMappingURL=matchesType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9tYXRjaGVzVHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHV0aWxzXFxtYXRjaGVzVHlwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gbWF0Y2hlc1R5cGUodGFyZ2V0VHlwZSwgZHJhZ2dlZEl0ZW1UeXBlKSB7XG4gICAgaWYgKGRyYWdnZWRJdGVtVHlwZSA9PT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gdGFyZ2V0VHlwZSA9PT0gbnVsbDtcbiAgICB9XG4gICAgcmV0dXJuIEFycmF5LmlzQXJyYXkodGFyZ2V0VHlwZSkgPyB0YXJnZXRUeXBlLnNvbWUoKHQpPT50ID09PSBkcmFnZ2VkSXRlbVR5cGVcbiAgICApIDogdGFyZ2V0VHlwZSA9PT0gZHJhZ2dlZEl0ZW1UeXBlO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYXRjaGVzVHlwZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\n");

/***/ })

};
;