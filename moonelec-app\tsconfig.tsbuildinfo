{"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.es2021.d.ts", "./node_modules/typescript/lib/lib.es2022.d.ts", "./node_modules/typescript/lib/lib.es2023.d.ts", "./node_modules/typescript/lib/lib.es2024.d.ts", "./node_modules/typescript/lib/lib.esnext.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2016.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.es2021.promise.d.ts", "./node_modules/typescript/lib/lib.es2021.string.d.ts", "./node_modules/typescript/lib/lib.es2021.weakref.d.ts", "./node_modules/typescript/lib/lib.es2021.intl.d.ts", "./node_modules/typescript/lib/lib.es2022.array.d.ts", "./node_modules/typescript/lib/lib.es2022.error.d.ts", "./node_modules/typescript/lib/lib.es2022.intl.d.ts", "./node_modules/typescript/lib/lib.es2022.object.d.ts", "./node_modules/typescript/lib/lib.es2022.string.d.ts", "./node_modules/typescript/lib/lib.es2022.regexp.d.ts", "./node_modules/typescript/lib/lib.es2023.array.d.ts", "./node_modules/typescript/lib/lib.es2023.collection.d.ts", "./node_modules/typescript/lib/lib.es2023.intl.d.ts", "./node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2024.collection.d.ts", "./node_modules/typescript/lib/lib.es2024.object.d.ts", "./node_modules/typescript/lib/lib.es2024.promise.d.ts", "./node_modules/typescript/lib/lib.es2024.regexp.d.ts", "./node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2024.string.d.ts", "./node_modules/typescript/lib/lib.esnext.array.d.ts", "./node_modules/typescript/lib/lib.esnext.collection.d.ts", "./node_modules/typescript/lib/lib.esnext.intl.d.ts", "./node_modules/typescript/lib/lib.esnext.disposable.d.ts", "./node_modules/typescript/lib/lib.esnext.promise.d.ts", "./node_modules/typescript/lib/lib.esnext.decorators.d.ts", "./node_modules/typescript/lib/lib.esnext.iterator.d.ts", "./node_modules/typescript/lib/lib.esnext.float16.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@types/react/global.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@types/react/index.d.ts", "./node_modules/next/dist/styled-jsx/types/css.d.ts", "./node_modules/next/dist/styled-jsx/types/macro.d.ts", "./node_modules/next/dist/styled-jsx/types/style.d.ts", "./node_modules/next/dist/styled-jsx/types/global.d.ts", "./node_modules/next/dist/styled-jsx/types/index.d.ts", "./node_modules/next/dist/shared/lib/amp.d.ts", "./node_modules/next/amp.d.ts", "./node_modules/next/dist/server/get-page-files.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/globals.typedarray.d.ts", "./node_modules/@types/node/buffer.buffer.d.ts", "./node_modules/buffer/index.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/env-http-proxy-agent.d.ts", "./node_modules/undici-types/retry-handler.d.ts", "./node_modules/undici-types/retry-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/util.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/eventsource.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/sea.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/index.d.ts", "./node_modules/@types/react/canary.d.ts", "./node_modules/@types/react/experimental.d.ts", "./node_modules/@types/react-dom/index.d.ts", "./node_modules/@types/react-dom/canary.d.ts", "./node_modules/@types/react-dom/experimental.d.ts", "./node_modules/next/dist/lib/fallback.d.ts", "./node_modules/next/dist/compiled/webpack/webpack.d.ts", "./node_modules/next/dist/server/config.d.ts", "./node_modules/next/dist/lib/load-custom-routes.d.ts", "./node_modules/next/dist/shared/lib/image-config.d.ts", "./node_modules/next/dist/build/webpack/plugins/subresource-integrity-plugin.d.ts", "./node_modules/next/dist/server/body-streams.d.ts", "./node_modules/next/dist/server/lib/cache-control.d.ts", "./node_modules/next/dist/lib/setup-exception-listeners.d.ts", "./node_modules/next/dist/lib/worker.d.ts", "./node_modules/next/dist/lib/constants.d.ts", "./node_modules/next/dist/client/components/app-router-headers.d.ts", "./node_modules/next/dist/build/rendering-mode.d.ts", "./node_modules/next/dist/server/lib/router-utils/build-prefetch-segment-data-route.d.ts", "./node_modules/next/dist/server/require-hook.d.ts", "./node_modules/next/dist/server/lib/experimental/ppr.d.ts", "./node_modules/next/dist/build/webpack/plugins/app-build-manifest-plugin.d.ts", "./node_modules/next/dist/lib/page-types.d.ts", "./node_modules/next/dist/build/segment-config/app/app-segment-config.d.ts", "./node_modules/next/dist/build/segment-config/pages/pages-segment-config.d.ts", "./node_modules/next/dist/build/analysis/get-page-static-info.d.ts", "./node_modules/next/dist/build/webpack/loaders/get-module-build-info.d.ts", "./node_modules/next/dist/build/webpack/plugins/middleware-plugin.d.ts", "./node_modules/next/dist/server/node-polyfill-crypto.d.ts", "./node_modules/next/dist/server/node-environment-baseline.d.ts", "./node_modules/next/dist/server/node-environment-extensions/error-inspect.d.ts", "./node_modules/next/dist/server/node-environment-extensions/random.d.ts", "./node_modules/next/dist/server/node-environment-extensions/date.d.ts", "./node_modules/next/dist/server/node-environment-extensions/web-crypto.d.ts", "./node_modules/next/dist/server/node-environment-extensions/node-crypto.d.ts", "./node_modules/next/dist/server/node-environment.d.ts", "./node_modules/next/dist/build/page-extensions-type.d.ts", "./node_modules/next/dist/build/webpack/plugins/flight-manifest-plugin.d.ts", "./node_modules/next/dist/server/route-kind.d.ts", "./node_modules/next/dist/server/route-definitions/route-definition.d.ts", "./node_modules/next/dist/server/route-modules/route-module.d.ts", "./node_modules/next/dist/shared/lib/deep-readonly.d.ts", "./node_modules/next/dist/server/load-components.d.ts", "./node_modules/next/dist/server/route-definitions/app-page-route-definition.d.ts", "./node_modules/next/dist/server/lib/cache-handlers/types.d.ts", "./node_modules/next/dist/server/response-cache/types.d.ts", "./node_modules/next/dist/server/resume-data-cache/cache-store.d.ts", "./node_modules/next/dist/server/resume-data-cache/resume-data-cache.d.ts", "./node_modules/next/dist/server/render-result.d.ts", "./node_modules/next/dist/build/webpack/plugins/next-font-manifest-plugin.d.ts", "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.d.ts", "./node_modules/next/dist/client/flight-data-helpers.d.ts", "./node_modules/next/dist/client/components/router-reducer/fetch-server-response.d.ts", "./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/router/utils/middleware-route-matcher.d.ts", "./node_modules/next/dist/server/route-definitions/locale-route-definition.d.ts", "./node_modules/next/dist/server/route-definitions/pages-route-definition.d.ts", "./node_modules/next/dist/shared/lib/mitt.d.ts", "./node_modules/next/dist/client/with-router.d.ts", "./node_modules/next/dist/client/router.d.ts", "./node_modules/next/dist/client/route-loader.d.ts", "./node_modules/next/dist/client/page-loader.d.ts", "./node_modules/next/dist/shared/lib/bloom-filter.d.ts", "./node_modules/next/dist/shared/lib/router/router.d.ts", "./node_modules/next/dist/shared/lib/router-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/loadable.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/amp-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.d.ts", "./node_modules/next/dist/server/route-modules/pages/vendored/contexts/entrypoints.d.ts", "./node_modules/next/dist/server/route-modules/pages/module.compiled.d.ts", "./node_modules/next/dist/build/templates/pages.d.ts", "./node_modules/next/dist/server/route-modules/pages/module.d.ts", "./node_modules/@types/react/jsx-runtime.d.ts", "./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.d.ts", "./node_modules/next/dist/server/render.d.ts", "./node_modules/next/dist/server/response-cache/index.d.ts", "./node_modules/next/dist/build/webpack/plugins/pages-manifest-plugin.d.ts", "./node_modules/next/dist/server/route-definitions/pages-api-route-definition.d.ts", "./node_modules/next/dist/server/route-matches/pages-api-route-match.d.ts", "./node_modules/next/dist/server/instrumentation/types.d.ts", "./node_modules/next/dist/server/route-matchers/route-matcher.d.ts", "./node_modules/next/dist/server/route-matcher-providers/route-matcher-provider.d.ts", "./node_modules/next/dist/server/lib/i18n-provider.d.ts", "./node_modules/next/dist/server/route-matcher-managers/route-matcher-manager.d.ts", "./node_modules/next/dist/server/normalizers/normalizer.d.ts", "./node_modules/next/dist/server/normalizers/locale-route-normalizer.d.ts", "./node_modules/next/dist/server/normalizers/request/pathname-normalizer.d.ts", "./node_modules/next/dist/server/normalizers/request/suffix.d.ts", "./node_modules/next/dist/server/normalizers/request/rsc.d.ts", "./node_modules/next/dist/server/normalizers/request/prefetch-rsc.d.ts", "./node_modules/next/dist/server/normalizers/request/next-data.d.ts", "./node_modules/next/dist/server/after/builtin-request-context.d.ts", "./node_modules/next/dist/server/normalizers/request/segment-prefix-rsc.d.ts", "./node_modules/next/dist/server/base-server.d.ts", "./node_modules/next/dist/server/web/next-url.d.ts", "./node_modules/next/dist/compiled/@edge-runtime/cookies/index.d.ts", "./node_modules/next/dist/server/web/spec-extension/cookies.d.ts", "./node_modules/next/dist/server/web/spec-extension/request.d.ts", "./node_modules/next/dist/server/web/spec-extension/fetch-event.d.ts", "./node_modules/next/dist/server/web/spec-extension/response.d.ts", "./node_modules/next/dist/build/segment-config/middleware/middleware-config.d.ts", "./node_modules/next/dist/server/web/types.d.ts", "./node_modules/next/dist/server/web/adapter.d.ts", "./node_modules/next/dist/server/use-cache/cache-life.d.ts", "./node_modules/next/dist/server/app-render/types.d.ts", "./node_modules/next/dist/shared/lib/modern-browserslist-target.d.ts", "./node_modules/next/dist/shared/lib/constants.d.ts", "./node_modules/next/dist/build/webpack/loaders/metadata/types.d.ts", "./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.d.ts", "./node_modules/next/dist/server/lib/app-dir-module.d.ts", "./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.d.ts", "./node_modules/next/dist/server/async-storage/draft-mode-provider.d.ts", "./node_modules/next/dist/server/web/spec-extension/adapters/headers.d.ts", "./node_modules/next/dist/server/app-render/cache-signal.d.ts", "./node_modules/next/dist/server/app-render/dynamic-rendering.d.ts", "./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.d.ts", "./node_modules/next/dist/server/request/fallback-params.d.ts", "./node_modules/next/dist/server/lib/lazy-result.d.ts", "./node_modules/next/dist/server/lib/implicit-tags.d.ts", "./node_modules/next/dist/server/app-render/work-unit-async-storage.external.d.ts", "./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.d.ts", "./node_modules/next/dist/server/app-render/clean-async-snapshot-instance.d.ts", "./node_modules/next/dist/server/app-render/clean-async-snapshot.external.d.ts", "./node_modules/next/dist/server/app-render/app-render.d.ts", "./node_modules/next/dist/shared/lib/server-inserted-metadata.shared-runtime.d.ts", "./node_modules/next/dist/server/route-modules/app-page/vendored/contexts/entrypoints.d.ts", "./node_modules/next/dist/client/components/error-boundary.d.ts", "./node_modules/next/dist/client/components/layout-router.d.ts", "./node_modules/next/dist/client/components/render-from-template-context.d.ts", "./node_modules/next/dist/server/app-render/action-async-storage-instance.d.ts", "./node_modules/next/dist/server/app-render/action-async-storage.external.d.ts", "./node_modules/next/dist/client/components/client-page.d.ts", "./node_modules/next/dist/client/components/client-segment.d.ts", "./node_modules/next/dist/server/request/search-params.d.ts", "./node_modules/next/dist/client/components/hooks-server-context.d.ts", "./node_modules/next/dist/client/components/http-access-fallback/error-boundary.d.ts", "./node_modules/next/dist/lib/metadata/types/alternative-urls-types.d.ts", "./node_modules/next/dist/lib/metadata/types/extra-types.d.ts", "./node_modules/next/dist/lib/metadata/types/metadata-types.d.ts", "./node_modules/next/dist/lib/metadata/types/manifest-types.d.ts", "./node_modules/next/dist/lib/metadata/types/opengraph-types.d.ts", "./node_modules/next/dist/lib/metadata/types/twitter-types.d.ts", "./node_modules/next/dist/lib/metadata/types/metadata-interface.d.ts", "./node_modules/next/dist/lib/metadata/types/resolvers.d.ts", "./node_modules/next/dist/lib/metadata/types/icons.d.ts", "./node_modules/next/dist/lib/metadata/resolve-metadata.d.ts", "./node_modules/next/dist/lib/metadata/metadata.d.ts", "./node_modules/next/dist/client/components/metadata/metadata-boundary.d.ts", "./node_modules/next/dist/server/app-render/rsc/preloads.d.ts", "./node_modules/next/dist/server/app-render/rsc/postpone.d.ts", "./node_modules/next/dist/server/app-render/rsc/taint.d.ts", "./node_modules/next/dist/server/app-render/collect-segment-data.d.ts", "./node_modules/next/dist/server/app-render/entry-base.d.ts", "./node_modules/next/dist/build/templates/app-page.d.ts", "./node_modules/next/dist/server/route-modules/app-page/module.d.ts", "./node_modules/next/dist/server/route-modules/app-page/module.compiled.d.ts", "./node_modules/next/dist/server/route-definitions/app-route-route-definition.d.ts", "./node_modules/next/dist/server/async-storage/work-store.d.ts", "./node_modules/next/dist/server/web/http.d.ts", "./node_modules/next/dist/server/route-modules/app-route/shared-modules.d.ts", "./node_modules/next/dist/client/components/redirect-status-code.d.ts", "./node_modules/next/dist/client/components/redirect-error.d.ts", "./node_modules/next/dist/build/templates/app-route.d.ts", "./node_modules/next/dist/server/route-modules/app-route/module.d.ts", "./node_modules/next/dist/server/route-modules/app-route/module.compiled.d.ts", "./node_modules/next/dist/build/segment-config/app/app-segments.d.ts", "./node_modules/next/dist/build/static-paths/types.d.ts", "./node_modules/next/dist/build/utils.d.ts", "./node_modules/next/dist/build/turborepo-access-trace/types.d.ts", "./node_modules/next/dist/build/turborepo-access-trace/result.d.ts", "./node_modules/next/dist/build/turborepo-access-trace/helpers.d.ts", "./node_modules/next/dist/build/turborepo-access-trace/index.d.ts", "./node_modules/next/dist/export/routes/types.d.ts", "./node_modules/next/dist/export/types.d.ts", "./node_modules/next/dist/export/worker.d.ts", "./node_modules/next/dist/build/worker.d.ts", "./node_modules/next/dist/build/index.d.ts", "./node_modules/next/dist/server/lib/incremental-cache/index.d.ts", "./node_modules/next/dist/server/after/after.d.ts", "./node_modules/next/dist/server/after/after-context.d.ts", "./node_modules/next/dist/server/app-render/work-async-storage-instance.d.ts", "./node_modules/next/dist/server/app-render/work-async-storage.external.d.ts", "./node_modules/next/dist/server/request/params.d.ts", "./node_modules/next/dist/server/route-matches/route-match.d.ts", "./node_modules/next/dist/server/request-meta.d.ts", "./node_modules/next/dist/cli/next-test.d.ts", "./node_modules/next/dist/server/config-shared.d.ts", "./node_modules/next/dist/server/base-http/index.d.ts", "./node_modules/next/dist/server/api-utils/index.d.ts", "./node_modules/next/dist/shared/lib/router/utils/parse-url.d.ts", "./node_modules/next/dist/server/base-http/node.d.ts", "./node_modules/next/dist/server/lib/async-callback-set.d.ts", "./node_modules/next/dist/shared/lib/router/utils/route-regex.d.ts", "./node_modules/next/dist/shared/lib/router/utils/route-matcher.d.ts", "./node_modules/sharp/lib/index.d.ts", "./node_modules/next/dist/server/image-optimizer.d.ts", "./node_modules/next/dist/server/next-server.d.ts", "./node_modules/next/dist/lib/coalesced-function.d.ts", "./node_modules/next/dist/server/lib/router-utils/types.d.ts", "./node_modules/next/dist/trace/types.d.ts", "./node_modules/next/dist/trace/trace.d.ts", "./node_modules/next/dist/trace/shared.d.ts", "./node_modules/next/dist/trace/index.d.ts", "./node_modules/next/dist/build/load-jsconfig.d.ts", "./node_modules/next/dist/build/webpack-config.d.ts", "./node_modules/next/dist/build/swc/generated-native.d.ts", "./node_modules/next/dist/build/swc/types.d.ts", "./node_modules/next/dist/server/dev/parse-version-info.d.ts", "./node_modules/next/dist/client/components/react-dev-overlay/types.d.ts", "./node_modules/next/dist/server/dev/dev-indicator-server-state.d.ts", "./node_modules/next/dist/server/dev/hot-reloader-types.d.ts", "./node_modules/next/dist/telemetry/storage.d.ts", "./node_modules/next/dist/server/lib/render-server.d.ts", "./node_modules/next/dist/server/lib/router-server.d.ts", "./node_modules/next/dist/shared/lib/router/utils/path-match.d.ts", "./node_modules/next/dist/server/lib/router-utils/filesystem.d.ts", "./node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.d.ts", "./node_modules/next/dist/server/lib/types.d.ts", "./node_modules/next/dist/server/lib/lru-cache.d.ts", "./node_modules/next/dist/server/lib/dev-bundler-service.d.ts", "./node_modules/next/dist/server/dev/static-paths-worker.d.ts", "./node_modules/next/dist/server/dev/next-dev-server.d.ts", "./node_modules/next/dist/server/next.d.ts", "./node_modules/next/dist/types.d.ts", "./node_modules/next/dist/shared/lib/html-context.shared-runtime.d.ts", "./node_modules/@next/env/dist/index.d.ts", "./node_modules/next/dist/shared/lib/utils.d.ts", "./node_modules/next/dist/pages/_app.d.ts", "./node_modules/next/app.d.ts", "./node_modules/next/dist/server/web/spec-extension/unstable-cache.d.ts", "./node_modules/next/dist/server/web/spec-extension/revalidate.d.ts", "./node_modules/next/dist/server/web/spec-extension/unstable-no-store.d.ts", "./node_modules/next/dist/server/use-cache/cache-tag.d.ts", "./node_modules/next/cache.d.ts", "./node_modules/next/dist/shared/lib/runtime-config.external.d.ts", "./node_modules/next/config.d.ts", "./node_modules/next/dist/pages/_document.d.ts", "./node_modules/next/document.d.ts", "./node_modules/next/dist/shared/lib/dynamic.d.ts", "./node_modules/next/dynamic.d.ts", "./node_modules/next/dist/pages/_error.d.ts", "./node_modules/next/error.d.ts", "./node_modules/next/dist/shared/lib/head.d.ts", "./node_modules/next/head.d.ts", "./node_modules/next/dist/server/request/cookies.d.ts", "./node_modules/next/dist/server/request/headers.d.ts", "./node_modules/next/dist/server/request/draft-mode.d.ts", "./node_modules/next/headers.d.ts", "./node_modules/next/dist/shared/lib/get-img-props.d.ts", "./node_modules/next/dist/client/image-component.d.ts", "./node_modules/next/dist/shared/lib/image-external.d.ts", "./node_modules/next/image.d.ts", "./node_modules/next/dist/client/link.d.ts", "./node_modules/next/link.d.ts", "./node_modules/next/dist/client/components/redirect.d.ts", "./node_modules/next/dist/client/components/not-found.d.ts", "./node_modules/next/dist/client/components/forbidden.d.ts", "./node_modules/next/dist/client/components/unauthorized.d.ts", "./node_modules/next/dist/client/components/unstable-rethrow.server.d.ts", "./node_modules/next/dist/client/components/unstable-rethrow.d.ts", "./node_modules/next/dist/client/components/navigation.react-server.d.ts", "./node_modules/next/dist/client/components/navigation.d.ts", "./node_modules/next/navigation.d.ts", "./node_modules/next/router.d.ts", "./node_modules/next/dist/client/script.d.ts", "./node_modules/next/script.d.ts", "./node_modules/next/dist/server/web/spec-extension/user-agent.d.ts", "./node_modules/next/dist/compiled/@edge-runtime/primitives/url.d.ts", "./node_modules/next/dist/server/web/spec-extension/image-response.d.ts", "./node_modules/next/dist/compiled/@vercel/og/satori/index.d.ts", "./node_modules/next/dist/compiled/@vercel/og/emoji/index.d.ts", "./node_modules/next/dist/compiled/@vercel/og/types.d.ts", "./node_modules/next/dist/server/after/index.d.ts", "./node_modules/next/dist/server/request/root-params.d.ts", "./node_modules/next/dist/server/request/connection.d.ts", "./node_modules/next/server.d.ts", "./node_modules/next/types/global.d.ts", "./node_modules/next/types/compiled.d.ts", "./node_modules/next/types.d.ts", "./node_modules/next/index.d.ts", "./node_modules/next/image-types/global.d.ts", "./next-env.d.ts", "./node_modules/next-auth/adapters.d.ts", "./node_modules/jose/dist/types/types.d.ts", "./node_modules/jose/dist/types/jwe/compact/decrypt.d.ts", "./node_modules/jose/dist/types/jwe/flattened/decrypt.d.ts", "./node_modules/jose/dist/types/jwe/general/decrypt.d.ts", "./node_modules/jose/dist/types/jwe/general/encrypt.d.ts", "./node_modules/jose/dist/types/jws/compact/verify.d.ts", "./node_modules/jose/dist/types/jws/flattened/verify.d.ts", "./node_modules/jose/dist/types/jws/general/verify.d.ts", "./node_modules/jose/dist/types/jwt/verify.d.ts", "./node_modules/jose/dist/types/jwt/decrypt.d.ts", "./node_modules/jose/dist/types/jwt/produce.d.ts", "./node_modules/jose/dist/types/jwe/compact/encrypt.d.ts", "./node_modules/jose/dist/types/jwe/flattened/encrypt.d.ts", "./node_modules/jose/dist/types/jws/compact/sign.d.ts", "./node_modules/jose/dist/types/jws/flattened/sign.d.ts", "./node_modules/jose/dist/types/jws/general/sign.d.ts", "./node_modules/jose/dist/types/jwt/sign.d.ts", "./node_modules/jose/dist/types/jwt/encrypt.d.ts", "./node_modules/jose/dist/types/jwk/thumbprint.d.ts", "./node_modules/jose/dist/types/jwk/embedded.d.ts", "./node_modules/jose/dist/types/jwks/local.d.ts", "./node_modules/jose/dist/types/jwks/remote.d.ts", "./node_modules/jose/dist/types/jwt/unsecured.d.ts", "./node_modules/jose/dist/types/key/export.d.ts", "./node_modules/jose/dist/types/key/import.d.ts", "./node_modules/jose/dist/types/util/decode_protected_header.d.ts", "./node_modules/jose/dist/types/util/decode_jwt.d.ts", "./node_modules/jose/dist/types/util/errors.d.ts", "./node_modules/jose/dist/types/key/generate_key_pair.d.ts", "./node_modules/jose/dist/types/key/generate_secret.d.ts", "./node_modules/jose/dist/types/util/base64url.d.ts", "./node_modules/jose/dist/types/util/runtime.d.ts", "./node_modules/jose/dist/types/index.d.ts", "./node_modules/openid-client/types/index.d.ts", "./node_modules/next-auth/providers/oauth-types.d.ts", "./node_modules/next-auth/providers/oauth.d.ts", "./node_modules/next-auth/providers/email.d.ts", "./node_modules/next-auth/core/lib/cookie.d.ts", "./node_modules/next-auth/core/index.d.ts", "./node_modules/next-auth/providers/credentials.d.ts", "./node_modules/next-auth/providers/index.d.ts", "./node_modules/next-auth/jwt/types.d.ts", "./node_modules/next-auth/jwt/index.d.ts", "./node_modules/next-auth/utils/logger.d.ts", "./node_modules/next-auth/core/types.d.ts", "./node_modules/next-auth/next/index.d.ts", "./node_modules/next-auth/index.d.ts", "./node_modules/next-auth/next/middleware.d.ts", "./node_modules/next-auth/middleware.d.ts", "./middleware.ts", "./next.config.ts", "./node_modules/bcryptjs/types.d.ts", "./node_modules/bcryptjs/index.d.ts", "./node_modules/@prisma/client/runtime/library.d.ts", "./node_modules/.prisma/client/index.d.ts", "./node_modules/.prisma/client/default.d.ts", "./node_modules/@prisma/client/default.d.ts", "./src/lib/prisma.ts", "./src/lib/auth.ts", "./scripts/create-admin.ts", "./scripts/fix-admin-quotes.ts", "./src/lib/auth-options.ts", "./node_modules/uuid/dist/esm-browser/types.d.ts", "./node_modules/uuid/dist/esm-browser/max.d.ts", "./node_modules/uuid/dist/esm-browser/nil.d.ts", "./node_modules/uuid/dist/esm-browser/parse.d.ts", "./node_modules/uuid/dist/esm-browser/stringify.d.ts", "./node_modules/uuid/dist/esm-browser/v1.d.ts", "./node_modules/uuid/dist/esm-browser/v1tov6.d.ts", "./node_modules/uuid/dist/esm-browser/v35.d.ts", "./node_modules/uuid/dist/esm-browser/v3.d.ts", "./node_modules/uuid/dist/esm-browser/v4.d.ts", "./node_modules/uuid/dist/esm-browser/v5.d.ts", "./node_modules/uuid/dist/esm-browser/v6.d.ts", "./node_modules/uuid/dist/esm-browser/v6tov1.d.ts", "./node_modules/uuid/dist/esm-browser/v7.d.ts", "./node_modules/uuid/dist/esm-browser/validate.d.ts", "./node_modules/uuid/dist/esm-browser/version.d.ts", "./node_modules/uuid/dist/esm-browser/index.d.ts", "./src/app/api/admin/clients/route.ts", "./src/app/api/admin/clients/[id]/route.ts", "./node_modules/xlsx/types/index.d.ts", "./node_modules/@types/pdfkit/index.d.ts", "./src/app/api/admin/clients/export/route.ts", "./src/lib/realtime-data.ts", "./src/middleware/security.ts", "./src/app/api/admin/dashboard/route.ts", "./src/app/api/admin/init/route.ts", "./src/app/api/auth/[...nextauth]/route.ts", "./src/app/api/auth/mobile/route.ts", "./src/app/api/auth/refresh-token/route.ts", "./src/app/api/auth/signup/route.ts", "./src/lib/brands.ts", "./src/app/api/brands/route.ts", "./src/app/api/brands/[id]/route.ts", "./src/lib/categories.ts", "./src/lib/mobile-auth.ts", "./src/app/api/categories/route.ts", "./src/app/api/categories/[id]/route.ts", "./src/app/api/chat/conversations/route.ts", "./src/app/api/chat/messages/route.ts", "./src/lib/upload.ts", "./src/app/api/chat/upload/route.ts", "./src/app/api/chat/users/route.ts", "./src/app/api/clients/dashboard/route.ts", "./src/lib/commercials.ts", "./src/app/api/commercials/route.ts", "./src/app/api/commercials/[id]/route.ts", "./src/app/api/commercials/dashboard/route.ts", "./node_modules/openai/_shims/manual-types.d.ts", "./node_modules/openai/_shims/auto/types.d.ts", "./node_modules/openai/streaming.d.ts", "./node_modules/openai/error.d.ts", "./node_modules/openai/_shims/multipartbody.d.ts", "./node_modules/openai/uploads.d.ts", "./node_modules/openai/core.d.ts", "./node_modules/openai/_shims/index.d.ts", "./node_modules/openai/pagination.d.ts", "./node_modules/openai/resources/shared.d.ts", "./node_modules/openai/resources/batches.d.ts", "./node_modules/openai/resources/chat/completions/messages.d.ts", "./node_modules/openai/resources/chat/completions/completions.d.ts", "./node_modules/openai/resources/completions.d.ts", "./node_modules/openai/resources/embeddings.d.ts", "./node_modules/openai/resources/files.d.ts", "./node_modules/openai/resources/images.d.ts", "./node_modules/openai/resources/models.d.ts", "./node_modules/openai/resources/moderations.d.ts", "./node_modules/openai/resources/audio/speech.d.ts", "./node_modules/openai/resources/audio/transcriptions.d.ts", "./node_modules/openai/resources/audio/translations.d.ts", "./node_modules/openai/resources/audio/audio.d.ts", "./node_modules/openai/resources/beta/threads/messages.d.ts", "./node_modules/openai/resources/beta/threads/runs/steps.d.ts", "./node_modules/openai/resources/beta/threads/runs/runs.d.ts", "./node_modules/openai/lib/eventstream.d.ts", "./node_modules/openai/lib/assistantstream.d.ts", "./node_modules/openai/resources/beta/threads/threads.d.ts", "./node_modules/openai/resources/beta/assistants.d.ts", "./node_modules/openai/resources/chat/completions.d.ts", "./node_modules/openai/lib/abstractchatcompletionrunner.d.ts", "./node_modules/openai/lib/chatcompletionstream.d.ts", "./node_modules/openai/lib/responsesparser.d.ts", "./node_modules/openai/resources/responses/input-items.d.ts", "./node_modules/openai/lib/responses/eventtypes.d.ts", "./node_modules/openai/lib/responses/responsestream.d.ts", "./node_modules/openai/resources/responses/responses.d.ts", "./node_modules/openai/lib/parser.d.ts", "./node_modules/openai/lib/chatcompletionstreamingrunner.d.ts", "./node_modules/openai/lib/jsonschema.d.ts", "./node_modules/openai/lib/runnablefunction.d.ts", "./node_modules/openai/lib/chatcompletionrunner.d.ts", "./node_modules/openai/resources/beta/chat/completions.d.ts", "./node_modules/openai/resources/beta/chat/chat.d.ts", "./node_modules/openai/resources/beta/realtime/sessions.d.ts", "./node_modules/openai/resources/beta/realtime/transcription-sessions.d.ts", "./node_modules/openai/resources/beta/realtime/realtime.d.ts", "./node_modules/openai/resources/beta/beta.d.ts", "./node_modules/openai/resources/containers/files/content.d.ts", "./node_modules/openai/resources/containers/files/files.d.ts", "./node_modules/openai/resources/containers/containers.d.ts", "./node_modules/openai/resources/graders/grader-models.d.ts", "./node_modules/openai/resources/evals/runs/output-items.d.ts", "./node_modules/openai/resources/evals/runs/runs.d.ts", "./node_modules/openai/resources/evals/evals.d.ts", "./node_modules/openai/resources/fine-tuning/methods.d.ts", "./node_modules/openai/resources/fine-tuning/alpha/graders.d.ts", "./node_modules/openai/resources/fine-tuning/alpha/alpha.d.ts", "./node_modules/openai/resources/fine-tuning/checkpoints/permissions.d.ts", "./node_modules/openai/resources/fine-tuning/checkpoints/checkpoints.d.ts", "./node_modules/openai/resources/fine-tuning/jobs/checkpoints.d.ts", "./node_modules/openai/resources/fine-tuning/jobs/jobs.d.ts", "./node_modules/openai/resources/fine-tuning/fine-tuning.d.ts", "./node_modules/openai/resources/graders/graders.d.ts", "./node_modules/openai/resources/uploads/parts.d.ts", "./node_modules/openai/resources/uploads/uploads.d.ts", "./node_modules/openai/resources/vector-stores/files.d.ts", "./node_modules/openai/resources/vector-stores/file-batches.d.ts", "./node_modules/openai/resources/vector-stores/vector-stores.d.ts", "./node_modules/openai/index.d.ts", "./node_modules/openai/resource.d.ts", "./node_modules/openai/resources/chat/chat.d.ts", "./node_modules/openai/resources/chat/completions/index.d.ts", "./node_modules/openai/resources/chat/index.d.ts", "./node_modules/openai/resources/index.d.ts", "./node_modules/openai/index.d.mts", "./node_modules/onnxruntime-common/dist/lib/tensor-utils.d.ts", "./node_modules/onnxruntime-common/dist/lib/tensor.d.ts", "./node_modules/onnxruntime-common/dist/lib/onnx-value.d.ts", "./node_modules/onnxruntime-common/dist/lib/inference-session.d.ts", "./node_modules/onnxruntime-common/dist/lib/backend-impl.d.ts", "./node_modules/onnxruntime-common/dist/lib/backend.d.ts", "./node_modules/onnxruntime-common/dist/lib/env.d.ts", "./node_modules/onnxruntime-common/dist/lib/index.d.ts", "./node_modules/@xenova/transformers/types/utils/maths.d.ts", "./node_modules/@xenova/transformers/types/utils/tensor.d.ts", "./node_modules/@xenova/transformers/types/utils/hub.d.ts", "./node_modules/@xenova/transformers/types/utils/generation.d.ts", "./node_modules/onnxruntime-web/types/lib/index.d.ts", "./node_modules/@xenova/transformers/types/models.d.ts", "./node_modules/@xenova/transformers/types/tokenizers.d.ts", "./node_modules/@xenova/transformers/types/utils/image.d.ts", "./node_modules/@xenova/transformers/types/processors.d.ts", "./node_modules/@xenova/transformers/types/pipelines.d.ts", "./node_modules/@xenova/transformers/types/env.d.ts", "./node_modules/@xenova/transformers/types/configs.d.ts", "./node_modules/@xenova/transformers/types/utils/audio.d.ts", "./node_modules/@xenova/transformers/types/transformers.d.ts", "./src/lib/ai-service.ts", "./node_modules/pdfjs-dist/types/src/shared/util.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/tools.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/toolbar.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/editor.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/freetext.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/highlight.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/draw.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/drawers/outline.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/drawers/inkdraw.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/ink.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/signature.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/stamp.d.ts", "./node_modules/pdfjs-dist/types/src/display/display_utils.d.ts", "./node_modules/pdfjs-dist/types/web/text_accessibility.d.ts", "./node_modules/pdfjs-dist/types/src/display/optional_content_config.d.ts", "./node_modules/pdfjs-dist/types/src/display/annotation_storage.d.ts", "./node_modules/pdfjs-dist/types/src/display/metadata.d.ts", "./node_modules/pdfjs-dist/types/src/shared/message_handler.d.ts", "./node_modules/pdfjs-dist/types/src/display/api.d.ts", "./node_modules/pdfjs-dist/types/web/interfaces.d.ts", "./node_modules/pdfjs-dist/types/web/struct_tree_layer_builder.d.ts", "./node_modules/pdfjs-dist/types/src/display/annotation_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/draw_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/annotation_editor_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/color_picker.d.ts", "./node_modules/pdfjs-dist/types/src/display/svg_factory.d.ts", "./node_modules/pdfjs-dist/types/src/display/worker_options.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/drawers/signaturedraw.d.ts", "./node_modules/pdfjs-dist/types/src/display/text_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/touch_manager.d.ts", "./node_modules/pdfjs-dist/types/src/display/xfa_layer.d.ts", "./node_modules/pdfjs-dist/types/src/pdf.d.ts", "./src/app/api/extract-pdf/route.ts", "./src/app/api/extract-pdf-test/route.ts", "./src/app/api/mobile/dashboard/route.ts", "./src/app/api/mobile/test/route.ts", "./src/lib/notifications.ts", "./src/app/api/notifications/route.ts", "./src/app/api/notifications/push/route.ts", "./src/lib/products.ts", "./src/app/api/products/route.ts", "./src/app/api/products/[id]/route.ts", "./src/app/api/products/[id]/images/route.ts", "./src/lib/utils.ts", "./src/lib/quotes.ts", "./src/app/api/quotes/route.ts", "./src/lib/route-utils.ts", "./src/app/api/quotes/[id]/route.ts", "./src/app/api/quotes/[id]/pdf/route.ts", "./src/app/api/realtime/sync/route.ts", "./src/lib/salesreports.ts", "./src/app/api/sales-reports/route.ts", "./src/app/api/sales-reports/[id]/route.ts", "./src/app/api/sales-reports/check-reminders/route.ts", "./src/app/api/sales-reports/export/route.ts", "./src/app/api/sales-reports/upload/route.ts", "./src/app/api/test/route.ts", "./src/app/api/test/[id]/route.ts", "./src/app/api/test2/[id]/route.ts", "./src/app/api/upload/route.ts", "./node_modules/jspdf/types/index.d.ts", "./node_modules/goober/goober.d.ts", "./node_modules/react-hot-toast/dist/index.d.ts", "./src/components/pdf/moonelecpdfgenerator.ts", "./src/utils/imageutils.ts", "./src/components/pdf/moonelecpdfgeneratorwithlogo.ts", "./src/utils/logobase64.ts", "./src/components/pdf/quotepdfgenerator.ts", "./src/data/staticdata.ts", "./node_modules/next-auth/client/_utils.d.ts", "./node_modules/next-auth/react/types.d.ts", "./node_modules/next-auth/react/index.d.ts", "./src/hooks/useauth.ts", "./src/hooks/usefileupload.ts", "./src/hooks/usemediaquery.ts", "./src/lib/reportreminders.ts", "./src/lib/serviceworker.ts", "./src/lib/startuptasks.ts", "./src/types/next-auth.d.ts", "./src/types/pdf-parse.d.ts", "./node_modules/canvas/index.d.ts", "./src/utils/imagetobase64.ts", "./node_modules/next/dist/compiled/@next/font/dist/types.d.ts", "./node_modules/next/dist/compiled/@next/font/dist/google/index.d.ts", "./node_modules/next/font/google/index.d.ts", "./src/context/authcontext.tsx", "./src/context/cartcontext.tsx", "./src/context/themecontext.tsx", "./src/components/ui/toastprovider.tsx", "./src/app/layout.tsx", "./node_modules/motion-utils/dist/index.d.ts", "./node_modules/motion-dom/dist/index.d.ts", "./node_modules/framer-motion/dist/types.d-ctupuryt.d.ts", "./node_modules/framer-motion/dist/types/index.d.ts", "./node_modules/react-icons/lib/iconsmanifest.d.ts", "./node_modules/react-icons/lib/iconbase.d.ts", "./node_modules/react-icons/lib/iconcontext.d.ts", "./node_modules/react-icons/lib/index.d.ts", "./node_modules/react-icons/fa/index.d.ts", "./src/components/cart/cartslidepanel.tsx", "./node_modules/date-fns/constants.d.ts", "./node_modules/date-fns/locale/types.d.ts", "./node_modules/date-fns/fp/types.d.ts", "./node_modules/date-fns/types.d.ts", "./node_modules/date-fns/add.d.ts", "./node_modules/date-fns/addbusinessdays.d.ts", "./node_modules/date-fns/adddays.d.ts", "./node_modules/date-fns/addhours.d.ts", "./node_modules/date-fns/addisoweekyears.d.ts", "./node_modules/date-fns/addmilliseconds.d.ts", "./node_modules/date-fns/addminutes.d.ts", "./node_modules/date-fns/addmonths.d.ts", "./node_modules/date-fns/addquarters.d.ts", "./node_modules/date-fns/addseconds.d.ts", "./node_modules/date-fns/addweeks.d.ts", "./node_modules/date-fns/addyears.d.ts", "./node_modules/date-fns/areintervalsoverlapping.d.ts", "./node_modules/date-fns/clamp.d.ts", "./node_modules/date-fns/closestindexto.d.ts", "./node_modules/date-fns/closestto.d.ts", "./node_modules/date-fns/compareasc.d.ts", "./node_modules/date-fns/comparedesc.d.ts", "./node_modules/date-fns/constructfrom.d.ts", "./node_modules/date-fns/constructnow.d.ts", "./node_modules/date-fns/daystoweeks.d.ts", "./node_modules/date-fns/differenceinbusinessdays.d.ts", "./node_modules/date-fns/differenceincalendardays.d.ts", "./node_modules/date-fns/differenceincalendarisoweekyears.d.ts", "./node_modules/date-fns/differenceincalendarisoweeks.d.ts", "./node_modules/date-fns/differenceincalendarmonths.d.ts", "./node_modules/date-fns/differenceincalendarquarters.d.ts", "./node_modules/date-fns/differenceincalendarweeks.d.ts", "./node_modules/date-fns/differenceincalendaryears.d.ts", "./node_modules/date-fns/differenceindays.d.ts", "./node_modules/date-fns/differenceinhours.d.ts", "./node_modules/date-fns/differenceinisoweekyears.d.ts", "./node_modules/date-fns/differenceinmilliseconds.d.ts", "./node_modules/date-fns/differenceinminutes.d.ts", "./node_modules/date-fns/differenceinmonths.d.ts", "./node_modules/date-fns/differenceinquarters.d.ts", "./node_modules/date-fns/differenceinseconds.d.ts", "./node_modules/date-fns/differenceinweeks.d.ts", "./node_modules/date-fns/differenceinyears.d.ts", "./node_modules/date-fns/eachdayofinterval.d.ts", "./node_modules/date-fns/eachhourofinterval.d.ts", "./node_modules/date-fns/eachminuteofinterval.d.ts", "./node_modules/date-fns/eachmonthofinterval.d.ts", "./node_modules/date-fns/eachquarterofinterval.d.ts", "./node_modules/date-fns/eachweekofinterval.d.ts", "./node_modules/date-fns/eachweekendofinterval.d.ts", "./node_modules/date-fns/eachweekendofmonth.d.ts", "./node_modules/date-fns/eachweekendofyear.d.ts", "./node_modules/date-fns/eachyearofinterval.d.ts", "./node_modules/date-fns/endofday.d.ts", "./node_modules/date-fns/endofdecade.d.ts", "./node_modules/date-fns/endofhour.d.ts", "./node_modules/date-fns/endofisoweek.d.ts", "./node_modules/date-fns/endofisoweekyear.d.ts", "./node_modules/date-fns/endofminute.d.ts", "./node_modules/date-fns/endofmonth.d.ts", "./node_modules/date-fns/endofquarter.d.ts", "./node_modules/date-fns/endofsecond.d.ts", "./node_modules/date-fns/endoftoday.d.ts", "./node_modules/date-fns/endoftomorrow.d.ts", "./node_modules/date-fns/endofweek.d.ts", "./node_modules/date-fns/endofyear.d.ts", "./node_modules/date-fns/endofyesterday.d.ts", "./node_modules/date-fns/_lib/format/formatters.d.ts", "./node_modules/date-fns/_lib/format/longformatters.d.ts", "./node_modules/date-fns/format.d.ts", "./node_modules/date-fns/formatdistance.d.ts", "./node_modules/date-fns/formatdistancestrict.d.ts", "./node_modules/date-fns/formatdistancetonow.d.ts", "./node_modules/date-fns/formatdistancetonowstrict.d.ts", "./node_modules/date-fns/formatduration.d.ts", "./node_modules/date-fns/formatiso.d.ts", "./node_modules/date-fns/formatiso9075.d.ts", "./node_modules/date-fns/formatisoduration.d.ts", "./node_modules/date-fns/formatrfc3339.d.ts", "./node_modules/date-fns/formatrfc7231.d.ts", "./node_modules/date-fns/formatrelative.d.ts", "./node_modules/date-fns/fromunixtime.d.ts", "./node_modules/date-fns/getdate.d.ts", "./node_modules/date-fns/getday.d.ts", "./node_modules/date-fns/getdayofyear.d.ts", "./node_modules/date-fns/getdaysinmonth.d.ts", "./node_modules/date-fns/getdaysinyear.d.ts", "./node_modules/date-fns/getdecade.d.ts", "./node_modules/date-fns/_lib/defaultoptions.d.ts", "./node_modules/date-fns/getdefaultoptions.d.ts", "./node_modules/date-fns/gethours.d.ts", "./node_modules/date-fns/getisoday.d.ts", "./node_modules/date-fns/getisoweek.d.ts", "./node_modules/date-fns/getisoweekyear.d.ts", "./node_modules/date-fns/getisoweeksinyear.d.ts", "./node_modules/date-fns/getmilliseconds.d.ts", "./node_modules/date-fns/getminutes.d.ts", "./node_modules/date-fns/getmonth.d.ts", "./node_modules/date-fns/getoverlappingdaysinintervals.d.ts", "./node_modules/date-fns/getquarter.d.ts", "./node_modules/date-fns/getseconds.d.ts", "./node_modules/date-fns/gettime.d.ts", "./node_modules/date-fns/getunixtime.d.ts", "./node_modules/date-fns/getweek.d.ts", "./node_modules/date-fns/getweekofmonth.d.ts", "./node_modules/date-fns/getweekyear.d.ts", "./node_modules/date-fns/getweeksinmonth.d.ts", "./node_modules/date-fns/getyear.d.ts", "./node_modules/date-fns/hourstomilliseconds.d.ts", "./node_modules/date-fns/hourstominutes.d.ts", "./node_modules/date-fns/hourstoseconds.d.ts", "./node_modules/date-fns/interval.d.ts", "./node_modules/date-fns/intervaltoduration.d.ts", "./node_modules/date-fns/intlformat.d.ts", "./node_modules/date-fns/intlformatdistance.d.ts", "./node_modules/date-fns/isafter.d.ts", "./node_modules/date-fns/isbefore.d.ts", "./node_modules/date-fns/isdate.d.ts", "./node_modules/date-fns/isequal.d.ts", "./node_modules/date-fns/isexists.d.ts", "./node_modules/date-fns/isfirstdayofmonth.d.ts", "./node_modules/date-fns/isfriday.d.ts", "./node_modules/date-fns/isfuture.d.ts", "./node_modules/date-fns/islastdayofmonth.d.ts", "./node_modules/date-fns/isleapyear.d.ts", "./node_modules/date-fns/ismatch.d.ts", "./node_modules/date-fns/ismonday.d.ts", "./node_modules/date-fns/ispast.d.ts", "./node_modules/date-fns/issameday.d.ts", "./node_modules/date-fns/issamehour.d.ts", "./node_modules/date-fns/issameisoweek.d.ts", "./node_modules/date-fns/issameisoweekyear.d.ts", "./node_modules/date-fns/issameminute.d.ts", "./node_modules/date-fns/issamemonth.d.ts", "./node_modules/date-fns/issamequarter.d.ts", "./node_modules/date-fns/issamesecond.d.ts", "./node_modules/date-fns/issameweek.d.ts", "./node_modules/date-fns/issameyear.d.ts", "./node_modules/date-fns/issaturday.d.ts", "./node_modules/date-fns/issunday.d.ts", "./node_modules/date-fns/isthishour.d.ts", "./node_modules/date-fns/isthisisoweek.d.ts", "./node_modules/date-fns/isthisminute.d.ts", "./node_modules/date-fns/isthismonth.d.ts", "./node_modules/date-fns/isthisquarter.d.ts", "./node_modules/date-fns/isthissecond.d.ts", "./node_modules/date-fns/isthisweek.d.ts", "./node_modules/date-fns/isthisyear.d.ts", "./node_modules/date-fns/isthursday.d.ts", "./node_modules/date-fns/istoday.d.ts", "./node_modules/date-fns/istomorrow.d.ts", "./node_modules/date-fns/istuesday.d.ts", "./node_modules/date-fns/isvalid.d.ts", "./node_modules/date-fns/iswednesday.d.ts", "./node_modules/date-fns/isweekend.d.ts", "./node_modules/date-fns/iswithininterval.d.ts", "./node_modules/date-fns/isyesterday.d.ts", "./node_modules/date-fns/lastdayofdecade.d.ts", "./node_modules/date-fns/lastdayofisoweek.d.ts", "./node_modules/date-fns/lastdayofisoweekyear.d.ts", "./node_modules/date-fns/lastdayofmonth.d.ts", "./node_modules/date-fns/lastdayofquarter.d.ts", "./node_modules/date-fns/lastdayofweek.d.ts", "./node_modules/date-fns/lastdayofyear.d.ts", "./node_modules/date-fns/_lib/format/lightformatters.d.ts", "./node_modules/date-fns/lightformat.d.ts", "./node_modules/date-fns/max.d.ts", "./node_modules/date-fns/milliseconds.d.ts", "./node_modules/date-fns/millisecondstohours.d.ts", "./node_modules/date-fns/millisecondstominutes.d.ts", "./node_modules/date-fns/millisecondstoseconds.d.ts", "./node_modules/date-fns/min.d.ts", "./node_modules/date-fns/minutestohours.d.ts", "./node_modules/date-fns/minutestomilliseconds.d.ts", "./node_modules/date-fns/minutestoseconds.d.ts", "./node_modules/date-fns/monthstoquarters.d.ts", "./node_modules/date-fns/monthstoyears.d.ts", "./node_modules/date-fns/nextday.d.ts", "./node_modules/date-fns/nextfriday.d.ts", "./node_modules/date-fns/nextmonday.d.ts", "./node_modules/date-fns/nextsaturday.d.ts", "./node_modules/date-fns/nextsunday.d.ts", "./node_modules/date-fns/nextthursday.d.ts", "./node_modules/date-fns/nexttuesday.d.ts", "./node_modules/date-fns/nextwednesday.d.ts", "./node_modules/date-fns/parse/_lib/types.d.ts", "./node_modules/date-fns/parse/_lib/setter.d.ts", "./node_modules/date-fns/parse/_lib/parser.d.ts", "./node_modules/date-fns/parse/_lib/parsers.d.ts", "./node_modules/date-fns/parse.d.ts", "./node_modules/date-fns/parseiso.d.ts", "./node_modules/date-fns/parsejson.d.ts", "./node_modules/date-fns/previousday.d.ts", "./node_modules/date-fns/previousfriday.d.ts", "./node_modules/date-fns/previousmonday.d.ts", "./node_modules/date-fns/previoussaturday.d.ts", "./node_modules/date-fns/previoussunday.d.ts", "./node_modules/date-fns/previousthursday.d.ts", "./node_modules/date-fns/previoustuesday.d.ts", "./node_modules/date-fns/previouswednesday.d.ts", "./node_modules/date-fns/quarterstomonths.d.ts", "./node_modules/date-fns/quarterstoyears.d.ts", "./node_modules/date-fns/roundtonearesthours.d.ts", "./node_modules/date-fns/roundtonearestminutes.d.ts", "./node_modules/date-fns/secondstohours.d.ts", "./node_modules/date-fns/secondstomilliseconds.d.ts", "./node_modules/date-fns/secondstominutes.d.ts", "./node_modules/date-fns/set.d.ts", "./node_modules/date-fns/setdate.d.ts", "./node_modules/date-fns/setday.d.ts", "./node_modules/date-fns/setdayofyear.d.ts", "./node_modules/date-fns/setdefaultoptions.d.ts", "./node_modules/date-fns/sethours.d.ts", "./node_modules/date-fns/setisoday.d.ts", "./node_modules/date-fns/setisoweek.d.ts", "./node_modules/date-fns/setisoweekyear.d.ts", "./node_modules/date-fns/setmilliseconds.d.ts", "./node_modules/date-fns/setminutes.d.ts", "./node_modules/date-fns/setmonth.d.ts", "./node_modules/date-fns/setquarter.d.ts", "./node_modules/date-fns/setseconds.d.ts", "./node_modules/date-fns/setweek.d.ts", "./node_modules/date-fns/setweekyear.d.ts", "./node_modules/date-fns/setyear.d.ts", "./node_modules/date-fns/startofday.d.ts", "./node_modules/date-fns/startofdecade.d.ts", "./node_modules/date-fns/startofhour.d.ts", "./node_modules/date-fns/startofisoweek.d.ts", "./node_modules/date-fns/startofisoweekyear.d.ts", "./node_modules/date-fns/startofminute.d.ts", "./node_modules/date-fns/startofmonth.d.ts", "./node_modules/date-fns/startofquarter.d.ts", "./node_modules/date-fns/startofsecond.d.ts", "./node_modules/date-fns/startoftoday.d.ts", "./node_modules/date-fns/startoftomorrow.d.ts", "./node_modules/date-fns/startofweek.d.ts", "./node_modules/date-fns/startofweekyear.d.ts", "./node_modules/date-fns/startofyear.d.ts", "./node_modules/date-fns/startofyesterday.d.ts", "./node_modules/date-fns/sub.d.ts", "./node_modules/date-fns/subbusinessdays.d.ts", "./node_modules/date-fns/subdays.d.ts", "./node_modules/date-fns/subhours.d.ts", "./node_modules/date-fns/subisoweekyears.d.ts", "./node_modules/date-fns/submilliseconds.d.ts", "./node_modules/date-fns/subminutes.d.ts", "./node_modules/date-fns/submonths.d.ts", "./node_modules/date-fns/subquarters.d.ts", "./node_modules/date-fns/subseconds.d.ts", "./node_modules/date-fns/subweeks.d.ts", "./node_modules/date-fns/subyears.d.ts", "./node_modules/date-fns/todate.d.ts", "./node_modules/date-fns/transpose.d.ts", "./node_modules/date-fns/weekstodays.d.ts", "./node_modules/date-fns/yearstodays.d.ts", "./node_modules/date-fns/yearstomonths.d.ts", "./node_modules/date-fns/yearstoquarters.d.ts", "./node_modules/date-fns/index.d.ts", "./node_modules/date-fns/locale/af.d.ts", "./node_modules/date-fns/locale/ar.d.ts", "./node_modules/date-fns/locale/ar-dz.d.ts", "./node_modules/date-fns/locale/ar-eg.d.ts", "./node_modules/date-fns/locale/ar-ma.d.ts", "./node_modules/date-fns/locale/ar-sa.d.ts", "./node_modules/date-fns/locale/ar-tn.d.ts", "./node_modules/date-fns/locale/az.d.ts", "./node_modules/date-fns/locale/be.d.ts", "./node_modules/date-fns/locale/be-tarask.d.ts", "./node_modules/date-fns/locale/bg.d.ts", "./node_modules/date-fns/locale/bn.d.ts", "./node_modules/date-fns/locale/bs.d.ts", "./node_modules/date-fns/locale/ca.d.ts", "./node_modules/date-fns/locale/ckb.d.ts", "./node_modules/date-fns/locale/cs.d.ts", "./node_modules/date-fns/locale/cy.d.ts", "./node_modules/date-fns/locale/da.d.ts", "./node_modules/date-fns/locale/de.d.ts", "./node_modules/date-fns/locale/de-at.d.ts", "./node_modules/date-fns/locale/el.d.ts", "./node_modules/date-fns/locale/en-au.d.ts", "./node_modules/date-fns/locale/en-ca.d.ts", "./node_modules/date-fns/locale/en-gb.d.ts", "./node_modules/date-fns/locale/en-ie.d.ts", "./node_modules/date-fns/locale/en-in.d.ts", "./node_modules/date-fns/locale/en-nz.d.ts", "./node_modules/date-fns/locale/en-us.d.ts", "./node_modules/date-fns/locale/en-za.d.ts", "./node_modules/date-fns/locale/eo.d.ts", "./node_modules/date-fns/locale/es.d.ts", "./node_modules/date-fns/locale/et.d.ts", "./node_modules/date-fns/locale/eu.d.ts", "./node_modules/date-fns/locale/fa-ir.d.ts", "./node_modules/date-fns/locale/fi.d.ts", "./node_modules/date-fns/locale/fr.d.ts", "./node_modules/date-fns/locale/fr-ca.d.ts", "./node_modules/date-fns/locale/fr-ch.d.ts", "./node_modules/date-fns/locale/fy.d.ts", "./node_modules/date-fns/locale/gd.d.ts", "./node_modules/date-fns/locale/gl.d.ts", "./node_modules/date-fns/locale/gu.d.ts", "./node_modules/date-fns/locale/he.d.ts", "./node_modules/date-fns/locale/hi.d.ts", "./node_modules/date-fns/locale/hr.d.ts", "./node_modules/date-fns/locale/ht.d.ts", "./node_modules/date-fns/locale/hu.d.ts", "./node_modules/date-fns/locale/hy.d.ts", "./node_modules/date-fns/locale/id.d.ts", "./node_modules/date-fns/locale/is.d.ts", "./node_modules/date-fns/locale/it.d.ts", "./node_modules/date-fns/locale/it-ch.d.ts", "./node_modules/date-fns/locale/ja.d.ts", "./node_modules/date-fns/locale/ja-hira.d.ts", "./node_modules/date-fns/locale/ka.d.ts", "./node_modules/date-fns/locale/kk.d.ts", "./node_modules/date-fns/locale/km.d.ts", "./node_modules/date-fns/locale/kn.d.ts", "./node_modules/date-fns/locale/ko.d.ts", "./node_modules/date-fns/locale/lb.d.ts", "./node_modules/date-fns/locale/lt.d.ts", "./node_modules/date-fns/locale/lv.d.ts", "./node_modules/date-fns/locale/mk.d.ts", "./node_modules/date-fns/locale/mn.d.ts", "./node_modules/date-fns/locale/ms.d.ts", "./node_modules/date-fns/locale/mt.d.ts", "./node_modules/date-fns/locale/nb.d.ts", "./node_modules/date-fns/locale/nl.d.ts", "./node_modules/date-fns/locale/nl-be.d.ts", "./node_modules/date-fns/locale/nn.d.ts", "./node_modules/date-fns/locale/oc.d.ts", "./node_modules/date-fns/locale/pl.d.ts", "./node_modules/date-fns/locale/pt.d.ts", "./node_modules/date-fns/locale/pt-br.d.ts", "./node_modules/date-fns/locale/ro.d.ts", "./node_modules/date-fns/locale/ru.d.ts", "./node_modules/date-fns/locale/se.d.ts", "./node_modules/date-fns/locale/sk.d.ts", "./node_modules/date-fns/locale/sl.d.ts", "./node_modules/date-fns/locale/sq.d.ts", "./node_modules/date-fns/locale/sr.d.ts", "./node_modules/date-fns/locale/sr-latn.d.ts", "./node_modules/date-fns/locale/sv.d.ts", "./node_modules/date-fns/locale/ta.d.ts", "./node_modules/date-fns/locale/te.d.ts", "./node_modules/date-fns/locale/th.d.ts", "./node_modules/date-fns/locale/tr.d.ts", "./node_modules/date-fns/locale/ug.d.ts", "./node_modules/date-fns/locale/uk.d.ts", "./node_modules/date-fns/locale/uz.d.ts", "./node_modules/date-fns/locale/uz-cyrl.d.ts", "./node_modules/date-fns/locale/vi.d.ts", "./node_modules/date-fns/locale/zh-cn.d.ts", "./node_modules/date-fns/locale/zh-hk.d.ts", "./node_modules/date-fns/locale/zh-tw.d.ts", "./node_modules/date-fns/locale.d.ts", "./src/components/notifications/notificationbell.tsx", "./src/components/shared/navbar.tsx", "./src/components/shared/footer.tsx", "./src/components/shared/loadinganimation.tsx", "./src/components/layout/pagelayout.tsx", "./src/components/ui/container.tsx", "./src/components/ui/button.tsx", "./src/components/icons/electricalicons.tsx", "./src/components/landing/herosection.tsx", "./node_modules/countup.js/dist/countup.d.ts", "./node_modules/react-countup/build/types.d.ts", "./node_modules/react-countup/build/countup.d.ts", "./node_modules/react-countup/build/usecountup.d.ts", "./node_modules/react-countup/build/index.d.ts", "./src/components/ui/section.tsx", "./src/components/ui/card.tsx", "./src/components/landing/aboutsection.tsx", "./src/components/landing/servicessection.tsx", "./src/components/landing/productssection.tsx", "./src/components/landing/brandssection.tsx", "./src/components/landing/contactsection.tsx", "./src/app/page.tsx", "./src/app/about/page.tsx", "./src/components/auth/routeguard.tsx", "./src/app/account/quotes/page.tsx", "./src/app/account/quotes/[id]/page.tsx", "./src/components/admin/adminsidebar.tsx", "./src/components/admin/adminheader.tsx", "./src/components/chat/chatwindow.tsx", "./src/app/admin/layout.tsx", "./src/components/ui/imageupload.tsx", "./src/app/admin/brands/page.tsx", "./src/app/admin/categories/page.tsx", "./src/app/admin/clients/page.tsx", "./src/app/admin/clients/[id]/page.tsx", "./src/app/admin/clients/new/page.tsx", "./src/app/admin/commercials/page.tsx", "./src/components/admin/dashboardcard.tsx", "./src/components/admin/dashboardchart.tsx", "./src/components/admin/recentactivitycard.tsx", "./src/components/admin/commercialperformancechart.tsx", "./src/components/admin/productstatschart.tsx", "./src/components/admin/clientorderscard.tsx", "./src/app/admin/dashboard/page.tsx", "./src/app/admin/init/page.tsx", "./src/app/admin/products/page.tsx", "./src/app/admin/products/[id]/page.tsx", "./src/components/ui/multiimageupload.tsx", "./src/app/admin/products/[id]/edit/page.tsx", "./src/app/admin/products/import/page.tsx", "./src/components/admin/pdfextractor.tsx", "./src/app/admin/products/new/page.tsx", "./src/app/admin/quotes/page.tsx", "./src/app/admin/quotes/[id]/page.tsx", "./src/app/admin/quotes/[id]/edit/page.tsx", "./src/app/admin/quotes/[id]/pdf/page.tsx", "./src/app/admin/quotes/new/page.tsx", "./node_modules/react-datepicker/dist/date_utils.d.ts", "./node_modules/react-datepicker/dist/input_time.d.ts", "./node_modules/react-datepicker/dist/day.d.ts", "./node_modules/react-datepicker/dist/week_number.d.ts", "./node_modules/react-datepicker/dist/week.d.ts", "./node_modules/react-datepicker/dist/month.d.ts", "./node_modules/react-datepicker/dist/month_dropdown_options.d.ts", "./node_modules/react-datepicker/dist/month_dropdown.d.ts", "./node_modules/react-datepicker/dist/month_year_dropdown_options.d.ts", "./node_modules/react-datepicker/dist/month_year_dropdown.d.ts", "./node_modules/react-datepicker/dist/time.d.ts", "./node_modules/react-datepicker/dist/year.d.ts", "./node_modules/react-datepicker/dist/year_dropdown_options.d.ts", "./node_modules/react-datepicker/dist/year_dropdown.d.ts", "./node_modules/react-datepicker/dist/click_outside_wrapper.d.ts", "./node_modules/react-datepicker/dist/calendar.d.ts", "./node_modules/react-datepicker/dist/calendar_icon.d.ts", "./node_modules/react-datepicker/dist/portal.d.ts", "./node_modules/react-datepicker/dist/tab_loop.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.d.mts", "./node_modules/@floating-ui/react/dist/floating-ui.react.d.mts", "./node_modules/react-datepicker/dist/with_floating.d.ts", "./node_modules/react-datepicker/dist/popper_component.d.ts", "./node_modules/react-datepicker/dist/calendar_container.d.ts", "./node_modules/react-datepicker/dist/index.d.ts", "./src/components/ui/daterangepicker.tsx", "./src/components/ui/whatsappaudioplayer.tsx", "./node_modules/dnd-core/dist/interfaces.d.ts", "./node_modules/dnd-core/dist/createdragdropmanager.d.ts", "./node_modules/dnd-core/dist/index.d.ts", "./node_modules/react-dnd/dist/core/dndcontext.d.ts", "./node_modules/react-dnd/dist/core/dndprovider.d.ts", "./node_modules/react-dnd/dist/types/options.d.ts", "./node_modules/react-dnd/dist/types/connectors.d.ts", "./node_modules/react-dnd/dist/types/monitors.d.ts", "./node_modules/react-dnd/dist/types/index.d.ts", "./node_modules/react-dnd/dist/core/dragpreviewimage.d.ts", "./node_modules/react-dnd/dist/core/index.d.ts", "./node_modules/react-dnd/dist/hooks/types.d.ts", "./node_modules/react-dnd/dist/hooks/usedrag/usedrag.d.ts", "./node_modules/react-dnd/dist/hooks/usedrag/index.d.ts", "./node_modules/react-dnd/dist/hooks/usedragdropmanager.d.ts", "./node_modules/react-dnd/dist/hooks/usedraglayer.d.ts", "./node_modules/react-dnd/dist/hooks/usedrop/usedrop.d.ts", "./node_modules/react-dnd/dist/hooks/usedrop/index.d.ts", "./node_modules/react-dnd/dist/hooks/index.d.ts", "./node_modules/react-dnd/dist/index.d.ts", "./node_modules/react-dnd-html5-backend/dist/getemptyimage.d.ts", "./node_modules/react-dnd-html5-backend/dist/nativetypes.d.ts", "./node_modules/react-dnd-html5-backend/dist/types.d.ts", "./node_modules/react-dnd-html5-backend/dist/index.d.ts", "./src/app/admin/reports/page.tsx", "./src/app/admin/reports/[id]/page.tsx", "./src/app/admin/tools/pdf-extractor/page.tsx", "./src/app/admin/tools/pdf-test/page.tsx", "./src/app/auth/error/page.tsx", "./src/components/ui/logo.tsx", "./src/components/auth/loginform.tsx", "./src/app/auth/signin/page.tsx", "./src/app/auth/signout/page.tsx", "./src/components/auth/signupform.tsx", "./src/app/auth/signup/page.tsx", "./src/app/cart/page.tsx", "./src/app/cart/quote-requested/page.tsx", "./src/app/client/layout.tsx", "./src/app/client/dashboard/page.tsx", "./src/app/client/favorites/page.tsx", "./src/app/client/invoices/page.tsx", "./src/app/client/orders/page.tsx", "./src/app/client/profile/page.tsx", "./src/app/client/quotes/page.tsx", "./src/app/commercial/layout.tsx", "./src/app/commercial/clients/page.tsx", "./src/app/commercial/dashboard/page.tsx", "./src/app/commercial/meetings/page.tsx", "./src/app/commercial/orders/page.tsx", "./src/app/commercial/reports/page.tsx", "./src/app/commercial/reports/[id]/page.tsx", "./src/app/commercial/reports/new/page.tsx", "./src/app/contact/page.tsx", "./src/app/products/page.tsx", "./src/components/cart/addtocartanimation.tsx", "./src/app/products/[id]/page.tsx", "./src/app/unauthorized/page.tsx", "./src/components/auth/signinform.tsx", "./src/components/landing/testimonialssection.tsx", "./.next/types/cache-life.d.ts", "./.next/types/app/page.ts", "./.next/types/app/about/page.ts", "./.next/types/app/account/quotes/page.ts", "./.next/types/app/account/quotes/[id]/page.ts", "./.next/types/app/admin/brands/page.ts", "./.next/types/app/admin/categories/page.ts", "./.next/types/app/admin/clients/page.ts", "./.next/types/app/admin/clients/[id]/page.ts", "./.next/types/app/admin/clients/new/page.ts", "./.next/types/app/admin/commercials/page.ts", "./.next/types/app/admin/dashboard/page.ts", "./.next/types/app/admin/init/page.ts", "./.next/types/app/admin/products/page.ts", "./.next/types/app/admin/products/[id]/page.ts", "./.next/types/app/admin/products/[id]/edit/page.ts", "./.next/types/app/admin/products/import/page.ts", "./.next/types/app/admin/products/new/page.ts", "./.next/types/app/admin/quotes/page.ts", "./.next/types/app/admin/quotes/[id]/page.ts", "./.next/types/app/admin/quotes/[id]/edit/page.ts", "./.next/types/app/admin/quotes/[id]/pdf/page.ts", "./.next/types/app/admin/quotes/new/page.ts", "./.next/types/app/admin/reports/page.ts", "./.next/types/app/admin/reports/[id]/page.ts", "./.next/types/app/admin/tools/pdf-extractor/page.ts", "./.next/types/app/admin/tools/pdf-test/page.ts", "./.next/types/app/api/admin/clients/route.ts", "./.next/types/app/api/admin/clients/[id]/route.ts", "./.next/types/app/api/admin/clients/export/route.ts", "./.next/types/app/api/admin/dashboard/route.ts", "./.next/types/app/api/admin/init/route.ts", "./.next/types/app/api/auth/[...nextauth]/route.ts", "./.next/types/app/api/auth/mobile/route.ts", "./.next/types/app/api/auth/refresh-token/route.ts", "./.next/types/app/api/auth/signup/route.ts", "./.next/types/app/api/brands/route.ts", "./.next/types/app/api/brands/[id]/route.ts", "./.next/types/app/api/categories/route.ts", "./.next/types/app/api/categories/[id]/route.ts", "./.next/types/app/api/chat/conversations/route.ts", "./.next/types/app/api/chat/messages/route.ts", "./.next/types/app/api/chat/upload/route.ts", "./.next/types/app/api/chat/users/route.ts", "./.next/types/app/api/clients/dashboard/route.ts", "./.next/types/app/api/commercials/route.ts", "./.next/types/app/api/commercials/[id]/route.ts", "./.next/types/app/api/commercials/dashboard/route.ts", "./.next/types/app/api/extract-pdf/route.ts", "./.next/types/app/api/extract-pdf-test/route.ts", "./.next/types/app/api/mobile/dashboard/route.ts", "./.next/types/app/api/mobile/test/route.ts", "./.next/types/app/api/notifications/route.ts", "./.next/types/app/api/notifications/push/route.ts", "./.next/types/app/api/products/route.ts", "./.next/types/app/api/products/[id]/route.ts", "./.next/types/app/api/products/[id]/images/route.ts", "./.next/types/app/api/quotes/route.ts", "./.next/types/app/api/quotes/[id]/route.ts", "./.next/types/app/api/quotes/[id]/pdf/route.ts", "./.next/types/app/api/realtime/sync/route.ts", "./.next/types/app/api/sales-reports/route.ts", "./.next/types/app/api/sales-reports/[id]/route.ts", "./.next/types/app/api/sales-reports/check-reminders/route.ts", "./.next/types/app/api/sales-reports/export/route.ts", "./.next/types/app/api/sales-reports/upload/route.ts", "./.next/types/app/api/test/route.ts", "./.next/types/app/api/test/[id]/route.ts", "./.next/types/app/api/test2/[id]/route.ts", "./.next/types/app/api/upload/route.ts", "./.next/types/app/auth/error/page.ts", "./.next/types/app/auth/signin/page.ts", "./.next/types/app/auth/signout/page.ts", "./.next/types/app/auth/signup/page.ts", "./.next/types/app/cart/page.ts", "./.next/types/app/cart/quote-requested/page.ts", "./.next/types/app/client/dashboard/page.ts", "./.next/types/app/client/favorites/page.ts", "./.next/types/app/client/invoices/page.ts", "./.next/types/app/client/orders/page.ts", "./.next/types/app/client/profile/page.ts", "./.next/types/app/client/quotes/page.ts", "./.next/types/app/commercial/clients/page.ts", "./.next/types/app/commercial/dashboard/page.ts", "./.next/types/app/commercial/meetings/page.ts", "./.next/types/app/commercial/orders/page.ts", "./.next/types/app/commercial/reports/page.ts", "./.next/types/app/commercial/reports/[id]/page.ts", "./.next/types/app/commercial/reports/new/page.ts", "./.next/types/app/contact/page.ts", "./.next/types/app/products/page.ts", "./.next/types/app/products/[id]/page.ts", "./.next/types/app/unauthorized/page.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@types/babel__generator/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@types/babel__template/index.d.ts", "./node_modules/@types/babel__traverse/index.d.ts", "./node_modules/@types/babel__core/index.d.ts", "./node_modules/@types/connect/index.d.ts", "./node_modules/@types/body-parser/index.d.ts", "./node_modules/@types/estree/index.d.ts", "./node_modules/@types/mime/index.d.ts", "./node_modules/@types/send/index.d.ts", "./node_modules/@types/qs/index.d.ts", "./node_modules/@types/range-parser/index.d.ts", "./node_modules/@types/express-serve-static-core/index.d.ts", "./node_modules/@types/http-errors/index.d.ts", "./node_modules/@types/serve-static/index.d.ts", "./node_modules/@types/express/index.d.ts", "./node_modules/@types/graceful-fs/index.d.ts", "./node_modules/@types/istanbul-lib-coverage/index.d.ts", "./node_modules/@types/istanbul-lib-report/index.d.ts", "./node_modules/@types/istanbul-reports/index.d.ts", "./node_modules/@types/json-schema/index.d.ts", "./node_modules/@types/json5/index.d.ts", "./node_modules/@types/long/index.d.ts", "./node_modules/@types/multer/index.d.ts", "./node_modules/form-data/index.d.ts", "./node_modules/@types/node-fetch/externals.d.ts", "./node_modules/@types/node-fetch/index.d.ts", "./node_modules/@types/raf/index.d.ts", "./node_modules/@types/stack-utils/index.d.ts", "./node_modules/@types/trusted-types/lib/index.d.ts", "./node_modules/@types/trusted-types/index.d.ts", "./node_modules/@types/yargs-parser/index.d.ts", "./node_modules/@types/yargs/index.d.ts"], "fileIdsList": [[97, 140, 336, 1162], [97, 140, 336, 1165], [97, 140, 336, 1164], [97, 140, 336, 1171], [97, 140, 336, 1172], [97, 140, 336, 1174], [97, 140, 336, 1175], [97, 140, 336, 1173], [97, 140, 336, 1176], [97, 140, 336, 1183], [97, 140, 336, 1184], [97, 140, 336, 1188], [97, 140, 336, 1186], [97, 140, 336, 1189], [97, 140, 336, 1191], [97, 140, 336, 1185], [97, 140, 336, 1194], [97, 140, 336, 1193], [97, 140, 336, 1195], [97, 140, 336, 1196], [97, 140, 336, 1192], [97, 140, 336, 1253], [97, 140, 336, 1252], [97, 140, 336, 1254], [97, 140, 336, 1255], [97, 140, 469, 557], [97, 140, 469, 560], [97, 140, 469, 556], [97, 140, 469, 563], [97, 140, 469, 564], [97, 140, 469, 565], [97, 140, 469, 566], [97, 140, 469, 567], [97, 140, 469, 568], [97, 140, 469, 571], [97, 140, 469, 570], [97, 140, 469, 575], [97, 140, 469, 574], [97, 140, 469, 576], [97, 140, 469, 577], [97, 140, 469, 579], [97, 140, 469, 580], [97, 140, 469, 581], [97, 140, 469, 584], [97, 140, 469, 585], [97, 140, 469, 583], [97, 140, 469, 719], [97, 140, 469, 718], [97, 140, 469, 720], [97, 140, 469, 721], [97, 140, 469, 724], [97, 140, 469, 723], [97, 140, 469, 728], [97, 140, 469, 727], [97, 140, 469, 726], [97, 140, 469, 734], [97, 140, 469, 733], [97, 140, 469, 731], [97, 140, 469, 735], [97, 140, 469, 738], [97, 140, 469, 739], [97, 140, 469, 740], [97, 140, 469, 737], [97, 140, 469, 741], [97, 140, 469, 743], [97, 140, 469, 742], [97, 140, 469, 744], [97, 140, 469, 745], [97, 140, 336, 1256], [97, 140, 336, 1259], [97, 140, 336, 1260], [97, 140, 336, 1262], [97, 140, 336, 1263], [97, 140, 336, 1264], [97, 140, 336, 1266], [97, 140, 336, 1267], [97, 140, 336, 1268], [97, 140, 336, 1269], [97, 140, 336, 1270], [97, 140, 336, 1271], [97, 140, 336, 1273], [97, 140, 336, 1274], [97, 140, 336, 1275], [97, 140, 336, 1276], [97, 140, 336, 1278], [97, 140, 336, 1279], [97, 140, 336, 1277], [97, 140, 336, 1280], [97, 140, 336, 1161], [97, 140, 336, 1283], [97, 140, 336, 1281], [97, 140, 336, 1284], [97, 140, 423, 424, 425, 426], [97, 140, 469, 525], [97, 140, 473, 474], [97, 140, 473], [97, 140, 531], [97, 140, 530], [97, 140, 1380], [97, 140], [97, 140, 1216], [97, 140, 1217, 1218], [83, 97, 140, 1219], [83, 97, 140, 1220], [97, 140, 532], [97, 140, 1380, 1381, 1382, 1383, 1384], [97, 140, 1380, 1382], [97, 140, 155, 189, 1386], [97, 140, 155, 189], [97, 140, 152, 155, 189, 1390, 1391, 1392], [97, 140, 1387, 1393, 1395], [97, 140, 153, 189], [97, 140, 1398], [97, 140, 1399], [97, 140, 171, 1396], [97, 140, 155, 182, 189, 1405, 1406], [97, 137, 140], [97, 139, 140], [140], [97, 140, 145, 174], [97, 140, 141, 146, 152, 153, 160, 171, 182], [97, 140, 141, 142, 152, 160], [92, 93, 94, 97, 140], [97, 140, 143, 183], [97, 140, 144, 145, 153, 161], [97, 140, 145, 171, 179], [97, 140, 146, 148, 152, 160], [97, 139, 140, 147], [97, 140, 148, 149], [97, 140, 152], [97, 140, 150, 152], [97, 139, 140, 152], [97, 140, 152, 153, 154, 171, 182], [97, 140, 152, 153, 154, 167, 171, 174], [97, 135, 140, 187], [97, 140, 148, 152, 155, 160, 171, 182], [97, 140, 152, 153, 155, 156, 160, 171, 179, 182], [97, 140, 155, 157, 171, 179, 182], [95, 96, 97, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188], [97, 140, 152, 158], [97, 140, 159, 182, 187], [97, 140, 148, 152, 160, 171], [97, 140, 161], [97, 140, 162], [97, 139, 140, 163], [97, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188], [97, 140, 165], [97, 140, 166], [97, 140, 152, 167, 168], [97, 140, 167, 169, 183, 185], [97, 140, 152, 171, 172, 174], [97, 140, 173, 174], [97, 140, 171, 172], [97, 140, 174], [97, 140, 175], [97, 137, 140, 171], [97, 140, 152, 177, 178], [97, 140, 177, 178], [97, 140, 145, 160, 171, 179], [97, 140, 180], [97, 140, 160, 181], [97, 140, 155, 166, 182], [97, 140, 145, 183], [97, 140, 171, 184], [97, 140, 159, 185], [97, 140, 186], [97, 140, 145, 152, 154, 163, 171, 182, 185, 187], [97, 140, 171, 188], [97, 140, 189], [83, 97, 140, 192, 194], [83, 87, 97, 140, 190, 191, 192, 193, 417, 465], [83, 97, 140], [83, 87, 97, 140, 191, 194, 417, 465], [83, 87, 97, 140, 190, 194, 417, 465], [81, 82, 97, 140], [97, 140, 153, 171, 189, 1389], [97, 140, 155, 189, 1390, 1394], [97, 140, 1410], [97, 140, 1412], [97, 140, 673], [97, 140, 670], [97, 140, 672, 673, 674, 675, 684], [97, 140, 672, 673, 674, 676, 677, 678, 679], [97, 140, 672, 673, 678], [97, 140, 672, 673], [97, 140, 671, 672, 676, 677, 678, 679, 680, 681, 682, 683], [97, 140, 672], [97, 140, 670, 671], [97, 140, 528], [97, 140, 171], [97, 140, 789], [97, 140, 787, 789], [97, 140, 787], [97, 140, 789, 853, 854], [97, 140, 789, 856], [97, 140, 789, 857], [97, 140, 874], [97, 140, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 951, 952, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967, 968, 969, 970, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042], [97, 140, 789, 950], [97, 140, 787, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108, 1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138], [97, 140, 789, 854, 974], [97, 140, 787, 971, 972], [97, 140, 973], [97, 140, 789, 971], [97, 140, 786, 787, 788], [97, 140, 1228], [97, 140, 1228, 1229], [97, 140, 155, 171, 189], [83, 97, 140, 266, 776, 777], [83, 97, 140, 266, 776, 777, 778], [82, 97, 140], [97, 140, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508], [97, 140, 477], [97, 140, 477, 487], [97, 140, 776], [97, 140, 523, 764], [97, 140, 155, 189, 523, 764], [97, 140, 514, 521], [97, 140, 469, 473, 521, 523, 764], [97, 140, 476, 510, 517, 519, 520, 764], [97, 140, 515, 521, 522], [97, 140, 469, 473, 518, 523, 764], [97, 140, 189, 523, 764], [97, 140, 524], [97, 140, 469, 519, 523, 764], [97, 140, 515, 517, 523, 764], [97, 140, 517, 521, 523, 764], [97, 140, 512, 513, 516], [97, 140, 509, 510, 511, 517, 523, 764], [83, 97, 140, 517, 523, 755, 756, 764], [83, 97, 140, 517, 523, 764], [89, 97, 140], [97, 140, 421], [97, 140, 428], [97, 140, 198, 212, 213, 214, 216, 380], [97, 140, 198, 202, 204, 205, 206, 207, 208, 369, 380, 382], [97, 140, 380], [97, 140, 213, 232, 349, 358, 376], [97, 140, 198], [97, 140, 195], [97, 140, 400], [97, 140, 380, 382, 399], [97, 140, 303, 346, 349, 471], [97, 140, 313, 328, 358, 375], [97, 140, 263], [97, 140, 363], [97, 140, 362, 363, 364], [97, 140, 362], [91, 97, 140, 155, 195, 198, 202, 205, 209, 210, 211, 213, 217, 225, 226, 297, 359, 360, 380, 417], [97, 140, 198, 215, 252, 300, 380, 396, 397, 471], [97, 140, 215, 471], [97, 140, 226, 300, 301, 380, 471], [97, 140, 471], [97, 140, 198, 215, 216, 471], [97, 140, 209, 361, 368], [97, 140, 166, 266, 376], [97, 140, 266, 376], [83, 97, 140, 266], [83, 97, 140, 266, 320], [97, 140, 243, 261, 376, 454], [97, 140, 355, 448, 449, 450, 451, 453], [97, 140, 266], [97, 140, 354], [97, 140, 354, 355], [97, 140, 206, 240, 241, 298], [97, 140, 242, 243, 298], [97, 140, 452], [97, 140, 243, 298], [83, 97, 140, 199, 442], [83, 97, 140, 182], [83, 97, 140, 215, 250], [83, 97, 140, 215], [97, 140, 248, 253], [83, 97, 140, 249, 420], [97, 140, 768], [83, 87, 97, 140, 155, 189, 190, 191, 194, 417, 463, 464], [97, 140, 155], [97, 140, 155, 202, 232, 268, 287, 298, 365, 366, 380, 381, 471], [97, 140, 225, 367], [97, 140, 417], [97, 140, 197], [83, 97, 140, 303, 317, 327, 337, 339, 375], [97, 140, 166, 303, 317, 336, 337, 338, 375], [97, 140, 330, 331, 332, 333, 334, 335], [97, 140, 332], [97, 140, 336], [83, 97, 140, 249, 266, 420], [83, 97, 140, 266, 418, 420], [83, 97, 140, 266, 420], [97, 140, 287, 372], [97, 140, 372], [97, 140, 155, 381, 420], [97, 140, 324], [97, 139, 140, 323], [97, 140, 227, 231, 238, 269, 298, 310, 312, 313, 314, 316, 348, 375, 378, 381], [97, 140, 315], [97, 140, 227, 243, 298, 310], [97, 140, 313, 375], [97, 140, 313, 320, 321, 322, 324, 325, 326, 327, 328, 329, 340, 341, 342, 343, 344, 345, 375, 376, 471], [97, 140, 308], [97, 140, 155, 166, 227, 231, 232, 237, 239, 243, 273, 287, 296, 297, 348, 371, 380, 381, 382, 417, 471], [97, 140, 375], [97, 139, 140, 213, 231, 297, 310, 311, 371, 373, 374, 381], [97, 140, 313], [97, 139, 140, 237, 269, 290, 304, 305, 306, 307, 308, 309, 312, 375, 376], [97, 140, 155, 290, 291, 304, 381, 382], [97, 140, 213, 287, 297, 298, 310, 371, 375, 381], [97, 140, 155, 380, 382], [97, 140, 155, 171, 378, 381, 382], [97, 140, 155, 166, 182, 195, 202, 215, 227, 231, 232, 238, 239, 244, 268, 269, 270, 272, 273, 276, 277, 279, 282, 283, 284, 285, 286, 298, 370, 371, 376, 378, 380, 381, 382], [97, 140, 155, 171], [97, 140, 198, 199, 200, 210, 378, 379, 417, 420, 471], [97, 140, 155, 171, 182, 229, 398, 400, 401, 402, 403, 471], [97, 140, 166, 182, 195, 229, 232, 269, 270, 277, 287, 295, 298, 371, 376, 378, 383, 384, 390, 396, 413, 414], [97, 140, 209, 210, 225, 297, 360, 371, 380], [97, 140, 155, 182, 199, 202, 269, 378, 380, 388], [97, 140, 302], [97, 140, 155, 410, 411, 412], [97, 140, 378, 380], [97, 140, 310, 311], [97, 140, 231, 269, 370, 420], [97, 140, 155, 166, 277, 287, 378, 384, 390, 392, 396, 413, 416], [97, 140, 155, 209, 225, 396, 406], [97, 140, 198, 244, 370, 380, 408], [97, 140, 155, 215, 244, 380, 391, 392, 404, 405, 407, 409], [91, 97, 140, 227, 230, 231, 417, 420], [97, 140, 155, 166, 182, 202, 209, 217, 225, 232, 238, 239, 269, 270, 272, 273, 285, 287, 295, 298, 370, 371, 376, 377, 378, 383, 384, 385, 387, 389, 420], [97, 140, 155, 171, 209, 378, 390, 410, 415], [97, 140, 220, 221, 222, 223, 224], [97, 140, 276, 278], [97, 140, 280], [97, 140, 278], [97, 140, 280, 281], [97, 140, 155, 202, 237, 381], [97, 140, 155, 166, 197, 199, 227, 231, 232, 238, 239, 265, 267, 378, 382, 417, 420], [97, 140, 155, 166, 182, 201, 206, 269, 377, 381], [97, 140, 304], [97, 140, 305], [97, 140, 306], [97, 140, 376], [97, 140, 228, 235], [97, 140, 155, 202, 228, 238], [97, 140, 234, 235], [97, 140, 236], [97, 140, 228, 229], [97, 140, 228, 245], [97, 140, 228], [97, 140, 275, 276, 377], [97, 140, 274], [97, 140, 229, 376, 377], [97, 140, 271, 377], [97, 140, 229, 376], [97, 140, 348], [97, 140, 230, 233, 238, 269, 298, 303, 310, 317, 319, 347, 378, 381], [97, 140, 243, 254, 257, 258, 259, 260, 261, 318], [97, 140, 357], [97, 140, 213, 230, 231, 291, 298, 313, 324, 328, 350, 351, 352, 353, 355, 356, 359, 370, 375, 380], [97, 140, 243], [97, 140, 265], [97, 140, 155, 230, 238, 246, 262, 264, 268, 378, 417, 420], [97, 140, 243, 254, 255, 256, 257, 258, 259, 260, 261, 418], [97, 140, 229], [97, 140, 291, 292, 295, 371], [97, 140, 155, 276, 380], [97, 140, 290, 313], [97, 140, 289], [97, 140, 285, 291], [97, 140, 288, 290, 380], [97, 140, 155, 201, 291, 292, 293, 294, 380, 381], [83, 97, 140, 240, 242, 298], [97, 140, 299], [83, 97, 140, 199], [83, 97, 140, 376], [83, 91, 97, 140, 231, 239, 417, 420], [97, 140, 199, 442, 443], [83, 97, 140, 253], [83, 97, 140, 166, 182, 197, 247, 249, 251, 252, 420], [97, 140, 215, 376, 381], [97, 140, 376, 386], [83, 97, 140, 153, 155, 166, 197, 253, 300, 417, 418, 419], [83, 97, 140, 190, 191, 194, 417, 465], [83, 84, 85, 86, 87, 97, 140], [97, 140, 145], [97, 140, 393, 394, 395], [97, 140, 393], [83, 87, 97, 140, 155, 157, 166, 189, 190, 191, 192, 194, 195, 197, 273, 336, 382, 416, 420, 465], [97, 140, 430], [97, 140, 432], [97, 140, 434], [97, 140, 769], [97, 140, 436], [97, 140, 438, 439, 440], [97, 140, 444], [88, 90, 97, 140, 422, 427, 429, 431, 433, 435, 437, 441, 445, 447, 456, 457, 459, 469, 470, 471, 472], [97, 140, 446], [97, 140, 455], [97, 140, 249], [97, 140, 458], [97, 139, 140, 291, 292, 293, 295, 327, 376, 460, 461, 462, 465, 466, 467, 468], [97, 140, 668], [97, 140, 665, 666, 667], [97, 140, 664, 665, 666, 668, 669], [97, 140, 665], [97, 140, 664], [97, 140, 663], [97, 140, 586, 587, 592], [97, 140, 588, 589, 591, 593], [97, 140, 592], [97, 140, 589, 591, 592, 593, 594, 596, 598, 599, 600, 601, 602, 603, 604, 608, 623, 634, 637, 641, 649, 650, 652, 655, 658, 661], [97, 140, 592, 599, 612, 616, 625, 627, 628, 629, 656], [97, 140, 592, 593, 609, 610, 611, 612, 614, 615], [97, 140, 616, 617, 624, 627, 656], [97, 140, 592, 593, 598, 617, 629, 656], [97, 140, 593, 616, 617, 618, 624, 627, 656], [97, 140, 589], [97, 140, 595, 616, 623, 629], [97, 140, 623], [97, 140, 592, 612, 619, 621, 623, 656], [97, 140, 616, 623, 624], [97, 140, 625, 626, 628], [97, 140, 656], [97, 140, 605, 606, 607, 657], [97, 140, 592, 593, 657], [97, 140, 588, 592, 606, 608, 657], [97, 140, 592, 606, 608, 657], [97, 140, 592, 594, 595, 596, 657], [97, 140, 592, 594, 595, 609, 610, 611, 613, 614, 657], [97, 140, 614, 615, 630, 633, 657], [97, 140, 629, 657], [97, 140, 592, 616, 617, 618, 624, 625, 627, 628, 657], [97, 140, 595, 631, 632, 633, 657], [97, 140, 592, 657], [97, 140, 592, 594, 595, 615, 657], [97, 140, 588, 592, 594, 595, 609, 610, 611, 613, 614, 615, 657], [97, 140, 592, 594, 595, 610, 657], [97, 140, 588, 592, 595, 609, 611, 613, 614, 615, 657], [97, 140, 595, 598, 657], [97, 140, 598], [97, 140, 588, 592, 594, 595, 597, 598, 599, 657], [97, 140, 597, 598], [97, 140, 592, 594, 598, 657], [97, 140, 658, 659], [97, 140, 588, 592, 598, 599, 657], [97, 140, 592, 594, 636, 657], [97, 140, 592, 594, 635, 657], [97, 140, 592, 594, 595, 623, 638, 640, 657], [97, 140, 592, 594, 640, 657], [97, 140, 592, 594, 595, 623, 639, 657], [97, 140, 592, 593, 594, 657], [97, 140, 643, 657], [97, 140, 592, 638, 657], [97, 140, 645, 657], [97, 140, 592, 594, 657], [97, 140, 642, 644, 646, 648, 657], [97, 140, 592, 594, 642, 647, 657], [97, 140, 638, 657], [97, 140, 623, 657], [97, 140, 595, 596, 599, 600, 601, 602, 603, 604, 608, 623, 634, 637, 641, 649, 650, 652, 655, 660], [97, 140, 592, 594, 623, 657], [97, 140, 588, 592, 594, 595, 619, 620, 622, 623, 657], [97, 140, 592, 601, 651, 657], [97, 140, 592, 594, 653, 655, 657], [97, 140, 592, 594, 655, 657], [97, 140, 592, 594, 595, 653, 654, 657], [97, 140, 593], [97, 140, 590, 592, 593], [97, 140, 145, 155, 156, 157, 182, 183, 189, 509], [97, 140, 698, 699, 701, 704, 705, 706], [97, 140, 698, 700, 701, 702, 703], [97, 140, 687, 689, 690, 691, 695, 696, 697, 698, 699, 705, 707, 708], [97, 140, 689], [97, 140, 693], [97, 140, 694], [97, 140, 687, 688, 709], [97, 140, 689, 709], [97, 140, 692, 694], [97, 140, 698, 704], [97, 140, 698, 701, 705], [97, 140, 686, 687, 698, 704, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716], [97, 140, 686], [97, 140, 704], [83, 97, 140, 1150], [97, 140, 1151, 1152], [83, 97, 140, 1149], [83, 97, 140, 1043, 1197, 1198, 1202, 1204, 1206, 1207, 1208, 1210, 1211], [97, 140, 1043], [83, 97, 140, 1197], [83, 97, 140, 1197, 1211, 1212, 1213, 1214, 1223, 1224], [83, 97, 140, 1197, 1201], [83, 97, 140, 1197, 1203], [83, 97, 140, 1197, 1205], [83, 97, 140, 1214, 1215, 1222], [83, 97, 140, 1199, 1200], [83, 97, 140, 1221], [83, 97, 140, 1209], [97, 140, 1230, 1248, 1249, 1250], [83, 97, 140, 1230], [83, 97, 140, 1236], [97, 140, 1231, 1232, 1237], [97, 140, 1239, 1241, 1242, 1243, 1245], [97, 140, 1230, 1236], [97, 140, 1240], [97, 140, 1236, 1239], [97, 140, 1230], [97, 140, 1236], [97, 140, 1244], [97, 140, 1236, 1238, 1246], [83, 97, 140, 1233], [97, 140, 1233, 1234, 1235], [83, 97, 140, 747], [97, 140, 783], [97, 140, 780, 781, 782], [97, 140, 171, 189], [97, 107, 111, 140, 182], [97, 107, 140, 171, 182], [97, 102, 140], [97, 104, 107, 140, 179, 182], [97, 140, 160, 179], [97, 102, 140, 189], [97, 104, 107, 140, 160, 182], [97, 99, 100, 103, 106, 140, 152, 171, 182], [97, 107, 114, 140], [97, 99, 105, 140], [97, 107, 128, 129, 140], [97, 103, 107, 140, 174, 182, 189], [97, 128, 140, 189], [97, 101, 102, 140, 189], [97, 107, 140], [97, 101, 102, 103, 104, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 130, 131, 132, 133, 134, 140], [97, 107, 122, 140], [97, 107, 114, 115, 140], [97, 105, 107, 115, 116, 140], [97, 106, 140], [97, 99, 102, 107, 140], [97, 107, 111, 115, 116, 140], [97, 111, 140], [97, 105, 107, 110, 140, 182], [97, 99, 104, 107, 114, 140], [97, 102, 107, 128, 140, 187, 189], [97, 140, 539, 540, 541, 542, 543, 544, 545, 547, 548, 549, 550, 551, 552, 553, 554], [97, 140, 539], [97, 140, 539, 546], [97, 140, 535], [97, 140, 533], [97, 140, 445, 779, 784, 1144, 1153], [83, 97, 140, 445, 447, 456, 533, 729, 758, 779, 784, 1144, 1163], [83, 97, 140, 447, 456, 533, 729, 758, 779, 784, 1144, 1163], [83, 97, 140, 445, 447, 758, 779, 784, 1163, 1170], [83, 97, 140, 447, 456, 758, 779, 784, 1163], [83, 97, 140, 456, 758, 779, 784, 1163], [83, 97, 140, 758, 779, 784, 1177, 1178, 1179, 1180, 1181, 1182], [83, 97, 140, 779, 784], [83, 97, 140, 456, 758, 779, 1163, 1166, 1167, 1168], [83, 97, 140, 447, 456, 779, 784, 1163, 1170, 1187], [83, 97, 140, 445, 447, 456, 779, 784, 1163], [83, 97, 140, 447, 456, 758, 779, 784, 1163, 1170, 1187], [83, 97, 140, 447, 456, 758, 779, 784, 1163, 1170, 1187, 1190], [83, 97, 140, 445, 447, 456, 758, 779, 784, 1163], [83, 97, 140, 445, 447, 456, 533, 729, 758, 779, 784, 1163], [83, 97, 140, 445, 447, 456, 748, 751, 758, 779, 784, 1163], [83, 97, 140, 445, 456, 758, 779, 784, 1163], [83, 97, 140, 445, 456, 758, 779, 784, 1163, 1226, 1227, 1247, 1251], [83, 97, 140, 447, 758, 779, 1163, 1190], [83, 97, 140, 447, 758, 779, 1163], [97, 140, 469, 523, 529, 534, 538, 764], [97, 140, 469, 523, 534, 538, 558, 559, 764], [97, 140, 469, 523, 529, 534, 538, 555, 764], [97, 140, 469, 523, 534, 538, 561, 562, 764], [97, 140, 469, 535], [97, 140, 523, 538, 764], [97, 140, 469, 529, 534, 562], [97, 140, 469, 534], [97, 140, 469, 569], [97, 140, 469, 572], [97, 140, 469, 523, 538, 572, 573, 764], [97, 140, 469, 523, 534, 538, 573, 764], [97, 140, 469, 523, 534, 538, 573, 578, 764], [97, 140, 469, 534, 573], [97, 140, 469, 582], [97, 140, 469, 535, 582], [97, 140, 469, 523, 538, 764, 765], [97, 140, 469, 523, 538, 685, 717, 764], [97, 140, 469, 523, 534, 538, 561, 562, 573, 764], [97, 140, 469, 523, 534, 538, 764], [97, 140, 469, 523, 538, 722, 764], [97, 140, 469, 725], [97, 140, 469, 523, 538, 573, 725, 764], [97, 140, 469, 523, 534, 538, 559, 732, 764], [97, 140, 469, 523, 533, 538, 730, 732, 764], [97, 140, 469, 523, 533, 534, 538, 573, 729, 730, 764], [97, 140, 469, 523, 538, 561, 562, 573, 764], [97, 140, 469, 523, 538, 732, 736, 764], [97, 140, 469, 523, 534, 538, 555, 736, 764], [97, 140, 153, 162, 469, 523, 538, 558, 736, 764], [97, 140, 469, 523, 538, 555, 736, 764], [97, 140, 469, 523, 538, 736, 764], [97, 140, 469], [97, 140, 469, 573], [97, 140, 469, 578], [83, 97, 140, 447, 456, 779, 784], [83, 97, 140, 456, 758, 1155, 1257, 1258], [83, 97, 140, 447, 456, 758, 779, 784], [83, 97, 140, 456, 758, 1155, 1257, 1261], [83, 97, 140, 447, 456, 758, 772, 784], [83, 97, 140, 447, 456, 758, 779, 784, 1144], [83, 97, 140, 758, 779, 784, 1163], [83, 97, 140, 445, 447, 758, 779, 784, 1163], [83, 97, 140, 447, 758, 779, 784, 1163], [83, 97, 140, 445, 447, 456, 758, 779, 784, 1163, 1168], [83, 97, 140, 445, 779, 784], [97, 140, 473, 763, 770, 771, 772, 773, 774], [97, 140, 1144, 1148, 1156, 1157, 1158, 1159, 1160], [83, 97, 140, 445, 447, 456, 772, 779, 784, 1144, 1282], [83, 97, 140, 445, 447, 456, 772, 779, 784, 1144], [97, 140, 447, 456, 758, 779, 784], [83, 97, 140, 445, 456, 758, 773, 779, 784], [83, 97, 140, 445, 447, 456, 779, 784], [83, 97, 140, 447, 779, 784], [83, 97, 140, 779], [97, 140, 779, 784], [83, 97, 140, 456, 533, 758, 1143], [83, 97, 140, 445, 779], [83, 97, 140, 445, 447, 456, 758, 772, 779, 784], [83, 97, 140, 757], [83, 97, 140, 445, 779, 784, 1153, 1154, 1155], [83, 97, 140, 445, 760, 784, 1154], [83, 97, 140, 445, 784, 1145, 1146, 1147], [83, 97, 140, 445, 447, 779, 784], [83, 97, 140, 779, 1147, 1154, 1155], [83, 97, 140, 1141, 1142, 1143], [83, 97, 140, 758, 762, 779, 784, 1043, 1139], [97, 140, 746, 748], [97, 140, 746, 748, 750], [97, 140, 746, 748, 752], [97, 140, 445, 447, 779, 784], [83, 97, 140, 445, 447, 758, 772, 773, 779, 784, 785, 1140], [83, 97, 140, 784, 1225], [83, 97, 140, 445, 759, 779, 784], [97, 140, 445, 447], [97, 140, 748], [83, 97, 140, 784], [97, 140, 456, 757], [97, 140, 662, 684], [97, 140, 516, 523, 535, 764], [97, 140, 529, 533, 534], [97, 140, 534], [97, 140, 533, 534, 535], [97, 140, 533, 534], [97, 140, 533, 534, 722, 729], [97, 140, 534, 555], [97, 140, 534, 555, 578], [97, 140, 761], [97, 140, 153, 162, 555], [97, 140, 519, 523, 764], [97, 140, 766]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "impliedFormat": 1}, {"version": "8bf8b5e44e3c9c36f98e1007e8b7018c0f38d8adc07aecef42f5200114547c70", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5ce7a470bc3628408429040c4e3a53a27755022a32fd05e2cb694e7015386c7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "742d4b7b02ffc3ba3c4258a3d196457da2b3fec0125872fd0776c50302a11b9d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "03566a51ebc848dec449a4ed69518e9f20caa6ac123fa32676aaaabe64adae8e", "impliedFormat": 1}, {"version": "acd8fd5090ac73902278889c38336ff3f48af6ba03aa665eb34a75e7ba1dccc4", "impliedFormat": 1}, {"version": "d6258883868fb2680d2ca96bc8b1352cab69874581493e6d52680c5ffecdb6cc", "impliedFormat": 1}, {"version": "1b61d259de5350f8b1e5db06290d31eaebebc6baafd5f79d314b5af9256d7153", "impliedFormat": 1}, {"version": "f258e3960f324a956fc76a3d3d9e964fff2244ff5859dcc6ce5951e5413ca826", "impliedFormat": 1}, {"version": "643f7232d07bf75e15bd8f658f664d6183a0efaca5eb84b48201c7671a266979", "impliedFormat": 1}, {"version": "0f6666b58e9276ac3a38fdc80993d19208442d6027ab885580d93aec76b4ef00", "impliedFormat": 1}, {"version": "05fd364b8ef02fb1e174fbac8b825bdb1e5a36a016997c8e421f5fab0a6da0a0", "impliedFormat": 1}, {"version": "631eff75b0e35d1b1b31081d55209abc43e16b49426546ab5a9b40bdd40b1f60", "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "24bd580b5743dc56402c440dc7f9a4f5d592ad7a419f25414d37a7bfe11e342b", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "6bdc71028db658243775263e93a7db2fd2abfce3ca569c3cca5aee6ed5eb186d", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "4d2b0eb911816f66abe4970898f97a2cfc902bcd743cbfa5017fad79f7ef90d8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "e53a3c2a9f624d90f24bf4588aacd223e7bec1b9d0d479b68d2f4a9e6011147f", "impliedFormat": 1}, {"version": "24b8685c62562f5d98615c5a0c1d05f297cf5065f15246edfe99e81ec4c0e011", "impliedFormat": 1}, {"version": "93507c745e8f29090efb99399c3f77bec07db17acd75634249dc92f961573387", "impliedFormat": 1}, {"version": "339dc5265ee5ed92e536a93a04c4ebbc2128f45eeec6ed29f379e0085283542c", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "8d31155317e3cceb916d113be587617534034977bc364687235cdf4c7bd87e31", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b042aa5d277ad6963e2837179fd2f8fbb01968ac67115b0833c0244e93d1d50", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "9e025aa38cad40827cc30aca974fe33fe2c4652fe8c88f48dadbbbd6300c8b07", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "008e4cac37da1a6831aa43f6726da0073957ae89da2235082311eaf479b2ffa5", "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "5b75ca915164e4a7ad94a60729fe45b8a62e7750ab232d0122f8ccdd768f5314", "impliedFormat": 1}, {"version": "93bd413918fa921c8729cef45302b24d8b6c7855d72d5bf82d3972595ae8dcbf", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "dccdf1677e531e33f8ac961a68bc537418c9a414797c1ea7e91307501cdc3f5e", "impliedFormat": 1}, {"version": "e184c4b8918ef56c8c9e68bd79f3f3780e2d0d75bf2b8a41da1509a40c2deb46", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "70731d10d5311bd4cf710ef7f6539b62660f4b0bfdbb3f9fbe1d25fe6366a7fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b19db3600a17af69d4f33d08cc7076a7d19fb65bb36e442cac58929ec7c9482", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "ba1f814c22fd970255ddd60d61fb7e00c28271c933ab5d5cc19cd3ca66b8f57c", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "09d479208911ac3ac6a7c2fe86217fc1abe6c4f04e2d52e4890e500699eeab32", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "c40b3d3cfbb1227c8935f681c2480a32b560e387dd771d329cdbd1641f2d6da5", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "5b566927cad2ed2139655d55d690ffa87df378b956e7fe1c96024c4d9f75c4cf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c4a3720550d1787c8d6284040853c0781ff1e2cd8d842f2cb44547525ee34c36", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "fe5748232eaa52bbfd7ce314e52b246871731c5f41318fdaf6633cb14fa20da0", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "616075a6ac578cf5a013ee12964188b4412823796ce0b202c6f1d2e4ca8480d7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "865a2612f5ec073dd48d454307ccabb04c48f8b96fda9940c5ebfe6b4b451f51", "impliedFormat": 1}, {"version": "70f79528d7e02028b3c12dd10764893b22df4c6e2a329e66456aa11bb304cabb", "impliedFormat": 1}, {"version": "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", "impliedFormat": 1}, {"version": "1be330b3a0b00590633f04c3b35db7fa618c9ee079258e2b24c137eb4ffcd728", "impliedFormat": 1}, {"version": "0a5ab5c020557d3ccc84b92c0ca55ff790e886d92662aae668020d6320ab1867", "impliedFormat": 1}, {"version": "413df52d4ea14472c2fa5bee62f7a40abd1eb49be0b9722ee01ee4e52e63beb2", "impliedFormat": 1}, {"version": "db6d2d9daad8a6d83f281af12ce4355a20b9a3e71b82b9f57cddcca0a8964a96", "impliedFormat": 1}, {"version": "7bd32a723a12f78ed756747468f2030bdd55774c68f628de07598dba5b912b14", "impliedFormat": 1}, {"version": "24f8562308dd8ba6013120557fa7b44950b619610b2c6cb8784c79f11e3c4f90", "impliedFormat": 1}, {"version": "a1d3d6e9718cceaf1e4352845387af0620564d3d2dff02611a5c3276f73c26cb", "impliedFormat": 1}, {"version": "a86f82d646a739041d6702101afa82dcb935c416dd93cbca7fd754fd0282ce1f", "impliedFormat": 1}, {"version": "57d6ac03382e30e9213641ff4f18cf9402bb246b77c13c8e848c0b1ca2b7ef92", "impliedFormat": 1}, {"version": "ce75b1aebb33d510ff28af960a9221410a3eaf7f18fc5f21f9404075fba77256", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "57e47d02e88abef89d214cdf52b478104dc17997015746e288cbb580beaef266", "impliedFormat": 1}, {"version": "b1177acd771acfcc2648a03fc03ad3b3a1b1d2bdfa6769db0f669293b596ca13", "impliedFormat": 1}, {"version": "3494c5bf00c1a40293ee5ff5128334b63d346abbf560c8987202c92dbc5bdc48", "impliedFormat": 1}, {"version": "9e2739b32f741859263fdba0244c194ca8e96da49b430377930b8f721d77c000", "impliedFormat": 1}, {"version": "99d62b942e98f691f508fc752637fec27661970aa3b0f5eb5a1e2775b995c273", "impliedFormat": 1}, {"version": "a9af0e608929aaf9ce96bd7a7b99c9360636c31d73670e4af09a09950df97841", "impliedFormat": 1}, {"version": "48d37b90a04e753a925228f50304d02c4f95d57bf682f8bb688621c3cd9d32ec", "impliedFormat": 1}, {"version": "361e2b13c6765d7f85bb7600b48fde782b90c7c41105b7dab1f6e7871071ba20", "impliedFormat": 1}, {"version": "c86fe861cf1b4c46a0fb7d74dffe596cf679a2e5e8b1456881313170f092e3fa", "impliedFormat": 1}, {"version": "b6db56e4903e9c32e533b78ac85522de734b3d3a8541bf24d256058d464bf04b", "impliedFormat": 1}, {"version": "24daa0366f837d22c94a5c0bad5bf1fd0f6b29e1fae92dc47c3072c3fdb2fbd5", "impliedFormat": 1}, {"version": "b68c4ed987ef5693d3dccd85222d60769463aca404f2ffca1c4c42781dce388e", "impliedFormat": 1}, {"version": "889c00f3d32091841268f0b994beba4dceaa5df7573be12c2c829d7c5fbc232c", "impliedFormat": 1}, {"version": "65f43099ded6073336e697512d9b80f2d4fec3182b7b2316abf712e84104db00", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "12b8dfed70961bea1861e5d39e433580e71323abb5d33da6605182ec569db584", "impliedFormat": 1}, {"version": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "7e560f533aaf88cf9d3b427dcf6c112dd3f2ee26d610e2587583b6c354c753db", "impliedFormat": 1}, {"version": "71e0082342008e4dfb43202df85ea0986ef8e003c921a1e49999d0234a3019da", "impliedFormat": 1}, {"version": "27ab780875bcbb65e09da7496f2ca36288b0c541abaa75c311450a077d54ec15", "impliedFormat": 1}, {"version": "b620391fe8060cf9bedc176a4d01366e6574d7a71e0ac0ab344a4e76576fcbb8", "impliedFormat": 1}, {"version": "380647d8f3b7f852cca6d154a376dbf8ac620a2f12b936594504a8a852e71d2f", "impliedFormat": 1}, {"version": "3e7efde639c6a6c3edb9847b3f61e308bf7a69685b92f665048c45132f51c218", "impliedFormat": 1}, {"version": "df45ca1176e6ac211eae7ddf51336dc075c5314bc5c253651bae639defd5eec5", "impliedFormat": 1}, {"version": "ef61792acbfa8c27c9bd113f02731e66229f7d3a169e3c1993b508134f1a58e0", "impliedFormat": 1}, {"version": "9c82171d836c47486074e4ca8e059735bf97b205e70b196535b5efd40cbe1bc5", "impliedFormat": 1}, {"version": "94fe3281392e1015b22f39535878610b4fa6f1388dc8d78746be3bc4e4bb8950", "impliedFormat": 1}, {"version": "106c6025f1d99fd468fd8bf6e5bda724e11e5905a4076c5d29790b6c3745e50c", "impliedFormat": 1}, {"version": "ce41407ff95aad31e28897741dfffb236d966eb38894f7a791c3a575b53f9d02", "impliedFormat": 1}, {"version": "fac1803c07fbc9574815fdb83afddd9d0d4a2ce13f56d4e4cbb4525f8c09ee0a", "impliedFormat": 1}, {"version": "824c76aec8d8c7e65769688cbee102238c0ef421ed6686f41b2a7d8e7e78a931", "impliedFormat": 1}, {"version": "5eef43ef86c9c3945780211c2ce25cb9b66143a102713e56a2bea85163c5c3c7", "impliedFormat": 1}, {"version": "a2a1cdf7273ad6641938a487ecf2fdd38f60abce41907817e44ab39e482e8739", "impliedFormat": 1}, {"version": "c5426dbfc1cf90532f66965a7aa8c1136a78d4d0f96d8180ecbfc11d7722f1a5", "impliedFormat": 1}, {"version": "ca921bf56756cb6fe957f6af693a35251b134fb932dc13f3dfff0bb7106f80b4", "impliedFormat": 1}, {"version": "4548fac59ea69a3ffd6c0285a4c53e0d736d936937b74297e3b5c4dfcd902419", "impliedFormat": 1}, {"version": "4da246ee3b860278888dd51913e6407a09ca43530db886e7bec2a592c9b9bde6", "impliedFormat": 1}, {"version": "8c05ac9ead787bfc3e144b88bdc7d1ad8c0c7f1cd8412ab58cd3e1208d1990af", "impliedFormat": 1}, {"version": "a23185bc5ef590c287c28a91baf280367b50ae4ea40327366ad01f6f4a8edbc5", "impliedFormat": 1}, {"version": "65a15fc47900787c0bd18b603afb98d33ede930bed1798fc984d5ebb78b26cf9", "impliedFormat": 1}, {"version": "9d202701f6e0744adb6314d03d2eb8fc994798fc83d91b691b75b07626a69801", "impliedFormat": 1}, {"version": "de9d2df7663e64e3a91bf495f315a7577e23ba088f2949d5ce9ec96f44fba37d", "impliedFormat": 1}, {"version": "c7af78a2ea7cb1cd009cfb5bdb48cd0b03dad3b54f6da7aab615c2e9e9d570c5", "impliedFormat": 1}, {"version": "1ee45496b5f8bdee6f7abc233355898e5bf9bd51255db65f5ff7ede617ca0027", "impliedFormat": 1}, {"version": "0c7c947ff881c4274c0800deaa0086971e0bfe51f89a33bd3048eaa3792d4876", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "db01d18853469bcb5601b9fc9826931cc84cc1a1944b33cad76fd6f1e3d8c544", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dba114fb6a32b355a9cfc26ca2276834d72fe0e94cd2c3494005547025015369", "impliedFormat": 1}, {"version": "a8f8e6ab2fa07b45251f403548b78eaf2022f3c2254df3dc186cb2671fe4996d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fa6c12a7c0f6b84d512f200690bfc74819e99efae69e4c95c4cd30f6884c526e", "impliedFormat": 1}, {"version": "f1c32f9ce9c497da4dc215c3bc84b722ea02497d35f9134db3bb40a8d918b92b", "impliedFormat": 1}, {"version": "b73c319af2cc3ef8f6421308a250f328836531ea3761823b4cabbd133047aefa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e433b0337b8106909e7953015e8fa3f2d30797cea27141d1c5b135365bb975a6", "impliedFormat": 1}, {"version": "15b36126e0089bfef173ab61329e8286ce74af5e809d8a72edcafd0cc049057f", "impliedFormat": 1}, {"version": "ddff7fc6edbdc5163a09e22bf8df7bef75f75369ebd7ecea95ba55c4386e2441", "impliedFormat": 1}, {"version": "13283350547389802aa35d9f2188effaeac805499169a06ef5cd77ce2a0bd63f", "impliedFormat": 1}, {"version": "2e4f37ffe8862b14d8e24ae8763daaa8340c0df0b859d9a9733def0eee7562d9", "impliedFormat": 1}, {"version": "d07cbc787a997d83f7bde3877fec5fb5b12ce8c1b7047eb792996ed9726b4dde", "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "impliedFormat": 1}, {"version": "8bba776476c48b0e319d243f353190f24096057acede3c2f620fee17ff885dba", "impliedFormat": 1}, {"version": "b83cb14474fa60c5f3ec660146b97d122f0735627f80d82dd03e8caa39b4388c", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "2b2f9dac86b659e6d5cd623bcc21519910a48114fc0cef52d8f86962c48d44e2", "impliedFormat": 1}, {"version": "7e8b76334c75984d57a810a0652c61066ffacede59001dfc5c633565f791ee60", "impliedFormat": 1}, {"version": "72ca9ca89ca15055cbb6ce767b6bf56615be5f1ea6a87ab432ee0603c8d19010", "impliedFormat": 1}, {"version": "7274fbffbd7c9589d8d0ffba68157237afd5cecff1e99881ea3399127e60572f", "impliedFormat": 1}, {"version": "b73cbf0a72c8800cf8f96a9acfe94f3ad32ca71342a8908b8ae484d61113f647", "impliedFormat": 1}, {"version": "bae6dd176832f6423966647382c0d7ba9e63f8c167522f09a982f086cd4e8b23", "impliedFormat": 1}, {"version": "208c9af9429dd3c76f5927b971263174aaa4bc7621ddec63f163640cbd3c473c", "impliedFormat": 1}, {"version": "20865ac316b8893c1a0cc383ccfc1801443fbcc2a7255be166cf90d03fac88c9", "impliedFormat": 1}, {"version": "c9958eb32126a3843deedda8c22fb97024aa5d6dd588b90af2d7f2bfac540f23", "impliedFormat": 1}, {"version": "d682336018141807fb602709e2d95a192828fcb8d5ba06dda3833a8ea98f69e3", "impliedFormat": 1}, {"version": "461d0ad8ae5f2ff981778af912ba71b37a8426a33301daa00f21c6ccb27f8156", "impliedFormat": 1}, {"version": "e927c2c13c4eaf0a7f17e6022eee8519eb29ef42c4c13a31e81a611ab8c95577", "impliedFormat": 1}, {"version": "fcafff163ca5e66d3b87126e756e1b6dfa8c526aa9cd2a2b0a9da837d81bbd72", "impliedFormat": 1}, {"version": "70246ad95ad8a22bdfe806cb5d383a26c0c6e58e7207ab9c431f1cb175aca657", "impliedFormat": 1}, {"version": "f00f3aa5d64ff46e600648b55a79dcd1333458f7a10da2ed594d9f0a44b76d0b", "impliedFormat": 1}, {"version": "772d8d5eb158b6c92412c03228bd9902ccb1457d7a705b8129814a5d1a6308fc", "impliedFormat": 1}, {"version": "45490817629431853543adcb91c0673c25af52a456479588b6486daba34f68bb", "impliedFormat": 1}, {"version": "802e797bcab5663b2c9f63f51bdf67eff7c41bc64c0fd65e6da3e7941359e2f7", "impliedFormat": 1}, {"version": "b01bd582a6e41457bc56e6f0f9de4cb17f33f5f3843a7cf8210ac9c18472fb0f", "impliedFormat": 1}, {"version": "8b4327413e5af38cd8cb97c59f48c3c866015d5d642f28518e3a891c469f240e", "impliedFormat": 1}, {"version": "cecad464ddaf764e5490018d248a8df1733f3d63435fbddac72941c1f4005b66", "impliedFormat": 1}, {"version": "6124e973eab8c52cabf3c07575204efc1784aca6b0a30c79eb85fe240a857efa", "impliedFormat": 1}, {"version": "0d891735a21edc75df51f3eb995e18149e119d1ce22fd40db2b260c5960b914e", "impliedFormat": 1}, {"version": "3b414b99a73171e1c4b7b7714e26b87d6c5cb03d200352da5342ab4088a54c85", "impliedFormat": 1}, {"version": "51b1709e7ad186919a0e30237a8607100143a86d28771b3d3f046359aca1e65c", "impliedFormat": 1}, {"version": "0a437ae178f999b46b6153d79095b60c42c996bc0458c04955f1c996dc68b971", "impliedFormat": 1}, {"version": "74b2a5e5197bd0f2e0077a1ea7c07455bbea67b87b0869d9786d55104006784f", "impliedFormat": 1}, {"version": "4a7baeb6325920044f66c0f8e5e6f1f52e06e6d87588d837bdf44feb6f35c664", "impliedFormat": 1}, {"version": "6dcf60530c25194a9ee0962230e874ff29d34c59605d8e069a49928759a17e0a", "impliedFormat": 1}, {"version": "56013416784a6b754f3855f8f2bf6ce132320679b8a435389aca0361bce4df6b", "impliedFormat": 1}, {"version": "43e96a3d5d1411ab40ba2f61d6a3192e58177bcf3b133a80ad2a16591611726d", "impliedFormat": 1}, {"version": "224e9eedb2ea67e27f28d699b19b1d966e9320e9ea8ac233b2a31dbd753b0dfe", "impliedFormat": 1}, {"version": "002eae065e6960458bda3cf695e578b0d1e2785523476f8a9170b103c709cd4f", "impliedFormat": 1}, {"version": "c51641ab4bfa31b7a50a0ca37edff67f56fab3149881024345b13f2b48b7d2de", "impliedFormat": 1}, {"version": "a57b1802794433adec9ff3fed12aa79d671faed86c49b09e02e1ac41b4f1d33a", "impliedFormat": 1}, {"version": "52abbd5035a97ebfb4240ec8ade2741229a7c26450c84eb73490dc5ea048b911", "impliedFormat": 1}, {"version": "1042064ece5bb47d6aba91648fbe0635c17c600ebdf567588b4ca715602f0a9d", "impliedFormat": 1}, {"version": "4360ad4de54de2d5c642c4375d5eab0e7fe94ebe8adca907e6c186bbef75a54d", "impliedFormat": 1}, {"version": "4a889f2c763edb4d55cb624257272ac10d04a1cad2ed2948b10ed4a7fda2a428", "impliedFormat": 1}, {"version": "7bb79aa2fead87d9d56294ef71e056487e848d7b550c9a367523ee5416c44cfa", "impliedFormat": 1}, {"version": "9c9cae45dc94c2192c7d25f80649414fa13c425d0399a2c7cb2b979e4e50af42", "impliedFormat": 1}, {"version": "6c87b6bcf4336b29c837ea49afbdde69cc15a91cbbfd9f20c0af8694927dec08", "impliedFormat": 1}, {"version": "27ff4196654e6373c9af16b6165120e2dd2169f9ad6abb5c935af5abd8c7938c", "impliedFormat": 1}, {"version": "6dd9bcf10678b889842d467706836a0ab42e6c58711e33918ed127073807ee65", "impliedFormat": 1}, {"version": "8c030e515014c10a2b98f9f48408e3ba18023dfd3f56e3312c6c2f3ae1f55a16", "impliedFormat": 1}, {"version": "dafc31e9e8751f437122eb8582b93d477e002839864410ff782504a12f2a550c", "impliedFormat": 1}, {"version": "ef9efc827cdad89c4ee54142164c793f530aa4d844ca9121cc35368310d5fb9c", "impliedFormat": 1}, {"version": "643672ce383e1c58ea665a92c5481f8441edbd3e91db36e535abccbc9035adeb", "impliedFormat": 1}, {"version": "8fa022ea514ce0ea78ac9b7092a9f97f08ead20c839c779891019e110fce8307", "impliedFormat": 1}, {"version": "c93235337600b786fd7d0ff9c71a00f37ca65c4d63e5d695fc75153be2690f09", "impliedFormat": 1}, {"version": "fa45f48f2def181ab2fb107a032c91b6c043ad05a179f3fbaafb8e5411fd01e4", "impliedFormat": 1}, {"version": "a8e493c0355aabdd495e141bf1c4ec93454a0698c8675df466724adc2fcfe630", "impliedFormat": 1}, {"version": "99702c9058170ae70ea72acbf01be3111784f06152dbf478f52c9afe423528bd", "impliedFormat": 1}, {"version": "cf32f58a7ad3498c69c909121772971ffdee176b882f39c78532d0e0ab41a30d", "impliedFormat": 1}, {"version": "e2bbc579a2fda9473e06b2a68d693e56928900f73ccfc03dabea789fe144e8a5", "impliedFormat": 1}, {"version": "ce0df82a9ae6f914ba08409d4d883983cc08e6d59eb2df02d8e4d68309e7848b", "impliedFormat": 1}, {"version": "796273b2edc72e78a04e86d7c58ae94d370ab93a0ddf40b1aa85a37a1c29ecd7", "impliedFormat": 1}, {"version": "5df15a69187d737d6d8d066e189ae4f97e41f4d53712a46b2710ff9f8563ec9f", "impliedFormat": 1}, {"version": "e17cd049a1448de4944800399daa4a64c5db8657cc9be7ef46be66e2a2cd0e7c", "impliedFormat": 1}, {"version": "d05fb434f4ba073aed74b6c62eff1723c835de2a963dbb091e000a2decb5a691", "impliedFormat": 1}, {"version": "bff8c8bffbf5f302a30ccb1c0557dae477892d50a80eecfe393bd89bac7fb41d", "impliedFormat": 1}, {"version": "43ba4f2fa8c698f5c304d21a3ef596741e8e85a810b7c1f9b692653791d8d97a", "impliedFormat": 1}, {"version": "4d4927cbee21750904af7acf940c5e3c491b4d5ebc676530211e389dd375607a", "impliedFormat": 1}, {"version": "72105519d0390262cf0abe84cf41c926ade0ff475d35eb21307b2f94de985778", "impliedFormat": 1}, {"version": "8a97e578a9bc40eb4f1b0ca78f476f2e9154ecbbfd5567ee72943bab37fc156a", "impliedFormat": 1}, {"version": "a58abf1f5c8feb335475097abeddd32fd71c4dc2065a3d28cf15cacabad9654a", "impliedFormat": 1}, {"version": "ccf6dd45b708fb74ba9ed0f2478d4eb9195c9dfef0ff83a6092fa3cf2ff53b4f", "impliedFormat": 1}, {"version": "2d7db1d73456e8c5075387d4240c29a2a900847f9c1bff106a2e490da8fbd457", "impliedFormat": 1}, {"version": "2b15c805f48e4e970f8ec0b1915f22d13ca6212375e8987663e2ef5f0205e832", "impliedFormat": 1}, {"version": "f22d05663d873ee7a600faf78abb67f3f719d32266803440cf11d5db7ac0cab2", "impliedFormat": 1}, {"version": "f0f05149debcf31b3a717ce8dd16e0323a789905cb9e27239167b604153b8885", "impliedFormat": 1}, {"version": "35069c2c417bd7443ae7c7cafd1de02f665bf015479fec998985ffbbf500628c", "impliedFormat": 1}, {"version": "b4f4d239a6632b86b315a6e4cfe0fac4e4bf6c934263bc07dd2bf5c7dbb8e6a5", "impliedFormat": 1}, {"version": "0d44227395ae4a117dd7c8c9a048e18ade1f1f631bc5b883f9d469126e3cedab", "impliedFormat": 1}, {"version": "9e21f8e2c0cfea713a4a372f284b60089c0841eb90bf3610539d89dbcd12d65a", "impliedFormat": 1}, {"version": "045b752f44bf9bbdcaffd882424ab0e15cb8d11fa94e1448942e338c8ef19fba", "impliedFormat": 1}, {"version": "2894c56cad581928bb37607810af011764a2f511f575d28c9f4af0f2ef02d1ab", "impliedFormat": 1}, {"version": "0a72186f94215d020cb386f7dca81d7495ab6c17066eb07d0f44a5bf33c1b21a", "impliedFormat": 1}, {"version": "a072c5f254d5cbb6522c0d4eeeb7cc4a6ce7f2f8ad84e2593d903bfe3aa44176", "impliedFormat": 1}, {"version": "52b390f86821086a1be50100487faa9f7b23fc04343efb590f304382b4950e04", "impliedFormat": 1}, {"version": "87122b31fe473758a5724388c93826caab566f62be2196aefc2ae8b04b814b52", "impliedFormat": 1}, {"version": "063ab26d3488a665d2c3bc963b18ce220dad7351190629179165bc8c499c6cd9", "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "impliedFormat": 1}, {"version": "2652448ac55a2010a1f71dd141f828b682298d39728f9871e1cdf8696ef443fd", "impliedFormat": 1}, {"version": "fb400501bee56d86fa9b490e9d8b07d7df163d34d8235fcea27c3f9e8d064d1a", "impliedFormat": 1}, {"version": "120599fd965257b1f4d0ff794bc696162832d9d8467224f4665f713a3119078b", "impliedFormat": 1}, {"version": "5433f33b0a20300cca35d2f229a7fc20b0e8477c44be2affeb21cb464af60c76", "impliedFormat": 1}, {"version": "db036c56f79186da50af66511d37d9fe77fa6793381927292d17f81f787bb195", "impliedFormat": 1}, {"version": "bd4131091b773973ca5d2326c60b789ab1f5e02d8843b3587effe6e1ea7c9d86", "impliedFormat": 1}, {"version": "794998dc1c5a19ce77a75086fe829fb9c92f2fd07b5631c7d5e0d04fd9bc540c", "impliedFormat": 1}, {"version": "409678793827cdf5814e027b1f9e52a0445acb1c322282311c1c4e0855a0918e", "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "impliedFormat": 1}, {"version": "0427df5c06fafc5fe126d14b9becd24160a288deff40e838bfbd92a35f8d0d00", "impliedFormat": 1}, {"version": "3545dc8a9bdbd33db34462af7eed83f703083e4fee9135dadbba7edfe1e7db3c", "impliedFormat": 1}, {"version": "7b5153a9b237898879441e5ddb576ded76ef3ab4c5baee4bb749ca5c72fc395d", "impliedFormat": 1}, {"version": "49c346823ba6d4b12278c12c977fb3a31c06b9ca719015978cb145eb86da1c61", "impliedFormat": 1}, {"version": "bfac6e50eaa7e73bb66b7e052c38fdc8ccfc8dbde2777648642af33cf349f7f1", "impliedFormat": 1}, {"version": "92f7c1a4da7fbfd67a2228d1687d5c2e1faa0ba865a94d3550a3941d7527a45d", "impliedFormat": 1}, {"version": "f53b120213a9289d9a26f5af90c4c686dd71d91487a0aa5451a38366c70dc64b", "impliedFormat": 1}, {"version": "83fe880c090afe485a5c02262c0b7cdd76a299a50c48d9bde02be8e908fb4ae6", "impliedFormat": 1}, {"version": "d5c2934185201f0768fb80d220f0e617cd05aa4c0c791ffcd508646c474b3c44", "impliedFormat": 1}, {"version": "57d67b72e06059adc5e9454de26bbfe567d412b962a501d263c75c2db430f40e", "impliedFormat": 1}, {"version": "6511e4503cf74c469c60aafd6589e4d14d5eb0a25f9bf043dcbecdf65f261972", "impliedFormat": 1}, {"version": "e326c507507d6c6f3df4152e9e132a6189b30e14a262782796c2a627ba5d42cc", "impliedFormat": 1}, {"version": "75efc43fb206f3825eb219c96b1e59fdabf2f2f042f424fa5f96335b99897540", "impliedFormat": 1}, {"version": "a67b87d0281c97dfc1197ef28dfe397fc2c865ccd41f7e32b53f647184cc7307", "impliedFormat": 1}, {"version": "771ffb773f1ddd562492a6b9aaca648192ac3f056f0e1d997678ff97dbb6bf9b", "impliedFormat": 1}, {"version": "232f70c0cf2b432f3a6e56a8dc3417103eb162292a9fd376d51a3a9ea5fbbf6f", "impliedFormat": 1}, {"version": "ca651584d8d718c1f0655ec4b0c340fbcd967ec1e1758807af3a3f43bc81f81e", "impliedFormat": 1}, {"version": "cfb5f0ab72180f4e0b9ed1534847a63d5394b9a8ee685ae149d25fd53f1aec66", "impliedFormat": 1}, {"version": "8a0e762ceb20c7e72504feef83d709468a70af4abccb304f32d6b9bac1129b2c", "impliedFormat": 1}, {"version": "f613e4e752659ebd241be4d991c05200248b50e753fcecf50a249d30f4367794", "impliedFormat": 1}, {"version": "9252d498a77517aab5d8d4b5eb9d71e4b225bbc7123df9713e08181de63180f6", "impliedFormat": 1}, {"version": "de1ccef0cb3623291d55871e39eb7005cb79d8da519cb46959b0ba5e2422184f", "impliedFormat": 1}, {"version": "35e6379c3f7cb27b111ad4c1aa69538fd8e788ab737b8ff7596a1b40e96f4f90", "impliedFormat": 1}, {"version": "1fffe726740f9787f15b532e1dc870af3cd964dbe29e191e76121aa3dd8693f2", "impliedFormat": 1}, {"version": "7cd657e359eac7829db5f02c856993e8945ffccc71999cdfb4ab3bf801a1bbc6", "impliedFormat": 1}, {"version": "1a82deef4c1d39f6882f28d275cad4c01f907b9b39be9cbc472fcf2cf051e05b", "impliedFormat": 1}, {"version": "4b20fcf10a5413680e39f5666464859fc56b1003e7dfe2405ced82371ebd49b6", "impliedFormat": 1}, {"version": "f0f3f57e29b40e9cb0c4b155a96de2f61e51700d2c335dd547ef3c85e668c6a8", "impliedFormat": 1}, {"version": "f7d628893c9fa52ba3ab01bcb5e79191636c4331ee5667ecc6373cbccff8ae12", "impliedFormat": 1}, {"version": "35117a2e59d2eca30c1848c9ff328c75d131d3468f8649c9012ca885c80fe2ce", "impliedFormat": 1}, {"version": "6a76daf108400ca1333e325772f24f40ebdde2120ef68f8c87d7a1adf0257541", "impliedFormat": 1}, {"version": "313698394e61f0343ebf11b64e5cde7e948110eaba98e8dbd7bdd67ee8df2639", "impliedFormat": 1}, {"version": "6459054aabb306821a043e02b89d54da508e3a6966601a41e71c166e4ea1474f", "impliedFormat": 1}, {"version": "bb37588926aba35c9283fe8d46ebf4e79ffe976343105f5c6d45f282793352b2", "impliedFormat": 1}, {"version": "05c97cddbaf99978f83d96de2d8af86aded9332592f08ce4a284d72d0952c391", "impliedFormat": 1}, {"version": "72179f9dd22a86deaad4cc3490eb0fe69ee084d503b686985965654013f1391b", "impliedFormat": 1}, {"version": "2e6114a7dd6feeef85b2c80120fdbfb59a5529c0dcc5bfa8447b6996c97a69f5", "impliedFormat": 1}, {"version": "7b6ff760c8a240b40dab6e4419b989f06a5b782f4710d2967e67c695ef3e93c4", "impliedFormat": 1}, {"version": "c8f004e6036aa1c764ad4ec543cf89a5c1893a9535c80ef3f2b653e370de45e6", "impliedFormat": 1}, {"version": "91357dba2d5a7234ccfae834dc8363b5635e08f373bd18f548a9046b01864619", "impliedFormat": 1}, {"version": "f31bbb122869d8903ff13c1036bdefc1e6a5bac9b2c3c35e42a9de84d43cd04a", "impliedFormat": 1}, {"version": "c7fdbcfa0991e15215e2a5751676115cac943b39289791546c7197d7bb889c51", "impliedFormat": 1}, {"version": "f974e4a06953682a2c15d5bd5114c0284d5abf8bc0fe4da25cb9159427b70072", "impliedFormat": 1}, {"version": "50256e9c31318487f3752b7ac12ff365c8949953e04568009c8705db802776fb", "impliedFormat": 1}, {"version": "7d73b24e7bf31dfb8a931ca6c4245f6bb0814dfae17e4b60c9e194a631fe5f7b", "impliedFormat": 1}, {"version": "4eac446ac161245bfc6daa95f2cc64d2da4f7844e36a7a5641abfd4771ef0923", "impliedFormat": 1}, {"version": "8de9fe97fa9e00ec00666fa77ab6e91b35d25af8ca75dabcb01e14ad3299b150", "impliedFormat": 1}, {"version": "076527b1c2fd207de3101ba10e0c2b7d155aa8369cc7fe3eed723811e428223d", "impliedFormat": 1}, {"version": "6c800b281b9e89e69165fd11536195488de3ff53004e55905e6c0059a2d8591e", "impliedFormat": 1}, {"version": "7d4254b4c6c67a29d5e7f65e67d72540480ac2cfb041ca484847f5ae70480b62", "impliedFormat": 1}, {"version": "397f568f996f8ffcf12d9156342552b0da42f6571eadba6bce61c99e1651977d", "impliedFormat": 1}, {"version": "ff0c0d446569f8756be0882b520fd94429468de9f922ab6bf9eed4da55eb0187", "impliedFormat": 1}, {"version": "d663134457d8d669ae0df34eabd57028bddc04fc444c4bc04bc5215afc91e1f4", "impliedFormat": 1}, {"version": "a52674bc98da7979607e0f44d4c015c59c1b1d264c83fc50ec79ff2cfea06723", "impliedFormat": 1}, {"version": "89b3d1b267c4380fbb8e5cadccbb284843b90066f16a2f6e8a5b3a030bb7dcfb", "impliedFormat": 1}, {"version": "f58226e78464f9c85be6cf47c665a8e33b32121ab4cdb2670b66a06f1114a55c", "impliedFormat": 1}, {"version": "9b06ce81ad598c9c6b011cb66182fa66575ad6bd1f8f655830a6a0223a197ab7", "impliedFormat": 1}, {"version": "e108f38a04a607f9386d68a4c6f3fdae1b712960f11f6482c6f1769bab056c2e", "impliedFormat": 1}, {"version": "a3128a84a9568762a2996df79717d92154d18dd894681fc0ab3a098fa7f8ee3b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "347791f3792f436950396dd6171d6450234358001ae7c94ca209f1406566ccbf", "impliedFormat": 1}, {"version": "dd80b1e600d00f5c6a6ba23f455b84a7db121219e68f89f10552c54ba46e4dc9", "impliedFormat": 1}, {"version": "2896c2e673a5d3bd9b4246811f79486a073cbb03950c3d252fba10003c57411a", "impliedFormat": 1}, {"version": "616775f16134fa9d01fc677ad3f76e68c051a056c22ab552c64cc281a9686790", "impliedFormat": 1}, {"version": "65c24a8baa2cca1de069a0ba9fba82a173690f52d7e2d0f1f7542d59d5eb4db0", "impliedFormat": 1}, {"version": "f9fe6af238339a0e5f7563acee3178f51db37f32a2e7c09f85273098cee7ec49", "impliedFormat": 1}, {"version": "51bf55bb6eb80f11b3aa59fb0a9571565a7ea304a19381f6da5630f4b2e206c4", "impliedFormat": 1}, {"version": "77e71242e71ebf8528c5802993697878f0533db8f2299b4d36aa015bae08a79c", "impliedFormat": 1}, {"version": "98a787be42bd92f8c2a37d7df5f13e5992da0d967fab794adbb7ee18370f9849", "impliedFormat": 1}, {"version": "5c96bad5f78466785cdad664c056e9e2802d5482ca5f862ed19ba34ffbb7b3a4", "impliedFormat": 1}, {"version": "b7fff2d004c5879cae335db8f954eb1d61242d9f2d28515e67902032723caeab", "impliedFormat": 1}, {"version": "5f3dc10ae646f375776b4e028d2bed039a93eebbba105694d8b910feebbe8b9c", "impliedFormat": 1}, {"version": "bb0cd7862b72f5eba39909c9889d566e198fcaddf7207c16737d0c2246112678", "impliedFormat": 1}, {"version": "4545c1a1ceca170d5d83452dd7c4994644c35cf676a671412601689d9a62da35", "impliedFormat": 1}, {"version": "320f4091e33548b554d2214ce5fc31c96631b513dffa806e2e3a60766c8c49d9", "impliedFormat": 1}, {"version": "a2d648d333cf67b9aeac5d81a1a379d563a8ffa91ddd61c6179f68de724260ff", "impliedFormat": 1}, {"version": "d90d5f524de38889d1e1dbc2aeef00060d779f8688c02766ddb9ca195e4a713d", "impliedFormat": 1}, {"version": "a3f41ed1b4f2fc3049394b945a68ae4fdefd49fa1739c32f149d32c0545d67f5", "impliedFormat": 1}, {"version": "bad68fd0401eb90fe7da408565c8aee9c7a7021c2577aec92fa1382e8876071a", "impliedFormat": 1}, {"version": "47699512e6d8bebf7be488182427189f999affe3addc1c87c882d36b7f2d0b0e", "impliedFormat": 1}, {"version": "fec01479923e169fb52bd4f668dbeef1d7a7ea6e6d491e15617b46f2cacfa37d", "impliedFormat": 1}, {"version": "8a8fb3097ba52f0ae6530ec6ab34e43e316506eb1d9aa29420a4b1e92a81442d", "impliedFormat": 1}, {"version": "44e09c831fefb6fe59b8e65ad8f68a7ecc0e708d152cfcbe7ba6d6080c31c61e", "impliedFormat": 1}, {"version": "1c0a98de1323051010ce5b958ad47bc1c007f7921973123c999300e2b7b0ecc0", "impliedFormat": 1}, {"version": "4655709c9cb3fd6db2b866cab7c418c40ed9533ce8ea4b66b5f17ec2feea46a9", "impliedFormat": 1}, {"version": "87affad8e2243635d3a191fa72ef896842748d812e973b7510a55c6200b3c2a4", "impliedFormat": 1}, {"version": "ad036a85efcd9e5b4f7dd5c1a7362c8478f9a3b6c3554654ca24a29aa850a9c5", "impliedFormat": 1}, {"version": "fedebeae32c5cdd1a85b4e0504a01996e4a8adf3dfa72876920d3dd6e42978e7", "impliedFormat": 1}, {"version": "22b87e96a61c525464e115db0148593a861e77806fd37ab280e1903019a6e212", "impliedFormat": 1}, {"version": "cdf21eee8007e339b1b9945abf4a7b44930b1d695cc528459e68a3adc39a622e", "impliedFormat": 1}, {"version": "330896c1a2b9693edd617be24fbf9e5895d6e18c7955d6c08f028f272b37314d", "impliedFormat": 1}, {"version": "1d9c0a9a6df4e8f29dc84c25c5aa0bb1da5456ebede7a03e03df08bb8b27bae6", "impliedFormat": 1}, {"version": "84380af21da938a567c65ef95aefb5354f676368ee1a1cbb4cae81604a4c7d17", "impliedFormat": 1}, {"version": "1af3e1f2a5d1332e136f8b0b95c0e6c0a02aaabd5092b36b64f3042a03debf28", "impliedFormat": 1}, {"version": "30d8da250766efa99490fc02801047c2c6d72dd0da1bba6581c7e80d1d8842a4", "impliedFormat": 1}, {"version": "03566202f5553bd2d9de22dfab0c61aa163cabb64f0223c08431fb3fc8f70280", "impliedFormat": 1}, {"version": "9a01f12466488eccd8d9eafc8fecb9926c175a4bf4a8f73a07c3bcf8b3363282", "impliedFormat": 1}, {"version": "b80f624162276f24a4ec78b8e86fbee80ca255938e12f8b58e7a8f1a6937120b", "impliedFormat": 1}, {"version": "1de80059b8078ea5749941c9f863aa970b4735bdbb003be4925c853a8b6b4450", "impliedFormat": 1}, {"version": "1d079c37fa53e3c21ed3fa214a27507bda9991f2a41458705b19ed8c2b61173d", "impliedFormat": 1}, {"version": "5bf5c7a44e779790d1eb54c234b668b15e34affa95e78eada73e5757f61ed76a", "impliedFormat": 1}, {"version": "5835a6e0d7cd2738e56b671af0e561e7c1b4fb77751383672f4b009f4e161d70", "impliedFormat": 1}, {"version": "5c634644d45a1b6bc7b05e71e05e52ec04f3d73d9ac85d5927f647a5f965181a", "impliedFormat": 1}, {"version": "4b7f74b772140395e7af67c4841be1ab867c11b3b82a51b1aeb692822b76c872", "impliedFormat": 1}, {"version": "27be6622e2922a1b412eb057faa854831b95db9db5035c3f6d4b677b902ab3b7", "impliedFormat": 1}, {"version": "b95a6f019095dd1d48fd04965b50dfd63e5743a6e75478343c46d2582a5132bf", "impliedFormat": 99}, {"version": "c2008605e78208cfa9cd70bd29856b72dda7ad89df5dc895920f8e10bcb9cd0a", "impliedFormat": 99}, {"version": "a61e739f0b2c0165086c77a28d7e4b58a2a8703c646cd1e1641788484afc6ff2", "impliedFormat": 99}, {"version": "63a7595a5015e65262557f883463f934904959da563b4f788306f699411e9bac", "impliedFormat": 1}, {"version": "9e40365afca304124bc53eb03412643abf074a1580e4dc279a7a16000d11f985", "impliedFormat": 1}, {"version": "4ba137d6553965703b6b55fd2000b4e07ba365f8caeb0359162ad7247f9707a6", "impliedFormat": 1}, {"version": "ceec3c81b2d81f5e3b855d9367c1d4c664ab5046dff8fd56552df015b7ccbe8f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4e18cfe14fa8602c7ff80cbbddb91e31608e5ae20bd361fe7e6a607706cb033c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a1219ee18b9282b4c6a31f1f0bcc9255b425e99363268ba6752a932cf76662f0", "impliedFormat": 1}, {"version": "3dc14e1ab45e497e5d5e4295271d54ff689aeae00b4277979fdd10fa563540ae", "impliedFormat": 1}, {"version": "1d63055b690a582006435ddd3aa9c03aac16a696fac77ce2ed808f3e5a06efab", "impliedFormat": 1}, {"version": "b789bf89eb19c777ed1e956dbad0925ca795701552d22e68fd130a032008b9f9", "impliedFormat": 1}, "f2b3bca04d1bfe583daae1e1f798c92ec24bb6693bd88d0a09ba6802dee362a8", {"version": "b7ca2f47522d4ea41e65ff92c4c6dd9c4c8260da7c456a7631a9c88dc056b4d0", "impliedFormat": 1}, {"version": "4f01e4d0959f9125b89e5737eb1ca2bfa69fd6b7d6126eba22feb8b505b00cde", "impliedFormat": 1}, {"version": "4363a1adb9c77f2ed1ca383a41fbab1afadd35d485c018b2f84e834edde6a2c7", "impliedFormat": 1}, {"version": "1d6458533adb99938d041a93e73c51d6c00e65f84724e9585e3cc8940b25523f", "impliedFormat": 1}, {"version": "b0878fbd194bdc4d49fc9c42bfeeb25650842fe1412c88e283dc80854b019768", "impliedFormat": 1}, {"version": "a892ea0b88d9d19281e99d61baba3155200acced679b8af290f86f695b589b16", "impliedFormat": 1}, {"version": "03b42e83b3bcdf5973d28641d72b81979e3ce200318e4b46feb8347a1828cd5d", "impliedFormat": 1}, {"version": "8a3d57426cd8fb0d59f6ca86f62e05dde8bfd769de3ba45a1a4b2265d84bac5a", "impliedFormat": 1}, {"version": "afc6e1f323b476fdf274e61dab70f26550a1be2353e061ab34e6eed180d349b6", "impliedFormat": 1}, {"version": "7c14483430d839976481fe42e26207f5092f797e1a4190823086f02cd09c113c", "impliedFormat": 1}, {"version": "828a3bea78921789cbd015e968b5b09b671f19b1c14c4bbf3490b58fbf7d6841", "impliedFormat": 1}, {"version": "69759c42e48938a714ee2f002fe5679a7ab56f0b5f29d571e4c31a5398d038fe", "impliedFormat": 1}, {"version": "6e5e666fa6adeb60774b576084eeff65181a40443166f0a46ae9ba0829300fcb", "impliedFormat": 1}, {"version": "1a4d43bdc0f2e240395fd204e597349411c1141dd08f5114c37d6268c3c9d577", "impliedFormat": 1}, {"version": "874e58f8d945c7ac25599128a40ec9615aa67546e91ca12cbf12f97f6baf54ff", "impliedFormat": 1}, {"version": "da2627da8d01662eb137ccd84af7ffa8c94cf2b2547d4970f17802324e54defc", "impliedFormat": 1}, {"version": "07af06b740c01ed0473ebdd3f2911c8e4f5ebf4094291d31db7c1ab24ff559aa", "impliedFormat": 1}, {"version": "ba1450574b1962fcf595fc53362b4d684c76603da5f45b44bc4c7eeed5de045b", "impliedFormat": 1}, {"version": "b7903668ee9558d758c64c15d66a89ed328fee5ac629b2077415f0b6ca2f41bc", "impliedFormat": 1}, {"version": "c7628425ee3076c4530b4074f7d48f012577a59f5ddade39cea236d6405c36ba", "impliedFormat": 1}, {"version": "28c8aff998cc623ab0864a26e2eb1a31da8eb04e59f31fa80f02ec78eb225bcd", "impliedFormat": 1}, {"version": "78d542989bdf7b6ba5410d5a884c0ab5ec54aa9ce46916d34267f885fcf65270", "impliedFormat": 1}, {"version": "4d95060af2775a3a86db5ab47ca7a0ed146d1f6f13e71d96f7ac3b321718a832", "impliedFormat": 1}, {"version": "6708cd298541a89c2abf66cceffc6c661f8ee31c013f98ddb58d2ec4407d0876", "impliedFormat": 1}, {"version": "2e90928c29c445563409d89a834662c2ba6a660204fb3d4dc181914e77f8e29d", "impliedFormat": 1}, {"version": "84be1b8b8011c2aab613901b83309d017d57f6e1c2450dfda11f7b107953286a", "impliedFormat": 1}, {"version": "d7af890ef486b4734d206a66b215ebc09f6743b7fb2f3c79f2fb8716d1912d27", "impliedFormat": 1}, {"version": "7e82c1d070c866eaf448ac7f820403d4e1b86112de582901178906317efc35ad", "impliedFormat": 1}, {"version": "c5c4f547338457f4e8e2bec09f661af14ee6e157c7dc711ccca321ab476dbc6d", "impliedFormat": 1}, {"version": "223e233cb645b44fa058320425293e68c5c00744920fc31f55f7df37b32f11ad", "impliedFormat": 1}, {"version": "1394fe4da1ab8ab3ea2f2b0fcbfd7ccbb8f65f5581f98d10b037c91194141b03", "impliedFormat": 1}, {"version": "086d9e59a579981bdf4f3bfa6e8e893570e5005f7219292bf7d90c153066cdfc", "impliedFormat": 1}, {"version": "1ea59d0d71022de8ea1c98a3f88d452ad5701c7f85e74ddaa0b3b9a34ed0e81c", "impliedFormat": 1}, {"version": "cd66a32437a555f7eb63490509a038d1122467f77fe7a114986186d156363215", "impliedFormat": 1}, {"version": "f53d243499acfacc46e882bbf0bf1ae93ecea350e6c22066a062520b94055e47", "impliedFormat": 1}, {"version": "65522e30a02d2720811b11b658c976bff99b553436d99bafd80944acba5b33b4", "impliedFormat": 1}, {"version": "76b3244ec0b2f5b09b4ebf0c7419260813820f128d2b592b07ea59622038e45c", "impliedFormat": 1}, {"version": "66eb7e876b49beff61e33f746f87b6e586382b49f3de21d54d41313aadb27ee6", "impliedFormat": 1}, {"version": "69e8dc4b276b4d431f5517cd6507f209669691c9fb2f97933e7dbd5619fd07b7", "impliedFormat": 1}, {"version": "361a647c06cec2e7437fa5d7cdf07a0dcce3247d93fbf3b6de1dc75139ff5700", "impliedFormat": 1}, {"version": "fe5726291be816d0c89213057cd0c411bb9e39e315ed7e1987adc873f0e26856", "impliedFormat": 1}, {"version": "1b76990de23762eb038e8d80b3f9c810974a7ed2335caa97262c5b752760f11a", "impliedFormat": 1}, {"version": "5e050e05fe99cd06f2d4ad70e73aa4a72961d0df99525e9cad4a78fa588f387b", "impliedFormat": 1}, {"version": "4ff327e8b16da9d54347b548f85675e35a1dc1076f2c22b2858e276771010dd2", "impliedFormat": 1}, {"version": "f767787945b5c51c0c488f50b3b3aeb2804dfd2ddafcb61125d8d8857c339f5a", "impliedFormat": 1}, {"version": "14ab21a9aeff5710d1d1262459a6d49fb42bed835aa0f4cfc36b75aa36faddcd", "impliedFormat": 1}, {"version": "ba3c4682491b477c63716864a035b2cfdd727e64ec3a61f2ca0c9af3c0116cfd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b222d32836d745e1e021bb10f6a0f4a562dd42206203060a8539a6b9f16523f0", "impliedFormat": 1}, {"version": "86e355fcc013f3caf1ce7d67b45cc7df1cc570532ae77d7aa8e701d3248e88f7", "impliedFormat": 1}, {"version": "db4af36f01c880562e5b3072a339be19314bd5007ae636055bc36c3c7ee90e72", "impliedFormat": 1}, {"version": "ff3c0d38c5bf2363e18db4cc001fbeddf6273196f1efbb15cd70ee15fa59adaf", "signature": "3061277a92c0a2f4075ed8c713608f30ff29ccaf2e403b51b21ddc2ae657e29f"}, {"version": "10d36638b13a95e102a1c4af2560e368158ef3c52c298263c2e3b5a41ea41cff", "signature": "435a1e418e8338be3f39614b96b81a9aa2700bc8c27bc6b98f064ff9ce17c363"}, {"version": "07fcc9be98e12bd2f0f71a501a9bfbe2e53d38c50e8a5e84223fdd05bd8749c5", "impliedFormat": 99}, {"version": "b887a4575db46263f82d7bde681bdc14526e4a2618a1172fef4206c467752d8f", "impliedFormat": 99}, {"version": "4775a0a636da470557a859dc6132b73d04c3ebdbac9dac2bb035b0df7382e188", "impliedFormat": 1}, {"version": "869f4ca0e1da09e6d2ce88d1788045302d3ea04793d2c7219fdf40cd935d1880", "impliedFormat": 1}, {"version": "d5eb5865d4cbaa9985cc3cfb920b230cdcf3363f1e70903a08dc4baab80b0ce1", "impliedFormat": 1}, {"version": "51ebca098538b252953b1ef83c165f25b52271bfb6049cd09d197dddd4cd43c5", "impliedFormat": 1}, {"version": "60f8e787e54fb61e64c2a8d9a72f6bfec36144cd5dbfcc4700c432408c860811", "signature": "6f3da80bcf7851c42a05d8c2b8979472127d0cb217e4e3723702bb52b5474877", "affectsGlobalScope": true}, {"version": "8266376d8b83d80ac0027a11718a1f67842731b0f3a903a9bee24ca457f1e2ab", "signature": "d342feb458148488a7c4153ff08153cbb7f678282b6432742b1f5546bd04d184"}, {"version": "e040e94bb326c7b84cd94d6bae2f226eb2aea9ad980f1a72ad9dc1b3f2cc95c3", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "b4769b39d3b78a0f924303f94848930de2505abb839d7512131b41fc72c625bf", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "ef05a990cec3e858871833827b63a95ea072aab97a9edf2395dbf0c248ec3d16", "signature": "139bb9b3da37dadce341c7fff751d8ae02142a1b8ce16af8bbb13c151e5eb654"}, {"version": "cff399d99c68e4fafdd5835d443a980622267a39ac6f3f59b9e3d60d60c4f133", "impliedFormat": 99}, {"version": "6ada175c0c585e89569e8feb8ff6fc9fc443d7f9ca6340b456e0f94cbef559bf", "impliedFormat": 99}, {"version": "e56e4d95fad615c97eb0ae39c329a4cda9c0af178273a9173676cc9b14b58520", "impliedFormat": 99}, {"version": "73e8dfd5e7d2abc18bdb5c5873e64dbdd1082408dd1921cad6ff7130d8339334", "impliedFormat": 99}, {"version": "fc820b2f0c21501f51f79b58a21d3fa7ae5659fc1812784dbfbb72af147659ee", "impliedFormat": 99}, {"version": "4f041ef66167b5f9c73101e5fd8468774b09429932067926f9b2960cc3e4f99d", "impliedFormat": 99}, {"version": "31501b8fc4279e78f6a05ca35e365e73c0b0c57d06dbe8faecb10c7254ce7714", "impliedFormat": 99}, {"version": "7bc76e7d4bbe3764abaf054aed3a622c5cdbac694e474050d71ce9d4ab93ea4b", "impliedFormat": 99}, {"version": "ff4e9db3eb1e95d7ba4b5765e4dc7f512b90fb3b588adfd5ca9b0d9d7a56a1ae", "impliedFormat": 99}, {"version": "f205fd03cd15ea054f7006b7ef8378ef29c315149da0726f4928d291e7dce7b9", "impliedFormat": 99}, {"version": "d683908557d53abeb1b94747e764b3bd6b6226273514b96a942340e9ce4b7be7", "impliedFormat": 99}, {"version": "7c6d5704e2f236fddaf8dbe9131d998a4f5132609ef795b78c3b63f46317f88a", "impliedFormat": 99}, {"version": "d05bd4d28c12545827349b0ac3a79c50658d68147dad38d13e97e22353544496", "impliedFormat": 99}, {"version": "b6436d90a5487d9b3c3916b939f68e43f7eaca4b0bb305d897d5124180a122b9", "impliedFormat": 99}, {"version": "04ace6bedd6f59c30ea6df1f0f8d432c728c8bc5c5fd0c5c1c80242d3ab51977", "impliedFormat": 99}, {"version": "57a8a7772769c35ba7b4b1ba125f0812deec5c7102a0d04d9e15b1d22880c9e8", "impliedFormat": 99}, {"version": "badcc9d59770b91987e962f8e3ddfa1e06671b0e4c5e2738bbd002255cad3f38", "impliedFormat": 99}, {"version": "48cbb04a6d405b5c82038c5f037c85005016dcf2250d78f963697e0bee05afc0", "signature": "c4344b457fefca6e6161152155da85ac51847e0a15573d96a659b26eb627e4ed"}, {"version": "33fba7d1470b5cf3bf3f212e11d0c2cdd8d40962935f208d23c6625949df950e", "signature": "0c6af90b83136f527174a3d579280a6db0bd80e0333d4a0c68049a3bc9fb0745"}, {"version": "593654eebe902db28ca173f021f74ea9f77e8b344aebb0a80fa4d10f29bb3a9d", "impliedFormat": 1}, {"version": "3727b9f1e2582a26c99d29ed1caf6d72cf19d178c39316b8446c086c45debcff", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4a27fda4280a6283cdbeb87a6e6f759d648d2595299dab3ef8b60d7521acc1b5", "signature": "ce488622793573d253e0ba00570b8abf66b288f107de6e0e0d1f2c53cd2ebb4e"}, {"version": "7608637b996714a35b25a808fbe1b518ee5e13400e382b7c6c7cec6569f41918", "signature": "ae5648c5a5e4bbefeda943267adfc34dc56e1c150c52d396123e2e4fb279079f"}, {"version": "b3cdccfd8b9c0630d3ad6fa90dfb8fdac948472cff6632c5cde32ac7a95d649e", "signature": "dd40cd20a2d9d0425ee216860fb17b0fa57326d8ed36930481cbef1bbdc2d19a"}, {"version": "89ff0628a34c2573be677dff9153d709ec7d24b2b0aaeb82cf4468aeb34aba90", "signature": "8d390cb7b123d60aaa3caaeab30b31008b65ebcba9bb4597103239402510c2b7"}, {"version": "7bcaa1a3f7cdb302fccd66f3cdf765fdff83be2005e793a57cbfa34aeccadad6", "signature": "1d77fd9f7056cc586b2a1e42f9edae4865ccde7a611fbfd0e44859317be1920f"}, {"version": "0e53700ab7ad6ddd0ddc559f191ed4307bf1836a4687bbd6233a0f16055329c2", "signature": "fee59a3b73bb54f17e7fef371369e21a48488d789acf062c1f49a2ba9e54131f"}, {"version": "d779b7c4cd02dbcac5870203256eb236c05970499e8dac97cd4d652593ffea29", "signature": "d676a2dc6e49da417149b49d20cefc05a99a8f31acea5f7dd2b41fb0bf97ef9a"}, {"version": "07c520e94f3b6d79d9ba321b40a0c0aaf7cef74da75395d549528a8dec870349", "signature": "6f36b48f011ad51c93acbff20ba3c062dc1acc2357f988a74a9562661223ca04"}, {"version": "652bcda5a01db5c37db52064bfe2e68254e4f9c477cae13c3ed54e91b8a4e9de", "signature": "06d1c04ced18a3cc1ae8c08af275e63344c6f8146509f00d3c24fcf0f2d78e29"}, {"version": "97243d1fa909cf8153c851754d5f70c56463a23fa41e3fc452717afb4ede44fe", "signature": "ec28880f6c335b29616ffafd28a696aa77a5606f83009767b53a2bffebadea71"}, {"version": "8fd35ea7ee1f3457a26ba1f7c178a7481d28bfd1dabc5cf67508e9424f45c355", "signature": "343fcb04498742a652e66a5a3b3001cd9eda549dee4b9227d289b45065381ac1"}, {"version": "6182926ae37da1eea9ec73616fc34ed3809cf6de78c267c48a21679f7aff3d75", "signature": "33c07476ce441e3fb1174355ee6c3fc79b43507165b61ecabff298bb35bee241"}, {"version": "24624ac20a2d1d42400875792f494db40be2c6e22d6d7c7cde723b47bcbe2e5a", "signature": "ac959a0f86d812c385666b5a0931d54533a93de2baba2b2b8edca96329179404"}, {"version": "bb21e70a5bc4d8a7e5d832ba3e96445f7eacdaf5bbf7f2d15462ab44bac7d49a", "signature": "c713ee393a127b56c57c389643bbd538a6f2fe3446702d7f7a9ddfcd4610e226"}, {"version": "bc939a0949344e18f2ecaacc3202fd1724c03b029d23ac9674b89f8e1d7a1dc2", "signature": "87a392bd83e06c30cc92cd989720de810af0eb3a3a1b37d324d0588c6cf1e8f9"}, {"version": "b63e705f0cea4b6104025c59d3350b41b3f4ec08d2b3834c21feb9814373e7f3", "signature": "589bc1912e1f60ef2a2e71d8fb3e89b49fcdb0729bd564a4a493e35e25add852"}, {"version": "856301421096347cafb2578d19ec89f9d1fe957d005474f234a25a96821bd5a4", "signature": "1d283f614172334ecce9ca44daa865e4b0e735366fa842f7dee112e1224b3bda"}, {"version": "371d6fb4796a51b2caa58f8bbd79336d9a424b6198cbbdc9478815825f3d6038", "signature": "a6e16be1fcd2653116cb37080439eeae8ab5627b0cc1cc0fe8e7d8555d2050cd"}, {"version": "4824e00c8e8581b2205977e32c7b362681e66cbd8c683aac07e6d743d833afe8", "signature": "d4c8386628ba20960fc60f2169f730453eb748bdc54afaf517b493123afafc32"}, {"version": "0d330fccb8e16b513db5f7f88e0f5736d7a0c858d644b27cda257788144f2c00", "signature": "10cbd769e958217d5d462414f223d37f90098a5b3d2d6592d8762074b1d38115"}, {"version": "32a3e1ffda9870baad7dc040ad868c9e1d9b03d0bc9797c2cabba1d16c9749c5", "signature": "c003c5779c6f1c153a9fb161f914a47acfecdfba043e321ecc2057c853a6e1e8"}, {"version": "5c5ffd8f2f617eaf78598b3c3f20e9567aa0d57158bb62237b138e398d92f6b9", "signature": "0d381e5cfbd8ac05d065da246e6825c1773e0d12472cfece15c7d558bb21b5c3"}, {"version": "6c94be3bd2ab80bb0c0aca68a65ca68721d9065481ecc5883e502c8a5d6951f6", "signature": "285fa3e7f633ca79b977421b00628600dfd3c513d72f93f75552262f027ee7b0"}, {"version": "6978a3a8e941c6496329ee3ab8805e070b85b8da97e37ad4fa64a2f0f4207253", "signature": "5e8aa95a99a336a1a80d10b896d1eb61e81ae9a1f5b0a10e6fa3cabd6e8dbcf9"}, {"version": "9ffa53c93a1123cc3746281a598b9d5708baaf4c86ff412b59cf8786e97d0984", "signature": "6c03b5723588ae5ac1a81fc5106d696f9b8ddd34a63f59445e378d43112b1313"}, {"version": "caff16300e455f82bc0b367e6fcd9a13642ce3059778dc174b64903193698095", "signature": "e07487b212e417ba897f0e2b3ec4b6ce2f9ba68820ecd6d43b54adcab0582926"}, {"version": "b1535397a73ca6046ca08957788a4c9a745730c7b2b887e9b9bc784214f3abac", "impliedFormat": 1}, {"version": "1dab12d45a7ab2b167b489150cc7d10043d97eadc4255bfee8d9e07697073c61", "impliedFormat": 1}, {"version": "611c4448eee5289fb486356d96a8049ce8e10e58885608b1d218ab6000c489b3", "impliedFormat": 1}, {"version": "5de017dece7444a2041f5f729fe5035c3e8a94065910fbd235949a25c0c5b035", "impliedFormat": 1}, {"version": "d47961927fe421b16a444286485165f10f18c2ef7b2b32a599c6f22106cd223b", "impliedFormat": 1}, {"version": "341672ca9475e1625c105a6a99f46e8b4f14dff977e53a828deef7b5e932638f", "impliedFormat": 1}, {"version": "d3b5d359e0523d0b9f85016266c9a50ce9cda399aeac1b9eeecb63ba577e4d27", "impliedFormat": 1}, {"version": "5b9f65234e953177fcc9088e69d363706ccd0696a15d254ac5787b28bdfb7cb0", "impliedFormat": 1}, {"version": "510a5373df4110d355b3fb5c72dfd3906782aeacbb44de71ceee0f0dece36352", "impliedFormat": 1}, {"version": "eb76f85d8a8893360da026a53b39152237aaa7f033a267009b8e590139afd7de", "impliedFormat": 1}, {"version": "1c19f268e0f1ed1a6485ca80e0cfd4e21bdc71cb974e2ac7b04b5fce0a91482b", "impliedFormat": 1}, {"version": "84a28d684e49bae482c89c996e8aeaabf44c0355237a3a1303749da2161a90c1", "impliedFormat": 1}, {"version": "89c36d61bae1591a26b3c08db2af6fdd43ffaab0f96646dead5af39ff0cf44d3", "impliedFormat": 1}, {"version": "fcd615891bdf6421c708b42a6006ed8b0cf50ca0ac2b37d66a5777d8222893ce", "impliedFormat": 1}, {"version": "1c87dfe5efcac5c2cd5fc454fe5df66116d7dc284b6e7b70bd30c07375176b36", "impliedFormat": 1}, {"version": "6362fcd24c5b52eb88e9cf33876abd9b066d520fc9d4c24173e58dcddcfe12d5", "impliedFormat": 1}, {"version": "aa064f60b7e64c04a759f5806a0d82a954452300ee27566232b0cf5dad5b6ba6", "impliedFormat": 1}, {"version": "7ffb4e58ca1b9ed5f26bed3dc0287c4abd7a2ba301ca55e2546d01a7f7f73de7", "impliedFormat": 1}, {"version": "65a6307cc74644b8813e553b468ea7cc7a1e5c4b241db255098b35f308bfc4b5", "impliedFormat": 1}, {"version": "bd8e8f02d1b0ebfa518f7d8b5f0db06ae260c192e211a1ef86397f4b49ee198f", "impliedFormat": 1}, {"version": "71b32ccf8c508c2f7445b1b2c144dd7eef9434f7bfa6a92a9ebd0253a75cb54a", "impliedFormat": 1}, {"version": "4fd8e7e446c8379cfb1f165961b1d2f984b40d73f5ad343d93e33962292ec2e0", "impliedFormat": 1}, {"version": "45079ac211d6cfda93dd7d0e7fc1cf2e510dad5610048ef71e47328b765515be", "impliedFormat": 1}, {"version": "6bc752f2f567aa7b0b210f754d738fac8877d0e6ff3b7ce00304544d76b7a8d1", "impliedFormat": 1}, {"version": "53b4d1135cab5b70f525a2cab49cacff16188040be1ec0216c30a670e438e615", "impliedFormat": 1}, {"version": "c89bf7fffc6247febd5419e0f3cfd56e54f14c292a393a4207e794d1a1fe3f70", "impliedFormat": 1}, {"version": "ba64b14db9d08613474dc7c06d8ffbcb22a00a4f9d2641b2dcf97bc91da14275", "impliedFormat": 1}, {"version": "530197974beb0a02c5a9eb7223f03e27651422345c8c35e1a13ddc67e6365af5", "impliedFormat": 1}, {"version": "3ab1d491b33b2bfc1bfcba4f1d75cdb2949fa4b6cda969801addf2108158995f", "impliedFormat": 1}, {"version": "0bfacd36c923f059779049c6c74c00823c56386397a541fefc8d8672d26e0c42", "impliedFormat": 1}, {"version": "19d04b82ed0dc5ba742521b6da97f22362fe40d6efa5ca5650f08381e5c939b2", "impliedFormat": 1}, {"version": "f02ac71075b54b5c0a384dddbd773c9852dba14b4bf61ca9f1c8ba6b09101d3e", "impliedFormat": 1}, {"version": "bbf0ae18efd0b886897a23141532d9695435c279921c24bcb86090f2466d0727", "impliedFormat": 1}, {"version": "067670de65606b4aa07964b0269b788a7fe48026864326cd3ab5db9fc5e93120", "impliedFormat": 1}, {"version": "7a094146e95764e687120cdb840d7e92fe9960c2168d697639ad51af7230ef5e", "impliedFormat": 1}, {"version": "21290aaea56895f836a0f1da5e1ef89285f8c0e85dc85fd59e2b887255484a6f", "impliedFormat": 1}, {"version": "a07254fded28555a750750f3016aa44ec8b41fbf3664b380829ed8948124bafe", "impliedFormat": 1}, {"version": "6af3ddf418784ecd9132f1eb4f684858b50ed7f98d71fdf8931ca1c9174b75f7", "impliedFormat": 1}, {"version": "46f640a5efe8e5d464ced887797e7855c60581c27575971493998f253931b9a3", "impliedFormat": 1}, {"version": "cdf62cebf884c6fde74f733d7993b7e255e513d6bc1d0e76c5c745ac8df98453", "impliedFormat": 1}, {"version": "e6dd8526d318cce4cb3e83bef3cb4bf3aa08186ddc984c4663cf7dee221d430e", "impliedFormat": 1}, {"version": "bc79e5e54981d32d02e32014b0279f1577055b2ebee12f4d2dc6451efd823a19", "impliedFormat": 1}, {"version": "ce9f76eceb4f35c5ecd9bf7a1a22774c8b4962c2c52e5d56a8d3581a07b392f9", "impliedFormat": 1}, {"version": "7d390f34038ca66aef27575cffb5a25a1034df470a8f7789a9079397a359bf8b", "impliedFormat": 1}, {"version": "18084f07f6e85e59ce11b7118163dff2e452694fffb167d9973617699405fbd1", "impliedFormat": 1}, {"version": "aee429e8f7b951429bc6c4d6f32d29538a0ec23da3629ac22cdb2bac53c749c6", "impliedFormat": 1}, {"version": "e7161539043ec90c90ffa52cbb586a15328e4ea0011b1d1b6cb2e4fbb822319a", "impliedFormat": 1}, {"version": "1a6ef8d98d08d4d697e37a66b79381e345acfbb8602de6e99724e1ce8b967fb3", "impliedFormat": 1}, {"version": "0d4ba4ad7632e46bab669c1261452a1b35b58c3b1f6a64fb456440488f9008cf", "impliedFormat": 1}, {"version": "4568e0cb188f1d5fadb8acc697598b59cfe230145c1e387b53fa1fde6cdf602a", "impliedFormat": 1}, {"version": "0a9ae72be840cc5be5b0af985997029c74e3f5bcd4237b0055096bb01241d723", "impliedFormat": 1}, {"version": "920004608418d82d0aad39134e275a427255aaf1dafe44dca10cc432ef5ca72a", "impliedFormat": 1}, {"version": "3ac2bd86af2bab352d126ccdde1381cd4db82e3d09a887391c5c1254790727a1", "impliedFormat": 1}, {"version": "2efc9ad74a84d3af0e00c12769a1032b2c349430d49aadebdf710f57857c9647", "impliedFormat": 1}, {"version": "f18cc4e4728203a0282b94fc542523dfd78967a8f160fabc920faa120688151f", "impliedFormat": 1}, {"version": "cc609a30a3dd07d6074290dadfb49b9f0f2c09d0ae7f2fa6b41e2dae2432417b", "impliedFormat": 1}, {"version": "c473f6bd005279b9f3a08c38986f1f0eaf1b0f9d094fec6bc66309e7504b6460", "impliedFormat": 1}, {"version": "0043ff78e9f07cbbbb934dd80d0f5fe190437715446ec9550d1f97b74ec951ac", "impliedFormat": 1}, {"version": "bdc013746db3189a2525e87e2da9a6681f78352ef25ae513aa5f9a75f541e0ae", "impliedFormat": 1}, {"version": "4f567b8360c2be77e609f98efc15de3ffcdbe2a806f34a3eba1ee607c04abab6", "impliedFormat": 1}, {"version": "615bf0ac5606a0e79312d70d4b978ac4a39b3add886b555b1b1a35472327034e", "impliedFormat": 1}, {"version": "818e96d8e24d98dfd8fd6d9d1bbabcac082bcf5fbbe64ca2a32d006209a8ee54", "impliedFormat": 1}, {"version": "18b0b9a38fe92aa95a40431676b2102139c5257e5635fe6a48b197e9dcb660f1", "impliedFormat": 1}, {"version": "86b382f98cb678ff23a74fe1d940cbbf67bcd3162259e8924590ecf8ee24701e", "impliedFormat": 1}, {"version": "aeea2c497f27ce34df29448cbe66adb0f07d3a5d210c24943d38b8026ffa6d3c", "impliedFormat": 1}, {"version": "0fbe1a754e3da007cc2726f61bc8f89b34b466fe205b20c1e316eb240bebe9e8", "impliedFormat": 1}, {"version": "aa2f3c289c7a3403633e411985025b79af473c0bf0fdd980b9712bd6a1705d59", "impliedFormat": 1}, {"version": "e140d9fa025dadc4b098c54278271a032d170d09f85f16f372e4879765277af8", "impliedFormat": 1}, {"version": "70d9e5189fd4dabc81b82cf7691d80e0abf55df5030cc7f12d57df62c72b5076", "impliedFormat": 1}, {"version": "a96be3ed573c2a6d4c7d4e7540f1738a6e90c92f05f684f5ee2533929dd8c6b2", "impliedFormat": 1}, {"version": "2a545aa0bc738bd0080a931ccf8d1d9486c75cbc93e154597d93f46d2f3be3b4", "impliedFormat": 1}, {"version": "137272a656222e83280287c3b6b6d949d38e6c125b48aff9e987cf584ff8eb42", "impliedFormat": 1}, {"version": "5277b2beeb856b348af1c23ffdaccde1ec447abede6f017a0ab0362613309587", "impliedFormat": 1}, {"version": "d4b6804b4c4cb3d65efd5dc8a672825cea7b39db98363d2d9c2608078adce5f8", "impliedFormat": 1}, {"version": "929f67e0e7f3b3a3bcd4e17074e2e60c94b1e27a8135472a7d002a36cd640629", "impliedFormat": 1}, {"version": "0c73536b65135298d43d1ef51dd81a6eba3b69ef0ce005db3de11365fda30a55", "impliedFormat": 1}, {"version": "2a545aa0bc738bd0080a931ccf8d1d9486c75cbc93e154597d93f46d2f3be3b4", "impliedFormat": 99}, {"version": "1bb429b6d30739a04f6fccfdd1efa926dc7f817e69a6383cdc43652d3d8fe9b5", "impliedFormat": 1}, {"version": "13d37e5a02b5645be4525b59f22f90088c0076059924b78e9b8bbe58a21a062b", "impliedFormat": 1}, {"version": "82637a41ed0a2c71072fa46f0032e6910014dbe1c312ca482daeb97d67ef498b", "impliedFormat": 1}, {"version": "e69728e4409029096a84c7fe8cb7bb19197c75cb6e381cf118dd40e8606e92e3", "impliedFormat": 1}, {"version": "c08444f90bd4511319184f08634b7db50cb3766ac8f7e4c18afb7c7901c8055e", "impliedFormat": 1}, {"version": "50bd18ab49fece19f9d86f6b70500af21ea87df9ff094b4b412b34bc9ed2ebb5", "impliedFormat": 1}, {"version": "f7f3423a747e06b02e2bcc375f2ba52a601129bb56d56e63a5d5f29a4a6bdd19", "impliedFormat": 1}, {"version": "3eccf1c5cfcf05eb5a1e507213fcb021a4dc68142749bc26960fbc44463ab1e2", "impliedFormat": 1}, {"version": "d8ba84f7ef6e8a81e7f39bac649261668c6bf73cfc2e748e2c4ca4549f61e2d4", "impliedFormat": 99}, {"version": "dbbc31d2e9e506d14178cdbb550a6869115fff2af40c2d50115947cd8f7cb225", "impliedFormat": 99}, {"version": "49e23b58e6373f1dd7156a1a36c601660c5a30e4ae0c4914f87d5f452c6270e2", "impliedFormat": 99}, {"version": "44002b38aff4208de147ff4579ebb833fb234bcf444a2bafead727c17e0acf45", "impliedFormat": 99}, {"version": "6028c13028266847d3f97dcbd9f84b044cc069cdb742ec86c0279d525c83865f", "impliedFormat": 1}, {"version": "c417faf00b31135e4ad954fcc7c9479870df71799570f7ef820e5928446a2755", "impliedFormat": 99}, {"version": "4beacc4d32af2b42500dad1480ec3c0ca536ec5f13fe7873cfdef32a1b3b34f3", "impliedFormat": 99}, {"version": "0a81da933008a136bae114f77dc1e28968f0256db072cb6861e0074fa644f735", "impliedFormat": 99}, {"version": "085021f1df48870da68d2943ad736069594dc08a72877fcbcfa214b2ec74d7fe", "impliedFormat": 99}, {"version": "fbe5dfe3bade6b5d07e20bf5da9cdf9210a046fa57f295a1de6c809cd1aaef8b", "impliedFormat": 99}, {"version": "8c48167566a34eaad79fc0fdbf8aedd24051583c36d16588ebc51705e0cec654", "impliedFormat": 99}, {"version": "7c100c9b9fd7b2dcfcf4f03e4e8803f9c45a64880479f02f8b97dc5397ca6cb3", "impliedFormat": 99}, {"version": "14597b5326b2529bc69c7c220a5986c6cb12a69326c463ec94f0f3a419d7af2c", "impliedFormat": 99}, {"version": "d18039cded461ecdd58db63c2ee4dbb3e2479709aed7b33688cd39837a689eaf", "impliedFormat": 99}, {"version": "811dcd772225cc8b93817055f84c5344a208a539a43ff5611ecc4b897b69d1f2", "signature": "cd9a4014be8bb8b8af2f7439c8ae6c0125e2b6e67c40b7e3cf7fe2bc2ff019db"}, {"version": "4071baf64ec5fe09267d1f82fe8d4787024da6df200ea9af4c4c3a5647022ed5", "impliedFormat": 1}, {"version": "45a9826271221e7502e672589222f42d16a5b8e4be013019acd703bb9cd03f5c", "impliedFormat": 1}, {"version": "716922a8d29406d301d37c2bef6565bd17e3abc071a44ba5a0d52397fe6ef4bd", "impliedFormat": 1}, {"version": "63e6331c56b53e5b77ff5fe5d869773c1213da583cde18b6b0c8051271331b77", "impliedFormat": 1}, {"version": "a1e9a6a7db7bc4e804080976fab5fbbc4cbfa2748a166f0af9f5f4d3818a2b83", "impliedFormat": 1}, {"version": "ba2e93d22cfdc1569b7953d9d467151e2d2eccb490bee74317f1f4f2333b13c7", "impliedFormat": 1}, {"version": "44d63aa3fef04a23e467d73eb8c1e4974e9027a56a67581e4e15b4024af0667d", "impliedFormat": 1}, {"version": "32b789a883704fb6be047f8a8f600685933eeda17f1ca9898ec42af0951bc5b1", "impliedFormat": 1}, {"version": "b0f25d7f2e82edf76b2c8a4423defa26747483d6e1e772ac8476d5b78f69fe4d", "impliedFormat": 1}, {"version": "51e4a1da037eac1dbb909891d7db4860ba80866159dc6813f737fe1eb33ca9bf", "impliedFormat": 1}, {"version": "4c39806d4d0cf15b0fe29a74d0c3645508ccd51bc4a33fee7782a843f3f0e4b3", "impliedFormat": 1}, {"version": "d82c4d30bf153c88224df1ee76b595ba52b0dd6bc3f09c8d31a52ef751ef0052", "impliedFormat": 1}, {"version": "355dcb43db7ed2eb373714116a33974f8e5b5813b88056c01bcf1802a8b252eb", "impliedFormat": 1}, {"version": "1705b40e0698ec8a6e844ba21fecec6b2fa5ecd6d126dbf7bfa8bf69e77b741c", "impliedFormat": 1}, {"version": "e82a51d5498c34f37bf96981223d5aa0b5051ced754d34b9747b1aa8a4f2174f", "impliedFormat": 1}, {"version": "7dca1745afe35b6aa588e0de49d9e1059fc2ecbc4279e8ae0f5b64d83c05251e", "impliedFormat": 1}, {"version": "83e01ec16b5a8424ba35047471fe772e4fdeba7dfa7884d9ced04dd84c01a26c", "impliedFormat": 1}, {"version": "ede088b09b00760b9ce0997bc1669e25bd155241f369faacf09f3d810dc0ede3", "impliedFormat": 1}, {"version": "410f5902503887a81b08fcf2d296f78b583bd378893383dcd984e5e4178f84ae", "impliedFormat": 1}, {"version": "b72123b1d2bc40c98869b15dda3af4afa153dfeec5c1984ec3fc37b19b2138bf", "impliedFormat": 1}, {"version": "42a1a75a5b70388825e244af4ab7c268f052a021310385fe191e8ee12ed6d696", "impliedFormat": 1}, {"version": "fe9706a96369dbcf8064a5a670e70112db672b17b5ac0e609a7379871db4b564", "impliedFormat": 1}, {"version": "09d9166970b3c0a143ce39bbab9260823baceb8a24743ed55c9414aa4367a141", "impliedFormat": 1}, {"version": "d2e7b9aa98824df7e48460262b2ba4152532f27ccef1313a62a00c25c49656e1", "impliedFormat": 1}, {"version": "120d183567e622fcaf1654355cf3ef6b2162e45b39389916e82bdd82444da76c", "impliedFormat": 1}, {"version": "22759dfadc7f47badde31230a0a34ba6810f88b29ff314fb5e0ffd5fe91ba379", "impliedFormat": 1}, {"version": "aa955cd4b93e17c382cf1efd03e8b4d7fe60b301b660f119c8934fd78627f45d", "impliedFormat": 1}, {"version": "97d48f5c823e3c84ae30f8de0525afe256b5af9cd2df5fde8a1d96f93b93776e", "impliedFormat": 1}, {"version": "a2d26be162532e61deb8a7449829a3db035880a84ea86abaceb60f8d0cc4bc51", "impliedFormat": 1}, {"version": "4764e86784bb88b8f3573279db3856c50a03e2b8bc184c06a40f897bff9d7e83", "impliedFormat": 1}, {"version": "027a4481143e6e6e28440b7f79e34ec1fd7bb0ddfced3d701694a1940c19672e", "impliedFormat": 1}, {"version": "b82da595c16f0115b344d6450870c052cfad0310e29d8c90b985a179c8164e97", "impliedFormat": 1}, {"version": "146095aafb9fa0041d14d7f2a2575e8909a158ae6069caa20b44b49043519ae1", "signature": "49fd87055d96a9b59fef4eb35310ccb572ab7315ddabce56b87b408b440799ce"}, {"version": "70030c0b7ef755c1560addc79180f47f8f3a3f32ef5a61c576b8073b65ef6c6e", "signature": "e7f6ee20857fe656fde19bdf896896dcf4bdf329a13969b84914d996208d3fa6"}, {"version": "bb3bc5dd64308422835484a62043048f1c5c1d8e5d09e279735ec765cd48cc52", "signature": "03b60e83fb8efca524d46a2417e2b90f8ee3e9863c9e59ae8936ede15dcce68a"}, {"version": "51a6ba67e716c98560be42adf1aadb7b81a395cc7132b16896749d0addab901f", "signature": "9fefec2ace2472d6acdad6b0ed878156f292dd69ce0235139a0f7f658b6cdd70"}, {"version": "91d25f6eab1fccc9f08864b38580dcd4c7618e04934206ec9c4bf1129f2a47d2", "signature": "b497bbe4983533bec0c2f56ad419ae56d2671eeaa23526076a96f20f192aec0b"}, {"version": "4480c46d495e1344bb13e82b2cd43066b9efa95a40e52a713336502c124951bf", "signature": "9b9f5bae83014f0446a251033be5a922542c755dd99997b6fb865f0db0091127"}, {"version": "dd2331ae4a69e8ec3c8a605b0ce7e90ce086841973f431556f8e52c384c5e661", "signature": "f1b411b73f69f0bf868146e29202c86f208f96581577b4b3773b227c623ca49f"}, {"version": "adc597ef975e10369fd16b18b3fdb126e910356aed150bc56c69756763df945a", "signature": "b99a4a0cdd26ef8f2892dbe8a931f57ae86d8e4788b031d8d100cffbb5bccbed"}, {"version": "3bbba827fab08d46f0111206530813a1f1f8bdbfd275bfcc2311515829f71740", "signature": "c6f13d29134997091c1f8f630daf530db2a2414f42c4de724e7c83c128dc4fe3"}, {"version": "c37233fc34cf16c63712df2a22489961651fbf85c8ff75e25825bce84eaa41db", "signature": "4cbe26633ece7f4f24db8d3e5688d87908ace3bbc8d590fc93536119b999758b"}, {"version": "e6c30f93be8c8f9602dbfbf9a16b25d9a617ae90f106ad7e2f82a00d13807fc3", "signature": "19b716dba53918a11954c36297241a12f3270b0fcd3a20cfa030651248e45d67"}, {"version": "197095ecef377f0bde7bf701f8e01194fd30798d6806d5a7ad94ff872869b5b4", "signature": "1f5748fb3a2f58c71cd4a85a8e9e615c1004024649c7270bbf59c7f86aeb0c5e"}, {"version": "fa56114b245c949b23579a5102bc4c27ca8cac12790b60e0b0313381123ebd02", "signature": "f821d918a771343e8d3c07bddbef0f742c5cb440b79fae93fdb42b5445912789"}, {"version": "4f28d2a4219c03b35718f3a3bd4eb1bda596c50d4851b3f33e1740fec01cec4d", "signature": "36d4f16a7c34d5d6936e00437d2d828919afe3c09e78de8205305ff47b7a55d9"}, {"version": "6ddc665cbb17e056f376faf51aef0fb8154505194f5ffbf79ef4e7a793ad698a", "signature": "91a86a39e17c7abab858ee98c05f056d0d6c24e15e2663abda773648e64ed001"}, {"version": "3b8db555d580344e53693d140c99e320a089ba3bf03d5055b25d5e4c8e749569", "signature": "f541aa3adf1ff8f0f29edc267ceb3405afc12be5347c710af8a0b0f0d4f7447f"}, {"version": "bf42cb836607a59341ce25f2f9c56ca1b2c514d67ab5eadd0b013c43f0cf0b1e", "signature": "b5e27943e40072b3326532ae694e4d30787436b74399d8ef80fda735fddb4139"}, {"version": "0b5a54ec27f22945269bfaa32d786a1c1f44fc6e7072bd87693ae512c06ec407", "signature": "529ae892690649c419bd57a409b5914f4476274bba03ca1933deb0e7b4924120"}, {"version": "72214593953b89798598d3bb7939f10279834b13ddf03df5e0221906c8282ae7", "signature": "cad0625999b9e433ffe376dbd6f19efa5ab3ca8a7a3499dce765edc6225316a8"}, {"version": "7fc72bbe40f108a49524103be30b73d6600acdc9bb98f2ce54d8a7b9dd9eeaa2", "signature": "ed6e73da2e7e071604d49b8fecfc387f45aed0d8d238369f1550b34894e8ba62"}, {"version": "6e486ffe9c1c3188d2f1449ddb9acd8387e14b5300b4537dc43b5a34de4ef5ba", "signature": "ffc760dfe912315e85aa7a770eb5d8398257bb44ca63c8734d23ceac0c40bd3a"}, {"version": "fa3499e1636e12f872eff4f7e7c1e4d5beaefeaf54b7740bef7d8b71c63f9269", "signature": "5d34b068b467710737fb2136b472cbade4079b6753668968951f393e28c1955f"}, {"version": "f6023984fb75a02e50bebe7bd0339ba8539bcc2083ba194f76b0d1392d76a8ce", "signature": "ce488622793573d253e0ba00570b8abf66b288f107de6e0e0d1f2c53cd2ebb4e"}, {"version": "35f7575ef25b5a399318de20bc2d2a8fb97edbdddd3e27bfa5f068cd0f8bd2e3", "signature": "f9e41ca9875717bd8e24a12d7bb131c58812f504669bcec6354bbe1d8c8d4dcd"}, {"version": "8bff2a60061c92bdc076a015fdcc77d350a331cf560a4d3037b3cb9523150c02", "signature": "0c22fb672b14d96700408ee820810d7f7aa434bd3ccf1e1bb8a9831808c71f5a"}, {"version": "22c41a611fb6cf975385e5829ee57e98cdfc93438858ef99d5a55dbb79d909d0", "signature": "4f55e9264d18c2c40520632f776b8a74b3c67e9c44b3d4a08dfa676ac81321df"}, {"version": "a6609a529817dccfdb7aad690f488a9f5cca9f03f96d8d78f6d96efa7054411c", "signature": "3284e1e8bd9a40e1a5e2bb1161de2beae49c0816518afba0d2cf94afc888a61b"}, {"version": "301ba6258b871e93c9e1b5abc774d8c03d4f17676e960d1e058691e85e115dfa", "signature": "b53069dbd4e31dba747fd1d9ea2214ac806f948f19b997bb2a087ed7f1c418c0"}, {"version": "caef5b191982cd88619282b10e1c52c3cde8c81d4eaf4650b4e62d73f77483d4", "impliedFormat": 1}, {"version": "c652e3653150b8ee84ffc9034860d9183e6b4c34be28e3ba41b34b1417941982", "impliedFormat": 99}, {"version": "eee0f5492d6584224fb6465b635e6fcde743565c130aba0d830dfd447d5e42b8", "impliedFormat": 1}, {"version": "26860a73dac31f27357a67dede56496acb6e44d1b28e9df31a1f916e2e65e4ca", "signature": "b4725fdf6e585a0d4edfb145345ef70867f005d1e5b8141d092d691105210306"}, {"version": "4cea8e8e2affee1bdda99cecfe614610fc71ab8b10b23dfb1838c9f1a2a7d72c", "signature": "fd859dd047c512d4d04cf9dfffb696ffeb9fe61de9ddfcaf55c3c858ec13fb46"}, {"version": "e703155b4f62d45bbb2304d3792cc84e1a8f6431aa6b0de45738acccdd816663", "signature": "2f17cdfbf48177818bd5ac73bf3967e5066e6129521e245d3c040fb1c5aa2b2a"}, {"version": "4289d15172fcaf2a2f1c9c79a56fe33de06dd16a88e341201110417bf8c024cc", "signature": "385e658419d47bd0e179dbb64023390a60c606d1fe00966ceda0c5c019b24abf"}, {"version": "c55636603b96323a750f28a512958c529eb8577c48c1a7e99905161b67f4b28c", "signature": "acc978e5d3b60276d974222e64cafcda58717aa006ae464d1c0609b545b32671"}, {"version": "520330ae5dce10b1e37d68819e82a8b8070d30a9a0544ff42b688b21ccd186fd", "signature": "4590db772edd299c4b5a6ebdf0aec8360bc671d6e108c23724b52ce834e54274"}, {"version": "c3d577953f04c0188d8b9c63b2748b814efda6440336fa49557f0079f5cf748a", "impliedFormat": 1}, {"version": "787fe950e18951b7970ec98cb05b3d0b11fcdfeb2091a7ea481ac9e52bf6c086", "impliedFormat": 1}, {"version": "13ceda04874f09091da1994ba5f58bf1e9439af93336616257691863560b3f13", "impliedFormat": 1}, {"version": "df7c4200a1528880e182933605a05ed8f93364cec323ae950ed77c6f91795677", "signature": "ffd057094f9d615b29e217ce26c55b5ffd0a4af9d5cb94167ad374fd950e2039"}, {"version": "cdf90f325296ee53df805b7db948253d2dd1e5451d4a35f38936f6692924183d", "signature": "405ffb8d08d7f74ae4d883986e0ad9946a304bd8d1f8919a336572f67df657c0"}, {"version": "1639e53b848530eade38ac5b266842d0abb99ed0ab38f7c5f4da73bf666050fc", "signature": "fa94c82b3f10e2e7956af6fff9b7683a3c2a4216d334e849d2c8d21663ea4791"}, {"version": "ad1af62c97dc98ba1803373ddc5d9778863ad24ded7bcb3e72cf2ba1ba924a89", "signature": "e8cc25e5bec4d1ca21df310094e38b2094dc6f493541fe7de179ef209b5e8b44"}, {"version": "a1665a38db67b03da9d6575f7bda979b1d55afa8124099dd23cbcbf9ccd5ba55", "signature": "aa7e633c7b1e312e3b14a7f306445cf8e90c79d95f1627a4dd18a53f5669729e"}, {"version": "764797b9c76e511cb764ba12aa0b5f84c58479a49c09339b21e3255f949acd3d", "signature": "a6a3ae6ba7b1b92d692b2cde8a40ca2112025f76e684b2b793468d2cce5764bd"}, "3000cebeb324e2481569fa65b0638a564972d97c8cf8444e89a37772f489f3be", "2dac20b6567cc7bb042235b62d14e0ab5452f175884f3e084f00ee6ac3ca8f56", {"version": "37e0f04a685dec085f35c8c130bb10dd76b81f6215cf69ba7374851d51dabf24", "impliedFormat": 1}, {"version": "42243eda9a4a7f3892bfd317f3f46c05ffeab838e66b4aabf044ac135036c5b2", "signature": "3d2d0dd0f1641e58bf1b21f9cec11236e45c3f99ec59a500b4c4190bcf98d65d"}, {"version": "fe93c474ab38ac02e30e3af073412b4f92b740152cf3a751fdaee8cbea982341", "impliedFormat": 1}, {"version": "c60093e32612d44af7042c3eb457c616aec3deee748a5a1eb1a6188e3d837f5c", "impliedFormat": 1}, {"version": "1e00b8bf9e3766c958218cd6144ffe08418286f89ff44ba5a2cc830c03dd22c7", "impliedFormat": 1}, {"version": "ef2b45c6a221043ad5ffe747f82325d1783555c1416bea8f36d67e308a93ba75", "signature": "42059e18fbe39050156b3e7ad76524f8604666f9dfcc206e561638647f4136ca"}, {"version": "0b3afe9aacd941baa4f3bd0a2f0e4a04982232e706a7ef28c4d02909b35888ee", "signature": "52210388c5906ef4e282253e50ccc337278f2b3e15a1321a0044e2f32ba4ba1c"}, {"version": "89f39a0e50c5d0bfb6de1ccaefb0b93d5fe3b593b19d57af17be9cfd341513ba", "signature": "cd49ce7b95c469732321faa92669a51d000e36d2d87bc1f9b6082b4b72591f73"}, {"version": "d3e9a948de2156d050c83193a931f04a8b1c7a6e83815c0704514518037d7435", "signature": "2d0312b4a70af0e0161af91fcd0730b7aec506341814968afa2465453259691a"}, {"version": "3630bcf5d3fecdf27209549424a4459cb41a3f039651ba22b06005d03ad1de0e", "signature": "01a977ade994fe0de990222140f158a0dc3b03529994c449aa39333d0facac02"}, {"version": "2920053ac2e193a0a4384d5268540ffd54dd27769e51b68f80802ec5bba88561", "impliedFormat": 1}, {"version": "a45d8f5a37d98bb2bb1ae20e66b65528262c31d8a1d6e189aae80e5ee6e46560", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0eb3c64f822f6828da816709075180cca47f12a51694eec1072a9b17a46c8b07", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "70c1c729a2b1312a6641715c003358edf975d74974c3a95ac6c85c85fda3dda9", "impliedFormat": 1}, {"version": "d04f947114fa00a20ee3c3182bb2863c30869df93293cc673f200defadbd69d9", "impliedFormat": 1}, {"version": "4c629a21fb1b4f2428660f662d5fef6282e359d369f9e5ec5fd6ac197c1906ee", "impliedFormat": 1}, {"version": "785926dee839d0b3f5e479615d5653d77f6a9ef8aa4eea5bbdce2703c860b254", "impliedFormat": 1}, {"version": "66d5c68894bb2975727cd550b53cd6f9d99f7cb77cb0cbecdd4af1c9332b01dd", "impliedFormat": 1}, {"version": "6e2669a02572bf29c6f5cea36a411c406fff3688318aee48d18cc837f4a4f19c", "impliedFormat": 1}, {"version": "3833449d2046606bf5489e0827ee792ebdc654a4089b7ffb44be8c603e884212", "signature": "47bb1091ac4dee0d0321b0c4f41eef5fd0828a20fb064a7df6fa9c64c7fc8277"}, {"version": "2cef84bf00cbdb452fdc5d8ecfe7b8c0aa3fa788bdc4ad8961e2e636530dbb60", "impliedFormat": 99}, {"version": "24104650185414f379d5cc35c0e2c19f06684a73de5b472bae79e0d855771ecf", "impliedFormat": 99}, {"version": "799003c0ab928582fca04977f47b8d85b43a8de610f4eef0ad2d069fbb9f9399", "impliedFormat": 99}, {"version": "b13dd41c344a23e085f81b2f5cd96792e6b35ae814f32b25e39d9841844ad240", "impliedFormat": 99}, {"version": "17d8b4e6416e48b6e23b73d05fd2fde407e2af8fddbe9da2a98ede14949c3489", "impliedFormat": 99}, {"version": "6d17b2b41f874ab4369b8e04bdbe660163ea5c8239785c850f767370604959e3", "impliedFormat": 99}, {"version": "04b4c044c8fe6af77b6c196a16c41e0f7d76b285d036d79dcaa6d92e24b4982b", "impliedFormat": 99}, {"version": "30bdeead5293c1ddfaea4097d3e9dd5a6b0bc59a1e07ff4714ea1bbe7c5b2318", "impliedFormat": 99}, {"version": "e7df226dcc1b0ce76b32f160556f3d1550124c894aae2d5f73cefaaf28df7779", "impliedFormat": 99}, {"version": "f2b7eef5c46c61e6e72fba9afd7cc612a08c0c48ed44c3c5518559d8508146a2", "impliedFormat": 99}, {"version": "00f0ba57e829398d10168b7db1e16217f87933e61bd8612b53a894bd7d6371da", "impliedFormat": 99}, {"version": "126b20947d9fa74a88bb4e9281462bda05e529f90e22d08ee9f116a224291e84", "impliedFormat": 99}, {"version": "40d9e43acee39702745eb5c641993978ac40f227475eacc99a83ba893ad995db", "impliedFormat": 99}, {"version": "8a66b69b21c8de9cb88b4b6d12f655d5b7636e692a014c5aa1bd81745c8c51d5", "impliedFormat": 99}, {"version": "ebbb846bdd5a78fdacff59ae04cea7a097912aeb1a2b34f8d88f4ebb84643069", "impliedFormat": 99}, {"version": "7321adb29ffd637acb33ee67ea035f1a97d0aa0b14173291cc2fd58e93296e04", "impliedFormat": 99}, {"version": "320816f1a4211188f07a782bdb6c1a44555b3e716ce13018f528ad7387108d5f", "impliedFormat": 99}, {"version": "b2cc8a474b7657f4a03c67baf6bff75e26635fd4b5850675e8cad524a09ddd0c", "impliedFormat": 99}, {"version": "0d081e9dc251063cc69611041c17d25847e8bdbe18164baaa89b7f1f1633c0ab", "impliedFormat": 99}, {"version": "a64c25d8f4ec16339db49867ea2324e77060782993432a875d6e5e8608b0de1e", "impliedFormat": 99}, {"version": "0739310b6b777f3e2baaf908c0fbc622c71160e6310eb93e0d820d86a52e2e23", "impliedFormat": 99}, {"version": "37b32e4eadd8cd3c263e7ac1681c58b2ac54f3f77bb34c5e4326cc78516d55a9", "impliedFormat": 99}, {"version": "9b7a8974e028c4ed6f7f9abb969e3eb224c069fd7f226e26fcc3a5b0e2a1eba8", "impliedFormat": 99}, {"version": "e8100b569926a5592146ed68a0418109d625a045a94ed878a8c5152b1379237c", "impliedFormat": 99}, {"version": "594201c616c318b7f3149a912abd8d6bdf338d765b7bcbde86bca2e66b144606", "impliedFormat": 99}, {"version": "03e380975e047c5c6ded532cf8589e6cc85abb7be3629e1e4b0c9e703f2fd36f", "impliedFormat": 99}, {"version": "fae14b53b7f52a8eb3274c67c11f261a58530969885599efe3df0277b48909e1", "impliedFormat": 99}, {"version": "c41206757c428186f2e0d1fd373915c823504c249336bdc9a9c9bbdf9da95fef", "impliedFormat": 99}, {"version": "e961f853b7b0111c42b763a6aa46fc70d06a697db3d8ed69b38f7ba0ae42a62b", "impliedFormat": 99}, {"version": "3db90f79e36bcb60b3f8de1bc60321026800979c150e5615047d598c787a64b7", "impliedFormat": 99}, {"version": "639b6fb3afbb8f6067c1564af2bd284c3e883f0f1556d59bd5eb87cdbbdd8486", "impliedFormat": 99}, {"version": "49795f5478cb607fd5965aa337135a8e7fd1c58bc40c0b6db726adf186dd403f", "impliedFormat": 99}, {"version": "7d8890e6e2e4e215959e71d5b5bd49482cf7a23be68d48ea446601a4c99bd511", "impliedFormat": 99}, {"version": "d56f72c4bb518de5702b8b6ae3d3c3045c99e0fd48b3d3b54c653693a8378017", "impliedFormat": 99}, {"version": "4c9ac40163e4265b5750510d6d2933fb7b39023eed69f7b7c68b540ad960826e", "impliedFormat": 99}, {"version": "8dfab17cf48e7be6e023c438a9cdf6d15a9b4d2fa976c26e223ba40c53eb8da8", "impliedFormat": 99}, {"version": "38bdf7ccacfd8e418de3a7b1e3cecc29b5625f90abc2fa4ac7843a290f3bf555", "impliedFormat": 99}, {"version": "9819e46a914735211fbc04b8dc6ba65152c62e3a329ca0601a46ba6e05b2c897", "impliedFormat": 99}, {"version": "50f0dc9a42931fb5d65cdd64ba0f7b378aedd36e0cfca988aa4109aad5e714cb", "impliedFormat": 99}, {"version": "894f23066f9fafccc6e2dd006ed5bd85f3b913de90f17cf1fe15a2eb677fd603", "impliedFormat": 99}, {"version": "abdf39173867e6c2d6045f120a316de451bbb6351a6929546b8470ddf2e4b3b9", "impliedFormat": 99}, {"version": "aa2cb4053f948fbd606228195bbe44d78733861b6f7204558bbee603202ee440", "impliedFormat": 99}, {"version": "6911b41bfe9942ac59c2da1bbcbe5c3c1f4e510bf65cae89ed00f434cc588860", "impliedFormat": 99}, {"version": "7b81bc4d4e2c764e85d869a8dd9fe3652b34b45c065482ac94ffaacc642b2507", "impliedFormat": 99}, {"version": "895df4edb46ccdcbce2ec982f5eed292cf7ea3f7168f1efea738ee346feab273", "impliedFormat": 99}, {"version": "8692bb1a4799eda7b2e3288a6646519d4cebb9a0bddf800085fc1bd8076997a0", "impliedFormat": 99}, {"version": "239c9e98547fe99711b01a0293f8a1a776fc10330094aa261f3970aaba957c82", "impliedFormat": 99}, {"version": "34833ec50360a32efdc12780ae624e9a710dd1fd7013b58c540abf856b54285a", "impliedFormat": 99}, {"version": "647538e4007dcc351a8882067310a0835b5bb8559d1cfa5f378e929bceb2e64d", "impliedFormat": 99}, {"version": "992d6b1abcc9b6092e5a574d51d441238566b6461ade5de53cb9718e4f27da46", "impliedFormat": 99}, {"version": "938702305649bf1050bd79f3803cf5cc2904596fc1edd4e3b91033184eae5c54", "impliedFormat": 99}, {"version": "1e931d3c367d4b96fe043e792196d9c2cf74f672ff9c0b894be54e000280a79d", "impliedFormat": 99}, {"version": "05bec322ea9f6eb9efcd6458bb47087e55bd688afdd232b78379eb5d526816ed", "impliedFormat": 99}, {"version": "4c449a874c2d2e5e5bc508e6aa98f3140218e78c585597a21a508a647acd780a", "impliedFormat": 99}, {"version": "dae15e326140a633d7693e92b1af63274f7295ea94fb7c322d5cbe3f5e48be88", "impliedFormat": 99}, {"version": "c2b0a869713bca307e58d81d1d1f4b99ebfc7ec8b8f17e80dde40739aa8a2bc6", "impliedFormat": 99}, {"version": "6e4b4ff6c7c54fa9c6022e88f2f3e675eac3c6923143eb8b9139150f09074049", "impliedFormat": 99}, {"version": "69559172a9a97bbe34a32bff8c24ef1d8c8063feb5f16a6d3407833b7ee504cf", "impliedFormat": 99}, {"version": "86b94a2a3edcb78d9bfcdb3b382547d47cb017e71abe770c9ee8721e9c84857f", "impliedFormat": 99}, {"version": "e3fafafda82853c45c0afc075fea1eaf0df373a06daf6e6c7f382f9f61b2deb3", "impliedFormat": 99}, {"version": "a4ba4b31de9e9140bc49c0addddbfaf96b943a7956a46d45f894822e12bf5560", "impliedFormat": 99}, {"version": "d8a7926fc75f2ed887f17bae732ee31a4064b8a95a406c87e430c58578ee1f67", "impliedFormat": 99}, {"version": "9886ffbb134b0a0059fd82219eba2a75f8af341d98bc6331b6ef8a921e10ec68", "impliedFormat": 99}, {"version": "c2ead057b70d0ae7b87a771461a6222ebdb187ba6f300c974768b0ae5966d10e", "impliedFormat": 99}, {"version": "46687d985aed8485ab2c71085f82fafb11e69e82e8552cf5d3849c00e64a00a5", "impliedFormat": 99}, {"version": "999ca66d4b5e2790b656e0a7ce42267737577fc7a52b891e97644ec418eff7ec", "impliedFormat": 99}, {"version": "ec948ee7e92d0888f92d4a490fdd0afb27fbf6d7aabebe2347a3e8ac82c36db9", "impliedFormat": 99}, {"version": "03ef2386c683707ce741a1c30cb126e8c51a908aa0acc01c3471fafb9baaacd5", "impliedFormat": 99}, {"version": "66a372e03c41d2d5e920df5282dadcec2acae4c629cb51cab850825d2a144cea", "impliedFormat": 99}, {"version": "ddf9b157bd4c06c2e4646c9f034f36267a0fbd028bd4738214709de7ea7c548b", "impliedFormat": 99}, {"version": "3e795aac9be23d4ad9781c00b153e7603be580602e40e5228e2dafe8a8e3aba1", "impliedFormat": 99}, {"version": "98c461ec5953dfb1b5d5bca5fee0833c8a932383b9e651ca6548e55f1e2c71c3", "impliedFormat": 99}, {"version": "5c42107b46cb1d36b6f1dee268df125e930b81f9b47b5fa0b7a5f2a42d556c10", "impliedFormat": 99}, {"version": "7e32f1251d1e986e9dd98b6ff25f62c06445301b94aeebdf1f4296dbd2b8652f", "impliedFormat": 99}, {"version": "2f7e328dda700dcb2b72db0f58c652ae926913de27391bd11505fc5e9aae6c33", "impliedFormat": 99}, {"version": "3de7190e4d37da0c316db53a8a60096dbcd06d1a50677ccf11d182fa26882080", "impliedFormat": 99}, {"version": "a9d6f87e59b32b02c861aade3f4477d7277c30d43939462b93f48644fa548c58", "impliedFormat": 99}, {"version": "2bce8fd2d16a9432110bbe0ba1e663fd02f7d8b8968cd10178ea7bc306c4a5df", "impliedFormat": 99}, {"version": "798bedbf45a8f1e55594e6879cd46023e8767757ecce1d3feaa78d16ad728703", "impliedFormat": 99}, {"version": "62723d5ac66f7ed6885a3931dd5cfa017797e73000d590492988a944832e8bc2", "impliedFormat": 99}, {"version": "03db8e7df7514bf17fc729c87fff56ca99567b9aa50821f544587a666537c233", "impliedFormat": 99}, {"version": "9b1f311ba4409968b68bf20b5d892dbd3c5b1d65c673d5841c7dbde351bc0d0b", "impliedFormat": 99}, {"version": "2d1e8b5431502739fe335ceec0aaded030b0f918e758a5d76f61effa0965b189", "impliedFormat": 99}, {"version": "e725839b8f884dab141b42e9d7ff5659212f6e1d7b4054caa23bc719a4629071", "impliedFormat": 99}, {"version": "4fa38a0b8ae02507f966675d0a7d230ed67c92ab8b5736d99a16c5fbe2b42036", "impliedFormat": 99}, {"version": "50ec1e8c23bad160ddedf8debeebc722becbddda127b8fdce06c23eacd3fe689", "impliedFormat": 99}, {"version": "9a0aea3a113064fd607f41375ade308c035911d3c8af5ae9db89593b5ca9f1f9", "impliedFormat": 99}, {"version": "8d643903b58a0bf739ce4e6a8b0e5fb3fbdfaacbae50581b90803934b27d5b89", "impliedFormat": 99}, {"version": "19de2915ccebc0a1482c2337b34cb178d446def2493bf775c4018a4ea355adb8", "impliedFormat": 99}, {"version": "9be8fc03c8b5392cd17d40fd61063d73f08d0ee3457ecf075dcb3768ae1427bd", "impliedFormat": 99}, {"version": "a2d89a8dc5a993514ca79585039eea083a56822b1d9b9d9d85b14232e4782cbe", "impliedFormat": 99}, {"version": "f526f20cae73f17e8f38905de4c3765287575c9c4d9ecacee41cfda8c887da5b", "impliedFormat": 99}, {"version": "d9ec0978b7023612b9b83a71fee8972e290d02f8ff894e95cdd732cd0213b070", "impliedFormat": 99}, {"version": "7ab10c473a058ec8ac4790b05cae6f3a86c56be9b0c0a897771d428a2a48a9f9", "impliedFormat": 99}, {"version": "451d7a93f8249d2e1453b495b13805e58f47784ef2131061821b0e456a9fd0e1", "impliedFormat": 99}, {"version": "21c56fe515d227ed4943f275a8b242d884046001722a4ba81f342a08dbe74ae2", "impliedFormat": 99}, {"version": "d8311f0c39381aa1825081c921efde36e618c5cf46258c351633342a11601208", "impliedFormat": 99}, {"version": "6b50c3bcc92dc417047740810596fcb2df2502aa3f280c9e7827e87896da168a", "impliedFormat": 99}, {"version": "18a6b318d1e7b31e5749a52be0cf9bbce1b275f63190ef32e2c79db0579328ca", "impliedFormat": 99}, {"version": "6a2d0af2c27b993aa85414f3759898502aa198301bc58b0d410948fe908b07b0", "impliedFormat": 99}, {"version": "2da11b6f5c374300e5e66a6b01c3c78ec21b5d3fec0748a28cc28e00be73e006", "impliedFormat": 99}, {"version": "0729691b39c24d222f0b854776b00530877217bfc30aac1dc7fa2f4b1795c536", "impliedFormat": 99}, {"version": "ca45bb5c98c474d669f0e47615e4a5ae65d90a2e78531fda7862ee43e687a059", "impliedFormat": 99}, {"version": "c1c058b91d5b9a24c95a51aea814b0ad4185f411c38ac1d5eef0bf3cebec17dc", "impliedFormat": 99}, {"version": "3ab0ed4060b8e5b5e594138aab3e7f0262d68ad671d6678bcda51568d4fc4ccc", "impliedFormat": 99}, {"version": "e2bf1faba4ff10a6020c41df276411f641d3fdce5c6bae1db0ec84a0bf042106", "impliedFormat": 99}, {"version": "80b0a8fe14d47a71e23d7c3d4dcee9584d4282ef1d843b70cab1a42a4ea1588c", "impliedFormat": 99}, {"version": "a0f02a73f6e3de48168d14abe33bf5970fdacdb52d7c574e908e75ad571e78f7", "impliedFormat": 99}, {"version": "c728002a759d8ec6bccb10eed56184e86aeff0a762c1555b62b5d0fa9d1f7d64", "impliedFormat": 99}, {"version": "586f94e07a295f3d02f847f9e0e47dbf14c16e04ccc172b011b3f4774a28aaea", "impliedFormat": 99}, {"version": "cfe1a0f4ed2df36a2c65ea6bc235dbb8cf6e6c25feb6629989f1fa51210b32e7", "impliedFormat": 99}, {"version": "8ba69c9bf6de79c177329451ffde48ddab7ec495410b86972ded226552f664df", "impliedFormat": 99}, {"version": "15111cbe020f8802ad1d150524f974a5251f53d2fe10eb55675f9df1e82dbb62", "impliedFormat": 99}, {"version": "782dc153c56a99c9ed07b2f6f497d8ad2747764966876dbfef32f3e27ce11421", "impliedFormat": 99}, {"version": "cc2db30c3d8bb7feb53a9c9ff9b0b859dd5e04c83d678680930b5594b2bf99cb", "impliedFormat": 99}, {"version": "46909b8c85a6fd52e0807d18045da0991e3bdc7373435794a6ba425bc23cc6be", "impliedFormat": 99}, {"version": "e4e511ff63bb6bd69a2a51e472c6044298bca2c27835a34a20827bc3ef9b7d13", "impliedFormat": 99}, {"version": "2c86f279d7db3c024de0f21cd9c8c2c972972f842357016bfbbd86955723b223", "impliedFormat": 99}, {"version": "112c895cff9554cf754f928477c7d58a21191c8089bffbf6905c87fe2dc6054f", "impliedFormat": 99}, {"version": "8cfc293b33082003cacbf7856b8b5e2d6dd3bde46abbd575b0c935dc83af4844", "impliedFormat": 99}, {"version": "d2c5c53f85ce0474b3a876d76c4fc44ff7bb766b14ed1bf495f9abac181d7f5f", "impliedFormat": 99}, {"version": "3c523f27926905fcbe20b8301a0cc2da317f3f9aea2273f8fc8d9ae88b524819", "impliedFormat": 99}, {"version": "9ca0d706f6b039cc52552323aeccb4db72e600b67ddc7a54cebc095fc6f35539", "impliedFormat": 99}, {"version": "a64909a9f75081342ddd061f8c6b49decf0d28051bc78e698d347bdcb9746577", "impliedFormat": 99}, {"version": "7d8d55ae58766d0d52033eae73084c4db6a93c4630a3e17f419dd8a0b2a4dcd8", "impliedFormat": 99}, {"version": "b8b5c8ba972d9ffff313b3c8a3321e7c14523fc58173862187e8d1cb814168ac", "impliedFormat": 99}, {"version": "9c42c0fa76ee36cf9cc7cc34b1389fbb4bd49033ec124b93674ec635fabf7ffe", "impliedFormat": 99}, {"version": "6184c8da9d8107e3e67c0b99dedb5d2dfe5ccf6dfea55c2a71d4037caf8ca196", "impliedFormat": 99}, {"version": "4030ceea7bf41449c1b86478b786e3b7eadd13dfe5a4f8f5fe2eb359260e08b3", "impliedFormat": 99}, {"version": "7bf516ec5dfc60e97a5bde32a6b73d772bd9de24a2e0ec91d83138d39ac83d04", "impliedFormat": 99}, {"version": "e6a6fb3e6525f84edf42ba92e261240d4efead3093aca3d6eb1799d5942ba393", "impliedFormat": 99}, {"version": "45df74648934f97d26800262e9b2af2f77ef7191d4a5c2eb1df0062f55e77891", "impliedFormat": 99}, {"version": "3fe361e4e567f32a53af1f2c67ad62d958e3d264e974b0a8763d174102fe3b29", "impliedFormat": 99}, {"version": "28b520acee4bc6911bfe458d1ad3ebc455fa23678463f59946ad97a327c9ab2b", "impliedFormat": 99}, {"version": "121b39b1a9ad5d23ed1076b0db2fe326025150ef476dccb8bf87778fcc4f6dd7", "impliedFormat": 99}, {"version": "f791f92a060b52aa043dde44eb60307938f18d4c7ac13df1b52c82a1e658953f", "impliedFormat": 99}, {"version": "df09443e7743fd6adc7eb108e760084bacdf5914403b7aac5fbd4dc4e24e0c2c", "impliedFormat": 99}, {"version": "eeb4ff4aa06956083eaa2aad59070361c20254b865d986bc997ee345dbd44cbb", "impliedFormat": 99}, {"version": "ed84d5043444d51e1e5908f664addc4472c227b9da8401f13daa565f23624b6e", "impliedFormat": 99}, {"version": "146bf888b703d8baa825f3f2fb1b7b31bda5dff803e15973d9636cdda33f4af3", "impliedFormat": 99}, {"version": "b4ec8b7a8d23bdf7e1c31e43e5beac3209deb7571d2ccf2a9572865bf242da7c", "impliedFormat": 99}, {"version": "3fba0d61d172091638e56fba651aa1f8a8500aac02147d29bd5a9cc0bc8f9ec2", "impliedFormat": 99}, {"version": "a5a57deb0351b03041e0a1448d3a0cc5558c48e0ed9b79b69c99163cdca64ad8", "impliedFormat": 99}, {"version": "9bcecf0cbc2bfc17e33199864c19549905309a0f9ecc37871146107aac6e05ae", "impliedFormat": 99}, {"version": "d6a211db4b4a821e93c978add57e484f2a003142a6aef9dbfa1fe990c66f337b", "impliedFormat": 99}, {"version": "bd4d10bd44ce3f630dd9ce44f102422cb2814ead5711955aa537a52c8d2cae14", "impliedFormat": 99}, {"version": "08e4c39ab1e52eea1e528ee597170480405716bae92ebe7a7c529f490afff1e0", "impliedFormat": 99}, {"version": "625bb2bc3867557ea7912bd4581288a9fca4f3423b8dffa1d9ed57fafc8610e3", "impliedFormat": 99}, {"version": "d1992164ecc334257e0bef56b1fd7e3e1cea649c70c64ffc39999bb480c0ecdf", "impliedFormat": 99}, {"version": "a53ff2c4037481eb357e33b85e0d78e8236e285b6428b93aa286ceea1db2f5dc", "impliedFormat": 99}, {"version": "4fe608d524954b6857d78857efce623852fcb0c155f010710656f9db86e973a5", "impliedFormat": 99}, {"version": "b53b62a9838d3f57b70cc456093662302abb9962e5555f5def046172a4fe0d4e", "impliedFormat": 99}, {"version": "9866369eb72b6e77be2a92589c9df9be1232a1a66e96736170819e8a1297b61f", "impliedFormat": 99}, {"version": "43abfbdf4e297868d780b8f4cfdd8b781b90ecd9f588b05e845192146a86df34", "impliedFormat": 99}, {"version": "582419791241fb851403ae4a08d0712a63d4c94787524a7419c2bc8e0eb1b031", "impliedFormat": 99}, {"version": "18437eeb932fe48590b15f404090db0ab3b32d58f831d5ffc157f63b04885ee5", "impliedFormat": 99}, {"version": "0c5eaedf622d7a8150f5c2ec1f79ac3d51eea1966b0b3e61bfdea35e8ca213a7", "impliedFormat": 99}, {"version": "fac39fc7a9367c0246de3543a6ee866a0cf2e4c3a8f64641461c9f2dac0d8aae", "impliedFormat": 99}, {"version": "3b9f559d0200134f3c196168630997caedeadc6733523c8b6076a09615d5dec8", "impliedFormat": 99}, {"version": "932af64286d9723da5ef7b77a0c4229829ce8e085e6bcc5f874cb0b83e8310d4", "impliedFormat": 99}, {"version": "adeb9278f11f5561157feee565171c72fd48f5fe34ed06f71abf24e561fcaa1e", "impliedFormat": 99}, {"version": "2269fef79b4900fc6b08c840260622ca33524771ff24fda5b9101ad98ea551f3", "impliedFormat": 99}, {"version": "73d47498a1b73d5392d40fb42a3e7b009ae900c8423f4088c4faa663cc508886", "impliedFormat": 99}, {"version": "7efc34cdc4da0968c3ba687bc780d5cacde561915577d8d1c1e46c7ac931d023", "impliedFormat": 99}, {"version": "3c20a3bb0c50c819419f44aa55acc58476dad4754a16884cef06012d02b0722f", "impliedFormat": 99}, {"version": "4569abf6bc7d51a455503670f3f1c0e9b4f8632a3b030e0794c61bfbba2d13be", "impliedFormat": 99}, {"version": "98b2297b4dc1404078a54b61758d8643e4c1d7830af724f3ed2445d77a7a2d57", "impliedFormat": 99}, {"version": "952ba89d75f1b589e07070fea2d8174332e3028752e76fd46e1c16cc51e6e2af", "impliedFormat": 99}, {"version": "b6c9a2deefb6a57ff68d2a38d33c34407b9939487fc9ee9f32ba3ecf2987a88a", "impliedFormat": 99}, {"version": "f6b371377bab3018dac2bca63e27502ecbd5d06f708ad7e312658d3b5315d948", "impliedFormat": 99}, {"version": "31947dd8f1c8eeb7841e1f139a493a73bd520f90e59a6415375d0d8e6a031f01", "impliedFormat": 99}, {"version": "95cd83b807e10b1af408e62caf5fea98562221e8ddca9d7ccc053d482283ddda", "impliedFormat": 99}, {"version": "19287d6b76288c2814f1633bdd68d2b76748757ffd355e73e41151644e4773d6", "impliedFormat": 99}, {"version": "fc4e6ec7dade5f9d422b153c5d8f6ad074bd9cc4e280415b7dc58fb5c52b5df1", "impliedFormat": 99}, {"version": "3aea973106e1184db82d8880f0ca134388b6cbc420f7309d1c8947b842886349", "impliedFormat": 99}, {"version": "765e278c464923da94dda7c2b281ece92f58981642421ae097862effe2bd30fa", "impliedFormat": 99}, {"version": "de260bed7f7d25593f59e859bd7c7f8c6e6bb87e8686a0fcafa3774cb5ca02d8", "impliedFormat": 99}, {"version": "b5c341ce978f5777fbe05bc86f65e9906a492fa6b327bda3c6aae900c22e76c6", "impliedFormat": 99}, {"version": "686ddbfaf88f06b02c6324005042f85317187866ca0f8f4c9584dd9479653344", "impliedFormat": 99}, {"version": "7f789c0c1db29dd3aab6e159d1ba82894a046bf8df595ac48385931ae6ad83e0", "impliedFormat": 99}, {"version": "8eb3057d4fe9b59b2492921b73a795a2455ebe94ccb3d01027a7866612ead137", "impliedFormat": 99}, {"version": "1e43c5d7aee1c5ec20611e28b5417f5840c75d048de9d7f1800d6808499236f8", "impliedFormat": 99}, {"version": "d42610a5a2bee4b71769968a24878885c9910cd049569daa2d2ee94208b3a7a5", "impliedFormat": 99}, {"version": "f6ed95506a6ed2d40ed5425747529befaa4c35fcbbc1e0d793813f6d725690fa", "impliedFormat": 99}, {"version": "a6fcc1cd6583939506c906dff1276e7ebdc38fbe12d3e108ba38ad231bd18d97", "impliedFormat": 99}, {"version": "ed13354f0d96fb6d5878655b1fead51722b54875e91d5e53ef16de5b71a0e278", "impliedFormat": 99}, {"version": "1193b4872c1fb65769d8b164ca48124c7ebacc33eae03abf52087c2b29e8c46c", "impliedFormat": 99}, {"version": "af682dfabe85688289b420d939020a10eb61f0120e393d53c127f1968b3e9f66", "impliedFormat": 99}, {"version": "0dca04006bf13f72240c6a6a502df9c0b49c41c3cab2be75e81e9b592dcd4ea8", "impliedFormat": 99}, {"version": "79d6ac4a2a229047259116688f9cd62fda25422dee3ad304f77d7e9af53a41ef", "impliedFormat": 99}, {"version": "64534c17173990dc4c3d9388d16675a059aac407031cfce8f7fdffa4ee2de988", "impliedFormat": 99}, {"version": "ba46d160a192639f3ca9e5b640b870b1263f24ac77b6895ab42960937b42dcbb", "impliedFormat": 99}, {"version": "5e5ddd6fc5b590190dde881974ab969455e7fad61012e32423415ae3d085b037", "impliedFormat": 99}, {"version": "1c16fd00c42b60b96fe0fa62113a953af58ddf0d93b0a49cb4919cf5644616f0", "impliedFormat": 99}, {"version": "eb240c0e6b412c57f7d9a9f1c6cd933642a929837c807b179a818f6e8d3a4e44", "impliedFormat": 99}, {"version": "4a7bde5a1155107fc7d9483b8830099f1a6072b6afda5b78d91eb5d6549b3956", "impliedFormat": 99}, {"version": "3c1baaffa9a24cc7ef9eea6b64742394498e0616b127ca630aca0e11e3298006", "impliedFormat": 99}, {"version": "87ca1c31a326c898fa3feb99ec10750d775e1c84dbb7c4b37252bcf3742c7b21", "impliedFormat": 99}, {"version": "d7bd26af1f5457f037225602035c2d7e876b80d02663ab4ca644099ad3a55888", "impliedFormat": 99}, {"version": "2ad0a6b93e84a56b64f92f36a07de7ebcb910822f9a72ad22df5f5d642aff6f3", "impliedFormat": 99}, {"version": "523d1775135260f53f672264937ee0f3dc42a92a39de8bee6c48c7ea60b50b5a", "impliedFormat": 99}, {"version": "e441b9eebbc1284e5d995d99b53ed520b76a87cab512286651c4612d86cd408e", "impliedFormat": 99}, {"version": "76f853ee21425c339a79d28e0859d74f2e53dee2e4919edafff6883dd7b7a80f", "impliedFormat": 99}, {"version": "00cf042cd6ba1915648c8d6d2aa00e63bbbc300ea54d28ed087185f0f662e080", "impliedFormat": 99}, {"version": "f57e6707d035ab89a03797d34faef37deefd3dd90aa17d90de2f33dce46a2c56", "impliedFormat": 99}, {"version": "cc8b559b2cf9380ca72922c64576a43f000275c72042b2af2415ce0fb88d7077", "impliedFormat": 99}, {"version": "1a337ca294c428ba8f2eb01e887b28d080ee4a4307ae87e02e468b1d26af4a74", "impliedFormat": 99}, {"version": "5a15362fc2e72765a908c0d4dd89e3ab3b763e8bc8c23f19234a709ecfd202fe", "impliedFormat": 99}, {"version": "2dffdfe62ac8af0943853234519616db6fd8958fc7ff631149fd8364e663f361", "impliedFormat": 99}, {"version": "5dbdb2b2229b5547d8177c34705272da5a10b8d0033c49efbc9f6efba5e617f2", "impliedFormat": 99}, {"version": "6fc0498cd8823d139004baff830343c9a0d210c687b2402c1384fb40f0aa461c", "impliedFormat": 99}, {"version": "8492306a4864a1dc6fc7e0cc0de0ae9279cbd37f3aae3e9dc1065afcdc83dddc", "impliedFormat": 99}, {"version": "c011b378127497d6337a93f020a05f726db2c30d55dc56d20e6a5090f05919a6", "impliedFormat": 99}, {"version": "f4556979e95a274687ae206bbab2bb9a71c3ad923b92df241d9ab88c184b3f40", "impliedFormat": 99}, {"version": "50e82bb6e238db008b5beba16d733b77e8b2a933c9152d1019cf8096845171a4", "impliedFormat": 99}, {"version": "d6011f8b8bbf5163ef1e73588e64a53e8bf1f13533c375ec53e631aad95f1375", "impliedFormat": 99}, {"version": "693cd7936ac7acfa026d4bcb5801fce71cec49835ba45c67af1ef90dbfd30af7", "impliedFormat": 99}, {"version": "195e2cf684ecddfc1f6420564535d7c469f9611ce7a380d6e191811f84556cd2", "impliedFormat": 99}, {"version": "1dc6b6e7b2a7f2962f31c77f4713f3a5a132bbe14c00db75d557568fe82e4311", "impliedFormat": 99}, {"version": "add93b1180e9aaac2dae4ef3b16f7655893e2ecbe62bd9e48366c305f0063d89", "impliedFormat": 99}, {"version": "594bd896fe37c970aafb7a376ebeec4c0d636b62a5f611e2e27d30fb839ad8a5", "impliedFormat": 99}, {"version": "b1c6a6faf60542ba4b4271db045d7faea56e143b326ef507d2797815250f3afc", "impliedFormat": 99}, {"version": "8c8b165beb794260f462679329b131419e9f5f35212de11c4d53e6d4d9cbedf6", "impliedFormat": 99}, {"version": "ee5a4cf57d49fcf977249ab73c690a59995997c4672bb73fcaaf2eed65dbd1b2", "impliedFormat": 99}, {"version": "f9f36051f138ab1c40b76b230c2a12b3ce6e1271179f4508da06a959f8bee4c1", "impliedFormat": 99}, {"version": "9dc2011a3573d271a45c12656326530c0930f92539accbec3531d65131a14a14", "impliedFormat": 99}, {"version": "091521ce3ede6747f784ae6f68ad2ea86bbda76b59d2bf678bcad2f9d141f629", "impliedFormat": 99}, {"version": "202c2be951f53bafe943fb2c8d1245e35ed0e4dfed89f48c9a948e4d186dd6d4", "impliedFormat": 99}, {"version": "c618aead1d799dbf4f5b28df5a6b9ce13d72722000a0ec3fe90a8115b1ea9226", "impliedFormat": 99}, {"version": "9b0bf59708549c3e77fddd36530b95b55419414f88bbe5893f7bc8b534617973", "impliedFormat": 99}, {"version": "7e216f67c4886f1bde564fb4eebdd6b185f262fe85ad1d6128cad9b229b10354", "impliedFormat": 99}, {"version": "cd51e60b96b4d43698df74a665aa7a16604488193de86aa60ec0c44d9f114951", "impliedFormat": 99}, {"version": "b63341fb6c7ba6f2aeabd9fc46b43e6cc2d2b9eec06534cfd583d9709f310ec2", "impliedFormat": 99}, {"version": "be2af50c81b15bcfe54ad60f53eb1c72dae681c72d0a9dce1967825e1b5830a3", "impliedFormat": 99}, {"version": "be5366845dfb9726f05005331b9b9645f237f1ddc594c0def851208e8b7d297b", "impliedFormat": 99}, {"version": "5ddd536aaeadd4bf0f020492b3788ed209a7050ce27abec4e01c7563ff65da81", "impliedFormat": 99}, {"version": "e243b24da119c1ef0d79af2a45217e50682b139cb48e7607efd66cc01bd9dcda", "impliedFormat": 99}, {"version": "5b1398c8257fd180d0bf62e999fe0a89751c641e87089a83b24392efda720476", "impliedFormat": 99}, {"version": "1588b1359f8507a16dbef67cd2759965fc2e8d305e5b3eb71be5aa9506277dff", "impliedFormat": 99}, {"version": "4c99f2524eee1ec81356e2b4f67047a4b7efaf145f1c4eb530cd358c36784423", "impliedFormat": 99}, {"version": "b30c6b9f6f30c35d6ef84daed1c3781e367f4360171b90598c02468b0db2fc3d", "impliedFormat": 99}, {"version": "79c0d32274ccfd45fae74ac61d17a2be27aea74c70806d22c43fc625b7e9f12a", "impliedFormat": 99}, {"version": "1b7e3958f668063c9d24ac75279f3e610755b0f49b1c02bb3b1c232deb958f54", "impliedFormat": 99}, {"version": "779d4022c3d0a4df070f94858a33d9ebf54af3664754536c4ce9fd37c6f4a8db", "impliedFormat": 99}, {"version": "e662f063d46aa8c088edffdf1d96cb13d9a2cbf06bc38dc6fc62b4d125fb7b49", "impliedFormat": 99}, {"version": "d1d612df1e41c90d9678b07740d13d4f8e6acec2f17390d4ff4be5c889a6d37d", "impliedFormat": 99}, {"version": "c95933fe140918892d569186f17b70ef6b1162f851a0f13f6a89e8f4d599c5a1", "impliedFormat": 99}, {"version": "1d8d30677f87c13c2786980a80750ac1e281bdb65aa013ea193766fe9f0edd74", "impliedFormat": 99}, {"version": "4661673cbc984b8a6ee5e14875a71ed529b64e7f8e347e12c0db4cecc25ad67d", "impliedFormat": 99}, {"version": "7f980a414274f0f23658baa9a16e21d828535f9eac538e2eab2bb965325841db", "impliedFormat": 99}, {"version": "20fb747a339d3c1d4a032a31881d0c65695f8167575e01f222df98791a65da9b", "impliedFormat": 99}, {"version": "dd4e7ebd3f205a11becf1157422f98db675a626243d2fbd123b8b93efe5fb505", "impliedFormat": 99}, {"version": "43ec6b74c8d31e88bb6947bb256ad78e5c6c435cbbbad991c3ff39315b1a3dba", "impliedFormat": 99}, {"version": "b27242dd3af2a5548d0c7231db7da63d6373636d6c4e72d9b616adaa2acef7e1", "impliedFormat": 99}, {"version": "e0ee7ba0571b83c53a3d6ec761cf391e7128d8f8f590f8832c28661b73c21b68", "impliedFormat": 99}, {"version": "072bfd97fc61c894ef260723f43a416d49ebd8b703696f647c8322671c598873", "impliedFormat": 99}, {"version": "e70875232f5d5528f1650dd6f5c94a5bed344ecf04bdbb998f7f78a3c1317d02", "impliedFormat": 99}, {"version": "8e495129cb6cd8008de6f4ff8ce34fe1302a9e0dcff8d13714bd5593be3f7898", "impliedFormat": 99}, {"version": "0345bc0b1067588c4ea4c48e34425d3284498c629bc6788ebc481c59949c9037", "impliedFormat": 99}, {"version": "e30f5b5d77c891bc16bd65a2e46cd5384ea57ab3d216c377f482f535db48fc8f", "impliedFormat": 99}, {"version": "f113afe92ee919df8fc29bca91cab6b2ffbdd12e4ac441d2bb56121eb5e7dbe3", "impliedFormat": 99}, {"version": "49d567cc002efb337f437675717c04f207033f7067825b42bb59c9c269313d83", "impliedFormat": 99}, {"version": "1d248f707d02dc76555298a934fba0f337f5028bb1163ce59cd7afb831c9070f", "impliedFormat": 99}, {"version": "5d8debffc9e7b842dc0f17b111673fe0fc0cca65e67655a2b543db2150743385", "impliedFormat": 99}, {"version": "5fccbedc3eb3b23bc6a3a1e44ceb110a1f1a70fa8e76941dce3ae25752caa7a9", "impliedFormat": 99}, {"version": "f4031b95f3bab2b40e1616bd973880fb2f1a97c730bac5491d28d6484fac9560", "impliedFormat": 99}, {"version": "dbe75b3c5ed547812656e7945628f023c4cd0bc1879db0db3f43a57fb8ec0e2b", "impliedFormat": 99}, {"version": "b754718a546a1939399a6d2a99f9022d8a515f2db646bab09f7d2b5bff3cbb82", "impliedFormat": 99}, {"version": "2eef10fb18ed0b4be450accf7a6d5bcce7b7f98e02cac4e6e793b7ad04fc0d79", "impliedFormat": 99}, {"version": "c46f471e172c3be12c0d85d24876fedcc0c334b0dab48060cdb1f0f605f09fed", "impliedFormat": 99}, {"version": "7d6ddeead1d208588586c58c26e4a23f0a826b7a143fb93de62ed094d0056a33", "impliedFormat": 99}, {"version": "7c5782291ff6e7f2a3593295681b9a411c126e3736b83b37848032834832e6b9", "impliedFormat": 99}, {"version": "3a3f09df6258a657dd909d06d4067ee360cd2dccc5f5d41533ae397944a11828", "impliedFormat": 99}, {"version": "ea54615be964503fec7bce04336111a6fa455d3e8d93d44da37b02c863b93eb8", "impliedFormat": 99}, {"version": "2a83694bc3541791b64b0e57766228ea23d92834df5bf0b0fcb93c5bb418069c", "impliedFormat": 99}, {"version": "b5913641d6830e7de0c02366c08b1d26063b5758132d8464c938e78a45355979", "impliedFormat": 99}, {"version": "46c095d39c1887979d9494a824eda7857ec13fb5c20a6d4f7d02c2975309bf45", "impliedFormat": 99}, {"version": "f6e02ca076dc8e624aa38038e3488ebd0091e2faea419082ed764187ba8a6500", "impliedFormat": 99}, {"version": "4d49e8a78aba1d4e0ad32289bf8727ae53bc2def9285dff56151a91e7d770c3e", "impliedFormat": 99}, {"version": "63315cf08117cc728eab8f3eec8801a91d2cd86f91d0ae895d7fd928ab54596d", "impliedFormat": 99}, {"version": "a14a6f3a5636bcaebfe9ec2ccfa9b07dc94deb1f6c30358e9d8ea800a1190d5e", "impliedFormat": 99}, {"version": "21206e7e81876dabf2a7af7aa403f343af1c205bdcf7eff24d9d7f4eee6214c4", "impliedFormat": 99}, {"version": "cd0a9f0ffec2486cad86b7ef1e4da42953ffeb0eb9f79f536e16ff933ec28698", "impliedFormat": 99}, {"version": "f609a6ec6f1ab04dba769e14d6b55411262fd4627a099e333aa8876ea125b822", "impliedFormat": 99}, {"version": "6d8052bb814be030c64cb22ca0e041fe036ad3fc8d66208170f4e90d0167d354", "impliedFormat": 99}, {"version": "851f72a5d3e8a2bf7eeb84a3544da82628f74515c92bdf23c4a40af26dcc1d16", "impliedFormat": 99}, {"version": "59692a7938aab65ea812a8339bbc63c160d64097fe5a457906ea734d6f36bcd4", "impliedFormat": 99}, {"version": "8cb3b95e610c44a9986a7eab94d7b8f8462e5de457d5d10a0b9c6dd16bde563b", "impliedFormat": 99}, {"version": "f571713abd9a676da6237fe1e624d2c6b88c0ca271c9f1acc1b4d8efeea60b66", "impliedFormat": 99}, {"version": "16c5d3637d1517a3d17ed5ebcfbb0524f8a9997a7b60f6100f7c5309b3bb5ac8", "impliedFormat": 99}, {"version": "ca1ec669726352c8e9d897f24899abf27ad15018a6b6bcf9168d5cd1242058ab", "impliedFormat": 99}, {"version": "bffb1b39484facf6d0c5d5feefe6c0736d06b73540b9ce0cf0f12da2edfd8e1d", "impliedFormat": 99}, {"version": "f1663c030754f6171b8bb429096c7d2743282de7733bccd6f67f84a4c588d96e", "impliedFormat": 99}, {"version": "dd09693285e58504057413c3adc84943f52b07d2d2fd455917f50fa2a63c9d69", "impliedFormat": 99}, {"version": "d94c94593d03d44a03810a85186ae6d61ebeb3a17a9b210a995d85f4b584f23d", "impliedFormat": 99}, {"version": "c7c3bf625a8cb5a04b1c0a2fbe8066ecdbb1f383d574ca3ffdabe7571589a935", "impliedFormat": 99}, {"version": "7a2f39a4467b819e873cd672c184f45f548511b18f6a408fe4e826136d0193bb", "impliedFormat": 99}, {"version": "f8a0ae0d3d4993616196619da15da60a6ec5a7dfaf294fe877d274385eb07433", "impliedFormat": 99}, {"version": "2cca80de38c80ef6c26deb4e403ca1ff4efbe3cf12451e26adae5e165421b58d", "impliedFormat": 99}, {"version": "0070d3e17aa5ad697538bf865faaff94c41f064db9304b2b949eb8bcccb62d34", "impliedFormat": 99}, {"version": "53df93f2db5b7eb8415e98242c1c60f6afcac2db44bce4a8830c8f21eee6b1dd", "impliedFormat": 99}, {"version": "d67bf28dc9e6691d165357424c8729c5443290367344263146d99b2f02a72584", "impliedFormat": 99}, {"version": "932557e93fbdf0c36cc29b9e35950f6875425b3ac917fa0d3c7c2a6b4f550078", "impliedFormat": 99}, {"version": "e3dc7ec1597fb61de7959335fb7f8340c17bebf2feb1852ed8167a552d9a4a25", "impliedFormat": 99}, {"version": "b64e15030511c5049542c2e0300f1fe096f926cf612662884f40227267f5cd9f", "impliedFormat": 99}, {"version": "1932796f09c193783801972a05d8fb1bfef941bb46ac76fbe1abb0b3bfb674fa", "impliedFormat": 99}, {"version": "d9575d5787311ee7d61ad503f5061ebcfaf76b531cfecce3dc12afb72bb2d105", "impliedFormat": 99}, {"version": "5b41d96c9a4c2c2d83f1200949f795c3b6a4d2be432b357ad1ab687e0f0de07c", "impliedFormat": 99}, {"version": "38ec829a548e869de4c5e51671245a909644c8fb8e7953259ebb028d36b4dd06", "impliedFormat": 99}, {"version": "20c2c5e44d37dac953b516620b5dba60c9abd062235cdf2c3bfbf722d877a96b", "impliedFormat": 99}, {"version": "875fe6f7103cf87c1b741a0895fda9240fed6353d5e7941c8c8cbfb686f072b4", "impliedFormat": 99}, {"version": "c0ccccf8fbcf5d95f88ed151d0d8ce3015aa88cf98d4fd5e8f75e5f1534ee7ae", "impliedFormat": 99}, {"version": "1b1f4aba21fd956269ced249b00b0e5bfdbd5ebd9e628a2877ab1a2cf493c919", "impliedFormat": 99}, {"version": "939e3299952dff0869330e3324ba16efe42d2cf25456d7721d7f01a43c1b0b34", "impliedFormat": 99}, {"version": "f0a9b52faec508ba22053dedfa4013a61c0425c8b96598cef3dea9e4a22637c6", "impliedFormat": 99}, {"version": "d5b302f50db61181adc6e209af46ae1f27d7ef3d822de5ea808c9f44d7d219fd", "impliedFormat": 99}, {"version": "19131632ba492c83e8eeadf91a481def0e0b39ffc3f155bc20a7f640e0570335", "impliedFormat": 99}, {"version": "4581c03abea21396c3e1bb119e2fd785a4d91408756209cbeed0de7070f0ab5b", "impliedFormat": 99}, {"version": "ebcd3b99e17329e9d542ef2ccdd64fddab7f39bc958ee99bbdb09056c02d6e64", "impliedFormat": 99}, {"version": "4b148999deb1d95b8aedd1a810473a41d9794655af52b40e4894b51a8a4e6a6d", "impliedFormat": 99}, {"version": "1781cc99a0f3b4f11668bb37cca7b8d71f136911e87269e032f15cf5baa339bf", "impliedFormat": 99}, {"version": "33f1b7fa96117d690035a235b60ecd3cd979fb670f5f77b08206e4d8eb2eb521", "impliedFormat": 99}, {"version": "01429b306b94ff0f1f5548ce5331344e4e0f5872b97a4776bd38fd2035ad4764", "impliedFormat": 99}, {"version": "c1bc4f2136de7044943d784e7a18cb8411c558dbb7be4e4b4876d273cbd952af", "impliedFormat": 99}, {"version": "5470f84a69b94643697f0d7ec2c8a54a4bea78838aaa9170189b9e0a6e75d2cf", "impliedFormat": 99}, {"version": "36aaa44ee26b2508e9a6e93cd567e20ec700940b62595caf962249035e95b5e3", "impliedFormat": 99}, {"version": "f8343562f283b7f701f86ad3732d0c7fd000c20fe5dc47fa4ed0073614202b4d", "impliedFormat": 99}, {"version": "a53c572630a78cd99a25b529069c1e1370f8a5d8586d98e798875f9052ad7ad1", "impliedFormat": 99}, {"version": "4ad3451d066711dde1430c544e30e123f39e23c744341b2dfd3859431c186c53", "impliedFormat": 99}, {"version": "8069cbef9efa7445b2f09957ffbc27b5f8946fdbade4358fb68019e23df4c462", "impliedFormat": 99}, {"version": "cd8b4e7ad04ba9d54eb5b28ac088315c07335b837ee6908765436a78d382b4c3", "impliedFormat": 99}, {"version": "d533d8f8e5c80a30c51f0cbfe067b60b89b620f2321d3a581b5ba9ac8ffd7c3a", "impliedFormat": 99}, {"version": "33f49f22fdda67e1ddbacdcba39e62924793937ea7f71f4948ed36e237555de3", "impliedFormat": 99}, {"version": "710c31d7c30437e2b8795854d1aca43b540cb37cefd5900f09cfcd9e5b8540c4", "impliedFormat": 99}, {"version": "b2c03a0e9628273bc26a1a58112c311ffbc7a0d39938f3878837ab14acf3bc41", "impliedFormat": 99}, {"version": "a93beb0aa992c9b6408e355ea3f850c6f41e20328186a8e064173106375876c2", "impliedFormat": 99}, {"version": "efdcba88fcd5421867898b5c0e8ea6331752492bd3547942dea96c7ebcb65194", "impliedFormat": 99}, {"version": "a98e777e7a6c2c32336a017b011ba1419e327320c3556b9139413e48a8460b9a", "impliedFormat": 99}, {"version": "ea44f7f8e1fe490516803c06636c1b33a6b82314366be1bd6ffa4ba89bc09f86", "impliedFormat": 99}, {"version": "c25f22d78cc7f46226179c33bef0e4b29c54912bde47b62e5fdaf9312f22ffcb", "impliedFormat": 99}, {"version": "d57579cfedc5a60fda79be303080e47dfe0c721185a5d95276523612228fcefc", "impliedFormat": 99}, {"version": "a41630012afe0d4a9ff14707f96a7e26e1154266c008ddbd229e3f614e4d1cf7", "impliedFormat": 99}, {"version": "298a858633dfa361bb8306bbd4cfd74f25ab7cc20631997dd9f57164bc2116d1", "impliedFormat": 99}, {"version": "921782c45e09940feb232d8626a0b8edb881be2956520c42c44141d9b1ddb779", "impliedFormat": 99}, {"version": "06117e4cc7399ce1c2b512aa070043464e0561f956bda39ef8971a2fcbcdbf2e", "impliedFormat": 99}, {"version": "daccf332594b304566c7677c2732fed6e8d356da5faac8c5f09e38c2f607a4ab", "impliedFormat": 99}, {"version": "4386051a0b6b072f35a2fc0695fecbe4a7a8a469a1d28c73be514548e95cd558", "impliedFormat": 99}, {"version": "78e41de491fe25947a7fd8eeef7ebc8f1c28c1849a90705d6e33f34b1a083b90", "impliedFormat": 99}, {"version": "3ccd198e0a693dd293ed22e527c8537c76b8fe188e1ebf20923589c7cfb2c270", "impliedFormat": 99}, {"version": "2ebf2ee015d5c8008428493d4987e2af9815a76e4598025dd8c2f138edc1dcae", "impliedFormat": 99}, {"version": "0dcc8f61382c9fcdafd48acc54b6ffda69ca4bb7e872f8ad12fb011672e8b20c", "impliedFormat": 99}, {"version": "9db563287eb527ead0bcb9eb26fbec32f662f225869101af3cabcb6aee9259cf", "impliedFormat": 99}, {"version": "068489bec523be43f12d8e4c5c337be4ff6a7efb4fe8658283673ae5aae14b85", "impliedFormat": 99}, {"version": "838212d0dc5b97f7c5b5e29a89953de3906f72fce13c5ae3c5ade346f561d226", "impliedFormat": 99}, {"version": "db235cbf740d7ded89a4fae291cd551374f3b21a2d2297410a111a5dc1c72a06", "signature": "b09f8198a1a35b3e6e682a989c4ff5c41b2c44391f22ba60c068f9b89e637caa"}, {"version": "37e719916624970e5a725e4acc3298768f18b8f1cbb91cca11f6826808a7a401", "signature": "6af4e3b448d5c532bc716d8493f39e17104927e1e9697aacf768fffa969f1850"}, {"version": "3809facfd045d520c615b3b6f7319fcc08fc1d06518387db93e669bf344949e3", "signature": "71f255c282738f3d1bd895a59df9a7270e2890169778e8e9f45032469555ffcb"}, {"version": "31fe20386c1f803f20fd384e113d81eee776db6c57e58c8110d935bbb27c50a4", "signature": "214663ac4a96b3a709e3e3434fa887890f23a81e25b72b845ff4b1f07322801f"}, {"version": "105dfdedcd7ba388177a0f6c55123468220126cddad16b1d5ec1e5f3e1eea553", "signature": "ccc23f3463bec04d88198415f2d768a23eeea8acaca9a8e719c28fb692902cdc"}, {"version": "519d89a9cf0b00f4e08549374ce9ce9b6f025c60693dae58991105209bd83ca4", "signature": "063aa7267df5bc9c62784e757dee4ce8bb09f8799ebb0f1ba23d1b76de0f8efb"}, {"version": "578d9e212eed527e027acf80b8ca715528c549c3711511b8afd4fe6fed528cc1", "signature": "0aa34123954adbe077df2e8882a17ab5594b82693009a689c3829b4bb1b751a0"}, {"version": "cc766645f7197c641371c347beaa45406486af4e3a4be3d11d3efffc3e7f69e7", "signature": "2f6e50dc06894d8a3c16a7c42a6c959197a26f008047b3ce71ab324f568c585a"}, {"version": "2e06ca982c2b1280165a8d5735907dc53e2f105d1284ad048a35bf9f99fa3cbf", "signature": "8fcb33281703259d9e2dc58f287a4457a54f7c6ded0ce70f0737f3927d403f1e"}, {"version": "50673625890854afc4b1886dbe5db2bf00dfd8adaba840356b5ea507706d8724", "impliedFormat": 1}, {"version": "f453c7a4fa3a927747a7f844fb14d5149d0543fb79a4803fd2edbc5e3e3fa7c0", "impliedFormat": 1}, {"version": "84ec1703b7cbba570b78f28f5b237402ba1960052094bb468f4ecad868f7ef71", "impliedFormat": 1}, {"version": "3e267ac5389dd4c2bd59456cddf01db7fa6688b5e0a5cdbe7d5fa1d50121ac7d", "impliedFormat": 1}, {"version": "76630e8108de18738f76636bd7d6fc87173809a7405eab44deea5ed32c1a9dcb", "impliedFormat": 1}, {"version": "1e2502cbf516e1c788e25b1a0afb2366388bdbdcddf3efa0f131d7ab0c3ea3ec", "signature": "697dfb789d43d394527d137c11d07495118b34bde2203f0dbac7336871effd3e"}, {"version": "6710aab64979f1b4bdc2952acd5854271b946f25989c205157a677f58c506cfc", "signature": "511739358697059ff37f84c50c68b8882736fa4f01077d072b77100fe5381450"}, {"version": "0923613452532e76bb84902eeb86a1ec891dfa08161e4dbe456158927d122ef0", "signature": "0c759161088395ec875e3ef57d4bde3d8c43458913e9c7c2ce47ea874d6fe86c"}, {"version": "2e25dcba6607483ec0847fad47b291a176c7e99af8c8630ef0569ccff93913fb", "signature": "5bb977436a545923740ce15857e568888a0ee2d54c82ed7309af4f6f6afa21d5"}, {"version": "ed6420c86fae0eef9e23c53318a44bd9aaa5da99534f432f2868ecbf31b95b89", "signature": "2a89067161618e17ec44ed86e6c122763dc879b4a9b9b862931fd97ccf126155"}, {"version": "53ebefc25352794dfa7a0466aa10cddf1458579a160eb9b138d7b299bdeda10d", "signature": "ecc542122730963e8268a59136732f7ae75a4effa803c5685b90be69a71840fa"}, {"version": "95e5d80bbda75d68d46e72077b6d374a3c790e32c30d610243c50d1d1f2b5a72", "signature": "6c07a38977f2224be37e02f308b247bcac7ea23046b9d5bf8da1586a585fc30f"}, {"version": "26a9465978df00ba6096ca32740ab209d01b1d6643c06e9e61050435746525c5", "signature": "3eb972ae325aa293fdb6077cdf956f209ee6ea34b4e874ff7ec8686b3079972f"}, {"version": "765e2af9305f3ccafff90447d776af9cfeb1ce08557595fe6a8f964e3505e22e", "signature": "68dfc22d24a462e2344abc4d521b2b7191d8e1a719f72b5d92fe3eee76d18d20"}, {"version": "f593e0c2da12ada8859a119510dccfddf3993e00a425c6365a9ace1ae843be5f", "signature": "2ac596579c18c549814c04e1f3934fa1a0f31742f80d121b4c502fc06181694a"}, {"version": "d90b0fb6e51b81c0f19bdf357049eea901bcbe4a7236d92fdb218d4b234eed0e", "signature": "cd897b02c25f473102a3e258e5dc98af9b8e9c11394c675f44cbd6cdf913d291"}, {"version": "3055609ceb50fb387dac396dca252f2658bef4dea5b6c9b86c275cd4a010330c", "signature": "882c14142552ddf30db7813b4e2990d1b069f89ef98ceea36ede1f28ed2130ea"}, {"version": "efa215714d8ef8ed5a79ba7842a19a73616d9964126e0486ec9ef4a55a204204", "signature": "dcf5abe8c2137501a1d6c3253356734177773eb35d292c85f6e591ee90d46e33"}, {"version": "4bc50ffc2e2ff8bf90631ce7042ba9c8c3284d4e1f4c4fecffa80d049f5d21a9", "signature": "e4de26704b456adaa76a8b8b9ffb1f54c09729aeb6a75beac716cd859c210c00"}, {"version": "9b0f642fec9862bb021cf6d2df948d53614042400b9bd873953612344d0b4980", "signature": "d13435abb4cf6a5d060f6143f2175ade9bb140ecc28db98e91c9794877fdac34"}, {"version": "a040c1c7f581d40552ed76cb573d104e1c14f917d6f9bc8be2a0c181b13c27df", "signature": "391990f43fb335e1aa2868a927f132523619c88241ec6d61882564bb52d0c835"}, {"version": "9ca86bea0a400c53e64432622154331cb2851261421ab8fc90b41955fa929d9d", "signature": "b152db4a825c72cb35255b351093d3a4e4c99d7bbcac556f2699627b1ec746de"}, {"version": "3ce20d97c23855f06a305580f6c42273a05c317a9ede7f9fcb615ccaefcce465", "signature": "6cda7085650cf43669d2cf27a3ed99bb5578c2aea3d07d0684694b3c326f1aec"}, {"version": "2663d78627ab217cbe87351ef49767b01a55db62b7a4b7921b41fd105adad9de", "signature": "bbf9d6b8f098c0fc30b9ade9489312bb5b22f308a0f6ea86e2e92dcca2716892"}, {"version": "172016540ae1b08b68288b404ea5b5525056c95ad37f99d6d7f1b02e2dbaa0d3", "signature": "72bc27c2488a7cb340be7eab2db32fd90e70cd0d6e64501a6a62cb0d1782b966"}, {"version": "fa18c93d9340505b9903837fe8a362db9e2c086f273409edfccec45a2aa91f63", "signature": "21b494d463619a214dc0b9c8287c4fdc38b3c2fab7edbc0111452540947110dd"}, {"version": "d94ecd7a78b87ec32ce05b2e0731d2db4bd4c0fbc931110e01feff93677295de", "signature": "06c53c11576b79723b7f6e11c55e2012c34c068912c27f6f3a8e33bd55463395"}, {"version": "0ff2f365a1c234102290cefcde87a73e6a38ba07a65caf14705d106adc34babc", "signature": "e78e41093a6df1fe2c9e462f25b79dff5dc8c9458f86b4892b082994fce1d812"}, {"version": "283dcc585d006301ec672b5870de44651b8746c0262c3f472bd3795d2d29da36", "signature": "93da257a6c73fb9e66bbf17c5a6c5a97f77c008c8ede8e9598534cd7e4896ed4"}, {"version": "d64888d9df157d8be7726f97e0ff3b349d1cbd6e8eac4ee245971ffce3999c42", "signature": "e79da6f325b059f0822d2febe3f68fa29b82175ae5bb00b3d40eee948b9af340"}, {"version": "c67fc2eacce1d5ba1c6f27614f1214b017ac80c15da8b2110da02130f629ad80", "signature": "97c2aeb13851c0b62f995d876bd62f5e22fb87a4e6baeb18e2e91cc1358c2c5e"}, {"version": "642756201ddeb1d8f3f79a5609f86b38e2b1070608fa1ff358c4707393780ec6", "signature": "fa50d8eadadbd5d2d572282f6dd7ae6c1f20184958c0ec98f9e2d91e1175cb06"}, {"version": "f6d7e8b043753b1b9b93138f40b0704f1a4e9ae95635c1ea6c0909d7828c6286", "signature": "55486a5e64bc92b90d4478f004f8b374f32d6ff161cecb7c9710924c02bb85dc"}, {"version": "88e7e7d70a332557a6c2884225259b7ac5643085d75c496fe13ecd7e052f3a80", "signature": "d4604e903439f04b5f94d6c682d32920161d987836d7349584512f20bbd46aaa"}, {"version": "3e3464c3f667a66ad2b58e32b440e9e7d08bcdb037471eefc0546215141d562b", "signature": "157973738cfdbb7de62e85daedc5a1733e9efb29a434269cb973d6e4fe39c6f2"}, {"version": "f838218e57b87af3fcc5cee75358866c578e08bb3ad6863d459657d29d2c5473", "signature": "71f0735c1ee06b88f1bf2368e0c38d434e27108525200d22ec7af74306b89b50"}, {"version": "5773c96f0256400861a2eb995b82e135cc808936a0df3e183736112d2b4b6598", "signature": "7b0559d1e37ee561420ac210f99e07fce224e83ef852c63f2cff3529d928c88a"}, {"version": "21634894bc3168fbb0d5ebedcf535899e49bab55dc6f3a63cd0adc486b41456d", "signature": "f6b5537520c772cf955a2c02be0b9114eafd123a801dd7395c9d2937e658f341"}, {"version": "ac48acc2ad2623f292e2921cf2690ac90bfc93d45a12df18d663101111ea3887", "signature": "7968f91fdbb843218d03b493d99330883155a47470fc587cba64a03f219179ba"}, {"version": "503e8a73d12239d64c3628f1f5554d75890731bc48cbb431b232d590b113b8bf", "signature": "60e23ac36f950440ce1a3d79d2d0b8710cf2bb302fd48101ce58f61bb5cc6dcb"}, {"version": "20cbd420705b2de7abe5d9214dd59e474336cbbbf1fb45a973fe551bfe5568c7", "signature": "a2b13abc729bec85de303a7aa644aad5e2e77f78989181d49d0b6f874899d9aa"}, {"version": "10e15c80609004a3ccb40f354a6506ce1a1442ba1af8da585430d730f7157d4c", "signature": "296923634e33cb2af2aa63c6f062c498795fb6002d71e02f6d811c1944411a0e"}, {"version": "9c764a11256feaa2194d8beb7b82a79807f7e2b81aff3dd951cb9caae83ea229", "signature": "c60e4899ea4a6f734f970bd7c9385ccada78cdbdece883af94a70669b54be37c"}, {"version": "e4082876b6d042b6bb02cd9adadbb146cd2ca3d5e2765bd1aae2e4ad1215e77b", "signature": "98e1f3e9c62c8ebce1e460cc70b40383b8dcdb29d6947710aff98a4d60601856"}, {"version": "06ebd9cb8e71f6e7fd64f8c4de8faa43d1e4858ecebc7046e67de7caf44ab7ff", "signature": "3e6149402da8a6876e2460ede67f2c1fe9d888b22542b14365786529d295010d"}, {"version": "295ef711635e6bfc04d7733ad36bf6837136d66dc5aeca3df8654ff63e3a081a", "signature": "93f9ac6d65f109205df61fc4b0147ec961fe577218e4b066aef7c2cbda6dbde2"}, {"version": "8a334801e719317672347f6e6a2c348bf7081968cb6fc168ce83d849d0a1e0cf", "signature": "1edd8e2f2d36ac528e909682d7331874f1f42d019871e539dc80f4afa9f08c4b"}, {"version": "ea49b4da63064ba2cfd7e480271b127f5256484fa36d541801fc929aa3d07ace", "signature": "e713f29ab5270481b07796753fb0a1b693a383b074d3f9ec665c9b45c1f12877"}, {"version": "216fdd04825aca879e8f4644a25b5c81451fbcb9fdfa86b6a1f61a04d7659750", "impliedFormat": 1}, {"version": "271b27c549833361eb5407e3b1acd5f82f6a3588848e6e341b067611d36b41b8", "impliedFormat": 1}, {"version": "3754da0d2705ad634693dd6a72bf1eff715d74661107a4d18611e4413c2c60d7", "impliedFormat": 1}, {"version": "15001c9dd6ad2c0515b48a3b0cd3955f89256f7eb2bb3dd4f0bab899565646f7", "impliedFormat": 1}, {"version": "16644569c814ea007149afbc849ba0dc726887e4baa513156787fbeccc96bb5f", "impliedFormat": 1}, {"version": "6b72cd1871580dee6481d2ebdb0641f380c39e41b2c1f6aedfae86fe021b34a1", "impliedFormat": 1}, {"version": "0693e3c9523391eb333248236f4e4df9a63961d729cda0081302ebf04e4745be", "impliedFormat": 1}, {"version": "8456ecc963bc4816e34b14dba7c5806a674a9305778fedd44bd3fb9f7cd0a278", "impliedFormat": 1}, {"version": "ef79a08ff6dbf02d7aa850d03768dfa7da8d38f1f8f1f70b5554b2eb69e30ef9", "impliedFormat": 1}, {"version": "4b01bf8cb509dd9235289ae0f1dc1d11973eeae5c4e8a6f4f1f7e7a0fbd9981f", "impliedFormat": 1}, {"version": "a6685c650245fc3edf0d01a5306b9741dfb4a10703fbfa73b11ff994e812ce71", "impliedFormat": 1}, {"version": "828e999b464c2a240163f13a50801d8cd2d3f3bb1810f6b1cc51618cde1f5307", "impliedFormat": 1}, {"version": "9f3cf8d45afb6c10da2ac7c5908a35b45942d80af726e11a56614e812c6cb1d9", "impliedFormat": 1}, {"version": "296d4f462ea7a071d145b4d2cbd5171ae1656a2b96e23aa95359c4d3fc1d9956", "impliedFormat": 1}, {"version": "79e52fd0cfd73ed170d509cdedc3eed59fc414527e1d05d455e69d60f825ca66", "impliedFormat": 1}, {"version": "6036e0a9fa044af3b92d7e0daeefdf9f871f362b4170d4e2c99f18ca48dcd967", "impliedFormat": 1}, {"version": "18c93713d0d514633603fe9a8cd44d7fbc90f23a231cd2c9a90aeaa3996837d6", "impliedFormat": 1}, {"version": "48c5cee2757d97d85d2f01d3f29a9268f56eaea28cbbada0e98f948cfcbc7770", "impliedFormat": 1}, {"version": "f0500091ff4e184c40bd50107a5000cb2846e40bfeee3f4bf9604fcc5ac1f764", "impliedFormat": 1}, {"version": "2b4276dde46aa2faf0dd86119999c76b81e6488cd6b0d0fcf9fb985769cd11c0", "impliedFormat": 99}, {"version": "88247402edb737af32da5c7f69ff80e66e831262065b7f0feb32ea8293260d22", "impliedFormat": 99}, {"version": "5ecea63968444d55f7c3cf677cbec9525db9229953b34f06be0386a24b0fffd2", "impliedFormat": 99}, {"version": "b50ee4bde16b52ecb08e2407dca49a5649b38e046e353485335aa024f6efb8ef", "impliedFormat": 99}, {"version": "0eb4089c3ae7e97d85c04dc70d78bac4b1e8ada6e9510f109fe8a86cdb42bb69", "impliedFormat": 99}, {"version": "e9d1d96cc55be993848bcea8598afa6c53bdf774934e22fb6c1749a9762681e4", "impliedFormat": 99}, {"version": "4e2d11861154220b941057210b53821022eb078f52a69bad9c44a0f3f4aaedb9", "impliedFormat": 1}, {"version": "0c9175b5bd2e620bf90a40f4cdd308d533e348a9157dd6f2b8c2d5e181ce77bc", "impliedFormat": 1}, {"version": "67f805fa48e767848d0a127e7c77df1f72e3a29b6a468243df0cfb4b3e0c75a7", "impliedFormat": 1}, {"version": "fca012dddf52eb63eaf6e5959aef233f1904d92eedf1a75f881316a6fc705d85", "impliedFormat": 1}, {"version": "b5b6651229996603456f3714bb6eed6bcf6219ca3e84265c62ba65a682e2e3f6", "signature": "572ddea973770be04ebe74a517a0d3b1d52548993c838bbc6df0c1bef9da2271"}, {"version": "b0ec327b46df9d50c67cb0f3401aba07931d2ff871c16c507eec0f017cd2815f", "signature": "61492b02a4a8d74287dcc8a1b1b7eb2ae3b6c9fcace707f7faf09cbdcb2e1d5d"}, {"version": "2cf84edb5635844129afdb601cf5684c5a56400d210751243a681cd04b57c1d9", "impliedFormat": 99}, {"version": "c610cd8509928c67c5e3f9de30905cd1ede08208563cf789ca3dd9ee3a484927", "impliedFormat": 99}, {"version": "414526d9290a176733f3a5eb959383c03b2fcd506978fb5ffc26788f201c970a", "impliedFormat": 99}, {"version": "b526e8dcac876944abce9efd72b5ebc6b789d84870575842be8450c6d3c74c4a", "impliedFormat": 99}, {"version": "65602b6521d79c38b911ab142fa8833b1460878d976c54b63b3cf2f3b86d7c00", "impliedFormat": 99}, {"version": "d0fde7c862376189423d11930ca69a7cad0c017ffdec17c776d0d607ada8b4a3", "impliedFormat": 99}, {"version": "4caa87fd9f69e1e15a1a57349948539b57041970086da342f7bd42ece1353c3a", "impliedFormat": 99}, {"version": "db8ba14996f88e34f4af93b6816944c6ea5d4b703244abc61de67cfe7f488ce5", "impliedFormat": 99}, {"version": "a3a51b4200f61ddf427f81fc42cb11936911d53714ad9a8b2677d32a548aad3e", "impliedFormat": 99}, {"version": "81171f0b7b97b3bf0e8cd9fa599f23c7cd8e43f3c34f0c197b53cb5f4f55a25c", "impliedFormat": 99}, {"version": "f722e6f337828933c52512cae32a8d9c9bb3e8409fbd39b4ab556d9f2e629b30", "impliedFormat": 99}, {"version": "c9cce0fdbf1e23604904ca1a552ab26492aaf119f351775f0b6eb451301410fc", "impliedFormat": 99}, {"version": "8f56bab88834bb5ff5d14063c0c7bcebebb9cab6893749605ea2ab0f8d0a879b", "impliedFormat": 99}, {"version": "74690a0a01465cec515784e0a9059d286276148cc62208a4eb85566b6890e962", "impliedFormat": 99}, {"version": "afd4f7197d02aeeb6bf1107176f99c0f1d6559cadbbec5c71c2b95f89e177912", "impliedFormat": 99}, {"version": "619d880e788c5066831a64d18108a59acc6a5c06b2331fa0472c9480154d8746", "impliedFormat": 99}, {"version": "ff0824d9a6582f789ced75948e309ad517a2b7aba097e0cc3cf8b7555dd5c790", "impliedFormat": 99}, {"version": "a3d4e893a96bf59fcda0d99da5fe737e807f8d1e4226418fb94c547bdc441026", "impliedFormat": 99}, {"version": "b5c09e3d2f3887fe27b1824c9106ab5e5c6ba50bd67e91fd68139445e730df35", "impliedFormat": 99}, {"version": "21cafd7a40b56b799977e4c31dba190ecfe6bb1e5d6b56b0ee346194c7773924", "impliedFormat": 99}, {"version": "294c0200eb9f9f0b08f8c70c2c4e5d6fd8bf0d0ba19e850d147f723d7a33501a", "impliedFormat": 99}, {"version": "b386e7b1fa1dca4a5ce1cb4ba97cf7288da377bddc7a0da1b3099c2cbe071067", "impliedFormat": 99}, {"version": "e5c813d1eda908a823a49b560fb85aacb5e1c867132bf3758571128baba3ebee", "impliedFormat": 99}, {"version": "914b10e122c91947fe38a9b88ab2902d1df81c1dd49ecc425a33afdf6b6b2351", "impliedFormat": 99}, {"version": "7d32271e3f625963b2ef86fe40c6963b1d44f293ea67548ce81b15f5fb1a2abb", "signature": "7c6d60867e435ab13a07bce4b481a6bad5ddd63de6d04551490664a9e09cacf4"}, {"version": "31f0968b59339aee8ebe41d7c1138685d54d2faa5c50b2faef1b20b41e840516", "signature": "700ffb55353d289c50c8a719d5fc542aa39a54e9edecb843ff6bb0341041368a"}, {"version": "77ec0e3153aec0e986de11665495937c179206e0aafdfd3c6d3db81fc8376fb3", "signature": "76f9540741366d66c4bd8a74091682a1c0d98e3ec39ded6d7a843863b89eb9a1"}, {"version": "8e52e24e0729abdfd7a37af5ee8e7a5d93a19a079c9745982833ff913ae9832b", "signature": "4c86a3e7600e1085aadef698beea1c948a19f0a6e66129f0a3d78c6df7677e5f"}, {"version": "c9a1afdad31595447e413780647f67a566212634ca94338c64a97964f53e3abc", "signature": "d22ea2e723034b2a82e87c62b639271740bdb536425d1d598a7ab87c180d9e9d"}, {"version": "6bb46f4ea85963aba46a29e1dba54295a21008ff14a982b2bbb4375a0f35a4eb", "signature": "93b04ac3c8c3792253b4592b189df67de73e0116af38bd2b644615b061e6a276"}, {"version": "4cbcaae7062115a3e146dd54fcc6d049cc39b47e5de8f2bdb9cf693ff0a0900e", "signature": "7a5c5f291d913e6dbb35edcbfb532b7b70db76263b616ab6781f8d43501485e5"}, {"version": "ceabdb9bdc29a4f659b658cb1186b4bfe997a1f984f84818a78aa821088bdaef", "signature": "dd2d50a1de95abf849646e3cb179239c0bb16ecd0bfac442c6ca498cd1cd5159"}, {"version": "3f6044035be914dcace68eae2c191c23f6f30d55768d22bc874f62104028ed69", "signature": "284b3b5a633a7d0ebb2d3033819c54e459bfd98fe5300ab0c732270f2c2481b2"}, {"version": "02947c967814a0158409cc3c87fc79bfc4045e2c91f7a79f4acc623f325ea9b5", "signature": "c81eb7fc2efca774a739426dae7e492b4eb3ac80d0fb67a3e8647ff99859909c"}, {"version": "aa3bcc8a5ef9a9bf1c09640dfb0b72f4ccfe3cd13e36cb6685e308b466b152a3", "signature": "b224bc69fea2186da9754f798b19ddc7e14a9fdcd35ab5e29d30bd0b0504e023"}, {"version": "2f4b26f04d0f3121b6872a3327866837b10eb07d303967043c52523a6a796e8f", "signature": "8a87d756ff16918e9e37fb6b283120e646b5c675478b940d133dd9e617839415"}, {"version": "87532452d91a09a29571aba8304536ca8ceb5538b4275de7b9f37ed84eb492cc", "signature": "df5e225d0cda33d894f8fcd8c2df56f03c733591dad7ef68658e2e0b0806b0c8"}, {"version": "6b3c8ea2af057486b8569ed816531b1a55e34f8904f40db321f19e6d69561790", "signature": "94c57726f1ebaa9713b01dfaf1f22bf41d846f18e018083c8cfbcb033782eca2"}, {"version": "48b7adf01458439839644743f1c15127086c716070591d3ed5590351ee9376f4", "signature": "df8204c9afb93e23e15cada73fb04a547c01dc74e66362252a1ec6134251d9ca"}, {"version": "adc2486e266e6bbf55cd3975613ff589003765f1fb9a3f50b8768f63b2b1f0a4", "signature": "8553e84bd6d69e29e0dfb63402f8bb4f8684fa2c55f89529285b2754c7a05e2f"}, {"version": "a4d0f7445ce223da24e0a10a101184446a555f1f25e03116aa2c3ac9bca4fa1c", "signature": "5f05ef9bd22e33a4208c467d638a89386935d221bdda8462535a5118141eb928"}, {"version": "b8f44385b54545d0f4ba50295fc2381879882a0181bc6d9983b41e1be762c070", "signature": "82f4f53461eb0e8d1ceee3298f44b69393914f13e104f0e00e3fea807155cf42"}, {"version": "341384537af44568758e76569ded1ee8472aa586ee9f28e47f08d9ac77febfc1", "signature": "69a10489cffaf08b7ef6b62bf486eebccf90957e5d03267628319dbfebb8de32"}, {"version": "4c54fe0b19718bc972c41fc06b3929fadd108b1aeb05754bae9f96151c9db0a7", "signature": "cd897b02c25f473102a3e258e5dc98af9b8e9c11394c675f44cbd6cdf913d291"}, {"version": "42423dab13c713ef56aca10cd6f7badbfccd0198461d439d001b2649c2b5c3c7", "signature": "63e8b17d7945642b18a80e9e67f1e4b266e59f5aa38b8212fbef06b2a254185b"}, {"version": "d91d18e651bf03f2fd87b9aeb5ed49fa1857315b78923ad48936a791db000466", "signature": "4446446f7207bed54b7cd583414f111f57ef96b938575d0bf3392264e66fa1cc"}, {"version": "a95be6e00489e13c11f7de2f53eb60846fa398db34ab5b827e314fbfb435e1d7", "signature": "ffb3f60b6caff7542d0bfda418f0c8988cb4c2edd936837a37fa6138e0e43a63"}, {"version": "f82fcd1752fc28fb02fba70f119727e84eb63390cb507baf626a37731c484b6c", "signature": "5adff7ef41d957f4d3eef014170018d563ecd94cc6aab218bc50b81db5407b7d"}, {"version": "02179f1ef0ba91bde4ce5e6be4a8b23df58eb277c3b89ea110fdc9e673144bab", "signature": "513c8ff745a59b3533ea26ed94388e2eaf94e722f392b3836737158a40b3af9b"}, {"version": "14684025aee18e17a807e05e84bd36699818d2535b7d56993bcad548c035c040", "signature": "3a67d6c19f4f43ac38f789b3b363dd6be98bc0c7bc28e58500fb929b5ea9a075"}, {"version": "c9ab61b5b0fd3310bb8186b75144645066f36680053f82c6e2394b9b75cc599f", "signature": "0ec4a02bebcc1fa9fa3a1ba1adc1d418e75c5d658c3300c04d106cfae47159d3"}, {"version": "a39e51bd6074accded3391f86c0b5a3e052d92050d2cff0a771d740c5b8a35aa", "signature": "5138fe8351839c96de89b8c6e940417526c5f26c049853a6032d2f68ecdd7e8c"}, {"version": "d0ddf92e6d17b8cd175ee60adc650bca78f3c43a1b7367d61a168be373a10ca3", "signature": "04a915f0f6efc8073dd37d01d889a61b742f9f5c27f2e4e54ac92aeecd97005f"}, {"version": "5216b0f596d0701c2e4def454e38caf194ab36c8c4f75d0c2f6f86510b66a248", "signature": "47ba833a4fd4e712eb41b1d12a32c8e341b01652e44df31487a0c995614d818e"}, {"version": "11832634c05ab2226581309dc9caa9af39d2476f1a53900dff6bf4a5098cb725", "signature": "ed5cbdbdce74c88caa2199416f1bfd08619a4da0364d0864a615a6dce23a90ec"}, {"version": "ab4a015837612083d6452e2d56a9f558c26e2e41624f6fec528292a2a100ecee", "signature": "f6b5537520c772cf955a2c02be0b9114eafd123a801dd7395c9d2937e658f341"}, {"version": "c2604cb28c0d1b5e6c029d224fcfb8244b7ddef79f6368f0f399bc8e165ae861", "signature": "a7e748487a0dc58095ee592386c1f8b4fa4df605e41256c2637a287a09c40fb4"}, {"version": "c0787c1717649af6cf76a7b170b870de6f89beebfb6e3de3b41e4a28bc99421d", "signature": "07193700c70d421a1e8c56aadf5cee695b66d522491debefddb17c853e86a341"}, {"version": "fc9f265c7748a44f2d09a8ae51849d1425a5c8a33d5c0535cee6add7df498c46", "signature": "ce8a24380a6f74fbb778f017cb37d492348379ccbeedba764fbeffebb3304f22"}, "c3085aa7c267e4a12c37d1330a7a4a29c38f8626db50456302ec5a79c215f656", {"version": "1e6174882899ff53f13b5300f4c0abbccc632566edc18afc0fff7a18a976bbc8", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "5ef2a5433e41633c6b938af1c2caca2ef66c18a2c1c80c378c75a7450ef03843", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "ad8713c2dc63f53cb2956b57ef1af09bf052cd8e01f95cadbac233382a077ee3", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "fe03f51a1fdf1ba2371f7567651dd8b4fd4ffa5a5ec6ca5c5594aa5cae743c91", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "e54fc16ea1efd92722857c0498d2cfd9036d199d2a9ca20b2e28943ab58c69c6", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "117b4f3b60067975062dff86a802b6739ee33ed9a32b270ccfc2cc2e3a13b5e6", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "d27c62ffae04e57b5d87f2dc877aba55d1c5d32cbd7a92b7422cfa14c3dc6248", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "e181fd5f5b6f089dd98bc3a2f951ecc01e7924f3652d187d4d6a8fa2c760465a", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "1c3b247ba5c493208847762b89c8d95c59662162b106f161390e3352cf5957ae", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "256c59e7a948d7a2b77d5d62d29cbba17f3720b1d1738794af479569894e6d98", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "28a76e54581020cf30ae8a38796fa2934b1c101c59ab4be1f9e1ed3f3b68cd5f", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "3fdda783007ee157462263162dfffaf3b4256001c8ade99d81fc4d381b98379d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "d2c0d2c4171cef83eafc8c0f83d391c97697ecd5b07a792fe8b2dd16bb1544f8", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "a5959243955b43b27616b55c2f93150557e7b6478d9b07a0ba25fac9e0418222", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "d60acbe3f29bb9c09681f5c15108a36e5f4c29e7460d9bf560b4e741315e7819", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "f4e9f63e2c07cef3e28698cd0210df13688224b9ea4d92c819c73897e03bc28a", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "67a650f6ca90ab44eb2397271221912ee3f1e34bef1b713bbd831657bd03bda5", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "1539e2ece569f7b02c9eaabed31d87b62fcdc42d41ddd19a4777511610e05c15", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "ce66b73fcc77639af49019b0751585146c769ca8a050ad49b88167aabda87554", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "b5622e61b0785accaff2cde0d198ea561bb19d9dec6c7a5f8aac414542d0c95a", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "67988b08f8c9f98d9f9fc7c9466af5612f4be0ac5b2a78db65292c41c39deb95", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "959fcbae93718b7b116c59502abadacc8daeeedafc5c436f8c9c2dc79cb2eddf", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "6b5fe07d4e5966ca8ae53a239dd587b7ecfc54684c01c788d9477d7b6007f85d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "58c3e0e184c43256830974eb5616ba1f37cc9cdee670437594c870a22082c963", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "a9b962f2f393236be49324114e77cd5f0a2d6d468162265054dbae61b2790a4e", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "bee3e79724370e9f91d0fcbbba0f58f3ccc9528a2fb8c9198f00c1426438ee72", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "e5bec34da06736dfc69663b5a7f0d49e0f802228c6073aa6abe3c95b68664d08", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "734a1100be6d486b46c135c69f780294da4a0dc8095090819f073eff7bb7e9cd", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "22fd56488ce4dee12994bfd2db70250c0b5d6b910c9c2a7eace70652b121137d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "c0746788aa124c2f4da971dfda0240ddbf401defd28fc21e2c32281b4253e964", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "0101a10677729cc035e7b109063b3337423362bd0eb0bcdc25b4859ac6d371d2", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "febd99f858fac347ab5ede9b6222c235b8a1efa1ba7e67631d32357ff6eb101e", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "29af01546501a1a36c11469e9865b6bed8aaee4d7fe44df0fdd283c53a1fadc6", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "6f50b0a26ab3f81d40b2fa803375a59c31bb0635d3ea50295304e458a3254e98", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "c4a49018fda7591c71881a17dcecda8785a0bee00c5fec891cc8151dd91e8150", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "6261e0b20e5bdfca8026813f001aeed21ccae9b9eb795caedb9bbcc326fc86c3", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "2b06fd74bd2a5b87ff811f02b18ad8086161d3dae0954bd3d73a4d37c0b303f6", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "046d167d6ed75ce342e3b862910957d5d45be37deb1e838ed3cb42df749d5a0b", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "979df447af2eccaa5fa11d9c10bfde3177de59365fb67ba6c86b7c7c31f90a1a", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "632acef013c0ee7e53ad355c22704bb76c7fc6bc30f83dcf633cd9857b79d637", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "db3f392b658e2716daedd6923bb3b4d885f6cb675aed455f5e56b954f7646a55", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "af65073225b5c08e6b5fbc252608725d6fd37465cc80924e54fbc6c5ca0baec2", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "5da66545be11a35a1823f73ffe43b058e4e63423d60938f30e0cd566fcd907d6", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "9aa5e402cc13acf15e15aed0ccd7f783ef872b7187d718e2a461bcfb37f5915d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "d73f456b24386d32f89a76acc8a14568b80bf20aceb06e2dc0afc23ad9d37086", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "9474313b901051689e8d16e91c903410d0edd1d109f0d8bf68f8bc09c0922bb7", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "1e35b0a39b3ade8927844ec59c445d2432778f510a8b602efe562c541cdb9800", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "904346223ae037796494dd8d7371db1ca07f0bdc2bd314d3895496e2c5832c06", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "14d5bc759c50f3a8d821718aaeb43a742834ffec41ffaf9c3f8132fb90e8c804", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "dd00fa70fdbaf9be57f0254dd1c858451be9dc0b13adf8a0d902bca44a194e16", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "f61558ce04a94b606372b1e1f60287118ce825ab01fe9f6ae76e0ebc3e621578", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "2a7fbf7ff589fc4d1326c5d4decf3247635d4cf934a76ccb4720787105073e77", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "89522b7b09b35d59b67b42743d5230c3b0db15cbf2a701956deabbf58b09b9af", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "7c302adfdaca9ada9f8b1ddeb6540b36ecc565393a345019bd4c78b4c5b954df", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "f6fc7cde8085a529b713ae87b6067e5a663084ad002f0de29f25f7a483fb026d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "28492ea14e98936c1141019a29e00dc14b04c67eb1cb76a5e748407b1af9db53", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "e07a465598549a55cade20488427a3085b328e616504bcfc47741847ba68258d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "0924387eee1569487cf49b39ec2fe9f4c785552057be5c14e3e116bc70f08a04", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "5e04e3ba5ecf3377fd9d774b81c1036de6439d749d025f8f93bd2f815044981c", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "1afbef9775d2beee0217f52d65251909f8f44d36bf2dc455ececacb67266349b", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "eeb6271805ca0cb121a49d0395ae84a3a9ea329c147def229bca5fa7db396e34", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "b3bf94fb5ee09413f341694a4e41af68362ca17240077f359a5452104ce352f5", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "fa572ec0b96a2ed3ed986780a978ccf44b2e7b1f0160bdaa89b23f1dc49ac161", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "371affa9d815eab92b41d11fc37ddb48a4d283fcf312165d6d79508a1def063c", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "870d3a4f8e8fc0e6aab03ea359e980429f91353abbdad6a1f9e3234b33ba716f", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "994c0343dfe1372efd98730346e6871fb7efcfc14e776dc1a726e1e3de2dcd8a", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "c2d4127c712316f475f4b985668fd0211587b25fabee53fa0c26cb9f13853db1", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "5d5fe619444ae15a175eaf168e368d558f8e5b47c63c6327e6b4dbe6636f8f5d", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "08f2eae14b9689d54b9ec100e70014bb0c137e52bd05cff429a09804db3504fa", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "62ffe1c05e6f728feaaecde1766a9a5794034145d5704423ac5a0534dae3e607", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "08818bbc001616116badaa5779a964e34e0c6d4f4b0b13df0ad7fc4474a5b9d8", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "b37cda2498cd0b3a88a0bc21f3e84ca605407d2d77dd9e6ddcbb3e98145c36d9", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "6f820cd217c5297aff1f66274eefa2d5bb34274787ef27e517d9164b113d5036", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "b90de94e71f898be2a2480cbbec5adf07e231c79c2808aa73073ffa40a022553", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "ec5c79711f621c9eb5615454a0ca5d1eddc07febe6554c92d1182f4ef2776855", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "54579d38152cb5e65e066939dcdd0521f4a404ff7563c76690d84c8ca42fb6ac", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "8ba80928b81d71cab2dc51c347afd74fde197633dd7a62a17adb65772cee46e0", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "59d437da257a0edb2a313d0defaab4ac3a585e462ccab23030b66326bee2d475", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "74bf63c62b48d490800f96f9e3c7c0c5d7fd300fd6b9d07ca065ed02e1bc4a62", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "6516f0401250f2a6d368ce83836e89b9cdd80336ba90843780755ddcf9b59090", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "de9e937db7529a796bd9822a1cb462e2ba352b659e3dbb6bb92a538cc3f48b58", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "9d3582ad620e33b49c5fdd93f31e64cad04a911505385cd5f5f24a7153eb508c", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "53b085b5f3c4d1385e6598a91e48d77de4db4004330d495e42adf4d95b7d942f", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "15f7dad0e29800ec85cc48be4859405465554dfddb9017f8be8ab4b0ecc00dbe", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "00a8f100db376a644f5efcc4380e6ecf79197b0bda86c98865a6b78aff8a6ca7", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "27c85cda037102b615fc3fb410f3dc02ec7433ef1da3ee2fa2513afffce65b6a", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "bd88e0cf0423ec6476fb58b1d21c8945a508e66a72cd0d4195afe790c450cadf", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "7f87dfc5938d52824736dcfff41bc359ab2285c1ec6f4fc40ce6660058e65b29", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "596edd5fd3c1902baae8ba75405c906e08070c63d57b9cddc53681c4ee4056e6", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "0081f7c9f4cff33fe460fb4fc0af5e34a74edc4f9e24b3fcd3f8a8df9062dee8", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "5960619f36567df529274dbd9e5578fac7a3f9110369157ae9d5bed4dda3ac4e", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "f51968bb00e2719e07a7097ec088e67c74e02687cc7adc76ea295fa16c555b06", "signature": "89b0f68f8f0b901f9dfff2b9e7255520283a783d6af7f2bc2953d771232317a2"}, {"version": "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "impliedFormat": 1}, {"version": "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "impliedFormat": 1}, {"version": "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "impliedFormat": 1}, {"version": "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "impliedFormat": 1}, {"version": "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "impliedFormat": 1}, {"version": "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "impliedFormat": 1}, {"version": "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "impliedFormat": 1}, {"version": "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", "impliedFormat": 1}, {"version": "e2b48abff5a8adc6bb1cd13a702b9ef05e6045a98e7cfa95a8779b53b6d0e69d", "impliedFormat": 1}, {"version": "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "impliedFormat": 1}, {"version": "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "impliedFormat": 1}, {"version": "baac9896d29bcc55391d769e408ff400d61273d832dd500f21de766205255acb", "impliedFormat": 1}, {"version": "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", "impliedFormat": 1}, {"version": "86ea91bfa7fef1eeb958056f30f1db4e0680bc9b5132e5e9d6e9cfd773c0c4fd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "impliedFormat": 1}, {"version": "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", "impliedFormat": 1}, {"version": "d26a79f97f25eb1c5fc36a8552e4decc7ad11104a016d31b1307c3afaf48feb1", "impliedFormat": 1}, {"version": "afe73051ff6a03a9565cbd8ebb0e956ee3df5e913ad5c1ded64218aabfa3dcb5", "impliedFormat": 1}, {"version": "035a5df183489c2e22f3cf59fc1ed2b043d27f357eecc0eb8d8e840059d44245", "impliedFormat": 1}, {"version": "a4809f4d92317535e6b22b01019437030077a76fec1d93b9881c9ed4738fcc54", "impliedFormat": 1}, {"version": "5f53fa0bd22096d2a78533f94e02c899143b8f0f9891a46965294ee8b91a9434", "impliedFormat": 1}, {"version": "f3d8c757e148ad968f0d98697987db363070abada5f503da3c06aefd9d4248c1", "impliedFormat": 1}, {"version": "96d14f21b7652903852eef49379d04dbda28c16ed36468f8c9fa08f7c14c9538", "impliedFormat": 1}, {"version": "0e60e0cbf2283adfd5a15430ae548cd2f662d581b5da6ecd98220203e7067c70", "impliedFormat": 1}, {"version": "d57be402cf1a3f1bd1852fc71b31ff54da497f64dcdcf8af9ad32435e3f32c1f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "736097ddbb2903bef918bb3b5811ef1c9c5656f2a73bd39b22a91b9cc2525e50", "impliedFormat": 1}, {"version": "4340936f4e937c452ae783514e7c7bbb7fc06d0c97993ff4865370d0962bb9cf", "impliedFormat": 1}, {"version": "b70c7ea83a7d0de17a791d9b5283f664033a96362c42cc4d2b2e0bdaa65ef7d1", "impliedFormat": 1}, {"version": "4cf58cd73f135e59d2268b4b792623bd8cc7ea887d96498f2a64d550beb930bb", "impliedFormat": 1}, {"version": "ab82804a14454734010dcdcd43f564ff7b0389bee4c5692eec76ff5b30d4cf66", "impliedFormat": 1}, {"version": "15fe687c59d62741b4494d5e623d497d55eb38966ecf5bea7f36e48fc3fbe15e", "impliedFormat": 1}, {"version": "2c3b8be03577c98530ef9cb1a76e2c812636a871f367e9edf4c5f3ce702b77f8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "bae8d023ef6b23df7da26f51cea44321f95817c190342a36882e93b80d07a960", "impliedFormat": 1}, {"version": "26a770cec4bd2e7dbba95c6e536390fffe83c6268b78974a93727903b515c4e7", "impliedFormat": 1}], "root": [475, 526, 527, [534, 538], 556, 557, [560, 585], 685, [718, 745], [749, 754], [758, 765], 767, [771, 775], 785, [1140, 1148], [1154, 1196], 1226, 1227, [1252, 1379]], "options": {"allowJs": true, "esModuleInterop": true, "jsx": 1, "module": 99, "skipLibCheck": true, "strict": true, "target": 4}, "referencedMap": [[1289, 1], [1291, 2], [1290, 3], [1292, 4], [1293, 5], [1295, 6], [1296, 7], [1294, 8], [1297, 9], [1298, 10], [1299, 11], [1302, 12], [1301, 13], [1303, 14], [1304, 15], [1300, 16], [1307, 17], [1306, 18], [1308, 19], [1309, 20], [1305, 21], [1311, 22], [1310, 23], [1312, 24], [1313, 25], [1315, 26], [1316, 27], [1314, 28], [1317, 29], [1318, 30], [1319, 31], [1320, 32], [1321, 33], [1322, 34], [1324, 35], [1323, 36], [1326, 37], [1325, 38], [1327, 39], [1328, 40], [1329, 41], [1330, 42], [1331, 43], [1333, 44], [1334, 45], [1332, 46], [1336, 47], [1335, 48], [1337, 49], [1338, 50], [1340, 51], [1339, 52], [1343, 53], [1342, 54], [1341, 55], [1346, 56], [1345, 57], [1344, 58], [1347, 59], [1349, 60], [1350, 61], [1351, 62], [1348, 63], [1352, 64], [1354, 65], [1353, 66], [1355, 67], [1356, 68], [1357, 69], [1358, 70], [1359, 71], [1360, 72], [1361, 73], [1362, 74], [1363, 75], [1364, 76], [1365, 77], [1366, 78], [1367, 79], [1368, 80], [1369, 81], [1370, 82], [1371, 83], [1372, 84], [1374, 85], [1375, 86], [1373, 87], [1376, 88], [1288, 89], [1378, 90], [1377, 91], [1379, 92], [1287, 93], [526, 94], [475, 95], [527, 96], [532, 97], [531, 98], [1382, 99], [1380, 100], [1217, 101], [1219, 102], [1220, 103], [1221, 104], [1216, 100], [1218, 100], [419, 100], [533, 105], [530, 100], [1385, 106], [1381, 99], [1383, 107], [1384, 99], [1387, 108], [1386, 109], [1388, 100], [1393, 110], [1396, 111], [1397, 112], [1394, 100], [1398, 100], [1399, 113], [1400, 114], [1401, 100], [1402, 100], [1403, 100], [1389, 100], [1404, 115], [1406, 100], [1407, 116], [137, 117], [138, 117], [139, 118], [97, 119], [140, 120], [141, 121], [142, 122], [92, 100], [95, 123], [93, 100], [94, 100], [143, 124], [144, 125], [145, 126], [146, 127], [147, 128], [148, 129], [149, 129], [151, 130], [150, 131], [152, 132], [153, 133], [154, 134], [136, 135], [96, 100], [155, 136], [156, 137], [157, 138], [189, 139], [158, 140], [159, 141], [160, 142], [161, 143], [162, 144], [163, 145], [164, 146], [165, 147], [166, 148], [167, 149], [168, 149], [169, 150], [170, 100], [171, 151], [173, 152], [172, 153], [174, 154], [175, 155], [176, 156], [177, 157], [178, 158], [179, 159], [180, 160], [181, 161], [182, 162], [183, 163], [184, 164], [185, 165], [186, 166], [187, 167], [188, 168], [559, 169], [1391, 100], [1408, 100], [1392, 100], [193, 170], [194, 171], [192, 172], [190, 173], [191, 174], [81, 100], [83, 175], [266, 172], [1390, 176], [1395, 177], [1409, 100], [1411, 178], [1410, 100], [1412, 100], [1413, 179], [682, 180], [681, 181], [676, 182], [680, 183], [679, 184], [677, 185], [684, 186], [683, 100], [674, 187], [673, 100], [678, 187], [671, 100], [672, 188], [529, 189], [528, 100], [98, 100], [766, 190], [1149, 100], [82, 100], [874, 191], [853, 192], [950, 100], [854, 193], [790, 191], [791, 191], [792, 191], [793, 191], [794, 191], [795, 191], [796, 191], [797, 191], [798, 191], [799, 191], [800, 191], [801, 191], [802, 191], [803, 191], [804, 191], [805, 191], [806, 191], [807, 191], [786, 100], [808, 191], [809, 191], [810, 100], [811, 191], [812, 191], [814, 191], [813, 191], [815, 191], [816, 191], [817, 191], [818, 191], [819, 191], [820, 191], [821, 191], [822, 191], [823, 191], [824, 191], [825, 191], [826, 191], [827, 191], [828, 191], [829, 191], [830, 191], [831, 191], [832, 191], [833, 191], [835, 191], [836, 191], [837, 191], [834, 191], [838, 191], [839, 191], [840, 191], [841, 191], [842, 191], [843, 191], [844, 191], [845, 191], [846, 191], [847, 191], [848, 191], [849, 191], [850, 191], [851, 191], [852, 191], [855, 194], [856, 191], [857, 191], [858, 195], [859, 196], [860, 191], [861, 191], [862, 191], [863, 191], [866, 191], [864, 191], [865, 191], [788, 100], [867, 191], [868, 191], [869, 191], [870, 191], [871, 191], [872, 191], [873, 191], [875, 197], [876, 191], [877, 191], [878, 191], [880, 191], [879, 191], [881, 191], [882, 191], [883, 191], [884, 191], [885, 191], [886, 191], [887, 191], [888, 191], [889, 191], [890, 191], [892, 191], [891, 191], [893, 191], [894, 100], [895, 100], [896, 100], [1043, 198], [897, 191], [898, 191], [899, 191], [900, 191], [901, 191], [902, 191], [903, 100], [904, 191], [905, 100], [906, 191], [907, 191], [908, 191], [909, 191], [910, 191], [911, 191], [912, 191], [913, 191], [914, 191], [915, 191], [916, 191], [917, 191], [918, 191], [919, 191], [920, 191], [921, 191], [922, 191], [923, 191], [924, 191], [925, 191], [926, 191], [927, 191], [928, 191], [929, 191], [930, 191], [931, 191], [932, 191], [933, 191], [934, 191], [935, 191], [936, 191], [937, 191], [938, 100], [939, 191], [940, 191], [941, 191], [942, 191], [943, 191], [944, 191], [945, 191], [946, 191], [947, 191], [948, 191], [949, 191], [951, 199], [1139, 200], [1044, 193], [1046, 193], [1047, 193], [1048, 193], [1049, 193], [1050, 193], [1045, 193], [1051, 193], [1053, 193], [1052, 193], [1054, 193], [1055, 193], [1056, 193], [1057, 193], [1058, 193], [1059, 193], [1060, 193], [1061, 193], [1063, 193], [1062, 193], [1064, 193], [1065, 193], [1066, 193], [1067, 193], [1068, 193], [1069, 193], [1070, 193], [1071, 193], [1072, 193], [1073, 193], [1074, 193], [1075, 193], [1076, 193], [1077, 193], [1078, 193], [1080, 193], [1081, 193], [1079, 193], [1082, 193], [1083, 193], [1084, 193], [1085, 193], [1086, 193], [1087, 193], [1088, 193], [1089, 193], [1090, 193], [1091, 193], [1092, 193], [1093, 193], [1095, 193], [1094, 193], [1097, 193], [1096, 193], [1098, 193], [1099, 193], [1100, 193], [1101, 193], [1102, 193], [1103, 193], [1104, 193], [1105, 193], [1106, 193], [1107, 193], [1108, 193], [1109, 193], [1110, 193], [1112, 193], [1111, 193], [1113, 193], [1114, 193], [1115, 193], [1117, 193], [1116, 193], [1118, 193], [1119, 193], [1120, 193], [1121, 193], [1122, 193], [1123, 193], [1125, 193], [1124, 193], [1126, 193], [1127, 193], [1128, 193], [1129, 193], [1130, 193], [787, 191], [1131, 193], [1132, 193], [1134, 193], [1133, 193], [1135, 193], [1136, 193], [1137, 193], [1138, 193], [952, 191], [953, 191], [954, 100], [955, 100], [956, 100], [957, 191], [958, 100], [959, 100], [960, 100], [961, 100], [962, 100], [963, 191], [964, 191], [965, 191], [966, 191], [967, 191], [968, 191], [969, 191], [970, 191], [975, 201], [973, 202], [974, 203], [972, 204], [971, 191], [976, 191], [977, 191], [978, 191], [979, 191], [980, 191], [981, 191], [982, 191], [983, 191], [984, 191], [985, 191], [986, 100], [987, 100], [988, 191], [989, 191], [990, 100], [991, 100], [992, 100], [993, 191], [994, 191], [995, 191], [996, 191], [997, 197], [998, 191], [999, 191], [1000, 191], [1001, 191], [1002, 191], [1003, 191], [1004, 191], [1005, 191], [1006, 191], [1007, 191], [1008, 191], [1009, 191], [1010, 191], [1011, 191], [1012, 191], [1013, 191], [1014, 191], [1015, 191], [1016, 191], [1017, 191], [1018, 191], [1019, 191], [1020, 191], [1021, 191], [1022, 191], [1023, 191], [1024, 191], [1025, 191], [1026, 191], [1027, 191], [1028, 191], [1029, 191], [1030, 191], [1031, 191], [1032, 191], [1033, 191], [1034, 191], [1035, 191], [1036, 191], [1037, 191], [1038, 191], [789, 205], [1039, 100], [1040, 100], [1041, 100], [1042, 100], [1229, 206], [1230, 207], [1228, 100], [1405, 208], [778, 209], [779, 210], [747, 211], [509, 212], [478, 213], [488, 213], [479, 213], [489, 213], [480, 213], [481, 213], [496, 213], [495, 213], [497, 213], [498, 213], [490, 213], [482, 213], [491, 213], [483, 213], [492, 213], [484, 213], [486, 213], [494, 214], [487, 213], [493, 214], [499, 214], [485, 213], [500, 213], [505, 213], [506, 213], [501, 213], [477, 100], [507, 100], [503, 213], [502, 213], [504, 213], [508, 213], [746, 100], [777, 215], [776, 100], [476, 216], [755, 217], [515, 218], [514, 219], [521, 220], [523, 221], [519, 222], [518, 223], [525, 224], [522, 219], [524, 225], [516, 226], [513, 227], [517, 228], [511, 100], [512, 229], [757, 230], [756, 231], [520, 100], [90, 232], [422, 233], [427, 93], [429, 234], [215, 235], [370, 236], [397, 237], [226, 100], [207, 100], [213, 100], [359, 238], [294, 239], [214, 100], [360, 240], [399, 241], [400, 242], [347, 243], [356, 244], [264, 245], [364, 246], [365, 247], [363, 248], [362, 100], [361, 249], [398, 250], [216, 251], [301, 100], [302, 252], [211, 100], [227, 253], [217, 254], [239, 253], [270, 253], [200, 253], [369, 255], [379, 100], [206, 100], [325, 256], [326, 257], [320, 258], [450, 100], [328, 100], [329, 258], [321, 259], [341, 172], [455, 260], [454, 261], [449, 100], [267, 262], [402, 100], [355, 263], [354, 100], [448, 264], [322, 172], [242, 265], [240, 266], [451, 100], [453, 267], [452, 100], [241, 268], [443, 269], [446, 270], [251, 271], [250, 272], [249, 273], [458, 172], [248, 274], [289, 100], [461, 100], [769, 275], [768, 100], [464, 100], [463, 172], [465, 276], [196, 100], [366, 277], [367, 278], [368, 279], [391, 100], [205, 280], [195, 100], [198, 281], [340, 282], [339, 283], [330, 100], [331, 100], [338, 100], [333, 100], [336, 284], [332, 100], [334, 285], [337, 286], [335, 285], [212, 100], [203, 100], [204, 253], [421, 287], [430, 288], [434, 289], [373, 290], [372, 100], [285, 100], [466, 291], [382, 292], [323, 293], [324, 294], [317, 295], [307, 100], [315, 100], [316, 296], [345, 297], [308, 298], [346, 299], [343, 300], [342, 100], [344, 100], [298, 301], [374, 302], [375, 303], [309, 304], [313, 305], [305, 306], [351, 307], [381, 308], [384, 309], [287, 310], [201, 311], [380, 312], [197, 237], [403, 100], [404, 313], [415, 314], [401, 100], [414, 315], [91, 100], [389, 316], [273, 100], [303, 317], [385, 100], [202, 100], [234, 100], [413, 318], [210, 100], [276, 319], [312, 320], [371, 321], [311, 100], [412, 100], [406, 322], [407, 323], [208, 100], [409, 324], [410, 325], [392, 100], [411, 311], [232, 326], [390, 327], [416, 328], [219, 100], [222, 100], [220, 100], [224, 100], [221, 100], [223, 100], [225, 329], [218, 100], [279, 330], [278, 100], [284, 331], [280, 332], [283, 333], [282, 333], [286, 331], [281, 332], [238, 334], [268, 335], [378, 336], [468, 100], [438, 337], [440, 338], [310, 100], [439, 339], [376, 302], [467, 340], [327, 302], [209, 100], [269, 341], [235, 342], [236, 343], [237, 344], [233, 345], [350, 345], [245, 345], [271, 346], [246, 346], [229, 347], [228, 100], [277, 348], [275, 349], [274, 350], [272, 351], [377, 352], [349, 353], [348, 354], [319, 355], [358, 356], [357, 357], [353, 358], [263, 359], [265, 360], [262, 361], [230, 362], [297, 100], [426, 100], [296, 363], [352, 100], [288, 364], [306, 277], [304, 365], [290, 366], [292, 367], [462, 100], [291, 368], [293, 368], [424, 100], [423, 100], [425, 100], [460, 100], [295, 369], [260, 172], [89, 100], [243, 370], [252, 100], [300, 371], [231, 100], [432, 172], [442, 372], [259, 172], [436, 258], [258, 373], [418, 374], [257, 372], [199, 100], [444, 375], [255, 172], [256, 172], [247, 100], [299, 100], [254, 376], [253, 377], [244, 378], [314, 148], [383, 148], [408, 100], [387, 379], [386, 100], [428, 100], [261, 172], [318, 172], [420, 380], [84, 172], [87, 381], [88, 382], [85, 172], [86, 100], [405, 383], [396, 384], [395, 100], [394, 385], [393, 100], [417, 386], [431, 387], [433, 388], [435, 389], [770, 390], [437, 391], [441, 392], [474, 393], [445, 393], [473, 394], [447, 395], [456, 396], [457, 397], [459, 398], [469, 399], [472, 280], [471, 100], [470, 169], [667, 400], [668, 401], [669, 100], [670, 402], [666, 403], [665, 404], [663, 404], [664, 405], [675, 181], [587, 100], [593, 406], [586, 100], [590, 100], [592, 407], [589, 408], [662, 409], [656, 409], [617, 410], [613, 411], [628, 412], [618, 413], [625, 414], [612, 415], [626, 100], [624, 416], [621, 417], [622, 418], [619, 419], [627, 420], [594, 408], [657, 421], [608, 422], [605, 423], [606, 424], [607, 425], [596, 426], [615, 427], [634, 428], [630, 429], [629, 430], [633, 431], [631, 432], [632, 432], [609, 433], [611, 434], [610, 435], [614, 436], [658, 437], [616, 438], [598, 439], [659, 440], [597, 441], [660, 442], [599, 443], [637, 444], [635, 432], [636, 445], [600, 432], [641, 446], [639, 447], [640, 448], [601, 449], [644, 450], [643, 451], [646, 452], [645, 453], [649, 454], [647, 453], [648, 455], [642, 456], [638, 457], [650, 456], [602, 432], [661, 458], [603, 453], [604, 432], [620, 459], [623, 460], [595, 100], [651, 432], [652, 461], [654, 462], [653, 463], [655, 464], [588, 465], [591, 466], [510, 467], [707, 468], [701, 100], [704, 469], [698, 100], [708, 100], [709, 470], [710, 100], [692, 471], [694, 472], [693, 100], [713, 473], [689, 474], [690, 475], [691, 471], [695, 476], [696, 476], [697, 471], [688, 100], [687, 475], [702, 100], [700, 100], [711, 100], [714, 477], [715, 100], [712, 100], [716, 478], [717, 479], [703, 480], [686, 100], [705, 477], [706, 481], [699, 100], [1151, 482], [1153, 483], [1150, 484], [1152, 482], [1212, 485], [1224, 172], [1213, 172], [1211, 172], [1197, 486], [1199, 487], [1225, 488], [1198, 172], [1202, 489], [1204, 490], [1203, 172], [1206, 491], [1205, 487], [1223, 492], [1214, 172], [1215, 172], [1207, 487], [1201, 493], [1200, 172], [1222, 494], [1208, 487], [1210, 495], [1209, 172], [1248, 100], [1251, 496], [1249, 100], [1250, 100], [1231, 497], [1232, 497], [1237, 498], [1238, 499], [1246, 500], [1239, 501], [1241, 502], [1240, 503], [1242, 504], [1243, 505], [1245, 506], [1244, 503], [1247, 507], [1234, 508], [1236, 509], [1235, 504], [1233, 100], [748, 510], [784, 511], [781, 172], [782, 172], [780, 100], [783, 512], [388, 513], [79, 100], [80, 100], [13, 100], [14, 100], [16, 100], [15, 100], [2, 100], [17, 100], [18, 100], [19, 100], [20, 100], [21, 100], [22, 100], [23, 100], [24, 100], [3, 100], [25, 100], [26, 100], [4, 100], [27, 100], [31, 100], [28, 100], [29, 100], [30, 100], [32, 100], [33, 100], [34, 100], [5, 100], [35, 100], [36, 100], [37, 100], [38, 100], [6, 100], [42, 100], [39, 100], [40, 100], [41, 100], [43, 100], [7, 100], [44, 100], [49, 100], [50, 100], [45, 100], [46, 100], [47, 100], [48, 100], [8, 100], [54, 100], [51, 100], [52, 100], [53, 100], [55, 100], [9, 100], [56, 100], [57, 100], [58, 100], [60, 100], [59, 100], [61, 100], [62, 100], [10, 100], [63, 100], [64, 100], [65, 100], [11, 100], [66, 100], [67, 100], [68, 100], [69, 100], [70, 100], [1, 100], [71, 100], [72, 100], [12, 100], [76, 100], [74, 100], [78, 100], [73, 100], [77, 100], [75, 100], [114, 514], [124, 515], [113, 514], [134, 516], [105, 517], [104, 518], [133, 169], [127, 519], [132, 520], [107, 521], [121, 522], [106, 523], [130, 524], [102, 525], [101, 169], [131, 526], [103, 527], [108, 528], [109, 100], [112, 528], [99, 100], [135, 529], [125, 530], [116, 531], [117, 532], [119, 533], [115, 534], [118, 535], [128, 169], [110, 536], [111, 537], [120, 538], [100, 190], [123, 530], [122, 528], [126, 100], [129, 539], [555, 540], [540, 100], [541, 100], [542, 100], [543, 100], [539, 100], [544, 541], [545, 100], [547, 542], [546, 541], [548, 541], [549, 542], [550, 541], [551, 100], [552, 541], [553, 100], [554, 100], [558, 100], [536, 543], [537, 544], [1162, 545], [1165, 546], [1164, 547], [1171, 548], [1172, 548], [1174, 549], [1175, 550], [1173, 549], [1176, 548], [1183, 551], [1184, 552], [1169, 553], [1188, 554], [1186, 555], [1189, 556], [1191, 557], [1185, 558], [1194, 558], [1193, 559], [1195, 560], [1196, 558], [1192, 549], [1253, 561], [1252, 562], [1254, 563], [1255, 564], [557, 565], [560, 566], [556, 567], [563, 568], [564, 569], [565, 570], [566, 571], [567, 572], [568, 569], [571, 573], [570, 573], [575, 574], [574, 575], [576, 576], [577, 576], [579, 577], [580, 576], [581, 578], [584, 579], [585, 578], [583, 580], [719, 581], [718, 582], [720, 583], [721, 576], [724, 584], [723, 585], [728, 586], [727, 586], [726, 587], [734, 588], [733, 589], [731, 590], [735, 591], [738, 592], [739, 593], [740, 594], [737, 595], [741, 596], [743, 597], [742, 598], [744, 597], [745, 599], [1256, 600], [1259, 601], [1260, 602], [1262, 603], [1263, 604], [1264, 605], [1266, 606], [1267, 607], [1268, 608], [1265, 558], [1269, 608], [1270, 606], [1271, 608], [1273, 608], [1274, 608], [1272, 609], [1275, 608], [1276, 608], [1278, 561], [1279, 561], [1277, 549], [1280, 610], [775, 611], [1161, 612], [1283, 613], [1281, 614], [1284, 615], [1167, 616], [1166, 617], [1182, 618], [1180, 552], [1177, 552], [1178, 619], [1190, 619], [1181, 552], [1179, 620], [1258, 602], [1163, 621], [1285, 602], [1261, 618], [1282, 622], [785, 623], [1168, 624], [1147, 172], [1156, 625], [1159, 626], [1160, 552], [1148, 627], [1158, 628], [1157, 629], [1286, 610], [1144, 630], [1140, 631], [749, 632], [751, 633], [753, 634], [1142, 635], [1143, 172], [1141, 636], [1146, 619], [1155, 619], [1145, 172], [1226, 637], [1170, 638], [1257, 639], [1187, 638], [1154, 172], [774, 640], [1227, 641], [771, 624], [772, 172], [773, 172], [754, 100], [758, 642], [759, 172], [760, 172], [685, 643], [538, 644], [535, 645], [569, 646], [572, 646], [582, 647], [573, 572], [722, 648], [534, 544], [725, 646], [730, 649], [561, 646], [761, 650], [732, 100], [736, 651], [762, 100], [763, 652], [578, 653], [729, 646], [562, 597], [764, 654], [765, 100], [767, 655], [750, 100], [752, 100]], "semanticDiagnosticsPerFile": [[526, [{"start": 3270, "length": 517, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./node_modules/next-auth/next/middleware.d.ts", "start": 3237, "length": 21, "messageText": "An argument for 'event' was not provided.", "category": 3, "code": 6210}]}]], [535, [{"start": 75, "length": 8, "messageText": "'\"@prisma/client\"' has no exported member named 'User<PERSON><PERSON>'. Did you mean 'user_role'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 4057, "length": 9, "messageText": "'user_role' is declared here.", "category": 3, "code": 2728}]}, {"start": 1015, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type '(Without<userCreateInput, userUncheckedCreateInput> & userUncheckedCreateInput) | (Without<...> & userCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type 'Without<userUncheckedCreateInput, userCreateInput> & userCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is missing the following properties from type 'userCreateInput': id, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type 'userCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 568983, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: userSelect<DefaultArgs> | null | undefined; omit?: userOmit<DefaultArgs> | null | undefined; include?: userInclude<DefaultArgs> | null | undefined; data: (Without<...> & userUncheckedCreateInput) | (Without<...> & userCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 1283, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ userId: string; company_name: string | undefined; }' is not assignable to type '(Without<clientCreateInput, clientUncheckedCreateInput> & clientUncheckedCreateInput) | (Without<...> & clientCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ userId: string; company_name: string | undefined; }' is not assignable to type 'Without<clientCreateInput, clientUncheckedCreateInput> & clientUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'id' is missing in type '{ userId: string; company_name: string | undefined; }' but required in type 'clientUncheckedCreateInput'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ userId: string; company_name: string | undefined; }' is not assignable to type 'clientUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 797086, "length": 2, "messageText": "'id' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/.prisma/client/index.d.ts", "start": 214526, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: clientSelect<DefaultArgs> | null | undefined; omit?: clientOmit<DefaultArgs> | null | undefined; include?: clientInclude<DefaultArgs> | null | undefined; data: (Without<...> & clientUncheckedCreateInput) | (Without<...> & clientCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 1914, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type '(Without<userCreateInput, userUncheckedCreateInput> & userUncheckedCreateInput) | (Without<...> & userCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type 'Without<userUncheckedCreateInput, userCreateInput> & userCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is missing the following properties from type 'userCreateInput': id, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type 'userCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 568983, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: userSelect<DefaultArgs> | null | undefined; omit?: userOmit<DefaultArgs> | null | undefined; include?: userInclude<DefaultArgs> | null | undefined; data: (Without<...> & userUncheckedCreateInput) | (Without<...> & userCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 2198, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ userId: string; profile_photo: string | undefined; }' is not assignable to type '(Without<commercialCreateInput, commercialUncheckedCreateInput> & commercialUncheckedCreateInput) | (Without<...> & commercialCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ userId: string; profile_photo: string | undefined; }' is not assignable to type 'Without<commercialCreateInput, commercialUncheckedCreateInput> & commercialUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'id' is missing in type '{ userId: string; profile_photo: string | undefined; }' but required in type 'commercialUncheckedCreateInput'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ userId: string; profile_photo: string | undefined; }' is not assignable to type 'commercialUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 799150, "length": 2, "messageText": "'id' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/.prisma/client/index.d.ts", "start": 249957, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: commercialSelect<DefaultArgs> | null | undefined; omit?: commercialOmit<DefaultArgs> | null | undefined; include?: commercialInclude<...> | ... 1 more ... | undefined; data: (Without<...> & commercialUncheckedCreateInput) | (Without<...> & commercialCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 2790, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type '(Without<userCreateInput, userUncheckedCreateInput> & userUncheckedCreateInput) | (Without<...> & userCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type 'Without<userUncheckedCreateInput, userCreateInput> & userCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is missing the following properties from type 'userCreateInput': id, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | undefined; role: any; }' is not assignable to type 'userCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 568983, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: userSelect<DefaultArgs> | null | undefined; omit?: userOmit<DefaultArgs> | null | undefined; include?: userInclude<DefaultArgs> | null | undefined; data: (Without<...> & userUncheckedCreateInput) | (Without<...> & userCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 3054, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ userId: string; }' is not assignable to type '(Without<adminCreateInput, adminUncheckedCreateInput> & adminUncheckedCreateInput) | (Without<...> & adminCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ userId: string; }' is not assignable to type 'Without<adminCreateInput, adminUncheckedCreateInput> & adminUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'id' is missing in type '{ userId: string; }' but required in type 'adminUncheckedCreateInput'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ userId: string; }' is not assignable to type 'adminUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 791420, "length": 2, "messageText": "'id' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/.prisma/client/index.d.ts", "start": 116722, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: adminSelect<DefaultArgs> | null | undefined; omit?: adminOmit<DefaultArgs> | null | undefined; include?: adminInclude<DefaultArgs> | null | undefined; data: (Without<...> & adminUncheckedCreateInput) | (Without<...> & adminCreateInput); }'", "category": 3, "code": 6500}]}]], [537, [{"start": 811, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ username: string; email: string; password: string; firstname: string; lastname: string; role: \"ADMIN\"; }' is not assignable to type '(Without<userCreateWithoutAdminInput, userUncheckedCreateWithoutAdminInput> & userUncheckedCreateWithoutAdminInput) | (Without<...> & userCreateWithoutAdminInput) | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ username: string; email: string; password: string; firstname: string; lastname: string; role: \"ADMIN\"; }' is not assignable to type 'Without<userUncheckedCreateWithoutAdminInput, userCreateWithoutAdminInput> & userCreateWithoutAdminInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ username: string; email: string; password: string; firstname: string; lastname: string; role: \"ADMIN\"; }' is missing the following properties from type 'userCreateWithoutAdminInput': id, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ username: string; email: string; password: string; firstname: string; lastname: string; role: \"ADMIN\"; }' is not assignable to type 'userCreateWithoutAdminInput'."}}]}]}}]], [556, [{"start": 4192, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: string; firstname: any; lastname: any; email: any; telephone: any; password: string; role: \"CLIENT\"; }' is not assignable to type '(Without<userCreateInput, userUncheckedCreateInput> & userUncheckedCreateInput) | (Without<...> & userCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; firstname: any; lastname: any; email: any; telephone: any; password: string; role: \"CLIENT\"; }' is not assignable to type 'Without<userUncheckedCreateInput, userCreateInput> & userCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; firstname: any; lastname: any; email: any; telephone: any; password: string; role: \"CLIENT\"; }' is missing the following properties from type 'userCreateInput': username, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; firstname: any; lastname: any; email: any; telephone: any; password: string; role: \"CLIENT\"; }' is not assignable to type 'userCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 568983, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: userSelect<DefaultArgs> | null | undefined; omit?: userOmit<DefaultArgs> | null | undefined; include?: userInclude<DefaultArgs> | null | undefined; data: (Without<...> & userUncheckedCreateInput) | (Without<...> & userCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 4560, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'company' does not exist in type 'Without<clientCreateInput, clientUncheckedCreateInput> & clientUncheckedCreateInput'.", "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 214526, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: clientSelect<DefaultArgs> | null | undefined; omit?: clientOmit<DefaultArgs> | null | undefined; include?: clientInclude<DefaultArgs> | null | undefined; data: (Without<...> & clientUncheckedCreateInput) | (Without<...> & clientCreateInput); }'", "category": 3, "code": 6500}]}]], [560, [{"start": 2251, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'createdAt' does not exist in type 'clientOrderByWithRelationInput | clientOrderByWithRelationInput[]'."}, {"start": 2413, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2449, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2486, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2524, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2573, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'company' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2612, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'address' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2649, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'city' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2689, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'postalCode' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2728, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'country' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 2788, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6093, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6118, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6225, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6293, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6370, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'company' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6441, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'city' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}, {"start": 6517, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ id: string; userId: string; company_name: string | null; }'."}]], [561, [{"start": 968, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 1089, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 1103, "length": 8, "messageText": "Parameter 'quoteSum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1113, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1720, "length": 6, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'quotes' does not exist in type 'userWhereInput'."}, {"start": 1877, "length": 6, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and '_count' does not exist in type 'userInclude<DefaultArgs>'."}, {"start": 2439, "length": 6, "code": 2339, "category": 1, "messageText": "Property '_count' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 2457, "length": 6, "code": 2339, "category": 1, "messageText": "Property '_count' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 2622, "length": 6, "code": 2339, "category": 1, "messageText": "Property '_count' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 2814, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'PrismaClient<PrismaClientOptions, never, DefaultArgs>'. Did you mean 'quoteitem'?", "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 12891, "length": 9, "messageText": "'quoteitem' is declared here.", "category": 3, "code": 2728}]}, {"start": 3144, "length": 2, "messageText": "Parameter 'pq' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3379, "length": 2, "messageText": "Parameter 'pq' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4318, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 4955, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 4969, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4974, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6266, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 6401, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 6415, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6420, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [562, [{"start": 75, "length": 5, "messageText": "Cannot find module 'zod' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 4258, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 4275, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [566, [{"start": 104, "length": 14, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'jsonwebtoken'. 'C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/jsonwebtoken/index.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "jsonwebtoken", "mode": 99}}]}}, {"start": 2879, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 4766, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'isActive' does not exist in type 'userSelect<DefaultArgs>'.", "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 562914, "length": 6, "messageText": "The expected type comes from property 'select' which is declared here on type '{ select?: userSelect<DefaultArgs> | null | undefined; omit?: userOmit<DefaultArgs> | null | undefined; include?: userInclude<DefaultArgs> | null | undefined; where: userWhereUniqueInput; }'", "category": 3, "code": 6500}]}, {"start": 4822, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}]], [567, [{"start": 73, "length": 14, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'jsonwebtoken'. 'C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/jsonwebtoken/index.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "jsonwebtoken", "mode": 99}}]}}, {"start": 1311, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type '{ client: { id: string; userId: string; company_name: string | null; } | null; admin: { id: string; userId: string; } | null; } & { id: string; email: string; username: string; password: string; ... 5 more ...; updatedAt: Date; }'."}]], [573, [{"start": 16, "length": 14, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'jsonwebtoken'. 'C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/jsonwebtoken/index.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "jsonwebtoken", "mode": 99}}]}}, {"start": 1619, "length": 8, "messageText": "'jwtError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 1652, "length": 13, "messageText": "'nextAuthError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 2240, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'isActive' is missing in type '{ id: string; email: string; username: string; lastname: string; firstname: string; role: user_role; }' but required in type 'MobileUser'.", "relatedInformation": [{"start": 255, "length": 8, "messageText": "'isActive' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; email: string; username: string; lastname: string; firstname: string; role: user_role; }' is not assignable to type 'MobileUser'."}}]], [581, [{"start": 861, "length": 6, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'userId' does not exist in type 'quoteWhereInput'."}, {"start": 912, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 1208, "length": 28, "messageText": "This comparison appears to be unintentional because the types 'quote_status' and '\"COMPLETED\"' have no overlap.", "category": 1, "code": 2367}, {"start": 1369, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 1383, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1388, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1599, "length": 10, "code": 2322, "category": 1, "messageText": "Type '\"En attente\"' is not assignable to type 'quote_status'."}, {"start": 1656, "length": 11, "code": 2678, "category": 1, "messageText": "Type '\"COMPLETED\"' is not comparable to type 'quote_status'."}, {"start": 1679, "length": 10, "code": 2322, "category": 1, "messageText": "Type '\"Terminé\"' is not assignable to type 'quote_status'."}, {"start": 1733, "length": 11, "code": 2678, "category": 1, "messageText": "Type '\"CANCELLED\"' is not comparable to type 'quote_status'."}, {"start": 1756, "length": 10, "code": 2322, "category": 1, "messageText": "Type '\"Annulé\"' is not assignable to type 'quote_status'."}, {"start": 2227, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'updatedAt' does not exist on type 'MobileUser'."}]], [582, [{"start": 105, "length": 8, "messageText": "'\"@prisma/client\"' has no exported member named 'User<PERSON><PERSON>'. Did you mean 'user_role'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 4057, "length": 9, "messageText": "'user_role' is declared here.", "category": 3, "code": 2728}]}, {"start": 5384, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ commercialId: string; clientId: string; }' is not assignable to type '(Without<commercialclientCreateInput, commercialclientUncheckedCreateInput> & commercialclientUncheckedCreateInput) | (Without<...> & commercialclientCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ commercialId: string; clientId: string; }' is not assignable to type 'Without<commercialclientCreateInput, commercialclientUncheckedCreateInput> & commercialclientUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'id' is missing in type '{ commercialId: string; clientId: string; }' but required in type 'commercialclientUncheckedCreateInput'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ commercialId: string; clientId: string; }' is not assignable to type 'commercialclientUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 801247, "length": 2, "messageText": "'id' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/.prisma/client/index.d.ts", "start": 286944, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: commercialclientSelect<DefaultArgs> | null | undefined; omit?: commercialclientOmit<DefaultArgs> | null | undefined; include?: commercialclientInclude<...> | ... 1 more ... | undefined; data: (Without<...> & commercialclientUncheckedCreateInput) | (Without<...> & commercialclientCreateInput); }'", "category": 3, "code": 6500}]}]], [585, [{"start": 1065, "length": 12, "code": 2561, "category": 1, "messageText": "Object literal may only specify known properties, but 'commercialId' does not exist in type 'userWhereInput'. Did you mean to write 'commercial'?"}, {"start": 1236, "length": 12, "code": 2561, "category": 1, "messageText": "Object literal may only specify known properties, but 'commercialId' does not exist in type 'userWhereInput'. Did you mean to write 'commercial'?"}, {"start": 1458, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'user' does not exist in type 'quoteWhereInput'."}, {"start": 1544, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 1710, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 1724, "length": 7, "messageText": "Parameter 'itemSum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1733, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2092, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 2106, "length": 7, "messageText": "Parameter 'itemSum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2115, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2349, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'user' does not exist in type 'quoteWhereInput'."}, {"start": 2596, "length": 12, "code": 2561, "category": 1, "messageText": "Object literal may only specify known properties, but 'commercialId' does not exist in type 'userWhereInput'. Did you mean to write 'commercial'?"}, {"start": 2653, "length": 6, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'quotes' does not exist in type 'userInclude<DefaultArgs>'."}, {"start": 3082, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'company' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 3115, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'quotes' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 3142, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'quotes' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 3227, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'quotes' does not exist on type '{ id: string; email: string; username: string; password: string; lastname: string; firstname: string; telephone: string | null; role: user_role; createdAt: Date; updatedAt: Date; }'."}, {"start": 3381, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'user' does not exist in type 'quoteWhereInput'."}, {"start": 3467, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'user' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 3707, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 3721, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3726, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3872, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 3896, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 3936, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}]], [720, [{"start": 4313, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'createdById' does not exist in type 'quoteWhereInput'."}, {"start": 4388, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'createdById' does not exist in type 'userWhereInput'."}, {"start": 5540, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'firstname' does not exist in type 'clientSelect<DefaultArgs>'."}, {"start": 5762, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'reference' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 5796, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 5822, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 5866, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'createdBy' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 5895, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'createdBy' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 6376, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'createdById' does not exist in type 'quoteWhereInput'."}, {"start": 6414, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 6507, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 6521, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6526, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6796, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'items' does not exist in type 'quoteInclude<DefaultArgs>'."}, {"start": 6889, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'items' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 6903, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6908, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7405, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'firstname' does not exist in type 'clientSelect<DefaultArgs>'."}, {"start": 7762, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'createdById' does not exist in type 'quoteWhereInput'."}, {"start": 7885, "length": 11, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'createdById' does not exist in type 'quoteWhereInput'."}, {"start": 8374, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'PrismaClient<PrismaClientOptions, never, DefaultArgs>'. Did you mean 'quoteitem'?", "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 12891, "length": 9, "messageText": "'quoteitem' is declared here.", "category": 3, "code": 2728}]}, {"start": 8626, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8851, "length": 5, "messageText": "Parameter 'count' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [722, [{"start": 44, "length": 16, "messageText": "'\"@prisma/client\"' has no exported member named 'NotificationType'. Did you mean 'notification_type'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3744, "length": 17, "messageText": "'notification_type' is declared here.", "category": 3, "code": 2728}]}, {"start": 548, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' is not assignable to type '(Without<notificationCreateInput, notificationUncheckedCreateInput> & notificationUncheckedCreateInput) | (Without<...> & notificationCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' is not assignable to type 'Without<notificationCreateInput, notificationUncheckedCreateInput> & notificationUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'id' is missing in type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' but required in type 'notificationUncheckedCreateInput'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' is not assignable to type 'notificationUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 802992, "length": 2, "messageText": "'id' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/.prisma/client/index.d.ts", "start": 322302, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: notificationSelect<DefaultArgs> | null | undefined; omit?: notificationOmit<DefaultArgs> | null | undefined; include?: notificationInclude<...> | ... 1 more ... | undefined; data: (Without<...> & notificationUncheckedCreateInput) | (Without<...> & notificationCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 2098, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' is not assignable to type '(Without<notificationCreateInput, notificationUncheckedCreateInput> & notificationUncheckedCreateInput) | (Without<...> & notificationCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' is not assignable to type 'Without<notificationCreateInput, notificationUncheckedCreateInput> & notificationUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'id' is missing in type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' but required in type 'notificationUncheckedCreateInput'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ quoteId?: string | undefined; type: NotificationType; message: string; adminId: string; }' is not assignable to type 'notificationUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 802992, "length": 2, "messageText": "'id' is declared here.", "category": 3, "code": 2728}, {"file": "./node_modules/.prisma/client/index.d.ts", "start": 322302, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: notificationSelect<DefaultArgs> | null | undefined; omit?: notificationOmit<DefaultArgs> | null | undefined; include?: notificationInclude<...> | ... 1 more ... | undefined; data: (Without<...> & notificationUncheckedCreateInput) | (Without<...> & notificationCreateInput); }'", "category": 3, "code": 6500}]}]], [725, [{"start": 4424, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}, {"start": 4551, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'orderItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'orderitem'?"}]], [730, [{"start": 44, "length": 11, "messageText": "'\"@prisma/client\"' has no exported member named 'QuoteStatus'. Did you mean 'quote_status'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3960, "length": 12, "messageText": "'quote_status' is declared here.", "category": 3, "code": 2728}]}, {"start": 57, "length": 16, "messageText": "'\"@prisma/client\"' has no exported member named 'NotificationType'. Did you mean 'notification_type'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3744, "length": 17, "messageText": "'notification_type' is declared here.", "category": 3, "code": 2728}]}, {"start": 2784, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ quoteNumber: string; clientId: string; status: any; notes: string | undefined; validUntil: Date | undefined; createdByAdminId: string | undefined; }' is not assignable to type '(Without<quoteCreateInput, quoteUncheckedCreateInput> & quoteUncheckedCreateInput) | (Without<...> & quoteCreateInput)'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ quoteNumber: string; clientId: string; status: any; notes: string | undefined; validUntil: Date | undefined; createdByAdminId: string | undefined; }' is not assignable to type 'Without<quoteCreateInput, quoteUncheckedCreateInput> & quoteUncheckedCreateInput'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ quoteNumber: string; clientId: string; status: any; notes: string | undefined; validUntil: Date | undefined; createdByAdminId: string | undefined; }' is missing the following properties from type 'quoteUncheckedCreateInput': id, updatedAt", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ quoteNumber: string; clientId: string; status: any; notes: string | undefined; validUntil: Date | undefined; createdByAdminId: string | undefined; }' is not assignable to type 'quoteUncheckedCreateInput'."}}]}]}, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 500149, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: quoteSelect<DefaultArgs> | null | undefined; omit?: quoteOmit<DefaultArgs> | null | undefined; include?: quoteInclude<DefaultArgs> | null | undefined; data: (Without<...> & quoteUncheckedCreateInput) | (Without<...> & quoteCreateInput); }'", "category": 3, "code": 6500}]}, {"start": 3029, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}, {"start": 6053, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'includes' does not exist on type 'never'."}, {"start": 6734, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}, {"start": 6927, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7100, "length": 1, "messageText": "Parameter 'i' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7264, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}, {"start": 7567, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}, {"start": 7988, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8105, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}, {"start": 8384, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'quoteItem' does not exist on type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, \"$connect\" | \"$disconnect\" | \"$on\" | \"$transaction\" | \"$use\" | \"$extends\">'. Did you mean 'quoteitem'?"}]], [731, [{"start": 167, "length": 11, "messageText": "'\"@prisma/client\"' has no exported member named 'QuoteStatus'. Did you mean 'quote_status'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3960, "length": 12, "messageText": "'quote_status' is declared here.", "category": 3, "code": 2728}]}, {"start": 2041, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'clientId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'clientId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}, {"start": 2075, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'clientId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'clientId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}, {"start": 5236, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'clientId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'clientId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}, {"start": 5492, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'clientId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'clientId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}, {"start": 5714, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'clientId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'clientId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}, {"start": 5814, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'clientId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'clientId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}, {"start": 6304, "length": 7, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'adminId' does not exist on type 'MobileUser | ({ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { ...; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'adminId' does not exist on type 'MobileUser'.", "category": 1, "code": 2339}]}}]], [733, [{"start": 183, "length": 11, "messageText": "'\"@prisma/client\"' has no exported member named 'QuoteStatus'. Did you mean 'quote_status'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3960, "length": 12, "messageText": "'quote_status' is declared here.", "category": 3, "code": 2728}]}]], [734, [{"start": 1326, "length": 10, "code": 2561, "category": 1, "messageText": "Object literal may only specify known properties, but 'quoteItems' does not exist in type 'quoteInclude<DefaultArgs>'. Did you mean to write 'quoteitem'?", "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 494206, "length": 7, "messageText": "The expected type comes from property 'include' which is declared here on type '{ select?: quoteSelect<DefaultArgs> | null | undefined; omit?: quoteOmit<DefaultArgs> | null | undefined; include?: quoteInclude<DefaultArgs> | null | undefined; where: quoteWhereUniqueInput; }'", "category": 3, "code": 6500}]}, {"start": 3146, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number | Date'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number | Date'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 4, '(value: string | number): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"start": 3349, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 3365, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 3422, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 3453, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 3514, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 3578, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 3635, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'. Did you mean 'clientId'?"}, {"start": 5501, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'quoteItems' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 5535, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'quoteItems' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 5556, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'quoteItems' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 5593, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'quoteItems' does not exist on type '{ status: quote_status; id: string; createdAt: Date; updatedAt: Date; clientId: string; totalAmount: number; quoteNumber: string; validUntil: Date | null; notes: string | null; createdByAdminId: string | null; pdfUrl: string | null; }'."}, {"start": 5613, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8387, "length": 12, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'pdfGenerated' does not exist in type '(Without<quoteUpdateInput, quoteUncheckedUpdateInput> & quoteUncheckedUpdateInput) | (Without<...> & quoteUpdateInput)'.", "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 501011, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type '{ select?: quoteSelect<DefaultArgs> | null | undefined; omit?: quoteOmit<DefaultArgs> | null | undefined; include?: quoteInclude<DefaultArgs> | null | undefined; data: (Without<...> & quoteUncheckedUpdateInput) | (Without<...> & quoteUpdateInput); where: quoteWhereUniqueInput; }'", "category": 3, "code": 6500}]}]], [739, [{"start": 1569, "length": 21, "messageText": "'result.reminderReport' is possibly 'undefined'.", "category": 1, "code": 18048}]], [1140, [{"start": 4123, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'vibrate' does not exist in type 'NotificationOptions'."}]], [1145, [{"start": 136, "length": 3, "messageText": "Cannot find namespace 'JSX'.", "category": 1, "code": 2503}, {"start": 289, "length": 9, "messageText": "JSX element type 'Component' does not have any construct or call signatures.", "category": 1, "code": 2604}, {"start": 289, "length": 9, "code": 2786, "category": 1, "messageText": {"messageText": "'Component' cannot be used as a JSX component.", "category": 1, "code": 2786, "next": [{"messageText": "Its type 'string | number | symbol' is not a valid JSX element type.", "category": 1, "code": 18053, "next": [{"messageText": "Type 'number' is not assignable to type 'ElementType'.", "category": 1, "code": 2322}]}]}}]], [1163, [{"start": 229, "length": 8, "messageText": "'\"@prisma/client\"' has no exported member named 'User<PERSON><PERSON>'. Did you mean 'user_role'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 4057, "length": 9, "messageText": "'user_role' is declared here.", "category": 3, "code": 2728}]}]], [1164, [{"start": 374, "length": 11, "messageText": "'\"@prisma/client\"' has no exported member named 'QuoteStatus'. Did you mean 'quote_status'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3960, "length": 12, "messageText": "'quote_status' is declared here.", "category": 3, "code": 2728}]}]], [1165, [{"start": 446, "length": 11, "messageText": "'\"@prisma/client\"' has no exported member named 'QuoteStatus'. Did you mean 'quote_status'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3960, "length": 12, "messageText": "'quote_status' is declared here.", "category": 3, "code": 2728}]}]], [1186, [{"start": 8144, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'brand' does not exist on type 'Product'."}, {"start": 8291, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'brand' does not exist on type 'Product'."}]], [1188, [{"start": 2997, "length": 3, "messageText": "Parameter 'img' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [1193, [{"start": 466, "length": 11, "messageText": "'\"@prisma/client\"' has no exported member named 'QuoteStatus'. Did you mean 'quote_status'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./node_modules/.prisma/client/index.d.ts", "start": 3960, "length": 12, "messageText": "'quote_status' is declared here.", "category": 3, "code": 2728}]}]], [1226, [{"start": 7474, "length": 7, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'Date | null' is not assignable to type 'Date | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'Date | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./node_modules/react-datepicker/dist/month_year_dropdown_options.d.ts", "start": 132, "length": 7, "messageText": "The expected type comes from property 'minDate' which is declared here on type 'IntrinsicAttributes & (IntrinsicClassAttributes<DatePicker> & ((Pick<Readonly<Omit<Omit<YearDropdownProps, \"year\" | ... 3 more ... | \"maxDate\"> & ... 9 more ... & { ...; }, \"className\" | ... 19 more ... | \"onTimeChange\"> & ... 4 more ... & { ...; }>, \"name\" | ... 121 more ... | \"onChangeRaw\"> & InexactPartial<...> &...'", "category": 3, "code": 6500}]}, {"start": 8562, "length": 8, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(date: Date) => void' is not assignable to type '((date: Date | null, event?: MouseEvent<HTMLElement, MouseEvent> | KeyboardEvent<HTMLElement> | undefined) => void) | ((date: [...], event?: MouseEvent<...> | ... 1 more ... | undefined) => void) | ((date: Date[] | null, event?: MouseEvent<...> | ... 1 more ... | undefined) => void) | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '(date: Date) => void' is not assignable to type '(date: Date | null, event?: MouseEvent<HTMLElement, MouseEvent> | KeyboardEvent<HTMLElement> | undefined) => void'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'date' and 'date' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Date | null' is not assignable to type 'Date'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'Date'.", "category": 1, "code": 2322}]}]}]}]}, "relatedInformation": [{"file": "./node_modules/react-datepicker/dist/index.d.ts", "start": 4022, "length": 8, "messageText": "The expected type comes from property 'onChange' which is declared here on type 'IntrinsicAttributes & (IntrinsicClassAttributes<DatePicker> & ((Pick<Readonly<Omit<Omit<YearDropdownProps, \"year\" | ... 3 more ... | \"maxDate\"> & ... 9 more ... & { ...; }, \"className\" | ... 19 more ... | \"onTimeChange\"> & ... 4 more ... & { ...; }>, \"name\" | ... 121 more ... | \"onChangeRaw\"> & InexactPartial<...> &...'", "category": 3, "code": 6500}]}]], [1266, [{"start": 1353, "length": 150, "code": 2322, "category": 1, "messageText": "Type '{ id: string; date: string; total: number; status: string; }' is not assignable to type 'never'."}, {"start": 1519, "length": 153, "code": 2322, "category": 1, "messageText": "Type '{ id: string; date: string; total: number; status: string; }' is not assignable to type 'never'."}]], [1270, [{"start": 1362, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'telephone' does not exist on type '{ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { name?: string | ... 1 more ... | undefined; email?: string | ... 1 more ... | undefined; image?: string | ... 1 more ... | undefined...'."}, {"start": 1406, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'client' does not exist on type '{ id: string; username: string; role: string; firstname: string; lastname: string; clientId?: string | undefined; commercialId?: string | undefined; adminId?: string | undefined; } & { name?: string | ... 1 more ... | undefined; email?: string | ... 1 more ... | undefined; image?: string | ... 1 more ... | undefined...'. Did you mean 'clientId'?", "relatedInformation": [{"file": "./src/types/next-auth.d.ts", "start": 422, "length": 8, "messageText": "'clientId' is declared here.", "category": 3, "code": 2728}]}]], [1274, [{"start": 1500, "length": 276, "code": 2322, "category": 1, "messageText": "Type '{ id: number; firstname: string; lastname: string; email: string; company: string; lastOrder: string; status: string; }' is not assignable to type 'never'."}, {"start": 1792, "length": 284, "code": 2322, "category": 1, "messageText": "Type '{ id: number; firstname: string; lastname: string; email: string; company: string; lastOrder: string; status: string; }' is not assignable to type 'never'."}]], [1279, [{"start": 5747, "length": 3, "messageText": "Parameter 'url' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [1285, [{"start": 2324, "length": 10, "messageText": "Cannot find name 'FaEnvelope'.", "category": 1, "code": 2304}]], [1295, [{"start": 1300, "length": 27, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ params: { id: string; }; }' does not satisfy the constraint 'PageProps'.", "category": 1, "code": 2344, "next": [{"messageText": "Types of property 'params' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; }' is not assignable to type 'Promise<any>'."}}]}]}}]], [1311, [{"start": 1300, "length": 27, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ params: { id: string; }; }' does not satisfy the constraint 'PageProps'.", "category": 1, "code": 2344, "next": [{"messageText": "Types of property 'params' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; }' is not assignable to type 'Promise<any>'."}}]}]}}]], [1315, [{"start": 1580, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 4826, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PUT\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5633, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1324, [{"start": 1553, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5606, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 6430, "length": 134, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PATCH\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1326, [{"start": 1565, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5618, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 6442, "length": 134, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PATCH\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1333, [{"start": 1568, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5621, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 6445, "length": 134, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PATCH\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1342, [{"start": 1559, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5612, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 6436, "length": 134, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PATCH\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1343, [{"start": 4030, "length": 132, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"POST\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5639, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1345, [{"start": 1553, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5606, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 6430, "length": 134, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PATCH\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1346, [{"start": 1571, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1349, [{"start": 1574, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 5627, "length": 136, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"DELETE\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}, {"start": 6451, "length": 134, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"PATCH\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1354, [{"start": 1547, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1355, [{"start": 1550, "length": 130, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ __tag__: \"GET\"; __param_position__: \"second\"; __param_type__: { params: { id: string; }; }; }' does not satisfy the constraint 'ParamCheck<RouteContext>'.", "category": 1, "code": 2344, "next": [{"messageText": "The types of '__param_type__.params' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ params: { id: string; }; }' is not assignable to type 'RouteContext'."}}]}]}}]], [1374, [{"start": 1315, "length": 27, "code": 2344, "category": 1, "messageText": {"messageText": "Type '{ params: { id: string; }; }' does not satisfy the constraint 'PageProps'.", "category": 1, "code": 2344, "next": [{"messageText": "Types of property 'params' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ id: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; }' is not assignable to type 'Promise<any>'."}}]}]}}]]], "affectedFilesPendingEmit": [1289, 1291, 1290, 1292, 1293, 1295, 1296, 1294, 1297, 1298, 1299, 1302, 1301, 1303, 1304, 1300, 1307, 1306, 1308, 1309, 1305, 1311, 1310, 1312, 1313, 1315, 1316, 1314, 1317, 1318, 1319, 1320, 1321, 1322, 1324, 1323, 1326, 1325, 1327, 1328, 1329, 1330, 1331, 1333, 1334, 1332, 1336, 1335, 1337, 1338, 1340, 1339, 1343, 1342, 1341, 1346, 1345, 1344, 1347, 1349, 1350, 1351, 1348, 1352, 1354, 1353, 1355, 1356, 1357, 1358, 1359, 1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1374, 1375, 1373, 1376, 1288, 1378, 1377, 1379, 526, 527, 536, 537, 1162, 1165, 1164, 1171, 1172, 1174, 1175, 1173, 1176, 1183, 1184, 1169, 1188, 1186, 1189, 1191, 1185, 1194, 1193, 1195, 1196, 1192, 1253, 1252, 1254, 1255, 557, 560, 556, 563, 564, 565, 566, 567, 568, 571, 570, 575, 574, 576, 577, 579, 580, 581, 584, 585, 583, 719, 718, 720, 721, 724, 723, 728, 727, 726, 734, 733, 731, 735, 738, 739, 740, 737, 741, 743, 742, 744, 745, 1256, 1259, 1260, 1262, 1263, 1264, 1266, 1267, 1268, 1265, 1269, 1270, 1271, 1273, 1274, 1272, 1275, 1276, 1278, 1279, 1277, 1280, 775, 1161, 1283, 1281, 1284, 1167, 1166, 1182, 1180, 1177, 1178, 1190, 1181, 1179, 1258, 1163, 1285, 1261, 1282, 785, 1168, 1147, 1156, 1159, 1160, 1148, 1158, 1157, 1286, 1144, 1140, 749, 751, 753, 1142, 1143, 1141, 1146, 1155, 1145, 1226, 1170, 1257, 1187, 1154, 774, 1227, 771, 772, 773, 754, 758, 759, 760, 685, 538, 535, 569, 572, 582, 573, 722, 534, 725, 730, 561, 761, 732, 736, 762, 763, 578, 729, 562, 767, 750, 752], "version": "5.8.3"}