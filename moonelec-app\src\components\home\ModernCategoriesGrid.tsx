'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { FaArrowRight, FaLightbulb, FaPlug, FaShieldAlt, FaCog, FaHome, FaIndustry } from 'react-icons/fa';

interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  icon: React.ReactNode;
  productCount: number;
  href: string;
  gradient: string;
}

const featuredCategories: Category[] = [
  {
    id: '1',
    name: 'Éclairage LED',
    description: 'Solutions d\'éclairage modernes et économes en énergie',
    image: '/images/hero/placeholder.svg',
    icon: <FaLightbulb className="text-2xl" />,
    productCount: 156,
    href: '/products?category=eclairage',
    gradient: 'from-yellow-400/80 to-orange-500/80'
  },
  {
    id: '2',
    name: 'Prises & Interrupteurs',
    description: 'Gamme complète de prises et interrupteurs design',
    image: '/images/hero/placeholder.svg',
    icon: <FaPlug className="text-2xl" />,
    productCount: 89,
    href: '/products?category=prises',
    gradient: 'from-blue-400/80 to-indigo-500/80'
  },
  {
    id: '3',
    name: 'Protection Électrique',
    description: 'Dispositifs de protection et sécurité électrique',
    image: '/images/hero/placeholder.svg',
    icon: <FaShieldAlt className="text-2xl" />,
    productCount: 67,
    href: '/products?category=protection',
    gradient: 'from-green-400/80 to-emerald-500/80'
  },
  {
    id: '4',
    name: 'Câbles & Conducteurs',
    description: 'Câbles électriques haute qualité pour tous usages',
    image: '/images/hero/placeholder.svg',
    icon: <FaCog className="text-2xl" />,
    productCount: 134,
    href: '/products?category=cables',
    gradient: 'from-purple-400/80 to-violet-500/80'
  },
  {
    id: '5',
    name: 'Domotique',
    description: 'Solutions intelligentes pour la maison connectée',
    image: '/images/hero/placeholder.svg',
    icon: <FaHome className="text-2xl" />,
    productCount: 45,
    href: '/products?category=domotique',
    gradient: 'from-cyan-400/80 to-blue-500/80'
  },
  {
    id: '6',
    name: 'Industriel',
    description: 'Équipements électriques pour applications industrielles',
    image: '/images/hero/placeholder.svg',
    icon: <FaIndustry className="text-2xl" />,
    productCount: 78,
    href: '/products?category=industriel',
    gradient: 'from-red-400/80 to-rose-500/80'
  }
];

export default function ModernCategoriesGrid() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (response.ok) {
          const data = await response.json();
          const realCategories = (data.categories || []).slice(0, 6).map((cat: any, index: number) => ({
            id: cat.id,
            name: cat.name,
            description: `Solutions ${cat.name.toLowerCase()} de haute qualité`,
            image: cat.image || '/images/hero/placeholder.svg',
            icon: featuredCategories[index % featuredCategories.length].icon,
            productCount: Math.floor(Math.random() * 200) + 50, // Mock count for now
            href: `/products?category=${cat.id}`,
            gradient: featuredCategories[index % featuredCategories.length].gradient
          }));

          // If we have real categories, use them, otherwise fallback to featured
          if (realCategories.length > 0) {
            setCategories(realCategories);
          } else {
            setCategories(featuredCategories);
          }
        } else {
          setCategories(featuredCategories);
        }
      } catch (error) {
        console.error('Error loading categories:', error);
        setCategories(featuredCategories); // Fallback
      } finally {
        setIsLoading(false);
      }
    };

    loadCategories();
  }, []);

  if (isLoading) {
    return (
      <section className="py-16 bg-light-gray">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Loading skeleton */}
            {[...Array(7)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg p-6 animate-pulse">
                <div className="h-48 bg-gray-200 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-light-gray">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Panel - Call to Action */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="lg:col-span-1 bg-white rounded-xl p-8 shadow-lg flex flex-col justify-center"
          >
            <div className="mb-6">
              <h2 className="text-3xl font-bold text-charcoal mb-4 font-heading">
                Catégories les Plus Populaires
              </h2>
              <p className="text-gray-600 leading-relaxed">
                Découvrez notre sélection de produits électriques de haute qualité, 
                organisés par catégories pour faciliter votre recherche.
              </p>
            </div>
            
            <div className="mb-6">
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-moonelec-red rounded-full mr-2"></div>
                  <span>Plus de 500 produits</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-electric-blue rounded-full mr-2"></div>
                  <span>Livraison rapide</span>
                </div>
              </div>
            </div>

            <Link
              href="/products"
              className="group inline-flex items-center justify-center space-x-2 bg-moonelec-red hover:bg-moonelec-red-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              <span>Explorer Produits</span>
              <FaArrowRight className="group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </motion.div>

          {/* Right Panel - Categories Grid */}
          <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Link href={category.href}>
                  <div className="relative h-64 bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    {/* Background Image */}
                    <div className="absolute inset-0">
                      <div 
                        className="w-full h-full bg-cover bg-center bg-no-repeat"
                        style={{
                          backgroundImage: `url(${category.image})`,
                        }}
                      />
                      {/* Gradient Overlay */}
                      <div className={`absolute inset-0 bg-gradient-to-t ${category.gradient} opacity-80 group-hover:opacity-90 transition-opacity duration-300`} />
                    </div>

                    {/* Content */}
                    <div className="relative z-10 h-full flex flex-col justify-between p-6 text-white">
                      {/* Icon */}
                      <div className="flex justify-between items-start">
                        <div className="p-3 bg-white/20 backdrop-blur-sm rounded-lg">
                          {category.icon}
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium opacity-90">
                            {category.productCount} produits
                          </div>
                        </div>
                      </div>

                      {/* Title and Description */}
                      <div>
                        <h3 className="text-xl font-bold mb-2 group-hover:text-white transition-colors">
                          {category.name}
                        </h3>
                        <p className="text-sm opacity-90 leading-relaxed">
                          {category.description}
                        </p>
                      </div>

                      {/* View Button */}
                      <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                        <div className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 px-4 py-2 rounded-lg transition-all duration-300">
                          <span className="text-sm font-medium">Voir produits</span>
                          <FaArrowRight className="text-xs" />
                        </div>
                      </div>
                    </div>

                    {/* Hover Effect Border */}
                    <div className="absolute inset-0 border-2 border-transparent group-hover:border-white/30 rounded-xl transition-all duration-300"></div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { number: '500+', label: 'Produits' },
            { number: '50+', label: 'Marques' },
            { number: '1000+', label: 'Clients Satisfaits' },
            { number: '24/7', label: 'Support' }
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-moonelec-red mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
