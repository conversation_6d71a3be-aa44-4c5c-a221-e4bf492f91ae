import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';
import {
  validateInput,
  schemas,
  sanitizeInput,
  checkIPSecurity,
  reportSuspiciousActivity,
  createSecureError,
  validateAuthToken
} from '@/middleware/security';

// POST /api/auth/mobile/login
export async function POST(request: NextRequest) {
  try {
    // Get client IP for security checks
    const clientIP = request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    'unknown';

    // Check IP security
    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    const body = await request.json();

    // Validate and sanitize input
    try {
      validateInput(schemas.email.or(schemas.username), body.username);
      validateInput(schemas.password, body.password);
    } catch (validationError: any) {
      console.log('❌ Validation error:', validationError.message);
      reportSuspiciousActivity(clientIP);
      return createSecureError('Invalid input format', 400);
    }

    const username = sanitizeInput(body.username);
    const password = body.password; // Don't sanitize password as it may contain special chars

    console.log('📱 Mobile login attempt for:', username);

    if (!username || !password) {
      reportSuspiciousActivity(clientIP);
      return NextResponse.json(
        { success: false, error: 'Username and password are required' },
        { status: 400 }
      );
    }

    // Find user by username or email
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: username }
        ]
      }
    });

    if (!user) {
      console.log('❌ User not found:', username);
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      console.log('❌ Invalid password for user:', username);
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Generate JWT token for mobile app using JWT_SECRET
    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
      },
      jwtSecret,
      { expiresIn: '7d' } // Token valid for 7 days
    );

    // Prepare user data (exclude password)
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstname: user.firstname,
      lastname: user.lastname,
      role: user.role,
      isActive: user.isActive,
    };

    console.log('✅ Mobile login successful for:', username, 'Role:', user.role);

    return NextResponse.json({
      success: true,
      user: userData,
      token: token,
      message: 'Login successful'
    });

  } catch (error) {
    console.error('❌ Mobile login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/auth/mobile/verify - Verify JWT token
export async function GET(request: NextRequest) {
  try {
    // Get client IP for security checks
    const clientIP = request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    'unknown';

    // Check IP security
    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      reportSuspiciousActivity(clientIP);
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Validate token format for security
    if (!validateAuthToken(token)) {
      reportSuspiciousActivity(clientIP);
      return createSecureError('Invalid token format', 401);
    }

    // Verify JWT token using JWT_SECRET
    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
    const decoded = jwt.verify(token, jwtSecret) as any;

    // Get fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        email: true,
        firstname: true,
        lastname: true,
        role: true,
        isActive: true,
      }
    });

    if (!user || !user.isActive) {
      return NextResponse.json(
        { success: false, error: 'User not found or inactive' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      user: user
    });

  } catch (error) {
    console.error('❌ Token verification error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid token' },
      { status: 401 }
    );
  }
}
