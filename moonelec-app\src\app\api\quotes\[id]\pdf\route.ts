import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth-options';
import PDFDocument from 'pdfkit';
import { extractId } from '@/lib/route-utils';

// GET /api/quotes/[id]/pdf - Générer un PDF pour un devis
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Vérifier si l'utilisateur est authentifié
    if (!session) {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      );
    }

    // Extraire l'ID de l'URL plutôt que des paramètres
    const url = request.url;
    const segments = url.split('/');
    const quoteId = segments[segments.length - 2]; // -2 car l'URL se termine par /pdf

    // Récupérer le devis avec ses éléments et les informations du client
    const quote = await prisma.quote.findUnique({
      where: { id: quoteId },
      include: {
        client: {
          include: {
            user: {
              select: {
                firstname: true,
                lastname: true,
                email: true,
                telephone: true,
              },
            },
          },
        },
        quoteItems: {
          include: {
            product: true
          }
        }
      }
    });

    if (!quote) {
      return NextResponse.json(
        { error: 'Devis non trouvé' },
        { status: 404 }
      );
    }

    // Vérifier les autorisations
    if (
      session.user.role === 'CLIENT' &&
      quote.clientId !== session.user.clientId
    ) {
      return NextResponse.json(
        { error: 'Non autorisé à accéder à ce devis' },
        { status: 403 }
      );
    }

    // Créer un nouveau document PDF
    const doc = new PDFDocument({ margin: 50 });

    // Créer un buffer pour stocker le PDF
    const chunks: Buffer[] = [];
    doc.on('data', (chunk) => chunks.push(chunk));

    // Promesse qui se résout lorsque le PDF est généré
    const pdfPromise = new Promise<Buffer>((resolve) => {
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve(pdfBuffer);
      });
    });

    // Ajouter le logo
    // doc.image('public/images/logo/logo-moonelec.png', 50, 45, { width: 150 });

    // Ajouter l'en-tête
    doc.fontSize(20).text('MOONELEC', 50, 50);
    doc.fontSize(10).text('Distribution de Matériel Électrique', 50, 75);
    doc.moveDown();
    doc.fontSize(10).text('123 Avenue Mohammed V', 50, doc.y);
    doc.text('Casablanca, Maroc', 50, doc.y + 15);
    doc.text('Tél: +212 522 123 456', 50, doc.y + 15);
    doc.text('Email: <EMAIL>', 50, doc.y + 15);

    // Ajouter les informations du devis
    doc.moveDown(2);
    doc.fontSize(16).text('DEVIS', { align: 'center' });
    doc.moveDown();

    doc.fontSize(10).text(`Numéro de devis: ${quote.quoteNumber}`, { align: 'right' });
    doc.text(`Date: ${new Date(quote.createdAt).toLocaleDateString('fr-FR')}`, { align: 'right' });
    doc.text(`Valide jusqu'au: ${new Date(quote.validUntil).toLocaleDateString('fr-FR')}`, { align: 'right' });

    // Ajouter les informations du client
    doc.moveDown(2);
    doc.fontSize(12).text('Informations du client:');

    if (quote.client && quote.client.user) {
      doc.fontSize(10).text(`Nom: ${quote.client.user.firstname} ${quote.client.user.lastname}`);
      doc.text(`Entreprise: ${quote.client.company_name || 'N/A'}`);
      doc.text(`Email: ${quote.client.user.email}`);
      doc.text(`Téléphone: ${quote.client.user.telephone || 'N/A'}`);
    } else {
      doc.fontSize(10).text('Informations client non disponibles');
    }

    // Ajouter les informations de l'administrateur si le devis a été créé par un admin
    if (quote.createdByAdminId) {
      doc.moveDown();
      doc.fontSize(12).text('Demande créée par:');

      // Récupérer les informations de l'administrateur
      const admin = await prisma.admin.findUnique({
        where: { id: quote.createdByAdminId },
        include: {
          user: {
            select: {
              firstname: true,
              lastname: true,
              email: true,
            },
          },
        },
      });

      if (admin && admin.user) {
        doc.fontSize(10).text(`Administrateur: ${admin.user.firstname} ${admin.user.lastname}`);
        doc.text(`Email: ${admin.user.email}`);
      } else {
        doc.fontSize(10).text('Informations administrateur non disponibles');
      }
    }

    // Ajouter le tableau des articles
    doc.moveDown(2);
    doc.fontSize(12).text('Articles:');

    // En-têtes du tableau
    const tableTop = doc.y + 10;
    const tableHeaders = ['Produit', 'Référence', 'Quantité', 'Prix unitaire (MAD)', 'Total (MAD)'];
    const columnWidths = [200, 100, 50, 100, 100];

    let yPosition = tableTop;

    // Dessiner l'en-tête du tableau
    doc.fontSize(10);
    doc.font('Helvetica-Bold');

    tableHeaders.forEach((header, i) => {
      let xPosition = 50;
      for (let j = 0; j < i; j++) {
        xPosition += columnWidths[j];
      }
      doc.text(header, xPosition, yPosition);
    });

    yPosition += 20;
    doc.moveTo(50, yPosition).lineTo(550, yPosition).stroke();
    yPosition += 10;

    // Dessiner les lignes du tableau
    doc.font('Helvetica');

    let totalAmount = 0;

    // Vérifier si quoteItems existe et est un tableau
    if (quote.quoteItems && Array.isArray(quote.quoteItems) && quote.quoteItems.length > 0) {
      quote.quoteItems.forEach((item) => {
        // Vérifier si l'item et le produit existent
        if (item && item.product) {
          const itemTotal = item.quantity * item.unitPrice;
          totalAmount += itemTotal;

          let xPosition = 50;

          // Produit
          doc.text(item.product.name || 'Produit inconnu', xPosition, yPosition, { width: columnWidths[0] });
          xPosition += columnWidths[0];

          // Référence
          doc.text(item.product.reference || 'N/A', xPosition, yPosition);
          xPosition += columnWidths[1];

          // Quantité
          doc.text(item.quantity.toString(), xPosition, yPosition);
          xPosition += columnWidths[2];

          // Prix unitaire
          doc.text(item.unitPrice.toFixed(2), xPosition, yPosition);
          xPosition += columnWidths[3];

          // Total
          doc.text(itemTotal.toFixed(2), xPosition, yPosition);
        } else {
          // Si l'item ou le produit n'existe pas, afficher un message d'erreur
          let xPosition = 50;
          doc.text('Données produit non disponibles', xPosition, yPosition, { width: columnWidths[0] });
        }

        yPosition += 30;

        // Vérifier si nous avons besoin d'une nouvelle page
        if (yPosition > 700) {
          doc.addPage();
          yPosition = 50;
        }
      });
    } else {
      // Si quoteItems n'existe pas ou est vide, afficher un message
      let xPosition = 50;
      doc.text('Aucun produit dans ce devis', xPosition, yPosition, { width: columnWidths[0] });
      yPosition += 30;
    }

    // Vérifier si nous avons besoin d'une nouvelle page après le tableau
    if (yPosition > 700) {
      doc.addPage();
      yPosition = 50;
    }

    // Dessiner la ligne de total
    doc.moveTo(50, yPosition).lineTo(550, yPosition).stroke();
    yPosition += 10;

    // Ajouter le total
    doc.font('Helvetica-Bold');
    doc.text('Total:', 350, yPosition);
    doc.text(`${totalAmount.toFixed(2)} MAD`, 450, yPosition);

    // Ajouter les notes
    if (quote.notes) {
      doc.moveDown(2);
      doc.font('Helvetica');
      doc.fontSize(12).text('Notes:');
      doc.fontSize(10).text(quote.notes);
    }

    // Ajouter le pied de page
    const pageCount = doc.bufferedPageRange().count;
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);

      // Pied de page
      doc.fontSize(8).text(
        `Page ${i + 1} sur ${pageCount}`,
        50,
        doc.page.height - 50,
        { align: 'center' }
      );
    }

    // Finaliser le document
    doc.end();

    // Attendre que le PDF soit généré
    const pdfBuffer = await pdfPromise;

    // Mettre à jour l'URL du PDF dans la base de données
    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        pdfGenerated: true,
        updatedAt: new Date()
      }
    });

    // Retourner le PDF
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="devis-${quote.quoteNumber}.pdf"`
      }
    });
  } catch (error) {
    console.error('Erreur lors de la génération du PDF:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la génération du PDF' },
      { status: 500 }
    );
  }
}
