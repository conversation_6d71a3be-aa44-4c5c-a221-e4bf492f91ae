'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaUpload, FaImage, FaTrash, Fa<PERSON><PERSON><PERSON>, Fa<PERSON>rrowUp, FaArrowDown } from 'react-icons/fa';
import { useFileUpload } from '@/hooks/useFileUpload';

interface ImageItem {
  url: string;
  alt?: string;
  order?: number;
}

interface MultiImageUploadProps {
  initialImages?: ImageItem[];
  onImagesChange: (images: ImageItem[]) => void;
  directory?: string;
  className?: string;
  maxImages?: number;
  maxSizeMB?: number;
  label?: string;
  required?: boolean;
}

export default function MultiImageUpload({
  initialImages = [],
  onImagesChange,
  directory = 'uploads',
  className = '',
  maxImages = 10,
  maxSizeMB = 5,
  label = 'Images',
  required = false,
}: MultiImageUploadProps) {
  const [images, setImages] = useState<ImageItem[]>(initialImages);
  const [error, setError] = useState<string | null>(null);
  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadFile, isUploading, progress } = useFileUpload();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Check if adding these files would exceed the maximum
    if (images.length + files.length > maxImages) {
      setError(`Vous ne pouvez pas ajouter plus de ${maxImages} images`);
      return;
    }

    setError(null);

    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Check file size
      const fileSizeMB = file.size / (1024 * 1024);
      if (fileSizeMB > maxSizeMB) {
        setError(`La taille du fichier ${file.name} dépasse la limite de ${maxSizeMB} MB`);
        continue;
      }
      
      // Check file type
      if (!file.type.startsWith('image/')) {
        setError(`Le fichier ${file.name} n'est pas une image`);
        continue;
      }
      
      try {
        setUploadingIndex(images.length + i);
        
        const url = await uploadFile(file, {
          directory,
          onSuccess: (url) => {
            setImages(prev => {
              const newImages = [...prev, { url, alt: file.name, order: prev.length }];
              // Call onImagesChange after state update
              setTimeout(() => onImagesChange(newImages), 0);
              return newImages;
            });
          },
          onError: (err) => {
            setError(`Erreur lors du téléchargement de ${file.name}: ${err.message}`);
          },
        });
      } catch (err) {
        // Error is handled by the hook
      }
    }
    
    setUploadingIndex(null);
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveImage = (index: number) => {
    setImages(prev => {
      const newImages = prev.filter((_, i) => i !== index).map((img, i) => ({
        ...img,
        order: i,
      }));
      // Call onImagesChange after state update
      setTimeout(() => onImagesChange(newImages), 0);
      return newImages;
    });
  };

  const handleMoveImage = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === images.length - 1)
    ) {
      return;
    }

    setImages(prev => {
      const newImages = [...prev];
      const targetIndex = direction === 'up' ? index - 1 : index + 1;

      // Swap the images
      [newImages[index], newImages[targetIndex]] = [newImages[targetIndex], newImages[index]];

      // Update the order
      newImages.forEach((img, i) => {
        img.order = i;
      });

      // Call onImagesChange after state update
      setTimeout(() => onImagesChange(newImages), 0);
      return newImages;
    });
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    
    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;
    
    // Check if adding these files would exceed the maximum
    if (images.length + files.length > maxImages) {
      setError(`Vous ne pouvez pas ajouter plus de ${maxImages} images`);
      return;
    }

    setError(null);

    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Check file size
      const fileSizeMB = file.size / (1024 * 1024);
      if (fileSizeMB > maxSizeMB) {
        setError(`La taille du fichier ${file.name} dépasse la limite de ${maxSizeMB} MB`);
        continue;
      }
      
      // Check file type
      if (!file.type.startsWith('image/')) {
        setError(`Le fichier ${file.name} n'est pas une image`);
        continue;
      }
      
      try {
        setUploadingIndex(images.length + i);
        
        const url = await uploadFile(file, {
          directory,
          onSuccess: (url) => {
            setImages(prev => {
              const newImages = [...prev, { url, alt: file.name, order: prev.length }];
              // Call onImagesChange after state update
              setTimeout(() => onImagesChange(newImages), 0);
              return newImages;
            });
          },
          onError: (err) => {
            setError(`Erreur lors du téléchargement de ${file.name}: ${err.message}`);
          },
        });
      } catch (err) {
        // Error is handled by the hook
      }
    }
    
    setUploadingIndex(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-4">
        {images.map((image, index) => (
          <div key={index} className="relative aspect-square rounded-lg overflow-hidden border border-gray-300 dark:border-gray-600">
            <Image
              src={image.url}
              alt={image.alt || `Image ${index + 1}`}
              fill
              style={{ objectFit: 'cover' }}
              className="w-full h-full"
            />
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
              <motion.button
                type="button"
                onClick={() => handleMoveImage(index, 'up')}
                className="p-2 bg-blue-600 text-white rounded-full disabled:opacity-50"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                disabled={index === 0}
              >
                <FaArrowUp />
              </motion.button>
              <motion.button
                type="button"
                onClick={() => handleMoveImage(index, 'down')}
                className="p-2 bg-blue-600 text-white rounded-full disabled:opacity-50"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                disabled={index === images.length - 1}
              >
                <FaArrowDown />
              </motion.button>
              <motion.button
                type="button"
                onClick={() => handleRemoveImage(index)}
                className="p-2 bg-red-600 text-white rounded-full"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <FaTrash />
              </motion.button>
            </div>
          </div>
        ))}
        
        {/* Upload placeholder */}
        {images.length < maxImages && (
          <div
            className={`relative aspect-square border-2 border-dashed rounded-lg ${
              error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } overflow-hidden`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            <div className="flex flex-col items-center justify-center p-4 h-full">
              {uploadingIndex !== null ? (
                <div className="text-center">
                  <FaSpinner className="animate-spin text-2xl text-primary mx-auto mb-2" />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Téléchargement... {Math.round(progress)}%
                  </p>
                </div>
              ) : (
                <>
                  <FaImage className="text-2xl text-gray-400 mb-2" />
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    Ajouter une image
                  </p>
                </>
              )}
            </div>
            
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              multiple
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={uploadingIndex !== null}
            />
          </div>
        )}
      </div>
      
      <div className="text-xs text-gray-500 dark:text-gray-400">
        {images.length} / {maxImages} images (max {maxSizeMB} MB par image)
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
}
