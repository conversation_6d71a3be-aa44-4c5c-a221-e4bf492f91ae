'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaSearch, 
  FaUser, 
  FaShoppingCart, 
  FaBars, 
  FaTimes,
  FaShippingFast,
  FaShieldAlt,
  FaGlobe,
  FaCheckCircle,
  FaChevronDown
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';

interface MegaMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const ShopMegaMenu: React.FC<MegaMenuProps> = ({ isOpen, onClose }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="absolute top-full left-0 w-full bg-white shadow-xl border-t border-gray-100 z-50"
          onMouseLeave={onClose}
        >
          <div className="container mx-auto px-6 py-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* Categories */}
              <div>
                <h3 className="font-semibold text-charcoal mb-4">Catégories</h3>
                <ul className="space-y-2">
                  <li><Link href="/products?category=eclairage" className="text-gray-600 hover:text-moonelec-red transition-colors">Éclairage</Link></li>
                  <li><Link href="/products?category=cables" className="text-gray-600 hover:text-moonelec-red transition-colors">Câbles</Link></li>
                  <li><Link href="/products?category=prises" className="text-gray-600 hover:text-moonelec-red transition-colors">Prises & Interrupteurs</Link></li>
                  <li><Link href="/products?category=protection" className="text-gray-600 hover:text-moonelec-red transition-colors">Protection</Link></li>
                </ul>
              </div>

              {/* Brands */}
              <div>
                <h3 className="font-semibold text-charcoal mb-4">Marques</h3>
                <ul className="space-y-2">
                  <li><Link href="/products?brand=schneider" className="text-gray-600 hover:text-moonelec-red transition-colors">Schneider Electric</Link></li>
                  <li><Link href="/products?brand=legrand" className="text-gray-600 hover:text-moonelec-red transition-colors">Legrand</Link></li>
                  <li><Link href="/products?brand=abb" className="text-gray-600 hover:text-moonelec-red transition-colors">ABB</Link></li>
                  <li><Link href="/products?brand=siemens" className="text-gray-600 hover:text-moonelec-red transition-colors">Siemens</Link></li>
                </ul>
              </div>

              {/* Solutions */}
              <div>
                <h3 className="font-semibold text-charcoal mb-4">Solutions</h3>
                <ul className="space-y-2">
                  <li><Link href="/solutions/residential" className="text-gray-600 hover:text-moonelec-red transition-colors">Résidentiel</Link></li>
                  <li><Link href="/solutions/commercial" className="text-gray-600 hover:text-moonelec-red transition-colors">Commercial</Link></li>
                  <li><Link href="/solutions/industrial" className="text-gray-600 hover:text-moonelec-red transition-colors">Industriel</Link></li>
                  <li><Link href="/solutions/smart-home" className="text-gray-600 hover:text-moonelec-red transition-colors">Maison Intelligente</Link></li>
                </ul>
              </div>

              {/* Featured Product */}
              <div className="bg-light-gray p-6 rounded-lg">
                <h3 className="font-semibold text-charcoal mb-2">Produit Vedette</h3>
                <div className="aspect-square bg-white rounded-md mb-3 flex items-center justify-center">
                  <div className="w-16 h-16 bg-gray-200 rounded"></div>
                </div>
                <p className="text-sm text-gray-600 mb-3">Nouveau système d'éclairage LED intelligent</p>
                <Link href="/products/featured" className="btn btn-primary text-sm py-2 px-4">
                  Découvrir
                </Link>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const FeatureBar: React.FC = () => {
  const features = [
    {
      icon: <FaShippingFast className="text-2xl" />,
      title: "Livraison Gratuite",
      description: "Sur toutes commandes de plus de 500 MAD"
    },
    {
      icon: <FaCheckCircle className="text-2xl" />,
      title: "Garantie 30 Jours",
      description: "Garantie de remboursement de 30 jours"
    },
    {
      icon: <FaGlobe className="text-2xl" />,
      title: "Livraison Nationale",
      description: "Livraison dans tout le Maroc"
    },
    {
      icon: <FaShieldAlt className="text-2xl" />,
      title: "Paiement Sécurisé",
      description: "100% sécurisé et protégé"
    }
  ];

  return (
    <div className="bg-white border-t border-gray-100 py-6">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-4 group cursor-pointer"
            >
              <div className="text-gray-400 group-hover:text-moonelec-red transition-colors duration-300">
                {feature.icon}
              </div>
              <div>
                <h4 className="font-semibold text-charcoal text-sm">{feature.title}</h4>
                <p className="text-xs text-gray-600">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function ModernHeader() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isShopMenuOpen, setIsShopMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { user, signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  const navigation = [
    { name: 'Accueil', href: '/' },
    { name: 'À Propos', href: '/about' },
    { 
      name: 'Boutique', 
      href: '/products',
      hasDropdown: true,
      onMouseEnter: () => setIsShopMenuOpen(true),
      onMouseLeave: () => setIsShopMenuOpen(false)
    },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <>
      {/* Main Header */}
      <header 
        className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${
          isScrolled 
            ? 'bg-white/95 backdrop-blur-md shadow-lg' 
            : 'bg-white'
        }`}
      >
        <div className="container mx-auto px-6">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-moonelec-red rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">M</span>
              </div>
              <span className="text-2xl font-bold text-charcoal font-heading">
                Moonelec
              </span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navigation.map((item) => (
                <div
                  key={item.name}
                  className="relative"
                  onMouseEnter={item.onMouseEnter}
                  onMouseLeave={item.onMouseLeave}
                >
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-1 font-medium transition-colors duration-200 ${
                      pathname === item.href
                        ? 'text-moonelec-red'
                        : 'text-charcoal hover:text-moonelec-red'
                    }`}
                  >
                    <span>{item.name}</span>
                    {item.hasDropdown && (
                      <FaChevronDown className="text-xs" />
                    )}
                  </Link>
                </div>
              ))}
            </nav>

            {/* Search & Actions */}
            <div className="flex items-center space-x-4">
              {/* Search */}
              <form onSubmit={handleSearch} className="hidden md:flex">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Rechercher des produits..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-64 pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent"
                  />
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
              </form>

              {/* User Menu */}
              {user ? (
                <div className="flex items-center space-x-2">
                  <Link
                    href={`/${user.role.toLowerCase()}/dashboard`}
                    className="p-2 text-gray-600 hover:text-moonelec-red transition-colors"
                  >
                    <FaUser className="text-xl" />
                  </Link>
                  <button
                    onClick={() => signOut()}
                    className="text-sm text-gray-600 hover:text-moonelec-red transition-colors"
                  >
                    Déconnexion
                  </button>
                </div>
              ) : (
                <Link
                  href="/auth/signin"
                  className="p-2 text-gray-600 hover:text-moonelec-red transition-colors"
                >
                  <FaUser className="text-xl" />
                </Link>
              )}

              {/* Cart */}
              <Link
                href="/cart"
                className="relative p-2 text-gray-600 hover:text-moonelec-red transition-colors"
              >
                <FaShoppingCart className="text-xl" />
                <span className="absolute -top-1 -right-1 bg-moonelec-red text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  0
                </span>
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 text-gray-600 hover:text-moonelec-red transition-colors"
              >
                {isMobileMenuOpen ? <FaTimes className="text-xl" /> : <FaBars className="text-xl" />}
              </button>
            </div>
          </div>
        </div>

        {/* Shop Mega Menu */}
        <ShopMegaMenu 
          isOpen={isShopMenuOpen} 
          onClose={() => setIsShopMenuOpen(false)} 
        />

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white border-t border-gray-100"
            >
              <div className="container mx-auto px-6 py-4">
                {/* Mobile Search */}
                <form onSubmit={handleSearch} className="mb-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Rechercher..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue"
                    />
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </form>

                {/* Mobile Navigation */}
                <nav className="space-y-2">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`block py-2 font-medium transition-colors ${
                        pathname === item.href
                          ? 'text-moonelec-red'
                          : 'text-charcoal hover:text-moonelec-red'
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* Feature Bar */}
      <div className="pt-20">
        <FeatureBar />
      </div>
    </>
  );
}
