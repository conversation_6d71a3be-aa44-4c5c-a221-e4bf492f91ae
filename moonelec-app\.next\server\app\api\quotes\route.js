/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/quotes/route";
exports.ids = ["app/api/quotes/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquotes%2Froute&page=%2Fapi%2Fquotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotes%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquotes%2Froute&page=%2Fapi%2Fquotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotes%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_quotes_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/quotes/route.ts */ \"(rsc)/./src/app/api/quotes/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/quotes/route\",\n        pathname: \"/api/quotes\",\n        filename: \"route\",\n        bundlePath: \"app/api/quotes/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\quotes\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_quotes_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquotes%2Froute&page=%2Fapi%2Fquotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotes%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/quotes/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/quotes/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_quotes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/quotes */ \"(rsc)/./src/lib/quotes.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./src/lib/auth-options.ts\");\n/* harmony import */ var _lib_mobile_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/mobile-auth */ \"(rsc)/./src/lib/mobile-auth.ts\");\n\n\n\n\n\n\n\n// GET /api/quotes - Récupérer tous les devis\nasync function GET(req) {\n    try {\n        console.log('📋 Quotes API - Checking authentication...');\n        // Check for mobile authentication first\n        const mobileUser = await (0,_lib_mobile_auth__WEBPACK_IMPORTED_MODULE_6__.getMobileUserFromRequest)(req);\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n        console.log('📋 Quotes API - Auth results:', {\n            mobileUser: mobileUser ? {\n                id: mobileUser.id,\n                role: mobileUser.role\n            } : null,\n            session: session ? {\n                id: session.user?.id,\n                role: session.user?.role\n            } : null\n        });\n        if (!mobileUser && !session) {\n            console.log('📋 Quotes API - No authentication found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Use mobile user data if available, otherwise use session\n        const user = mobileUser || session?.user;\n        console.log('📋 Quotes API - Using user:', {\n            id: user?.id,\n            role: user?.role\n        });\n        const searchParams = req.nextUrl.searchParams;\n        const search = searchParams.get('search') || undefined;\n        const status = searchParams.get('status');\n        const page = searchParams.has('page') ? parseInt(searchParams.get('page')) : 1;\n        const limit = searchParams.has('limit') ? parseInt(searchParams.get('limit')) : 10;\n        const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')) : (page - 1) * limit;\n        const take = searchParams.has('take') ? parseInt(searchParams.get('take')) : limit;\n        // Filtrer par client si l'utilisateur est un client\n        let clientId = undefined;\n        if (user?.role === 'CLIENT' && user?.clientId) {\n            clientId = user.clientId;\n        } else if (searchParams.has('clientId')) {\n            clientId = searchParams.get('clientId') || undefined;\n        }\n        // Utiliser la fonction existante pour récupérer les devis\n        const { quotes, total } = await (0,_lib_quotes__WEBPACK_IMPORTED_MODULE_2__.getQuotes)({\n            clientId,\n            status,\n            search,\n            skip,\n            take\n        });\n        // Pour le tableau de bord, nous avons besoin d'informations supplémentaires\n        // Si le paramètre 'dashboard' est présent, enrichir les données\n        if (searchParams.get('dashboard') === 'true') {\n            // Récupérer les informations détaillées pour chaque devis\n            const enrichedQuotes = await Promise.all(quotes.map(async (quote)=>{\n                // Récupérer les informations du client\n                const client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.client.findUnique({\n                    where: {\n                        id: quote.clientId\n                    },\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                firstname: true,\n                                lastname: true,\n                                email: true,\n                                telephone: true\n                            }\n                        }\n                    }\n                });\n                // Récupérer les éléments du devis avec les informations des produits\n                const items = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.quoteitem.findMany({\n                    where: {\n                        quoteId: quote.id\n                    },\n                    include: {\n                        product: {\n                            select: {\n                                name: true,\n                                reference: true\n                            }\n                        }\n                    }\n                });\n                return {\n                    ...quote,\n                    client,\n                    items\n                };\n            }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                quotes: enrichedQuotes,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            quotes,\n            total,\n            page,\n            limit,\n            totalPages: Math.ceil(total / limit)\n        });\n    } catch (error) {\n        console.error('Error fetching quotes:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch quotes'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/quotes - Créer un nouveau devis\nasync function POST(req) {\n    try {\n        // Check for mobile authentication first\n        const mobileUser = await (0,_lib_mobile_auth__WEBPACK_IMPORTED_MODULE_6__.getMobileUserFromRequest)(req);\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n        if (!mobileUser && !session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Use mobile user data if available, otherwise use session\n        const user = mobileUser || session?.user;\n        const body = await req.json();\n        const { items, notes } = body;\n        // Vérifier que les items sont fournis\n        if (!items || !Array.isArray(items) || items.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Items are required and must be a non-empty array'\n            }, {\n                status: 400\n            });\n        }\n        // Déterminer l'ID du client\n        let clientId;\n        let createdByAdminId = undefined;\n        if (user?.role === 'CLIENT') {\n            // Si l'utilisateur est un client, utiliser son ID client\n            if (!user.clientId) {\n                console.error('Client ID not found in user:', user);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Client ID not found in user data. Please contact support.'\n                }, {\n                    status: 400\n                });\n            }\n            clientId = user.clientId;\n        } else if (user?.role === 'ADMIN') {\n            // Si l'utilisateur est un admin\n            if (body.clientId) {\n                // Si un clientId est fourni, l'utiliser\n                clientId = body.clientId;\n            } else if (user.clientId) {\n                // Si l'admin a aussi un compte client, utiliser cet ID\n                clientId = user.clientId;\n            } else {\n                // Créer un devis pour le premier client trouvé (pour les tests)\n                const firstClient = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.client.findFirst();\n                if (!firstClient) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'No client found in the system. Please create a client first.'\n                    }, {\n                        status: 400\n                    });\n                }\n                clientId = firstClient.id;\n            }\n            // Enregistrer l'ID de l'admin qui crée le devis\n            createdByAdminId = user.adminId;\n        } else {\n            // Les commerciaux ne peuvent pas créer de devis directement\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized to create quotes'\n            }, {\n                status: 403\n            });\n        }\n        // Créer le devis\n        const quote = await (0,_lib_quotes__WEBPACK_IMPORTED_MODULE_2__.createQuote)({\n            clientId,\n            notes,\n            validUntil: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getDefaultQuoteExpiryDate)(),\n            items: items.map((item)=>({\n                    productId: item.productId,\n                    quantity: item.quantity\n                })),\n            createdByAdminId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            quote\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating quote:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to create quote'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/quotes/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconsole.log('🔧 Auth options module loaded');\nconsole.log('🔧 Creating auth options...');\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                username: {\n                    label: \"Nom d'utilisateur\",\n                    type: 'text'\n                },\n                password: {\n                    label: 'Mot de passe',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                console.log('🔍 AUTHORIZE FUNCTION CALLED!');\n                console.log('🔍 Auth attempt:', {\n                    username: credentials?.username,\n                    hasPassword: !!credentials?.password\n                });\n                if (!credentials?.username || !credentials?.password) {\n                    console.log('❌ Missing credentials');\n                    return null;\n                }\n                try {\n                    // Rechercher l'utilisateur par nom d'utilisateur\n                    console.log('🔍 Looking for user:', credentials.username);\n                    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.findUserByUsername)(credentials.username);\n                    // Vérifier si l'utilisateur existe\n                    if (!user) {\n                        console.log('❌ User not found:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ User found:', {\n                        id: user.id,\n                        username: user.username,\n                        role: user.role\n                    });\n                    // Vérifier le mot de passe\n                    console.log('🔍 Verifying password...');\n                    const isPasswordValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log('❌ Invalid password for user:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ Password valid for user:', credentials.username);\n                    // Retourner les données de l'utilisateur sans le mot de passe\n                    const userObject = {\n                        id: user.id,\n                        email: user.email,\n                        username: user.username,\n                        name: `${user.firstname} ${user.lastname}`,\n                        firstname: user.firstname,\n                        lastname: user.lastname,\n                        role: user.role,\n                        clientId: user.client?.id,\n                        commercialId: user.commercial?.id,\n                        adminId: user.admin?.id\n                    };\n                    console.log('✅ Returning user object:', userObject);\n                    return userObject;\n                } catch (error) {\n                    console.error('❌ Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.username = user.username;\n                token.role = user.role;\n                token.firstname = user.firstname;\n                token.lastname = user.lastname;\n                token.clientId = user.clientId;\n                token.commercialId = user.commercialId;\n                token.adminId = user.adminId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.username = token.username;\n                session.user.role = token.role;\n                session.user.firstname = token.firstname;\n                session.user.lastname = token.lastname;\n                session.user.clientId = token.clientId;\n                session.user.commercialId = token.commercialId;\n                session.user.adminId = token.adminId;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signOut: '/auth/signout',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgtb3B0aW9ucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0U7QUFDRjtBQUdoRUcsUUFBUUMsR0FBRyxDQUFDO0FBRVpELFFBQVFDLEdBQUcsQ0FBQztBQUVMLE1BQU1DLGNBQStCO0lBQzFDQyxXQUFXO1FBQ1ROLDJFQUFtQkEsQ0FBQztZQUNsQk8sTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxVQUFVO29CQUFFQyxPQUFPO29CQUFxQkMsTUFBTTtnQkFBTztnQkFDckRDLFVBQVU7b0JBQUVGLE9BQU87b0JBQWdCQyxNQUFNO2dCQUFXO1lBQ3REO1lBQ0EsTUFBTUUsV0FBVUwsV0FBVztnQkFDekJMLFFBQVFDLEdBQUcsQ0FBQztnQkFDWkQsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQjtvQkFBRUssVUFBVUQsYUFBYUM7b0JBQVVLLGFBQWEsQ0FBQyxDQUFDTixhQUFhSTtnQkFBUztnQkFFeEcsSUFBSSxDQUFDSixhQUFhQyxZQUFZLENBQUNELGFBQWFJLFVBQVU7b0JBQ3BEVCxRQUFRQyxHQUFHLENBQUM7b0JBQ1osT0FBTztnQkFDVDtnQkFFQSxJQUFJO29CQUNGLGlEQUFpRDtvQkFDakRELFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JJLFlBQVlDLFFBQVE7b0JBQ3hELE1BQU1NLE9BQU8sTUFBTWQsNkRBQWtCQSxDQUFDTyxZQUFZQyxRQUFRO29CQUUxRCxtQ0FBbUM7b0JBQ25DLElBQUksQ0FBQ00sTUFBTTt3QkFDVFosUUFBUUMsR0FBRyxDQUFDLHFCQUFxQkksWUFBWUMsUUFBUTt3QkFDckQsT0FBTztvQkFDVDtvQkFFQU4sUUFBUUMsR0FBRyxDQUFDLGlCQUFpQjt3QkFBRVksSUFBSUQsS0FBS0MsRUFBRTt3QkFBRVAsVUFBVU0sS0FBS04sUUFBUTt3QkFBRVEsTUFBTUYsS0FBS0UsSUFBSTtvQkFBQztvQkFFckYsMkJBQTJCO29CQUMzQmQsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU1jLGtCQUFrQixNQUFNaEIseURBQWNBLENBQzFDTSxZQUFZSSxRQUFRLEVBQ3BCRyxLQUFLSCxRQUFRO29CQUdmLElBQUksQ0FBQ00saUJBQWlCO3dCQUNwQmYsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0ksWUFBWUMsUUFBUTt3QkFDaEUsT0FBTztvQkFDVDtvQkFFQU4sUUFBUUMsR0FBRyxDQUFDLDhCQUE4QkksWUFBWUMsUUFBUTtvQkFFOUQsOERBQThEO29CQUM5RCxNQUFNVSxhQUFhO3dCQUNqQkgsSUFBSUQsS0FBS0MsRUFBRTt3QkFDWEksT0FBT0wsS0FBS0ssS0FBSzt3QkFDakJYLFVBQVVNLEtBQUtOLFFBQVE7d0JBQ3ZCRixNQUFNLEdBQUdRLEtBQUtNLFNBQVMsQ0FBQyxDQUFDLEVBQUVOLEtBQUtPLFFBQVEsRUFBRTt3QkFDMUNELFdBQVdOLEtBQUtNLFNBQVM7d0JBQ3pCQyxVQUFVUCxLQUFLTyxRQUFRO3dCQUN2QkwsTUFBTUYsS0FBS0UsSUFBSTt3QkFDZk0sVUFBVVIsS0FBS1MsTUFBTSxFQUFFUjt3QkFDdkJTLGNBQWNWLEtBQUtXLFVBQVUsRUFBRVY7d0JBQy9CVyxTQUFTWixLQUFLYSxLQUFLLEVBQUVaO29CQUN2QjtvQkFFQWIsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QmU7b0JBQ3hDLE9BQU9BO2dCQUNULEVBQUUsT0FBT1UsT0FBTztvQkFDZDFCLFFBQVEwQixLQUFLLENBQUMsaUJBQWlCQTtvQkFDL0IsT0FBTztnQkFDVDtZQUNGO1FBQ0Y7S0FDRDtJQUNEQyxXQUFXO1FBQ1QsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUVqQixJQUFJLEVBQUU7WUFDdkIsSUFBSUEsTUFBTTtnQkFDUmlCLE1BQU1oQixFQUFFLEdBQUdELEtBQUtDLEVBQUU7Z0JBQ2xCZ0IsTUFBTXZCLFFBQVEsR0FBR00sS0FBS04sUUFBUTtnQkFDOUJ1QixNQUFNZixJQUFJLEdBQUdGLEtBQUtFLElBQUk7Z0JBQ3RCZSxNQUFNWCxTQUFTLEdBQUdOLEtBQUtNLFNBQVM7Z0JBQ2hDVyxNQUFNVixRQUFRLEdBQUdQLEtBQUtPLFFBQVE7Z0JBQzlCVSxNQUFNVCxRQUFRLEdBQUdSLEtBQUtRLFFBQVE7Z0JBQzlCUyxNQUFNUCxZQUFZLEdBQUdWLEtBQUtVLFlBQVk7Z0JBQ3RDTyxNQUFNTCxPQUFPLEdBQUdaLEtBQUtZLE9BQU87WUFDOUI7WUFDQSxPQUFPSztRQUNUO1FBQ0EsTUFBTUMsU0FBUSxFQUFFQSxPQUFPLEVBQUVELEtBQUssRUFBRTtZQUM5QixJQUFJQSxTQUFTQyxRQUFRbEIsSUFBSSxFQUFFO2dCQUN6QmtCLFFBQVFsQixJQUFJLENBQUNDLEVBQUUsR0FBR2dCLE1BQU1oQixFQUFFO2dCQUMxQmlCLFFBQVFsQixJQUFJLENBQUNOLFFBQVEsR0FBR3VCLE1BQU12QixRQUFRO2dCQUN0Q3dCLFFBQVFsQixJQUFJLENBQUNFLElBQUksR0FBR2UsTUFBTWYsSUFBSTtnQkFDOUJnQixRQUFRbEIsSUFBSSxDQUFDTSxTQUFTLEdBQUdXLE1BQU1YLFNBQVM7Z0JBQ3hDWSxRQUFRbEIsSUFBSSxDQUFDTyxRQUFRLEdBQUdVLE1BQU1WLFFBQVE7Z0JBQ3RDVyxRQUFRbEIsSUFBSSxDQUFDUSxRQUFRLEdBQUdTLE1BQU1ULFFBQVE7Z0JBQ3RDVSxRQUFRbEIsSUFBSSxDQUFDVSxZQUFZLEdBQUdPLE1BQU1QLFlBQVk7Z0JBQzlDUSxRQUFRbEIsSUFBSSxDQUFDWSxPQUFPLEdBQUdLLE1BQU1MLE9BQU87WUFDdEM7WUFDQSxPQUFPTTtRQUNUO0lBQ0Y7SUFDQUMsT0FBTztRQUNMQyxRQUFRO1FBQ1JDLFNBQVM7UUFDVFAsT0FBTztJQUNUO0lBQ0FJLFNBQVM7UUFDUEksVUFBVTtRQUNWQyxRQUFRLEtBQUssS0FBSyxLQUFLO0lBQ3pCO0lBQ0FDLFFBQVFDLFFBQVFDLEdBQUcsQ0FBQ0MsZUFBZTtJQUNuQ0MsT0FBTztBQUNULEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxhdXRoLW9wdGlvbnMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENyZWRlbnRpYWxzUHJvdmlkZXIgZnJvbSAnbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscyc7XG5pbXBvcnQgeyBmaW5kVXNlckJ5VXNlcm5hbWUsIHZlcmlmeVBhc3N3b3JkIH0gZnJvbSAnQC9saWIvYXV0aCc7XG5pbXBvcnQgeyBOZXh0QXV0aE9wdGlvbnMgfSBmcm9tICduZXh0LWF1dGgnO1xuXG5jb25zb2xlLmxvZygn8J+UpyBBdXRoIG9wdGlvbnMgbW9kdWxlIGxvYWRlZCcpO1xuXG5jb25zb2xlLmxvZygn8J+UpyBDcmVhdGluZyBhdXRoIG9wdGlvbnMuLi4nKTtcblxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XG4gIHByb3ZpZGVyczogW1xuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xuICAgICAgbmFtZTogJ0NyZWRlbnRpYWxzJyxcbiAgICAgIGNyZWRlbnRpYWxzOiB7XG4gICAgICAgIHVzZXJuYW1lOiB7IGxhYmVsOiBcIk5vbSBkJ3V0aWxpc2F0ZXVyXCIsIHR5cGU6ICd0ZXh0JyB9LFxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogJ01vdCBkZSBwYXNzZScsIHR5cGU6ICdwYXNzd29yZCcgfSxcbiAgICAgIH0sXG4gICAgICBhc3luYyBhdXRob3JpemUoY3JlZGVudGlhbHMpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gQVVUSE9SSVpFIEZVTkNUSU9OIENBTExFRCEnKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gQXV0aCBhdHRlbXB0OicsIHsgdXNlcm5hbWU6IGNyZWRlbnRpYWxzPy51c2VybmFtZSwgaGFzUGFzc3dvcmQ6ICEhY3JlZGVudGlhbHM/LnBhc3N3b3JkIH0pO1xuXG4gICAgICAgIGlmICghY3JlZGVudGlhbHM/LnVzZXJuYW1lIHx8ICFjcmVkZW50aWFscz8ucGFzc3dvcmQpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4p2MIE1pc3NpbmcgY3JlZGVudGlhbHMnKTtcbiAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gUmVjaGVyY2hlciBsJ3V0aWxpc2F0ZXVyIHBhciBub20gZCd1dGlsaXNhdGV1clxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIExvb2tpbmcgZm9yIHVzZXI6JywgY3JlZGVudGlhbHMudXNlcm5hbWUpO1xuICAgICAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBmaW5kVXNlckJ5VXNlcm5hbWUoY3JlZGVudGlhbHMudXNlcm5hbWUpO1xuXG4gICAgICAgICAgLy8gVsOpcmlmaWVyIHNpIGwndXRpbGlzYXRldXIgZXhpc3RlXG4gICAgICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4p2MIFVzZXIgbm90IGZvdW5kOicsIGNyZWRlbnRpYWxzLnVzZXJuYW1lKTtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgVXNlciBmb3VuZDonLCB7IGlkOiB1c2VyLmlkLCB1c2VybmFtZTogdXNlci51c2VybmFtZSwgcm9sZTogdXNlci5yb2xlIH0pO1xuXG4gICAgICAgICAgLy8gVsOpcmlmaWVyIGxlIG1vdCBkZSBwYXNzZVxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFZlcmlmeWluZyBwYXNzd29yZC4uLicpO1xuICAgICAgICAgIGNvbnN0IGlzUGFzc3dvcmRWYWxpZCA9IGF3YWl0IHZlcmlmeVBhc3N3b3JkKFxuICAgICAgICAgICAgY3JlZGVudGlhbHMucGFzc3dvcmQsXG4gICAgICAgICAgICB1c2VyLnBhc3N3b3JkXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIGlmICghaXNQYXNzd29yZFZhbGlkKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4p2MIEludmFsaWQgcGFzc3dvcmQgZm9yIHVzZXI6JywgY3JlZGVudGlhbHMudXNlcm5hbWUpO1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBQYXNzd29yZCB2YWxpZCBmb3IgdXNlcjonLCBjcmVkZW50aWFscy51c2VybmFtZSk7XG5cbiAgICAgICAgICAvLyBSZXRvdXJuZXIgbGVzIGRvbm7DqWVzIGRlIGwndXRpbGlzYXRldXIgc2FucyBsZSBtb3QgZGUgcGFzc2VcbiAgICAgICAgICBjb25zdCB1c2VyT2JqZWN0ID0ge1xuICAgICAgICAgICAgaWQ6IHVzZXIuaWQsXG4gICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICAgIHVzZXJuYW1lOiB1c2VyLnVzZXJuYW1lLFxuICAgICAgICAgICAgbmFtZTogYCR7dXNlci5maXJzdG5hbWV9ICR7dXNlci5sYXN0bmFtZX1gLFxuICAgICAgICAgICAgZmlyc3RuYW1lOiB1c2VyLmZpcnN0bmFtZSxcbiAgICAgICAgICAgIGxhc3RuYW1lOiB1c2VyLmxhc3RuYW1lLFxuICAgICAgICAgICAgcm9sZTogdXNlci5yb2xlLFxuICAgICAgICAgICAgY2xpZW50SWQ6IHVzZXIuY2xpZW50Py5pZCxcbiAgICAgICAgICAgIGNvbW1lcmNpYWxJZDogdXNlci5jb21tZXJjaWFsPy5pZCxcbiAgICAgICAgICAgIGFkbWluSWQ6IHVzZXIuYWRtaW4/LmlkLFxuICAgICAgICAgIH07XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFJldHVybmluZyB1c2VyIG9iamVjdDonLCB1c2VyT2JqZWN0KTtcbiAgICAgICAgICByZXR1cm4gdXNlck9iamVjdDtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQXV0aCBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSksXG4gIF0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIGp3dCh7IHRva2VuLCB1c2VyIH0pIHtcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIHRva2VuLmlkID0gdXNlci5pZDtcbiAgICAgICAgdG9rZW4udXNlcm5hbWUgPSB1c2VyLnVzZXJuYW1lO1xuICAgICAgICB0b2tlbi5yb2xlID0gdXNlci5yb2xlO1xuICAgICAgICB0b2tlbi5maXJzdG5hbWUgPSB1c2VyLmZpcnN0bmFtZTtcbiAgICAgICAgdG9rZW4ubGFzdG5hbWUgPSB1c2VyLmxhc3RuYW1lO1xuICAgICAgICB0b2tlbi5jbGllbnRJZCA9IHVzZXIuY2xpZW50SWQ7XG4gICAgICAgIHRva2VuLmNvbW1lcmNpYWxJZCA9IHVzZXIuY29tbWVyY2lhbElkO1xuICAgICAgICB0b2tlbi5hZG1pbklkID0gdXNlci5hZG1pbklkO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHRva2VuO1xuICAgIH0sXG4gICAgYXN5bmMgc2Vzc2lvbih7IHNlc3Npb24sIHRva2VuIH0pIHtcbiAgICAgIGlmICh0b2tlbiAmJiBzZXNzaW9uLnVzZXIpIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdG9rZW4uaWQgYXMgc3RyaW5nO1xuICAgICAgICBzZXNzaW9uLnVzZXIudXNlcm5hbWUgPSB0b2tlbi51c2VybmFtZSBhcyBzdHJpbmc7XG4gICAgICAgIHNlc3Npb24udXNlci5yb2xlID0gdG9rZW4ucm9sZSBhcyBzdHJpbmc7XG4gICAgICAgIHNlc3Npb24udXNlci5maXJzdG5hbWUgPSB0b2tlbi5maXJzdG5hbWUgYXMgc3RyaW5nO1xuICAgICAgICBzZXNzaW9uLnVzZXIubGFzdG5hbWUgPSB0b2tlbi5sYXN0bmFtZSBhcyBzdHJpbmc7XG4gICAgICAgIHNlc3Npb24udXNlci5jbGllbnRJZCA9IHRva2VuLmNsaWVudElkIGFzIHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmNvbW1lcmNpYWxJZCA9IHRva2VuLmNvbW1lcmNpYWxJZCBhcyBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gICAgICAgIHNlc3Npb24udXNlci5hZG1pbklkID0gdG9rZW4uYWRtaW5JZCBhcyBzdHJpbmcgfCB1bmRlZmluZWQ7XG4gICAgICB9XG4gICAgICByZXR1cm4gc2Vzc2lvbjtcbiAgICB9LFxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogJy9hdXRoL3NpZ25pbicsXG4gICAgc2lnbk91dDogJy9hdXRoL3NpZ25vdXQnLFxuICAgIGVycm9yOiAnL2F1dGgvZXJyb3InLFxuICB9LFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6ICdqd3QnLFxuICAgIG1heEFnZTogMzAgKiAyNCAqIDYwICogNjAsIC8vIDMwIGpvdXJzXG4gIH0sXG4gIHNlY3JldDogcHJvY2Vzcy5lbnYuTkVYVEFVVEhfU0VDUkVULFxuICBkZWJ1ZzogdHJ1ZSxcbn07XG4iXSwibmFtZXMiOlsiQ3JlZGVudGlhbHNQcm92aWRlciIsImZpbmRVc2VyQnlVc2VybmFtZSIsInZlcmlmeVBhc3N3b3JkIiwiY29uc29sZSIsImxvZyIsImF1dGhPcHRpb25zIiwicHJvdmlkZXJzIiwibmFtZSIsImNyZWRlbnRpYWxzIiwidXNlcm5hbWUiLCJsYWJlbCIsInR5cGUiLCJwYXNzd29yZCIsImF1dGhvcml6ZSIsImhhc1Bhc3N3b3JkIiwidXNlciIsImlkIiwicm9sZSIsImlzUGFzc3dvcmRWYWxpZCIsInVzZXJPYmplY3QiLCJlbWFpbCIsImZpcnN0bmFtZSIsImxhc3RuYW1lIiwiY2xpZW50SWQiLCJjbGllbnQiLCJjb21tZXJjaWFsSWQiLCJjb21tZXJjaWFsIiwiYWRtaW5JZCIsImFkbWluIiwiZXJyb3IiLCJjYWxsYmFja3MiLCJqd3QiLCJ0b2tlbiIsInNlc3Npb24iLCJwYWdlcyIsInNpZ25JbiIsInNpZ25PdXQiLCJzdHJhdGVneSIsIm1heEFnZSIsInNlY3JldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUQVVUSF9TRUNSRVQiLCJkZWJ1ZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   createClientUser: () => (/* binding */ createClientUser),\n/* harmony export */   createCommercialUser: () => (/* binding */ createCommercialUser),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Fonction pour hacher un mot de passe\nasync function hashPassword(password) {\n    const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].genSalt(10);\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, salt);\n}\n// Fonction pour vérifier un mot de passe\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Fonction pour créer un utilisateur client\nasync function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.CLIENT\n            }\n        });\n        // Créer le profil client associé\n        const client = await tx.client.create({\n            data: {\n                userId: user.id,\n                company_name\n            }\n        });\n        return {\n            user,\n            client\n        };\n    });\n}\n// Fonction pour créer un utilisateur commercial\nasync function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.COMMERCIAL\n            }\n        });\n        // Créer le profil commercial associé\n        const commercial = await tx.commercial.create({\n            data: {\n                userId: user.id,\n                profile_photo\n            }\n        });\n        return {\n            user,\n            commercial\n        };\n    });\n}\n// Fonction pour créer un utilisateur administrateur\nasync function createAdminUser({ email, username, password, lastname, firstname, telephone }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN\n            }\n        });\n        // Créer le profil admin associé\n        const admin = await tx.admin.create({\n            data: {\n                userId: user.id\n            }\n        });\n        return {\n            user,\n            admin\n        };\n    });\n}\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nasync function findUserByUsername(username) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            username\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n// Fonction pour trouver un utilisateur par son email\nasync function findUserByEmail(email) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            email\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mobile-auth.ts":
/*!********************************!*\
  !*** ./src/lib/mobile-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMobileUserFromRequest: () => (/* binding */ getMobileUserFromRequest),\n/* harmony export */   isMobileRequest: () => (/* binding */ isMobileRequest),\n/* harmony export */   verifyMobileToken: () => (/* binding */ verifyMobileToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function verifyMobileToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            console.log('🔐 No valid authorization header found');\n            return null;\n        }\n        const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n        console.log('🔐 Mobile token received (length):', token.length);\n        // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET\n        let decoded;\n        try {\n            const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';\n            console.log('🔐 Trying JWT_SECRET for token verification');\n            decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n            console.log('🔐 Token decoded successfully with JWT_SECRET:', {\n                userId: decoded.userId,\n                role: decoded.role\n            });\n        } catch (jwtError) {\n            console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');\n            try {\n                const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';\n                decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, nextAuthSecret);\n                console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', {\n                    userId: decoded.userId,\n                    role: decoded.role\n                });\n            } catch (nextAuthError) {\n                console.error('🔐 Both JWT secrets failed:', {\n                    jwtError: jwtError.message,\n                    nextAuthError: nextAuthError.message\n                });\n                throw new Error('Invalid token signature');\n            }\n        }\n        // Get fresh user data from database\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: decoded.userId\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                firstname: true,\n                lastname: true,\n                role: true\n            }\n        });\n        if (!user) {\n            console.log('🔐 User not found:', {\n                found: !!user\n            });\n            return null;\n        }\n        console.log('🔐 Mobile user authenticated successfully:', {\n            id: user.id,\n            role: user.role\n        });\n        return user;\n    } catch (error) {\n        console.error('Mobile token verification error:', error);\n        return null;\n    }\n}\nfunction isMobileRequest(request) {\n    const userAgent = request.headers.get('user-agent') || '';\n    const authHeader = request.headers.get('authorization');\n    // Check if it's a mobile request with JWT token\n    return authHeader?.startsWith('Bearer ') || userAgent.includes('Expo') || userAgent.includes('ReactNative');\n}\nasync function getMobileUserFromRequest(request) {\n    // Always try to verify mobile token if Authorization header is present\n    const authHeader = request.headers.get('authorization');\n    if (authHeader?.startsWith('Bearer ')) {\n        return await verifyMobileToken(request);\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mobile-auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/notifications.ts":
/*!**********************************!*\
  !*** ./src/lib/notifications.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotificationForAdmin: () => (/* binding */ createNotificationForAdmin),\n/* harmony export */   createNotificationForAllAdmins: () => (/* binding */ createNotificationForAllAdmins),\n/* harmony export */   getUnreadNotificationsForAdmin: () => (/* binding */ getUnreadNotificationsForAdmin),\n/* harmony export */   markAllNotificationsAsRead: () => (/* binding */ markAllNotificationsAsRead),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n/**\n * Crée une notification pour tous les administrateurs\n */ async function createNotificationForAllAdmins(type, message, quoteId) {\n    try {\n        // Récupérer tous les administrateurs\n        const admins = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.admin.findMany();\n        // Créer une notification pour chaque administrateur\n        const notifications = await Promise.all(admins.map((admin)=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n                data: {\n                    type,\n                    message,\n                    adminId: admin.id,\n                    ...quoteId && {\n                        quoteId\n                    }\n                }\n            })));\n        // Dans une application réelle, vous récupéreriez les abonnements push de chaque administrateur\n        // et vous enverriez une notification push à chacun d'eux\n        // Exemple :\n        /*\n    for (const admin of admins) {\n      // Récupérer les abonnements push de l'administrateur\n      const pushSubscriptions = await prisma.pushSubscription.findMany({\n        where: { adminId: admin.id },\n      });\n\n      // Envoyer une notification push à chaque abonnement\n      for (const subscription of pushSubscriptions) {\n        try {\n          await sendPushNotification(\n            JSON.parse(subscription.subscription),\n            {\n              id: notifications.find(n => n.adminId === admin.id)?.id,\n              type,\n              message,\n              quoteId,\n            }\n          );\n        } catch (error) {\n          console.error(`Erreur lors de l'envoi de la notification push à l'administrateur ${admin.id}:`, error);\n        }\n      }\n    }\n    */ return notifications;\n    } catch (error) {\n        console.error('Erreur lors de la création des notifications:', error);\n        throw error;\n    }\n}\n/**\n * Crée une notification pour un administrateur spécifique\n */ async function createNotificationForAdmin(adminId, type, message, quoteId) {\n    try {\n        const notification = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n            data: {\n                type,\n                message,\n                adminId,\n                ...quoteId && {\n                    quoteId\n                }\n            }\n        });\n        return notification;\n    } catch (error) {\n        console.error('Erreur lors de la création de la notification:', error);\n        throw error;\n    }\n}\n/**\n * Récupère les notifications non lues pour un administrateur\n */ async function getUnreadNotificationsForAdmin(adminId) {\n    try {\n        const notifications = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.findMany({\n            where: {\n                adminId,\n                isRead: false\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                quote: {\n                    select: {\n                        quoteNumber: true,\n                        client: {\n                            include: {\n                                user: {\n                                    select: {\n                                        firstname: true,\n                                        lastname: true\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return notifications;\n    } catch (error) {\n        console.error('Erreur lors de la récupération des notifications:', error);\n        throw error;\n    }\n}\n/**\n * Marque une notification comme lue\n */ async function markNotificationAsRead(notificationId) {\n    try {\n        const notification = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.update({\n            where: {\n                id: notificationId\n            },\n            data: {\n                isRead: true\n            }\n        });\n        return notification;\n    } catch (error) {\n        console.error('Erreur lors de la mise à jour de la notification:', error);\n        throw error;\n    }\n}\n/**\n * Marque toutes les notifications d'un administrateur comme lues\n */ async function markAllNotificationsAsRead(adminId) {\n    try {\n        const result = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.updateMany({\n            where: {\n                adminId,\n                isRead: false\n            },\n            data: {\n                isRead: true\n            }\n        });\n        return result;\n    } catch (error) {\n        console.error('Erreur lors de la mise à jour des notifications:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/notifications.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/quotes.ts":
/*!***************************!*\
  !*** ./src/lib/quotes.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertQuoteToOrder: () => (/* binding */ convertQuoteToOrder),\n/* harmony export */   createQuote: () => (/* binding */ createQuote),\n/* harmony export */   deleteQuote: () => (/* binding */ deleteQuote),\n/* harmony export */   getClientQuotes: () => (/* binding */ getClientQuotes),\n/* harmony export */   getQuoteById: () => (/* binding */ getQuoteById),\n/* harmony export */   getQuotes: () => (/* binding */ getQuotes),\n/* harmony export */   updateQuote: () => (/* binding */ updateQuote)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var _notifications__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifications */ \"(rsc)/./src/lib/notifications.ts\");\n\n\n\n\n// Obtenir tous les devis avec pagination et filtrage\nasync function getQuotes(options) {\n    const { clientId, status, search, skip = 0, take = 50 } = options || {};\n    const where = {\n        ...clientId ? {\n            clientId\n        } : {},\n        ...status ? {\n            status\n        } : {},\n        ...search ? {\n            OR: [\n                {\n                    quoteNumber: {\n                        contains: search\n                    }\n                },\n                {\n                    client: {\n                        user: {\n                            firstname: {\n                                contains: search\n                            }\n                        }\n                    }\n                },\n                {\n                    client: {\n                        user: {\n                            lastname: {\n                                contains: search\n                            }\n                        }\n                    }\n                },\n                {\n                    client: {\n                        company_name: {\n                            contains: search\n                        }\n                    }\n                }\n            ]\n        } : {}\n    };\n    const [quotes, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findMany({\n            where,\n            include: {\n                client: {\n                    include: {\n                        user: {\n                            select: {\n                                firstname: true,\n                                lastname: true,\n                                email: true,\n                                telephone: true\n                            }\n                        }\n                    }\n                },\n                quoteitem: {\n                    include: {\n                        product: true\n                    }\n                }\n            },\n            skip,\n            take,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.count({\n            where\n        })\n    ]);\n    return {\n        quotes,\n        total\n    };\n}\n// Obtenir un devis par son ID\nasync function getQuoteById(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findUnique({\n        where: {\n            id\n        },\n        include: {\n            client: {\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            firstname: true,\n                            lastname: true,\n                            email: true,\n                            telephone: true,\n                            role: true\n                        }\n                    }\n                }\n            },\n            quoteitem: {\n                include: {\n                    product: {\n                        include: {\n                            category: true,\n                            brand: true,\n                            productimage: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n}\n// Créer un nouveau devis\nasync function createQuote(data) {\n    const { clientId, notes, validUntil, items, createdByAdminId } = data;\n    // Générer un numéro de devis unique\n    const quoteNumber = await (0,_utils__WEBPACK_IMPORTED_MODULE_2__.generateQuoteNumber)();\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // Créer le devis\n        const quote = await tx.quote.create({\n            data: {\n                quoteNumber,\n                clientId,\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_1__.QuoteStatus.DRAFT,\n                notes,\n                validUntil,\n                createdByAdminId\n            }\n        });\n        // Ajouter les produits au devis\n        for (const item of items){\n            await tx.quoteItem.create({\n                data: {\n                    quoteId: quote.id,\n                    productId: item.productId,\n                    quantity: item.quantity,\n                    unitPrice: 0\n                }\n            });\n        }\n        // Récupérer les informations du client pour la notification\n        const client = await tx.client.findUnique({\n            where: {\n                id: clientId\n            },\n            include: {\n                user: {\n                    select: {\n                        firstname: true,\n                        lastname: true\n                    }\n                }\n            }\n        });\n        // Créer une notification pour tous les administrateurs\n        let notificationMessage = '';\n        if (createdByAdminId) {\n            // Si le devis a été créé par un administrateur\n            const admin = await tx.admin.findUnique({\n                where: {\n                    id: createdByAdminId\n                },\n                include: {\n                    user: {\n                        select: {\n                            firstname: true,\n                            lastname: true\n                        }\n                    }\n                }\n            });\n            if (admin && admin.user) {\n                notificationMessage = `L'administrateur ${admin.user.firstname} ${admin.user.lastname} a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;\n            } else {\n                notificationMessage = `Un administrateur a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;\n            }\n        } else {\n            // Si le devis a été créé par un client\n            notificationMessage = `${client?.user.firstname} ${client?.user.lastname} a demandé un devis`;\n        }\n        // Créer la notification en dehors de la transaction pour éviter les problèmes\n        try {\n            await (0,_notifications__WEBPACK_IMPORTED_MODULE_3__.createNotificationForAllAdmins)(_prisma_client__WEBPACK_IMPORTED_MODULE_1__.NotificationType.QUOTE_REQUESTED, notificationMessage, quote.id);\n        } catch (error) {\n            console.error('Erreur lors de la création des notifications:', error);\n        // Ne pas bloquer la création du devis si la notification échoue\n        }\n        return quote;\n    });\n}\n// Mettre à jour un devis existant\nasync function updateQuote(id, data) {\n    const { status, notes, validUntil, totalAmount, pdfUrl, items } = data;\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // Préparer les données à mettre à jour\n        const updateData = {};\n        if (status !== undefined) updateData.status = status;\n        if (notes !== undefined) updateData.notes = notes;\n        if (totalAmount !== undefined) updateData.totalAmount = totalAmount;\n        if (pdfUrl !== undefined) updateData.pdfUrl = pdfUrl;\n        // Traiter la date de validité\n        if (validUntil !== undefined) {\n            if (validUntil === null) {\n                updateData.validUntil = null;\n            } else if (typeof validUntil === 'string') {\n                // Si c'est une chaîne de caractères, s'assurer qu'elle est au format ISO\n                if (validUntil.includes('T')) {\n                    // Déjà au format ISO complet\n                    updateData.validUntil = new Date(validUntil);\n                } else {\n                    // Ajouter l'heure (minuit) pour compléter le format ISO\n                    updateData.validUntil = new Date(`${validUntil}T00:00:00.000Z`);\n                }\n            } else if (validUntil instanceof Date) {\n                updateData.validUntil = validUntil;\n            }\n        }\n        // Mettre à jour le devis\n        const quote = await tx.quote.update({\n            where: {\n                id\n            },\n            data: updateData\n        });\n        // Mettre à jour les produits du devis si fournis\n        if (items && items.length > 0) {\n            // Récupérer les items existants\n            const existingItems = await tx.quoteItem.findMany({\n                where: {\n                    quoteId: id\n                }\n            });\n            // Créer un map des items existants pour un accès rapide\n            const existingItemsMap = new Map(existingItems.map((item)=>[\n                    item.productId,\n                    item\n                ]));\n            // Traiter chaque item\n            for (const item of items){\n                const existingItem = item.id ? existingItems.find((i)=>i.id === item.id) : existingItemsMap.get(item.productId);\n                if (existingItem) {\n                    // Mettre à jour l'item existant\n                    await tx.quoteItem.update({\n                        where: {\n                            id: existingItem.id\n                        },\n                        data: {\n                            quantity: item.quantity,\n                            ...item.unitPrice !== undefined && {\n                                unitPrice: item.unitPrice\n                            }\n                        }\n                    });\n                } else {\n                    // Créer un nouvel item\n                    await tx.quoteItem.create({\n                        data: {\n                            quoteId: id,\n                            productId: item.productId,\n                            quantity: item.quantity,\n                            unitPrice: item.unitPrice || 0\n                        }\n                    });\n                }\n            }\n            // Supprimer les items qui ne sont plus dans la liste\n            const newProductIds = items.map((item)=>item.productId);\n            const itemsToDelete = existingItems.filter((item)=>!newProductIds.includes(item.productId));\n            for (const item of itemsToDelete){\n                await tx.quoteItem.delete({\n                    where: {\n                        id: item.id\n                    }\n                });\n            }\n        }\n        return quote;\n    });\n}\n// Supprimer un devis\nasync function deleteQuote(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // Supprimer d'abord les items du devis\n        await tx.quoteItem.deleteMany({\n            where: {\n                quoteId: id\n            }\n        });\n        // Supprimer le devis\n        return tx.quote.delete({\n            where: {\n                id\n            }\n        });\n    });\n}\n// Convertir un devis en commande\nasync function convertQuoteToOrder(id) {\n    const quote = await getQuoteById(id);\n    if (!quote) {\n        throw new Error('Quote not found');\n    }\n    if (quote.status !== _prisma_client__WEBPACK_IMPORTED_MODULE_1__.QuoteStatus.APPROVED) {\n        throw new Error('Only approved quotes can be converted to orders');\n    }\n// Logique pour créer une commande à partir du devis\n// À implémenter selon les besoins\n}\n// Obtenir les devis d'un client\nasync function getClientQuotes(clientId, options) {\n    const { status, skip = 0, take = 50 } = options || {};\n    const where = {\n        clientId,\n        ...status ? {\n            status\n        } : {}\n    };\n    const [quotes, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findMany({\n            where,\n            include: {\n                quoteitem: {\n                    include: {\n                        product: true\n                    }\n                }\n            },\n            skip,\n            take,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.count({\n            where\n        })\n    ]);\n    return {\n        quotes,\n        total\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3F1b3Rlcy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBa0M7QUFDNkI7QUFDakI7QUFDbUI7QUFFakUscURBQXFEO0FBQzlDLGVBQWVLLFVBQVVDLE9BTS9CO0lBQ0MsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxPQUFPLENBQUMsRUFBRUMsT0FBTyxFQUFFLEVBQUUsR0FBR0wsV0FBVyxDQUFDO0lBRXRFLE1BQU1NLFFBQVE7UUFDWixHQUFJTCxXQUFXO1lBQUVBO1FBQVMsSUFBSSxDQUFDLENBQUM7UUFDaEMsR0FBSUMsU0FBUztZQUFFQTtRQUFPLElBQUksQ0FBQyxDQUFDO1FBQzVCLEdBQUlDLFNBQ0E7WUFDRUksSUFBSTtnQkFDRjtvQkFBRUMsYUFBYTt3QkFBRUMsVUFBVU47b0JBQU87Z0JBQUU7Z0JBQ3BDO29CQUFFTyxRQUFRO3dCQUFFQyxNQUFNOzRCQUFFQyxXQUFXO2dDQUFFSCxVQUFVTjs0QkFBTzt3QkFBRTtvQkFBRTtnQkFBRTtnQkFDeEQ7b0JBQUVPLFFBQVE7d0JBQUVDLE1BQU07NEJBQUVFLFVBQVU7Z0NBQUVKLFVBQVVOOzRCQUFPO3dCQUFFO29CQUFFO2dCQUFFO2dCQUN2RDtvQkFBRU8sUUFBUTt3QkFBRUksY0FBYzs0QkFBRUwsVUFBVU47d0JBQU87b0JBQUU7Z0JBQUU7YUFDbEQ7UUFDSCxJQUNBLENBQUMsQ0FBQztJQUNSO0lBRUEsTUFBTSxDQUFDWSxRQUFRQyxNQUFNLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO1FBQ3hDeEIsMkNBQU1BLENBQUN5QixLQUFLLENBQUNDLFFBQVEsQ0FBQztZQUNwQmQ7WUFDQWUsU0FBUztnQkFDUFgsUUFBUTtvQkFDTlcsU0FBUzt3QkFDUFYsTUFBTTs0QkFDSlcsUUFBUTtnQ0FDTlYsV0FBVztnQ0FDWEMsVUFBVTtnQ0FDVlUsT0FBTztnQ0FDUEMsV0FBVzs0QkFDYjt3QkFDRjtvQkFDRjtnQkFDRjtnQkFDQUMsV0FBVztvQkFDVEosU0FBUzt3QkFDUEssU0FBUztvQkFDWDtnQkFDRjtZQUNGO1lBQ0F0QjtZQUNBQztZQUNBc0IsU0FBUztnQkFDUEMsV0FBVztZQUNiO1FBQ0Y7UUFDQWxDLDJDQUFNQSxDQUFDeUIsS0FBSyxDQUFDVSxLQUFLLENBQUM7WUFBRXZCO1FBQU07S0FDNUI7SUFFRCxPQUFPO1FBQUVTO1FBQVFDO0lBQU07QUFDekI7QUFFQSw4QkFBOEI7QUFDdkIsZUFBZWMsYUFBYUMsRUFBVTtJQUMzQyxPQUFPckMsMkNBQU1BLENBQUN5QixLQUFLLENBQUNhLFVBQVUsQ0FBQztRQUM3QjFCLE9BQU87WUFBRXlCO1FBQUc7UUFDWlYsU0FBUztZQUNQWCxRQUFRO2dCQUNOVyxTQUFTO29CQUNQVixNQUFNO3dCQUNKVyxRQUFROzRCQUNOUyxJQUFJOzRCQUNKbkIsV0FBVzs0QkFDWEMsVUFBVTs0QkFDVlUsT0FBTzs0QkFDUEMsV0FBVzs0QkFDWFMsTUFBTTt3QkFDUjtvQkFDRjtnQkFDRjtZQUNGO1lBQ0FSLFdBQVc7Z0JBQ1RKLFNBQVM7b0JBQ1BLLFNBQVM7d0JBQ1BMLFNBQVM7NEJBQ1BhLFVBQVU7NEJBQ1ZDLE9BQU87NEJBQ1BDLGNBQWM7d0JBQ2hCO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGO0FBQ0Y7QUFFQSx5QkFBeUI7QUFDbEIsZUFBZUMsWUFBWUMsSUFTakM7SUFDQyxNQUFNLEVBQUVyQyxRQUFRLEVBQUVzQyxLQUFLLEVBQUVDLFVBQVUsRUFBRUMsS0FBSyxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHSjtJQUVqRSxvQ0FBb0M7SUFDcEMsTUFBTTlCLGNBQWMsTUFBTVgsMkRBQW1CQTtJQUU3QyxPQUFPSCwyQ0FBTUEsQ0FBQ2lELFlBQVksQ0FBQyxPQUFPQztRQUNoQyxpQkFBaUI7UUFDakIsTUFBTXpCLFFBQVEsTUFBTXlCLEdBQUd6QixLQUFLLENBQUMwQixNQUFNLENBQUM7WUFDbENQLE1BQU07Z0JBQ0o5QjtnQkFDQVA7Z0JBQ0FDLFFBQVFQLHVEQUFXQSxDQUFDbUQsS0FBSztnQkFDekJQO2dCQUNBQztnQkFDQUU7WUFDRjtRQUNGO1FBRUEsZ0NBQWdDO1FBQ2hDLEtBQUssTUFBTUssUUFBUU4sTUFBTztZQUN4QixNQUFNRyxHQUFHSSxTQUFTLENBQUNILE1BQU0sQ0FBQztnQkFDeEJQLE1BQU07b0JBQ0pXLFNBQVM5QixNQUFNWSxFQUFFO29CQUNqQm1CLFdBQVdILEtBQUtHLFNBQVM7b0JBQ3pCQyxVQUFVSixLQUFLSSxRQUFRO29CQUN2QkMsV0FBVztnQkFDYjtZQUNGO1FBQ0Y7UUFFQSw0REFBNEQ7UUFDNUQsTUFBTTFDLFNBQVMsTUFBTWtDLEdBQUdsQyxNQUFNLENBQUNzQixVQUFVLENBQUM7WUFDeEMxQixPQUFPO2dCQUFFeUIsSUFBSTlCO1lBQVM7WUFDdEJvQixTQUFTO2dCQUNQVixNQUFNO29CQUNKVyxRQUFRO3dCQUNOVixXQUFXO3dCQUNYQyxVQUFVO29CQUNaO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLHVEQUF1RDtRQUN2RCxJQUFJd0Msc0JBQXNCO1FBRTFCLElBQUlYLGtCQUFrQjtZQUNwQiwrQ0FBK0M7WUFDL0MsTUFBTVksUUFBUSxNQUFNVixHQUFHVSxLQUFLLENBQUN0QixVQUFVLENBQUM7Z0JBQ3RDMUIsT0FBTztvQkFBRXlCLElBQUlXO2dCQUFpQjtnQkFDOUJyQixTQUFTO29CQUNQVixNQUFNO3dCQUNKVyxRQUFROzRCQUNOVixXQUFXOzRCQUNYQyxVQUFVO3dCQUNaO29CQUNGO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJeUMsU0FBU0EsTUFBTTNDLElBQUksRUFBRTtnQkFDdkIwQyxzQkFBc0IsQ0FBQyxpQkFBaUIsRUFBRUMsTUFBTTNDLElBQUksQ0FBQ0MsU0FBUyxDQUFDLENBQUMsRUFBRTBDLE1BQU0zQyxJQUFJLENBQUNFLFFBQVEsQ0FBQyxzQkFBc0IsRUFBRUgsUUFBUUMsS0FBS0MsVUFBVSxDQUFDLEVBQUVGLFFBQVFDLEtBQUtFLFVBQVU7WUFDakssT0FBTztnQkFDTHdDLHNCQUFzQixDQUFDLHVDQUF1QyxFQUFFM0MsUUFBUUMsS0FBS0MsVUFBVSxDQUFDLEVBQUVGLFFBQVFDLEtBQUtFLFVBQVU7WUFDbkg7UUFDRixPQUFPO1lBQ0wsdUNBQXVDO1lBQ3ZDd0Msc0JBQXNCLEdBQUczQyxRQUFRQyxLQUFLQyxVQUFVLENBQUMsRUFBRUYsUUFBUUMsS0FBS0UsU0FBUyxtQkFBbUIsQ0FBQztRQUMvRjtRQUVBLDhFQUE4RTtRQUM5RSxJQUFJO1lBQ0YsTUFBTWYsOEVBQThCQSxDQUNsQ0YsNERBQWdCQSxDQUFDMkQsZUFBZSxFQUNoQ0YscUJBQ0FsQyxNQUFNWSxFQUFFO1FBRVosRUFBRSxPQUFPeUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaURBQWlEQTtRQUMvRCxnRUFBZ0U7UUFDbEU7UUFFQSxPQUFPckM7SUFDVDtBQUNGO0FBRUEsa0NBQWtDO0FBQzNCLGVBQWV1QyxZQUNwQjNCLEVBQVUsRUFDVk8sSUFZQztJQUVELE1BQU0sRUFBRXBDLE1BQU0sRUFBRXFDLEtBQUssRUFBRUMsVUFBVSxFQUFFbUIsV0FBVyxFQUFFQyxNQUFNLEVBQUVuQixLQUFLLEVBQUUsR0FBR0g7SUFFbEUsT0FBTzVDLDJDQUFNQSxDQUFDaUQsWUFBWSxDQUFDLE9BQU9DO1FBQ2hDLHVDQUF1QztRQUN2QyxNQUFNaUIsYUFBa0IsQ0FBQztRQUV6QixJQUFJM0QsV0FBVzRELFdBQVdELFdBQVczRCxNQUFNLEdBQUdBO1FBQzlDLElBQUlxQyxVQUFVdUIsV0FBV0QsV0FBV3RCLEtBQUssR0FBR0E7UUFDNUMsSUFBSW9CLGdCQUFnQkcsV0FBV0QsV0FBV0YsV0FBVyxHQUFHQTtRQUN4RCxJQUFJQyxXQUFXRSxXQUFXRCxXQUFXRCxNQUFNLEdBQUdBO1FBRTlDLDhCQUE4QjtRQUM5QixJQUFJcEIsZUFBZXNCLFdBQVc7WUFDNUIsSUFBSXRCLGVBQWUsTUFBTTtnQkFDdkJxQixXQUFXckIsVUFBVSxHQUFHO1lBQzFCLE9BQU8sSUFBSSxPQUFPQSxlQUFlLFVBQVU7Z0JBQ3pDLHlFQUF5RTtnQkFDekUsSUFBSUEsV0FBV3VCLFFBQVEsQ0FBQyxNQUFNO29CQUM1Qiw2QkFBNkI7b0JBQzdCRixXQUFXckIsVUFBVSxHQUFHLElBQUl3QixLQUFLeEI7Z0JBQ25DLE9BQU87b0JBQ0wsd0RBQXdEO29CQUN4RHFCLFdBQVdyQixVQUFVLEdBQUcsSUFBSXdCLEtBQUssR0FBR3hCLFdBQVcsY0FBYyxDQUFDO2dCQUNoRTtZQUNGLE9BQU8sSUFBSUEsc0JBQXNCd0IsTUFBTTtnQkFDckNILFdBQVdyQixVQUFVLEdBQUdBO1lBQzFCO1FBQ0Y7UUFFQSx5QkFBeUI7UUFDekIsTUFBTXJCLFFBQVEsTUFBTXlCLEdBQUd6QixLQUFLLENBQUM4QyxNQUFNLENBQUM7WUFDbEMzRCxPQUFPO2dCQUFFeUI7WUFBRztZQUNaTyxNQUFNdUI7UUFDUjtRQUVBLGlEQUFpRDtRQUNqRCxJQUFJcEIsU0FBU0EsTUFBTXlCLE1BQU0sR0FBRyxHQUFHO1lBQzdCLGdDQUFnQztZQUNoQyxNQUFNQyxnQkFBZ0IsTUFBTXZCLEdBQUdJLFNBQVMsQ0FBQzVCLFFBQVEsQ0FBQztnQkFDaERkLE9BQU87b0JBQUUyQyxTQUFTbEI7Z0JBQUc7WUFDdkI7WUFFQSx3REFBd0Q7WUFDeEQsTUFBTXFDLG1CQUFtQixJQUFJQyxJQUMzQkYsY0FBY0csR0FBRyxDQUFDdkIsQ0FBQUEsT0FBUTtvQkFBQ0EsS0FBS0csU0FBUztvQkFBRUg7aUJBQUs7WUFHbEQsc0JBQXNCO1lBQ3RCLEtBQUssTUFBTUEsUUFBUU4sTUFBTztnQkFDeEIsTUFBTThCLGVBQWV4QixLQUFLaEIsRUFBRSxHQUN4Qm9DLGNBQWNLLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTFDLEVBQUUsS0FBS2dCLEtBQUtoQixFQUFFLElBQ3hDcUMsaUJBQWlCTSxHQUFHLENBQUMzQixLQUFLRyxTQUFTO2dCQUV2QyxJQUFJcUIsY0FBYztvQkFDaEIsZ0NBQWdDO29CQUNoQyxNQUFNM0IsR0FBR0ksU0FBUyxDQUFDaUIsTUFBTSxDQUFDO3dCQUN4QjNELE9BQU87NEJBQUV5QixJQUFJd0MsYUFBYXhDLEVBQUU7d0JBQUM7d0JBQzdCTyxNQUFNOzRCQUNKYSxVQUFVSixLQUFLSSxRQUFROzRCQUN2QixHQUFJSixLQUFLSyxTQUFTLEtBQUtVLGFBQWE7Z0NBQUVWLFdBQVdMLEtBQUtLLFNBQVM7NEJBQUMsQ0FBQzt3QkFDbkU7b0JBQ0Y7Z0JBQ0YsT0FBTztvQkFDTCx1QkFBdUI7b0JBQ3ZCLE1BQU1SLEdBQUdJLFNBQVMsQ0FBQ0gsTUFBTSxDQUFDO3dCQUN4QlAsTUFBTTs0QkFDSlcsU0FBU2xCOzRCQUNUbUIsV0FBV0gsS0FBS0csU0FBUzs0QkFDekJDLFVBQVVKLEtBQUtJLFFBQVE7NEJBQ3ZCQyxXQUFXTCxLQUFLSyxTQUFTLElBQUk7d0JBQy9CO29CQUNGO2dCQUNGO1lBQ0Y7WUFFQSxxREFBcUQ7WUFDckQsTUFBTXVCLGdCQUFnQmxDLE1BQU02QixHQUFHLENBQUN2QixDQUFBQSxPQUFRQSxLQUFLRyxTQUFTO1lBQ3RELE1BQU0wQixnQkFBZ0JULGNBQWNVLE1BQU0sQ0FDeEM5QixDQUFBQSxPQUFRLENBQUM0QixjQUFjWixRQUFRLENBQUNoQixLQUFLRyxTQUFTO1lBR2hELEtBQUssTUFBTUgsUUFBUTZCLGNBQWU7Z0JBQ2hDLE1BQU1oQyxHQUFHSSxTQUFTLENBQUM4QixNQUFNLENBQUM7b0JBQ3hCeEUsT0FBTzt3QkFBRXlCLElBQUlnQixLQUFLaEIsRUFBRTtvQkFBQztnQkFDdkI7WUFDRjtRQUNGO1FBRUEsT0FBT1o7SUFDVDtBQUNGO0FBRUEscUJBQXFCO0FBQ2QsZUFBZTRELFlBQVloRCxFQUFVO0lBQzFDLE9BQU9yQywyQ0FBTUEsQ0FBQ2lELFlBQVksQ0FBQyxPQUFPQztRQUNoQyx1Q0FBdUM7UUFDdkMsTUFBTUEsR0FBR0ksU0FBUyxDQUFDZ0MsVUFBVSxDQUFDO1lBQzVCMUUsT0FBTztnQkFBRTJDLFNBQVNsQjtZQUFHO1FBQ3ZCO1FBRUEscUJBQXFCO1FBQ3JCLE9BQU9hLEdBQUd6QixLQUFLLENBQUMyRCxNQUFNLENBQUM7WUFDckJ4RSxPQUFPO2dCQUFFeUI7WUFBRztRQUNkO0lBQ0Y7QUFDRjtBQUVBLGlDQUFpQztBQUMxQixlQUFla0Qsb0JBQW9CbEQsRUFBVTtJQUNsRCxNQUFNWixRQUFRLE1BQU1XLGFBQWFDO0lBRWpDLElBQUksQ0FBQ1osT0FBTztRQUNWLE1BQU0sSUFBSStELE1BQU07SUFDbEI7SUFFQSxJQUFJL0QsTUFBTWpCLE1BQU0sS0FBS1AsdURBQVdBLENBQUN3RixRQUFRLEVBQUU7UUFDekMsTUFBTSxJQUFJRCxNQUFNO0lBQ2xCO0FBRUEsb0RBQW9EO0FBQ3BELGtDQUFrQztBQUNwQztBQUVBLGdDQUFnQztBQUN6QixlQUFlRSxnQkFBZ0JuRixRQUFnQixFQUFFRCxPQUl2RDtJQUNDLE1BQU0sRUFBRUUsTUFBTSxFQUFFRSxPQUFPLENBQUMsRUFBRUMsT0FBTyxFQUFFLEVBQUUsR0FBR0wsV0FBVyxDQUFDO0lBRXBELE1BQU1NLFFBQVE7UUFDWkw7UUFDQSxHQUFJQyxTQUFTO1lBQUVBO1FBQU8sSUFBSSxDQUFDLENBQUM7SUFDOUI7SUFFQSxNQUFNLENBQUNhLFFBQVFDLE1BQU0sR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7UUFDeEN4QiwyQ0FBTUEsQ0FBQ3lCLEtBQUssQ0FBQ0MsUUFBUSxDQUFDO1lBQ3BCZDtZQUNBZSxTQUFTO2dCQUNQSSxXQUFXO29CQUNUSixTQUFTO3dCQUNQSyxTQUFTO29CQUNYO2dCQUNGO1lBQ0Y7WUFDQXRCO1lBQ0FDO1lBQ0FzQixTQUFTO2dCQUNQQyxXQUFXO1lBQ2I7UUFDRjtRQUNBbEMsMkNBQU1BLENBQUN5QixLQUFLLENBQUNVLEtBQUssQ0FBQztZQUFFdkI7UUFBTTtLQUM1QjtJQUVELE9BQU87UUFBRVM7UUFBUUM7SUFBTTtBQUN6QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxQcm9qZWN0c1xcTW9vbmVsZWNBcHBcXG1vb25lbGVjLWFwcFxcc3JjXFxsaWJcXHF1b3Rlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBwcmlzbWEgfSBmcm9tICcuL3ByaXNtYSc7XG5pbXBvcnQgeyBRdW90ZVN0YXR1cywgTm90aWZpY2F0aW9uVHlwZSB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcbmltcG9ydCB7IGdlbmVyYXRlUXVvdGVOdW1iZXIgfSBmcm9tICcuL3V0aWxzJztcbmltcG9ydCB7IGNyZWF0ZU5vdGlmaWNhdGlvbkZvckFsbEFkbWlucyB9IGZyb20gJy4vbm90aWZpY2F0aW9ucyc7XG5cbi8vIE9idGVuaXIgdG91cyBsZXMgZGV2aXMgYXZlYyBwYWdpbmF0aW9uIGV0IGZpbHRyYWdlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0UXVvdGVzKG9wdGlvbnM/OiB7XG4gIGNsaWVudElkPzogc3RyaW5nO1xuICBzdGF0dXM/OiBRdW90ZVN0YXR1cztcbiAgc2VhcmNoPzogc3RyaW5nO1xuICBza2lwPzogbnVtYmVyO1xuICB0YWtlPzogbnVtYmVyO1xufSkge1xuICBjb25zdCB7IGNsaWVudElkLCBzdGF0dXMsIHNlYXJjaCwgc2tpcCA9IDAsIHRha2UgPSA1MCB9ID0gb3B0aW9ucyB8fCB7fTtcblxuICBjb25zdCB3aGVyZSA9IHtcbiAgICAuLi4oY2xpZW50SWQgPyB7IGNsaWVudElkIH0gOiB7fSksXG4gICAgLi4uKHN0YXR1cyA/IHsgc3RhdHVzIH0gOiB7fSksXG4gICAgLi4uKHNlYXJjaFxuICAgICAgPyB7XG4gICAgICAgICAgT1I6IFtcbiAgICAgICAgICAgIHsgcXVvdGVOdW1iZXI6IHsgY29udGFpbnM6IHNlYXJjaCB9IH0sXG4gICAgICAgICAgICB7IGNsaWVudDogeyB1c2VyOiB7IGZpcnN0bmFtZTogeyBjb250YWluczogc2VhcmNoIH0gfSB9IH0sXG4gICAgICAgICAgICB7IGNsaWVudDogeyB1c2VyOiB7IGxhc3RuYW1lOiB7IGNvbnRhaW5zOiBzZWFyY2ggfSB9IH0gfSxcbiAgICAgICAgICAgIHsgY2xpZW50OiB7IGNvbXBhbnlfbmFtZTogeyBjb250YWluczogc2VhcmNoIH0gfSB9LFxuICAgICAgICAgIF0sXG4gICAgICAgIH1cbiAgICAgIDoge30pLFxuICB9O1xuXG4gIGNvbnN0IFtxdW90ZXMsIHRvdGFsXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICBwcmlzbWEucXVvdGUuZmluZE1hbnkoe1xuICAgICAgd2hlcmUsXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIGNsaWVudDoge1xuICAgICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICAgIHVzZXI6IHtcbiAgICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgICAgZmlyc3RuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICAgIGxhc3RuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICAgIGVtYWlsOiB0cnVlLFxuICAgICAgICAgICAgICAgIHRlbGVwaG9uZTogdHJ1ZSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgcXVvdGVpdGVtOiB7XG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgcHJvZHVjdDogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICAgIHNraXAsXG4gICAgICB0YWtlLFxuICAgICAgb3JkZXJCeToge1xuICAgICAgICBjcmVhdGVkQXQ6ICdkZXNjJyxcbiAgICAgIH0sXG4gICAgfSksXG4gICAgcHJpc21hLnF1b3RlLmNvdW50KHsgd2hlcmUgfSksXG4gIF0pO1xuXG4gIHJldHVybiB7IHF1b3RlcywgdG90YWwgfTtcbn1cblxuLy8gT2J0ZW5pciB1biBkZXZpcyBwYXIgc29uIElEXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0UXVvdGVCeUlkKGlkOiBzdHJpbmcpIHtcbiAgcmV0dXJuIHByaXNtYS5xdW90ZS5maW5kVW5pcXVlKHtcbiAgICB3aGVyZTogeyBpZCB9LFxuICAgIGluY2x1ZGU6IHtcbiAgICAgIGNsaWVudDoge1xuICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgdXNlcjoge1xuICAgICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgICAgICBmaXJzdG5hbWU6IHRydWUsXG4gICAgICAgICAgICAgIGxhc3RuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgICAgICAgdGVsZXBob25lOiB0cnVlLFxuICAgICAgICAgICAgICByb2xlOiB0cnVlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICAgIHF1b3RlaXRlbToge1xuICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgcHJvZHVjdDoge1xuICAgICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgICBjYXRlZ29yeTogdHJ1ZSxcbiAgICAgICAgICAgICAgYnJhbmQ6IHRydWUsXG4gICAgICAgICAgICAgIHByb2R1Y3RpbWFnZTogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSxcbiAgfSk7XG59XG5cbi8vIENyw6llciB1biBub3V2ZWF1IGRldmlzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlUXVvdGUoZGF0YToge1xuICBjbGllbnRJZDogc3RyaW5nO1xuICBub3Rlcz86IHN0cmluZztcbiAgdmFsaWRVbnRpbD86IERhdGU7XG4gIGl0ZW1zOiB7XG4gICAgcHJvZHVjdElkOiBzdHJpbmc7XG4gICAgcXVhbnRpdHk6IG51bWJlcjtcbiAgfVtdO1xuICBjcmVhdGVkQnlBZG1pbklkPzogc3RyaW5nOyAvLyBJRCBkZSBsJ2FkbWluIHF1aSBjcsOpZSBsZSBkZXZpcyAoc2kgYXBwbGljYWJsZSlcbn0pIHtcbiAgY29uc3QgeyBjbGllbnRJZCwgbm90ZXMsIHZhbGlkVW50aWwsIGl0ZW1zLCBjcmVhdGVkQnlBZG1pbklkIH0gPSBkYXRhO1xuXG4gIC8vIEfDqW7DqXJlciB1biBudW3DqXJvIGRlIGRldmlzIHVuaXF1ZVxuICBjb25zdCBxdW90ZU51bWJlciA9IGF3YWl0IGdlbmVyYXRlUXVvdGVOdW1iZXIoKTtcblxuICByZXR1cm4gcHJpc21hLiR0cmFuc2FjdGlvbihhc3luYyAodHgpID0+IHtcbiAgICAvLyBDcsOpZXIgbGUgZGV2aXNcbiAgICBjb25zdCBxdW90ZSA9IGF3YWl0IHR4LnF1b3RlLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHF1b3RlTnVtYmVyLFxuICAgICAgICBjbGllbnRJZCxcbiAgICAgICAgc3RhdHVzOiBRdW90ZVN0YXR1cy5EUkFGVCxcbiAgICAgICAgbm90ZXMsXG4gICAgICAgIHZhbGlkVW50aWwsXG4gICAgICAgIGNyZWF0ZWRCeUFkbWluSWQsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgLy8gQWpvdXRlciBsZXMgcHJvZHVpdHMgYXUgZGV2aXNcbiAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgaXRlbXMpIHtcbiAgICAgIGF3YWl0IHR4LnF1b3RlSXRlbS5jcmVhdGUoe1xuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgcXVvdGVJZDogcXVvdGUuaWQsXG4gICAgICAgICAgcHJvZHVjdElkOiBpdGVtLnByb2R1Y3RJZCxcbiAgICAgICAgICBxdWFudGl0eTogaXRlbS5xdWFudGl0eSxcbiAgICAgICAgICB1bml0UHJpY2U6IDAsIC8vIExlIHByaXggc2VyYSBkw6lmaW5pIHBhciBsJ2FkbWluaXN0cmF0ZXVyXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBSw6ljdXDDqXJlciBsZXMgaW5mb3JtYXRpb25zIGR1IGNsaWVudCBwb3VyIGxhIG5vdGlmaWNhdGlvblxuICAgIGNvbnN0IGNsaWVudCA9IGF3YWl0IHR4LmNsaWVudC5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7IGlkOiBjbGllbnRJZCB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBmaXJzdG5hbWU6IHRydWUsXG4gICAgICAgICAgICBsYXN0bmFtZTogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIENyw6llciB1bmUgbm90aWZpY2F0aW9uIHBvdXIgdG91cyBsZXMgYWRtaW5pc3RyYXRldXJzXG4gICAgbGV0IG5vdGlmaWNhdGlvbk1lc3NhZ2UgPSAnJztcblxuICAgIGlmIChjcmVhdGVkQnlBZG1pbklkKSB7XG4gICAgICAvLyBTaSBsZSBkZXZpcyBhIMOpdMOpIGNyw6nDqSBwYXIgdW4gYWRtaW5pc3RyYXRldXJcbiAgICAgIGNvbnN0IGFkbWluID0gYXdhaXQgdHguYWRtaW4uZmluZFVuaXF1ZSh7XG4gICAgICAgIHdoZXJlOiB7IGlkOiBjcmVhdGVkQnlBZG1pbklkIH0sXG4gICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICAgICAgZmlyc3RuYW1lOiB0cnVlLFxuICAgICAgICAgICAgICBsYXN0bmFtZTogdHJ1ZSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoYWRtaW4gJiYgYWRtaW4udXNlcikge1xuICAgICAgICBub3RpZmljYXRpb25NZXNzYWdlID0gYEwnYWRtaW5pc3RyYXRldXIgJHthZG1pbi51c2VyLmZpcnN0bmFtZX0gJHthZG1pbi51c2VyLmxhc3RuYW1lfSBhIGNyw6nDqSB1biBkZXZpcyBwb3VyICR7Y2xpZW50Py51c2VyLmZpcnN0bmFtZX0gJHtjbGllbnQ/LnVzZXIubGFzdG5hbWV9YDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbk1lc3NhZ2UgPSBgVW4gYWRtaW5pc3RyYXRldXIgYSBjcsOpw6kgdW4gZGV2aXMgcG91ciAke2NsaWVudD8udXNlci5maXJzdG5hbWV9ICR7Y2xpZW50Py51c2VyLmxhc3RuYW1lfWA7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFNpIGxlIGRldmlzIGEgw6l0w6kgY3LDqcOpIHBhciB1biBjbGllbnRcbiAgICAgIG5vdGlmaWNhdGlvbk1lc3NhZ2UgPSBgJHtjbGllbnQ/LnVzZXIuZmlyc3RuYW1lfSAke2NsaWVudD8udXNlci5sYXN0bmFtZX0gYSBkZW1hbmTDqSB1biBkZXZpc2A7XG4gICAgfVxuXG4gICAgLy8gQ3LDqWVyIGxhIG5vdGlmaWNhdGlvbiBlbiBkZWhvcnMgZGUgbGEgdHJhbnNhY3Rpb24gcG91ciDDqXZpdGVyIGxlcyBwcm9ibMOobWVzXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGNyZWF0ZU5vdGlmaWNhdGlvbkZvckFsbEFkbWlucyhcbiAgICAgICAgTm90aWZpY2F0aW9uVHlwZS5RVU9URV9SRVFVRVNURUQsXG4gICAgICAgIG5vdGlmaWNhdGlvbk1lc3NhZ2UsXG4gICAgICAgIHF1b3RlLmlkXG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJldXIgbG9ycyBkZSBsYSBjcsOpYXRpb24gZGVzIG5vdGlmaWNhdGlvbnM6JywgZXJyb3IpO1xuICAgICAgLy8gTmUgcGFzIGJsb3F1ZXIgbGEgY3LDqWF0aW9uIGR1IGRldmlzIHNpIGxhIG5vdGlmaWNhdGlvbiDDqWNob3VlXG4gICAgfVxuXG4gICAgcmV0dXJuIHF1b3RlO1xuICB9KTtcbn1cblxuLy8gTWV0dHJlIMOgIGpvdXIgdW4gZGV2aXMgZXhpc3RhbnRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGRhdGVRdW90ZShcbiAgaWQ6IHN0cmluZyxcbiAgZGF0YToge1xuICAgIHN0YXR1cz86IFF1b3RlU3RhdHVzO1xuICAgIG5vdGVzPzogc3RyaW5nO1xuICAgIHZhbGlkVW50aWw/OiBEYXRlIHwgbnVsbDtcbiAgICB0b3RhbEFtb3VudD86IG51bWJlcjtcbiAgICBwZGZVcmw/OiBzdHJpbmc7XG4gICAgaXRlbXM/OiB7XG4gICAgICBpZD86IHN0cmluZzsgLy8gU2kgbCdpdGVtIGV4aXN0ZSBkw6lqw6BcbiAgICAgIHByb2R1Y3RJZDogc3RyaW5nO1xuICAgICAgcXVhbnRpdHk6IG51bWJlcjtcbiAgICAgIHVuaXRQcmljZT86IG51bWJlcjtcbiAgICB9W107XG4gIH1cbikge1xuICBjb25zdCB7IHN0YXR1cywgbm90ZXMsIHZhbGlkVW50aWwsIHRvdGFsQW1vdW50LCBwZGZVcmwsIGl0ZW1zIH0gPSBkYXRhO1xuXG4gIHJldHVybiBwcmlzbWEuJHRyYW5zYWN0aW9uKGFzeW5jICh0eCkgPT4ge1xuICAgIC8vIFByw6lwYXJlciBsZXMgZG9ubsOpZXMgw6AgbWV0dHJlIMOgIGpvdXJcbiAgICBjb25zdCB1cGRhdGVEYXRhOiBhbnkgPSB7fTtcblxuICAgIGlmIChzdGF0dXMgIT09IHVuZGVmaW5lZCkgdXBkYXRlRGF0YS5zdGF0dXMgPSBzdGF0dXM7XG4gICAgaWYgKG5vdGVzICE9PSB1bmRlZmluZWQpIHVwZGF0ZURhdGEubm90ZXMgPSBub3RlcztcbiAgICBpZiAodG90YWxBbW91bnQgIT09IHVuZGVmaW5lZCkgdXBkYXRlRGF0YS50b3RhbEFtb3VudCA9IHRvdGFsQW1vdW50O1xuICAgIGlmIChwZGZVcmwgIT09IHVuZGVmaW5lZCkgdXBkYXRlRGF0YS5wZGZVcmwgPSBwZGZVcmw7XG5cbiAgICAvLyBUcmFpdGVyIGxhIGRhdGUgZGUgdmFsaWRpdMOpXG4gICAgaWYgKHZhbGlkVW50aWwgIT09IHVuZGVmaW5lZCkge1xuICAgICAgaWYgKHZhbGlkVW50aWwgPT09IG51bGwpIHtcbiAgICAgICAgdXBkYXRlRGF0YS52YWxpZFVudGlsID0gbnVsbDtcbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbGlkVW50aWwgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIC8vIFNpIGMnZXN0IHVuZSBjaGHDrm5lIGRlIGNhcmFjdMOocmVzLCBzJ2Fzc3VyZXIgcXUnZWxsZSBlc3QgYXUgZm9ybWF0IElTT1xuICAgICAgICBpZiAodmFsaWRVbnRpbC5pbmNsdWRlcygnVCcpKSB7XG4gICAgICAgICAgLy8gRMOpasOgIGF1IGZvcm1hdCBJU08gY29tcGxldFxuICAgICAgICAgIHVwZGF0ZURhdGEudmFsaWRVbnRpbCA9IG5ldyBEYXRlKHZhbGlkVW50aWwpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIEFqb3V0ZXIgbCdoZXVyZSAobWludWl0KSBwb3VyIGNvbXBsw6l0ZXIgbGUgZm9ybWF0IElTT1xuICAgICAgICAgIHVwZGF0ZURhdGEudmFsaWRVbnRpbCA9IG5ldyBEYXRlKGAke3ZhbGlkVW50aWx9VDAwOjAwOjAwLjAwMFpgKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICh2YWxpZFVudGlsIGluc3RhbmNlb2YgRGF0ZSkge1xuICAgICAgICB1cGRhdGVEYXRhLnZhbGlkVW50aWwgPSB2YWxpZFVudGlsO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIE1ldHRyZSDDoCBqb3VyIGxlIGRldmlzXG4gICAgY29uc3QgcXVvdGUgPSBhd2FpdCB0eC5xdW90ZS51cGRhdGUoe1xuICAgICAgd2hlcmU6IHsgaWQgfSxcbiAgICAgIGRhdGE6IHVwZGF0ZURhdGEsXG4gICAgfSk7XG5cbiAgICAvLyBNZXR0cmUgw6Agam91ciBsZXMgcHJvZHVpdHMgZHUgZGV2aXMgc2kgZm91cm5pc1xuICAgIGlmIChpdGVtcyAmJiBpdGVtcy5sZW5ndGggPiAwKSB7XG4gICAgICAvLyBSw6ljdXDDqXJlciBsZXMgaXRlbXMgZXhpc3RhbnRzXG4gICAgICBjb25zdCBleGlzdGluZ0l0ZW1zID0gYXdhaXQgdHgucXVvdGVJdGVtLmZpbmRNYW55KHtcbiAgICAgICAgd2hlcmU6IHsgcXVvdGVJZDogaWQgfSxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBDcsOpZXIgdW4gbWFwIGRlcyBpdGVtcyBleGlzdGFudHMgcG91ciB1biBhY2PDqHMgcmFwaWRlXG4gICAgICBjb25zdCBleGlzdGluZ0l0ZW1zTWFwID0gbmV3IE1hcChcbiAgICAgICAgZXhpc3RpbmdJdGVtcy5tYXAoaXRlbSA9PiBbaXRlbS5wcm9kdWN0SWQsIGl0ZW1dKVxuICAgICAgKTtcblxuICAgICAgLy8gVHJhaXRlciBjaGFxdWUgaXRlbVxuICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGl0ZW1zKSB7XG4gICAgICAgIGNvbnN0IGV4aXN0aW5nSXRlbSA9IGl0ZW0uaWRcbiAgICAgICAgICA/IGV4aXN0aW5nSXRlbXMuZmluZChpID0+IGkuaWQgPT09IGl0ZW0uaWQpXG4gICAgICAgICAgOiBleGlzdGluZ0l0ZW1zTWFwLmdldChpdGVtLnByb2R1Y3RJZCk7XG5cbiAgICAgICAgaWYgKGV4aXN0aW5nSXRlbSkge1xuICAgICAgICAgIC8vIE1ldHRyZSDDoCBqb3VyIGwnaXRlbSBleGlzdGFudFxuICAgICAgICAgIGF3YWl0IHR4LnF1b3RlSXRlbS51cGRhdGUoe1xuICAgICAgICAgICAgd2hlcmU6IHsgaWQ6IGV4aXN0aW5nSXRlbS5pZCB9LFxuICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICBxdWFudGl0eTogaXRlbS5xdWFudGl0eSxcbiAgICAgICAgICAgICAgLi4uKGl0ZW0udW5pdFByaWNlICE9PSB1bmRlZmluZWQgJiYgeyB1bml0UHJpY2U6IGl0ZW0udW5pdFByaWNlIH0pLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBDcsOpZXIgdW4gbm91dmVsIGl0ZW1cbiAgICAgICAgICBhd2FpdCB0eC5xdW90ZUl0ZW0uY3JlYXRlKHtcbiAgICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgICAgcXVvdGVJZDogaWQsXG4gICAgICAgICAgICAgIHByb2R1Y3RJZDogaXRlbS5wcm9kdWN0SWQsXG4gICAgICAgICAgICAgIHF1YW50aXR5OiBpdGVtLnF1YW50aXR5LFxuICAgICAgICAgICAgICB1bml0UHJpY2U6IGl0ZW0udW5pdFByaWNlIHx8IDAsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIFN1cHByaW1lciBsZXMgaXRlbXMgcXVpIG5lIHNvbnQgcGx1cyBkYW5zIGxhIGxpc3RlXG4gICAgICBjb25zdCBuZXdQcm9kdWN0SWRzID0gaXRlbXMubWFwKGl0ZW0gPT4gaXRlbS5wcm9kdWN0SWQpO1xuICAgICAgY29uc3QgaXRlbXNUb0RlbGV0ZSA9IGV4aXN0aW5nSXRlbXMuZmlsdGVyKFxuICAgICAgICBpdGVtID0+ICFuZXdQcm9kdWN0SWRzLmluY2x1ZGVzKGl0ZW0ucHJvZHVjdElkKVxuICAgICAgKTtcblxuICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGl0ZW1zVG9EZWxldGUpIHtcbiAgICAgICAgYXdhaXQgdHgucXVvdGVJdGVtLmRlbGV0ZSh7XG4gICAgICAgICAgd2hlcmU6IHsgaWQ6IGl0ZW0uaWQgfSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHF1b3RlO1xuICB9KTtcbn1cblxuLy8gU3VwcHJpbWVyIHVuIGRldmlzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlUXVvdGUoaWQ6IHN0cmluZykge1xuICByZXR1cm4gcHJpc21hLiR0cmFuc2FjdGlvbihhc3luYyAodHgpID0+IHtcbiAgICAvLyBTdXBwcmltZXIgZCdhYm9yZCBsZXMgaXRlbXMgZHUgZGV2aXNcbiAgICBhd2FpdCB0eC5xdW90ZUl0ZW0uZGVsZXRlTWFueSh7XG4gICAgICB3aGVyZTogeyBxdW90ZUlkOiBpZCB9LFxuICAgIH0pO1xuXG4gICAgLy8gU3VwcHJpbWVyIGxlIGRldmlzXG4gICAgcmV0dXJuIHR4LnF1b3RlLmRlbGV0ZSh7XG4gICAgICB3aGVyZTogeyBpZCB9LFxuICAgIH0pO1xuICB9KTtcbn1cblxuLy8gQ29udmVydGlyIHVuIGRldmlzIGVuIGNvbW1hbmRlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY29udmVydFF1b3RlVG9PcmRlcihpZDogc3RyaW5nKSB7XG4gIGNvbnN0IHF1b3RlID0gYXdhaXQgZ2V0UXVvdGVCeUlkKGlkKTtcblxuICBpZiAoIXF1b3RlKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdRdW90ZSBub3QgZm91bmQnKTtcbiAgfVxuXG4gIGlmIChxdW90ZS5zdGF0dXMgIT09IFF1b3RlU3RhdHVzLkFQUFJPVkVEKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdPbmx5IGFwcHJvdmVkIHF1b3RlcyBjYW4gYmUgY29udmVydGVkIHRvIG9yZGVycycpO1xuICB9XG5cbiAgLy8gTG9naXF1ZSBwb3VyIGNyw6llciB1bmUgY29tbWFuZGUgw6AgcGFydGlyIGR1IGRldmlzXG4gIC8vIMOAIGltcGzDqW1lbnRlciBzZWxvbiBsZXMgYmVzb2luc1xufVxuXG4vLyBPYnRlbmlyIGxlcyBkZXZpcyBkJ3VuIGNsaWVudFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldENsaWVudFF1b3RlcyhjbGllbnRJZDogc3RyaW5nLCBvcHRpb25zPzoge1xuICBzdGF0dXM/OiBRdW90ZVN0YXR1cztcbiAgc2tpcD86IG51bWJlcjtcbiAgdGFrZT86IG51bWJlcjtcbn0pIHtcbiAgY29uc3QgeyBzdGF0dXMsIHNraXAgPSAwLCB0YWtlID0gNTAgfSA9IG9wdGlvbnMgfHwge307XG5cbiAgY29uc3Qgd2hlcmUgPSB7XG4gICAgY2xpZW50SWQsXG4gICAgLi4uKHN0YXR1cyA/IHsgc3RhdHVzIH0gOiB7fSksXG4gIH07XG5cbiAgY29uc3QgW3F1b3RlcywgdG90YWxdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgIHByaXNtYS5xdW90ZS5maW5kTWFueSh7XG4gICAgICB3aGVyZSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgcXVvdGVpdGVtOiB7XG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgcHJvZHVjdDogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICAgIHNraXAsXG4gICAgICB0YWtlLFxuICAgICAgb3JkZXJCeToge1xuICAgICAgICBjcmVhdGVkQXQ6ICdkZXNjJyxcbiAgICAgIH0sXG4gICAgfSksXG4gICAgcHJpc21hLnF1b3RlLmNvdW50KHsgd2hlcmUgfSksXG4gIF0pO1xuXG4gIHJldHVybiB7IHF1b3RlcywgdG90YWwgfTtcbn1cbiJdLCJuYW1lcyI6WyJwcmlzbWEiLCJRdW90ZVN0YXR1cyIsIk5vdGlmaWNhdGlvblR5cGUiLCJnZW5lcmF0ZVF1b3RlTnVtYmVyIiwiY3JlYXRlTm90aWZpY2F0aW9uRm9yQWxsQWRtaW5zIiwiZ2V0UXVvdGVzIiwib3B0aW9ucyIsImNsaWVudElkIiwic3RhdHVzIiwic2VhcmNoIiwic2tpcCIsInRha2UiLCJ3aGVyZSIsIk9SIiwicXVvdGVOdW1iZXIiLCJjb250YWlucyIsImNsaWVudCIsInVzZXIiLCJmaXJzdG5hbWUiLCJsYXN0bmFtZSIsImNvbXBhbnlfbmFtZSIsInF1b3RlcyIsInRvdGFsIiwiUHJvbWlzZSIsImFsbCIsInF1b3RlIiwiZmluZE1hbnkiLCJpbmNsdWRlIiwic2VsZWN0IiwiZW1haWwiLCJ0ZWxlcGhvbmUiLCJxdW90ZWl0ZW0iLCJwcm9kdWN0Iiwib3JkZXJCeSIsImNyZWF0ZWRBdCIsImNvdW50IiwiZ2V0UXVvdGVCeUlkIiwiaWQiLCJmaW5kVW5pcXVlIiwicm9sZSIsImNhdGVnb3J5IiwiYnJhbmQiLCJwcm9kdWN0aW1hZ2UiLCJjcmVhdGVRdW90ZSIsImRhdGEiLCJub3RlcyIsInZhbGlkVW50aWwiLCJpdGVtcyIsImNyZWF0ZWRCeUFkbWluSWQiLCIkdHJhbnNhY3Rpb24iLCJ0eCIsImNyZWF0ZSIsIkRSQUZUIiwiaXRlbSIsInF1b3RlSXRlbSIsInF1b3RlSWQiLCJwcm9kdWN0SWQiLCJxdWFudGl0eSIsInVuaXRQcmljZSIsIm5vdGlmaWNhdGlvbk1lc3NhZ2UiLCJhZG1pbiIsIlFVT1RFX1JFUVVFU1RFRCIsImVycm9yIiwiY29uc29sZSIsInVwZGF0ZVF1b3RlIiwidG90YWxBbW91bnQiLCJwZGZVcmwiLCJ1cGRhdGVEYXRhIiwidW5kZWZpbmVkIiwiaW5jbHVkZXMiLCJEYXRlIiwidXBkYXRlIiwibGVuZ3RoIiwiZXhpc3RpbmdJdGVtcyIsImV4aXN0aW5nSXRlbXNNYXAiLCJNYXAiLCJtYXAiLCJleGlzdGluZ0l0ZW0iLCJmaW5kIiwiaSIsImdldCIsIm5ld1Byb2R1Y3RJZHMiLCJpdGVtc1RvRGVsZXRlIiwiZmlsdGVyIiwiZGVsZXRlIiwiZGVsZXRlUXVvdGUiLCJkZWxldGVNYW55IiwiY29udmVydFF1b3RlVG9PcmRlciIsIkVycm9yIiwiQVBQUk9WRUQiLCJnZXRDbGllbnRRdW90ZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/quotes.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateOrderNumber: () => (/* binding */ generateOrderNumber),\n/* harmony export */   generateQuoteNumber: () => (/* binding */ generateQuoteNumber),\n/* harmony export */   getDefaultQuoteExpiryDate: () => (/* binding */ getDefaultQuoteExpiryDate)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n/**\n * Génère un numéro de devis unique au format Q-YYYY-XXXX\n * où YYYY est l'année en cours et XXXX est un numéro séquentiel\n */ async function generateQuoteNumber() {\n    const currentYear = new Date().getFullYear();\n    // Trouver le dernier devis de l'année en cours\n    const lastQuote = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findFirst({\n        where: {\n            quoteNumber: {\n                startsWith: `Q-${currentYear}-`\n            }\n        },\n        orderBy: {\n            quoteNumber: 'desc'\n        }\n    });\n    let sequentialNumber = 1;\n    if (lastQuote) {\n        // Extraire le numéro séquentiel du dernier devis\n        const match = lastQuote.quoteNumber.match(/Q-\\d{4}-(\\d+)/);\n        if (match && match[1]) {\n            sequentialNumber = parseInt(match[1], 10) + 1;\n        }\n    }\n    // Formater le numéro séquentiel avec des zéros en préfixe\n    const formattedNumber = sequentialNumber.toString().padStart(4, '0');\n    return `Q-${currentYear}-${formattedNumber}`;\n}\n/**\n * Génère un numéro de commande unique au format O-YYYY-XXXX\n * où YYYY est l'année en cours et XXXX est un numéro séquentiel\n */ async function generateOrderNumber() {\n    const currentYear = new Date().getFullYear();\n    // Trouver la dernière commande de l'année en cours\n    const lastOrder = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.order.findFirst({\n        where: {\n            orderNumber: {\n                startsWith: `O-${currentYear}-`\n            }\n        },\n        orderBy: {\n            orderNumber: 'desc'\n        }\n    });\n    let sequentialNumber = 1;\n    if (lastOrder) {\n        // Extraire le numéro séquentiel de la dernière commande\n        const match = lastOrder.orderNumber.match(/O-\\d{4}-(\\d+)/);\n        if (match && match[1]) {\n            sequentialNumber = parseInt(match[1], 10) + 1;\n        }\n    }\n    // Formater le numéro séquentiel avec des zéros en préfixe\n    const formattedNumber = sequentialNumber.toString().padStart(4, '0');\n    return `O-${currentYear}-${formattedNumber}`;\n}\n/**\n * Formate un prix en MAD\n */ function formatPrice(price) {\n    return new Intl.NumberFormat('fr-MA', {\n        style: 'currency',\n        currency: 'MAD',\n        minimumFractionDigits: 2\n    }).format(price);\n}\n/**\n * Formate une date au format local\n */ function formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('fr-MA', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n    }).format(dateObj);\n}\n/**\n * Calcule la date d'expiration par défaut pour un devis (30 jours à partir d'aujourd'hui)\n */ function getDefaultQuoteExpiryDate() {\n    const date = new Date();\n    date.setDate(date.getDate() + 30);\n    return date;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquotes%2Froute&page=%2Fapi%2Fquotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotes%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();