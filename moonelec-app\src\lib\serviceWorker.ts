// Fonction pour enregistrer le service worker
export function registerServiceWorker() {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('Service Worker enregistré avec succès:', registration);
        })
        .catch((error) => {
          console.error('Erreur lors de l\'enregistrement du Service Worker:', error);
        });
    });
  }
}

// Fonction pour demander la permission de notification
export async function requestNotificationPermission() {
  if (typeof window !== 'undefined' && 'Notification' in window) {
    if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return Notification.permission === 'granted';
  }
  return false;
}

// Fonction pour s'abonner aux notifications push
export async function subscribeToPushNotifications() {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator && 'PushManager' in window) {
    try {
      const registration = await navigator.serviceWorker.ready;
      
      // Vérifier si l'utilisateur est déjà abonné
      let subscription = await registration.pushManager.getSubscription();
      
      if (!subscription) {
        // Créer un nouvel abonnement
        // Note: Dans une application réelle, vous devriez générer une clé VAPID côté serveur
        // et l'utiliser ici. Pour cet exemple, nous utilisons une clé fictive.
        const vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U';
        const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);
        
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: convertedVapidKey
        });
        
        // Dans une application réelle, vous enverriez cet abonnement à votre serveur
        console.log('Nouvel abonnement push créé:', subscription);
      } else {
        console.log('Utilisateur déjà abonné aux notifications push');
      }
      
      return subscription;
    } catch (error) {
      console.error('Erreur lors de l\'abonnement aux notifications push:', error);
      return null;
    }
  }
  return null;
}

// Fonction utilitaire pour convertir une clé VAPID en Uint8Array
function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
  
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  
  return outputArray;
}
