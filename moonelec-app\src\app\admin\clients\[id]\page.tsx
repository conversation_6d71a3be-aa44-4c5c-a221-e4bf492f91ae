'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  FaArrowLeft, 
  FaEdit, 
  FaTrash, 
  Fa<PERSON><PERSON><PERSON>,
  Fa<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaBuilding,
  FaCalendarAlt,
  FaGlobe
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';

interface Client {
  id: string;
  user: {
    id: string;
    firstname: string;
    lastname: string;
    email: string;
    telephone?: string;
    createdAt: string;
    updatedAt: string;
  };
  company?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  createdAt: string;
}

export default function ClientDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [client, setClient] = useState<Client | null>(null);

  // Fetch client details
  const fetchClient = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/admin/clients/${params.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch client');
      }
      
      const data = await response.json();
      setClient(data.client);
    } catch (error) {
      console.error('Error fetching client:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete client
  const handleDeleteClient = async () => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce client ? Cette action est irréversible.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/clients/${params.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete client');
      }
      
      // Redirect to clients list
      router.push('/admin/clients?success=deleted');
    } catch (error) {
      console.error('Error deleting client:', error);
      alert('Erreur lors de la suppression du client');
    }
  };

  // Fetch client on initial load
  useEffect(() => {
    if (user) {
      fetchClient();
    }
  }, [user, params.id]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <FaSpinner className="animate-spin text-4xl text-primary" />
      </div>
    );
  }

  if (!client) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
            Client Non Trouvé
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Le client que vous recherchez n'existe pas ou vous n'avez pas les permissions pour le voir.
          </p>
          <button
            onClick={() => router.push('/admin/clients')}
            className="flex items-center text-primary hover:text-primary-dark"
          >
            <FaArrowLeft className="mr-2" />
            Retour à la liste des clients
          </button>
        </div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <button
              onClick={() => router.push('/admin/clients')}
              className="mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
            >
              <FaArrowLeft className="text-xl" />
            </button>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Détails du Client
            </h1>
          </div>
          
          <div className="flex gap-3">
            <Link href={`/admin/clients/${client.id}/edit`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                <FaEdit />
                <span>Modifier</span>
              </motion.button>
            </Link>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleDeleteClient}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              <FaTrash />
              <span>Supprimer</span>
            </motion.button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              {/* Client Header */}
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white mr-4">
                  <FaUser className="text-2xl" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                    {client.user.firstname} {client.user.lastname}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300">
                    ID: {client.id}
                  </p>
                </div>
              </div>
              
              {/* Personal Information */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  Informations Personnelles
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <FaEnvelope className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                      <p className="text-gray-800 dark:text-white">{client.user.email}</p>
                    </div>
                  </div>
                  
                  {client.user.telephone && (
                    <div className="flex items-start">
                      <FaPhone className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Téléphone</p>
                        <p className="text-gray-800 dark:text-white">{client.user.telephone}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Company Information */}
              {(client.company || client.address || client.city) && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    Informations de l'Entreprise
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {client.company && (
                      <div className="flex items-start">
                        <FaBuilding className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Entreprise</p>
                          <p className="text-gray-800 dark:text-white">{client.company}</p>
                        </div>
                      </div>
                    )}
                    
                    {client.address && (
                      <div className="flex items-start md:col-span-2">
                        <FaMapMarkerAlt className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Adresse</p>
                          <p className="text-gray-800 dark:text-white">{client.address}</p>
                          {(client.city || client.postalCode) && (
                            <p className="text-gray-800 dark:text-white">
                              {client.postalCode && `${client.postalCode} `}
                              {client.city}
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {client.country && (
                      <div className="flex items-start">
                        <FaGlobe className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Pays</p>
                          <p className="text-gray-800 dark:text-white">{client.country}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Sidebar Information */}
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                Informations du Compte
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <FaCalendarAlt className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Date d'inscription</p>
                    <p className="text-gray-800 dark:text-white">
                      {new Date(client.user.createdAt).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <FaCalendarAlt className="text-gray-500 dark:text-gray-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Dernière mise à jour</p>
                    <p className="text-gray-800 dark:text-white">
                      {new Date(client.user.updatedAt).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>
                
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Statut</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Actif
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                Actions Rapides
              </h3>
              
              <div className="space-y-3">
                <Link href={`/admin/clients/${client.id}/edit`}>
                  <button className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <FaEdit />
                    <span>Modifier le Client</span>
                  </button>
                </Link>
                
                <button
                  onClick={handleDeleteClient}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  <FaTrash />
                  <span>Supprimer le Client</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RouteGuard>
  );
}
