import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { uploadSalesReportFiles } from '@/lib/salesReports';

// POST /api/sales-reports/upload - Upload files for a sales report
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only commercials can upload files
    if (session.user.role !== 'COMMERCIAL') {
      return NextResponse.json(
        { error: 'Only sales team members can upload files' },
        { status: 403 }
      );
    }

    const formData = await req.formData();
    
    // Extract file uploads
    const imageFiles: File[] = [];
    let videoFile: File | null = null;
    let audioFile: File | null = null;
    let pdfFile: File | null = null;
    
    // Process images (multiple files)
    for (let i = 0; formData.get(`image${i}`); i++) {
      const file = formData.get(`image${i}`) as File;
      if (file) {
        imageFiles.push(file);
      }
    }
    
    // Process other files (single files)
    videoFile = formData.get('video') as File || null;
    audioFile = formData.get('audio') as File || null;
    pdfFile = formData.get('pdf') as File || null;
    
    // Upload files
    const uploadResults = await uploadSalesReportFiles({
      images: imageFiles.length > 0 ? imageFiles : undefined,
      video: videoFile,
      audio: audioFile,
      pdf: pdfFile,
    });

    return NextResponse.json({ success: true, ...uploadResults });
  } catch (error: any) {
    console.error('Error uploading files:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to upload files' },
      { status: 500 }
    );
  }
}
