import { NextRequest, NextResponse } from 'next/server';
import { getProducts, createProduct } from '@/lib/products';

// GET /api/products - Get all products with optional filtering
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const categoryId = searchParams.get('categoryId') || undefined;
    const brandId = searchParams.get('brandId') || undefined;
    const search = searchParams.get('search') || undefined;
    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;
    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;

    const { products, total } = await getProducts({
      categoryId,
      brandId,
      search,
      skip,
      take,
    });

    return NextResponse.json({ products, total });
  } catch (error: any) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST /api/products - Create a new product
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      reference,
      name,
      description,
      characteristics,
      mainImage,
      categoryId,
      brandId,
      images,
    } = body;

    // Validate required fields
    if (!reference || !name || !description) {
      return NextResponse.json(
        { error: 'Reference, name, and description are required' },
        { status: 400 }
      );
    }

    // Create the product
    const product = await createProduct({
      reference,
      name,
      description,
      characteristics: characteristics || {},
      mainImage,
      categoryId: categoryId || undefined,
      brandId: brandId || undefined,
      images,
    });

    return NextResponse.json(product, { status: 201 });
  } catch (error: any) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create product' },
      { status: 500 }
    );
  }
}
