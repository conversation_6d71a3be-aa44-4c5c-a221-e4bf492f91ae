{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface LogoProps {\n  width?: number;\n  height?: number;\n  className?: string;\n  linkClassName?: string;\n  href?: string;\n}\n\nexport default function Logo({\n  width = 180,\n  height = 60,\n  className = '',\n  linkClassName = '',\n  href = '/'\n}: LogoProps) {\n  return (\n    <Link href={href} className={`flex items-center ${linkClassName}`}>\n      <Image\n        src=\"/images/logo/logo-moonelec.png\"\n        alt=\"Moonelec Logo\"\n        width={width}\n        height={height}\n        className={className}\n        priority\n      />\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS,KAAK,EAC3B,QAAQ,GAAG,EACX,SAAS,EAAE,EACX,YAAY,EAAE,EACd,gBAAgB,EAAE,EAClB,OAAO,GAAG,EACA;IACV,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW,CAAC,kBAAkB,EAAE,eAAe;kBAC/D,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAI;YACJ,KAAI;YACJ,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;;;;;;;;;;;AAIhB;KAnBwB", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\n\nexport function useAuth() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading = status === 'loading';\n  const user = session?.user;\n\n  const isClient = isAuthenticated && user?.role === 'CLIENT';\n  const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';\n  const isAdmin = isAuthenticated && user?.role === 'ADMIN';\n\n  const login = async (username: string, password: string) => {\n    const result = await signIn('credentials', {\n      username,\n      password,\n      redirect: false,\n    });\n\n    return result;\n  };\n\n  const logout = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const redirectToLogin = () => {\n    router.push('/auth/signin');\n  };\n\n  const redirectToDashboard = () => {\n    if (isAdmin) {\n      router.push('/admin/quotes');\n    } else if (isCommercial) {\n      router.push('/commercial/quotes');\n    } else if (isClient) {\n      router.push('/account/quotes');\n    } else {\n      router.push('/');\n    }\n  };\n\n  return {\n    session,\n    status,\n    user,\n    isAuthenticated,\n    isLoading,\n    isClient,\n    isCommercial,\n    isAdmin,\n    login,\n    logout,\n    redirectToLogin,\n    redirectToDashboard,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,WAAW;IACnC,MAAM,YAAY,WAAW;IAC7B,MAAM,OAAO,SAAS;IAEtB,MAAM,WAAW,mBAAmB,MAAM,SAAS;IACnD,MAAM,eAAe,mBAAmB,MAAM,SAAS;IACvD,MAAM,UAAU,mBAAmB,MAAM,SAAS;IAElD,MAAM,QAAQ,OAAO,UAAkB;QACrC,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;YACzC;YACA;YACA,UAAU;QACZ;QAEA,OAAO;IACT;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,cAAc;YACvB,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,UAAU;YACnB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzDgB;;QACoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/auth/LoginForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { FaU<PERSON>, <PERSON>aLock, FaEye, FaEyeSlash, FaGoogle, FaFacebook } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\n\nexport default function LoginForm() {\n  const router = useRouter();\n  const { login, redirectToDashboard } = useAuth();\n  \n  const [formState, setFormState] = useState({\n    username: '',\n    password: '',\n    rememberMe: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    setFormState(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    \n    // Effacer l'erreur lorsque l'utilisateur modifie les champs\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formState.username || !formState.password) {\n      setError('Veuillez remplir tous les champs');\n      return;\n    }\n    \n    setIsSubmitting(true);\n    setError('');\n    \n    try {\n      const result = await login(formState.username, formState.password);\n      \n      if (result?.error) {\n        throw new Error(result.error || 'Identifiants invalides');\n      }\n      \n      // Redirection vers le tableau de bord approprié\n      redirectToDashboard();\n    } catch (error: any) {\n      setError(error.message || 'Une erreur est survenue lors de la connexion');\n      setIsSubmitting(false);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n        className=\"bg-white dark:bg-[#1a1a1a] rounded-lg shadow-xl overflow-hidden\"\n      >\n        <div className=\"p-8\">\n          <div className=\"text-center mb-8\">\n            <h2 className=\"text-2xl font-bold text-text-primary mb-2\">Connexion</h2>\n            <p className=\"text-text-secondary\">\n              Accédez à votre compte Moonelec\n            </p>\n          </div>\n\n          {error && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              className=\"mb-4 p-3 bg-red-100 text-red-700 rounded-md\"\n            >\n              {error}\n            </motion.div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.1 }}\n            >\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                Nom d'utilisateur\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\">\n                  <FaUser />\n                </div>\n                <input\n                  type=\"text\"\n                  id=\"username\"\n                  name=\"username\"\n                  value={formState.username}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                  placeholder=\"Nom d'utilisateur\"\n                />\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.2 }}\n            >\n              <div className=\"flex items-center justify-between mb-1\">\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-text-secondary\">\n                  Mot de passe\n                </label>\n                <Link href=\"/auth/forgot-password\" className=\"text-sm text-primary hover:underline\">\n                  Mot de passe oublié?\n                </Link>\n              </div>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\">\n                  <FaLock />\n                </div>\n                <input\n                  type={showPassword ? \"text\" : \"password\"}\n                  id=\"password\"\n                  name=\"password\"\n                  value={formState.password}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\"\n                  placeholder=\"••••••••\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={togglePasswordVisibility}\n                  className=\"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  {showPassword ? <FaEyeSlash /> : <FaEye />}\n                </button>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.3 }}\n              className=\"flex items-center\"\n            >\n              <input\n                type=\"checkbox\"\n                id=\"rememberMe\"\n                name=\"rememberMe\"\n                checked={formState.rememberMe}\n                onChange={handleChange}\n                className=\"w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary\"\n              />\n              <label htmlFor=\"rememberMe\" className=\"ml-2 text-sm text-text-secondary\">\n                Se souvenir de moi\n              </label>\n            </motion.div>\n\n            <motion.button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`btn-primary w-full py-3 ${\n                isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\n              }`}\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: 0.4 }}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {isSubmitting ? (\n                <span className=\"flex items-center justify-center\">\n                  <span className=\"animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2\"></span>\n                  Connexion en cours...\n                </span>\n              ) : (\n                'Se connecter'\n              )}\n            </motion.button>\n          </form>\n\n          <motion.div\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3, delay: 0.5 }}\n            className=\"mt-6\"\n          >\n            <div className=\"relative flex items-center justify-center\">\n              <div className=\"border-t border-gray-300 dark:border-gray-700 w-full\"></div>\n              <div className=\"absolute bg-white dark:bg-[#1a1a1a] px-4 text-sm text-gray-500\">ou</div>\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4 mt-6\">\n              <button\n                type=\"button\"\n                className=\"flex items-center justify-center gap-2 py-2.5 px-4 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-[#222] transition-colors\"\n              >\n                <FaGoogle className=\"text-red-500\" />\n                <span className=\"text-sm font-medium\">Google</span>\n              </button>\n              <button\n                type=\"button\"\n                className=\"flex items-center justify-center gap-2 py-2.5 px-4 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-[#222] transition-colors\"\n              >\n                <FaFacebook className=\"text-blue-600\" />\n                <span className=\"text-sm font-medium\">Facebook</span>\n              </button>\n            </div>\n          </motion.div>\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: 0.6 }}\n          className=\"bg-gray-50 dark:bg-[#111] py-4 px-8 text-center\"\n        >\n          <p className=\"text-text-secondary\">\n            Vous n'avez pas de compte?{' '}\n            <Link href=\"/auth/signup\" className=\"text-primary font-medium hover:underline\">\n              S'inscrire\n            </Link>\n          </p>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,UAAU;QACV,UAAU;QACV,YAAY;IACd;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;QAED,4DAA4D;QAC5D,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ,EAAE;YAC9C,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,EAAE,UAAU,QAAQ;YAEjE,IAAI,QAAQ,OAAO;gBACjB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,gDAAgD;YAChD;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;YAC1B,gBAAgB;QAClB;IACF;IAEA,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;wBAKpC,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,WAAU;sCAET;;;;;;sCAIL,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAqD;;;;;;sDAGzF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,UAAU,QAAQ;oDACzB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAgD;;;;;;8DAGpF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAuC;;;;;;;;;;;;sDAItF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;8DAET,6LAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,IAAG;oDACH,MAAK;oDACL,OAAO,UAAU,QAAQ;oDACzB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DAET,6BAAe,6LAAC,iJAAA,CAAA,aAAU;;;;6EAAM,6LAAC,iJAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;;;;;;8CAK7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,SAAS,UAAU,UAAU;4CAC7B,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAmC;;;;;;;;;;;;8CAK3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,wBAAwB,EAClC,eAAe,kCAAkC,IACjD;oCACF,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,6BACC,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;;;;;;4CAA2F;;;;;;+CAI7G;;;;;;;;;;;;sCAKN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDAAiE;;;;;;;;;;;;8CAGlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC,iJAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAExC,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC,iJAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAE,WAAU;;4BAAsB;4BACN;0CAC3B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAe,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3F;GAvOwB;;QACP,qIAAA,CAAA,YAAS;QACe,0HAAA,CAAA,UAAO;;;KAFxB", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  animate?: boolean;\n  delay?: number;\n  onClick?: () => void;\n  hoverEffect?: boolean;\n}\n\nexport default function Card({\n  children,\n  className = '',\n  animate = false,\n  delay = 0,\n  onClick,\n  hoverEffect = true\n}: CardProps) {\n  const baseStyles = 'bg-white dark:bg-[#1a1a1a] rounded-lg shadow-md overflow-hidden';\n  \n  if (animate) {\n    return (\n      <motion.div\n        className={`${baseStyles} ${className}`}\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        viewport={{ once: true, margin: '-50px' }}\n        transition={{ duration: 0.5, delay }}\n        onClick={onClick}\n        whileHover={hoverEffect ? { y: -5, transition: { duration: 0.2 } } : {}}\n      >\n        {children}\n      </motion.div>\n    );\n  }\n  \n  return (\n    <div \n      className={`${baseStyles} ${className} ${hoverEffect ? 'transition-transform hover:-translate-y-1 duration-200' : ''}`}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAce,SAAS,KAAK,EAC3B,QAAQ,EACR,YAAY,EAAE,EACd,UAAU,KAAK,EACf,QAAQ,CAAC,EACT,OAAO,EACP,cAAc,IAAI,EACR;IACV,MAAM,aAAa;IAEnB,IAAI,SAAS;QACX,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,GAAG,WAAW,CAAC,EAAE,WAAW;YACvC,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAQ;YACxC,YAAY;gBAAE,UAAU;gBAAK;YAAM;YACnC,SAAS;YACT,YAAY,cAAc;gBAAE,GAAG,CAAC;gBAAG,YAAY;oBAAE,UAAU;gBAAI;YAAE,IAAI,CAAC;sBAErE;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW,GAAG,WAAW,CAAC,EAAE,UAAU,CAAC,EAAE,cAAc,2DAA2D,IAAI;QACtH,SAAS;kBAER;;;;;;AAGP;KAlCwB", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Logo from '@/components/ui/Logo';\nimport LoginForm from '@/components/auth/LoginForm';\nimport Card from '@/components/ui/Card';\nimport { useAuth } from '@/hooks/useAuth';\n\nexport default function SignInPage() {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, redirectToDashboard } = useAuth();\n\n  useEffect(() => {\n    // Si l'utilisateur est déjà authentifié, le rediriger vers son tableau de bord\n    if (!isLoading && isAuthenticated) {\n      redirectToDashboard();\n    }\n  }, [isLoading, isAuthenticated, redirectToDashboard]);\n\n  // Ne pas afficher la page si l'utilisateur est déjà authentifié\n  if (isLoading || isAuthenticated) {\n    return null;\n  }\n  return (\n    <div className=\"min-h-screen flex flex-col md:flex-row\">\n      {/* Left Side - Form */}\n      <div className=\"w-full md:w-1/2 flex flex-col justify-center items-center p-8 md:p-16\">\n        <div className=\"w-full max-w-md\">\n          <div className=\"mb-8 flex justify-center md:justify-start\">\n            <Logo width={180} height={60} />\n          </div>\n\n          <LoginForm />\n        </div>\n      </div>\n\n      {/* Right Side - Image */}\n      <div className=\"hidden md:block md:w-1/2 relative bg-primary\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary to-primary/70\"></div>\n        <div className=\"absolute inset-0 flex flex-col justify-center items-center text-white p-16\">\n          <h2 className=\"text-3xl font-bold mb-6 text-center\">\n            Bienvenue chez Moonelec\n          </h2>\n          <p className=\"text-lg text-center mb-8\">\n            Accédez à votre compte pour découvrir notre gamme complète de produits électriques et bénéficier de nos services exclusifs.\n          </p>\n          <div className=\"grid grid-cols-2 gap-6 w-full max-w-md\">\n            {[\n              { title: \"+300 000\", description: \"Références produits\" },\n              { title: \"Depuis 1990\", description: \"À votre service\" },\n              { title: \"Livraison rapide\", description: \"Partout en France\" },\n              { title: \"Support expert\", description: \"À votre écoute\" }\n            ].map((item, index) => (\n              <Card key={index} className=\"bg-white/10 backdrop-blur-sm p-4 border-none\">\n                <h3 className=\"font-semibold mb-2\">{item.title}</h3>\n                <p className=\"text-sm\">{item.description}</p>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,+EAA+E;YAC/E,IAAI,CAAC,aAAa,iBAAiB;gBACjC;YACF;QACF;+BAAG;QAAC;QAAW;QAAiB;KAAoB;IAEpD,gEAAgE;IAChE,IAAI,aAAa,iBAAiB;QAChC,OAAO;IACT;IACA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,UAAI;gCAAC,OAAO;gCAAK,QAAQ;;;;;;;;;;;sCAG5B,6LAAC,0IAAA,CAAA,UAAS;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAY,aAAa;oCAAsB;oCACxD;wCAAE,OAAO;wCAAe,aAAa;oCAAkB;oCACvD;wCAAE,OAAO;wCAAoB,aAAa;oCAAoB;oCAC9D;wCAAE,OAAO;wCAAkB,aAAa;oCAAiB;iCAC1D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,mIAAA,CAAA,UAAI;wCAAa,WAAU;;0DAC1B,6LAAC;gDAAG,WAAU;0DAAsB,KAAK,KAAK;;;;;;0DAC9C,6LAAC;gDAAE,WAAU;0DAAW,KAAK,WAAW;;;;;;;uCAF/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzB;GAvDwB;;QACP,qIAAA,CAAA,YAAS;QACoC,0HAAA,CAAA,UAAO;;;KAF7C", "debugId": null}}]}