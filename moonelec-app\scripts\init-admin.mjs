// Ce script permet d'initialiser l'administrateur par défaut
// Exécutez-le avec: node scripts/init-admin.mjs

import fetch from 'node-fetch';

async function initAdmin() {
  try {
    console.log('Initialisation de l\'administrateur par défaut...');
    
    const response = await fetch('http://localhost:3001/api/admin/init', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: 'moonelec-admin-init-token',
      }),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ ' + data.message);
      console.log('\nInformations de connexion:');
      console.log('- Nom d\'utilisateur: hicham.ezzamzami');
      console.log('- Mot de passe: 123456');
      console.log('\n⚠️ Veuillez changer ce mot de passe après votre première connexion!');
    } else {
      console.error('❌ Erreur:', data.error);
    }
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    console.log('\nAssurez-vous que:');
    console.log('1. Le serveur Next.js est en cours d\'exécution (npm run dev)');
    console.log('2. Le serveur est accessible à l\'adresse http://localhost:3001');
  }
}

initAdmin();
