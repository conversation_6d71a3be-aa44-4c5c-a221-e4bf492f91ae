'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
}

interface ExtractionResult {
  success: boolean;
  data: ExtractedProductData;
  metadata: {
    fileName: string;
    fileSize: number;
    textLength: number;
    extractedAt: string;
  };
}

interface PdfExtractorProps {
  onDataExtracted?: (data: ExtractedProductData) => void;
  onClose?: () => void;
}

export default function PdfExtractor({ onDataExtracted, onClose }: PdfExtractorProps) {
  const [file, setFile] = useState<File | null>(null);
  const [extractedData, setExtractedData] = useState<ExtractedProductData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (selectedFile: File) => {
    setError(null);
    setExtractedData(null);

    // Validation du fichier
    if (selectedFile.type !== 'application/pdf') {
      setError('Veuillez sélectionner un fichier PDF');
      return;
    }

    if (selectedFile.size > 10 * 1024 * 1024) {
      setError('Le fichier doit faire moins de 10MB');
      return;
    }

    setFile(selectedFile);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const extractData = async () => {
    if (!file) {
      setError('Veuillez sélectionner un fichier PDF');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/extract-pdf', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erreur lors de l\'extraction');
      }

      const extractionResult = result as ExtractionResult;
      setExtractedData(extractionResult.data);

      // Callback pour utiliser les données extraites
      if (onDataExtracted) {
        onDataExtracted(extractionResult.data);
      }

    } catch (err) {
      console.error('Extraction error:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'extraction');
    } finally {
      setLoading(false);
    }
  };

  const formatCharacteristics = (characteristics: Record<string, string>) => {
    return Object.entries(characteristics)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  };

  const useExtractedData = () => {
    if (extractedData && onDataExtracted) {
      onDataExtracted(extractedData);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">🤖 Extraction PDF avec IA</h2>
          <p className="text-gray-600 mt-1">
            Extrayez automatiquement les données produit depuis une fiche technique PDF
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ✕
          </button>
        )}
      </div>

      {/* Zone de drop */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="application/pdf"
          onChange={handleFileChange}
          className="hidden"
        />

        <div className="space-y-4">
          <div className="text-6xl">📄</div>
          <div>
            <p className="text-lg font-medium text-gray-900">
              {file ? file.name : 'Glissez votre PDF ici ou cliquez pour sélectionner'}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Formats supportés: PDF • Taille max: 10MB
            </p>
          </div>

          {file && (
            <div className="text-sm text-green-600">
              ✅ Fichier sélectionné: {(file.size / 1024 / 1024).toFixed(2)} MB
            </div>
          )}
        </div>
      </div>

      {/* Bouton d'extraction */}
      <div className="mt-6 flex justify-center">
        <motion.button
          onClick={extractData}
          disabled={!file || loading}
          className={`px-8 py-3 rounded-lg font-medium transition-all ${
            !file || loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl'
          }`}
          whileHover={!file || loading ? {} : { scale: 1.05 }}
          whileTap={!file || loading ? {} : { scale: 0.95 }}
        >
          {loading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
              <span>Extraction en cours...</span>
            </div>
          ) : (
            '🚀 Extraire les données'
          )}
        </motion.button>
      </div>

      {/* Erreur */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg"
          >
            <div className="flex items-center space-x-2">
              <span className="text-red-500">❌</span>
              <span className="text-red-700 font-medium">Erreur</span>
            </div>
            <p className="text-red-600 mt-1">{error}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Données extraites */}
      <AnimatePresence>
        {extractedData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 p-6 bg-green-50 border border-green-200 rounded-lg"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-green-800">
                ✅ Données extraites avec succès !
              </h3>
              {onDataExtracted && (
                <button
                  onClick={useExtractedData}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Utiliser ces données
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Informations générales</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Nom:</strong> {extractedData.productName}</div>
                  <div><strong>Référence:</strong> {
                    Array.isArray(extractedData.reference)
                      ? extractedData.reference.join(', ')
                      : extractedData.reference
                  }</div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Caractéristiques</h4>
                <div className="text-sm space-y-1">
                  {Object.entries(extractedData.characteristics).map(([key, value]) => (
                    <div key={key}>
                      <strong>{key}:</strong> {value}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-4">
              <h4 className="font-semibold text-gray-900 mb-2">Description</h4>
              <p className="text-sm text-gray-700 bg-white p-3 rounded border">
                {extractedData.description}
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
