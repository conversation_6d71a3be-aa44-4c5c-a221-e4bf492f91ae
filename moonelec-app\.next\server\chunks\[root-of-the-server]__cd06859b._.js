module.exports = {

"[project]/.next-internal/server/app/api/commercials/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminUser": (()=>createAdminUser),
    "createClientUser": (()=>createClientUser),
    "createCommercialUser": (()=>createCommercialUser),
    "findUserByEmail": (()=>findUserByEmail),
    "findUserByUsername": (()=>findUserByUsername),
    "hashPassword": (()=>hashPassword),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
async function hashPassword(password) {
    const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(10);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, salt);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
async function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].CLIENT
            }
        });
        // Créer le profil client associé
        const client = await tx.client.create({
            data: {
                userId: user.id,
                company_name
            }
        });
        return {
            user,
            client
        };
    });
}
async function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].COMMERCIAL
            }
        });
        // Créer le profil commercial associé
        const commercial = await tx.commercial.create({
            data: {
                userId: user.id,
                profile_photo
            }
        });
        return {
            user,
            commercial
        };
    });
}
async function createAdminUser({ email, username, password, lastname, firstname, telephone }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].ADMIN
            }
        });
        // Créer le profil admin associé
        const admin = await tx.admin.create({
            data: {
                userId: user.id
            }
        });
        return {
            user,
            admin
        };
    });
}
async function findUserByUsername(username) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            username
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
async function findUserByEmail(email) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            email
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
}}),
"[project]/src/lib/commercials.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "assignClientToCommercial": (()=>assignClientToCommercial),
    "createCommercial": (()=>createCommercial),
    "deleteCommercial": (()=>deleteCommercial),
    "getCommercialById": (()=>getCommercialById),
    "getCommercialClients": (()=>getCommercialClients),
    "getCommercialStats": (()=>getCommercialStats),
    "getCommercials": (()=>getCommercials),
    "removeClientFromCommercial": (()=>removeClientFromCommercial),
    "updateCommercial": (()=>updateCommercial)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
async function getCommercials(options) {
    const { search, skip = 0, take = 50 } = options || {};
    const where = search ? {
        user: {
            OR: [
                {
                    firstname: {
                        contains: search
                    }
                },
                {
                    lastname: {
                        contains: search
                    }
                },
                {
                    email: {
                        contains: search
                    }
                },
                {
                    username: {
                        contains: search
                    }
                }
            ]
        }
    } : {};
    const [commercials, total] = await Promise.all([
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercial.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        username: true,
                        firstname: true,
                        lastname: true,
                        telephone: true,
                        createdAt: true,
                        updatedAt: true,
                        role: true
                    }
                },
                commercialclient: {
                    include: {
                        client: {
                            include: {
                                user: {
                                    select: {
                                        firstname: true,
                                        lastname: true,
                                        email: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
            skip,
            take,
            orderBy: {
                user: {
                    lastname: 'asc'
                }
            }
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercial.count({
            where
        })
    ]);
    return {
        commercials,
        total
    };
}
async function getCommercialById(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercial.findUnique({
        where: {
            id
        },
        include: {
            user: {
                select: {
                    id: true,
                    email: true,
                    username: true,
                    firstname: true,
                    lastname: true,
                    telephone: true,
                    createdAt: true,
                    updatedAt: true,
                    role: true
                }
            },
            commercialclient: {
                include: {
                    client: {
                        include: {
                            user: {
                                select: {
                                    firstname: true,
                                    lastname: true,
                                    email: true
                                }
                            }
                        }
                    }
                }
            }
        }
    });
}
async function createCommercial(data) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCommercialUser"])(data);
        return result;
    } catch (error) {
        console.error('Error creating commercial:', error);
        throw error;
    }
}
async function updateCommercial(id, data) {
    const { email, firstname, lastname, telephone, profile_photo } = data;
    // Trouver le commercial pour obtenir l'ID de l'utilisateur
    const commercial = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercial.findUnique({
        where: {
            id
        },
        include: {
            user: true
        }
    });
    if (!commercial) {
        throw new Error('Commercial not found');
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Mettre à jour les informations de l'utilisateur
        if (email || firstname || lastname || telephone) {
            await tx.user.update({
                where: {
                    id: commercial.userId
                },
                data: {
                    ...email && {
                        email
                    },
                    ...firstname && {
                        firstname
                    },
                    ...lastname && {
                        lastname
                    },
                    ...telephone && {
                        telephone
                    }
                }
            });
        }
        // Mettre à jour les informations spécifiques au commercial
        if (profile_photo !== undefined) {
            await tx.commercial.update({
                where: {
                    id
                },
                data: {
                    profile_photo
                }
            });
        }
        // Récupérer le commercial mis à jour
        return tx.commercial.findUnique({
            where: {
                id
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        username: true,
                        firstname: true,
                        lastname: true,
                        telephone: true,
                        createdAt: true,
                        updatedAt: true,
                        role: true
                    }
                }
            }
        });
    });
}
async function deleteCommercial(id) {
    // Trouver le commercial pour obtenir l'ID de l'utilisateur
    const commercial = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercial.findUnique({
        where: {
            id
        },
        include: {
            user: true
        }
    });
    if (!commercial) {
        throw new Error('Commercial not found');
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Supprimer d'abord les relations avec les clients
        await tx.commercialclient.deleteMany({
            where: {
                commercialId: id
            }
        });
        // Supprimer le commercial
        await tx.commercial.delete({
            where: {
                id
            }
        });
        // Supprimer l'utilisateur associé
        return tx.user.delete({
            where: {
                id: commercial.userId
            }
        });
    });
}
async function assignClientToCommercial(commercialId, clientId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercialclient.create({
        data: {
            commercialId,
            clientId
        }
    });
}
async function removeClientFromCommercial(commercialId, clientId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercialclient.delete({
        where: {
            commercialId_clientId: {
                commercialId,
                clientId
            }
        }
    });
}
async function getCommercialClients(commercialId) {
    const commercialClients = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercialclient.findMany({
        where: {
            commercialId
        },
        include: {
            client: {
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            username: true,
                            firstname: true,
                            lastname: true,
                            telephone: true
                        }
                    }
                }
            }
        }
    });
    return commercialClients.map((cc)=>cc.client);
}
async function getCommercialStats(commercialId) {
    // Nombre de clients
    const clientCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercialclient.count({
        where: {
            commercialId
        }
    });
    // Nombre de commandes des clients de ce commercial
    const clients = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].commercialclient.findMany({
        where: {
            commercialId
        },
        select: {
            clientId: true
        }
    });
    const clientIds = clients.map((c)=>c.clientId);
    const orderCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.count({
        where: {
            clientId: {
                in: clientIds
            }
        }
    });
    // Montant total des commandes
    const orders = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.findMany({
        where: {
            clientId: {
                in: clientIds
            }
        },
        select: {
            totalAmount: true
        }
    });
    const totalAmount = orders.reduce((sum, order)=>sum + order.totalAmount, 0);
    return {
        clientCount,
        orderCount,
        totalAmount
    };
}
}}),
"[project]/src/app/api/commercials/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$commercials$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/commercials.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
;
async function GET(req) {
    try {
        const searchParams = req.nextUrl.searchParams;
        const search = searchParams.get('search') || undefined;
        const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')) : undefined;
        const take = searchParams.has('take') ? parseInt(searchParams.get('take')) : undefined;
        const { commercials, total } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$commercials$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCommercials"])({
            search,
            skip,
            take
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            commercials,
            total
        });
    } catch (error) {
        console.error('Error fetching commercials:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to fetch commercials'
        }, {
            status: 500
        });
    }
}
async function POST(req) {
    try {
        const body = await req.json();
        const { email, username, password, firstname, lastname, telephone, profile_photo } = body;
        // Vérifier que tous les champs requis sont présents
        if (!email || !username || !password || !firstname || !lastname) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Email, username, password, firstname, and lastname are required'
            }, {
                status: 400
            });
        }
        // Vérifier si l'email est déjà utilisé
        const existingEmail = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findUserByEmail"])(email);
        if (existingEmail) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Email is already in use'
            }, {
                status: 400
            });
        }
        // Vérifier si le nom d'utilisateur est déjà utilisé
        const existingUsername = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findUserByUsername"])(username);
        if (existingUsername) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Username is already in use'
            }, {
                status: 400
            });
        }
        // Créer le commercial
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$commercials$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCommercial"])({
            email,
            username,
            password,
            firstname,
            lastname,
            telephone,
            profile_photo
        });
        // Retourner une réponse de succès sans le mot de passe
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            commercial: {
                id: result.commercial.id,
                user: {
                    id: result.user.id,
                    email: result.user.email,
                    username: result.user.username,
                    firstname: result.user.firstname,
                    lastname: result.user.lastname,
                    telephone: result.user.telephone,
                    role: result.user.role
                },
                profile_photo: result.commercial.profile_photo
            }
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating commercial:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to create commercial'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cd06859b._.js.map