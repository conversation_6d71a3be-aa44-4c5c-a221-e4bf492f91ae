// Service Worker pour les notifications en arrière-plan
self.addEventListener('install', (event) => {
  console.log('Service Worker installé');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker activé');
  return self.clients.claim();
});

// Écouter les événements push
self.addEventListener('push', (event) => {
  if (!event.data) return;

  try {
    const data = event.data.json();
    
    const options = {
      body: data.message,
      icon: '/images/logo/logo-moonelec.png',
      badge: '/images/logo/logo-moonelec.png',
      tag: data.id,
      requireInteraction: true,
      vibrate: [200, 100, 200],
      data: {
        url: data.quoteId ? `/admin/quotes/${data.quoteId}` : '/admin/quotes',
      },
    };

    event.waitUntil(
      self.registration.showNotification('Moonelec - Nouvelle demande de devis', options)
    );
  } catch (error) {
    console.error('Erreur lors du traitement de la notification push:', error);
  }
});

// Gérer le clic sur la notification
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  const url = event.notification.data.url || '/admin/quotes';

  event.waitUntil(
    clients.matchAll({ type: 'window' }).then((clientList) => {
      // Vérifier si une fenêtre est déjà ouverte et la focaliser
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          client.navigate(url);
          return client.focus();
        }
      }
      
      // Sinon, ouvrir une nouvelle fenêtre
      if (clients.openWindow) {
        return clients.openWindow(url);
      }
    })
  );
});
