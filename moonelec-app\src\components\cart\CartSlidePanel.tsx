'use client';

import { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaTrash, FaPlus, FaMinus, FaFileAlt, FaShoppingCart } from 'react-icons/fa';
import Image from 'next/image';
import Link from 'next/link';
import { useCart } from '@/context/CartContext';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface CartSlidePanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CartSlidePanel({ isOpen, onClose }: CartSlidePanelProps) {
  const { items, updateItemQuantity, removeItem, notes, setNotes } = useCart();
  const { isAuthenticated, redirectToLogin } = useAuth();
  const router = useRouter();
  const panelRef = useRef<HTMLDivElement>(null);

  // Fermer le panneau lorsque l'utilisateur clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node) && isOpen) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    
    // Désactiver le défilement du body lorsque le panneau est ouvert
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Gérer la demande de devis
  const handleRequestQuote = () => {
    if (!isAuthenticated) {
      redirectToLogin();
      return;
    }
    
    onClose();
    router.push('/cart');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black z-50"
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            ref={panelRef}
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="fixed top-0 right-0 h-full w-full sm:w-96 bg-white dark:bg-gray-800 shadow-xl z-50 overflow-y-auto"
          >
            {/* Header */}
            <div className="sticky top-0 bg-white dark:bg-gray-800 z-10 border-b border-gray-200 dark:border-gray-700 p-4 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                <FaShoppingCart className="mr-2" />
                Votre Panier ({items.length})
              </h2>
              <button
                onClick={onClose}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                aria-label="Fermer"
              >
                <FaTimes className="text-gray-600 dark:text-gray-300" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4">
              {items.length === 0 ? (
                <div className="text-center py-8">
                  <div className="flex justify-center mb-4">
                    <FaShoppingCart className="text-5xl text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                    Votre panier est vide
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Parcourez notre catalogue pour ajouter des produits à votre panier.
                  </p>
                  <Link href="/products">
                    <button
                      onClick={onClose}
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                    >
                      Découvrir nos produits
                    </button>
                  </Link>
                </div>
              ) : (
                <>
                  {/* Cart Items */}
                  <div className="divide-y divide-gray-200 dark:divide-gray-700 mb-4">
                    {items.map((item) => (
                      <div key={item.id} className="py-4 flex items-start">
                        {/* Product Image */}
                        <div className="w-16 h-16 relative flex-shrink-0 mr-3">
                          {item.image ? (
                            <Image
                              src={item.image}
                              alt={item.name}
                              fill
                              style={{ objectFit: 'contain' }}
                              className="rounded-md"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
                              <span className="text-gray-400 text-xs">No image</span>
                            </div>
                          )}
                        </div>

                        {/* Product Info */}
                        <div className="flex-grow">
                          <h3 className="text-sm font-medium text-gray-800 dark:text-white mb-1 line-clamp-1">
                            {item.name}
                          </h3>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                            Réf: {item.reference}
                          </p>

                          {/* Quantity Controls */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <button
                                onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                                className="p-1 bg-gray-100 dark:bg-gray-700 rounded-l-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                aria-label="Diminuer la quantité"
                              >
                                <FaMinus className="text-gray-600 dark:text-gray-300 text-xs" />
                              </button>
                              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-xs font-medium">
                                {item.quantity}
                              </span>
                              <button
                                onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                                className="p-1 bg-gray-100 dark:bg-gray-700 rounded-r-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                aria-label="Augmenter la quantité"
                              >
                                <FaPlus className="text-gray-600 dark:text-gray-300 text-xs" />
                              </button>
                            </div>

                            <button
                              onClick={() => removeItem(item.id)}
                              className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                              aria-label="Supprimer l'article"
                            >
                              <FaTrash className="text-xs" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Notes */}
                  <div className="mb-4">
                    <label htmlFor="cart-notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Notes ou instructions spéciales
                    </label>
                    <textarea
                      id="cart-notes"
                      rows={3}
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Ajoutez des notes ou des instructions spéciales pour votre devis..."
                    ></textarea>
                  </div>

                  {/* Actions */}
                  <div className="space-y-3">
                    <button
                      onClick={handleRequestQuote}
                      className="w-full py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center"
                    >
                      <FaFileAlt className="mr-2" />
                      Demander un devis
                    </button>
                    
                    <Link href="/cart" className="block w-full">
                      <button
                        onClick={onClose}
                        className="w-full py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      >
                        Voir le panier complet
                      </button>
                    </Link>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
