"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/components/layout/ModernFooter.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ModernFooter.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaEnvelope,FaFacebook,FaHeart,FaInstagram,FaLinkedin,FaMapMarkerAlt,FaPhone,FaTwitter!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ModernFooter() {\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    const footerLinks = {\n        company: [\n            {\n                name: 'À Propos',\n                href: '/about'\n            },\n            {\n                name: 'Notre Histoire',\n                href: '/about#history'\n            },\n            {\n                name: 'Équipe',\n                href: '/about#team'\n            },\n            {\n                name: 'Carrières',\n                href: '/careers'\n            },\n            {\n                name: 'Actualités',\n                href: '/news'\n            }\n        ],\n        products: [\n            {\n                name: 'Éclairage LED',\n                href: '/products?category=eclairage'\n            },\n            {\n                name: 'Prises & Interrupteurs',\n                href: '/products?category=prises'\n            },\n            {\n                name: 'Protection Électrique',\n                href: '/products?category=protection'\n            },\n            {\n                name: 'Câbles',\n                href: '/products?category=cables'\n            },\n            {\n                name: 'Domotique',\n                href: '/products?category=domotique'\n            }\n        ],\n        services: [\n            {\n                name: 'Installation',\n                href: '/services/installation'\n            },\n            {\n                name: 'Maintenance',\n                href: '/services/maintenance'\n            },\n            {\n                name: 'Conseil Technique',\n                href: '/services/consulting'\n            },\n            {\n                name: 'Formation',\n                href: '/services/training'\n            },\n            {\n                name: 'Support 24/7',\n                href: '/support'\n            }\n        ],\n        support: [\n            {\n                name: 'Centre d\\'Aide',\n                href: '/help'\n            },\n            {\n                name: 'Contact',\n                href: '/contact'\n            },\n            {\n                name: 'Garanties',\n                href: '/warranty'\n            },\n            {\n                name: 'Retours',\n                href: '/returns'\n            },\n            {\n                name: 'FAQ',\n                href: '/faq'\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: 'Facebook',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaFacebook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 55,\n                columnNumber: 31\n            }, this),\n            href: 'https://facebook.com/moonelec',\n            color: 'hover:text-blue-600'\n        },\n        {\n            name: 'Twitter',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTwitter, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 56,\n                columnNumber: 30\n            }, this),\n            href: 'https://twitter.com/moonelec',\n            color: 'hover:text-blue-400'\n        },\n        {\n            name: 'Instagram',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaInstagram, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 57,\n                columnNumber: 32\n            }, this),\n            href: 'https://instagram.com/moonelec',\n            color: 'hover:text-pink-600'\n        },\n        {\n            name: 'LinkedIn',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLinkedin, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 58,\n                columnNumber: 31\n            }, this),\n            href: 'https://linkedin.com/company/moonelec',\n            color: 'hover:text-blue-700'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-charcoal text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-14 h-14\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"/images/logo/logo-moonelec.png\",\n                                                        alt: \"Moonelec Logo\",\n                                                        fill: true,\n                                                        className: \"object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold font-heading\",\n                                                    children: \"Moonelec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 leading-relaxed mb-6\",\n                                            children: \"Votre partenaire de confiance pour tous vos besoins \\xe9lectriques au Maroc. Nous offrons des solutions innovantes et de qualit\\xe9 sup\\xe9rieure depuis plus de 15 ans.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaPhone, {\n                                                            className: \"text-moonelec-red\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"+212 5 22 XX XX XX\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaEnvelope, {\n                                                            className: \"text-moonelec-red\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaMapMarkerAlt, {\n                                                            className: \"text-moonelec-red\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"Casablanca, Maroc\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Entreprise\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.company.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-moonelec-red transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Produits\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.products.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-electric-blue transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.services.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-electric-blue transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.support.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-moonelec-red transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.5\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-12 pt-8 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"Restez Inform\\xe9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6\",\n                                    children: \"Abonnez-vous \\xe0 notre newsletter pour recevoir les derni\\xe8res actualit\\xe9s et offres sp\\xe9ciales.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Votre adresse email\",\n                                            className: \"flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-moonelec-red focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn btn-primary px-6 py-3 whitespace-nowrap\",\n                                            children: \"S'abonner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        \"\\xa9 2024 Moonelec. Tous droits r\\xe9serv\\xe9s. Fait avec\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHeart, {\n                                            className: \"text-moonelec-red mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"au Maroc.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                        href: social.href,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        whileHover: {\n                                            scale: 1.2\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        className: \"text-gray-400 \".concat(social.color, \" transition-colors duration-300 text-xl\"),\n                                        \"aria-label\": social.name,\n                                        children: social.icon\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Confidentialit\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Cookies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                onClick: scrollToTop,\n                className: \"fixed bottom-8 right-8 p-3 bg-moonelec-red hover:bg-moonelec-red-dark text-white rounded-full shadow-lg transition-all duration-300 z-50\",\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                initial: {\n                    opacity: 0,\n                    y: 100\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowUp, {\n                    className: \"text-lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = ModernFooter;\nvar _c;\n$RefreshReg$(_c, \"ModernFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/ModernFooter.tsx\n"));

/***/ })

});