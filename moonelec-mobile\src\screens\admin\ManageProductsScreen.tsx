import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  RefreshControl,
  Image,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Product, Category } from '../../types';
import ApiService from '../../services/api';
import WebApiService from '../../services/webApi';
import { useAuth } from '../../contexts/AuthContext';
import LoadingAnimation from '../../components/LoadingAnimation';
import ImagePickerComponent from '../../components/ImagePicker';

const ManageProductsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Form state
  const [newProduct, setNewProduct] = useState({
    reference: '',
    name: '',
    description: '',
    categoryId: '',
    mainImage: '',
    characteristics: {} as Record<string, string>,
  });

  useEffect(() => {
    if (user?.role !== 'ADMIN') {
      Alert.alert('Accès refusé', 'Cette fonctionnalité est réservée aux administrateurs');
      navigation.goBack();
      return;
    }
    loadData();
  }, [user]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      console.log('📦 Loading products and categories with WebAPI...');

      // Try WebAPI first
      try {
        const [productsData, categoriesData] = await Promise.all([
          WebApiService.getProducts(),
          WebApiService.getCategories(),
        ]);

        setProducts(Array.isArray(productsData.products) ? productsData.products : []);
        setCategories(Array.isArray(categoriesData) ? categoriesData : []);
        console.log('✅ Data loaded with WebAPI - Products:', productsData.products?.length, 'Categories:', categoriesData.length);
      } catch (webApiError) {
        console.log('⚠️ WebAPI failed, falling back to original API:', webApiError);

        const [productsData, categoriesData] = await Promise.all([
          ApiService.getProducts(),
          ApiService.getCategories(),
        ]);

        setProducts(Array.isArray(productsData.products) ? productsData.products : []);
        setCategories(Array.isArray(categoriesData) ? categoriesData : []);
        console.log('✅ Data loaded with fallback API');
      }
    } catch (error) {
      console.error('❌ Error loading data:', error);
      Alert.alert('Erreur', 'Impossible de charger les données');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const createProduct = async () => {
    if (!newProduct.reference.trim() || !newProduct.name.trim()) {
      Alert.alert('Erreur', 'La référence et le nom sont requis');
      return;
    }

    setIsCreating(true);
    try {
      console.log('📦 Creating product with WebAPI:', newProduct.name);

      const createdProduct = await WebApiService.createProduct({
        reference: newProduct.reference.trim(),
        name: newProduct.name.trim(),
        description: newProduct.description.trim() || undefined,
        categoryId: newProduct.categoryId || undefined,
        mainImage: newProduct.mainImage || undefined,
        characteristics: newProduct.characteristics,
      });

      setProducts(prev => [createdProduct, ...prev]);
      setShowCreateModal(false);
      resetForm();
      Alert.alert('Succès', 'Produit créé avec succès');
      console.log('✅ Product created successfully');
    } catch (error) {
      console.error('❌ Error creating product:', error);
      Alert.alert('Erreur', error instanceof Error ? error.message : 'Erreur lors de la création');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setNewProduct({
      reference: '',
      name: '',
      description: '',
      categoryId: '',
      mainImage: '',
      characteristics: {},
    });
  };

  const deleteProduct = async (productId: string, productName: string) => {
    Alert.alert(
      'Confirmer la suppression',
      `Êtes-vous sûr de vouloir supprimer le produit "${productName}" ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('📦 Deleting product with WebAPI:', productId);
              await WebApiService.deleteProduct(productId);

              setProducts(prev => prev.filter(prod => prod.id !== productId));
              Alert.alert('Succès', 'Produit supprimé avec succès');
              console.log('✅ Product deleted successfully');
            } catch (error) {
              console.error('❌ Error deleting product:', error);
              Alert.alert('Erreur', 'Erreur lors de la suppression');
            }
          },
        },
      ]
    );
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.reference.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderProduct = ({ item }: { item: Product }) => {
    const category = categories.find(cat => cat.id === item.categoryId);

    return (
      <View style={styles.productCard}>
        <View style={styles.productHeader}>
          {item.mainImage ? (
            <Image source={{ uri: item.mainImage }} style={styles.productImage} />
          ) : (
            <View style={styles.placeholderImage}>
              <Ionicons name="image-outline" size={24} color="#ccc" />
            </View>
          )}

          <View style={styles.productInfo}>
            <Text style={styles.productName}>{item.name}</Text>
            <Text style={styles.productReference}>Réf: {item.reference}</Text>
            {category && (
              <Text style={styles.productCategory}>📁 {category.name}</Text>
            )}
          </View>

          <View style={styles.productActions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.editButton]}
              onPress={() => {
                Alert.alert('Info', 'Fonctionnalité d\'édition à venir');
              }}
            >
              <Ionicons name="pencil" size={16} color="#fff" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={() => deleteProduct(item.id, item.name)}
            >
              <Ionicons name="trash" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>

        {item.description && (
          <Text style={styles.productDescription} numberOfLines={2}>
            {item.description}
          </Text>
        )}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="cube-outline" size={64} color="#ccc" />
      <Text style={styles.emptyText}>Aucun produit</Text>
      <Text style={styles.emptySubtext}>Créez votre premier produit</Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingAnimation size={60} color="#006db7" />
        <Text style={styles.loadingText}>Chargement des produits...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>📦 Gestion des Produits</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateModal(true)}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher un produit..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Liste des produits */}
      <FlatList
        data={filteredProducts}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />

      {/* Modal de création */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Nouveau Produit</Text>
            <TouchableOpacity
              onPress={() => {
                setShowCreateModal(false);
                resetForm();
              }}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Référence *</Text>
              <TextInput
                style={styles.textInput}
                value={newProduct.reference}
                onChangeText={(text) => setNewProduct(prev => ({ ...prev, reference: text }))}
                placeholder="Ex: LED-001, PRISE-12V, etc."
                maxLength={50}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Nom du produit *</Text>
              <TextInput
                style={styles.textInput}
                value={newProduct.name}
                onChangeText={(text) => setNewProduct(prev => ({ ...prev, name: text }))}
                placeholder="Ex: Ampoule LED 12W, Prise 16A, etc."
                maxLength={100}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Image du produit</Text>
              <ImagePickerComponent
                onImageSelected={(imageUri) => setNewProduct(prev => ({ ...prev, mainImage: imageUri }))}
                currentImage={newProduct.mainImage}
                placeholder="Ajouter une image"
                style={styles.imagePicker}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Catégorie</Text>
              <View style={styles.pickerContainer}>
                <Text style={styles.pickerPlaceholder}>
                  {categories.find(cat => cat.id === newProduct.categoryId)?.name || 'Sélectionner une catégorie'}
                </Text>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={newProduct.description}
                onChangeText={(text) => setNewProduct(prev => ({ ...prev, description: text }))}
                placeholder="Description détaillée du produit..."
                multiline
                numberOfLines={3}
                maxLength={500}
              />
            </View>

            <TouchableOpacity
              style={[styles.createButton, (!newProduct.reference.trim() || !newProduct.name.trim()) && styles.createButtonDisabled]}
              onPress={createProduct}
              disabled={!newProduct.reference.trim() || !newProduct.name.trim() || isCreating}
            >
              {isCreating ? (
                <LoadingAnimation size={20} color="#fff" />
              ) : (
                <Text style={styles.createButtonText}>Créer le produit</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#006db7',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  productCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  productReference: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  productCategory: {
    fontSize: 12,
    color: '#006db7',
  },
  productActions: {
    flexDirection: 'row',
    marginLeft: 12,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  editButton: {
    backgroundColor: '#006db7',
  },
  deleteButton: {
    backgroundColor: '#ed1c24',
  },
  productDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    lineHeight: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  pickerPlaceholder: {
    fontSize: 16,
    color: '#666',
  },
  createButton: {
    backgroundColor: '#006db7',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  imagePicker: {
    alignSelf: 'flex-start',
  },
});

export default ManageProductsScreen;
