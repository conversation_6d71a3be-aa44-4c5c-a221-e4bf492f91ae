/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/clients/route";
exports.ids = ["app/api/admin/clients/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_admin_clients_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/clients/route.ts */ \"(rsc)/./src/app/api/admin/clients/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/clients/route\",\n        pathname: \"/api/admin/clients\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/clients/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\admin\\\\clients\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_admin_clients_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/clients/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/admin/clients/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./src/lib/auth-options.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n\n\n\n// GET /api/admin/clients - Get all clients with filtering and pagination\nasync function GET(req) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session?.user || session.user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const searchParams = req.nextUrl.searchParams;\n        const skip = parseInt(searchParams.get('skip') || '0');\n        const take = parseInt(searchParams.get('take') || '10');\n        const search = searchParams.get('search') || '';\n        const city = searchParams.get('city') || '';\n        const company = searchParams.get('company') || '';\n        // Build where clause\n        const where = {\n            user: {\n                role: 'CLIENT'\n            }\n        };\n        // Add search filter\n        if (search) {\n            where.OR = [\n                {\n                    user: {\n                        firstname: {\n                            contains: search,\n                            mode: 'insensitive'\n                        }\n                    }\n                },\n                {\n                    user: {\n                        lastname: {\n                            contains: search,\n                            mode: 'insensitive'\n                        }\n                    }\n                },\n                {\n                    user: {\n                        email: {\n                            contains: search,\n                            mode: 'insensitive'\n                        }\n                    }\n                },\n                {\n                    company: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        // Add city filter\n        if (city) {\n            where.city = {\n                contains: city,\n                mode: 'insensitive'\n            };\n        }\n        // Add company filter\n        if (company) {\n            where.company = {\n                contains: company,\n                mode: 'insensitive'\n            };\n        }\n        // Get clients with pagination\n        const [clients, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findMany({\n                where,\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            firstname: true,\n                            lastname: true,\n                            email: true,\n                            telephone: true,\n                            createdAt: true\n                        }\n                    }\n                },\n                skip,\n                take,\n                orderBy: {\n                    user: {\n                        createdAt: 'desc'\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.count({\n                where\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            clients,\n            total,\n            page: Math.floor(skip / take) + 1,\n            totalPages: Math.ceil(total / take)\n        });\n    } catch (error) {\n        console.error('Error fetching clients:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/admin/clients - Create a new client\nasync function POST(req) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session?.user || session.user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await req.json();\n        const { firstname, lastname, email, telephone, password, company, address, city, postalCode, country } = body;\n        // Validate required fields\n        if (!firstname || !lastname || !email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Les champs prénom, nom, email et mot de passe sont requis'\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already exists\n        const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                email\n            }\n        });\n        if (existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Un utilisateur avec cet email existe déjà'\n            }, {\n                status: 400\n            });\n        }\n        // Hash password\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].hash(password, 12);\n        // Create user and client in a transaction\n        const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n            // Create user\n            const user = await tx.user.create({\n                data: {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    firstname,\n                    lastname,\n                    email,\n                    telephone: telephone || null,\n                    password: hashedPassword,\n                    role: 'CLIENT'\n                }\n            });\n            // Create client profile\n            const client = await tx.client.create({\n                data: {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    userId: user.id,\n                    company: company || null,\n                    address: address || null,\n                    city: city || null,\n                    postalCode: postalCode || null,\n                    country: country || 'France'\n                },\n                include: {\n                    user: {\n                        select: {\n                            id: true,\n                            firstname: true,\n                            lastname: true,\n                            email: true,\n                            telephone: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            return client;\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Client créé avec succès',\n            client: result\n        });\n    } catch (error) {\n        console.error('Error creating client:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Erreur lors de la création du client'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/clients/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconsole.log('🔧 Auth options module loaded');\nconsole.log('🔧 Creating auth options...');\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                username: {\n                    label: \"Nom d'utilisateur\",\n                    type: 'text'\n                },\n                password: {\n                    label: 'Mot de passe',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                console.log('🔍 AUTHORIZE FUNCTION CALLED!');\n                console.log('🔍 Auth attempt:', {\n                    username: credentials?.username,\n                    hasPassword: !!credentials?.password\n                });\n                if (!credentials?.username || !credentials?.password) {\n                    console.log('❌ Missing credentials');\n                    return null;\n                }\n                try {\n                    // Rechercher l'utilisateur par nom d'utilisateur\n                    console.log('🔍 Looking for user:', credentials.username);\n                    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.findUserByUsername)(credentials.username);\n                    // Vérifier si l'utilisateur existe\n                    if (!user) {\n                        console.log('❌ User not found:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ User found:', {\n                        id: user.id,\n                        username: user.username,\n                        role: user.role\n                    });\n                    // Vérifier le mot de passe\n                    console.log('🔍 Verifying password...');\n                    const isPasswordValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log('❌ Invalid password for user:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ Password valid for user:', credentials.username);\n                    // Retourner les données de l'utilisateur sans le mot de passe\n                    const userObject = {\n                        id: user.id,\n                        email: user.email,\n                        username: user.username,\n                        name: `${user.firstname} ${user.lastname}`,\n                        firstname: user.firstname,\n                        lastname: user.lastname,\n                        role: user.role,\n                        clientId: user.client?.id,\n                        commercialId: user.commercial?.id,\n                        adminId: user.admin?.id\n                    };\n                    console.log('✅ Returning user object:', userObject);\n                    return userObject;\n                } catch (error) {\n                    console.error('❌ Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.username = user.username;\n                token.role = user.role;\n                token.firstname = user.firstname;\n                token.lastname = user.lastname;\n                token.clientId = user.clientId;\n                token.commercialId = user.commercialId;\n                token.adminId = user.adminId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.username = token.username;\n                session.user.role = token.role;\n                session.user.firstname = token.firstname;\n                session.user.lastname = token.lastname;\n                session.user.clientId = token.clientId;\n                session.user.commercialId = token.commercialId;\n                session.user.adminId = token.adminId;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signOut: '/auth/signout',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   createClientUser: () => (/* binding */ createClientUser),\n/* harmony export */   createCommercialUser: () => (/* binding */ createCommercialUser),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Fonction pour hacher un mot de passe\nasync function hashPassword(password) {\n    const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].genSalt(10);\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, salt);\n}\n// Fonction pour vérifier un mot de passe\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Fonction pour créer un utilisateur client\nasync function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.CLIENT\n            }\n        });\n        // Créer le profil client associé\n        const client = await tx.client.create({\n            data: {\n                userId: user.id,\n                company_name\n            }\n        });\n        return {\n            user,\n            client\n        };\n    });\n}\n// Fonction pour créer un utilisateur commercial\nasync function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.COMMERCIAL\n            }\n        });\n        // Créer le profil commercial associé\n        const commercial = await tx.commercial.create({\n            data: {\n                userId: user.id,\n                profile_photo\n            }\n        });\n        return {\n            user,\n            commercial\n        };\n    });\n}\n// Fonction pour créer un utilisateur administrateur\nasync function createAdminUser({ email, username, password, lastname, firstname, telephone }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN\n            }\n        });\n        // Créer le profil admin associé\n        const admin = await tx.admin.create({\n            data: {\n                userId: user.id\n            }\n        });\n        return {\n            user,\n            admin\n        };\n    });\n}\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nasync function findUserByUsername(username) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            username\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n// Fonction pour trouver un utilisateur par son email\nasync function findUserByEmail(email) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            email\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/uuid","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fclients%2Froute&page=%2Fapi%2Fadmin%2Fclients%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclients%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();