import { PrismaClient } from '@prisma/client';

// Déclaration pour éviter les erreurs TypeScript
declare global {
  var prisma: PrismaClient | undefined;
}

// Création d'un client Prisma singleton
export const prisma = global.prisma || new PrismaClient();

// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma;
}
