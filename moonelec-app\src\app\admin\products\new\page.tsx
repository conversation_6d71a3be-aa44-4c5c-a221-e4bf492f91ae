'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaSave, FaPlus, FaTrash, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';
import ImageUpload from '@/components/ui/ImageUpload';
import MultiImageUpload from '@/components/ui/MultiImageUpload';
import PdfExtractor from '@/components/admin/PdfExtractor';

interface Category {
  id: string;
  name: string;
}

export default function NewProductPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<{ id: string; name: string }[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showPdfExtractor, setShowPdfExtractor] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    reference: '',
    name: '',
    description: '',
    mainImage: '',
    categoryId: '',
    brandId: '',
  });

  const [characteristics, setCharacteristics] = useState<{ key: string; value: string }[]>([
    { key: 'Modèle', value: '' },
    { key: 'Puissance', value: '' },
    { key: 'Durée de vie', value: '' },
    { key: 'Lumens', value: '' }
  ]);

  const [productImages, setProductImages] = useState<{ url: string; alt?: string; order?: number }[]>([]);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);

        // Fetch brands
        const brandsResponse = await fetch('/api/brands');
        if (!brandsResponse.ok) {
          throw new Error('Failed to fetch brands');
        }
        const brandsData = await brandsResponse.json();
        setBrands(brandsData.brands);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'An error occurred while fetching data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCharacteristicChange = (index: number, field: 'key' | 'value', value: string) => {
    setCharacteristics(prev => {
      const newCharacteristics = [...prev];
      newCharacteristics[index][field] = value;
      return newCharacteristics;
    });
  };

  const addCharacteristic = () => {
    setCharacteristics(prev => [...prev, { key: '', value: '' }]);
  };

  const removeCharacteristic = (index: number) => {
    setCharacteristics(prev => prev.filter((_, i) => i !== index));
  };

  const handleMainImageChange = (imageUrl: string | null) => {
    setFormData(prev => ({ ...prev, mainImage: imageUrl || '' }));
  };

  const handleProductImagesChange = (images: { url: string; alt?: string; order?: number }[]) => {
    setProductImages(images);
  };

  // Fonction pour traiter les données extraites du PDF
  const handlePdfDataExtracted = (extractedData: any) => {
    // Pré-remplir le formulaire avec les données extraites
    setFormData(prev => ({
      ...prev,
      reference: Array.isArray(extractedData.reference)
        ? extractedData.reference[0]
        : extractedData.reference || prev.reference,
      name: extractedData.productName || prev.name,
      description: extractedData.description || prev.description,
    }));

    // Convertir les caractéristiques extraites
    if (extractedData.characteristics && Object.keys(extractedData.characteristics).length > 0) {
      const newCharacteristics = Object.entries(extractedData.characteristics).map(([key, value]) => ({
        key,
        value: String(value)
      }));
      setCharacteristics(newCharacteristics);
    }

    // Essayer de trouver la catégorie correspondante
    if (extractedData.category) {
      const matchingCategory = categories.find(cat =>
        cat.name.toLowerCase().includes(extractedData.category.toLowerCase()) ||
        extractedData.category.toLowerCase().includes(cat.name.toLowerCase())
      );
      if (matchingCategory) {
        setFormData(prev => ({ ...prev, categoryId: matchingCategory.id }));
      }
    }

    // Essayer de trouver la marque correspondante
    if (extractedData.brand) {
      const matchingBrand = brands.find(brand =>
        brand.name.toLowerCase().includes(extractedData.brand.toLowerCase()) ||
        extractedData.brand.toLowerCase().includes(brand.name.toLowerCase())
      );
      if (matchingBrand) {
        setFormData(prev => ({ ...prev, brandId: matchingBrand.id }));
      }
    }

    // Fermer l'extracteur
    setShowPdfExtractor(false);

    // Afficher un message de succès
    setError(null);
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.reference.trim()) {
      errors.reference = 'La référence est requise';
    }

    if (!formData.name.trim()) {
      errors.name = 'Le nom est requis';
    }

    if (!formData.description.trim()) {
      errors.description = 'La description est requise';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Convert characteristics array to object
      const characteristicsObject = characteristics.reduce((obj, { key, value }) => {
        if (key.trim()) {
          obj[key.trim()] = value.trim();
        }
        return obj;
      }, {} as Record<string, string>);

      // Create the product
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reference: formData.reference,
          name: formData.name,
          description: formData.description,
          characteristics: characteristicsObject,
          mainImage: formData.mainImage || null,
          categoryId: formData.categoryId || null,
          brandId: formData.brandId || null,
          images: productImages,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      const product = await response.json();

      // Redirect to the product detail page
      router.push(`/admin/products/${product.id}`);
    } catch (err: any) {
      console.error('Error creating product:', err);
      setError(err.message || 'Une erreur est survenue lors de la création du produit');
      setIsSubmitting(false);
    }
  };

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Ajouter un nouveau produit
          </h1>
          <div className="flex space-x-3">
            <motion.button
              onClick={() => setShowPdfExtractor(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
            >
              🤖 Extraire depuis PDF
            </motion.button>
            <Link href="/admin/products">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Retour à la liste
              </motion.button>
            </Link>
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <FaSpinner className="animate-spin text-4xl text-primary" />
          </div>
        ) : (
          <>
            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center">
                <FaExclamationTriangle className="mr-2" />
                <span>{error}</span>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* Reference */}
                <div>
                  <label htmlFor="reference" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Référence <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="reference"
                    name="reference"
                    value={formData.reference}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      formErrors.reference ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {formErrors.reference && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.reference}</p>
                  )}
                </div>

                {/* Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nom <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      formErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>
                  )}
                </div>

                {/* Category */}
                <div>
                  <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Catégorie
                  </label>
                  <select
                    id="categoryId"
                    name="categoryId"
                    value={formData.categoryId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Sélectionner une catégorie</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Brand */}
                <div>
                  <label htmlFor="brandId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Marque
                  </label>
                  <select
                    id="brandId"
                    name="brandId"
                    value={formData.brandId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Sélectionner une marque</option>
                    {brands.map((brand) => (
                      <option key={brand.id} value={brand.id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Main Image */}
                <div>
                  <ImageUpload
                    initialImage={formData.mainImage}
                    onImageChange={handleMainImageChange}
                    directory="products"
                    label="Image principale"
                    aspectRatio="square"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={5}
                  className={`w-full px-4 py-2 border ${
                    formErrors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                ></textarea>
                {formErrors.description && (
                  <p className="mt-1 text-sm text-red-500">{formErrors.description}</p>
                )}
              </div>

              {/* Characteristics */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Caractéristiques
                  </label>
                  <motion.button
                    type="button"
                    onClick={addCharacteristic}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-1 bg-primary text-white rounded-full"
                  >
                    <FaPlus />
                  </motion.button>
                </div>

                {characteristics.map((characteristic, index) => (
                  <div key={index} className="flex items-center gap-2 mb-2">
                    <input
                      type="text"
                      value={characteristic.key}
                      onChange={(e) => handleCharacteristicChange(index, 'key', e.target.value)}
                      placeholder="Nom"
                      className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    <input
                      type="text"
                      value={characteristic.value}
                      onChange={(e) => handleCharacteristicChange(index, 'value', e.target.value)}
                      placeholder="Valeur"
                      className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    <motion.button
                      type="button"
                      onClick={() => removeCharacteristic(index)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-2 text-red-500 hover:text-red-700 dark:hover:text-red-300"
                    >
                      <FaTrash />
                    </motion.button>
                  </div>
                ))}
              </div>

              {/* Product Images */}
              <div className="mb-6">
                <MultiImageUpload
                  initialImages={productImages}
                  onImagesChange={handleProductImagesChange}
                  directory="products"
                  label="Images du produit"
                  maxImages={10}
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className={`px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center ${
                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isSubmitting ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2" />
                      Enregistrer le produit
                    </>
                  )}
                </motion.button>
              </div>
            </form>
          </>
        )}

        {/* PDF Extractor Modal */}
        {showPdfExtractor && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <PdfExtractor
                onDataExtracted={handlePdfDataExtracted}
                onClose={() => setShowPdfExtractor(false)}
              />
            </div>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
