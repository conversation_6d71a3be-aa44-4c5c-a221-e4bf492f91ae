'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, Fa<PERSON><PERSON>ch, <PERSON>a<PERSON><PERSON><PERSON>, FaEx<PERSON><PERSON><PERSON><PERSON>, FaUser<PERSON>ie, FaPhone, FaEnvelope, <PERSON>a<PERSON>ser } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Image from 'next/image';
import Link from 'next/link';
import ImageUpload from '@/components/ui/ImageUpload';

interface Commercial {
  id: string;
  user: {
    id: string;
    email: string;
    username: string;
    firstname: string;
    lastname: string;
    telephone?: string;
    role: string;
  };
  profile_photo?: string;
  commercialclient: any[];
}

export default function AdminCommercialsPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [commercials, setCommercials] = useState<Commercial[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Form state
  const [showForm, setShowForm] = useState(false);
  const [editingCommercial, setEditingCommercial] = useState<Commercial | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    firstname: '',
    lastname: '',
    telephone: '',
    profile_photo: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchCommercials();
  }, []);

  const fetchCommercials = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/commercials');

      if (!response.ok) {
        throw new Error('Failed to fetch commercials');
      }

      const data = await response.json();
      setCommercials(data.commercials);
    } catch (err: any) {
      console.error('Error fetching commercials:', err);
      setError(err.message || 'An error occurred while fetching commercials');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleImageChange = (imageUrl: string | null) => {
    setFormData(prev => ({ ...prev, profile_photo: imageUrl || '' }));
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email.trim()) {
      errors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'L\'email n\'est pas valide';
    }

    if (!editingCommercial && !formData.username.trim()) {
      errors.username = 'Le nom d\'utilisateur est requis';
    }

    if (!editingCommercial && !formData.password.trim()) {
      errors.password = 'Le mot de passe est requis';
    } else if (!editingCommercial && formData.password.length < 6) {
      errors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    if (!formData.firstname.trim()) {
      errors.firstname = 'Le prénom est requis';
    }

    if (!formData.lastname.trim()) {
      errors.lastname = 'Le nom est requis';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccessMessage(null);

    try {
      if (editingCommercial) {
        // Update existing commercial
        const response = await fetch(`/api/commercials/${editingCommercial.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: formData.email,
            firstname: formData.firstname,
            lastname: formData.lastname,
            telephone: formData.telephone || undefined,
            profile_photo: formData.profile_photo || undefined,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update commercial');
        }
      } else {
        // Create new commercial
        const response = await fetch('/api/commercials', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: formData.email,
            username: formData.username,
            password: formData.password,
            firstname: formData.firstname,
            lastname: formData.lastname,
            telephone: formData.telephone || undefined,
            profile_photo: formData.profile_photo || undefined,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create commercial');
        }
      }

      // Refresh the commercial list
      await fetchCommercials();

      // Reset form
      setFormData({
        email: '',
        username: '',
        password: '',
        firstname: '',
        lastname: '',
        telephone: '',
        profile_photo: '',
      });

      setShowForm(false);
      setEditingCommercial(null);
      setSuccessMessage(`Commercial ${editingCommercial ? 'modifié' : 'créé'} avec succès`);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err: any) {
      console.error(`Error ${editingCommercial ? 'updating' : 'creating'} commercial:`, err);
      setError(err.message || `Une erreur est survenue lors de la ${editingCommercial ? 'modification' : 'création'} du commercial`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (commercial: Commercial) => {
    setEditingCommercial(commercial);
    setFormData({
      email: commercial.user.email,
      username: commercial.user.username,
      password: '', // We don't show or edit the password
      firstname: commercial.user.firstname,
      lastname: commercial.user.lastname,
      telephone: commercial.user.telephone || '',
      profile_photo: commercial.profile_photo || '',
    });
    setShowForm(true);
  };

  const handleDelete = async (commercialId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce commercial ? Cette action est irréversible.')) {
      return;
    }

    try {
      setIsDeleting(commercialId);
      setError(null);

      const response = await fetch(`/api/commercials/${commercialId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete commercial');
      }

      // Refresh the commercial list
      await fetchCommercials();

      setSuccessMessage('Commercial supprimé avec succès');

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error deleting commercial:', err);
      setError(err.message || 'Une erreur est survenue lors de la suppression du commercial');
    } finally {
      setIsDeleting(null);
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingCommercial(null);
    setFormData({
      email: '',
      username: '',
      password: '',
      firstname: '',
      lastname: '',
      telephone: '',
      profile_photo: '',
    });
    setFormErrors({});
  };

  const filteredCommercials = commercials.filter(commercial => {
    const searchTermLower = searchTerm.toLowerCase();
    return (
      commercial.user.firstname.toLowerCase().includes(searchTermLower) ||
      commercial.user.lastname.toLowerCase().includes(searchTermLower) ||
      commercial.user.email.toLowerCase().includes(searchTermLower) ||
      commercial.user.username.toLowerCase().includes(searchTermLower)
    );
  });

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            Gestion des Commerciaux
          </h1>
          {!showForm && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowForm(true)}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              <FaPlus />
              <span>Ajouter un commercial</span>
            </motion.button>
          )}
        </div>

        {/* Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center">
            <FaExclamationTriangle className="mr-2" />
            <span>{error}</span>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md">
            {successMessage}
          </div>
        )}

        {/* Commercial Form */}
        {showForm && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
              {editingCommercial ? 'Modifier le commercial' : 'Ajouter un commercial'}
            </h2>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="firstname" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Prénom <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="firstname"
                    name="firstname"
                    value={formData.firstname}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      formErrors.firstname ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {formErrors.firstname && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.firstname}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="lastname" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nom <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="lastname"
                    name="lastname"
                    value={formData.lastname}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      formErrors.lastname ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {formErrors.lastname && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.lastname}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      formErrors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {formErrors.email && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.email}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="telephone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Téléphone
                  </label>
                  <input
                    type="tel"
                    id="telephone"
                    name="telephone"
                    value={formData.telephone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>

                {!editingCommercial && (
                  <>
                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Nom d'utilisateur <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 border ${
                          formErrors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                      />
                      {formErrors.username && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.username}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Mot de passe <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 border ${
                          formErrors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                      />
                      {formErrors.password && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.password}</p>
                      )}
                    </div>
                  </>
                )}

                <div className={!editingCommercial ? 'md:col-span-2' : ''}>
                  <ImageUpload
                    initialImage={formData.profile_photo}
                    onImageChange={handleImageChange}
                    directory="commercials"
                    label="Photo de profil"
                    aspectRatio="square"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <motion.button
                  type="button"
                  onClick={handleCancel}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  Annuler
                </motion.button>

                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center ${
                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      {editingCommercial ? 'Modification...' : 'Création...'}
                    </>
                  ) : (
                    <>{editingCommercial ? 'Modifier' : 'Créer'}</>
                  )}
                </motion.button>
              </div>
            </form>
          </div>
        )}

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Rechercher un commercial..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* Commercials List */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          {isLoading && commercials.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <FaSpinner className="animate-spin text-4xl text-primary" />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Commercial
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Clients
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredCommercials.length > 0 ? (
                    filteredCommercials.map((commercial) => (
                      <tr key={commercial.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 relative rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
                              {commercial.profile_photo ? (
                                <Image
                                  src={commercial.profile_photo}
                                  alt={`${commercial.user.firstname} ${commercial.user.lastname}`}
                                  fill
                                  style={{ objectFit: 'cover' }}
                                />
                              ) : (
                                <div className="h-full w-full flex items-center justify-center text-gray-400">
                                  <FaUserTie size={20} />
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {commercial.user.firstname} {commercial.user.lastname}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                @{commercial.user.username}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white flex items-center">
                            <FaEnvelope className="mr-2 text-gray-400" />
                            {commercial.user.email}
                          </div>
                          {commercial.user.telephone && (
                            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                              <FaPhone className="mr-2 text-gray-400" />
                              {commercial.user.telephone}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {commercial.commercialclient.length}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEdit(commercial)}
                              className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                            >
                              <FaEdit />
                            </button>
                            <button
                              onClick={() => handleDelete(commercial.id)}
                              disabled={isDeleting === commercial.id}
                              className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 ${
                                isDeleting === commercial.id ? 'opacity-50 cursor-not-allowed' : ''
                              }`}
                            >
                              {isDeleting === commercial.id ? <FaSpinner className="animate-spin" /> : <FaTrash />}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {isLoading ? 'Chargement...' : 'Aucun commercial trouvé'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </RouteGuard>
  );
}
