import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  try {
    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
    });

    if (existingUser) {
      console.log('L\'utilisateur existe déjà.');
      return;
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash('123456', 10);

    // Créer l'utilisateur
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'hicham.ezzamzami',
        password: hashedPassword,
        firstname: 'Hicham',
        lastname: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        telephone: null,
        role: 'ADMIN',
        admin: {
          create: {}, // Créer un enregistrement Admin lié
        },
      },
    });

    console.log('Administrateur créé avec succès:', user);
  } catch (error) {
    console.error('Erreur lors de la création de l\'administrateur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
