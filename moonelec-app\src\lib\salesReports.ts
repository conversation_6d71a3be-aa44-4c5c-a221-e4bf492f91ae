import { prisma } from './prisma';
import { v4 as uuidv4 } from 'uuid';
import { saveFile } from './upload';

// Types
export interface SalesReportInput {
  need: string;
  articleRef?: string;
  comment?: string;
  visitDate: Date;
  denomination: string;
  images?: string[];
  name: string;
  visitPurpose: string;
  complaint?: string;
  city: string;
  videoUrl?: string;
  audioUrl?: string;
  pdfUrl?: string;
}

export interface SalesReportFilter {
  startDate?: Date;
  endDate?: Date;
  specificDates?: Date[];
  commercialId?: string;
  city?: string;
}

// Create a new sales report
export async function createSalesReport(
  commercialId: string,
  data: SalesReportInput
) {
  const id = uuidv4();

  // Convert images array to JSON string
  const imagesJson = data.images ? JSON.stringify(data.images) : null;

  return prisma.salesreport.create({
    data: {
      id,
      commercialId,
      need: data.need,
      articleRef: data.articleRef,
      comment: data.comment,
      visitDate: data.visitDate,
      denomination: data.denomination,
      images: imagesJson,
      name: data.name,
      visitPurpose: data.visitPurpose,
      complaint: data.complaint,
      city: data.city,
      videoUrl: data.videoUrl,
      audioUrl: data.audioUrl,
      pdfUrl: data.pdfUrl,
      isCompleted: true,
    },
    include: {
      commercial: {
        include: {
          user: true,
        },
      },
    },
  });
}

// Get a sales report by ID
export async function getSalesReportById(id: string) {
  const report = await prisma.salesreport.findUnique({
    where: { id },
    include: {
      commercial: {
        include: {
          user: true,
        },
      },
    },
  });

  if (report && report.images) {
    try {
      // Parse the JSON string back to an array
      const imagesArray = JSON.parse(report.images);
      return { ...report, imagesArray };
    } catch (error) {
      console.error('Error parsing images JSON:', error);
      return report;
    }
  }

  return report;
}

// Get all sales reports with optional filtering
export async function getSalesReports(
  filter?: SalesReportFilter,
  skip = 0,
  take = 10
) {
  const where: any = {};

  // Handle date filtering
  if (filter?.specificDates && filter.specificDates.length > 0) {
    // For specific dates, we need to match any of the selected dates
    const dateConditions = filter.specificDates.map(date => {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      return {
        visitDate: {
          gte: startOfDay,
          lte: endOfDay,
        },
      };
    });

    where.OR = dateConditions;
  } else if (filter?.startDate && filter?.endDate) {
    where.visitDate = {
      gte: filter.startDate,
      lte: filter.endDate,
    };
  } else if (filter?.startDate) {
    where.visitDate = {
      gte: filter.startDate,
    };
  } else if (filter?.endDate) {
    where.visitDate = {
      lte: filter.endDate,
    };
  }

  if (filter?.commercialId) {
    where.commercialId = filter.commercialId;
  }

  if (filter?.city) {
    where.city = {
      contains: filter.city,
    };
  }

  const [reports, total] = await Promise.all([
    prisma.salesreport.findMany({
      where,
      include: {
        commercial: {
          include: {
            user: {
              select: {
                firstname: true,
                lastname: true,
                email: true,
              },
            },
          },
        },
      },
      skip,
      take,
      orderBy: {
        submittedAt: 'desc',
      },
    }),
    prisma.salesreport.count({ where }),
  ]);

  // Process images for each report
  const processedReports = reports.map(report => {
    if (report.images) {
      try {
        const imagesArray = JSON.parse(report.images);
        return { ...report, imagesArray };
      } catch (error) {
        console.error('Error parsing images JSON:', error);
        return report;
      }
    }
    return report;
  });

  return { reports: processedReports, total };
}

// Update a sales report
export async function updateSalesReport(
  id: string,
  data: Partial<SalesReportInput>
) {
  // Convert images array to JSON string if provided
  const updateData: any = { ...data };

  if (data.images) {
    updateData.images = JSON.stringify(data.images);
  }

  return prisma.salesreport.update({
    where: { id },
    data: updateData,
  });
}

// Delete a sales report
export async function deleteSalesReport(id: string) {
  return prisma.salesreport.delete({
    where: { id },
  });
}

// Check for incomplete reports and send reminders
export async function checkIncompleteReports() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Get all commercials
  const commercials = await prisma.commercial.findMany({
    include: {
      user: true,
    },
  });

  const results = [];

  for (const commercial of commercials) {
    // Check if the commercial has submitted a report today
    const report = await prisma.salesreport.findFirst({
      where: {
        commercialId: commercial.id,
        submittedAt: {
          gte: today,
        },
        isCompleted: true,
      },
    });

    if (!report) {
      // Commercial hasn't submitted a report today
      // Check if we need to send a reminder (every 2 hours)
      const lastReminder = await prisma.salesreport.findFirst({
        where: {
          commercialId: commercial.id,
          lastReminder: {
            not: null,
          },
          isCompleted: false,
        },
        orderBy: {
          lastReminder: 'desc',
        },
      });

      const twoHoursAgo = new Date();
      twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);

      if (!lastReminder || (lastReminder.lastReminder && lastReminder.lastReminder < twoHoursAgo)) {
        // Create or update a reminder report
        const reportId = lastReminder ? lastReminder.id : uuidv4();

        const reminderReport = await prisma.salesreport.upsert({
          where: {
            id: reportId,
          },
          update: {
            lastReminder: new Date(),
          },
          create: {
            id: reportId,
            commercialId: commercial.id,
            need: '',
            visitDate: today,
            denomination: '',
            name: '',
            visitPurpose: '',
            city: '',
            lastReminder: new Date(),
            isCompleted: false,
          },
        });

        results.push({
          commercial,
          reminderReport,
          needsReminder: true,
        });
      } else {
        results.push({
          commercial,
          needsReminder: false,
        });
      }
    } else {
      results.push({
        commercial,
        report,
        needsReminder: false,
      });
    }
  }

  return results;
}

// Upload files for a sales report
export async function uploadSalesReportFiles(
  files: {
    images?: File[];
    video?: File;
    audio?: File;
    pdf?: File;
  }
) {
  const uploadResults: {
    imageUrls?: string[];
    videoUrl?: string;
    audioUrl?: string;
    pdfUrl?: string;
  } = {};

  // Upload images
  if (files.images && files.images.length > 0) {
    const imagePromises = files.images.map(image => saveFile(image, 'reports/images'));
    uploadResults.imageUrls = await Promise.all(imagePromises);
  }

  // Upload video
  if (files.video) {
    uploadResults.videoUrl = await saveFile(files.video, 'reports/videos');
  }

  // Upload audio
  if (files.audio) {
    uploadResults.audioUrl = await saveFile(files.audio, 'reports/audio');
  }

  // Upload PDF
  if (files.pdf) {
    uploadResults.pdfUrl = await saveFile(files.pdf, 'reports/pdfs');
  }

  return uploadResults;
}
