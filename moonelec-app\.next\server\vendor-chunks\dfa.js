"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dfa";
exports.ids = ["vendor-chunks/dfa"];
exports.modules = {

/***/ "(rsc)/./node_modules/dfa/index.js":
/*!***********************************!*\
  !*** ./node_modules/dfa/index.js ***!
  \***********************************/
/***/ ((module) => {

eval("\n\nvar INITIAL_STATE = 1;\nvar FAIL_STATE = 0;\n/**\n * A StateMachine represents a deterministic finite automaton.\n * It can perform matches over a sequence of values, similar to a regular expression.\n */\n\nclass StateMachine {\n  constructor(dfa) {\n    this.stateTable = dfa.stateTable;\n    this.accepting = dfa.accepting;\n    this.tags = dfa.tags;\n  }\n  /**\n   * Returns an iterable object that yields pattern matches over the input sequence.\n   * Matches are of the form [startIndex, endIndex, tags].\n   */\n\n\n  match(str) {\n    var self = this;\n    return {\n      *[Symbol.iterator]() {\n        var state = INITIAL_STATE;\n        var startRun = null;\n        var lastAccepting = null;\n        var lastState = null;\n\n        for (var p = 0; p < str.length; p++) {\n          var c = str[p];\n          lastState = state;\n          state = self.stateTable[state][c];\n\n          if (state === FAIL_STATE) {\n            // yield the last match if any\n            if (startRun != null && lastAccepting != null && lastAccepting >= startRun) {\n              yield [startRun, lastAccepting, self.tags[lastState]];\n            } // reset the state as if we started over from the initial state\n\n\n            state = self.stateTable[INITIAL_STATE][c];\n            startRun = null;\n          } // start a run if not in the failure state\n\n\n          if (state !== FAIL_STATE && startRun == null) {\n            startRun = p;\n          } // if accepting, mark the potential match end\n\n\n          if (self.accepting[state]) {\n            lastAccepting = p;\n          } // reset the state to the initial state if we get into the failure state\n\n\n          if (state === FAIL_STATE) {\n            state = INITIAL_STATE;\n          }\n        } // yield the last match if any\n\n\n        if (startRun != null && lastAccepting != null && lastAccepting >= startRun) {\n          yield [startRun, lastAccepting, self.tags[state]];\n        }\n      }\n\n    };\n  }\n  /**\n   * For each match over the input sequence, action functions matching\n   * the tag definitions in the input pattern are called with the startIndex,\n   * endIndex, and sub-match sequence.\n   */\n\n\n  apply(str, actions) {\n    for (var [start, end, tags] of this.match(str)) {\n      for (var tag of tags) {\n        if (typeof actions[tag] === 'function') {\n          actions[tag](start, end, str.slice(start, end + 1));\n        }\n      }\n    }\n  }\n\n}\n\nmodule.exports = StateMachine;\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dfa/index.js\n");

/***/ })

};
;