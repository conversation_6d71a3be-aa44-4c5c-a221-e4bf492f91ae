"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/ModernHeader.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ModernHeader.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaCheckCircle,FaChevronDown,FaGlobe,FaSearch,FaShieldAlt,FaShippingFast,FaShoppingCart,FaTimes,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nconst ShopMegaMenu = (param)=>{\n    let { isOpen, onClose } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [brands, setBrands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ShopMegaMenu.useEffect\": ()=>{\n            if (isOpen) {\n                // Load categories and brands when menu opens\n                Promise.all([\n                    fetch('/api/categories').then({\n                        \"ShopMegaMenu.useEffect\": (res)=>res.ok ? res.json() : {\n                                categories: []\n                            }\n                    }[\"ShopMegaMenu.useEffect\"]),\n                    fetch('/api/brands').then({\n                        \"ShopMegaMenu.useEffect\": (res)=>res.ok ? res.json() : {\n                                brands: []\n                            }\n                    }[\"ShopMegaMenu.useEffect\"])\n                ]).then({\n                    \"ShopMegaMenu.useEffect\": (param)=>{\n                        let [categoriesData, brandsData] = param;\n                        setCategories((categoriesData.categories || []).slice(0, 6));\n                        setBrands((brandsData.brands || []).slice(0, 6));\n                    }\n                }[\"ShopMegaMenu.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"ShopMegaMenu.useEffect\"], [\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: -10\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -10\n            },\n            transition: {\n                duration: 0.2\n            },\n            className: \"absolute top-full left-0 w-full bg-white shadow-xl border-t border-gray-100 z-50\",\n            onMouseLeave: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Cat\\xe9gories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: categories.length > 0 ? categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=\".concat(category.id),\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, category.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 23\n                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?category=eclairage\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"\\xc9clairage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?category=cables\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"C\\xe2bles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?category=prises\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"Prises & Interrupteurs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?category=protection\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"Protection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Marques\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: brands.length > 0 ? brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=\".concat(brand.id),\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: brand.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, brand.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 23\n                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?brand=schneider\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"Schneider Electric\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?brand=legrand\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"Legrand\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?brand=abb\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"ABB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products?brand=siemens\",\n                                                    className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"Siemens\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"R\\xe9sidentiel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Commercial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Industriel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Maison Intelligente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-light-gray p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-2\",\n                                    children: \"Produit Vedette\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-white rounded-md mb-3 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"D\\xe9couvrez notre gamme compl\\xe8te de produits \\xe9lectriques\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    className: \"btn btn-primary text-sm py-2 px-4\",\n                                    children: \"D\\xe9couvrir\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 55,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShopMegaMenu, \"CCjCKGeRSJMKYO6MdQLV5Zefsvk=\");\n_c = ShopMegaMenu;\nconst FeatureBar = ()=>{\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaShippingFast, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 142,\n                columnNumber: 13\n            }, undefined),\n            title: \"Livraison Gratuite\",\n            description: \"Sur toutes commandes de plus de 500 MAD\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaCheckCircle, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 147,\n                columnNumber: 13\n            }, undefined),\n            title: \"Garantie 30 Jours\",\n            description: \"Garantie de remboursement de 30 jours\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaGlobe, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 152,\n                columnNumber: 13\n            }, undefined),\n            title: \"Livraison Nationale\",\n            description: \"Livraison dans tout le Maroc\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaShieldAlt, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 157,\n                columnNumber: 13\n            }, undefined),\n            title: \"Paiement Sécurisé\",\n            description: \"100% sécurisé et protégé\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-t border-gray-100 py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-center space-x-4 group cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 group-hover:text-moonelec-red transition-colors duration-300\",\n                                children: feature.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-charcoal text-sm\",\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = FeatureBar;\nfunction ModernHeader() {\n    _s1();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isShopMenuOpen, setIsShopMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { user, signOut } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"ModernHeader.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"ModernHeader.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernHeader.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ModernHeader.useEffect\"];\n        }\n    }[\"ModernHeader.useEffect\"], []);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(\"/products?search=\".concat(encodeURIComponent(searchQuery.trim())));\n            setSearchQuery('');\n        }\n    };\n    const navigation = [\n        {\n            name: 'Accueil',\n            href: '/'\n        },\n        {\n            name: 'À Propos',\n            href: '/about'\n        },\n        {\n            name: 'Boutique',\n            href: '/products',\n            hasDropdown: true,\n            onMouseEnter: ()=>setIsShopMenuOpen(true),\n            onMouseLeave: ()=>setIsShopMenuOpen(false)\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-40 transition-all duration-300 \".concat(isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-white'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-12 h-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: \"/images/logo/logo-moonelec.png\",\n                                                alt: \"Moonelec Logo\",\n                                                fill: true,\n                                                className: \"object-contain\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:block text-2xl font-bold text-charcoal font-heading\",\n                                            children: \"Moonelec\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: item.onMouseEnter,\n                                            onMouseLeave: item.onMouseLeave,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"flex items-center space-x-1 font-medium transition-colors duration-200 \".concat(pathname === item.href ? 'text-moonelec-red' : 'text-charcoal hover:text-moonelec-red'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.hasDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaChevronDown, {\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSearch,\n                                            className: \"hidden md:flex\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Rechercher des produits...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"w-64 pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaSearch, {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this),\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(user.role.toLowerCase(), \"/dashboard\"),\n                                                    className: \"p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>signOut(),\n                                                    className: \"text-sm text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"D\\xe9connexion\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaUser, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/cart\",\n                                            className: \"relative p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaShoppingCart, {\n                                                    className: \"text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-moonelec-red text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                            className: \"lg:hidden p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaTimes, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaBars, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 71\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopMegaMenu, {\n                        isOpen: isShopMenuOpen,\n                        onClose: ()=>setIsShopMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"lg:hidden bg-white border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearch,\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Rechercher...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaSearch, {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"block py-2 font-medium transition-colors \".concat(pathname === item.href ? 'text-moonelec-red' : 'text-charcoal hover:text-moonelec-red'),\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: item.name\n                                            }, item.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureBar, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(ModernHeader, \"QMPdWmtEOIcEQfKEgdl9NvgxJoA=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c2 = ModernHeader;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ShopMegaMenu\");\n$RefreshReg$(_c1, \"FeatureBar\");\n$RefreshReg$(_c2, \"ModernHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/ModernHeader.tsx\n"));

/***/ })

});