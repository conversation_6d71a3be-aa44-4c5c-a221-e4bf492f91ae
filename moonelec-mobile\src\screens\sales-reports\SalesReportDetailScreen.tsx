import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  Linking,
  Share,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { SalesReport, RootStackParamList } from '../../types';
import ApiService from '../../services/api';

type SalesReportDetailRouteProp = RouteProp<RootStackParamList, 'SalesReportDetail'>;

const { width } = Dimensions.get('window');

const SalesReportDetailScreen: React.FC = () => {
  const route = useRoute<SalesReportDetailRouteProp>();
  const navigation = useNavigation();
  const { reportId } = route.params;

  const [report, setReport] = useState<SalesReport | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  useEffect(() => {
    loadReport();
  }, [reportId]);

  const loadReport = async () => {
    try {
      setIsLoading(true);
      // Note: You'll need to add this endpoint to your API service
      const reportData = await ApiService.getSalesReport(reportId);
      setReport(reportData);
    } catch (error) {
      console.error('Error loading sales report:', error);
      Alert.alert('Erreur', 'Impossible de charger le rapport de visite');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (!report) return;

    try {
      const message = `Rapport de visite - ${report.denomination}\n` +
                     `Contact: ${report.name}\n` +
                     `Ville: ${report.city}\n` +
                     `Date: ${new Date(report.visitDate).toLocaleDateString('fr-FR')}\n` +
                     `Objet: ${report.visitPurpose}`;

      await Share.share({
        message,
        title: `Rapport de visite - ${report.denomination}`,
      });
    } catch (error) {
      console.error('Error sharing report:', error);
    }
  };

  const openFile = async (url: string, type: 'video' | 'audio' | 'pdf') => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Erreur', `Impossible d'ouvrir le fichier ${type}`);
      }
    } catch (error) {
      console.error(`Error opening ${type}:`, error);
      Alert.alert('Erreur', `Erreur lors de l'ouverture du fichier ${type}`);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#006db7" />
        <Text style={styles.loadingText}>Chargement du rapport...</Text>
      </View>
    );
  }

  if (!report) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color="#ccc" />
        <Text style={styles.errorText}>Rapport non trouvé</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.denomination}>{report.denomination}</Text>
          <Text style={styles.visitDate}>
            {new Date(report.visitDate).toLocaleDateString('fr-FR')}
          </Text>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations de contact</Text>
          <View style={styles.contactInfo}>
            <View style={styles.contactRow}>
              <Ionicons name="person-outline" size={20} color="#666" />
              <Text style={styles.contactText}>{report.name}</Text>
            </View>
            <View style={styles.contactRow}>
              <Ionicons name="location-outline" size={20} color="#666" />
              <Text style={styles.contactText}>{report.city}</Text>
            </View>
          </View>
        </View>

        {/* Visit Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Détails de la visite</Text>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Objet de la visite</Text>
            <Text style={styles.detailValue}>{report.visitPurpose}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Besoin identifié</Text>
            <Text style={styles.detailValue}>{report.need}</Text>
          </View>

          {report.articleRef && (
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Référence article</Text>
              <Text style={styles.detailValue}>{report.articleRef}</Text>
            </View>
          )}

          {report.comment && (
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Commentaires</Text>
              <Text style={styles.detailValue}>{report.comment}</Text>
            </View>
          )}

          {report.complaint && (
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Réclamation</Text>
              <Text style={[styles.detailValue, styles.complaint]}>{report.complaint}</Text>
            </View>
          )}
        </View>

        {/* Images */}
        {report.images && report.images.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Photos ({report.images.length})</Text>

            <Image
              source={{ uri: report.images[selectedImageIndex] }}
              style={styles.mainImage}
              resizeMode="contain"
            />

            {report.images.length > 1 && (
              <ScrollView
                horizontal
                style={styles.thumbnailContainer}
                showsHorizontalScrollIndicator={false}
              >
                {report.images.map((image, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => setSelectedImageIndex(index)}
                    style={[
                      styles.thumbnail,
                      selectedImageIndex === index && styles.selectedThumbnail
                    ]}
                  >
                    <Image source={{ uri: image }} style={styles.thumbnailImage} />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            )}
          </View>
        )}

        {/* Media Files */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Fichiers joints</Text>

          {report.videoUrl && (
            <TouchableOpacity
              style={styles.mediaItem}
              onPress={() => openFile(report.videoUrl!, 'video')}
            >
              <View style={styles.mediaIcon}>
                <Ionicons name="videocam" size={24} color="#2196F3" />
              </View>
              <View style={styles.mediaInfo}>
                <Text style={styles.mediaTitle}>Vidéo de la visite</Text>
                <Text style={styles.mediaSubtitle}>Appuyez pour lire</Text>
              </View>
              <Ionicons name="play-circle-outline" size={24} color="#2196F3" />
            </TouchableOpacity>
          )}

          {report.audioUrl && (
            <TouchableOpacity
              style={styles.mediaItem}
              onPress={() => openFile(report.audioUrl!, 'audio')}
            >
              <View style={styles.mediaIcon}>
                <Ionicons name="mic" size={24} color="#FF9800" />
              </View>
              <View style={styles.mediaInfo}>
                <Text style={styles.mediaTitle}>Enregistrement audio</Text>
                <Text style={styles.mediaSubtitle}>Appuyez pour écouter</Text>
              </View>
              <Ionicons name="play-circle-outline" size={24} color="#FF9800" />
            </TouchableOpacity>
          )}

          {report.pdfUrl && (
            <TouchableOpacity
              style={styles.mediaItem}
              onPress={() => openFile(report.pdfUrl!, 'pdf')}
            >
              <View style={styles.mediaIcon}>
                <Ionicons name="document" size={24} color="#F44336" />
              </View>
              <View style={styles.mediaInfo}>
                <Text style={styles.mediaTitle}>Document PDF</Text>
                <Text style={styles.mediaSubtitle}>Appuyez pour ouvrir</Text>
              </View>
              <Ionicons name="open-outline" size={24} color="#F44336" />
            </TouchableOpacity>
          )}

          {!report.videoUrl && !report.audioUrl && !report.pdfUrl && (
            <View style={styles.emptyMedia}>
              <Ionicons name="attach-outline" size={48} color="#ccc" />
              <Text style={styles.emptyText}>Aucun fichier joint</Text>
            </View>
          )}
        </View>

        {/* Metadata */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations</Text>
          <View style={styles.metadataRow}>
            <Text style={styles.metadataLabel}>Créé le</Text>
            <Text style={styles.metadataValue}>
              {new Date(report.createdAt).toLocaleDateString('fr-FR')} à {new Date(report.createdAt).toLocaleTimeString('fr-FR')}
            </Text>
          </View>
          <View style={styles.metadataRow}>
            <Text style={styles.metadataLabel}>Modifié le</Text>
            <Text style={styles.metadataValue}>
              {new Date(report.updatedAt).toLocaleDateString('fr-FR')} à {new Date(report.updatedAt).toLocaleTimeString('fr-FR')}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={20} color="#006db7" />
          <Text style={styles.actionButtonText}>Partager</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="create-outline" size={20} color="#006db7" />
          <Text style={styles.actionButtonText}>Modifier</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: '#006db7',
    padding: 16,
    marginBottom: 8,
  },
  denomination: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  visitDate: {
    fontSize: 16,
    color: '#e3f2fd',
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  contactInfo: {
    gap: 12,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  contactText: {
    fontSize: 16,
    color: '#333',
  },
  detailItem: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  complaint: {
    color: '#F44336',
    fontWeight: '500',
  },
  mainImage: {
    width: width - 32,
    height: 250,
    borderRadius: 8,
    marginBottom: 16,
  },
  thumbnailContainer: {
    marginTop: 8,
  },
  thumbnail: {
    marginRight: 8,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedThumbnail: {
    borderColor: '#006db7',
  },
  thumbnailImage: {
    width: 60,
    height: 60,
  },
  mediaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 12,
  },
  mediaIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  mediaInfo: {
    flex: 1,
  },
  mediaTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  mediaSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  emptyMedia: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  metadataLabel: {
    fontSize: 14,
    color: '#666',
  },
  metadataValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#006db7',
    backgroundColor: '#fff',
    gap: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#006db7',
  },
});

export default SalesReportDetailScreen;
