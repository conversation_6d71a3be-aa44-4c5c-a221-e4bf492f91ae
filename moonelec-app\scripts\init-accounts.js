import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting account initialization...');

    // Vérifier si l'administrateur existe déjà
    const existingAdminUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
      include: {
        admin: true,
      },
    });

    let adminUser;
    let admin;

    if (existingAdminUser) {
      console.log(`Admin account already exists: ${existingAdminUser.firstname} ${existingAdminUser.lastname} (${existingAdminUser.email})`);
      adminUser = existingAdminUser;
      admin = existingAdminUser.admin;
    } else {
      // Créer un compte administrateur
      const adminPassword = await bcrypt.hash('123456', 10);
      const adminUserId = uuidv4();
      const adminId = uuidv4();

      adminUser = await prisma.user.create({
        data: {
          id: adminUserId,
          username: 'hicham.ezzamzami',
          email: '<EMAIL>',
          password: adminPassword,
          firstname: 'Hicham',
          lastname: 'Ezzamzami',
          role: 'ADMIN',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      admin = await prisma.admin.create({
        data: {
          id: adminId,
          userId: adminUserId,
        },
      });

      console.log(`Created admin account: ${adminUser.firstname} ${adminUser.lastname} (${adminUser.email})`);
    }

    // Vérifier si le client existe déjà
    const existingClientUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
      include: {
        client: true,
      },
    });

    let clientUser;
    let client;

    if (existingClientUser) {
      console.log(`Client account already exists: ${existingClientUser.firstname} ${existingClientUser.lastname} (${existingClientUser.email})`);
      clientUser = existingClientUser;
      client = existingClientUser.client;
    } else {
      // Créer un compte client
      const clientPassword = await bcrypt.hash('123456', 10);
      const clientUserId = uuidv4();
      const clientId = uuidv4();

      clientUser = await prisma.user.create({
        data: {
          id: clientUserId,
          username: 'client.test',
          email: '<EMAIL>',
          password: clientPassword,
          firstname: 'Client',
          lastname: 'Test',
          role: 'CLIENT',
          telephone: '**********',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      client = await prisma.client.create({
        data: {
          id: clientId,
          userId: clientUserId,
          company_name: 'Société Test',
        },
      });

      console.log(`Created client account: ${clientUser.firstname} ${clientUser.lastname} (${clientUser.email})`);
    }

    // Vérifier si le commercial existe déjà
    const existingCommercialUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
      include: {
        commercial: true,
      },
    });

    let commercialUser;
    let commercial;

    if (existingCommercialUser) {
      console.log(`Commercial account already exists: ${existingCommercialUser.firstname} ${existingCommercialUser.lastname} (${existingCommercialUser.email})`);
      commercialUser = existingCommercialUser;
      commercial = existingCommercialUser.commercial;
    } else {
      // Créer un compte commercial
      const commercialPassword = await bcrypt.hash('123456', 10);
      const commercialUserId = uuidv4();
      const commercialId = uuidv4();

      commercialUser = await prisma.user.create({
        data: {
          id: commercialUserId,
          username: 'commercial.test',
          email: '<EMAIL>',
          password: commercialPassword,
          firstname: 'Commercial',
          lastname: 'Test',
          role: 'COMMERCIAL',
          telephone: '**********',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      commercial = await prisma.commercial.create({
        data: {
          id: commercialId,
          userId: commercialUserId,
        },
      });

      console.log(`Created commercial account: ${commercialUser.firstname} ${commercialUser.lastname} (${commercialUser.email})`);
    }

    console.log('Account initialization completed successfully');
  } catch (error) {
    console.error('Error during account initialization:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
