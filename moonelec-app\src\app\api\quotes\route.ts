import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getQuotes, createQuote } from '@/lib/quotes';
import { QuoteStatus } from '@prisma/client';
import { getDefaultQuoteExpiryDate } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';

// GET /api/quotes - Récupérer tous les devis
export async function GET(req: NextRequest) {
  try {
    console.log('📋 Quotes API - Checking authentication...');

    // Check for mobile authentication first
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    console.log('📋 Quotes API - Auth results:', {
      mobileUser: mobileUser ? { id: mobileUser.id, role: mobileUser.role } : null,
      session: session ? { id: session.user?.id, role: session.user?.role } : null
    });

    if (!mobileUser && !session) {
      console.log('📋 Quotes API - No authentication found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use mobile user data if available, otherwise use session
    const user = mobileUser || session?.user;
    console.log('📋 Quotes API - Using user:', { id: user?.id, role: user?.role });

    const searchParams = req.nextUrl.searchParams;
    const search = searchParams.get('search') || undefined;
    const status = searchParams.get('status') as QuoteStatus | undefined;
    const page = searchParams.has('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.has('limit') ? parseInt(searchParams.get('limit')!) : 10;
    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : (page - 1) * limit;
    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : limit;

    // Filtrer par client si l'utilisateur est un client
    let clientId: string | undefined = undefined;
    if (user?.role === 'CLIENT' && user?.clientId) {
      clientId = user.clientId;
    } else if (searchParams.has('clientId')) {
      clientId = searchParams.get('clientId') || undefined;
    }

    // Utiliser la fonction existante pour récupérer les devis
    const { quotes, total } = await getQuotes({
      clientId,
      status,
      search,
      skip,
      take,
    });

    // Pour le tableau de bord, nous avons besoin d'informations supplémentaires
    // Si le paramètre 'dashboard' est présent, enrichir les données
    if (searchParams.get('dashboard') === 'true') {
      // Récupérer les informations détaillées pour chaque devis
      const enrichedQuotes = await Promise.all(
        quotes.map(async (quote) => {
          // Récupérer les informations du client
          const client = await prisma.client.findUnique({
            where: { id: quote.clientId },
            include: {
              user: {
                select: {
                  id: true,
                  firstname: true,
                  lastname: true,
                  email: true,
                  telephone: true
                }
              }
            }
          });

          // Récupérer les éléments du devis avec les informations des produits
          const items = await prisma.quoteitem.findMany({
            where: { quoteId: quote.id },
            include: {
              product: {
                select: {
                  name: true,
                  reference: true
                }
              }
            }
          });

          return {
            ...quote,
            client,
            items
          };
        })
      );

      return NextResponse.json({
        quotes: enrichedQuotes,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      });
    }

    return NextResponse.json({
      quotes,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });
  } catch (error: any) {
    console.error('Error fetching quotes:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch quotes' },
      { status: 500 }
    );
  }
}

// POST /api/quotes - Créer un nouveau devis
export async function POST(req: NextRequest) {
  try {
    // Check for mobile authentication first
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use mobile user data if available, otherwise use session
    const user = mobileUser || session?.user;

    const body = await req.json();
    const { items, notes } = body;

    // Vérifier que les items sont fournis
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Items are required and must be a non-empty array' },
        { status: 400 }
      );
    }

    // Déterminer l'ID du client
    let clientId: string;
    let createdByAdminId: string | undefined = undefined;

    if (user?.role === 'CLIENT') {
      // Si l'utilisateur est un client, utiliser son ID client
      if (!user.clientId) {
        console.error('Client ID not found in user:', user);
        return NextResponse.json(
          { error: 'Client ID not found in user data. Please contact support.' },
          { status: 400 }
        );
      }
      clientId = user.clientId;
    } else if (user?.role === 'ADMIN') {
      // Si l'utilisateur est un admin
      if (body.clientId) {
        // Si un clientId est fourni, l'utiliser
        clientId = body.clientId;
      } else if (user.clientId) {
        // Si l'admin a aussi un compte client, utiliser cet ID
        clientId = user.clientId;
      } else {
        // Créer un devis pour le premier client trouvé (pour les tests)
        const firstClient = await prisma.client.findFirst();
        if (!firstClient) {
          return NextResponse.json(
            { error: 'No client found in the system. Please create a client first.' },
            { status: 400 }
          );
        }
        clientId = firstClient.id;
      }

      // Enregistrer l'ID de l'admin qui crée le devis
      createdByAdminId = user.adminId;
    } else {
      // Les commerciaux ne peuvent pas créer de devis directement
      return NextResponse.json(
        { error: 'Unauthorized to create quotes' },
        { status: 403 }
      );
    }

    // Créer le devis
    const quote = await createQuote({
      clientId,
      notes,
      validUntil: getDefaultQuoteExpiryDate(),
      items: items.map((item: any) => ({
        productId: item.productId,
        quantity: item.quantity,
      })),
      createdByAdminId,
    });

    return NextResponse.json({ quote }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating quote:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create quote' },
      { status: 500 }
    );
  }
}
