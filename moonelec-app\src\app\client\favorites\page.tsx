'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Fa<PERSON>eart, FaShoppingCart, FaSearch, FaTrash } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';
import Image from 'next/image';

// Types
interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  mainImage: string | null;
  category: string;
  dateAdded: string;
}

export default function ClientFavoritesPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [favorites, setFavorites] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Données simulées pour les produits favoris
  const mockFavorites: Product[] = [
    {
      id: '1',
      reference: 'LED-A60-10W',
      name: 'Ampoule LED A60 10W E27 Blanc Chaud 2700K',
      description: 'Ampoule LED A60 10W E27 offrant une lumière blanc chaud de 2700K, idéale pour créer une ambiance chaleureuse.',
      mainImage: '/images/products/ampoule-led.jpg',
      category: 'Éclairage',
      dateAdded: '15/07/2023'
    },
    {
      id: '2',
      reference: 'SPOT-GU10-5W',
      name: 'Spot LED GU10 5W Blanc Neutre 4000K',
      description: 'Spot LED GU10 5W offrant une lumière blanc neutre de 4000K, parfait pour l\'éclairage de précision.',
      mainImage: '/images/products/spot-led.jpg',
      category: 'Éclairage',
      dateAdded: '10/07/2023'
    },
    {
      id: '3',
      reference: 'CABLE-3G1.5',
      name: 'Câble électrique 3G1.5 mm² - 100m',
      description: 'Câble électrique 3G1.5 mm² de haute qualité, vendu en bobine de 100 mètres.',
      mainImage: '/images/products/cable.jpg',
      category: 'Câblage',
      dateAdded: '05/07/2023'
    },
    {
      id: '4',
      reference: 'DISJ-16A',
      name: 'Disjoncteur magnétothermique 16A',
      description: 'Disjoncteur magnétothermique 16A pour la protection des circuits électriques.',
      mainImage: '/images/products/disjoncteur.jpg',
      category: 'Protection',
      dateAdded: '01/07/2023'
    }
  ];

  // Catégories simulées
  const categories = [
    'Éclairage',
    'Câblage',
    'Protection',
    'Appareillage',
    'Domotique'
  ];

  useEffect(() => {
    // Simuler le chargement des données
    const timer = setTimeout(() => {
      setFavorites(mockFavorites);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filtrer les produits favoris en fonction de la recherche et de la catégorie
  const filteredFavorites = favorites.filter(product => {
    const matchesSearch = 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory ? product.category === selectedCategory : true;
    
    return matchesSearch && matchesCategory;
  });

  const removeFavorite = (productId: string) => {
    setFavorites(favorites.filter(product => product.id !== productId));
  };

  const addToCart = (productId: string) => {
    // Logique pour ajouter au panier
    console.log('Ajout au panier du produit', productId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['CLIENT']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            Mes Produits Favoris
          </h1>
        </div>

        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Rechercher par nom, référence ou description..."
              />
            </div>
            <div className="w-full md:w-64">
              <select
                value={selectedCategory || ''}
                onChange={(e) => setSelectedCategory(e.target.value || null)}
                className="block w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Toutes les catégories</option>
                {categories.map((category, index) => (
                  <option key={index} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Favorites List */}
        {filteredFavorites.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFavorites.map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
              >
                <div className="relative h-48 w-full">
                  {product.mainImage ? (
                    <Image
                      src={product.mainImage}
                      alt={product.name}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-700">
                      <span className="text-gray-400">Aucune image</span>
                    </div>
                  )}
                  <div className="absolute top-2 right-2">
                    <button
                      onClick={() => removeFavorite(product.id)}
                      className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-md text-red-500 hover:text-red-700 transition-colors"
                    >
                      <FaHeart />
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h2 className="text-lg font-semibold text-gray-800 dark:text-white line-clamp-2">
                      {product.name}
                    </h2>
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                      {product.dateAdded}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    Réf: {product.reference}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                    {product.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full">
                      {product.category}
                    </span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => removeFavorite(product.id)}
                        className="p-2 text-red-500 hover:text-red-700 transition-colors"
                      >
                        <FaTrash title="Retirer des favoris" />
                      </button>
                      <button
                        onClick={() => addToCart(product.id)}
                        className="p-2 text-primary hover:text-primary-dark transition-colors"
                      >
                        <FaShoppingCart title="Ajouter au panier" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div className="flex justify-center mb-4">
              <FaHeart className="text-5xl text-gray-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              Aucun produit favori
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Vous n'avez pas encore ajouté de produits à vos favoris ou aucun produit ne correspond à vos critères de recherche.
            </p>
            <Link href="/products">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Découvrir nos produits
              </motion.button>
            </Link>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
