import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { RealTimeDataService, DataCache } from '@/lib/realtime-data';
import { checkIPSecurity, createSecureError } from '@/middleware/security';

// POST /api/realtime/sync - Trigger real-time data synchronization
export async function POST(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { type, action, entityId } = body;

    // Validate input
    if (!type || !action) {
      return NextResponse.json({ 
        error: 'Type and action are required' 
      }, { status: 400 });
    }

    // Clear relevant cache entries
    DataCache.clear();

    // Notify data change
    const notification = await RealTimeDataService.notifyDataChange(type, action);

    // Get updated data based on type
    let updatedData = null;
    
    switch (type) {
      case 'quote':
        updatedData = await RealTimeDataService.calculateRealRevenue();
        break;
      case 'product':
        updatedData = await RealTimeDataService.getProductPerformance();
        break;
      case 'user':
        updatedData = await RealTimeDataService.getUserActivityMetrics();
        break;
      default:
        updatedData = await RealTimeDataService.getDashboardData();
    }

    return NextResponse.json({
      success: true,
      notification,
      updatedData,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Real-time sync error:', error);
    return NextResponse.json({ 
      error: 'Sync failed' 
    }, { status: 500 });
  }
}

// GET /api/realtime/sync - Get current real-time data
export async function GET(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const dataType = url.searchParams.get('type') || 'dashboard';
    const forceRefresh = url.searchParams.get('refresh') === 'true';

    // Clear cache if force refresh
    if (forceRefresh) {
      DataCache.clear();
    }

    let data;
    const cacheKey = `realtime-${dataType}`;
    
    // Try to get from cache first
    if (!forceRefresh) {
      data = DataCache.get(cacheKey);
    }

    // If not in cache, fetch fresh data
    if (!data) {
      switch (dataType) {
        case 'revenue':
          data = await RealTimeDataService.calculateRealRevenue();
          break;
        case 'users':
          data = await RealTimeDataService.getUserActivityMetrics();
          break;
        case 'products':
          data = await RealTimeDataService.getProductPerformance();
          break;
        case 'trends':
          data = await RealTimeDataService.getSalesTrends();
          break;
        case 'dashboard':
        default:
          data = await RealTimeDataService.getDashboardData();
          break;
      }

      // Cache for 1 minute
      DataCache.set(cacheKey, data, 1);
    }

    return NextResponse.json({
      success: true,
      data,
      type: dataType,
      cached: !forceRefresh && DataCache.get(cacheKey) !== null,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Real-time data fetch error:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch real-time data' 
    }, { status: 500 });
  }
}

// WebSocket-like functionality for real-time updates
// This would be implemented with actual WebSocket or Server-Sent Events in production
export async function PUT(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { subscriptions } = body;

    // In a real implementation, this would:
    // 1. Register the client for real-time updates
    // 2. Set up WebSocket connection
    // 3. Send periodic updates based on subscriptions

    // For now, return the current data for all subscribed types
    const data: any = {};
    
    if (subscriptions.includes('dashboard')) {
      data.dashboard = await RealTimeDataService.getDashboardData();
    }
    
    if (subscriptions.includes('revenue')) {
      data.revenue = await RealTimeDataService.calculateRealRevenue();
    }
    
    if (subscriptions.includes('users')) {
      data.users = await RealTimeDataService.getUserActivityMetrics();
    }
    
    if (subscriptions.includes('products')) {
      data.products = await RealTimeDataService.getProductPerformance();
    }

    return NextResponse.json({
      success: true,
      subscriptions,
      data,
      message: 'Subscribed to real-time updates',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Real-time subscription error:', error);
    return NextResponse.json({ 
      error: 'Subscription failed' 
    }, { status: 500 });
  }
}

// DELETE /api/realtime/sync - Clear all cached data
export async function DELETE(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = mobileUser || session?.user;
    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Clear all cached data
    DataCache.clear();

    return NextResponse.json({
      success: true,
      message: 'All cached data cleared',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Cache clear error:', error);
    return NextResponse.json({ 
      error: 'Failed to clear cache' 
    }, { status: 500 });
  }
}
