import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Quote, QuoteStatus } from '../../types';
import ApiService from '../../services/api';

const QuotesScreen: React.FC = () => {
  const navigation = useNavigation();
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadQuotes();
  }, []);

  const loadQuotes = async () => {
    try {
      setIsLoading(true);
      const response = await ApiService.getQuotes({ limit: 50 });
      setQuotes(response.quotes);
    } catch (error) {
      console.error('Error loading quotes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadQuotes();
    setRefreshing(false);
  };

  const getStatusColor = (status: QuoteStatus) => {
    switch (status) {
      case QuoteStatus.PENDING:
        return '#FF9800';
      case QuoteStatus.APPROVED:
        return '#4CAF50';
      case QuoteStatus.REJECTED:
        return '#F44336';
      case QuoteStatus.EXPIRED:
        return '#9E9E9E';
      default:
        return '#666';
    }
  };

  const getStatusText = (status: QuoteStatus) => {
    switch (status) {
      case QuoteStatus.PENDING:
        return 'En attente';
      case QuoteStatus.APPROVED:
        return 'Approuvé';
      case QuoteStatus.REJECTED:
        return 'Rejeté';
      case QuoteStatus.EXPIRED:
        return 'Expiré';
      default:
        return status;
    }
  };

  const renderQuote = ({ item }: { item: Quote }) => (
    <TouchableOpacity
      style={styles.quoteCard}
      onPress={() => navigation.navigate('QuoteDetail', { quoteId: item.id })}
    >
      <View style={styles.quoteHeader}>
        <Text style={styles.quoteId}>Devis #{item.id.slice(-8)}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>

      <View style={styles.quoteInfo}>
        <Text style={styles.quoteDate}>
          Créé le {new Date(item.createdAt).toLocaleDateString('fr-FR')}
        </Text>
        <Text style={styles.quoteExpiry}>
          Valide jusqu'au {new Date(item.validUntil).toLocaleDateString('fr-FR')}
        </Text>
        {item.notes && (
          <Text style={styles.quoteNotes} numberOfLines={2}>
            {item.notes}
          </Text>
        )}
      </View>

      <View style={styles.quoteFooter}>
        <Text style={styles.itemsCount}>
          {item.items?.length || 0} article(s)
        </Text>
        <Ionicons name="chevron-forward" size={20} color="#666" />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Mes Devis</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreateQuote')}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={quotes}
        renderItem={renderQuote}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#006db7',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  quoteCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quoteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  quoteId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  quoteInfo: {
    marginBottom: 12,
  },
  quoteDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  quoteExpiry: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  quoteNotes: {
    fontSize: 14,
    color: '#333',
    fontStyle: 'italic',
  },
  quoteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemsCount: {
    fontSize: 14,
    color: '#006db7',
    fontWeight: '600',
  },
});

export default QuotesScreen;
