import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import {
  getSalesReports,
  createSalesReport,
  SalesReportInput
} from '@/lib/salesReports';
import { v4 as uuidv4 } from 'uuid';

// GET /api/sales-reports - Get all sales reports with optional filtering
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins and commercials can access sales reports
    if (session.user.role !== 'ADMIN' && session.user.role !== 'COMMERCIAL') {
      return NextResponse.json(
        { error: 'Only administrators and sales team can access sales reports' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const skip = parseInt(searchParams.get('skip') || '0', 10);
    const take = parseInt(searchParams.get('take') || '10', 10);

    // Parse filter parameters
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const commercialId = searchParams.get('commercialId') || undefined;
    const city = searchParams.get('city') || undefined;

    // Handle specific dates
    const specificDatesCount = parseInt(searchParams.get('specificDatesCount') || '0');
    let specificDates: Date[] | undefined = undefined;

    if (specificDatesCount > 0) {
      specificDates = [];
      for (let i = 0; i < specificDatesCount; i++) {
        const dateParam = searchParams.get(`specificDate${i}`);
        if (dateParam) {
          specificDates.push(new Date(dateParam));
        }
      }
    }

    const filter: any = {};

    if (specificDates && specificDates.length > 0) {
      filter.specificDates = specificDates;
    } else {
      if (startDateParam) {
        filter.startDate = new Date(startDateParam);
      }

      if (endDateParam) {
        filter.endDate = new Date(endDateParam);
      }
    }

    if (commercialId) {
      filter.commercialId = commercialId;
    } else if (session.user.role === 'COMMERCIAL') {
      // If the user is a commercial, only show their reports
      filter.commercialId = session.user.commercialId;
    }

    if (city) {
      filter.city = city;
    }

    const { reports, total } = await getSalesReports(filter, skip, take);

    return NextResponse.json({ reports, total, skip, take });
  } catch (error: any) {
    console.error('Error fetching sales reports:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch sales reports' },
      { status: 500 }
    );
  }
}

// POST /api/sales-reports - Create a new sales report
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only commercials can create sales reports
    if (session.user.role !== 'COMMERCIAL') {
      return NextResponse.json(
        { error: 'Only sales team members can create sales reports' },
        { status: 403 }
      );
    }

    const commercialId = session.user.commercialId;

    if (!commercialId) {
      return NextResponse.json(
        { error: 'Commercial ID not found in session' },
        { status: 400 }
      );
    }

    const formData = await req.formData();

    // Extract file uploads
    const imageFiles: File[] = [];
    let videoFile: File | null = null;
    let audioFile: File | null = null;
    let pdfFile: File | null = null;

    // Process images (multiple files)
    for (let i = 0; formData.get(`image${i}`); i++) {
      const file = formData.get(`image${i}`) as File;
      if (file) {
        imageFiles.push(file);
      }
    }

    // Process other files (single files)
    videoFile = formData.get('video') as File || null;
    audioFile = formData.get('audio') as File || null;
    pdfFile = formData.get('pdf') as File || null;

    // Process form fields
    const reportData: SalesReportInput = {
      need: formData.get('need') as string,
      articleRef: formData.get('articleRef') as string || undefined,
      comment: formData.get('comment') as string || undefined,
      visitDate: new Date(formData.get('visitDate') as string),
      denomination: formData.get('denomination') as string,
      name: formData.get('name') as string,
      visitPurpose: formData.get('visitPurpose') as string,
      complaint: formData.get('complaint') as string || undefined,
      city: formData.get('city') as string,
      images: formData.getAll('imageUrls').map(url => url.toString()),
      videoUrl: formData.get('videoUrl') as string || undefined,
      audioUrl: formData.get('audioUrl') as string || undefined,
      pdfUrl: formData.get('pdfUrl') as string || undefined,
    };

    // Create the sales report
    const report = await createSalesReport(commercialId, reportData);

    return NextResponse.json({ success: true, report });
  } catch (error: any) {
    console.error('Error creating sales report:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create sales report' },
      { status: 500 }
    );
  }
}
