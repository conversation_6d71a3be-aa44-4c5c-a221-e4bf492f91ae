{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/auth/refresh-token/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport { prisma } from '@/lib/prisma';\n\n// POST /api/auth/refresh-token - Generate a new token with the correct secret\nexport async function POST(req: NextRequest) {\n  try {\n    const body = await req.json();\n    const { username, password } = body;\n\n    if (!username || !password) {\n      return NextResponse.json(\n        { error: 'Username and password are required' },\n        { status: 400 }\n      );\n    }\n\n    console.log('🔄 Refresh token request for:', username);\n\n    // Find user by username or email\n    const user = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { username: username },\n          { email: username }\n        ]\n      },\n      include: {\n        client: true,\n        admin: true,\n      }\n    });\n\n    if (!user) {\n      console.log('❌ User not found:', username);\n      return NextResponse.json(\n        { error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n\n    // Simple password check (in production, use proper hashing)\n    if (user.password !== password) {\n      console.log('❌ Invalid password for user:', username);\n      return NextResponse.json(\n        { error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n\n    if (!user.isActive) {\n      console.log('❌ User account is inactive:', username);\n      return NextResponse.json(\n        { error: 'Account is inactive' },\n        { status: 401 }\n      );\n    }\n\n    // Generate new JWT token using JWT_SECRET\n    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';\n    console.log('🔄 Generating new token with JWT_SECRET');\n    \n    const token = jwt.sign(\n      {\n        userId: user.id,\n        username: user.username,\n        email: user.email,\n        role: user.role,\n      },\n      jwtSecret,\n      { expiresIn: '7d' } // Token valid for 7 days\n    );\n\n    // Prepare user data for response\n    const userData = {\n      id: user.id,\n      username: user.username,\n      email: user.email,\n      firstname: user.firstname,\n      lastname: user.lastname,\n      role: user.role,\n      clientId: user.client?.id,\n      adminId: user.admin?.id,\n    };\n\n    console.log('✅ New token generated successfully for:', username);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Token refreshed successfully',\n      token,\n      user: userData,\n    });\n\n  } catch (error) {\n    console.error('❌ Refresh token error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAE/B,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,iCAAiC;QACjC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;oBACF;wBAAE,UAAU;oBAAS;oBACrB;wBAAE,OAAO;oBAAS;iBACnB;YACH;YACA,SAAS;gBACP,QAAQ;gBACR,OAAO;YACT;QACF;QAEA,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,qBAAqB;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,4DAA4D;QAC5D,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,0CAA0C;QAC1C,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC3E,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,QAAQ,KAAK,EAAE;YACf,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB,GACA,WACA;YAAE,WAAW;QAAK,EAAE,yBAAyB;;QAG/C,iCAAiC;QACjC,MAAM,WAAW;YACf,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,MAAM,EAAE;YACvB,SAAS,KAAK,KAAK,EAAE;QACvB;QAEA,QAAQ,GAAG,CAAC,2CAA2C;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}