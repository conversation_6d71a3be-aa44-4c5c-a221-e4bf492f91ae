'use client';

import { useRef } from 'react';
import Image from 'next/image';
import { motion, useInView } from 'framer-motion';
import CountUp from 'react-countup';
import { FaHistory, FaUsers, FaWarehouse, FaGlobe } from 'react-icons/fa';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <Section
      id="about"
      accent
      title="À Propos de Moonelec"
      titleHighlight="Moonelec"
      subtitle="Découvrez l'histoire et l'expertise de Moonelec, votre partenaire de confiance dans la distribution de matériel électrique depuis plus de trois décennies."
      ref={ref}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        {/* Image Side */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative"
        >
          <div className="relative w-full h-[400px] rounded-lg overflow-hidden">
            <Image
              src="https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?q=80&w=2070&auto=format&fit=crop"
              alt="Entrepôt Moonelec"
              fill
              style={{ objectFit: 'cover' }}
              className="rounded-lg"
            />
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/30 to-transparent rounded-lg"></div>
          </div>

          {/* Experience Badge */}
          <motion.div
            className="absolute -bottom-8 -right-8 bg-white dark:bg-[#1a1a1a] p-6 rounded-lg shadow-xl"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-1">
                {isInView && (
                  <CountUp
                    start={0}
                    end={new Date().getFullYear() - 1990}
                    duration={2.5}
                    suffix=" ans"
                  />
                )}
              </div>
              <p className="text-sm text-text-secondary">d'expérience</p>
            </div>
          </motion.div>
        </motion.div>

        {/* Text Content */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h3 className="text-2xl font-semibold mb-6 text-text-primary">
            Notre Histoire et Notre Expertise
          </h3>

          <p className="text-text-secondary mb-6">
            La société Moonelec, est spécialisée dans la distribution de matériel électrique depuis 1990. Forte de plus de 300 000 références produits, elle offre une gamme complète de matériel électrique dans les secteurs résidentiel, tertiaire et industriel.
          </p>

          <p className="text-text-secondary mb-8">
            Ainsi elle met à disposition tout son savoir-faire et sa parfaite connaissance des produits. Afin d'accompagner de plus près ses clients dans la réalisation de leurs projets.
          </p>

          <Card className="p-6 mb-8 border-l-4 border-primary bg-primary/5 dark:bg-primary/10">
            <p className="text-text-secondary font-medium italic">
              "Parce qu'elle sait tirer profit de son expérience tout en ayant une vision de son activité résolument tournée vers l'avenir, est sans cesse à la recherche des innovations qui feront de demain un monde connecté."
            </p>
          </Card>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-6 mt-8">
            <StatItem
              icon={<FaHistory />}
              value={1990}
              label="Fondée en"
              delay={0.6}
              isInView={isInView}
            />
            <StatItem
              icon={<FaUsers />}
              value={5000}
              label="Clients satisfaits"
              delay={0.7}
              isInView={isInView}
              plus
            />
            <StatItem
              icon={<FaWarehouse />}
              value={300000}
              label="Références produits"
              delay={0.8}
              isInView={isInView}
              plus
            />
            <StatItem
              icon={<FaGlobe />}
              value={15}
              label="Pays desservis"
              delay={0.9}
              isInView={isInView}
              plus
            />
          </div>
        </motion.div>
      </div>
    </Section>
  );
}

function StatItem({
  icon,
  value,
  label,
  delay,
  isInView,
  plus = false
}: {
  icon: React.ReactNode;
  value: number;
  label: string;
  delay: number;
  isInView: boolean;
  plus?: boolean;
}) {
  return (
    <motion.div
      className="flex items-start"
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay }}
    >
      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary text-lg mr-4 mt-1">
        {icon}
      </div>
      <div>
        <div className="text-xl font-bold text-text-primary">
          {isInView && (
            <CountUp
              start={0}
              end={value}
              duration={2.5}
              separator=" "
              suffix={plus ? "+" : ""}
            />
          )}
        </div>
        <p className="text-sm text-text-secondary">{label}</p>
      </div>
    </motion.div>
  );
}
