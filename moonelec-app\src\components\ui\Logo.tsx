'use client';

import Image from 'next/image';
import Link from 'next/link';

interface LogoProps {
  width?: number;
  height?: number;
  className?: string;
  linkClassName?: string;
  href?: string;
}

export default function Logo({
  width = 180,
  height = 60,
  className = '',
  linkClassName = '',
  href = '/'
}: LogoProps) {
  return (
    <Link href={href} className={`flex items-center ${linkClassName}`}>
      <Image
        src="/images/logo/logo-moonelec.png"
        alt="Moonelec Logo"
        width={width}
        height={height}
        className={className}
        priority
      />
    </Link>
  );
}
