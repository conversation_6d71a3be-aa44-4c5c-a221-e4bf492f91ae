{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/utils.ts"], "sourcesContent": ["import { prisma } from './prisma';\n\n/**\n * Génère un numéro de devis unique au format Q-YYYY-XXXX\n * où YYYY est l'année en cours et XXXX est un numéro séquentiel\n */\nexport async function generateQuoteNumber(): Promise<string> {\n  const currentYear = new Date().getFullYear();\n  \n  // Trouver le dernier devis de l'année en cours\n  const lastQuote = await prisma.quote.findFirst({\n    where: {\n      quoteNumber: {\n        startsWith: `Q-${currentYear}-`,\n      },\n    },\n    orderBy: {\n      quoteNumber: 'desc',\n    },\n  });\n  \n  let sequentialNumber = 1;\n  \n  if (lastQuote) {\n    // Extraire le numéro séquentiel du dernier devis\n    const match = lastQuote.quoteNumber.match(/Q-\\d{4}-(\\d+)/);\n    if (match && match[1]) {\n      sequentialNumber = parseInt(match[1], 10) + 1;\n    }\n  }\n  \n  // Formater le numéro séquentiel avec des zéros en préfixe\n  const formattedNumber = sequentialNumber.toString().padStart(4, '0');\n  \n  return `Q-${currentYear}-${formattedNumber}`;\n}\n\n/**\n * Génère un numéro de commande unique au format O-YYYY-XXXX\n * où YYYY est l'année en cours et XXXX est un numéro séquentiel\n */\nexport async function generateOrderNumber(): Promise<string> {\n  const currentYear = new Date().getFullYear();\n  \n  // Trouver la dernière commande de l'année en cours\n  const lastOrder = await prisma.order.findFirst({\n    where: {\n      orderNumber: {\n        startsWith: `O-${currentYear}-`,\n      },\n    },\n    orderBy: {\n      orderNumber: 'desc',\n    },\n  });\n  \n  let sequentialNumber = 1;\n  \n  if (lastOrder) {\n    // Extraire le numéro séquentiel de la dernière commande\n    const match = lastOrder.orderNumber.match(/O-\\d{4}-(\\d+)/);\n    if (match && match[1]) {\n      sequentialNumber = parseInt(match[1], 10) + 1;\n    }\n  }\n  \n  // Formater le numéro séquentiel avec des zéros en préfixe\n  const formattedNumber = sequentialNumber.toString().padStart(4, '0');\n  \n  return `O-${currentYear}-${formattedNumber}`;\n}\n\n/**\n * Formate un prix en MAD\n */\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('fr-MA', {\n    style: 'currency',\n    currency: 'MAD',\n    minimumFractionDigits: 2,\n  }).format(price);\n}\n\n/**\n * Formate une date au format local\n */\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  return new Intl.DateTimeFormat('fr-MA', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n  }).format(dateObj);\n}\n\n/**\n * Calcule la date d'expiration par défaut pour un devis (30 jours à partir d'aujourd'hui)\n */\nexport function getDefaultQuoteExpiryDate(): Date {\n  const date = new Date();\n  date.setDate(date.getDate() + 30);\n  return date;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,+CAA+C;IAC/C,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;QAC7C,OAAO;YACL,aAAa;gBACX,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;YACjC;QACF;QACA,SAAS;YACP,aAAa;QACf;IACF;IAEA,IAAI,mBAAmB;IAEvB,IAAI,WAAW;QACb,iDAAiD;QACjD,MAAM,QAAQ,UAAU,WAAW,CAAC,KAAK,CAAC;QAC1C,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,mBAAmB,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM;QAC9C;IACF;IAEA,0DAA0D;IAC1D,MAAM,kBAAkB,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAEhE,OAAO,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,iBAAiB;AAC9C;AAMO,eAAe;IACpB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,mDAAmD;IACnD,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;QAC7C,OAAO;YACL,aAAa;gBACX,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;YACjC;QACF;QACA,SAAS;YACP,aAAa;QACf;IACF;IAEA,IAAI,mBAAmB;IAEvB,IAAI,WAAW;QACb,wDAAwD;QACxD,MAAM,QAAQ,UAAU,WAAW,CAAC,KAAK,CAAC;QAC1C,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,mBAAmB,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM;QAC9C;IACF;IAEA,0DAA0D;IAC1D,MAAM,kBAAkB,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAEhE,OAAO,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,iBAAiB;AAC9C;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS;IACd,MAAM,OAAO,IAAI;IACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;IAC9B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/notifications.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { NotificationType } from '@prisma/client';\n\n/**\n * Crée une notification pour tous les administrateurs\n */\nexport async function createNotificationForAllAdmins(\n  type: NotificationType,\n  message: string,\n  quoteId?: string\n) {\n  try {\n    // Récupérer tous les administrateurs\n    const admins = await prisma.admin.findMany();\n\n    // Créer une notification pour chaque administrateur\n    const notifications = await Promise.all(\n      admins.map(admin =>\n        prisma.notification.create({\n          data: {\n            type,\n            message,\n            adminId: admin.id,\n            ...(quoteId && { quoteId }),\n          },\n        })\n      )\n    );\n\n    // Dans une application réelle, vous récupéreriez les abonnements push de chaque administrateur\n    // et vous enverriez une notification push à chacun d'eux\n    // Exemple :\n    /*\n    for (const admin of admins) {\n      // Récupérer les abonnements push de l'administrateur\n      const pushSubscriptions = await prisma.pushSubscription.findMany({\n        where: { adminId: admin.id },\n      });\n\n      // Envoyer une notification push à chaque abonnement\n      for (const subscription of pushSubscriptions) {\n        try {\n          await sendPushNotification(\n            JSON.parse(subscription.subscription),\n            {\n              id: notifications.find(n => n.adminId === admin.id)?.id,\n              type,\n              message,\n              quoteId,\n            }\n          );\n        } catch (error) {\n          console.error(`Erreur lors de l'envoi de la notification push à l'administrateur ${admin.id}:`, error);\n        }\n      }\n    }\n    */\n\n    return notifications;\n  } catch (error) {\n    console.error('Erreur lors de la création des notifications:', error);\n    throw error;\n  }\n}\n\n/**\n * Crée une notification pour un administrateur spécifique\n */\nexport async function createNotificationForAdmin(\n  adminId: string,\n  type: NotificationType,\n  message: string,\n  quoteId?: string\n) {\n  try {\n    const notification = await prisma.notification.create({\n      data: {\n        type,\n        message,\n        adminId,\n        ...(quoteId && { quoteId }),\n      },\n    });\n\n    return notification;\n  } catch (error) {\n    console.error('Erreur lors de la création de la notification:', error);\n    throw error;\n  }\n}\n\n/**\n * Récupère les notifications non lues pour un administrateur\n */\nexport async function getUnreadNotificationsForAdmin(adminId: string) {\n  try {\n    const notifications = await prisma.notification.findMany({\n      where: {\n        adminId,\n        isRead: false,\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n      include: {\n        quote: {\n          select: {\n            quoteNumber: true,\n            client: {\n              include: {\n                user: {\n                  select: {\n                    firstname: true,\n                    lastname: true,\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    });\n\n    return notifications;\n  } catch (error) {\n    console.error('Erreur lors de la récupération des notifications:', error);\n    throw error;\n  }\n}\n\n/**\n * Marque une notification comme lue\n */\nexport async function markNotificationAsRead(notificationId: string) {\n  try {\n    const notification = await prisma.notification.update({\n      where: { id: notificationId },\n      data: { isRead: true },\n    });\n\n    return notification;\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour de la notification:', error);\n    throw error;\n  }\n}\n\n/**\n * Marque toutes les notifications d'un administrateur comme lues\n */\nexport async function markAllNotificationsAsRead(adminId: string) {\n  try {\n    const result = await prisma.notification.updateMany({\n      where: {\n        adminId,\n        isRead: false,\n      },\n      data: {\n        isRead: true,\n      },\n    });\n\n    return result;\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour des notifications:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAMO,eAAe,+BACpB,IAAsB,EACtB,OAAe,EACf,OAAgB;IAEhB,IAAI;QACF,qCAAqC;QACrC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAE1C,oDAAoD;QACpD,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,OAAO,GAAG,CAAC,CAAA,QACT,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACzB,MAAM;oBACJ;oBACA;oBACA,SAAS,MAAM,EAAE;oBACjB,GAAI,WAAW;wBAAE;oBAAQ,CAAC;gBAC5B;YACF;QAIJ,+FAA+F;QAC/F,yDAAyD;QACzD,YAAY;QACZ;;;;;;;;;;;;;;;;;;;;;;;;IAwBA,GAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,MAAM;IACR;AACF;AAKO,eAAe,2BACpB,OAAe,EACf,IAAsB,EACtB,OAAe,EACf,OAAgB;IAEhB,IAAI;QACF,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACpD,MAAM;gBACJ;gBACA;gBACA;gBACA,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,MAAM;IACR;AACF;AAKO,eAAe,+BAA+B,OAAe;IAClE,IAAI;QACF,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YACvD,OAAO;gBACL;gBACA,QAAQ;YACV;YACA,SAAS;gBACP,WAAW;YACb;YACA,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,aAAa;wBACb,QAAQ;4BACN,SAAS;gCACP,MAAM;oCACJ,QAAQ;wCACN,WAAW;wCACX,UAAU;oCACZ;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,cAAsB;IACjE,IAAI;QACF,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACpD,OAAO;gBAAE,IAAI;YAAe;YAC5B,MAAM;gBAAE,QAAQ;YAAK;QACvB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,MAAM;IACR;AACF;AAKO,eAAe,2BAA2B,OAAe;IAC9D,IAAI;QACF,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YAClD,OAAO;gBACL;gBACA,QAAQ;YACV;YACA,MAAM;gBACJ,QAAQ;YACV;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oDAAoD;QAClE,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/quotes.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { QuoteStatus, NotificationType } from '@prisma/client';\nimport { generateQuoteNumber } from './utils';\nimport { createNotificationForAllAdmins } from './notifications';\n\n// Obtenir tous les devis avec pagination et filtrage\nexport async function getQuotes(options?: {\n  clientId?: string;\n  status?: QuoteStatus;\n  search?: string;\n  skip?: number;\n  take?: number;\n}) {\n  const { clientId, status, search, skip = 0, take = 50 } = options || {};\n\n  const where = {\n    ...(clientId ? { clientId } : {}),\n    ...(status ? { status } : {}),\n    ...(search\n      ? {\n          OR: [\n            { quoteNumber: { contains: search } },\n            { client: { user: { firstname: { contains: search } } } },\n            { client: { user: { lastname: { contains: search } } } },\n            { client: { company_name: { contains: search } } },\n          ],\n        }\n      : {}),\n  };\n\n  const [quotes, total] = await Promise.all([\n    prisma.quote.findMany({\n      where,\n      include: {\n        client: {\n          include: {\n            user: {\n              select: {\n                firstname: true,\n                lastname: true,\n                email: true,\n                telephone: true,\n              },\n            },\n          },\n        },\n        quoteitem: {\n          include: {\n            product: true,\n          },\n        },\n      },\n      skip,\n      take,\n      orderBy: {\n        createdAt: 'desc',\n      },\n    }),\n    prisma.quote.count({ where }),\n  ]);\n\n  return { quotes, total };\n}\n\n// Obtenir un devis par son ID\nexport async function getQuoteById(id: string) {\n  return prisma.quote.findUnique({\n    where: { id },\n    include: {\n      client: {\n        include: {\n          user: {\n            select: {\n              id: true,\n              firstname: true,\n              lastname: true,\n              email: true,\n              telephone: true,\n              role: true,\n            },\n          },\n        },\n      },\n      quoteitem: {\n        include: {\n          product: {\n            include: {\n              category: true,\n              brand: true,\n              productimage: true,\n            },\n          },\n        },\n      },\n    },\n  });\n}\n\n// Créer un nouveau devis\nexport async function createQuote(data: {\n  clientId: string;\n  notes?: string;\n  validUntil?: Date;\n  items: {\n    productId: string;\n    quantity: number;\n  }[];\n  createdByAdminId?: string; // ID de l'admin qui crée le devis (si applicable)\n}) {\n  const { clientId, notes, validUntil, items, createdByAdminId } = data;\n\n  // Générer un numéro de devis unique\n  const quoteNumber = await generateQuoteNumber();\n\n  return prisma.$transaction(async (tx) => {\n    // Créer le devis\n    const quote = await tx.quote.create({\n      data: {\n        quoteNumber,\n        clientId,\n        status: QuoteStatus.DRAFT,\n        notes,\n        validUntil,\n        createdByAdminId,\n      },\n    });\n\n    // Ajouter les produits au devis\n    for (const item of items) {\n      await tx.quoteItem.create({\n        data: {\n          quoteId: quote.id,\n          productId: item.productId,\n          quantity: item.quantity,\n          unitPrice: 0, // Le prix sera défini par l'administrateur\n        },\n      });\n    }\n\n    // Récupérer les informations du client pour la notification\n    const client = await tx.client.findUnique({\n      where: { id: clientId },\n      include: {\n        user: {\n          select: {\n            firstname: true,\n            lastname: true,\n          },\n        },\n      },\n    });\n\n    // Créer une notification pour tous les administrateurs\n    let notificationMessage = '';\n\n    if (createdByAdminId) {\n      // Si le devis a été créé par un administrateur\n      const admin = await tx.admin.findUnique({\n        where: { id: createdByAdminId },\n        include: {\n          user: {\n            select: {\n              firstname: true,\n              lastname: true,\n            },\n          },\n        },\n      });\n\n      if (admin && admin.user) {\n        notificationMessage = `L'administrateur ${admin.user.firstname} ${admin.user.lastname} a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;\n      } else {\n        notificationMessage = `Un administrateur a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;\n      }\n    } else {\n      // Si le devis a été créé par un client\n      notificationMessage = `${client?.user.firstname} ${client?.user.lastname} a demandé un devis`;\n    }\n\n    // Créer la notification en dehors de la transaction pour éviter les problèmes\n    try {\n      await createNotificationForAllAdmins(\n        NotificationType.QUOTE_REQUESTED,\n        notificationMessage,\n        quote.id\n      );\n    } catch (error) {\n      console.error('Erreur lors de la création des notifications:', error);\n      // Ne pas bloquer la création du devis si la notification échoue\n    }\n\n    return quote;\n  });\n}\n\n// Mettre à jour un devis existant\nexport async function updateQuote(\n  id: string,\n  data: {\n    status?: QuoteStatus;\n    notes?: string;\n    validUntil?: Date | null;\n    totalAmount?: number;\n    pdfUrl?: string;\n    items?: {\n      id?: string; // Si l'item existe déjà\n      productId: string;\n      quantity: number;\n      unitPrice?: number;\n    }[];\n  }\n) {\n  const { status, notes, validUntil, totalAmount, pdfUrl, items } = data;\n\n  return prisma.$transaction(async (tx) => {\n    // Préparer les données à mettre à jour\n    const updateData: any = {};\n\n    if (status !== undefined) updateData.status = status;\n    if (notes !== undefined) updateData.notes = notes;\n    if (totalAmount !== undefined) updateData.totalAmount = totalAmount;\n    if (pdfUrl !== undefined) updateData.pdfUrl = pdfUrl;\n\n    // Traiter la date de validité\n    if (validUntil !== undefined) {\n      if (validUntil === null) {\n        updateData.validUntil = null;\n      } else if (typeof validUntil === 'string') {\n        // Si c'est une chaîne de caractères, s'assurer qu'elle est au format ISO\n        if (validUntil.includes('T')) {\n          // Déjà au format ISO complet\n          updateData.validUntil = new Date(validUntil);\n        } else {\n          // Ajouter l'heure (minuit) pour compléter le format ISO\n          updateData.validUntil = new Date(`${validUntil}T00:00:00.000Z`);\n        }\n      } else if (validUntil instanceof Date) {\n        updateData.validUntil = validUntil;\n      }\n    }\n\n    // Mettre à jour le devis\n    const quote = await tx.quote.update({\n      where: { id },\n      data: updateData,\n    });\n\n    // Mettre à jour les produits du devis si fournis\n    if (items && items.length > 0) {\n      // Récupérer les items existants\n      const existingItems = await tx.quoteItem.findMany({\n        where: { quoteId: id },\n      });\n\n      // Créer un map des items existants pour un accès rapide\n      const existingItemsMap = new Map(\n        existingItems.map(item => [item.productId, item])\n      );\n\n      // Traiter chaque item\n      for (const item of items) {\n        const existingItem = item.id\n          ? existingItems.find(i => i.id === item.id)\n          : existingItemsMap.get(item.productId);\n\n        if (existingItem) {\n          // Mettre à jour l'item existant\n          await tx.quoteItem.update({\n            where: { id: existingItem.id },\n            data: {\n              quantity: item.quantity,\n              ...(item.unitPrice !== undefined && { unitPrice: item.unitPrice }),\n            },\n          });\n        } else {\n          // Créer un nouvel item\n          await tx.quoteItem.create({\n            data: {\n              quoteId: id,\n              productId: item.productId,\n              quantity: item.quantity,\n              unitPrice: item.unitPrice || 0,\n            },\n          });\n        }\n      }\n\n      // Supprimer les items qui ne sont plus dans la liste\n      const newProductIds = items.map(item => item.productId);\n      const itemsToDelete = existingItems.filter(\n        item => !newProductIds.includes(item.productId)\n      );\n\n      for (const item of itemsToDelete) {\n        await tx.quoteItem.delete({\n          where: { id: item.id },\n        });\n      }\n    }\n\n    return quote;\n  });\n}\n\n// Supprimer un devis\nexport async function deleteQuote(id: string) {\n  return prisma.$transaction(async (tx) => {\n    // Supprimer d'abord les items du devis\n    await tx.quoteItem.deleteMany({\n      where: { quoteId: id },\n    });\n\n    // Supprimer le devis\n    return tx.quote.delete({\n      where: { id },\n    });\n  });\n}\n\n// Convertir un devis en commande\nexport async function convertQuoteToOrder(id: string) {\n  const quote = await getQuoteById(id);\n\n  if (!quote) {\n    throw new Error('Quote not found');\n  }\n\n  if (quote.status !== QuoteStatus.APPROVED) {\n    throw new Error('Only approved quotes can be converted to orders');\n  }\n\n  // Logique pour créer une commande à partir du devis\n  // À implémenter selon les besoins\n}\n\n// Obtenir les devis d'un client\nexport async function getClientQuotes(clientId: string, options?: {\n  status?: QuoteStatus;\n  skip?: number;\n  take?: number;\n}) {\n  const { status, skip = 0, take = 50 } = options || {};\n\n  const where = {\n    clientId,\n    ...(status ? { status } : {}),\n  };\n\n  const [quotes, total] = await Promise.all([\n    prisma.quote.findMany({\n      where,\n      include: {\n        quoteitem: {\n          include: {\n            product: true,\n          },\n        },\n      },\n      skip,\n      take,\n      orderBy: {\n        createdAt: 'desc',\n      },\n    }),\n    prisma.quote.count({ where }),\n  ]);\n\n  return { quotes, total };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,UAAU,OAM/B;IACC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAEtE,MAAM,QAAQ;QACZ,GAAI,WAAW;YAAE;QAAS,IAAI,CAAC,CAAC;QAChC,GAAI,SAAS;YAAE;QAAO,IAAI,CAAC,CAAC;QAC5B,GAAI,SACA;YACE,IAAI;gBACF;oBAAE,aAAa;wBAAE,UAAU;oBAAO;gBAAE;gBACpC;oBAAE,QAAQ;wBAAE,MAAM;4BAAE,WAAW;gCAAE,UAAU;4BAAO;wBAAE;oBAAE;gBAAE;gBACxD;oBAAE,QAAQ;wBAAE,MAAM;4BAAE,UAAU;gCAAE,UAAU;4BAAO;wBAAE;oBAAE;gBAAE;gBACvD;oBAAE,QAAQ;wBAAE,cAAc;4BAAE,UAAU;wBAAO;oBAAE;gBAAE;aAClD;QACH,IACA,CAAC,CAAC;IACR;IAEA,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QACxC,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpB;YACA,SAAS;gBACP,QAAQ;oBACN,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW;4BACb;wBACF;oBACF;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;YACA;YACA;YACA,SAAS;gBACP,WAAW;YACb;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE;QAAM;KAC5B;IAED,OAAO;QAAE;QAAQ;IAAM;AACzB;AAGO,eAAe,aAAa,EAAU;IAC3C,OAAO,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;QAC7B,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,WAAW;4BACX,UAAU;4BACV,OAAO;4BACP,WAAW;4BACX,MAAM;wBACR;oBACF;gBACF;YACF;YACA,WAAW;gBACT,SAAS;oBACP,SAAS;wBACP,SAAS;4BACP,UAAU;4BACV,OAAO;4BACP,cAAc;wBAChB;oBACF;gBACF;YACF;QACF;IACF;AACF;AAGO,eAAe,YAAY,IASjC;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG;IAEjE,oCAAoC;IACpC,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;IAE5C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,iBAAiB;QACjB,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ;gBACA;gBACA,QAAQ,6HAAA,CAAA,cAAW,CAAC,KAAK;gBACzB;gBACA;gBACA;YACF;QACF;QAEA,gCAAgC;QAChC,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gBACxB,MAAM;oBACJ,SAAS,MAAM,EAAE;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,WAAW;gBACb;YACF;QACF;QAEA,4DAA4D;QAC5D,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAS;YACtB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,uDAAuD;QACvD,IAAI,sBAAsB;QAE1B,IAAI,kBAAkB;YACpB,+CAA+C;YAC/C,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;gBACtC,OAAO;oBAAE,IAAI;gBAAiB;gBAC9B,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,WAAW;4BACX,UAAU;wBACZ;oBACF;gBACF;YACF;YAEA,IAAI,SAAS,MAAM,IAAI,EAAE;gBACvB,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,QAAQ,KAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,UAAU;YACjK,OAAO;gBACL,sBAAsB,CAAC,uCAAuC,EAAE,QAAQ,KAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,UAAU;YACnH;QACF,OAAO;YACL,uCAAuC;YACvC,sBAAsB,GAAG,QAAQ,KAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,SAAS,mBAAmB,CAAC;QAC/F;QAEA,8EAA8E;QAC9E,IAAI;YACF,MAAM,CAAA,GAAA,6HAAA,CAAA,iCAA8B,AAAD,EACjC,6HAAA,CAAA,mBAAgB,CAAC,eAAe,EAChC,qBACA,MAAM,EAAE;QAEZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,gEAAgE;QAClE;QAEA,OAAO;IACT;AACF;AAGO,eAAe,YACpB,EAAU,EACV,IAYC;IAED,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAElE,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,uCAAuC;QACvC,MAAM,aAAkB,CAAC;QAEzB,IAAI,WAAW,WAAW,WAAW,MAAM,GAAG;QAC9C,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG;QAC5C,IAAI,gBAAgB,WAAW,WAAW,WAAW,GAAG;QACxD,IAAI,WAAW,WAAW,WAAW,MAAM,GAAG;QAE9C,8BAA8B;QAC9B,IAAI,eAAe,WAAW;YAC5B,IAAI,eAAe,MAAM;gBACvB,WAAW,UAAU,GAAG;YAC1B,OAAO,IAAI,OAAO,eAAe,UAAU;gBACzC,yEAAyE;gBACzE,IAAI,WAAW,QAAQ,CAAC,MAAM;oBAC5B,6BAA6B;oBAC7B,WAAW,UAAU,GAAG,IAAI,KAAK;gBACnC,OAAO;oBACL,wDAAwD;oBACxD,WAAW,UAAU,GAAG,IAAI,KAAK,GAAG,WAAW,cAAc,CAAC;gBAChE;YACF,OAAO,IAAI,sBAAsB,MAAM;gBACrC,WAAW,UAAU,GAAG;YAC1B;QACF;QAEA,yBAAyB;QACzB,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,OAAO;gBAAE;YAAG;YACZ,MAAM;QACR;QAEA,iDAAiD;QACjD,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,gCAAgC;YAChC,MAAM,gBAAgB,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAChD,OAAO;oBAAE,SAAS;gBAAG;YACvB;YAEA,wDAAwD;YACxD,MAAM,mBAAmB,IAAI,IAC3B,cAAc,GAAG,CAAC,CAAA,OAAQ;oBAAC,KAAK,SAAS;oBAAE;iBAAK;YAGlD,sBAAsB;YACtB,KAAK,MAAM,QAAQ,MAAO;gBACxB,MAAM,eAAe,KAAK,EAAE,GACxB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,IACxC,iBAAiB,GAAG,CAAC,KAAK,SAAS;gBAEvC,IAAI,cAAc;oBAChB,gCAAgC;oBAChC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;wBACxB,OAAO;4BAAE,IAAI,aAAa,EAAE;wBAAC;wBAC7B,MAAM;4BACJ,UAAU,KAAK,QAAQ;4BACvB,GAAI,KAAK,SAAS,KAAK,aAAa;gCAAE,WAAW,KAAK,SAAS;4BAAC,CAAC;wBACnE;oBACF;gBACF,OAAO;oBACL,uBAAuB;oBACvB,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;wBACxB,MAAM;4BACJ,SAAS;4BACT,WAAW,KAAK,SAAS;4BACzB,UAAU,KAAK,QAAQ;4BACvB,WAAW,KAAK,SAAS,IAAI;wBAC/B;oBACF;gBACF;YACF;YAEA,qDAAqD;YACrD,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,SAAS;YACtD,MAAM,gBAAgB,cAAc,MAAM,CACxC,CAAA,OAAQ,CAAC,cAAc,QAAQ,CAAC,KAAK,SAAS;YAGhD,KAAK,MAAM,QAAQ,cAAe;gBAChC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;oBACxB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;gBACvB;YACF;QACF;QAEA,OAAO;IACT;AACF;AAGO,eAAe,YAAY,EAAU;IAC1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,uCAAuC;QACvC,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;YAC5B,OAAO;gBAAE,SAAS;YAAG;QACvB;QAEA,qBAAqB;QACrB,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;YACrB,OAAO;gBAAE;YAAG;QACd;IACF;AACF;AAGO,eAAe,oBAAoB,EAAU;IAClD,MAAM,QAAQ,MAAM,aAAa;IAEjC,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,MAAM,MAAM,KAAK,6HAAA,CAAA,cAAW,CAAC,QAAQ,EAAE;QACzC,MAAM,IAAI,MAAM;IAClB;AAEA,oDAAoD;AACpD,kCAAkC;AACpC;AAGO,eAAe,gBAAgB,QAAgB,EAAE,OAIvD;IACC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAEpD,MAAM,QAAQ;QACZ;QACA,GAAI,SAAS;YAAE;QAAO,IAAI,CAAC,CAAC;IAC9B;IAEA,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QACxC,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpB;YACA,SAAS;gBACP,WAAW;oBACT,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;YACA;YACA;YACA,SAAS;gBACP,WAAW;YACb;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE;QAAM;KAC5B;IAED,OAAO;QAAE;QAAQ;IAAM;AACzB", "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n\n    // Verify JWT token\n    const decoded = jwt.verify(\n      token,\n      process.env.NEXTAUTH_SECRET || 'fallback-secret'\n    ) as any;\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n        isActive: true,\n      }\n    });\n\n    if (!user || !user.isActive) {\n      return null;\n    }\n\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n  \n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') || \n         userAgent.includes('Expo') || \n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  if (!isMobileRequest(request)) {\n    return null;\n  }\n  \n  return await verifyMobileToken(request);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QAEjE,mBAAmB;QACnB,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CACxB,OACA,QAAQ,GAAG,CAAC,eAAe,IAAI;QAGjC,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,IAAI,CAAC,gBAAgB,UAAU;QAC7B,OAAO;IACT;IAEA,OAAO,MAAM,kBAAkB;AACjC", "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/quotes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { getQuotes, createQuote } from '@/lib/quotes';\nimport { QuoteStatus } from '@prisma/client';\nimport { getDefaultQuoteExpiryDate } from '@/lib/utils';\nimport { prisma } from '@/lib/prisma';\nimport { authOptions } from '@/lib/auth-options';\nimport { getMobileUserFromRequest } from '@/lib/mobile-auth';\n\n// GET /api/quotes - Récupérer tous les devis\nexport async function GET(req: NextRequest) {\n  try {\n    // Check for mobile authentication first\n    const mobileUser = await getMobileUserFromRequest(req);\n    const session = await getServerSession(authOptions);\n\n    if (!mobileUser && !session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Use mobile user data if available, otherwise use session\n    const user = mobileUser || session?.user;\n\n    const searchParams = req.nextUrl.searchParams;\n    const search = searchParams.get('search') || undefined;\n    const status = searchParams.get('status') as QuoteStatus | undefined;\n    const page = searchParams.has('page') ? parseInt(searchParams.get('page')!) : 1;\n    const limit = searchParams.has('limit') ? parseInt(searchParams.get('limit')!) : 10;\n    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : (page - 1) * limit;\n    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : limit;\n\n    // Filtrer par client si l'utilisateur est un client\n    let clientId: string | undefined = undefined;\n    if (user?.role === 'CLIENT' && user?.clientId) {\n      clientId = user.clientId;\n    } else if (searchParams.has('clientId')) {\n      clientId = searchParams.get('clientId') || undefined;\n    }\n\n    // Utiliser la fonction existante pour récupérer les devis\n    const { quotes, total } = await getQuotes({\n      clientId,\n      status,\n      search,\n      skip,\n      take,\n    });\n\n    // Pour le tableau de bord, nous avons besoin d'informations supplémentaires\n    // Si le paramètre 'dashboard' est présent, enrichir les données\n    if (searchParams.get('dashboard') === 'true') {\n      // Récupérer les informations détaillées pour chaque devis\n      const enrichedQuotes = await Promise.all(\n        quotes.map(async (quote) => {\n          // Récupérer les informations du client\n          const client = await prisma.client.findUnique({\n            where: { id: quote.clientId },\n            include: {\n              user: {\n                select: {\n                  id: true,\n                  firstname: true,\n                  lastname: true,\n                  email: true,\n                  telephone: true\n                }\n              }\n            }\n          });\n\n          // Récupérer les éléments du devis avec les informations des produits\n          const items = await prisma.quoteitem.findMany({\n            where: { quoteId: quote.id },\n            include: {\n              product: {\n                select: {\n                  name: true,\n                  reference: true\n                }\n              }\n            }\n          });\n\n          return {\n            ...quote,\n            client,\n            items\n          };\n        })\n      );\n\n      return NextResponse.json({\n        quotes: enrichedQuotes,\n        total,\n        page,\n        limit,\n        totalPages: Math.ceil(total / limit)\n      });\n    }\n\n    return NextResponse.json({\n      quotes,\n      total,\n      page,\n      limit,\n      totalPages: Math.ceil(total / limit)\n    });\n  } catch (error: any) {\n    console.error('Error fetching quotes:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch quotes' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/quotes - Créer un nouveau devis\nexport async function POST(req: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const body = await req.json();\n    const { items, notes } = body;\n\n    // Vérifier que les items sont fournis\n    if (!items || !Array.isArray(items) || items.length === 0) {\n      return NextResponse.json(\n        { error: 'Items are required and must be a non-empty array' },\n        { status: 400 }\n      );\n    }\n\n    // Déterminer l'ID du client\n    let clientId: string;\n    let createdByAdminId: string | undefined = undefined;\n\n    if (session.user.role === 'CLIENT') {\n      // Si l'utilisateur est un client, utiliser son ID client\n      if (!session.user.clientId) {\n        console.error('Client ID not found in session:', session.user);\n        return NextResponse.json(\n          { error: 'Client ID not found in session. Please contact support.' },\n          { status: 400 }\n        );\n      }\n      clientId = session.user.clientId;\n    } else if (session.user.role === 'ADMIN') {\n      // Si l'utilisateur est un admin\n      if (body.clientId) {\n        // Si un clientId est fourni, l'utiliser\n        clientId = body.clientId;\n      } else if (session.user.clientId) {\n        // Si l'admin a aussi un compte client, utiliser cet ID\n        clientId = session.user.clientId;\n      } else {\n        // Créer un devis pour le premier client trouvé (pour les tests)\n        const firstClient = await prisma.client.findFirst();\n        if (!firstClient) {\n          return NextResponse.json(\n            { error: 'No client found in the system. Please create a client first.' },\n            { status: 400 }\n          );\n        }\n        clientId = firstClient.id;\n      }\n\n      // Enregistrer l'ID de l'admin qui crée le devis\n      createdByAdminId = session.user.adminId;\n    } else {\n      // Les commerciaux ne peuvent pas créer de devis directement\n      return NextResponse.json(\n        { error: 'Unauthorized to create quotes' },\n        { status: 403 }\n      );\n    }\n\n    // Créer le devis\n    const quote = await createQuote({\n      clientId,\n      notes,\n      validUntil: getDefaultQuoteExpiryDate(),\n      items: items.map((item: any) => ({\n        productId: item.productId,\n        quantity: item.quantity,\n      })),\n      createdByAdminId,\n    });\n\n    return NextResponse.json({ quote }, { status: 201 });\n  } catch (error: any) {\n    console.error('Error creating quote:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to create quote' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,wCAAwC;QACxC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,2DAA2D;QAC3D,MAAM,OAAO,cAAc,SAAS;QAEpC,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,QAAQ,aAAa,GAAG,CAAC,WAAW,SAAS,aAAa,GAAG,CAAC,YAAa;QACjF,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY,CAAC,OAAO,CAAC,IAAI;QAC3F,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAE9E,oDAAoD;QACpD,IAAI,WAA+B;QACnC,IAAI,MAAM,SAAS,YAAY,MAAM,UAAU;YAC7C,WAAW,KAAK,QAAQ;QAC1B,OAAO,IAAI,aAAa,GAAG,CAAC,aAAa;YACvC,WAAW,aAAa,GAAG,CAAC,eAAe;QAC7C;QAEA,0DAA0D;QAC1D,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE;YACxC;YACA;YACA;YACA;YACA;QACF;QAEA,4EAA4E;QAC5E,gEAAgE;QAChE,IAAI,aAAa,GAAG,CAAC,iBAAiB,QAAQ;YAC5C,0DAA0D;YAC1D,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CACtC,OAAO,GAAG,CAAC,OAAO;gBAChB,uCAAuC;gBACvC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;oBAC5C,OAAO;wBAAE,IAAI,MAAM,QAAQ;oBAAC;oBAC5B,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW;4BACb;wBACF;oBACF;gBACF;gBAEA,qEAAqE;gBACrE,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAC5C,OAAO;wBAAE,SAAS,MAAM,EAAE;oBAAC;oBAC3B,SAAS;wBACP,SAAS;4BACP,QAAQ;gCACN,MAAM;gCACN,WAAW;4BACb;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR;oBACA;gBACF;YACF;YAGF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR;gBACA;gBACA;gBACA,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA;YACA;YACA,YAAY,KAAK,IAAI,CAAC,QAAQ;QAChC;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAyB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;QAEzB,sCAAsC;QACtC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmD,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,IAAI;QACJ,IAAI,mBAAuC;QAE3C,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,UAAU;YAClC,yDAAyD;YACzD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAC1B,QAAQ,KAAK,CAAC,mCAAmC,QAAQ,IAAI;gBAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA0D,GACnE;oBAAE,QAAQ;gBAAI;YAElB;YACA,WAAW,QAAQ,IAAI,CAAC,QAAQ;QAClC,OAAO,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACxC,gCAAgC;YAChC,IAAI,KAAK,QAAQ,EAAE;gBACjB,wCAAwC;gBACxC,WAAW,KAAK,QAAQ;YAC1B,OAAO,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAChC,uDAAuD;gBACvD,WAAW,QAAQ,IAAI,CAAC,QAAQ;YAClC,OAAO;gBACL,gEAAgE;gBAChE,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS;gBACjD,IAAI,CAAC,aAAa;oBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBAAE,OAAO;oBAA+D,GACxE;wBAAE,QAAQ;oBAAI;gBAElB;gBACA,WAAW,YAAY,EAAE;YAC3B;YAEA,gDAAgD;YAChD,mBAAmB,QAAQ,IAAI,CAAC,OAAO;QACzC,OAAO;YACL,4DAA4D;YAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;YAC9B;YACA;YACA,YAAY,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD;YACpC,OAAO,MAAM,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC/B,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;gBACzB,CAAC;YACD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM,GAAG;YAAE,QAAQ;QAAI;IACpD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAyB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}