/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sales-reports/route";
exports.ids = ["app/api/sales-reports/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsales-reports%2Froute&page=%2Fapi%2Fsales-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales-reports%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsales-reports%2Froute&page=%2Fapi%2Fsales-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales-reports%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_sales_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/sales-reports/route.ts */ \"(rsc)/./src/app/api/sales-reports/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sales-reports/route\",\n        pathname: \"/api/sales-reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/sales-reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\sales-reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_sales_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsales-reports%2Froute&page=%2Fapi%2Fsales-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales-reports%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/sales-reports/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/sales-reports/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./src/lib/auth-options.ts\");\n/* harmony import */ var _lib_salesReports__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/salesReports */ \"(rsc)/./src/lib/salesReports.ts\");\n\n\n\n\n// GET /api/sales-reports - Get all sales reports with optional filtering\nasync function GET(req) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Only admins and commercials can access sales reports\n        if (session.user.role !== 'ADMIN' && session.user.role !== 'COMMERCIAL') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Only administrators and sales team can access sales reports'\n            }, {\n                status: 403\n            });\n        }\n        // Parse query parameters\n        const searchParams = req.nextUrl.searchParams;\n        const skip = parseInt(searchParams.get('skip') || '0', 10);\n        const take = parseInt(searchParams.get('take') || '10', 10);\n        // Parse filter parameters\n        const startDateParam = searchParams.get('startDate');\n        const endDateParam = searchParams.get('endDate');\n        const commercialId = searchParams.get('commercialId') || undefined;\n        const city = searchParams.get('city') || undefined;\n        // Handle specific dates\n        const specificDatesCount = parseInt(searchParams.get('specificDatesCount') || '0');\n        let specificDates = undefined;\n        if (specificDatesCount > 0) {\n            specificDates = [];\n            for(let i = 0; i < specificDatesCount; i++){\n                const dateParam = searchParams.get(`specificDate${i}`);\n                if (dateParam) {\n                    specificDates.push(new Date(dateParam));\n                }\n            }\n        }\n        const filter = {};\n        if (specificDates && specificDates.length > 0) {\n            filter.specificDates = specificDates;\n        } else {\n            if (startDateParam) {\n                filter.startDate = new Date(startDateParam);\n            }\n            if (endDateParam) {\n                filter.endDate = new Date(endDateParam);\n            }\n        }\n        if (commercialId) {\n            filter.commercialId = commercialId;\n        } else if (session.user.role === 'COMMERCIAL') {\n            // If the user is a commercial, only show their reports\n            filter.commercialId = session.user.commercialId;\n        }\n        if (city) {\n            filter.city = city;\n        }\n        const { reports, total } = await (0,_lib_salesReports__WEBPACK_IMPORTED_MODULE_3__.getSalesReports)(filter, skip, take);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            reports,\n            total,\n            skip,\n            take\n        });\n    } catch (error) {\n        console.error('Error fetching sales reports:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch sales reports'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/sales-reports - Create a new sales report\nasync function POST(req) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Only commercials can create sales reports\n        if (session.user.role !== 'COMMERCIAL') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Only sales team members can create sales reports'\n            }, {\n                status: 403\n            });\n        }\n        const commercialId = session.user.commercialId;\n        if (!commercialId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Commercial ID not found in session'\n            }, {\n                status: 400\n            });\n        }\n        const formData = await req.formData();\n        // Extract file uploads\n        const imageFiles = [];\n        let videoFile = null;\n        let audioFile = null;\n        let pdfFile = null;\n        // Process images (multiple files)\n        for(let i = 0; formData.get(`image${i}`); i++){\n            const file = formData.get(`image${i}`);\n            if (file) {\n                imageFiles.push(file);\n            }\n        }\n        // Process other files (single files)\n        videoFile = formData.get('video') || null;\n        audioFile = formData.get('audio') || null;\n        pdfFile = formData.get('pdf') || null;\n        // Process form fields\n        const reportData = {\n            need: formData.get('need'),\n            articleRef: formData.get('articleRef') || undefined,\n            comment: formData.get('comment') || undefined,\n            visitDate: new Date(formData.get('visitDate')),\n            denomination: formData.get('denomination'),\n            name: formData.get('name'),\n            visitPurpose: formData.get('visitPurpose'),\n            complaint: formData.get('complaint') || undefined,\n            city: formData.get('city'),\n            images: formData.getAll('imageUrls').map((url)=>url.toString()),\n            videoUrl: formData.get('videoUrl') || undefined,\n            audioUrl: formData.get('audioUrl') || undefined,\n            pdfUrl: formData.get('pdfUrl') || undefined\n        };\n        // Create the sales report\n        const report = await (0,_lib_salesReports__WEBPACK_IMPORTED_MODULE_3__.createSalesReport)(commercialId, reportData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            report\n        });\n    } catch (error) {\n        console.error('Error creating sales report:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to create sales report'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/sales-reports/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconsole.log('🔧 Auth options module loaded');\nconsole.log('🔧 Creating auth options...');\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                username: {\n                    label: \"Nom d'utilisateur\",\n                    type: 'text'\n                },\n                password: {\n                    label: 'Mot de passe',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                console.log('🔍 AUTHORIZE FUNCTION CALLED!');\n                console.log('🔍 Auth attempt:', {\n                    username: credentials?.username,\n                    hasPassword: !!credentials?.password\n                });\n                if (!credentials?.username || !credentials?.password) {\n                    console.log('❌ Missing credentials');\n                    return null;\n                }\n                try {\n                    // Rechercher l'utilisateur par nom d'utilisateur\n                    console.log('🔍 Looking for user:', credentials.username);\n                    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.findUserByUsername)(credentials.username);\n                    // Vérifier si l'utilisateur existe\n                    if (!user) {\n                        console.log('❌ User not found:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ User found:', {\n                        id: user.id,\n                        username: user.username,\n                        role: user.role\n                    });\n                    // Vérifier le mot de passe\n                    console.log('🔍 Verifying password...');\n                    const isPasswordValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log('❌ Invalid password for user:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ Password valid for user:', credentials.username);\n                    // Retourner les données de l'utilisateur sans le mot de passe\n                    const userObject = {\n                        id: user.id,\n                        email: user.email,\n                        username: user.username,\n                        name: `${user.firstname} ${user.lastname}`,\n                        firstname: user.firstname,\n                        lastname: user.lastname,\n                        role: user.role,\n                        clientId: user.client?.id,\n                        commercialId: user.commercial?.id,\n                        adminId: user.admin?.id\n                    };\n                    console.log('✅ Returning user object:', userObject);\n                    return userObject;\n                } catch (error) {\n                    console.error('❌ Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.username = user.username;\n                token.role = user.role;\n                token.firstname = user.firstname;\n                token.lastname = user.lastname;\n                token.clientId = user.clientId;\n                token.commercialId = user.commercialId;\n                token.adminId = user.adminId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.username = token.username;\n                session.user.role = token.role;\n                session.user.firstname = token.firstname;\n                session.user.lastname = token.lastname;\n                session.user.clientId = token.clientId;\n                session.user.commercialId = token.commercialId;\n                session.user.adminId = token.adminId;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signOut: '/auth/signout',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   createClientUser: () => (/* binding */ createClientUser),\n/* harmony export */   createCommercialUser: () => (/* binding */ createCommercialUser),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Fonction pour hacher un mot de passe\nasync function hashPassword(password) {\n    const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].genSalt(10);\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, salt);\n}\n// Fonction pour vérifier un mot de passe\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Fonction pour créer un utilisateur client\nasync function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.CLIENT\n            }\n        });\n        // Créer le profil client associé\n        const client = await tx.client.create({\n            data: {\n                userId: user.id,\n                company_name\n            }\n        });\n        return {\n            user,\n            client\n        };\n    });\n}\n// Fonction pour créer un utilisateur commercial\nasync function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.COMMERCIAL\n            }\n        });\n        // Créer le profil commercial associé\n        const commercial = await tx.commercial.create({\n            data: {\n                userId: user.id,\n                profile_photo\n            }\n        });\n        return {\n            user,\n            commercial\n        };\n    });\n}\n// Fonction pour créer un utilisateur administrateur\nasync function createAdminUser({ email, username, password, lastname, firstname, telephone }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN\n            }\n        });\n        // Créer le profil admin associé\n        const admin = await tx.admin.create({\n            data: {\n                userId: user.id\n            }\n        });\n        return {\n            user,\n            admin\n        };\n    });\n}\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nasync function findUserByUsername(username) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            username\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n// Fonction pour trouver un utilisateur par son email\nasync function findUserByEmail(email) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            email\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/salesReports.ts":
/*!*********************************!*\
  !*** ./src/lib/salesReports.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIncompleteReports: () => (/* binding */ checkIncompleteReports),\n/* harmony export */   createSalesReport: () => (/* binding */ createSalesReport),\n/* harmony export */   deleteSalesReport: () => (/* binding */ deleteSalesReport),\n/* harmony export */   getSalesReportById: () => (/* binding */ getSalesReportById),\n/* harmony export */   getSalesReports: () => (/* binding */ getSalesReports),\n/* harmony export */   updateSalesReport: () => (/* binding */ updateSalesReport),\n/* harmony export */   uploadSalesReportFiles: () => (/* binding */ uploadSalesReportFiles)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _upload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./upload */ \"(rsc)/./src/lib/upload.ts\");\n\n\n\n// Create a new sales report\nasync function createSalesReport(commercialId, data) {\n    const id = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Convert images array to JSON string\n    const imagesJson = data.images ? JSON.stringify(data.images) : null;\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.create({\n        data: {\n            id,\n            commercialId,\n            need: data.need,\n            articleRef: data.articleRef,\n            comment: data.comment,\n            visitDate: data.visitDate,\n            denomination: data.denomination,\n            images: imagesJson,\n            name: data.name,\n            visitPurpose: data.visitPurpose,\n            complaint: data.complaint,\n            city: data.city,\n            videoUrl: data.videoUrl,\n            audioUrl: data.audioUrl,\n            pdfUrl: data.pdfUrl,\n            isCompleted: true\n        },\n        include: {\n            commercial: {\n                include: {\n                    user: true\n                }\n            }\n        }\n    });\n}\n// Get a sales report by ID\nasync function getSalesReportById(id) {\n    const report = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findUnique({\n        where: {\n            id\n        },\n        include: {\n            commercial: {\n                include: {\n                    user: true\n                }\n            }\n        }\n    });\n    if (report && report.images) {\n        try {\n            // Parse the JSON string back to an array\n            const imagesArray = JSON.parse(report.images);\n            return {\n                ...report,\n                images: imagesArray,\n                imagesArray\n            };\n        } catch (error) {\n            console.error('Error parsing images JSON:', error);\n            return {\n                ...report,\n                images: []\n            };\n        }\n    }\n    return {\n        ...report,\n        images: []\n    };\n}\n// Get all sales reports with optional filtering\nasync function getSalesReports(filter, skip = 0, take = 10) {\n    const where = {};\n    // Handle date filtering\n    if (filter?.specificDates && filter.specificDates.length > 0) {\n        // For specific dates, we need to match any of the selected dates\n        const dateConditions = filter.specificDates.map((date)=>{\n            const startOfDay = new Date(date);\n            startOfDay.setHours(0, 0, 0, 0);\n            const endOfDay = new Date(date);\n            endOfDay.setHours(23, 59, 59, 999);\n            return {\n                visitDate: {\n                    gte: startOfDay,\n                    lte: endOfDay\n                }\n            };\n        });\n        where.OR = dateConditions;\n    } else if (filter?.startDate && filter?.endDate) {\n        where.visitDate = {\n            gte: filter.startDate,\n            lte: filter.endDate\n        };\n    } else if (filter?.startDate) {\n        where.visitDate = {\n            gte: filter.startDate\n        };\n    } else if (filter?.endDate) {\n        where.visitDate = {\n            lte: filter.endDate\n        };\n    }\n    if (filter?.commercialId) {\n        where.commercialId = filter.commercialId;\n    }\n    if (filter?.city) {\n        where.city = {\n            contains: filter.city\n        };\n    }\n    const [reports, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findMany({\n            where,\n            include: {\n                commercial: {\n                    include: {\n                        user: {\n                            select: {\n                                firstname: true,\n                                lastname: true,\n                                email: true\n                            }\n                        }\n                    }\n                }\n            },\n            skip,\n            take,\n            orderBy: {\n                submittedAt: 'desc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.count({\n            where\n        })\n    ]);\n    // Process images for each report\n    const processedReports = reports.map((report)=>{\n        if (report.images) {\n            try {\n                const imagesArray = JSON.parse(report.images);\n                return {\n                    ...report,\n                    images: imagesArray,\n                    imagesArray\n                };\n            } catch (error) {\n                console.error('Error parsing images JSON:', error);\n                return {\n                    ...report,\n                    images: []\n                };\n            }\n        }\n        return {\n            ...report,\n            images: []\n        };\n    });\n    return {\n        reports: processedReports,\n        total\n    };\n}\n// Update a sales report\nasync function updateSalesReport(id, data) {\n    // Convert images array to JSON string if provided\n    const updateData = {\n        ...data\n    };\n    if (data.images) {\n        updateData.images = JSON.stringify(data.images);\n    }\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.update({\n        where: {\n            id\n        },\n        data: updateData\n    });\n}\n// Delete a sales report\nasync function deleteSalesReport(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.delete({\n        where: {\n            id\n        }\n    });\n}\n// Check for incomplete reports and send reminders\nasync function checkIncompleteReports() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    // Get all commercials\n    const commercials = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.commercial.findMany({\n        include: {\n            user: true\n        }\n    });\n    const results = [];\n    for (const commercial of commercials){\n        // Check if the commercial has submitted a report today\n        const report = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findFirst({\n            where: {\n                commercialId: commercial.id,\n                submittedAt: {\n                    gte: today\n                },\n                isCompleted: true\n            }\n        });\n        if (!report) {\n            // Commercial hasn't submitted a report today\n            // Check if we need to send a reminder (every 2 hours)\n            const lastReminder = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findFirst({\n                where: {\n                    commercialId: commercial.id,\n                    lastReminder: {\n                        not: null\n                    },\n                    isCompleted: false\n                },\n                orderBy: {\n                    lastReminder: 'desc'\n                }\n            });\n            const twoHoursAgo = new Date();\n            twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);\n            if (!lastReminder || lastReminder.lastReminder && lastReminder.lastReminder < twoHoursAgo) {\n                // Create or update a reminder report\n                const reportId = lastReminder ? lastReminder.id : (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                const reminderReport = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.upsert({\n                    where: {\n                        id: reportId\n                    },\n                    update: {\n                        lastReminder: new Date()\n                    },\n                    create: {\n                        id: reportId,\n                        commercialId: commercial.id,\n                        need: '',\n                        visitDate: today,\n                        denomination: '',\n                        name: '',\n                        visitPurpose: '',\n                        city: '',\n                        lastReminder: new Date(),\n                        isCompleted: false\n                    }\n                });\n                results.push({\n                    commercial,\n                    reminderReport,\n                    needsReminder: true\n                });\n            } else {\n                results.push({\n                    commercial,\n                    needsReminder: false\n                });\n            }\n        } else {\n            results.push({\n                commercial,\n                report,\n                needsReminder: false\n            });\n        }\n    }\n    return results;\n}\n// Upload files for a sales report\nasync function uploadSalesReportFiles(files) {\n    const uploadResults = {};\n    // Upload images\n    if (files.images && files.images.length > 0) {\n        const imagePromises = files.images.map((image)=>(0,_upload__WEBPACK_IMPORTED_MODULE_1__.saveFile)(image, 'reports/images'));\n        uploadResults.imageUrls = await Promise.all(imagePromises);\n    }\n    // Upload video\n    if (files.video) {\n        uploadResults.videoUrl = await (0,_upload__WEBPACK_IMPORTED_MODULE_1__.saveFile)(files.video, 'reports/videos');\n    }\n    // Upload audio\n    if (files.audio) {\n        uploadResults.audioUrl = await (0,_upload__WEBPACK_IMPORTED_MODULE_1__.saveFile)(files.audio, 'reports/audio');\n    }\n    // Upload PDF\n    if (files.pdf) {\n        uploadResults.pdfUrl = await (0,_upload__WEBPACK_IMPORTED_MODULE_1__.saveFile)(files.pdf, 'reports/pdfs');\n    }\n    return uploadResults;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/salesReports.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/upload.ts":
/*!***************************!*\
  !*** ./src/lib/upload.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteFile: () => (/* binding */ deleteFile),\n/* harmony export */   saveBase64Image: () => (/* binding */ saveBase64Image),\n/* harmony export */   saveFile: () => (/* binding */ saveFile)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n// Security configuration\nconst SECURITY_CONFIG = {\n    maxFileSize: 25 * 1024 * 1024,\n    allowedMimeTypes: [\n        // Images\n        'image/jpeg',\n        'image/jpg',\n        'image/png',\n        'image/gif',\n        'image/webp',\n        'image/svg+xml',\n        // Documents\n        'application/pdf',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'application/vnd.ms-excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-powerpoint',\n        'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n        // Text files\n        'text/plain',\n        'text/csv',\n        'text/xml',\n        'application/json',\n        // Archives (limited)\n        'application/zip'\n    ],\n    dangerousExtensions: [\n        'exe',\n        'bat',\n        'cmd',\n        'com',\n        'pif',\n        'scr',\n        'vbs',\n        'js',\n        'jar',\n        'php',\n        'asp',\n        'jsp',\n        'sh'\n    ],\n    suspiciousPatterns: [\n        Buffer.from('MZ'),\n        Buffer.from('#!/')\n    ]\n};\n// Security validation functions\nconst validateFileSize = (file)=>{\n    if (file.size > SECURITY_CONFIG.maxFileSize) {\n        throw new Error(`File too large. Maximum size is ${SECURITY_CONFIG.maxFileSize / (1024 * 1024)}MB`);\n    }\n};\nconst validateFileType = (file)=>{\n    if (!SECURITY_CONFIG.allowedMimeTypes.includes(file.type)) {\n        throw new Error('File type not allowed. Allowed types: images, PDF, Office documents, text files');\n    }\n};\nconst validateFileExtension = (fileName)=>{\n    const extension = path__WEBPACK_IMPORTED_MODULE_1___default().extname(fileName).toLowerCase().substring(1);\n    if (SECURITY_CONFIG.dangerousExtensions.includes(extension)) {\n        throw new Error('File extension not allowed for security reasons');\n    }\n};\nconst scanForMalware = (buffer)=>{\n    for (const pattern of SECURITY_CONFIG.suspiciousPatterns){\n        if (buffer.indexOf(pattern) === 0) {\n            throw new Error('File contains suspicious content and cannot be uploaded');\n        }\n    }\n};\n// Ensure upload directory exists\nconst createUploadDir = (dir)=>{\n    const uploadDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'public', dir);\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(uploadDir)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(uploadDir, {\n            recursive: true\n        });\n    }\n    return uploadDir;\n};\n// Save a file to the public directory with security validation\nconst saveFile = async (file, directory = 'uploads')=>{\n    // Security validations\n    validateFileSize(file);\n    validateFileType(file);\n    validateFileExtension(file.name);\n    const uploadDir = createUploadDir(directory);\n    // Convert file to buffer for malware scanning\n    const buffer = Buffer.from(await file.arrayBuffer());\n    scanForMalware(buffer);\n    // Generate a unique filename\n    const fileExtension = path__WEBPACK_IMPORTED_MODULE_1___default().extname(file.name);\n    const fileName = `${(0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()}${fileExtension}`;\n    const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadDir, fileName);\n    // Write file to disk\n    fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, buffer);\n    // Return the public URL\n    return `/${directory}/${fileName}`;\n};\n// Save a base64 image to the public directory\nconst saveBase64Image = (base64Data, directory = 'uploads')=>{\n    // Create directory if it doesn't exist\n    const uploadDir = createUploadDir(directory);\n    // Extract the file extension from the base64 data\n    const matches = base64Data.match(/^data:image\\/([a-zA-Z]+);base64,/);\n    if (!matches || matches.length !== 2) {\n        throw new Error('Invalid base64 image format');\n    }\n    const fileExtension = matches[1];\n    const base64Image = base64Data.replace(/^data:image\\/[a-zA-Z]+;base64,/, '');\n    // Generate a unique filename\n    const fileName = `${(0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()}.${fileExtension}`;\n    const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadDir, fileName);\n    // Write file to disk\n    fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, base64Image, 'base64');\n    // Return the public URL\n    return `/${directory}/${fileName}`;\n};\n// Delete a file from the public directory\nconst deleteFile = (fileUrl)=>{\n    try {\n        // Extract the file path from the URL\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'public', fileUrl);\n        // Check if file exists\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(filePath)) {\n            // Delete the file\n            fs__WEBPACK_IMPORTED_MODULE_0___default().unlinkSync(filePath);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error('Error deleting file:', error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/upload.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/uuid","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsales-reports%2Froute&page=%2Fapi%2Fsales-reports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales-reports%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();