{"version": 3, "file": "completions.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/chat/completions.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,oBAAoB,EAAE,kCAAkC,EAAE,MAAM,mCAAmC,CAAC;AAC7G,OAAO,EACL,6BAA6B,EAC7B,2CAA2C,EAC5C,MAAM,4CAA4C,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,2CAA2C,CAAC;AAC1E,OAAO,EAAE,8BAA8B,EAAE,MAAM,mCAAmC,CAAC;AACnF,OAAO,EAAE,uCAAuC,EAAE,MAAM,4CAA4C,CAAC;AACrG,OAAO,EAAE,oBAAoB,EAAE,KAAK,0BAA0B,EAAE,MAAM,mCAAmC,CAAC;AAC1G,OAAO,EACL,cAAc,EACd,sCAAsC,EACtC,qBAAqB,EACrB,6BAA6B,EAC9B,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,8BAA8B,EAA2C,MAAM,qBAAqB,CAAC;AAE9G,OAAO,EACL,6BAA6B,EAC7B,KAAK,2CAA2C,GACjD,MAAM,4CAA4C,CAAC;AACpD,OAAO,EACL,KAAK,gBAAgB,EACrB,KAAK,iBAAiB,EACtB,KAAK,yBAAyB,EAC9B,KAAK,4BAA4B,EACjC,eAAe,EACf,mBAAmB,GACpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,KAAK,8BAA8B,EAAE,MAAM,mCAAmC,CAAC;AACxF,OAAO,EAAE,KAAK,uCAAuC,EAAE,MAAM,4CAA4C,CAAC;AAC1G,OAAO,EAAE,oBAAoB,EAAE,KAAK,0BAA0B,EAAE,MAAM,mCAAmC,CAAC;AAC1G,OAAO,EACL,oBAAoB,EACpB,KAAK,kCAAkC,GACxC,MAAM,mCAAmC,CAAC;AAE3C,MAAM,WAAW,cAAe,SAAQ,6BAA6B,CAAC,QAAQ;IAC5E,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,sBAAuB,SAAQ,6BAA6B;IAC3E,QAAQ,EAAE,cAAc,CAAC;CAC1B;AAED,MAAM,WAAW,2BAA2B,CAAC,OAAO,CAAE,SAAQ,qBAAqB;IACjF,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC;IACvB,UAAU,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;CAC5C;AAED,MAAM,WAAW,YAAY,CAAC,OAAO,CAAE,SAAQ,cAAc,CAAC,MAAM;IAClE,OAAO,EAAE,2BAA2B,CAAC,OAAO,CAAC,CAAC;CAC/C;AAED,MAAM,WAAW,oBAAoB,CAAC,OAAO,CAAE,SAAQ,cAAc;IACnE,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;CACvC;AAED,MAAM,MAAM,yBAAyB,GAAG,sCAAsC,CAAC;AAE/E,qBAAa,WAAY,SAAQ,WAAW;IAC1C,KAAK,CAAC,MAAM,SAAS,yBAAyB,EAAE,OAAO,GAAG,8BAA8B,CAAC,MAAM,CAAC,EAC9F,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAcjD;;OAEG;IACH,YAAY,CAAC,aAAa,SAAS,iBAAiB,EAClD,IAAI,EAAE,kCAAkC,CAAC,aAAa,CAAC,EACvD,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,oBAAoB,CAAC,IAAI,CAAC;IAC7B,YAAY,CAAC,aAAa,SAAS,iBAAiB,EAClD,IAAI,EAAE,2CAA2C,CAAC,aAAa,CAAC,EAChE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,6BAA6B,CAAC,IAAI,CAAC;IAqBtC;;;;;;;;OAQG;IACH,QAAQ,CACN,MAAM,SAAS,8BAA8B,CAAC,GAAG,CAAC,EAClD,OAAO,GAAG,8BAA8B,CAAC,MAAM,CAAC,EAChD,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,oBAAoB,CAAC,OAAO,CAAC;IAEvE,QAAQ,CACN,MAAM,SAAS,uCAAuC,CAAC,GAAG,CAAC,EAC3D,OAAO,GAAG,8BAA8B,CAAC,MAAM,CAAC,EAChD,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,6BAA6B,CAAC,OAAO,CAAC;IAoBhF;;OAEG;IACH,MAAM,CAAC,MAAM,SAAS,0BAA0B,EAAE,OAAO,GAAG,8BAA8B,CAAC,MAAM,CAAC,EAChG,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,oBAAoB,CAAC,OAAO,CAAC;CAGjC"}