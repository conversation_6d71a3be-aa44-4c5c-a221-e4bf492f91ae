generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model admin {
  id           String             @id
  userId       String             @unique
  user         user               @relation(fields: [userId], references: [id], onDelete: Cascade)
  notification notification[]
  quote        quote[]
  chatconversation chatconversation[]
}

model brand {
  id        String    @id
  name      String    @unique
  image     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  product   product[]
}

model category {
  id          String    @id
  name        String
  description String?
  image       String?
  product     product[]
}

model client {
  id               String             @id
  userId           String             @unique
  company_name     String?
  user             user               @relation(fields: [userId], references: [id], onDelete: Cascade)
  commercialclient commercialclient[]
  order            order[]
  quote            quote[]
}

model commercial {
  id               String             @id
  userId           String             @unique
  profile_photo    String?
  user             user               @relation(fields: [userId], references: [id], onDelete: Cascade)
  commercialclient commercialclient[]
  salesreport      salesreport[]
  chatconversation chatconversation[]
}

model commercialclient {
  id           String     @id
  commercialId String
  clientId     String
  assignedAt   DateTime   @default(now())
  client       client     @relation(fields: [clientId], references: [id], onDelete: Cascade)
  commercial   commercial @relation(fields: [commercialId], references: [id], onDelete: Cascade)

  @@unique([commercialId, clientId])
}

model notification {
  id           String            @id
  type         notification_type
  message      String
  isRead       Boolean           @default(false)
  createdAt    DateTime          @default(now())
  adminId      String
  quoteId      String?
  salesReportId String?
  admin        admin             @relation(fields: [adminId], references: [id], onDelete: Cascade)
  quote        quote?            @relation(fields: [quoteId], references: [id])
  salesreport  salesreport?      @relation(fields: [salesReportId], references: [id])
}

model order {
  id          String       @id
  clientId    String
  orderNumber String       @unique
  status      order_status @default(PENDING)
  totalAmount Float
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  quoteId     String?      @unique
  client      client       @relation(fields: [clientId], references: [id], onDelete: Cascade)
  quote       quote?       @relation(fields: [quoteId], references: [id])
  orderitem   orderitem[]
}

model orderitem {
  id        String  @id
  orderId   String
  productId String
  quantity  Int
  unitPrice Float
  order     order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   product @relation(fields: [productId], references: [id])

  @@unique([orderId, productId])
}

model product {
  id              String         @id
  reference       String         @unique
  name            String
  description     String
  characteristics String
  mainImage       String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  categoryId      String?
  brandId         String?
  orderitem       orderitem[]
  brand           brand?         @relation(fields: [brandId], references: [id])
  category        category?      @relation(fields: [categoryId], references: [id])
  productimage    productimage[]
  quoteitem       quoteitem[]
}

model productimage {
  id        String   @id
  url       String
  alt       String?
  order     Int      @default(0)
  productId String
  createdAt DateTime @default(now())
  product   product  @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model quote {
  id               String         @id
  quoteNumber      String         @unique
  clientId         String
  status           quote_status   @default(DRAFT)
  totalAmount      Float          @default(0)
  validUntil       DateTime?
  notes            String?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  createdByAdminId String?
  pdfUrl           String?
  notification     notification[]
  order            order?
  client           client         @relation(fields: [clientId], references: [id], onDelete: Cascade)
  admin            admin?         @relation(fields: [createdByAdminId], references: [id])
  quoteitem        quoteitem[]
}

model quoteitem {
  id        String  @id
  quoteId   String
  productId String
  quantity  Int
  unitPrice Float   @default(0)
  product   product @relation(fields: [productId], references: [id])
  quote     quote   @relation(fields: [quoteId], references: [id], onDelete: Cascade)

  @@unique([quoteId, productId])
}

model user {
  id         String      @id
  email      String      @unique
  username   String      @unique
  password   String
  lastname   String
  firstname  String
  telephone  String?
  role       user_role
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  admin      admin?
  client     client?
  commercial commercial?
}

enum notification_type {
  QUOTE_REQUESTED
  QUOTE_APPROVED
  QUOTE_REJECTED
  ORDER_PLACED
  ORDER_SHIPPED
  REPORT_REMINDER
  REPORT_SUBMITTED
}

enum order_status {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum quote_status {
  DRAFT
  PENDING
  APPROVED
  REJECTED
  EXPIRED
  CONVERTED
}

enum user_role {
  CLIENT
  COMMERCIAL
  ADMIN
}

model salesreport {
  id              String         @id
  commercialId    String
  need            String
  articleRef      String?
  comment         String?
  visitDate       DateTime
  denomination    String
  images          String? // JSON array of image URLs
  name            String
  visitPurpose    String
  complaint       String?
  city            String
  videoUrl        String?
  audioUrl        String?
  pdfUrl          String?
  submittedAt     DateTime       @default(now())
  lastReminder    DateTime?
  isCompleted     Boolean        @default(false)
  commercial      commercial     @relation(fields: [commercialId], references: [id], onDelete: Cascade)
  notification    notification[]
}

model chatconversation {
  id           String        @id @default(cuid())
  adminId      String
  commercialId String
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  lastMessage  String?
  lastMessageAt DateTime?
  isActive     Boolean       @default(true)
  admin        admin         @relation(fields: [adminId], references: [id], onDelete: Cascade)
  commercial   commercial    @relation(fields: [commercialId], references: [id], onDelete: Cascade)
  messages     chatmessage[]
  files        chatfile[]

  @@unique([adminId, commercialId])
}

model chatmessage {
  id             String           @id @default(cuid())
  conversationId String
  senderId       String
  senderType     chat_sender_type
  content        String
  messageType    chat_message_type @default(TEXT)
  fileUrl        String?
  fileName       String?
  isRead         Boolean          @default(false)
  createdAt      DateTime         @default(now())
  conversation   chatconversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
}

enum chat_sender_type {
  ADMIN
  COMMERCIAL
}

enum chat_message_type {
  TEXT
  FILE
  IMAGE
}

model chatfile {
  id             String           @id @default(cuid())
  fileName       String
  fileUrl        String
  fileSize       Int
  fileType       String
  uploadedById   String
  conversationId String
  createdAt      DateTime         @default(now())
  conversation   chatconversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
}
