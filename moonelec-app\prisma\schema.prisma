generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model admin {
  id           String             @id
  userId       String             @unique(map: "Admin_userId_key")
  user         user               @relation(fields: [userId], references: [id], onDelete: Cascade, map: "Admin_userId_fkey")
  notification notification[]
  quote        quote[]
  chatconversation chatconversation[]
}

model brand {
  id        String    @id
  name      String    @unique(map: "Brand_name_key")
  image     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime
  product   product[]
}

model category {
  id          String    @id
  name        String
  description String?   @db.Text
  image       String?
  product     product[]
}

model client {
  id               String             @id
  userId           String             @unique(map: "Client_userId_key")
  company_name     String?
  user             user               @relation(fields: [userId], references: [id], onDelete: Cascade, map: "Client_userId_fkey")
  commercialclient commercialclient[]
  order            order[]
  quote            quote[]
}

model commercial {
  id               String             @id
  userId           String             @unique(map: "Commercial_userId_key")
  profile_photo    String?
  user             user               @relation(fields: [userId], references: [id], onDelete: Cascade, map: "Commercial_userId_fkey")
  commercialclient commercialclient[]
  salesreport      salesreport[]
  chatconversation chatconversation[]
}

model commercialclient {
  id           String     @id
  commercialId String
  clientId     String
  assignedAt   DateTime   @default(now())
  client       client     @relation(fields: [clientId], references: [id], onDelete: Cascade, map: "CommercialClient_clientId_fkey")
  commercial   commercial @relation(fields: [commercialId], references: [id], onDelete: Cascade, map: "CommercialClient_commercialId_fkey")

  @@unique([commercialId, clientId], map: "CommercialClient_commercialId_clientId_key")
  @@index([clientId], map: "CommercialClient_clientId_fkey")
}

model notification {
  id           String            @id
  type         notification_type
  message      String
  isRead       Boolean           @default(false)
  createdAt    DateTime          @default(now())
  adminId      String
  quoteId      String?
  salesReportId String?
  admin        admin             @relation(fields: [adminId], references: [id], onDelete: Cascade, map: "Notification_adminId_fkey")
  quote        quote?            @relation(fields: [quoteId], references: [id], map: "Notification_quoteId_fkey")
  salesreport  salesreport?      @relation(fields: [salesReportId], references: [id], map: "Notification_salesReportId_fkey")

  @@index([adminId], map: "Notification_adminId_fkey")
  @@index([quoteId], map: "Notification_quoteId_fkey")
  @@index([salesReportId], map: "Notification_salesReportId_fkey")
}

model order {
  id          String       @id
  clientId    String
  orderNumber String       @unique(map: "Order_orderNumber_key")
  status      order_status @default(PENDING)
  totalAmount Float
  createdAt   DateTime     @default(now())
  updatedAt   DateTime
  quoteId     String?      @unique(map: "Order_quoteId_key")
  client      client       @relation(fields: [clientId], references: [id], onDelete: Cascade, map: "Order_clientId_fkey")
  quote       quote?       @relation(fields: [quoteId], references: [id], map: "Order_quoteId_fkey")
  orderitem   orderitem[]

  @@index([clientId], map: "Order_clientId_fkey")
}

model orderitem {
  id        String  @id
  orderId   String
  productId String
  quantity  Int
  unitPrice Float
  order     order   @relation(fields: [orderId], references: [id], onDelete: Cascade, map: "OrderItem_orderId_fkey")
  product   product @relation(fields: [productId], references: [id], map: "OrderItem_productId_fkey")

  @@unique([orderId, productId], map: "OrderItem_orderId_productId_key")
  @@index([productId], map: "OrderItem_productId_fkey")
}

model product {
  id              String         @id
  reference       String         @unique(map: "Product_reference_key")
  name            String
  description     String         @db.Text
  characteristics String         @db.LongText
  mainImage       String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime
  categoryId      String?
  brandId         String?
  orderitem       orderitem[]
  brand           brand?         @relation(fields: [brandId], references: [id], map: "Product_brandId_fkey")
  category        category?      @relation(fields: [categoryId], references: [id], map: "Product_categoryId_fkey")
  productimage    productimage[]
  quoteitem       quoteitem[]

  @@index([brandId], map: "Product_brandId_fkey")
  @@index([categoryId], map: "Product_categoryId_fkey")
}

model productimage {
  id        String   @id
  url       String
  alt       String?
  order     Int      @default(0)
  productId String
  createdAt DateTime @default(now())
  product   product  @relation(fields: [productId], references: [id], onDelete: Cascade, map: "ProductImage_productId_fkey")

  @@index([productId], map: "ProductImage_productId_fkey")
}

model quote {
  id               String         @id
  quoteNumber      String         @unique(map: "Quote_quoteNumber_key")
  clientId         String
  status           quote_status   @default(DRAFT)
  totalAmount      Float          @default(0)
  validUntil       DateTime?
  notes            String?        @db.Text
  createdAt        DateTime       @default(now())
  updatedAt        DateTime
  createdByAdminId String?
  pdfUrl           String?
  notification     notification[]
  order            order?
  client           client         @relation(fields: [clientId], references: [id], onDelete: Cascade, map: "Quote_clientId_fkey")
  admin            admin?         @relation(fields: [createdByAdminId], references: [id], map: "Quote_createdByAdminId_fkey")
  quoteitem        quoteitem[]

  @@index([clientId], map: "Quote_clientId_fkey")
  @@index([createdByAdminId], map: "Quote_createdByAdminId_fkey")
}

model quoteitem {
  id        String  @id
  quoteId   String
  productId String
  quantity  Int
  unitPrice Float   @default(0)
  product   product @relation(fields: [productId], references: [id], map: "QuoteItem_productId_fkey")
  quote     quote   @relation(fields: [quoteId], references: [id], onDelete: Cascade, map: "QuoteItem_quoteId_fkey")

  @@unique([quoteId, productId], map: "QuoteItem_quoteId_productId_key")
  @@index([productId], map: "QuoteItem_productId_fkey")
}

model user {
  id         String      @id
  email      String      @unique(map: "User_email_key")
  username   String      @unique(map: "User_username_key")
  password   String
  lastname   String
  firstname  String
  telephone  String?
  role       user_role
  createdAt  DateTime    @default(now())
  updatedAt  DateTime
  admin      admin?
  client     client?
  commercial commercial?
}

enum notification_type {
  QUOTE_REQUESTED
  QUOTE_APPROVED
  QUOTE_REJECTED
  ORDER_PLACED
  ORDER_SHIPPED
  REPORT_REMINDER
  REPORT_SUBMITTED
}

enum order_status {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum quote_status {
  DRAFT
  PENDING
  APPROVED
  REJECTED
  EXPIRED
  CONVERTED
}

enum user_role {
  CLIENT
  COMMERCIAL
  ADMIN
}

model salesreport {
  id              String         @id
  commercialId    String
  need            String         @db.Text
  articleRef      String?
  comment         String?        @db.Text
  visitDate       DateTime
  denomination    String
  images          String?        @db.Text // JSON array of image URLs
  name            String
  visitPurpose    String         @db.Text
  complaint       String?        @db.Text
  city            String
  videoUrl        String?
  audioUrl        String?
  pdfUrl          String?
  submittedAt     DateTime       @default(now())
  lastReminder    DateTime?
  isCompleted     Boolean        @default(false)
  commercial      commercial     @relation(fields: [commercialId], references: [id], onDelete: Cascade)
  notification    notification[]

  @@index([commercialId], map: "SalesReport_commercialId_fkey")
  @@index([visitDate], map: "SalesReport_visitDate_idx")
  @@index([submittedAt], map: "SalesReport_submittedAt_idx")
}

model chatconversation {
  id           String        @id @default(cuid())
  adminId      String
  commercialId String
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  lastMessage  String?       @db.Text
  lastMessageAt DateTime?
  isActive     Boolean       @default(true)
  admin        admin         @relation(fields: [adminId], references: [id], onDelete: Cascade)
  commercial   commercial    @relation(fields: [commercialId], references: [id], onDelete: Cascade)
  messages     chatmessage[]
  files        chatfile[]

  @@unique([adminId, commercialId])
  @@index([adminId])
  @@index([commercialId])
  @@index([lastMessageAt])
}

model chatmessage {
  id             String           @id @default(cuid())
  conversationId String
  senderId       String
  senderType     chat_sender_type
  content        String           @db.Text
  messageType    chat_message_type @default(TEXT)
  fileUrl        String?
  fileName       String?
  isRead         Boolean          @default(false)
  createdAt      DateTime         @default(now())
  conversation   chatconversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([senderId])
  @@index([createdAt])
}

enum chat_sender_type {
  ADMIN
  COMMERCIAL
}

enum chat_message_type {
  TEXT
  FILE
  IMAGE
}

model chatfile {
  id             String           @id @default(cuid())
  fileName       String
  fileUrl        String
  fileSize       Int
  fileType       String
  uploadedById   String
  conversationId String
  createdAt      DateTime         @default(now())
  conversation   chatconversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([uploadedById])
  @@index([createdAt])
}
