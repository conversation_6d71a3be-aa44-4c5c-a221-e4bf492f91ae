import { NextRequest, NextResponse } from 'next/server';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { z } from 'zod';

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
};

// Create rate limiter
export const createRateLimit = (options?: Partial<typeof rateLimitConfig>) => {
  return rateLimit({
    ...rateLimitConfig,
    ...options,
  });
};

// Strict rate limiting for auth endpoints
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs for auth
  message: 'Too many authentication attempts, please try again later.',
});

// File upload rate limiting
export const uploadRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 uploads per minute
  message: 'Too many file uploads, please try again later.',
});

// Input validation schemas
export const schemas = {
  email: z.string().email().max(255),
  password: z.string().min(8).max(128),
  name: z.string().min(1).max(100).regex(/^[a-zA-ZÀ-ÿ\s'-]+$/),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/),
  text: z.string().max(1000),
  id: z.string().uuid(),
  reference: z.string().min(1).max(50).regex(/^[a-zA-Z0-9-_]+$/),
  url: z.string().url().max(500),
};

// XSS protection
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .trim();
};

// SQL injection protection
export const sanitizeForDatabase = (input: string): string => {
  return input
    .replace(/['";\\]/g, '') // Remove dangerous SQL characters
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove SQL block comments
    .replace(/\*\//g, '')
    .trim();
};

// CSRF token generation and validation
export const generateCSRFToken = (): string => {
  return require('crypto').randomBytes(32).toString('hex');
};

export const validateCSRFToken = (token: string, sessionToken: string): boolean => {
  return token === sessionToken && token.length === 64;
};

// Security headers middleware
export const securityHeaders = (request: NextRequest) => {
  const response = NextResponse.next();
  
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: blob:",
    "font-src 'self'",
    "connect-src 'self'",
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', csp);
  
  return response;
};

// Input validation middleware
export const validateInput = (schema: z.ZodSchema, data: any) => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`);
    }
    throw error;
  }
};

// File upload security validation
export const validateFileUpload = (file: File) => {
  const maxSize = 25 * 1024 * 1024; // 25MB
  const allowedTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv'
  ];
  
  const dangerousExtensions = [
    'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'jsp'
  ];
  
  // Check file size
  if (file.size > maxSize) {
    throw new Error('File too large. Maximum size is 25MB');
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    throw new Error('File type not allowed');
  }
  
  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (dangerousExtensions.includes(extension || '')) {
    throw new Error('File extension not allowed for security reasons');
  }
  
  return true;
};

// Authentication token validation
export const validateAuthToken = (token: string): boolean => {
  if (!token || token.length < 10) {
    return false;
  }
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /[<>]/g, // HTML tags
    /javascript:/gi, // JavaScript protocol
    /data:/gi, // Data protocol
    /vbscript:/gi, // VBScript protocol
  ];
  
  return !suspiciousPatterns.some(pattern => pattern.test(token));
};

// IP address validation and blocking
const blockedIPs = new Set<string>();
const suspiciousActivity = new Map<string, number>();

export const checkIPSecurity = (ip: string): boolean => {
  // Check if IP is blocked
  if (blockedIPs.has(ip)) {
    return false;
  }
  
  // Track suspicious activity
  const attempts = suspiciousActivity.get(ip) || 0;
  if (attempts > 50) { // Block after 50 suspicious attempts
    blockedIPs.add(ip);
    return false;
  }
  
  return true;
};

export const reportSuspiciousActivity = (ip: string) => {
  const current = suspiciousActivity.get(ip) || 0;
  suspiciousActivity.set(ip, current + 1);
};

// Password security validation
export const validatePasswordStrength = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
};

// Session security
export const generateSecureSessionId = (): string => {
  return require('crypto').randomBytes(32).toString('hex');
};

export const validateSessionSecurity = (sessionData: any): boolean => {
  // Check session expiration
  if (sessionData.expiresAt && new Date() > new Date(sessionData.expiresAt)) {
    return false;
  }
  
  // Check session integrity
  if (!sessionData.userId || !sessionData.createdAt) {
    return false;
  }
  
  return true;
};

// Error handling that doesn't leak sensitive information
export const createSecureError = (message: string, statusCode: number = 400) => {
  // Don't expose internal errors in production
  const isProduction = process.env.NODE_ENV === 'production';
  const safeMessage = isProduction ? 'An error occurred' : message;
  
  return NextResponse.json(
    { error: safeMessage },
    { status: statusCode }
  );
};
