"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/ModernFooter.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ModernFooter.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowUp,FaEnvelope,FaFacebook,FaHeart,FaInstagram,FaLinkedin,FaMapMarkerAlt,FaPhone,FaTwitter!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ModernFooter() {\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    const footerLinks = {\n        company: [\n            {\n                name: 'À Propos',\n                href: '/about'\n            },\n            {\n                name: 'Notre Histoire',\n                href: '/about#history'\n            },\n            {\n                name: 'Équipe',\n                href: '/about#team'\n            },\n            {\n                name: 'Carrières',\n                href: '/careers'\n            },\n            {\n                name: 'Actualités',\n                href: '/news'\n            }\n        ],\n        products: [\n            {\n                name: 'Éclairage LED',\n                href: '/products?category=eclairage'\n            },\n            {\n                name: 'Prises & Interrupteurs',\n                href: '/products?category=prises'\n            },\n            {\n                name: 'Protection Électrique',\n                href: '/products?category=protection'\n            },\n            {\n                name: 'Câbles',\n                href: '/products?category=cables'\n            },\n            {\n                name: 'Domotique',\n                href: '/products?category=domotique'\n            }\n        ],\n        services: [\n            {\n                name: 'Installation',\n                href: '/services/installation'\n            },\n            {\n                name: 'Maintenance',\n                href: '/services/maintenance'\n            },\n            {\n                name: 'Conseil Technique',\n                href: '/services/consulting'\n            },\n            {\n                name: 'Formation',\n                href: '/services/training'\n            },\n            {\n                name: 'Support 24/7',\n                href: '/support'\n            }\n        ],\n        support: [\n            {\n                name: 'Centre d\\'Aide',\n                href: '/help'\n            },\n            {\n                name: 'Contact',\n                href: '/contact'\n            },\n            {\n                name: 'Garanties',\n                href: '/warranty'\n            },\n            {\n                name: 'Retours',\n                href: '/returns'\n            },\n            {\n                name: 'FAQ',\n                href: '/faq'\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: 'Facebook',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaFacebook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 55,\n                columnNumber: 31\n            }, this),\n            href: 'https://facebook.com/moonelec',\n            color: 'hover:text-blue-600'\n        },\n        {\n            name: 'Twitter',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaTwitter, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 56,\n                columnNumber: 30\n            }, this),\n            href: 'https://twitter.com/moonelec',\n            color: 'hover:text-blue-400'\n        },\n        {\n            name: 'Instagram',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaInstagram, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 57,\n                columnNumber: 32\n            }, this),\n            href: 'https://instagram.com/moonelec',\n            color: 'hover:text-pink-600'\n        },\n        {\n            name: 'LinkedIn',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaLinkedin, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 58,\n                columnNumber: 31\n            }, this),\n            href: 'https://linkedin.com/company/moonelec',\n            color: 'hover:text-blue-700'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-charcoal text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-moonelec-red rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-2xl\",\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold font-heading\",\n                                                    children: \"Moonelec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 leading-relaxed mb-6\",\n                                            children: \"Votre partenaire de confiance pour tous vos besoins \\xe9lectriques au Maroc. Nous offrons des solutions innovantes et de qualit\\xe9 sup\\xe9rieure depuis plus de 15 ans.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaPhone, {\n                                                            className: \"text-moonelec-red\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"+212 5 22 XX XX XX\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaEnvelope, {\n                                                            className: \"text-moonelec-red\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaMapMarkerAlt, {\n                                                            className: \"text-moonelec-red\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"Casablanca, Maroc\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Entreprise\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.company.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-moonelec-red transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Produits\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.products.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-electric-blue transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.services.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-electric-blue transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6 text-white\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.support.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-moonelec-red transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.5\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-12 pt-8 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"Restez Inform\\xe9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6\",\n                                    children: \"Abonnez-vous \\xe0 notre newsletter pour recevoir les derni\\xe8res actualit\\xe9s et offres sp\\xe9ciales.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Votre adresse email\",\n                                            className: \"flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-moonelec-red focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn btn-primary px-6 py-3 whitespace-nowrap\",\n                                            children: \"S'abonner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        \"\\xa9 2024 Moonelec. Tous droits r\\xe9serv\\xe9s. Fait avec\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaHeart, {\n                                            className: \"text-moonelec-red mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"au Maroc.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                        href: social.href,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        whileHover: {\n                                            scale: 1.2\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        className: \"text-gray-400 \".concat(social.color, \" transition-colors duration-300 text-xl\"),\n                                        \"aria-label\": social.name,\n                                        children: social.icon\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Confidentialit\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cookies\",\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"Cookies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                onClick: scrollToTop,\n                className: \"fixed bottom-8 right-8 p-3 bg-moonelec-red hover:bg-moonelec-red-dark text-white rounded-full shadow-lg transition-all duration-300 z-50\",\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                initial: {\n                    opacity: 0,\n                    y: 100\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowUp_FaEnvelope_FaFacebook_FaHeart_FaInstagram_FaLinkedin_FaMapMarkerAlt_FaPhone_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaArrowUp, {\n                    className: \"text-lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernFooter.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = ModernFooter;\nvar _c;\n$RefreshReg$(_c, \"ModernFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/ModernFooter.tsx\n"));

/***/ })

});