import { createAdminUser, findUserByUsername } from '../src/lib/auth';

async function main() {
  try {
    console.log('Vérification de l\'existence de l\'administrateur par défaut...');
    
    // Vérifier si l'administrateur existe déjà
    const existingAdmin = await findUserByUsername('hicham.ezzamzami');
    
    if (existingAdmin) {
      console.log('L\'administrateur existe déjà.');
      return;
    }
    
    // Créer l'administrateur par défaut
    console.log('Création de l\'administrateur par défaut...');
    await createAdminUser({
      email: '<EMAIL>',
      username: 'hicham.ezzamzami',
      password: '123456789',
      lastname: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      firstname: 'Hicha<PERSON>',
      telephone: '+212600000000',
    });
    
    console.log('Administrateur créé avec succès!');
  } catch (error) {
    console.error('Erreur lors de la création de l\'administrateur:', error);
  } finally {
    process.exit(0);
  }
}

main();
