{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useFileUpload.ts"], "sourcesContent": ["import { useState } from 'react';\n\ninterface UploadOptions {\n  directory?: string;\n  onSuccess?: (url: string) => void;\n  onError?: (error: Error) => void;\n}\n\nexport function useFileUpload() {\n  const [isUploading, setIsUploading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [error, setError] = useState<Error | null>(null);\n\n  const uploadFile = async (file: File, options: UploadOptions = {}) => {\n    const { directory = 'uploads', onSuccess, onError } = options;\n    \n    setIsUploading(true);\n    setProgress(0);\n    setError(null);\n    \n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('directory', directory);\n      \n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setProgress((prev) => {\n          const newProgress = prev + Math.random() * 10;\n          return newProgress > 90 ? 90 : newProgress;\n        });\n      }, 200);\n      \n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n      \n      clearInterval(progressInterval);\n      \n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to upload file');\n      }\n      \n      const data = await response.json();\n      setProgress(100);\n      \n      if (onSuccess) {\n        onSuccess(data.url);\n      }\n      \n      return data.url;\n    } catch (err: any) {\n      setError(err);\n      \n      if (onError) {\n        onError(err);\n      }\n      \n      throw err;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const uploadBase64 = async (base64Data: string, options: UploadOptions = {}) => {\n    const { directory = 'uploads', onSuccess, onError } = options;\n    \n    setIsUploading(true);\n    setProgress(0);\n    setError(null);\n    \n    try {\n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setProgress((prev) => {\n          const newProgress = prev + Math.random() * 10;\n          return newProgress > 90 ? 90 : newProgress;\n        });\n      }, 200);\n      \n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          base64: base64Data,\n          directory,\n        }),\n      });\n      \n      clearInterval(progressInterval);\n      \n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to upload file');\n      }\n      \n      const data = await response.json();\n      setProgress(100);\n      \n      if (onSuccess) {\n        onSuccess(data.url);\n      }\n      \n      return data.url;\n    } catch (err: any) {\n      setError(err);\n      \n      if (onError) {\n        onError(err);\n      }\n      \n      throw err;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return {\n    uploadFile,\n    uploadBase64,\n    isUploading,\n    progress,\n    error,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,aAAa,OAAO,MAAY,UAAyB,CAAC,CAAC;QAC/D,MAAM,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEtD,eAAe;QACf,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,aAAa;YAE7B,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,YAAY,CAAC;oBACX,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK;oBAC3C,OAAO,cAAc,KAAK,KAAK;gBACjC;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,cAAc;YAEd,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;YAEZ,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;YACpB;YAEA,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS;YAET,IAAI,SAAS;gBACX,QAAQ;YACV;YAEA,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,OAAO,YAAoB,UAAyB,CAAC,CAAC;QACzE,MAAM,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEtD,eAAe;QACf,YAAY;QACZ,SAAS;QAET,IAAI;YACF,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,YAAY,CAAC;oBACX,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK;oBAC3C,OAAO,cAAc,KAAK,KAAK;gBACjC;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;gBACF;YACF;YAEA,cAAc;YAEd,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;YAEZ,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;YACpB;YAEA,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS;YAET,IAAI,SAAS;gBACX,QAAQ;YACV;YAEA,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { FaUpload, FaImage, FaTrash, FaSpinner } from 'react-icons/fa';\nimport { useFileUpload } from '@/hooks/useFileUpload';\n\ninterface ImageUploadProps {\n  initialImage?: string;\n  onImageChange: (imageUrl: string | null) => void;\n  directory?: string;\n  className?: string;\n  aspectRatio?: 'square' | '16/9' | '4/3' | 'auto';\n  maxSizeMB?: number;\n  label?: string;\n  required?: boolean;\n}\n\nexport default function ImageUpload({\n  initialImage,\n  onImageChange,\n  directory = 'uploads',\n  className = '',\n  aspectRatio = 'square',\n  maxSizeMB = 5,\n  label = 'Image',\n  required = false,\n}: ImageUploadProps) {\n  const [imageUrl, setImageUrl] = useState<string | null>(initialImage || null);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { uploadFile, isUploading, progress } = useFileUpload();\n\n  const aspectRatioClass = {\n    square: 'aspect-square',\n    '16/9': 'aspect-video',\n    '4/3': 'aspect-[4/3]',\n    'auto': '',\n  }[aspectRatio];\n\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n\n    const file = files[0];\n    \n    // Check file size\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSizeMB) {\n      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);\n      return;\n    }\n    \n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image');\n      return;\n    }\n    \n    setError(null);\n    \n    try {\n      const url = await uploadFile(file, {\n        directory,\n        onSuccess: (url) => {\n          setImageUrl(url);\n          onImageChange(url);\n        },\n        onError: (err) => {\n          setError(err.message);\n        },\n      });\n    } catch (err) {\n      // Error is handled by the hook\n    }\n  };\n\n  const handleRemoveImage = () => {\n    setImageUrl(null);\n    onImageChange(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    \n    const files = e.dataTransfer.files;\n    if (!files || files.length === 0) return;\n    \n    const file = files[0];\n    \n    // Check file size\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSizeMB) {\n      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);\n      return;\n    }\n    \n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image');\n      return;\n    }\n    \n    setError(null);\n    \n    try {\n      const url = await uploadFile(file, {\n        directory,\n        onSuccess: (url) => {\n          setImageUrl(url);\n          onImageChange(url);\n        },\n        onError: (err) => {\n          setError(err.message);\n        },\n      });\n    } catch (err) {\n      // Error is handled by the hook\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n  };\n\n  return (\n    <div className={className}>\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n        {label} {required && <span className=\"text-red-500\">*</span>}\n      </label>\n      \n      <div\n        className={`relative border-2 border-dashed rounded-lg ${\n          error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n        } ${aspectRatioClass} overflow-hidden`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n      >\n        {imageUrl ? (\n          <div className=\"relative w-full h-full\">\n            <Image\n              src={imageUrl}\n              alt=\"Uploaded image\"\n              fill\n              style={{ objectFit: 'cover' }}\n              className=\"w-full h-full\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center\">\n              <motion.button\n                type=\"button\"\n                onClick={handleRemoveImage}\n                className=\"p-2 bg-red-600 text-white rounded-full\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <FaTrash />\n              </motion.button>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex flex-col items-center justify-center p-6 h-full\">\n            {isUploading ? (\n              <div className=\"text-center\">\n                <FaSpinner className=\"animate-spin text-3xl text-primary mx-auto mb-2\" />\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Téléchargement en cours... {Math.round(progress)}%\n                </p>\n              </div>\n            ) : (\n              <>\n                <FaImage className=\"text-3xl text-gray-400 mb-2\" />\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 text-center mb-2\">\n                  Glissez-déposez une image ici ou cliquez pour sélectionner\n                </p>\n                <p className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n                  Formats acceptés: JPG, PNG, GIF, WebP (max {maxSizeMB} MB)\n                </p>\n              </>\n            )}\n          </div>\n        )}\n        \n        <input\n          type=\"file\"\n          ref={fileInputRef}\n          onChange={handleFileChange}\n          accept=\"image/*\"\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n          disabled={isUploading}\n        />\n      </div>\n      \n      {error && (\n        <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAmBe,SAAS,YAAY,EAClC,YAAY,EACZ,aAAa,EACb,YAAY,SAAS,EACrB,YAAY,EAAE,EACd,cAAc,QAAQ,EACtB,YAAY,CAAC,EACb,QAAQ,OAAO,EACf,WAAW,KAAK,EACC;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,gBAAgB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAE1D,MAAM,mBAAmB;QACvB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,CAAC,CAAC,YAAY;IAEd,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,WAAW;YAC1B,SAAS,CAAC,0CAA0C,EAAE,UAAU,GAAG,CAAC;YACpE;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,MAAM;gBACjC;gBACA,WAAW,CAAC;oBACV,YAAY;oBACZ,cAAc;gBAChB;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;QACZ,+BAA+B;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,WAAW;YAC1B,SAAS,CAAC,0CAA0C,EAAE,UAAU,GAAG,CAAC;YACpE;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,MAAM;gBACjC;gBACA,WAAW,CAAC;oBACV,YAAY;oBACZ,cAAc;gBAChB;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;QACZ,+BAA+B;QACjC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,WAAW,CAAC,2CAA2C,EACrD,QAAQ,mBAAmB,uCAC5B,CAAC,EAAE,iBAAiB,gBAAgB,CAAC;gBACtC,QAAQ;gBACR,YAAY;;oBAEX,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,OAAO;oCAAE,WAAW;gCAAQ;gCAC5B,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEvB,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;6CAKd,8OAAC;wBAAI,WAAU;kCACZ,4BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;;wCAA2C;wCAC1B,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;iDAIrD;;8CACE,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAA4D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;;wCAAuD;wCACtB;wCAAU;;;;;;;;;;;;;;kCAOhE,8OAAC;wBACC,MAAK;wBACL,KAAK;wBACL,UAAU;wBACV,QAAO;wBACP,WAAU;wBACV,UAAU;;;;;;;;;;;;YAIb,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/MultiImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { FaUpload, FaImage, FaTrash, Fa<PERSON><PERSON><PERSON>, Fa<PERSON>rrowUp, FaArrowDown } from 'react-icons/fa';\nimport { useFileUpload } from '@/hooks/useFileUpload';\n\ninterface ImageItem {\n  url: string;\n  alt?: string;\n  order?: number;\n}\n\ninterface MultiImageUploadProps {\n  initialImages?: ImageItem[];\n  onImagesChange: (images: ImageItem[]) => void;\n  directory?: string;\n  className?: string;\n  maxImages?: number;\n  maxSizeMB?: number;\n  label?: string;\n  required?: boolean;\n}\n\nexport default function MultiImageUpload({\n  initialImages = [],\n  onImagesChange,\n  directory = 'uploads',\n  className = '',\n  maxImages = 10,\n  maxSizeMB = 5,\n  label = 'Images',\n  required = false,\n}: MultiImageUploadProps) {\n  const [images, setImages] = useState<ImageItem[]>(initialImages);\n  const [error, setError] = useState<string | null>(null);\n  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { uploadFile, isUploading, progress } = useFileUpload();\n\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n\n    // Check if adding these files would exceed the maximum\n    if (images.length + files.length > maxImages) {\n      setError(`Vous ne pouvez pas ajouter plus de ${maxImages} images`);\n      return;\n    }\n\n    setError(null);\n\n    // Process each file\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      \n      // Check file size\n      const fileSizeMB = file.size / (1024 * 1024);\n      if (fileSizeMB > maxSizeMB) {\n        setError(`La taille du fichier ${file.name} dépasse la limite de ${maxSizeMB} MB`);\n        continue;\n      }\n      \n      // Check file type\n      if (!file.type.startsWith('image/')) {\n        setError(`Le fichier ${file.name} n'est pas une image`);\n        continue;\n      }\n      \n      try {\n        setUploadingIndex(images.length + i);\n        \n        const url = await uploadFile(file, {\n          directory,\n          onSuccess: (url) => {\n            setImages(prev => {\n              const newImages = [...prev, { url, alt: file.name, order: prev.length }];\n              onImagesChange(newImages);\n              return newImages;\n            });\n          },\n          onError: (err) => {\n            setError(`Erreur lors du téléchargement de ${file.name}: ${err.message}`);\n          },\n        });\n      } catch (err) {\n        // Error is handled by the hook\n      }\n    }\n    \n    setUploadingIndex(null);\n    \n    // Reset the file input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleRemoveImage = (index: number) => {\n    setImages(prev => {\n      const newImages = prev.filter((_, i) => i !== index).map((img, i) => ({\n        ...img,\n        order: i,\n      }));\n      onImagesChange(newImages);\n      return newImages;\n    });\n  };\n\n  const handleMoveImage = (index: number, direction: 'up' | 'down') => {\n    if (\n      (direction === 'up' && index === 0) ||\n      (direction === 'down' && index === images.length - 1)\n    ) {\n      return;\n    }\n\n    setImages(prev => {\n      const newImages = [...prev];\n      const targetIndex = direction === 'up' ? index - 1 : index + 1;\n      \n      // Swap the images\n      [newImages[index], newImages[targetIndex]] = [newImages[targetIndex], newImages[index]];\n      \n      // Update the order\n      newImages.forEach((img, i) => {\n        img.order = i;\n      });\n      \n      onImagesChange(newImages);\n      return newImages;\n    });\n  };\n\n  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    \n    const files = e.dataTransfer.files;\n    if (!files || files.length === 0) return;\n    \n    // Check if adding these files would exceed the maximum\n    if (images.length + files.length > maxImages) {\n      setError(`Vous ne pouvez pas ajouter plus de ${maxImages} images`);\n      return;\n    }\n\n    setError(null);\n\n    // Process each file\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      \n      // Check file size\n      const fileSizeMB = file.size / (1024 * 1024);\n      if (fileSizeMB > maxSizeMB) {\n        setError(`La taille du fichier ${file.name} dépasse la limite de ${maxSizeMB} MB`);\n        continue;\n      }\n      \n      // Check file type\n      if (!file.type.startsWith('image/')) {\n        setError(`Le fichier ${file.name} n'est pas une image`);\n        continue;\n      }\n      \n      try {\n        setUploadingIndex(images.length + i);\n        \n        const url = await uploadFile(file, {\n          directory,\n          onSuccess: (url) => {\n            setImages(prev => {\n              const newImages = [...prev, { url, alt: file.name, order: prev.length }];\n              onImagesChange(newImages);\n              return newImages;\n            });\n          },\n          onError: (err) => {\n            setError(`Erreur lors du téléchargement de ${file.name}: ${err.message}`);\n          },\n        });\n      } catch (err) {\n        // Error is handled by the hook\n      }\n    }\n    \n    setUploadingIndex(null);\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n  };\n\n  return (\n    <div className={className}>\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n        {label} {required && <span className=\"text-red-500\">*</span>}\n      </label>\n      \n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-4\">\n        {images.map((image, index) => (\n          <div key={index} className=\"relative aspect-square rounded-lg overflow-hidden border border-gray-300 dark:border-gray-600\">\n            <Image\n              src={image.url}\n              alt={image.alt || `Image ${index + 1}`}\n              fill\n              style={{ objectFit: 'cover' }}\n              className=\"w-full h-full\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2\">\n              <motion.button\n                type=\"button\"\n                onClick={() => handleMoveImage(index, 'up')}\n                className=\"p-2 bg-blue-600 text-white rounded-full disabled:opacity-50\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                disabled={index === 0}\n              >\n                <FaArrowUp />\n              </motion.button>\n              <motion.button\n                type=\"button\"\n                onClick={() => handleMoveImage(index, 'down')}\n                className=\"p-2 bg-blue-600 text-white rounded-full disabled:opacity-50\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                disabled={index === images.length - 1}\n              >\n                <FaArrowDown />\n              </motion.button>\n              <motion.button\n                type=\"button\"\n                onClick={() => handleRemoveImage(index)}\n                className=\"p-2 bg-red-600 text-white rounded-full\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <FaTrash />\n              </motion.button>\n            </div>\n          </div>\n        ))}\n        \n        {/* Upload placeholder */}\n        {images.length < maxImages && (\n          <div\n            className={`relative aspect-square border-2 border-dashed rounded-lg ${\n              error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n            } overflow-hidden`}\n            onDrop={handleDrop}\n            onDragOver={handleDragOver}\n          >\n            <div className=\"flex flex-col items-center justify-center p-4 h-full\">\n              {uploadingIndex !== null ? (\n                <div className=\"text-center\">\n                  <FaSpinner className=\"animate-spin text-2xl text-primary mx-auto mb-2\" />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Téléchargement... {Math.round(progress)}%\n                  </p>\n                </div>\n              ) : (\n                <>\n                  <FaImage className=\"text-2xl text-gray-400 mb-2\" />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 text-center\">\n                    Ajouter une image\n                  </p>\n                </>\n              )}\n            </div>\n            \n            <input\n              type=\"file\"\n              ref={fileInputRef}\n              onChange={handleFileChange}\n              accept=\"image/*\"\n              multiple\n              className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n              disabled={uploadingIndex !== null}\n            />\n          </div>\n        )}\n      </div>\n      \n      <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n        {images.length} / {maxImages} images (max {maxSizeMB} MB par image)\n      </div>\n      \n      {error && (\n        <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAyBe,SAAS,iBAAiB,EACvC,gBAAgB,EAAE,EAClB,cAAc,EACd,YAAY,SAAS,EACrB,YAAY,EAAE,EACd,YAAY,EAAE,EACd,YAAY,CAAC,EACb,QAAQ,QAAQ,EAChB,WAAW,KAAK,EACM;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAE1D,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,uDAAuD;QACvD,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM,GAAG,WAAW;YAC5C,SAAS,CAAC,mCAAmC,EAAE,UAAU,OAAO,CAAC;YACjE;QACF;QAEA,SAAS;QAET,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,kBAAkB;YAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;YAC3C,IAAI,aAAa,WAAW;gBAC1B,SAAS,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,sBAAsB,EAAE,UAAU,GAAG,CAAC;gBACjF;YACF;YAEA,kBAAkB;YAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,SAAS,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC;gBACtD;YACF;YAEA,IAAI;gBACF,kBAAkB,OAAO,MAAM,GAAG;gBAElC,MAAM,MAAM,MAAM,WAAW,MAAM;oBACjC;oBACA,WAAW,CAAC;wBACV,UAAU,CAAA;4BACR,MAAM,YAAY;mCAAI;gCAAM;oCAAE;oCAAK,KAAK,KAAK,IAAI;oCAAE,OAAO,KAAK,MAAM;gCAAC;6BAAE;4BACxE,eAAe;4BACf,OAAO;wBACT;oBACF;oBACA,SAAS,CAAC;wBACR,SAAS,CAAC,iCAAiC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE;oBAC1E;gBACF;YACF,EAAE,OAAO,KAAK;YACZ,+BAA+B;YACjC;QACF;QAEA,kBAAkB;QAElB,uBAAuB;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,CAAA;YACR,MAAM,YAAY,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,IAAM,CAAC;oBACpE,GAAG,GAAG;oBACN,OAAO;gBACT,CAAC;YACD,eAAe;YACf,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC,OAAe;QACtC,IACE,AAAC,cAAc,QAAQ,UAAU,KAChC,cAAc,UAAU,UAAU,OAAO,MAAM,GAAG,GACnD;YACA;QACF;QAEA,UAAU,CAAA;YACR,MAAM,YAAY;mBAAI;aAAK;YAC3B,MAAM,cAAc,cAAc,OAAO,QAAQ,IAAI,QAAQ;YAE7D,kBAAkB;YAClB,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,YAAY,CAAC,GAAG;gBAAC,SAAS,CAAC,YAAY;gBAAE,SAAS,CAAC,MAAM;aAAC;YAEvF,mBAAmB;YACnB,UAAU,OAAO,CAAC,CAAC,KAAK;gBACtB,IAAI,KAAK,GAAG;YACd;YAEA,eAAe;YACf,OAAO;QACT;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,uDAAuD;QACvD,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM,GAAG,WAAW;YAC5C,SAAS,CAAC,mCAAmC,EAAE,UAAU,OAAO,CAAC;YACjE;QACF;QAEA,SAAS;QAET,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,kBAAkB;YAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;YAC3C,IAAI,aAAa,WAAW;gBAC1B,SAAS,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,sBAAsB,EAAE,UAAU,GAAG,CAAC;gBACjF;YACF;YAEA,kBAAkB;YAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,SAAS,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC;gBACtD;YACF;YAEA,IAAI;gBACF,kBAAkB,OAAO,MAAM,GAAG;gBAElC,MAAM,MAAM,MAAM,WAAW,MAAM;oBACjC;oBACA,WAAW,CAAC;wBACV,UAAU,CAAA;4BACR,MAAM,YAAY;mCAAI;gCAAM;oCAAE;oCAAK,KAAK,KAAK,IAAI;oCAAE,OAAO,KAAK,MAAM;gCAAC;6BAAE;4BACxE,eAAe;4BACf,OAAO;wBACT;oBACF;oBACA,SAAS,CAAC;wBACR,SAAS,CAAC,iCAAiC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE;oBAC1E;gBACF;YACF,EAAE,OAAO,KAAK;YACZ,+BAA+B;YACjC;QACF;QAEA,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,GAAG;oCACd,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG;oCACtC,IAAI;oCACJ,OAAO;wCAAE,WAAW;oCAAQ;oCAC5B,WAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS,IAAM,gBAAgB,OAAO;4CACtC,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,UAAU,UAAU;sDAEpB,cAAA,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;sDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS,IAAM,gBAAgB,OAAO;4CACtC,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,UAAU,UAAU,OAAO,MAAM,GAAG;sDAEpC,cAAA,8OAAC,8IAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;sDAEvB,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;2BApCJ;;;;;oBA2CX,OAAO,MAAM,GAAG,2BACf,8OAAC;wBACC,WAAW,CAAC,yDAAyD,EACnE,QAAQ,mBAAmB,uCAC5B,gBAAgB,CAAC;wBAClB,QAAQ;wBACR,YAAY;;0CAEZ,8OAAC;gCAAI,WAAU;0CACZ,mBAAmB,qBAClB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAE,WAAU;;gDAA2C;gDACnC,KAAK,KAAK,CAAC;gDAAU;;;;;;;;;;;;yDAI5C;;sDACE,8OAAC,8IAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;;;;;;;;0CAO1E,8OAAC;gCACC,MAAK;gCACL,KAAK;gCACL,UAAU;gCACV,QAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,MAAM;oBAAC;oBAAI;oBAAU;oBAAc;oBAAU;;;;;;;YAGtD,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/PdfExtractor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface ExtractedProductData {\n  productName: string;\n  reference: string | string[];\n  description: string;\n  characteristics: Record<string, string>;\n}\n\ninterface ExtractionResult {\n  success: boolean;\n  data: ExtractedProductData;\n  metadata: {\n    fileName: string;\n    fileSize: number;\n    textLength: number;\n    extractedAt: string;\n  };\n}\n\ninterface PdfExtractorProps {\n  onDataExtracted?: (data: ExtractedProductData) => void;\n  onClose?: () => void;\n}\n\nexport default function PdfExtractor({ onDataExtracted, onClose }: PdfExtractorProps) {\n  const [file, setFile] = useState<File | null>(null);\n  const [extractedData, setExtractedData] = useState<ExtractedProductData | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = (selectedFile: File) => {\n    setError(null);\n    setExtractedData(null);\n\n    // Validation du fichier\n    if (selectedFile.type !== 'application/pdf') {\n      setError('Veuillez sélectionner un fichier PDF');\n      return;\n    }\n\n    if (selectedFile.size > 10 * 1024 * 1024) {\n      setError('Le fichier doit faire moins de 10MB');\n      return;\n    }\n\n    setFile(selectedFile);\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0];\n    if (selectedFile) {\n      handleFileSelect(selectedFile);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n\n    const droppedFile = e.dataTransfer.files[0];\n    if (droppedFile) {\n      handleFileSelect(droppedFile);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  const extractData = async () => {\n    if (!file) {\n      setError('Veuillez sélectionner un fichier PDF');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await fetch('/api/extract-pdf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Erreur lors de l\\'extraction');\n      }\n\n      const extractionResult = result as ExtractionResult;\n      setExtractedData(extractionResult.data);\n\n      // Callback pour utiliser les données extraites\n      if (onDataExtracted) {\n        onDataExtracted(extractionResult.data);\n      }\n\n    } catch (err) {\n      console.error('Extraction error:', err);\n      setError(err instanceof Error ? err.message : 'Erreur lors de l\\'extraction');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCharacteristics = (characteristics: Record<string, string>) => {\n    return Object.entries(characteristics)\n      .map(([key, value]) => `${key}: ${value}`)\n      .join('\\n');\n  };\n\n  const useExtractedData = () => {\n    if (extractedData && onDataExtracted) {\n      onDataExtracted(extractedData);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">🤖 Extraction PDF avec IA</h2>\n          <p className=\"text-gray-600 mt-1\">\n            Extrayez automatiquement les données produit depuis une fiche technique PDF\n          </p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n          >\n            ✕\n          </button>\n        )}\n      </div>\n\n      {/* Zone de drop */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n          dragOver\n            ? 'border-blue-500 bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onClick={() => fileInputRef.current?.click()}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept=\"application/pdf\"\n          onChange={handleFileChange}\n          className=\"hidden\"\n        />\n\n        <div className=\"space-y-4\">\n          <div className=\"text-6xl\">📄</div>\n          <div>\n            <p className=\"text-lg font-medium text-gray-900\">\n              {file ? file.name : 'Glissez votre PDF ici ou cliquez pour sélectionner'}\n            </p>\n            <p className=\"text-sm text-gray-500 mt-2\">\n              Formats supportés: PDF • Taille max: 10MB\n            </p>\n          </div>\n\n          {file && (\n            <div className=\"text-sm text-green-600\">\n              ✅ Fichier sélectionné: {(file.size / 1024 / 1024).toFixed(2)} MB\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Bouton d'extraction */}\n      <div className=\"mt-6 flex justify-center\">\n        <motion.button\n          onClick={extractData}\n          disabled={!file || loading}\n          className={`px-8 py-3 rounded-lg font-medium transition-all ${\n            !file || loading\n              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl'\n          }`}\n          whileHover={!file || loading ? {} : { scale: 1.05 }}\n          whileTap={!file || loading ? {} : { scale: 0.95 }}\n        >\n          {loading ? (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full\"></div>\n              <span>Extraction en cours...</span>\n            </div>\n          ) : (\n            '🚀 Extraire les données'\n          )}\n        </motion.button>\n      </div>\n\n      {/* Erreur */}\n      <AnimatePresence>\n        {error && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-red-500\">❌</span>\n              <span className=\"text-red-700 font-medium\">Erreur</span>\n            </div>\n            <p className=\"text-red-600 mt-1\">{error}</p>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Données extraites */}\n      <AnimatePresence>\n        {extractedData && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"mt-6 p-6 bg-green-50 border border-green-200 rounded-lg\"\n          >\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-bold text-green-800\">\n                ✅ Données extraites avec succès !\n              </h3>\n              {onDataExtracted && (\n                <button\n                  onClick={useExtractedData}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  Utiliser ces données\n                </button>\n              )}\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Informations générales</h4>\n                <div className=\"space-y-2 text-sm\">\n                  <div><strong>Nom:</strong> {extractedData.productName}</div>\n                  <div><strong>Référence:</strong> {\n                    Array.isArray(extractedData.reference)\n                      ? extractedData.reference.join(', ')\n                      : extractedData.reference\n                  }</div>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Caractéristiques</h4>\n                <div className=\"text-sm space-y-1\">\n                  {Object.entries(extractedData.characteristics).map(([key, value]) => (\n                    <div key={key}>\n                      <strong>{key}:</strong> {value}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"mt-4\">\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Description</h4>\n              <p className=\"text-sm text-gray-700 bg-white p-3 rounded border\">\n                {extractedData.description}\n              </p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AA4Be,SAAS,aAAa,EAAE,eAAe,EAAE,OAAO,EAAqB;IAClF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,iBAAiB;QAEjB,wBAAwB;QACxB,IAAI,aAAa,IAAI,KAAK,mBAAmB;YAC3C,SAAS;YACT;QACF;QAEA,IAAI,aAAa,IAAI,GAAG,KAAK,OAAO,MAAM;YACxC,SAAS;YACT;QACF;QAEA,QAAQ;IACV;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,YAAY;QAEZ,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC3C,IAAI,aAAa;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,MAAM,mBAAmB;YACzB,iBAAiB,iBAAiB,IAAI;YAEtC,+CAA+C;YAC/C,IAAI,iBAAiB;gBACnB,gBAAgB,iBAAiB,IAAI;YACvC;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qBAAqB;YACnC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,OAAO,OAAO,CAAC,iBACnB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,EAAE,EAAE,OAAO,EACxC,IAAI,CAAC;IACV;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB,iBAAiB;YACpC,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAInC,yBACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,WACI,+BACA,yCACJ;gBACF,QAAQ;gBACR,YAAY;gBACZ,aAAa;gBACb,SAAS,IAAM,aAAa,OAAO,EAAE;;kCAErC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAW;;;;;;0CAC1B,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDACV,OAAO,KAAK,IAAI,GAAG;;;;;;kDAEtB,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;4BAK3C,sBACC,8OAAC;gCAAI,WAAU;;oCAAyB;oCACd,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;0BAOrE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;oBACT,UAAU,CAAC,QAAQ;oBACnB,WAAW,CAAC,gDAAgD,EAC1D,CAAC,QAAQ,UACL,iDACA,sEACJ;oBACF,YAAY,CAAC,QAAQ,UAAU,CAAC,IAAI;wBAAE,OAAO;oBAAK;oBAClD,UAAU,CAAC,QAAQ,UAAU,CAAC,IAAI;wBAAE,OAAO;oBAAK;8BAE/C,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;+BAGR;;;;;;;;;;;0BAMN,8OAAC,yLAAA,CAAA,kBAAe;0BACb,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,8OAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;0BAMxC,8OAAC,yLAAA,CAAA,kBAAe;0BACb,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;gCAGhD,iCACC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEAAI,8OAAC;sEAAO;;;;;;wDAAa;wDAAE,cAAc,WAAW;;;;;;;8DACrD,8OAAC;;sEAAI,8OAAC;sEAAO;;;;;;wDAAmB;wDAC9B,MAAM,OAAO,CAAC,cAAc,SAAS,IACjC,cAAc,SAAS,CAAC,IAAI,CAAC,QAC7B,cAAc,SAAS;;;;;;;;;;;;;;;;;;;8CAKjC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,cAAc,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC9D,8OAAC;;sEACC,8OAAC;;gEAAQ;gEAAI;;;;;;;wDAAU;wDAAE;;mDADjB;;;;;;;;;;;;;;;;;;;;;;sCAQlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CACV,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/products/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { FaSave, FaPlus, FaTrash, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport Link from 'next/link';\nimport ImageUpload from '@/components/ui/ImageUpload';\nimport MultiImageUpload from '@/components/ui/MultiImageUpload';\nimport PdfExtractor from '@/components/admin/PdfExtractor';\n\ninterface Category {\n  id: string;\n  name: string;\n}\n\nexport default function NewProductPage() {\n  const router = useRouter();\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [brands, setBrands] = useState<{ id: string; name: string }[]>([]);\n  const [error, setError] = useState<string | null>(null);\n  const [showPdfExtractor, setShowPdfExtractor] = useState(false);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    reference: '',\n    name: '',\n    description: '',\n    mainImage: '',\n    categoryId: '',\n    brandId: '',\n  });\n\n  const [characteristics, setCharacteristics] = useState<{ key: string; value: string }[]>([\n    { key: 'Modèle', value: '' },\n    { key: 'Puissance', value: '' },\n    { key: 'Durée de vie', value: '' },\n    { key: 'Lumens', value: '' }\n  ]);\n\n  const [productImages, setProductImages] = useState<{ url: string; alt?: string; order?: number }[]>([]);\n  const [formErrors, setFormErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch categories\n        const categoriesResponse = await fetch('/api/categories');\n        if (!categoriesResponse.ok) {\n          throw new Error('Failed to fetch categories');\n        }\n        const categoriesData = await categoriesResponse.json();\n        setCategories(categoriesData.categories);\n\n        // Fetch brands\n        const brandsResponse = await fetch('/api/brands');\n        if (!brandsResponse.ok) {\n          throw new Error('Failed to fetch brands');\n        }\n        const brandsData = await brandsResponse.json();\n        setBrands(brandsData.brands);\n      } catch (err: any) {\n        console.error('Error fetching data:', err);\n        setError(err.message || 'An error occurred while fetching data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error for this field\n    if (formErrors[name]) {\n      setFormErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[name];\n        return newErrors;\n      });\n    }\n  };\n\n  const handleCharacteristicChange = (index: number, field: 'key' | 'value', value: string) => {\n    setCharacteristics(prev => {\n      const newCharacteristics = [...prev];\n      newCharacteristics[index][field] = value;\n      return newCharacteristics;\n    });\n  };\n\n  const addCharacteristic = () => {\n    setCharacteristics(prev => [...prev, { key: '', value: '' }]);\n  };\n\n  const removeCharacteristic = (index: number) => {\n    setCharacteristics(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const handleMainImageChange = (imageUrl: string | null) => {\n    setFormData(prev => ({ ...prev, mainImage: imageUrl || '' }));\n  };\n\n  const handleProductImagesChange = (images: { url: string; alt?: string; order?: number }[]) => {\n    setProductImages(images);\n  };\n\n  // Fonction pour traiter les données extraites du PDF\n  const handlePdfDataExtracted = (extractedData: any) => {\n    // Pré-remplir le formulaire avec les données extraites\n    setFormData(prev => ({\n      ...prev,\n      reference: Array.isArray(extractedData.reference)\n        ? extractedData.reference[0]\n        : extractedData.reference || prev.reference,\n      name: extractedData.productName || prev.name,\n      description: extractedData.description || prev.description,\n    }));\n\n    // Convertir les caractéristiques extraites\n    if (extractedData.characteristics && Object.keys(extractedData.characteristics).length > 0) {\n      const newCharacteristics = Object.entries(extractedData.characteristics).map(([key, value]) => ({\n        key,\n        value: String(value)\n      }));\n      setCharacteristics(newCharacteristics);\n    }\n\n    // Note: Les champs category et brand ne sont plus extraits automatiquement\n    // L'utilisateur devra les sélectionner manuellement\n\n    // Fermer l'extracteur\n    setShowPdfExtractor(false);\n\n    // Afficher un message de succès\n    setError(null);\n  };\n\n  const validateForm = () => {\n    const errors: Record<string, string> = {};\n\n    if (!formData.reference.trim()) {\n      errors.reference = 'La référence est requise';\n    }\n\n    if (!formData.name.trim()) {\n      errors.name = 'Le nom est requis';\n    }\n\n    if (!formData.description.trim()) {\n      errors.description = 'La description est requise';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    setError(null);\n\n    try {\n      // Convert characteristics array to object\n      const characteristicsObject = characteristics.reduce((obj, { key, value }) => {\n        if (key.trim()) {\n          obj[key.trim()] = value.trim();\n        }\n        return obj;\n      }, {} as Record<string, string>);\n\n      // Create the product\n      const response = await fetch('/api/products', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          reference: formData.reference,\n          name: formData.name,\n          description: formData.description,\n          characteristics: characteristicsObject,\n          mainImage: formData.mainImage || null,\n          categoryId: formData.categoryId || null,\n          brandId: formData.brandId || null,\n          images: productImages,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to create product');\n      }\n\n      const product = await response.json();\n\n      // Redirect to the product detail page\n      router.push(`/admin/products/${product.id}`);\n    } catch (err: any) {\n      console.error('Error creating product:', err);\n      setError(err.message || 'Une erreur est survenue lors de la création du produit');\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n            Ajouter un nouveau produit\n          </h1>\n          <div className=\"flex space-x-3\">\n            <motion.button\n              onClick={() => setShowPdfExtractor(true)}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center\"\n            >\n              🤖 Extraire depuis PDF\n            </motion.button>\n            <Link href=\"/admin/products\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\"\n              >\n                Retour à la liste\n              </motion.button>\n            </Link>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {isLoading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <FaSpinner className=\"animate-spin text-4xl text-primary\" />\n          </div>\n        ) : (\n          <>\n            {/* Error Message */}\n            {error && (\n              <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center\">\n                <FaExclamationTriangle className=\"mr-2\" />\n                <span>{error}</span>\n              </div>\n            )}\n\n            {/* Form */}\n            <form onSubmit={handleSubmit} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                {/* Reference */}\n                <div>\n                  <label htmlFor=\"reference\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Référence <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"reference\"\n                    name=\"reference\"\n                    value={formData.reference}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      formErrors.reference ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  />\n                  {formErrors.reference && (\n                    <p className=\"mt-1 text-sm text-red-500\">{formErrors.reference}</p>\n                  )}\n                </div>\n\n                {/* Name */}\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Nom <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      formErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  />\n                  {formErrors.name && (\n                    <p className=\"mt-1 text-sm text-red-500\">{formErrors.name}</p>\n                  )}\n                </div>\n\n                {/* Category */}\n                <div>\n                  <label htmlFor=\"categoryId\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Catégorie\n                  </label>\n                  <select\n                    id=\"categoryId\"\n                    name=\"categoryId\"\n                    value={formData.categoryId}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                  >\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    {categories.map((category) => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Brand */}\n                <div>\n                  <label htmlFor=\"brandId\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Marque\n                  </label>\n                  <select\n                    id=\"brandId\"\n                    name=\"brandId\"\n                    value={formData.brandId}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                  >\n                    <option value=\"\">Sélectionner une marque</option>\n                    {brands.map((brand) => (\n                      <option key={brand.id} value={brand.id}>\n                        {brand.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Main Image */}\n                <div>\n                  <ImageUpload\n                    initialImage={formData.mainImage}\n                    onImageChange={handleMainImageChange}\n                    directory=\"products\"\n                    label=\"Image principale\"\n                    aspectRatio=\"square\"\n                  />\n                </div>\n              </div>\n\n              {/* Description */}\n              <div className=\"mb-6\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Description <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"description\"\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleInputChange}\n                  rows={5}\n                  className={`w-full px-4 py-2 border ${\n                    formErrors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                ></textarea>\n                {formErrors.description && (\n                  <p className=\"mt-1 text-sm text-red-500\">{formErrors.description}</p>\n                )}\n              </div>\n\n              {/* Characteristics */}\n              <div className=\"mb-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Caractéristiques\n                  </label>\n                  <motion.button\n                    type=\"button\"\n                    onClick={addCharacteristic}\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-1 bg-primary text-white rounded-full\"\n                  >\n                    <FaPlus />\n                  </motion.button>\n                </div>\n\n                {characteristics.map((characteristic, index) => (\n                  <div key={index} className=\"flex items-center gap-2 mb-2\">\n                    <input\n                      type=\"text\"\n                      value={characteristic.key}\n                      onChange={(e) => handleCharacteristicChange(index, 'key', e.target.value)}\n                      placeholder=\"Nom\"\n                      className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                    />\n                    <input\n                      type=\"text\"\n                      value={characteristic.value}\n                      onChange={(e) => handleCharacteristicChange(index, 'value', e.target.value)}\n                      placeholder=\"Valeur\"\n                      className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                    />\n                    <motion.button\n                      type=\"button\"\n                      onClick={() => removeCharacteristic(index)}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-2 text-red-500 hover:text-red-700 dark:hover:text-red-300\"\n                    >\n                      <FaTrash />\n                    </motion.button>\n                  </div>\n                ))}\n              </div>\n\n              {/* Product Images */}\n              <div className=\"mb-6\">\n                <MultiImageUpload\n                  initialImages={productImages}\n                  onImagesChange={handleProductImagesChange}\n                  directory=\"products\"\n                  label=\"Images du produit\"\n                  maxImages={10}\n                />\n              </div>\n\n              {/* Submit Button */}\n              <div className=\"flex justify-end\">\n                <motion.button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className={`px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center ${\n                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {isSubmitting ? (\n                    <>\n                      <FaSpinner className=\"animate-spin mr-2\" />\n                      Enregistrement...\n                    </>\n                  ) : (\n                    <>\n                      <FaSave className=\"mr-2\" />\n                      Enregistrer le produit\n                    </>\n                  )}\n                </motion.button>\n              </div>\n            </form>\n          </>\n        )}\n\n        {/* PDF Extractor Modal */}\n        {showPdfExtractor && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n            <div className=\"max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n              <PdfExtractor\n                onDataExtracted={handlePdfDataExtracted}\n                onClose={() => setShowPdfExtractor(false)}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC,EAAE;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;QACvF;YAAE,KAAK;YAAU,OAAO;QAAG;QAC3B;YAAE,KAAK;YAAa,OAAO;QAAG;QAC9B;YAAE,KAAK;YAAgB,OAAO;QAAG;QACjC;YAAE,KAAK;YAAU,OAAO;QAAG;KAC5B;IAED,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD,EAAE;IACtG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,mBAAmB;gBACnB,MAAM,qBAAqB,MAAM,MAAM;gBACvC,IAAI,CAAC,mBAAmB,EAAE,EAAE;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;gBACpD,cAAc,eAAe,UAAU;gBAEvC,eAAe;gBACf,MAAM,iBAAiB,MAAM,MAAM;gBACnC,IAAI,CAAC,eAAe,EAAE,EAAE;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,aAAa,MAAM,eAAe,IAAI;gBAC5C,UAAU,WAAW,MAAM;YAC7B,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,SAAS,IAAI,OAAO,IAAI;YAC1B,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,6BAA6B;QAC7B,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA;gBACZ,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,KAAK;gBACtB,OAAO;YACT;QACF;IACF;IAEA,MAAM,6BAA6B,CAAC,OAAe,OAAwB;QACzE,mBAAmB,CAAA;YACjB,MAAM,qBAAqB;mBAAI;aAAK;YACpC,kBAAkB,CAAC,MAAM,CAAC,MAAM,GAAG;YACnC,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB;QACxB,mBAAmB,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,KAAK;oBAAI,OAAO;gBAAG;aAAE;IAC9D;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACzD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW,YAAY;YAAG,CAAC;IAC7D;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;IACnB;IAEA,qDAAqD;IACrD,MAAM,yBAAyB,CAAC;QAC9B,uDAAuD;QACvD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,MAAM,OAAO,CAAC,cAAc,SAAS,IAC5C,cAAc,SAAS,CAAC,EAAE,GAC1B,cAAc,SAAS,IAAI,KAAK,SAAS;gBAC7C,MAAM,cAAc,WAAW,IAAI,KAAK,IAAI;gBAC5C,aAAa,cAAc,WAAW,IAAI,KAAK,WAAW;YAC5D,CAAC;QAED,2CAA2C;QAC3C,IAAI,cAAc,eAAe,IAAI,OAAO,IAAI,CAAC,cAAc,eAAe,EAAE,MAAM,GAAG,GAAG;YAC1F,MAAM,qBAAqB,OAAO,OAAO,CAAC,cAAc,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC;oBAC9F;oBACA,OAAO,OAAO;gBAChB,CAAC;YACD,mBAAmB;QACrB;QAEA,2EAA2E;QAC3E,oDAAoD;QAEpD,sBAAsB;QACtB,oBAAoB;QAEpB,gCAAgC;QAChC,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO,SAAS,GAAG;QACrB;QAEA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,OAAO,IAAI,GAAG;QAChB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,OAAO,WAAW,GAAG;QACvB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,0CAA0C;YAC1C,MAAM,wBAAwB,gBAAgB,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;gBACvE,IAAI,IAAI,IAAI,IAAI;oBACd,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,MAAM,IAAI;gBAC9B;gBACA,OAAO;YACT,GAAG,CAAC;YAEJ,qBAAqB;YACrB,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW,SAAS,SAAS;oBAC7B,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;oBACjC,iBAAiB;oBACjB,WAAW,SAAS,SAAS,IAAI;oBACjC,YAAY,SAAS,UAAU,IAAI;oBACnC,SAAS,SAAS,OAAO,IAAI;oBAC7B,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YAEnC,sCAAsC;YACtC,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;QAC7C,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,IAAI,OAAO,IAAI;YACxB,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,wIAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,oBAAoB;oCACnC,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;gBAQN,0BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;yCAGvB;;wBAEG,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,wBAAqB;oCAAC,WAAU;;;;;;8CACjC,8OAAC;8CAAM;;;;;;;;;;;;sCAKX,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;;wDAAkE;sEAC3F,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE3C,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,SAAS,GAAG,mBAAmB,uCAC3C,wHAAwH,CAAC;;;;;;gDAE3H,WAAW,SAAS,kBACnB,8OAAC;oDAAE,WAAU;8DAA6B,WAAW,SAAS;;;;;;;;;;;;sDAKlE,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;;wDAAkE;sEAC5F,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAErC,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAW,CAAC,wBAAwB,EAClC,WAAW,IAAI,GAAG,mBAAmB,uCACtC,wHAAwH,CAAC;;;;;;gDAE3H,WAAW,IAAI,kBACd,8OAAC;oDAAE,WAAU;8DAA6B,WAAW,IAAI;;;;;;;;;;;;sDAK7D,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAkE;;;;;;8DAGxG,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gEAAyB,OAAO,SAAS,EAAE;0EACzC,SAAS,IAAI;+DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;sDAQ9B,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAAkE;;;;;;8DAGrG,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gEAAsB,OAAO,MAAM,EAAE;0EACnC,MAAM,IAAI;+DADA,MAAM,EAAE;;;;;;;;;;;;;;;;;sDAQ3B,8OAAC;sDACC,cAAA,8OAAC,uIAAA,CAAA,UAAW;gDACV,cAAc,SAAS,SAAS;gDAChC,eAAe;gDACf,WAAU;gDACV,OAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;;gDAAkE;8DAC3F,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE7C,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,WAAW;4CAC3B,UAAU;4CACV,MAAM;4CACN,WAAW,CAAC,wBAAwB,EAClC,WAAW,WAAW,GAAG,mBAAmB,uCAC7C,wHAAwH,CAAC;;;;;;wCAE3H,WAAW,WAAW,kBACrB,8OAAC;4CAAE,WAAU;sDAA6B,WAAW,WAAW;;;;;;;;;;;;8CAKpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA6D;;;;;;8DAG9E,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,MAAK;oDACL,SAAS;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;;;;;;;wCAIV,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,sBACpC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDACC,MAAK;wDACL,OAAO,eAAe,GAAG;wDACzB,UAAU,CAAC,IAAM,2BAA2B,OAAO,OAAO,EAAE,MAAM,CAAC,KAAK;wDACxE,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,OAAO,eAAe,KAAK;wDAC3B,UAAU,CAAC,IAAM,2BAA2B,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC1E,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,MAAK;wDACL,SAAS,IAAM,qBAAqB;wDACpC,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;kEAEV,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;+CAtBF;;;;;;;;;;;8CA6Bd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4IAAA,CAAA,UAAgB;wCACf,eAAe;wCACf,gBAAgB;wCAChB,WAAU;wCACV,OAAM;wCACN,WAAW;;;;;;;;;;;8CAKf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,qGAAqG,EAC/G,eAAe,kCAAkC,IACjD;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAEvB,6BACC;;8DACE,8OAAC,8IAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAsB;;yEAI7C;;8DACE,8OAAC,8IAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;gBAWxC,kCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2IAAA,CAAA,UAAY;4BACX,iBAAiB;4BACjB,SAAS,IAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}]}