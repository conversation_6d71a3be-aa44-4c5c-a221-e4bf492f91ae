import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';

// GET /api/chat/conversations - Get all conversations for current user
export async function GET(req: NextRequest) {
  try {
    // Check for mobile authentication first
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    let conversations;

    if (user.role === 'ADMIN') {
      // Admin can see all conversations
      conversations = await prisma.chatconversation.findMany({
        include: {
          admin: {
            include: {
              user: {
                select: {
                  id: true,
                  firstname: true,
                  lastname: true,
                  email: true,
                }
              }
            }
          },
          commercial: {
            include: {
              user: {
                select: {
                  id: true,
                  firstname: true,
                  lastname: true,
                  email: true,
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          }
        },
        orderBy: { lastMessageAt: 'desc' }
      });
    } else {
      // Commercial can only see their conversations
      const commercial = await prisma.commercial.findUnique({
        where: { userId: user.id }
      });

      if (!commercial) {
        return NextResponse.json({ error: 'Commercial profile not found' }, { status: 404 });
      }

      conversations = await prisma.chatconversation.findMany({
        where: { commercialId: commercial.id },
        include: {
          admin: {
            include: {
              user: {
                select: {
                  id: true,
                  firstname: true,
                  lastname: true,
                  email: true,
                }
              }
            }
          },
          commercial: {
            include: {
              user: {
                select: {
                  id: true,
                  firstname: true,
                  lastname: true,
                  email: true,
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          }
        },
        orderBy: { lastMessageAt: 'desc' }
      });
    }

    return NextResponse.json({ conversations });

  } catch (error) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/chat/conversations - Create new conversation
export async function POST(req: NextRequest) {
  try {
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { participantId } = await req.json();

    if (!participantId) {
      return NextResponse.json({ error: 'Participant ID is required' }, { status: 400 });
    }

    let adminId: string;
    let commercialId: string;

    if (user.role === 'ADMIN') {
      const admin = await prisma.admin.findUnique({
        where: { userId: user.id }
      });
      
      if (!admin) {
        return NextResponse.json({ error: 'Admin profile not found' }, { status: 404 });
      }
      
      adminId = admin.id;
      commercialId = participantId;
    } else {
      const commercial = await prisma.commercial.findUnique({
        where: { userId: user.id }
      });
      
      if (!commercial) {
        return NextResponse.json({ error: 'Commercial profile not found' }, { status: 404 });
      }
      
      commercialId = commercial.id;
      adminId = participantId;
    }

    // Check if conversation already exists
    const existingConversation = await prisma.chatconversation.findUnique({
      where: {
        adminId_commercialId: {
          adminId,
          commercialId
        }
      }
    });

    if (existingConversation) {
      return NextResponse.json({ conversation: existingConversation });
    }

    // Create new conversation
    const conversation = await prisma.chatconversation.create({
      data: {
        adminId,
        commercialId,
      },
      include: {
        admin: {
          include: {
            user: {
              select: {
                id: true,
                firstname: true,
                lastname: true,
                email: true,
              }
            }
          }
        },
        commercial: {
          include: {
            user: {
              select: {
                id: true,
                firstname: true,
                lastname: true,
                email: true,
              }
            }
          }
        }
      }
    });

    return NextResponse.json({ conversation }, { status: 201 });

  } catch (error) {
    console.error('Error creating conversation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
