const { PrismaClient } = require('@prisma/client');

// Use MySQL connection
process.env.DATABASE_URL = 'mysql://root@localhost:3306/moonelec_db';

const prisma = new PrismaClient();

async function testMySQLAPIs() {
  try {
    console.log('🔍 Testing MySQL APIs with real data...');

    // Test 1: Get brands
    console.log('\n1. Testing Brands API...');
    const brands = await prisma.brand.findMany({
      orderBy: { name: 'asc' }
    });
    console.log(`✅ Brands found: ${brands.length}`);
    brands.forEach(brand => {
      console.log(`  - ${brand.name} (ID: ${brand.id})`);
    });

    // Test 2: Get categories
    console.log('\n2. Testing Categories API...');
    const categories = await prisma.category.findMany({
      orderBy: { name: 'asc' }
    });
    console.log(`✅ Categories found: ${categories.length}`);
    categories.forEach(category => {
      console.log(`  - ${category.name} (ID: ${category.id})`);
    });

    // Test 3: Get products
    console.log('\n3. Testing Products API...');
    const products = await prisma.product.findMany({
      include: {
        category: true,
        brand: true,
      },
      take: 5, // Limit to first 5
      orderBy: { name: 'asc' }
    });
    console.log(`✅ Products found: ${products.length}`);
    products.forEach(product => {
      console.log(`  - ${product.name} (${product.reference})`);
      console.log(`    Category: ${product.category?.name || 'None'}`);
      console.log(`    Brand: ${product.brand?.name || 'None'}`);
    });

    // Test 4: Get users with profiles
    console.log('\n4. Testing Users with Profiles...');
    const users = await prisma.user.findMany({
      include: {
        admin: true,
        commercial: true,
        client: true,
      },
      orderBy: { role: 'asc' }
    });
    console.log(`✅ Users found: ${users.length}`);
    users.forEach(user => {
      console.log(`  - ${user.firstname} ${user.lastname} (${user.role})`);
      console.log(`    Email: ${user.email}`);
      console.log(`    Username: ${user.username}`);
      if (user.admin) console.log(`    Admin Profile: ${user.admin.id}`);
      if (user.commercial) console.log(`    Commercial Profile: ${user.commercial.id}`);
      if (user.client) console.log(`    Client Profile: ${user.client.id}`);
    });

    // Test 5: Get quotes
    console.log('\n5. Testing Quotes...');
    const quotes = await prisma.quote.findMany({
      include: {
        client: {
          include: {
            user: true
          }
        },
        items: {
          include: {
            product: true
          }
        }
      },
      take: 3
    });
    console.log(`✅ Quotes found: ${quotes.length}`);
    quotes.forEach(quote => {
      console.log(`  - Quote ${quote.quoteNumber}: ${quote.totalAmount}€`);
      console.log(`    Client: ${quote.client?.user?.firstname} ${quote.client?.user?.lastname}`);
      console.log(`    Status: ${quote.status}`);
      console.log(`    Items: ${quote.items?.length || 0}`);
    });

    // Test 6: Get sales reports
    console.log('\n6. Testing Sales Reports...');
    const salesReports = await prisma.salesreport.findMany({
      include: {
        commercial: {
          include: {
            user: true
          }
        }
      },
      take: 3,
      orderBy: { visitDate: 'desc' }
    });
    console.log(`✅ Sales Reports found: ${salesReports.length}`);
    salesReports.forEach(report => {
      console.log(`  - Report ${report.id}`);
      console.log(`    Commercial: ${report.commercial?.user?.firstname} ${report.commercial?.user?.lastname}`);
      console.log(`    Client: ${report.name} (${report.denomination})`);
      console.log(`    Date: ${report.visitDate.toISOString().split('T')[0]}`);
      console.log(`    Completed: ${report.isCompleted ? 'Yes' : 'No'}`);
    });

    console.log('\n🎉 All MySQL APIs tested successfully!');
    console.log('\n📊 SUMMARY:');
    console.log(`  - ${brands.length} Brands`);
    console.log(`  - ${categories.length} Categories`);
    console.log(`  - ${products.length}+ Products`);
    console.log(`  - ${users.length} Users`);
    console.log(`  - ${quotes.length} Quotes`);
    console.log(`  - ${salesReports.length} Sales Reports`);

  } catch (error) {
    console.error('❌ Error testing MySQL APIs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMySQLAPIs();
