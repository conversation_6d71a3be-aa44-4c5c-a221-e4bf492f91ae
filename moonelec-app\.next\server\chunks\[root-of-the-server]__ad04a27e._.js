module.exports = {

"[project]/.next-internal/server/app/api/admin/dashboard/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminUser": (()=>createAdminUser),
    "createClientUser": (()=>createClientUser),
    "createCommercialUser": (()=>createCommercialUser),
    "findUserByEmail": (()=>findUserByEmail),
    "findUserByUsername": (()=>findUserByUsername),
    "hashPassword": (()=>hashPassword),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
async function hashPassword(password) {
    const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(10);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, salt);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
async function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].CLIENT
            }
        });
        // Créer le profil client associé
        const client = await tx.client.create({
            data: {
                userId: user.id,
                company_name
            }
        });
        return {
            user,
            client
        };
    });
}
async function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].COMMERCIAL
            }
        });
        // Créer le profil commercial associé
        const commercial = await tx.commercial.create({
            data: {
                userId: user.id,
                profile_photo
            }
        });
        return {
            user,
            commercial
        };
    });
}
async function createAdminUser({ email, username, password, lastname, firstname, telephone }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].ADMIN
            }
        });
        // Créer le profil admin associé
        const admin = await tx.admin.create({
            data: {
                userId: user.id
            }
        });
        return {
            user,
            admin
        };
    });
}
async function findUserByUsername(username) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            username
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
async function findUserByEmail(email) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            email
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
}}),
"[project]/src/lib/auth-options.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'Credentials',
            credentials: {
                username: {
                    label: "Nom d'utilisateur",
                    type: 'text'
                },
                password: {
                    label: 'Mot de passe',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.username || !credentials?.password) {
                    return null;
                }
                try {
                    // Rechercher l'utilisateur par nom d'utilisateur
                    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findUserByUsername"])(credentials.username);
                    // Vérifier si l'utilisateur existe
                    if (!user) {
                        return null;
                    }
                    // Vérifier le mot de passe
                    const isPasswordValid = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyPassword"])(credentials.password, user.password);
                    if (!isPasswordValid) {
                        return null;
                    }
                    // Retourner les données de l'utilisateur sans le mot de passe
                    return {
                        id: user.id,
                        email: user.email,
                        username: user.username,
                        name: `${user.firstname} ${user.lastname}`,
                        firstname: user.firstname,
                        lastname: user.lastname,
                        role: user.role,
                        clientId: user.client?.id,
                        commercialId: user.commercial?.id,
                        adminId: user.admin?.id
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.username = user.username;
                token.role = user.role;
                token.firstname = user.firstname;
                token.lastname = user.lastname;
                token.clientId = user.clientId;
                token.commercialId = user.commercialId;
                token.adminId = user.adminId;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.id;
                session.user.username = token.username;
                session.user.role = token.role;
                session.user.firstname = token.firstname;
                session.user.lastname = token.lastname;
                session.user.clientId = token.clientId;
                session.user.commercialId = token.commercialId;
                session.user.adminId = token.adminId;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signOut: '/auth/signout',
        error: '/auth/error'
    },
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/lib/realtime-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DataCache": (()=>DataCache),
    "RealTimeDataService": (()=>RealTimeDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
class RealTimeDataService {
    // Calculate real revenue from quotes and orders
    static async calculateRealRevenue(timeframe = 'month') {
        const now = new Date();
        let startDate;
        switch(timeframe){
            case 'day':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                break;
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                break;
        }
        // Calculate revenue from quotes
        const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where: {
                createdAt: {
                    gte: startDate,
                    lte: now
                }
            },
            include: {
                items: true
            }
        });
        const totalRevenue = quotes.reduce((total, quote)=>{
            const quoteTotal = quote.items.reduce((quoteSum, item)=>{
                return quoteSum + item.quantity * item.unitPrice;
            }, 0);
            return total + quoteTotal;
        }, 0);
        return {
            totalRevenue,
            quotesCount: quotes.length,
            averageQuoteValue: quotes.length > 0 ? totalRevenue / quotes.length : 0
        };
    }
    // Get real user activity metrics
    static async getUserActivityMetrics() {
        const now = new Date();
        const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        // Active users in last 30 days (based on quote creation)
        const activeUsers = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
            where: {
                quotes: {
                    some: {
                        createdAt: {
                            gte: last30Days
                        }
                    }
                }
            },
            include: {
                _count: {
                    select: {
                        quotes: true
                    }
                }
            }
        });
        // Total users by role
        const usersByRole = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.groupBy({
            by: [
                'role'
            ],
            _count: {
                id: true
            }
        });
        return {
            activeUsers: activeUsers.length,
            totalUsers: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count(),
            usersByRole: usersByRole.reduce((acc, item)=>{
                acc[item.role] = item._count.id;
                return acc;
            }, {}),
            topActiveUsers: activeUsers.sort((a, b)=>b._count.quotes - a._count.quotes).slice(0, 5).map((user)=>({
                    id: user.id,
                    name: `${user.firstname} ${user.lastname}`,
                    quotesCount: user._count.quotes
                }))
        };
    }
    // Get real product performance data
    static async getProductPerformance() {
        // Products with most quotes
        const productQuotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quoteItem.groupBy({
            by: [
                'productId'
            ],
            _count: {
                id: true
            },
            _sum: {
                quantity: true,
                unitPrice: true
            },
            orderBy: {
                _count: {
                    id: 'desc'
                }
            },
            take: 10
        });
        // Get product details
        const productIds = productQuotes.map((pq)=>pq.productId).filter(Boolean);
        const products = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findMany({
            where: {
                id: {
                    in: productIds
                }
            }
        });
        const productPerformance = productQuotes.map((pq)=>{
            const product = products.find((p)=>p.id === pq.productId);
            return {
                productId: pq.productId,
                productName: product?.name || 'Unknown Product',
                productReference: product?.reference || 'N/A',
                quotesCount: pq._count.id,
                totalQuantity: pq._sum.quantity || 0,
                totalRevenue: (pq._sum.quantity || 0) * (pq._sum.unitPrice || 0)
            };
        });
        return {
            topProducts: productPerformance,
            totalProducts: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.count(),
            totalCategories: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.count()
        };
    }
    // Get real sales trends
    static async getSalesTrends(days = 30) {
        const now = new Date();
        const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
        const dailyQuotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where: {
                createdAt: {
                    gte: startDate,
                    lte: now
                }
            },
            include: {
                items: true
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
        // Group by day
        const dailyData = new Map();
        for(let i = 0; i < days; i++){
            const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
            const dateKey = date.toISOString().split('T')[0];
            dailyData.set(dateKey, {
                quotes: 0,
                revenue: 0
            });
        }
        dailyQuotes.forEach((quote)=>{
            const dateKey = quote.createdAt.toISOString().split('T')[0];
            const existing = dailyData.get(dateKey) || {
                quotes: 0,
                revenue: 0
            };
            const quoteRevenue = quote.items.reduce((sum, item)=>{
                return sum + item.quantity * item.unitPrice;
            }, 0);
            dailyData.set(dateKey, {
                quotes: existing.quotes + 1,
                revenue: existing.revenue + quoteRevenue
            });
        });
        return Array.from(dailyData.entries()).map(([date, data])=>({
                date,
                quotes: data.quotes,
                revenue: data.revenue
            }));
    }
    // Get comprehensive dashboard data
    static async getDashboardData() {
        const [revenueData, userMetrics, productPerformance, salesTrends] = await Promise.all([
            this.calculateRealRevenue('month'),
            this.getUserActivityMetrics(),
            this.getProductPerformance(),
            this.getSalesTrends(30)
        ]);
        // Calculate growth rates
        const lastMonthRevenue = await this.calculateRealRevenue('month');
        const currentMonthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
        const lastMonthStart = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
        const lastMonthEnd = new Date(new Date().getFullYear(), new Date().getMonth(), 0);
        const lastMonthQuotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where: {
                createdAt: {
                    gte: lastMonthStart,
                    lte: lastMonthEnd
                }
            },
            include: {
                items: true
            }
        });
        const lastMonthRevenueTotal = lastMonthQuotes.reduce((total, quote)=>{
            return total + quote.items.reduce((sum, item)=>sum + item.quantity * item.unitPrice, 0);
        }, 0);
        const revenueGrowth = lastMonthRevenueTotal > 0 ? (revenueData.totalRevenue - lastMonthRevenueTotal) / lastMonthRevenueTotal * 100 : 0;
        return {
            revenue: {
                ...revenueData,
                growth: revenueGrowth
            },
            users: userMetrics,
            products: productPerformance,
            trends: salesTrends,
            summary: {
                totalQuotes: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.count(),
                totalProducts: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.count(),
                totalUsers: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count(),
                totalCategories: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.count()
            }
        };
    }
    // Real-time data change notification
    static async notifyDataChange(type, action) {
        // This would integrate with WebSocket or Server-Sent Events for real-time updates
        console.log(`📊 Data change notification: ${type} ${action}`);
        // In a real implementation, you would:
        // 1. Emit WebSocket event to connected clients
        // 2. Update cached dashboard data
        // 3. Trigger real-time UI updates
        return {
            type,
            action,
            timestamp: new Date().toISOString()
        };
    }
}
// Cache management for performance
class DataCache {
    static cache = new Map();
    static set(key, data, ttlMinutes = 5) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: ttlMinutes * 60 * 1000
        });
    }
    static get(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        if (Date.now() - cached.timestamp > cached.ttl) {
            this.cache.delete(key);
            return null;
        }
        return cached.data;
    }
    static clear() {
        this.cache.clear();
    }
}
;
}}),
"[project]/src/middleware/security.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authRateLimit": (()=>authRateLimit),
    "checkIPSecurity": (()=>checkIPSecurity),
    "createRateLimit": (()=>createRateLimit),
    "createSecureError": (()=>createSecureError),
    "generateCSRFToken": (()=>generateCSRFToken),
    "generateSecureSessionId": (()=>generateSecureSessionId),
    "reportSuspiciousActivity": (()=>reportSuspiciousActivity),
    "sanitizeForDatabase": (()=>sanitizeForDatabase),
    "sanitizeInput": (()=>sanitizeInput),
    "schemas": (()=>schemas),
    "securityHeaders": (()=>securityHeaders),
    "uploadRateLimit": (()=>uploadRateLimit),
    "validateAuthToken": (()=>validateAuthToken),
    "validateCSRFToken": (()=>validateCSRFToken),
    "validateFileUpload": (()=>validateFileUpload),
    "validateInput": (()=>validateInput),
    "validatePasswordStrength": (()=>validatePasswordStrength),
    "validateSessionSecurity": (()=>validateSessionSecurity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module 'express-rate-limit'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
(()=>{
    const e = new Error("Cannot find module 'zod'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
;
;
;
// Rate limiting configuration
const rateLimitConfig = {
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
};
const createRateLimit = (options)=>{
    return rateLimit({
        ...rateLimitConfig,
        ...options
    });
};
const authRateLimit = createRateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: 'Too many authentication attempts, please try again later.'
});
const uploadRateLimit = createRateLimit({
    windowMs: 60 * 1000,
    max: 10,
    message: 'Too many file uploads, please try again later.'
});
const schemas = {
    email: z.string().email().max(255),
    password: z.string().min(8).max(128),
    name: z.string().min(1).max(100).regex(/^[a-zA-ZÀ-ÿ\s'-]+$/),
    phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/),
    text: z.string().max(1000),
    id: z.string().uuid(),
    reference: z.string().min(1).max(50).regex(/^[a-zA-Z0-9-_]+$/),
    url: z.string().url().max(500)
};
const sanitizeInput = (input)=>{
    return input.replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .trim();
};
const sanitizeForDatabase = (input)=>{
    return input.replace(/['";\\]/g, '') // Remove dangerous SQL characters
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove SQL block comments
    .replace(/\*\//g, '').trim();
};
const generateCSRFToken = ()=>{
    return __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)").randomBytes(32).toString('hex');
};
const validateCSRFToken = (token, sessionToken)=>{
    return token === sessionToken && token.length === 64;
};
const securityHeaders = (request)=>{
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    // Content Security Policy
    const csp = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: blob:",
        "font-src 'self'",
        "connect-src 'self'",
        "media-src 'self'",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'",
        "upgrade-insecure-requests"
    ].join('; ');
    response.headers.set('Content-Security-Policy', csp);
    return response;
};
const validateInput = (schema, data)=>{
    try {
        return schema.parse(data);
    } catch (error) {
        if (error instanceof z.ZodError) {
            throw new Error(`Validation error: ${error.errors.map((e)=>e.message).join(', ')}`);
        }
        throw error;
    }
};
const validateFileUpload = (file)=>{
    const maxSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv'
    ];
    const dangerousExtensions = [
        'exe',
        'bat',
        'cmd',
        'com',
        'pif',
        'scr',
        'vbs',
        'js',
        'jar',
        'php',
        'asp',
        'jsp'
    ];
    // Check file size
    if (file.size > maxSize) {
        throw new Error('File too large. Maximum size is 25MB');
    }
    // Check file type
    if (!allowedTypes.includes(file.type)) {
        throw new Error('File type not allowed');
    }
    // Check file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (dangerousExtensions.includes(extension || '')) {
        throw new Error('File extension not allowed for security reasons');
    }
    return true;
};
const validateAuthToken = (token)=>{
    if (!token || token.length < 10) {
        return false;
    }
    // Check for suspicious patterns
    const suspiciousPatterns = [
        /[<>]/g,
        /javascript:/gi,
        /data:/gi,
        /vbscript:/gi
    ];
    return !suspiciousPatterns.some((pattern)=>pattern.test(token));
};
// IP address validation and blocking
const blockedIPs = new Set();
const suspiciousActivity = new Map();
const checkIPSecurity = (ip)=>{
    // Check if IP is blocked
    if (blockedIPs.has(ip)) {
        return false;
    }
    // Track suspicious activity
    const attempts = suspiciousActivity.get(ip) || 0;
    if (attempts > 50) {
        blockedIPs.add(ip);
        return false;
    }
    return true;
};
const reportSuspiciousActivity = (ip)=>{
    const current = suspiciousActivity.get(ip) || 0;
    suspiciousActivity.set(ip, current + 1);
};
const validatePasswordStrength = (password)=>{
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
};
const generateSecureSessionId = ()=>{
    return __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)").randomBytes(32).toString('hex');
};
const validateSessionSecurity = (sessionData)=>{
    // Check session expiration
    if (sessionData.expiresAt && new Date() > new Date(sessionData.expiresAt)) {
        return false;
    }
    // Check session integrity
    if (!sessionData.userId || !sessionData.createdAt) {
        return false;
    }
    return true;
};
const createSecureError = (message, statusCode = 400)=>{
    // Don't expose internal errors in production
    const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
    const safeMessage = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : message;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        error: safeMessage
    }, {
        status: statusCode
    });
};
}}),
"[project]/src/app/api/admin/dashboard/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-options.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$realtime$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/realtime-data.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/middleware/security.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function GET(req) {
    try {
        // Security check
        const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkIPSecurity"])(clientIP)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSecureError"])('Access denied', 403);
        }
        // Vérifier l'authentification et les autorisations
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !session.user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Vérifier si l'utilisateur est un administrateur
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: session.user.id
            },
            select: {
                role: true
            }
        });
        if (!user || user.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Forbidden'
            }, {
                status: 403
            });
        }
        // Check cache first for performance
        const cacheKey = 'admin-dashboard-data';
        let dashboardData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$realtime$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DataCache"].get(cacheKey);
        if (!dashboardData) {
            // Get real-time dashboard data
            dashboardData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$realtime$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RealTimeDataService"].getDashboardData();
            // Cache for 2 minutes
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$realtime$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DataCache"].set(cacheKey, dashboardData, 2);
        }
        // Format response to match existing frontend expectations
        const response = {
            totalClients: dashboardData.users.usersByRole.CLIENT || 0,
            totalOrders: dashboardData.summary.totalQuotes,
            totalProducts: dashboardData.summary.totalProducts,
            totalRevenue: Math.round(dashboardData.revenue.totalRevenue),
            totalCommercials: dashboardData.users.usersByRole.COMMERCIAL || 0,
            totalSuppliers: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].brand.count(),
            totalCategories: dashboardData.summary.totalCategories,
            totalBrands: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].brand.count(),
            // Additional real-time data
            revenueGrowth: dashboardData.revenue.growth,
            activeUsers: dashboardData.users.activeUsers,
            averageQuoteValue: Math.round(dashboardData.revenue.averageQuoteValue),
            topProducts: dashboardData.products.topProducts.slice(0, 5),
            salesTrends: dashboardData.trends.slice(-7),
            // Performance metrics
            quotesThisMonth: dashboardData.revenue.quotesCount,
            lastUpdated: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to fetch dashboard data'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ad04a27e._.js.map