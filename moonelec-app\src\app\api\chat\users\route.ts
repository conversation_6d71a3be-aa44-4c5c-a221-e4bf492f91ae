import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';

// GET /api/chat/users - Get available users for chat
export async function GET(req: NextRequest) {
  try {
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    let availableUsers;

    if (user.role === 'ADMIN') {
      // <PERSON><PERSON> can chat with all commercials
      availableUsers = await prisma.commercial.findMany({
        include: {
          user: {
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
              telephone: true,
            }
          }
        }
      });

      // Format the response
      const formattedUsers = availableUsers.map(commercial => ({
        id: commercial.id,
        userId: commercial.user.id,
        name: `${commercial.user.firstname} ${commercial.user.lastname}`,
        email: commercial.user.email,
        telephone: commercial.user.telephone,
        role: 'COMMERCIAL',
        profilePhoto: commercial.profile_photo,
      }));

      return NextResponse.json({ users: formattedUsers });

    } else {
      // Commercial can chat with all admins
      availableUsers = await prisma.admin.findMany({
        include: {
          user: {
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
              telephone: true,
            }
          }
        }
      });

      // Format the response
      const formattedUsers = availableUsers.map(admin => ({
        id: admin.id,
        userId: admin.user.id,
        name: `${admin.user.firstname} ${admin.user.lastname}`,
        email: admin.user.email,
        telephone: admin.user.telephone,
        role: 'ADMIN',
        profilePhoto: null,
      }));

      return NextResponse.json({ users: formattedUsers });
    }

  } catch (error) {
    console.error('Error fetching chat users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
