(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__de110ef0._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "createHealthResponse": (()=>createHealthResponse),
    "default": (()=>middleware),
    "logRequest": (()=>logRequest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
// Temporarily disable withAuth to fix build error
// import { withAuth } from 'next-auth/middleware';
// CORS configuration for mobile app
const corsOptions = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400'
};
// Security headers
const securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
};
function corsMiddleware(request) {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
        const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"](null, {
            status: 200
        });
        // Add CORS headers
        Object.entries(corsOptions).forEach(([key, value])=>{
            response.headers.set(key, value);
        });
        return response;
    }
    // Continue with the request
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Add CORS headers to all responses
    Object.entries(corsOptions).forEach(([key, value])=>{
        response.headers.set(key, value);
    });
    // Add security headers (except for API routes to avoid conflicts)
    if (!request.nextUrl.pathname.startsWith('/api/')) {
        Object.entries(securityHeaders).forEach(([key, value])=>{
            response.headers.set(key, value);
        });
    }
    return response;
}
// API routes that don't require authentication
const publicApiRoutes = [
    '/api/auth/mobile',
    '/api/mobile/test',
    '/api/test',
    '/api/health'
];
// API routes that require mobile authentication
const mobileApiRoutes = [
    '/api/mobile/',
    '/api/chat/',
    '/api/upload',
    '/api/realtime/'
];
function shouldBypassAuth(pathname) {
    return publicApiRoutes.some((route)=>pathname.startsWith(route));
}
function isMobileApiRoute(pathname) {
    return mobileApiRoutes.some((route)=>pathname.startsWith(route));
}
function middleware(request) {
    const { pathname } = request.nextUrl;
    console.log('🔒 Middleware processing:', {
        method: request.method,
        pathname,
        userAgent: request.headers.get('user-agent')?.substring(0, 50),
        hasAuth: !!request.headers.get('authorization')
    });
    // Apply CORS for all requests
    const corsResponse = corsMiddleware(request);
    if (corsResponse.status === 200 && request.method === 'OPTIONS') {
        return corsResponse;
    }
    // Handle API routes
    if (pathname.startsWith('/api/')) {
        // Public API routes - no auth required
        if (shouldBypassAuth(pathname)) {
            console.log('🔓 Public API route, bypassing auth:', pathname);
            return corsResponse;
        }
        // Mobile API routes - handle mobile auth differently
        if (isMobileApiRoute(pathname)) {
            console.log('📱 Mobile API route, allowing through:', pathname);
            return corsResponse;
        }
        // Other API routes - let them handle their own auth
        return corsResponse;
    }
    // For non-API routes, just return the CORS response for now
    // TODO: Re-enable NextAuth middleware after fixing build issues
    console.log('🔐 Middleware for non-API route:', pathname);
    return corsResponse;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'
    ]
};
function logRequest(request) {
    console.log('📝 Request details:', {
        method: request.method,
        url: request.url,
        pathname: request.nextUrl.pathname,
        headers: {
            authorization: request.headers.get('authorization') ? 'Bearer [PRESENT]' : 'Not provided',
            contentType: request.headers.get('content-type'),
            userAgent: request.headers.get('user-agent')?.substring(0, 100),
            origin: request.headers.get('origin'),
            referer: request.headers.get('referer')
        },
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    });
}
function createHealthResponse() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        middleware: 'active',
        cors: 'enabled',
        security: 'enabled'
    });
}
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__de110ef0._.js.map