const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Use MySQL connection
process.env.DATABASE_URL = 'mysql://root@localhost:3306/moonelec_db';

const prisma = new PrismaClient();

async function testMySQLAuth() {
  try {
    console.log('🔍 Testing MySQL authentication with existing data...');

    // Test 1: Get all users from MySQL
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        firstname: true,
        lastname: true,
        role: true,
      }
    });

    console.log('✅ Users found in MySQL:', users.length);
    users.forEach(user => {
      console.log(`  - ${user.username} (${user.email}) - ${user.role}`);
    });

    // Test 2: Get brands from MySQL
    const brands = await prisma.brand.findMany({
      select: {
        id: true,
        name: true,
      }
    });

    console.log('✅ Brands found in MySQL:', brands.length);
    brands.forEach(brand => {
      console.log(`  - ${brand.name}`);
    });

    // Test 3: Try to authenticate with existing users
    console.log('\n🔍 Testing authentication with existing users...');
    
    for (const user of users) {
      console.log(`\n🔍 Testing user: ${user.username}`);
      
      // Get full user data including password
      const fullUser = await prisma.user.findUnique({
        where: { username: user.username },
        include: {
          client: true,
          commercial: true,
          admin: true,
        },
      });

      if (fullUser) {
        console.log(`  ✅ User found with password hash: ${fullUser.password.substring(0, 20)}...`);
        
        // Test common passwords
        const testPasswords = ['admin123', 'password', '123456', 'admin', user.username];
        
        for (const testPassword of testPasswords) {
          try {
            const isValid = await bcrypt.compare(testPassword, fullUser.password);
            if (isValid) {
              console.log(`  ✅ PASSWORD FOUND: "${testPassword}" works for ${user.username}!`);
              
              // Build user object like in auth
              const userObject = {
                id: fullUser.id,
                email: fullUser.email,
                username: fullUser.username,
                name: `${fullUser.firstname} ${fullUser.lastname}`,
                firstname: fullUser.firstname,
                lastname: fullUser.lastname,
                role: fullUser.role,
                clientId: fullUser.client?.id,
                commercialId: fullUser.commercial?.id,
                adminId: fullUser.admin?.id,
              };
              
              console.log(`  📋 User object:`, userObject);
              break;
            }
          } catch (error) {
            // Password comparison failed, continue
          }
        }
      }
    }

    // Test 4: Check if we need to create test passwords
    console.log('\n🔧 Checking if we need to update passwords...');
    
    let needsPasswordUpdate = false;
    for (const user of users) {
      const fullUser = await prisma.user.findUnique({
        where: { username: user.username }
      });
      
      const isValidAdmin123 = await bcrypt.compare('admin123', fullUser.password);
      if (!isValidAdmin123) {
        console.log(`  ❌ User ${user.username} doesn't have 'admin123' password`);
        needsPasswordUpdate = true;
      } else {
        console.log(`  ✅ User ${user.username} already has 'admin123' password`);
      }
    }

    if (needsPasswordUpdate) {
      console.log('\n🔧 Updating passwords to "admin123" for testing...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      for (const user of users) {
        await prisma.user.update({
          where: { id: user.id },
          data: { password: hashedPassword }
        });
        console.log(`  ✅ Updated password for ${user.username}`);
      }
      
      console.log('\n✅ All passwords updated to "admin123"');
    }

    console.log('\n🎉 MySQL authentication test completed!');
    console.log('\n📋 AVAILABLE TEST ACCOUNTS:');
    users.forEach(user => {
      console.log(`👤 ${user.role}: ${user.email} / admin123`);
    });

  } catch (error) {
    console.error('❌ Error testing MySQL auth:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMySQLAuth();
