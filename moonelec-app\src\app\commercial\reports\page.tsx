'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  FaPlus,
  <PERSON>aEye,
  FaSpinner,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaFileAlt,
  FaEdit,
  FaTrash
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';

interface SalesReport {
  id: string;
  need: string;
  articleRef?: string;
  visitDate: string;
  denomination: string;
  city: string;
  submittedAt: string;
  isCompleted: boolean;
}

export default function CommercialReportsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [reports, setReports] = useState<SalesReport[]>([]);
  const [totalReports, setTotalReports] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [reportsPerPage, setReportsPerPage] = useState(10);
  const [hasSubmittedTodayReport, setHasSubmittedTodayReport] = useState(false);
  const [deletingReportId, setDeletingReportId] = useState<string | null>(null);

  // Fetch reports
  const fetchReports = async () => {
    setIsLoading(true);
    
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('skip', ((currentPage - 1) * reportsPerPage).toString());
      params.append('take', reportsPerPage.toString());
      
      const response = await fetch(`/api/sales-reports?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch reports');
      }
      
      const data = await response.json();
      setReports(data.reports);
      setTotalReports(data.total);
      
      // Check if a report has been submitted today
      const today = new Date().toISOString().split('T')[0];
      const hasSubmittedToday = data.reports.some((report: SalesReport) => {
        const reportDate = new Date(report.submittedAt).toISOString().split('T')[0];
        return reportDate === today && report.isCompleted;
      });
      
      setHasSubmittedTodayReport(hasSubmittedToday);
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Delete report function
  const handleDeleteReport = async (reportId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce rapport ?')) {
      return;
    }

    setDeletingReportId(reportId);

    try {
      const response = await fetch(`/api/sales-reports/${reportId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete report');
      }

      // Refresh the reports list
      await fetchReports();

    } catch (error) {
      console.error('Error deleting report:', error);
      alert('Erreur lors de la suppression du rapport');
    } finally {
      setDeletingReportId(null);
    }
  };

  // Fetch reports on initial load and when page changes
  useEffect(() => {
    if (user) {
      fetchReports();
    }
  }, [user, currentPage, reportsPerPage]);

  return (
    <RouteGuard allowedRoles={['COMMERCIAL']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            My Daily Reports
          </h1>
          <Link href="/commercial/reports/new">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              <FaPlus />
              <span>New Report</span>
            </motion.button>
          </Link>
        </div>
        
        {/* Daily Report Status */}
        <div className={`p-4 mb-6 rounded-lg ${
          hasSubmittedTodayReport 
            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
            : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
        }`}>
          <div className="flex items-center">
            {hasSubmittedTodayReport ? (
              <>
                <div className="flex-shrink-0 mr-3">
                  <svg className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <p>You have submitted your daily report for today. Thank you!</p>
              </>
            ) : (
              <>
                <div className="flex-shrink-0 mr-3">
                  <svg className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <p>You have not submitted your daily report for today. Please submit it as soon as possible.</p>
              </>
            )}
          </div>
        </div>
        
        {/* Reports Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          {isLoading && reports.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <FaSpinner className="animate-spin text-4xl text-primary" />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      City
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Denomination
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Need
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {reports.length > 0 ? (
                    reports.map((report) => (
                      <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <FaCalendarAlt className="text-gray-500 mr-2" />
                            <span className="text-sm text-gray-900 dark:text-white">
                              {new Date(report.visitDate).toLocaleDateString('fr-FR')}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <FaMapMarkerAlt className="text-gray-500 mr-2" />
                            <span className="text-sm text-gray-900 dark:text-white">
                              {report.city}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900 dark:text-white">
                            {report.denomination}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <span className="text-sm text-gray-900 dark:text-white">
                            {report.need.length > 50 ? `${report.need.substring(0, 50)}...` : report.need}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900 dark:text-white">
                            {new Date(report.submittedAt).toLocaleDateString('fr-FR', {
                              hour: '2-digit',
                              minute: '2-digit',
                            })}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex space-x-2">
                            <Link href={`/commercial/reports/${report.id}`}>
                              <button
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                title="Voir le rapport"
                              >
                                <FaEye />
                              </button>
                            </Link>
                            <Link href={`/commercial/reports/${report.id}/edit`}>
                              <button
                                className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                                title="Modifier le rapport"
                              >
                                <FaEdit />
                              </button>
                            </Link>
                            <button
                              onClick={() => handleDeleteReport(report.id)}
                              disabled={deletingReportId === report.id}
                              className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 ${
                                deletingReportId === report.id ? 'opacity-50 cursor-not-allowed' : ''
                              }`}
                              title="Supprimer le rapport"
                            >
                              {deletingReportId === report.id ? <FaSpinner className="animate-spin" /> : <FaTrash />}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        No reports found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
        
        {/* Pagination */}
        {totalReports > 0 && (
          <div className="flex justify-between items-center mt-4">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Showing {Math.min((currentPage - 1) * reportsPerPage + 1, totalReports)} to {Math.min(currentPage * reportsPerPage, totalReports)} of {totalReports} reports
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage * reportsPerPage >= totalReports}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
