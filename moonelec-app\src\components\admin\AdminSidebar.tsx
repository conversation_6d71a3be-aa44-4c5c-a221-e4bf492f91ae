'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaTachometerAlt,
  FaUsers,
  FaUserTie,
  FaShoppingCart,
  FaBox,
  FaCog,
  FaChartBar,
  FaChevronLeft,
  FaChevronRight,
  FaTags,
  FaCopyright,
  FaClipboardList
} from 'react-icons/fa';

export default function AdminSidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    { icon: <FaTachometerAlt />, label: 'Tableau de bord', href: '/admin/dashboard' },
    { icon: <FaUsers />, label: 'Clients', href: '/admin/clients' },
    { icon: <FaUserTie />, label: 'Commerciaux', href: '/admin/commercials' },
    { icon: <FaShoppingCart />, label: 'Devis', href: '/admin/quotes' },
    { icon: <FaBox />, label: 'Produits', href: '/admin/products' },
    { icon: <FaTags />, label: 'Catégories', href: '/admin/categories' },
    { icon: <FaCopyright />, label: 'Marques', href: '/admin/brands' },
    { icon: <FaClipboardList />, label: 'Rapports', href: '/admin/reports' },
    { icon: <FaChartBar />, label: 'Statistiques', href: '/admin/statistics' },
    { icon: <FaCog />, label: 'Paramètres', href: '/admin/settings' },
  ];

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <motion.div
      className={`bg-[#0a1f2f] text-white ${isCollapsed ? 'w-20' : 'w-64'} h-screen flex flex-col transition-all duration-300 shadow-lg z-20`}
      animate={{ width: isCollapsed ? 80 : 256 }}
      transition={{ duration: 0.3 }}
    >
      {/* Logo */}
      <div className="p-4 flex justify-between items-center border-b border-gray-700">
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="flex items-center"
            >
              <Image
                src="/images/logo/logo-moonelec.png"
                alt="Moonelec Logo"
                width={150}
                height={40}
                className="h-10 w-auto"
              />
            </motion.div>
          )}
        </AnimatePresence>

        {isCollapsed && (
          <div className="mx-auto">
            <Image
              src="/images/logo/logo-icon.png"
              alt="Moonelec Icon"
              width={32}
              height={32}
              className="h-8 w-auto"
            />
          </div>
        )}

        <button
          onClick={toggleSidebar}
          className="text-gray-400 hover:text-white transition-colors p-1 rounded-full hover:bg-gray-700"
        >
          {isCollapsed ? <FaChevronRight size={16} /> : <FaChevronLeft size={16} />}
        </button>
      </div>

      {/* Menu Items */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-2 px-2">
          {menuItems.map((item, index) => {
            const isActive = pathname === item.href;

            return (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Link
                  href={item.href}
                  className={`flex items-center p-3 rounded-lg transition-all ${
                    isActive
                      ? 'bg-primary text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  <span className="text-xl">{item.icon}</span>
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.span
                        initial={{ opacity: 0, width: 0 }}
                        animate={{ opacity: 1, width: 'auto' }}
                        exit={{ opacity: 0, width: 0 }}
                        transition={{ duration: 0.2 }}
                        className="ml-3 whitespace-nowrap overflow-hidden"
                      >
                        {item.label}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </Link>
              </motion.li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700 text-center">
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="text-xs text-gray-400"
            >
              © {new Date().getFullYear()} Moonelec
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}
