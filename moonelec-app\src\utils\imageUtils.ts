/**
 * Utilitaires pour la manipulation d'images
 */

/**
 * Convertit une image en base64 en utilisant l'API Canvas
 * @param imagePath Chemin de l'image (relatif au dossier public)
 * @returns Promise avec la chaîne base64 de l'image
 */
export const imageToBase64 = (imagePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    // Créer un élément image
    const img = new Image();
    
    // Définir le chemin complet de l'image (relatif au dossier public)
    img.src = imagePath;
    
    // Gérer les erreurs de chargement
    img.onerror = () => {
      reject(new Error(`Impossible de charger l'image: ${imagePath}`));
    };
    
    // Quand l'image est chargée
    img.onload = () => {
      try {
        // Créer un canvas avec les dimensions de l'image
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        
        // Dessiner l'image sur le canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Impossible de créer le contexte 2D du canvas'));
          return;
        }
        
        ctx.drawImage(img, 0, 0);
        
        // Convertir le canvas en base64
        const dataURL = canvas.toDataURL('image/png');
        
        // Résoudre la promesse avec la chaîne base64
        resolve(dataURL);
      } catch (error) {
        reject(error);
      }
    };
    
    // Assurer que l'image est chargée depuis le cache si disponible
    img.crossOrigin = 'Anonymous';
  });
};

/**
 * Convertit une image en base64 de manière synchrone (pour les tests)
 * Cette fonction ne doit être utilisée que pour les tests ou lorsque l'image est déjà en mémoire
 * @param img Élément image HTML
 * @returns Chaîne base64 de l'image
 */
export const imageToBase64Sync = (img: HTMLImageElement): string => {
  // Créer un canvas avec les dimensions de l'image
  const canvas = document.createElement('canvas');
  canvas.width = img.width;
  canvas.height = img.height;
  
  // Dessiner l'image sur le canvas
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('Impossible de créer le contexte 2D du canvas');
  }
  
  ctx.drawImage(img, 0, 0);
  
  // Convertir le canvas en base64
  return canvas.toDataURL('image/png');
};

/**
 * Précharge une image et la convertit en base64
 * Utile pour préparer des images avant de générer un PDF
 * @param imagePath Chemin de l'image (relatif au dossier public)
 * @returns Promise avec la chaîne base64 de l'image
 */
export const preloadImageAsBase64 = (imagePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    // Créer un élément image
    const img = new Image();
    
    // Définir le chemin complet de l'image (relatif au dossier public)
    img.src = imagePath;
    
    // Gérer les erreurs de chargement
    img.onerror = () => {
      reject(new Error(`Impossible de charger l'image: ${imagePath}`));
    };
    
    // Quand l'image est chargée
    img.onload = () => {
      try {
        // Convertir l'image en base64
        const base64 = imageToBase64Sync(img);
        resolve(base64);
      } catch (error) {
        reject(error);
      }
    };
    
    // Assurer que l'image est chargée depuis le cache si disponible
    img.crossOrigin = 'Anonymous';
  });
};
