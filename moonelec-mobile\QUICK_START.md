# 🚀 Guide de Démarrage Rapide - Moonelec Mobile

## ⚠️ **IMPORTANT : Démarrer l'Application Web d'abord**

L'application mobile a besoin de l'application web pour fonctionner.

### **1. Démarrer l'Application Web**

```bash
# Dans un nouveau terminal
cd ../moonelec-app
npm run dev
```

L'application web doit être accessible sur `http://localhost:3000`

### **2. Vérifier la Connectivité**

Testez que l'API fonctionne :
```bash
curl http://localhost:3000/api/auth/csrf
```

Vous devriez voir une réponse avec un `csrfToken`.

### **3. Configurer l'IP pour Mobile**

#### **Option A : Utiliser votre IP locale**
1. Trouvez votre IP locale :
   ```bash
   # Windows
   ipconfig
   
   # Mac/Linux
   ifconfig
   ```

2. Modifiez `src/services/api.ts` ligne 19 :
   ```typescript
   const BASE_URL = 'http://VOTRE_IP:3000/api';
   ```

#### **Option B : Utiliser localhost (pour émulateur)**
```typescript
const BASE_URL = 'http://localhost:3000/api';
```

### **4. Démarrer l'Application Mobile**

```bash
# Dans le dossier moonelec-mobile
npx expo start
```

### **5. Tester l'Authentification**

Utilisez ces comptes de test :
```
Admin:
- Username: admin
- Password: admin123

Commercial:
- Username: commercial  
- Password: commercial123

Client:
- Username: client
- Password: client123
```

## 🔧 **Résolution des Problèmes**

### **Erreur "Network Error"**
1. ✅ Vérifiez que l'app web est démarrée
2. ✅ Vérifiez l'URL dans `src/services/api.ts`
3. ✅ Testez avec `curl http://VOTRE_IP:3000/api/auth/csrf`

### **Erreur d'animation**
✅ **CORRIGÉ** - Les animations utilisent maintenant `transform` au lieu de `width`

### **App web ne démarre pas**
```bash
cd ../moonelec-app
npm install
npm run dev
```

### **Endpoint mobile manquant**
✅ **CRÉÉ** - L'endpoint `/api/auth/mobile` a été ajouté à l'app web

## 📱 **Configuration Actuelle**

- **URL API** : `http://**************:3000/api`
- **Endpoint mobile** : `/auth/mobile`
- **Fallback** : NextAuth compatible
- **Logs** : Détaillés pour debugging

## ✅ **Checklist de Démarrage**

- [ ] Application web démarrée (`npm run dev`)
- [ ] API accessible (`curl test`)
- [ ] IP configurée dans `api.ts`
- [ ] Application mobile démarrée (`expo start`)
- [ ] Test de connexion avec compte admin

## 🎯 **Prochaines Étapes**

Une fois l'authentification fonctionnelle :
1. ✅ Testez toutes les fonctionnalités
2. ✅ Naviguez entre les écrans
3. ✅ Créez des devis
4. ✅ Testez les rapports (commerciaux)
5. ✅ Vérifiez les animations

**🚀 L'application mobile Moonelec est prête à fonctionner !**
