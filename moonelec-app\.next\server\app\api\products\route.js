/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_products__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/products */ \"(rsc)/./src/lib/products.ts\");\n/* harmony import */ var _lib_mobile_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mobile-auth */ \"(rsc)/./src/lib/mobile-auth.ts\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./src/lib/auth-options.ts\");\n\n\n\n\n\n// GET /api/products - Get all products with optional filtering\nasync function GET(req) {\n    try {\n        console.log('📦 Products API - Request received');\n        // Check for mobile authentication\n        const mobileUser = await (0,_lib_mobile_auth__WEBPACK_IMPORTED_MODULE_3__.getMobileUserFromRequest)(req);\n        if (mobileUser) {\n            console.log('📱 Mobile user authenticated:', mobileUser.username, mobileUser.role);\n        } else {\n            console.log('📱 No mobile user found, allowing public access to products');\n        }\n        const searchParams = req.nextUrl.searchParams;\n        const categoryId = searchParams.get('categoryId') || undefined;\n        const brandId = searchParams.get('brandId') || undefined;\n        const search = searchParams.get('search') || undefined;\n        const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')) : undefined;\n        const take = searchParams.has('take') ? parseInt(searchParams.get('take')) : undefined;\n        const { products, total } = await (0,_lib_products__WEBPACK_IMPORTED_MODULE_2__.getProducts)({\n            categoryId,\n            brandId,\n            search,\n            skip,\n            take\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            products,\n            total\n        });\n    } catch (error) {\n        console.error('Error fetching products:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch products'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/products - Create a new product\nasync function POST(req) {\n    try {\n        // Check for mobile authentication first\n        const mobileUser = await (0,_lib_mobile_auth__WEBPACK_IMPORTED_MODULE_3__.getMobileUserFromRequest)(req);\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_4__.authOptions);\n        if (!mobileUser && !session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Use mobile user data if available, otherwise use session\n        const user = mobileUser || session?.user;\n        if (!user || user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized - Admin access required'\n            }, {\n                status: 403\n            });\n        }\n        const body = await req.json();\n        const { reference, name, description, characteristics, mainImage, categoryId, brandId, images } = body;\n        // Validate required fields\n        if (!reference || !name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Reference and name are required'\n            }, {\n                status: 400\n            });\n        }\n        // Create the product\n        const product = await (0,_lib_products__WEBPACK_IMPORTED_MODULE_2__.createProduct)({\n            reference,\n            name,\n            description,\n            characteristics: characteristics || {},\n            mainImage,\n            categoryId,\n            brandId,\n            images: images || []\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(product, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to create product'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9wcm9kdWN0cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF3RDtBQUNYO0FBQ2U7QUFDQztBQUNaO0FBRWpELCtEQUErRDtBQUN4RCxlQUFlTSxJQUFJQyxHQUFnQjtJQUN4QyxJQUFJO1FBQ0ZDLFFBQVFDLEdBQUcsQ0FBQztRQUVaLGtDQUFrQztRQUNsQyxNQUFNQyxhQUFhLE1BQU1OLDBFQUF3QkEsQ0FBQ0c7UUFDbEQsSUFBSUcsWUFBWTtZQUNkRixRQUFRQyxHQUFHLENBQUMsaUNBQWlDQyxXQUFXQyxRQUFRLEVBQUVELFdBQVdFLElBQUk7UUFDbkYsT0FBTztZQUNMSixRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUVBLE1BQU1JLGVBQWVOLElBQUlPLE9BQU8sQ0FBQ0QsWUFBWTtRQUM3QyxNQUFNRSxhQUFhRixhQUFhRyxHQUFHLENBQUMsaUJBQWlCQztRQUNyRCxNQUFNQyxVQUFVTCxhQUFhRyxHQUFHLENBQUMsY0FBY0M7UUFDL0MsTUFBTUUsU0FBU04sYUFBYUcsR0FBRyxDQUFDLGFBQWFDO1FBQzdDLE1BQU1HLE9BQU9QLGFBQWFRLEdBQUcsQ0FBQyxVQUFVQyxTQUFTVCxhQUFhRyxHQUFHLENBQUMsV0FBWUM7UUFDOUUsTUFBTU0sT0FBT1YsYUFBYVEsR0FBRyxDQUFDLFVBQVVDLFNBQVNULGFBQWFHLEdBQUcsQ0FBQyxXQUFZQztRQUU5RSxNQUFNLEVBQUVPLFFBQVEsRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTXZCLDBEQUFXQSxDQUFDO1lBQzVDYTtZQUNBRztZQUNBQztZQUNBQztZQUNBRztRQUNGO1FBRUEsT0FBT3ZCLHFEQUFZQSxDQUFDMEIsSUFBSSxDQUFDO1lBQUVGO1lBQVVDO1FBQU07SUFDN0MsRUFBRSxPQUFPRSxPQUFZO1FBQ25CbkIsUUFBUW1CLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU8zQixxREFBWUEsQ0FBQzBCLElBQUksQ0FDdEI7WUFBRUMsT0FBT0EsTUFBTUMsT0FBTyxJQUFJO1FBQTJCLEdBQ3JEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsNENBQTRDO0FBQ3JDLGVBQWVDLEtBQUt2QixHQUFnQjtJQUN6QyxJQUFJO1FBQ0Ysd0NBQXdDO1FBQ3hDLE1BQU1HLGFBQWEsTUFBTU4sMEVBQXdCQSxDQUFDRztRQUNsRCxNQUFNd0IsVUFBVSxNQUFNOUIsMkRBQWdCQSxDQUFDSSwwREFBV0E7UUFFbEQsSUFBSSxDQUFDSyxjQUFjLENBQUNxQixTQUFTO1lBQzNCLE9BQU8vQixxREFBWUEsQ0FBQzBCLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFlLEdBQUc7Z0JBQUVFLFFBQVE7WUFBSTtRQUNwRTtRQUVBLDJEQUEyRDtRQUMzRCxNQUFNRyxPQUFPdEIsY0FBY3FCLFNBQVNDO1FBRXBDLElBQUksQ0FBQ0EsUUFBUUEsS0FBS3BCLElBQUksS0FBSyxTQUFTO1lBQ2xDLE9BQU9aLHFEQUFZQSxDQUFDMEIsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQXVDLEdBQUc7Z0JBQUVFLFFBQVE7WUFBSTtRQUM1RjtRQUVBLE1BQU1JLE9BQU8sTUFBTTFCLElBQUltQixJQUFJO1FBQzNCLE1BQU0sRUFDSlEsU0FBUyxFQUNUQyxJQUFJLEVBQ0pDLFdBQVcsRUFDWEMsZUFBZSxFQUNmQyxTQUFTLEVBQ1R2QixVQUFVLEVBQ1ZHLE9BQU8sRUFDUHFCLE1BQU0sRUFDUCxHQUFHTjtRQUVKLDJCQUEyQjtRQUMzQixJQUFJLENBQUNDLGFBQWEsQ0FBQ0MsTUFBTTtZQUN2QixPQUFPbkMscURBQVlBLENBQUMwQixJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQWtDLEdBQzNDO2dCQUFFRSxRQUFRO1lBQUk7UUFFbEI7UUFFQSxxQkFBcUI7UUFDckIsTUFBTVcsVUFBVSxNQUFNckMsNERBQWFBLENBQUM7WUFDbEMrQjtZQUNBQztZQUNBQztZQUNBQyxpQkFBaUJBLG1CQUFtQixDQUFDO1lBQ3JDQztZQUNBdkI7WUFDQUc7WUFDQXFCLFFBQVFBLFVBQVUsRUFBRTtRQUN0QjtRQUVBLE9BQU92QyxxREFBWUEsQ0FBQzBCLElBQUksQ0FBQ2MsU0FBUztZQUFFWCxRQUFRO1FBQUk7SUFDbEQsRUFBRSxPQUFPRixPQUFZO1FBQ25CbkIsUUFBUW1CLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU8zQixxREFBWUEsQ0FBQzBCLElBQUksQ0FDdEI7WUFBRUMsT0FBT0EsTUFBTUMsT0FBTyxJQUFJO1FBQTJCLEdBQ3JEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGFwcFxcYXBpXFxwcm9kdWN0c1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgnO1xuaW1wb3J0IHsgZ2V0UHJvZHVjdHMsIGNyZWF0ZVByb2R1Y3QgfSBmcm9tICdAL2xpYi9wcm9kdWN0cyc7XG5pbXBvcnQgeyBnZXRNb2JpbGVVc2VyRnJvbVJlcXVlc3QgfSBmcm9tICdAL2xpYi9tb2JpbGUtYXV0aCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgtb3B0aW9ucyc7XG5cbi8vIEdFVCAvYXBpL3Byb2R1Y3RzIC0gR2V0IGFsbCBwcm9kdWN0cyB3aXRoIG9wdGlvbmFsIGZpbHRlcmluZ1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ/Cfk6YgUHJvZHVjdHMgQVBJIC0gUmVxdWVzdCByZWNlaXZlZCcpO1xuXG4gICAgLy8gQ2hlY2sgZm9yIG1vYmlsZSBhdXRoZW50aWNhdGlvblxuICAgIGNvbnN0IG1vYmlsZVVzZXIgPSBhd2FpdCBnZXRNb2JpbGVVc2VyRnJvbVJlcXVlc3QocmVxKTtcbiAgICBpZiAobW9iaWxlVXNlcikge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk7EgTW9iaWxlIHVzZXIgYXV0aGVudGljYXRlZDonLCBtb2JpbGVVc2VyLnVzZXJuYW1lLCBtb2JpbGVVc2VyLnJvbGUpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+TsSBObyBtb2JpbGUgdXNlciBmb3VuZCwgYWxsb3dpbmcgcHVibGljIGFjY2VzcyB0byBwcm9kdWN0cycpO1xuICAgIH1cblxuICAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHJlcS5uZXh0VXJsLnNlYXJjaFBhcmFtcztcbiAgICBjb25zdCBjYXRlZ29yeUlkID0gc2VhcmNoUGFyYW1zLmdldCgnY2F0ZWdvcnlJZCcpIHx8IHVuZGVmaW5lZDtcbiAgICBjb25zdCBicmFuZElkID0gc2VhcmNoUGFyYW1zLmdldCgnYnJhbmRJZCcpIHx8IHVuZGVmaW5lZDtcbiAgICBjb25zdCBzZWFyY2ggPSBzZWFyY2hQYXJhbXMuZ2V0KCdzZWFyY2gnKSB8fCB1bmRlZmluZWQ7XG4gICAgY29uc3Qgc2tpcCA9IHNlYXJjaFBhcmFtcy5oYXMoJ3NraXAnKSA/IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoJ3NraXAnKSEpIDogdW5kZWZpbmVkO1xuICAgIGNvbnN0IHRha2UgPSBzZWFyY2hQYXJhbXMuaGFzKCd0YWtlJykgPyBwYXJzZUludChzZWFyY2hQYXJhbXMuZ2V0KCd0YWtlJykhKSA6IHVuZGVmaW5lZDtcblxuICAgIGNvbnN0IHsgcHJvZHVjdHMsIHRvdGFsIH0gPSBhd2FpdCBnZXRQcm9kdWN0cyh7XG4gICAgICBjYXRlZ29yeUlkLFxuICAgICAgYnJhbmRJZCxcbiAgICAgIHNlYXJjaCxcbiAgICAgIHNraXAsXG4gICAgICB0YWtlLFxuICAgIH0pO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgcHJvZHVjdHMsIHRvdGFsIH0pO1xuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcHJvZHVjdHM6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBmZXRjaCBwcm9kdWN0cycgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gUE9TVCAvYXBpL3Byb2R1Y3RzIC0gQ3JlYXRlIGEgbmV3IHByb2R1Y3RcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcTogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICAvLyBDaGVjayBmb3IgbW9iaWxlIGF1dGhlbnRpY2F0aW9uIGZpcnN0XG4gICAgY29uc3QgbW9iaWxlVXNlciA9IGF3YWl0IGdldE1vYmlsZVVzZXJGcm9tUmVxdWVzdChyZXEpO1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKTtcblxuICAgIGlmICghbW9iaWxlVXNlciAmJiAhc2Vzc2lvbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSk7XG4gICAgfVxuXG4gICAgLy8gVXNlIG1vYmlsZSB1c2VyIGRhdGEgaWYgYXZhaWxhYmxlLCBvdGhlcndpc2UgdXNlIHNlc3Npb25cbiAgICBjb25zdCB1c2VyID0gbW9iaWxlVXNlciB8fCBzZXNzaW9uPy51c2VyO1xuXG4gICAgaWYgKCF1c2VyIHx8IHVzZXIucm9sZSAhPT0gJ0FETUlOJykge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQgLSBBZG1pbiBhY2Nlc3MgcmVxdWlyZWQnIH0sIHsgc3RhdHVzOiA0MDMgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcS5qc29uKCk7XG4gICAgY29uc3Qge1xuICAgICAgcmVmZXJlbmNlLFxuICAgICAgbmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgY2hhcmFjdGVyaXN0aWNzLFxuICAgICAgbWFpbkltYWdlLFxuICAgICAgY2F0ZWdvcnlJZCxcbiAgICAgIGJyYW5kSWQsXG4gICAgICBpbWFnZXNcbiAgICB9ID0gYm9keTtcblxuICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgIGlmICghcmVmZXJlbmNlIHx8ICFuYW1lKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdSZWZlcmVuY2UgYW5kIG5hbWUgYXJlIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHRoZSBwcm9kdWN0XG4gICAgY29uc3QgcHJvZHVjdCA9IGF3YWl0IGNyZWF0ZVByb2R1Y3Qoe1xuICAgICAgcmVmZXJlbmNlLFxuICAgICAgbmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgY2hhcmFjdGVyaXN0aWNzOiBjaGFyYWN0ZXJpc3RpY3MgfHwge30sXG4gICAgICBtYWluSW1hZ2UsXG4gICAgICBjYXRlZ29yeUlkLFxuICAgICAgYnJhbmRJZCxcbiAgICAgIGltYWdlczogaW1hZ2VzIHx8IFtdLFxuICAgIH0pO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHByb2R1Y3QsIHsgc3RhdHVzOiAyMDEgfSk7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBwcm9kdWN0OicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gY3JlYXRlIHByb2R1Y3QnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59Il0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdldFNlcnZlclNlc3Npb24iLCJnZXRQcm9kdWN0cyIsImNyZWF0ZVByb2R1Y3QiLCJnZXRNb2JpbGVVc2VyRnJvbVJlcXVlc3QiLCJhdXRoT3B0aW9ucyIsIkdFVCIsInJlcSIsImNvbnNvbGUiLCJsb2ciLCJtb2JpbGVVc2VyIiwidXNlcm5hbWUiLCJyb2xlIiwic2VhcmNoUGFyYW1zIiwibmV4dFVybCIsImNhdGVnb3J5SWQiLCJnZXQiLCJ1bmRlZmluZWQiLCJicmFuZElkIiwic2VhcmNoIiwic2tpcCIsImhhcyIsInBhcnNlSW50IiwidGFrZSIsInByb2R1Y3RzIiwidG90YWwiLCJqc29uIiwiZXJyb3IiLCJtZXNzYWdlIiwic3RhdHVzIiwiUE9TVCIsInNlc3Npb24iLCJ1c2VyIiwiYm9keSIsInJlZmVyZW5jZSIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImNoYXJhY3RlcmlzdGljcyIsIm1haW5JbWFnZSIsImltYWdlcyIsInByb2R1Y3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconsole.log('🔧 Auth options module loaded');\nconsole.log('🔧 Creating auth options...');\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                username: {\n                    label: \"Nom d'utilisateur\",\n                    type: 'text'\n                },\n                password: {\n                    label: 'Mot de passe',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                console.log('🔍 AUTHORIZE FUNCTION CALLED!');\n                console.log('🔍 Auth attempt:', {\n                    username: credentials?.username,\n                    hasPassword: !!credentials?.password\n                });\n                if (!credentials?.username || !credentials?.password) {\n                    console.log('❌ Missing credentials');\n                    return null;\n                }\n                try {\n                    // Rechercher l'utilisateur par nom d'utilisateur\n                    console.log('🔍 Looking for user:', credentials.username);\n                    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.findUserByUsername)(credentials.username);\n                    // Vérifier si l'utilisateur existe\n                    if (!user) {\n                        console.log('❌ User not found:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ User found:', {\n                        id: user.id,\n                        username: user.username,\n                        role: user.role\n                    });\n                    // Vérifier le mot de passe\n                    console.log('🔍 Verifying password...');\n                    const isPasswordValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log('❌ Invalid password for user:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ Password valid for user:', credentials.username);\n                    // Retourner les données de l'utilisateur sans le mot de passe\n                    const userObject = {\n                        id: user.id,\n                        email: user.email,\n                        username: user.username,\n                        name: `${user.firstname} ${user.lastname}`,\n                        firstname: user.firstname,\n                        lastname: user.lastname,\n                        role: user.role,\n                        clientId: user.client?.id,\n                        commercialId: user.commercial?.id,\n                        adminId: user.admin?.id\n                    };\n                    console.log('✅ Returning user object:', userObject);\n                    return userObject;\n                } catch (error) {\n                    console.error('❌ Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.username = user.username;\n                token.role = user.role;\n                token.firstname = user.firstname;\n                token.lastname = user.lastname;\n                token.clientId = user.clientId;\n                token.commercialId = user.commercialId;\n                token.adminId = user.adminId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.username = token.username;\n                session.user.role = token.role;\n                session.user.firstname = token.firstname;\n                session.user.lastname = token.lastname;\n                session.user.clientId = token.clientId;\n                session.user.commercialId = token.commercialId;\n                session.user.adminId = token.adminId;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signOut: '/auth/signout',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   createClientUser: () => (/* binding */ createClientUser),\n/* harmony export */   createCommercialUser: () => (/* binding */ createCommercialUser),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Fonction pour hacher un mot de passe\nasync function hashPassword(password) {\n    const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].genSalt(10);\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, salt);\n}\n// Fonction pour vérifier un mot de passe\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Fonction pour créer un utilisateur client\nasync function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.CLIENT\n            }\n        });\n        // Créer le profil client associé\n        const client = await tx.client.create({\n            data: {\n                userId: user.id,\n                company_name\n            }\n        });\n        return {\n            user,\n            client\n        };\n    });\n}\n// Fonction pour créer un utilisateur commercial\nasync function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.COMMERCIAL\n            }\n        });\n        // Créer le profil commercial associé\n        const commercial = await tx.commercial.create({\n            data: {\n                userId: user.id,\n                profile_photo\n            }\n        });\n        return {\n            user,\n            commercial\n        };\n    });\n}\n// Fonction pour créer un utilisateur administrateur\nasync function createAdminUser({ email, username, password, lastname, firstname, telephone }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN\n            }\n        });\n        // Créer le profil admin associé\n        const admin = await tx.admin.create({\n            data: {\n                userId: user.id\n            }\n        });\n        return {\n            user,\n            admin\n        };\n    });\n}\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nasync function findUserByUsername(username) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            username\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n// Fonction pour trouver un utilisateur par son email\nasync function findUserByEmail(email) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            email\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mobile-auth.ts":
/*!********************************!*\
  !*** ./src/lib/mobile-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMobileUserFromRequest: () => (/* binding */ getMobileUserFromRequest),\n/* harmony export */   isMobileRequest: () => (/* binding */ isMobileRequest),\n/* harmony export */   verifyMobileToken: () => (/* binding */ verifyMobileToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function verifyMobileToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            console.log('🔐 No valid authorization header found');\n            return null;\n        }\n        const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n        console.log('🔐 Mobile token received (length):', token.length);\n        // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET\n        let decoded;\n        try {\n            const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';\n            console.log('🔐 Trying JWT_SECRET for token verification');\n            decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n            console.log('🔐 Token decoded successfully with JWT_SECRET:', {\n                userId: decoded.userId,\n                role: decoded.role\n            });\n        } catch (jwtError) {\n            console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');\n            try {\n                const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';\n                decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, nextAuthSecret);\n                console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', {\n                    userId: decoded.userId,\n                    role: decoded.role\n                });\n            } catch (nextAuthError) {\n                console.error('🔐 Both JWT secrets failed:', {\n                    jwtError: jwtError.message,\n                    nextAuthError: nextAuthError.message\n                });\n                throw new Error('Invalid token signature');\n            }\n        }\n        // Get fresh user data from database\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: decoded.userId\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                firstname: true,\n                lastname: true,\n                role: true\n            }\n        });\n        if (!user) {\n            console.log('🔐 User not found:', {\n                found: !!user\n            });\n            return null;\n        }\n        console.log('🔐 Mobile user authenticated successfully:', {\n            id: user.id,\n            role: user.role\n        });\n        return user;\n    } catch (error) {\n        console.error('Mobile token verification error:', error);\n        return null;\n    }\n}\nfunction isMobileRequest(request) {\n    const userAgent = request.headers.get('user-agent') || '';\n    const authHeader = request.headers.get('authorization');\n    // Check if it's a mobile request with JWT token\n    return authHeader?.startsWith('Bearer ') || userAgent.includes('Expo') || userAgent.includes('ReactNative');\n}\nasync function getMobileUserFromRequest(request) {\n    // Always try to verify mobile token if Authorization header is present\n    const authHeader = request.headers.get('authorization');\n    if (authHeader?.startsWith('Bearer ')) {\n        return await verifyMobileToken(request);\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mobile-auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/products.ts":
/*!*****************************!*\
  !*** ./src/lib/products.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addProductImages: () => (/* binding */ addProductImages),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteAllProductImages: () => (/* binding */ deleteAllProductImages),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   deleteProductImage: () => (/* binding */ deleteProductImage),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductByReference: () => (/* binding */ getProductByReference),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct),\n/* harmony export */   updateProductImage: () => (/* binding */ updateProductImage)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// Get all products with optional filtering\nasync function getProducts(options) {\n    const { categoryId, brandId, search, skip = 0, take = 50 } = options || {};\n    const where = {\n        ...categoryId ? {\n            categoryId\n        } : {},\n        ...brandId ? {\n            brandId\n        } : {},\n        ...search ? {\n            OR: [\n                {\n                    name: {\n                        contains: search\n                    }\n                },\n                {\n                    reference: {\n                        contains: search\n                    }\n                },\n                {\n                    description: {\n                        contains: search\n                    }\n                }\n            ]\n        } : {}\n    };\n    const [rawProducts, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findMany({\n            where,\n            include: {\n                category: true,\n                brand: true,\n                productimage: {\n                    orderBy: {\n                        order: 'asc'\n                    }\n                }\n            },\n            skip,\n            take,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.count({\n            where\n        })\n    ]);\n    // Parse characteristics for all products\n    const products = rawProducts.map((product)=>{\n        let parsedCharacteristics = {};\n        try {\n            parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};\n        } catch (error) {\n            console.error('Error parsing characteristics for product', product.id, ':', error);\n            parsedCharacteristics = {};\n        }\n        return {\n            ...product,\n            characteristics: parsedCharacteristics\n        };\n    });\n    return {\n        products,\n        total\n    };\n}\n// Get a single product by ID\nasync function getProductById(id) {\n    const product = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findUnique({\n        where: {\n            id\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    // Parse characteristics JSON string back to object\n    let parsedCharacteristics = {};\n    try {\n        parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};\n    } catch (error) {\n        console.error('Error parsing characteristics for product', id, ':', error);\n        parsedCharacteristics = {};\n    }\n    return {\n        ...product,\n        characteristics: parsedCharacteristics\n    };\n}\n// Get a single product by reference\nasync function getProductByReference(reference) {\n    const product = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findUnique({\n        where: {\n            reference\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n    if (!product) return null;\n    // Parse characteristics JSON string back to object\n    let parsedCharacteristics = {};\n    try {\n        parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};\n    } catch (error) {\n        console.error('Error parsing characteristics for product', reference, ':', error);\n        parsedCharacteristics = {};\n    }\n    return {\n        ...product,\n        characteristics: parsedCharacteristics\n    };\n}\n// Create a new product\nasync function createProduct(data) {\n    const { images, ...productData } = data;\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // Check if reference already exists\n        const existingProduct = await tx.product.findUnique({\n            where: {\n                reference: productData.reference\n            }\n        });\n        if (existingProduct) {\n            throw new Error(`Un produit avec la référence ${productData.reference} existe déjà`);\n        }\n        // Create the product\n        const product = await tx.product.create({\n            data: {\n                id: crypto.randomUUID(),\n                reference: productData.reference,\n                name: productData.name,\n                description: productData.description,\n                characteristics: JSON.stringify(productData.characteristics),\n                mainImage: productData.mainImage,\n                categoryId: productData.categoryId,\n                brandId: productData.brandId,\n                updatedAt: new Date()\n            }\n        });\n        // Add images if provided\n        if (images && images.length > 0) {\n            await tx.productimage.createMany({\n                data: images.map((image, index)=>({\n                        id: crypto.randomUUID(),\n                        url: image.url,\n                        alt: image.alt || product.name,\n                        order: image.order || index,\n                        productId: product.id\n                    }))\n            });\n        }\n        return product;\n    });\n}\n// Update an existing product\nasync function updateProduct(id, data) {\n    // If reference is being updated, check if it already exists\n    if (data.reference) {\n        const existingProduct = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findFirst({\n            where: {\n                reference: data.reference,\n                id: {\n                    not: id\n                }\n            }\n        });\n        if (existingProduct) {\n            throw new Error(`Un produit avec la référence ${data.reference} existe déjà`);\n        }\n    }\n    const updatedProduct = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.update({\n        where: {\n            id\n        },\n        data: {\n            ...data,\n            characteristics: data.characteristics ? JSON.stringify(data.characteristics) : undefined,\n            updatedAt: new Date()\n        },\n        include: {\n            category: true,\n            brand: true,\n            productimage: {\n                orderBy: {\n                    order: 'asc'\n                }\n            }\n        }\n    });\n    // Parse characteristics JSON string back to object\n    let parsedCharacteristics = {};\n    try {\n        parsedCharacteristics = updatedProduct.characteristics ? JSON.parse(updatedProduct.characteristics) : {};\n    } catch (error) {\n        console.error('Error parsing characteristics for updated product', id, ':', error);\n        parsedCharacteristics = {};\n    }\n    return {\n        ...updatedProduct,\n        characteristics: parsedCharacteristics\n    };\n}\n// Delete a product\nasync function deleteProduct(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n        // 1. Supprimer d'abord les références dans QuoteItem\n        await tx.quoteItem.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 2. Supprimer les références dans OrderItem\n        await tx.orderItem.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 3. Supprimer les images du produit\n        await tx.productimage.deleteMany({\n            where: {\n                productId: id\n            }\n        });\n        // 4. Enfin, supprimer le produit lui-même\n        return tx.product.delete({\n            where: {\n                id\n            }\n        });\n    });\n}\n// Add images to a product\nasync function addProductImages(productId, images) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.createMany({\n        data: images.map((image, index)=>({\n                id: crypto.randomUUID(),\n                url: image.url,\n                alt: image.alt,\n                order: image.order || index,\n                productId\n            }))\n    });\n}\n// Update a product image\nasync function updateProductImage(id, data) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.update({\n        where: {\n            id\n        },\n        data\n    });\n}\n// Delete a product image\nasync function deleteProductImage(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.delete({\n        where: {\n            id\n        }\n    });\n}\n// Delete all images for a product\nasync function deleteAllProductImages(productId) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.productimage.deleteMany({\n        where: {\n            productId\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/products.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();