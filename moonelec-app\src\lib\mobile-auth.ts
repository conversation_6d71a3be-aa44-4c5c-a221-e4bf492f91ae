import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';

export interface MobileUser {
  id: string;
  username: string;
  email: string;
  firstname: string;
  lastname: string;
  role: string;
  isActive: boolean;
}

export async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {
  try {
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('🔐 No valid authorization header found');
      return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    console.log('🔐 Mobile token received:', token.substring(0, 50) + '...');

    // Verify JWT token using JWT_SECRET (for mobile auth)
    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
    console.log('🔐 Using JWT secret:', jwtSecret ? 'Available' : 'Missing');

    const decoded = jwt.verify(token, jwtSecret) as any;
    console.log('🔐 Token decoded successfully:', { userId: decoded.userId, role: decoded.role });

    // Get fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        email: true,
        firstname: true,
        lastname: true,
        role: true,
        isActive: true,
      }
    });

    if (!user || !user.isActive) {
      console.log('🔐 User not found or inactive:', { found: !!user, active: user?.isActive });
      return null;
    }

    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });
    return user;
  } catch (error) {
    console.error('Mobile token verification error:', error);
    return null;
  }
}

export function isMobileRequest(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || '';
  const authHeader = request.headers.get('authorization');

  // Check if it's a mobile request with JWT token
  return authHeader?.startsWith('Bearer ') ||
         userAgent.includes('Expo') ||
         userAgent.includes('ReactNative');
}

export async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {
  if (!isMobileRequest(request)) {
    return null;
  }

  return await verifyMobileToken(request);
}
