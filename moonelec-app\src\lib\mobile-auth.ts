import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';

export interface MobileUser {
  id: string;
  username: string;
  email: string;
  firstname: string;
  lastname: string;
  role: string;
  isActive: boolean;
}

export async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {
  try {
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('🔐 No valid authorization header found');
      return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    console.log('🔐 Mobile token received (length):', token.length);

    // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET
    let decoded: any;
    try {
      const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
      console.log('🔐 Trying JWT_SECRET for token verification');
      decoded = jwt.verify(token, jwtSecret) as any;
      console.log('🔐 Token decoded successfully with JWT_SECRET:', { userId: decoded.userId, role: decoded.role });
    } catch (jwtError) {
      console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');
      try {
        const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';
        decoded = jwt.verify(token, nextAuthSecret) as any;
        console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', { userId: decoded.userId, role: decoded.role });
      } catch (nextAuthError) {
        console.error('🔐 Both JWT secrets failed:', { jwtError: jwtError.message, nextAuthError: nextAuthError.message });
        throw new Error('Invalid token signature');
      }
    }

    // Get fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        email: true,
        firstname: true,
        lastname: true,
        role: true,
      }
    });

    if (!user) {
      console.log('🔐 User not found:', { found: !!user });
      return null;
    }

    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });
    return user;
  } catch (error) {
    console.error('Mobile token verification error:', error);
    return null;
  }
}

export function isMobileRequest(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || '';
  const authHeader = request.headers.get('authorization');

  // Check if it's a mobile request with JWT token
  return authHeader?.startsWith('Bearer ') ||
         userAgent.includes('Expo') ||
         userAgent.includes('ReactNative');
}

export async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {
  // Always try to verify mobile token if Authorization header is present
  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return await verifyMobileToken(request);
  }

  return null;
}
