{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/realtime-data.ts"], "sourcesContent": ["import { prisma } from './prisma';\n\n// Real-time data calculation functions\nexport class RealTimeDataService {\n  \n  // Calculate real revenue from quotes and orders\n  static async calculateRealRevenue(timeframe: 'day' | 'week' | 'month' | 'year' = 'month') {\n    const now = new Date();\n    let startDate: Date;\n\n    switch (timeframe) {\n      case 'day':\n        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        break;\n      case 'week':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case 'month':\n        startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        break;\n      case 'year':\n        startDate = new Date(now.getFullYear(), 0, 1);\n        break;\n    }\n\n    // Calculate revenue from quotes\n    const quotes = await prisma.quote.findMany({\n      where: {\n        createdAt: {\n          gte: startDate,\n          lte: now,\n        },\n      },\n      include: {\n        items: true,\n      },\n    });\n\n    const totalRevenue = quotes.reduce((total, quote) => {\n      const quoteTotal = quote.items.reduce((quoteSum, item) => {\n        return quoteSum + (item.quantity * item.unitPrice);\n      }, 0);\n      return total + quoteTotal;\n    }, 0);\n\n    return {\n      totalRevenue,\n      quotesCount: quotes.length,\n      averageQuoteValue: quotes.length > 0 ? totalRevenue / quotes.length : 0,\n    };\n  }\n\n  // Get real user activity metrics\n  static async getUserActivityMetrics() {\n    const now = new Date();\n    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    // Active users in last 30 days (based on quote creation)\n    const activeUsers = await prisma.user.findMany({\n      where: {\n        quotes: {\n          some: {\n            createdAt: {\n              gte: last30Days,\n            },\n          },\n        },\n      },\n      include: {\n        _count: {\n          select: {\n            quotes: true,\n          },\n        },\n      },\n    });\n\n    // Total users by role\n    const usersByRole = await prisma.user.groupBy({\n      by: ['role'],\n      _count: {\n        id: true,\n      },\n    });\n\n    return {\n      activeUsers: activeUsers.length,\n      totalUsers: await prisma.user.count(),\n      usersByRole: usersByRole.reduce((acc, item) => {\n        acc[item.role] = item._count.id;\n        return acc;\n      }, {} as Record<string, number>),\n      topActiveUsers: activeUsers\n        .sort((a, b) => b._count.quotes - a._count.quotes)\n        .slice(0, 5)\n        .map(user => ({\n          id: user.id,\n          name: `${user.firstname} ${user.lastname}`,\n          quotesCount: user._count.quotes,\n        })),\n    };\n  }\n\n  // Get real product performance data\n  static async getProductPerformance() {\n    // Products with most quotes\n    const productQuotes = await prisma.quoteItem.groupBy({\n      by: ['productId'],\n      _count: {\n        id: true,\n      },\n      _sum: {\n        quantity: true,\n        unitPrice: true,\n      },\n      orderBy: {\n        _count: {\n          id: 'desc',\n        },\n      },\n      take: 10,\n    });\n\n    // Get product details\n    const productIds = productQuotes.map(pq => pq.productId).filter(Boolean);\n    const products = await prisma.product.findMany({\n      where: {\n        id: {\n          in: productIds as string[],\n        },\n      },\n    });\n\n    const productPerformance = productQuotes.map(pq => {\n      const product = products.find(p => p.id === pq.productId);\n      return {\n        productId: pq.productId,\n        productName: product?.name || 'Unknown Product',\n        productReference: product?.reference || 'N/A',\n        quotesCount: pq._count.id,\n        totalQuantity: pq._sum.quantity || 0,\n        totalRevenue: (pq._sum.quantity || 0) * (pq._sum.unitPrice || 0),\n      };\n    });\n\n    return {\n      topProducts: productPerformance,\n      totalProducts: await prisma.product.count(),\n      totalCategories: await prisma.category.count(),\n    };\n  }\n\n  // Get real sales trends\n  static async getSalesTrends(days: number = 30) {\n    const now = new Date();\n    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);\n\n    const dailyQuotes = await prisma.quote.findMany({\n      where: {\n        createdAt: {\n          gte: startDate,\n          lte: now,\n        },\n      },\n      include: {\n        items: true,\n      },\n      orderBy: {\n        createdAt: 'asc',\n      },\n    });\n\n    // Group by day\n    const dailyData = new Map<string, { quotes: number; revenue: number }>();\n    \n    for (let i = 0; i < days; i++) {\n      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);\n      const dateKey = date.toISOString().split('T')[0];\n      dailyData.set(dateKey, { quotes: 0, revenue: 0 });\n    }\n\n    dailyQuotes.forEach(quote => {\n      const dateKey = quote.createdAt.toISOString().split('T')[0];\n      const existing = dailyData.get(dateKey) || { quotes: 0, revenue: 0 };\n      \n      const quoteRevenue = quote.items.reduce((sum, item) => {\n        return sum + (item.quantity * item.unitPrice);\n      }, 0);\n\n      dailyData.set(dateKey, {\n        quotes: existing.quotes + 1,\n        revenue: existing.revenue + quoteRevenue,\n      });\n    });\n\n    return Array.from(dailyData.entries()).map(([date, data]) => ({\n      date,\n      quotes: data.quotes,\n      revenue: data.revenue,\n    }));\n  }\n\n  // Get comprehensive dashboard data\n  static async getDashboardData() {\n    const [\n      revenueData,\n      userMetrics,\n      productPerformance,\n      salesTrends,\n    ] = await Promise.all([\n      this.calculateRealRevenue('month'),\n      this.getUserActivityMetrics(),\n      this.getProductPerformance(),\n      this.getSalesTrends(30),\n    ]);\n\n    // Calculate growth rates\n    const lastMonthRevenue = await this.calculateRealRevenue('month');\n    const currentMonthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);\n    const lastMonthStart = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);\n    const lastMonthEnd = new Date(new Date().getFullYear(), new Date().getMonth(), 0);\n\n    const lastMonthQuotes = await prisma.quote.findMany({\n      where: {\n        createdAt: {\n          gte: lastMonthStart,\n          lte: lastMonthEnd,\n        },\n      },\n      include: {\n        items: true,\n      },\n    });\n\n    const lastMonthRevenueTotal = lastMonthQuotes.reduce((total, quote) => {\n      return total + quote.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);\n    }, 0);\n\n    const revenueGrowth = lastMonthRevenueTotal > 0 \n      ? ((revenueData.totalRevenue - lastMonthRevenueTotal) / lastMonthRevenueTotal) * 100 \n      : 0;\n\n    return {\n      revenue: {\n        ...revenueData,\n        growth: revenueGrowth,\n      },\n      users: userMetrics,\n      products: productPerformance,\n      trends: salesTrends,\n      summary: {\n        totalQuotes: await prisma.quote.count(),\n        totalProducts: await prisma.product.count(),\n        totalUsers: await prisma.user.count(),\n        totalCategories: await prisma.category.count(),\n      },\n    };\n  }\n\n  // Real-time data change notification\n  static async notifyDataChange(type: 'quote' | 'product' | 'user' | 'category', action: 'create' | 'update' | 'delete') {\n    // This would integrate with WebSocket or Server-Sent Events for real-time updates\n    console.log(`📊 Data change notification: ${type} ${action}`);\n    \n    // In a real implementation, you would:\n    // 1. Emit WebSocket event to connected clients\n    // 2. Update cached dashboard data\n    // 3. Trigger real-time UI updates\n    \n    return {\n      type,\n      action,\n      timestamp: new Date().toISOString(),\n    };\n  }\n}\n\n// Cache management for performance\nclass DataCache {\n  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();\n\n  static set(key: string, data: any, ttlMinutes: number = 5) {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl: ttlMinutes * 60 * 1000,\n    });\n  }\n\n  static get(key: string) {\n    const cached = this.cache.get(key);\n    if (!cached) return null;\n\n    if (Date.now() - cached.timestamp > cached.ttl) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return cached.data;\n  }\n\n  static clear() {\n    this.cache.clear();\n  }\n}\n\nexport { DataCache };\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM;IAEX,gDAAgD;IAChD,aAAa,qBAAqB,YAA+C,OAAO,EAAE;QACxF,MAAM,MAAM,IAAI;QAChB,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;gBACnE;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;gBACxD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;gBACxD;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,GAAG;gBAC3C;QACJ;QAEA,gCAAgC;QAChC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,KAAK;gBACP;YACF;YACA,SAAS;gBACP,OAAO;YACT;QACF;QAEA,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,OAAO;YACzC,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU;gBAC/C,OAAO,WAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;YACnD,GAAG;YACH,OAAO,QAAQ;QACjB,GAAG;QAEH,OAAO;YACL;YACA,aAAa,OAAO,MAAM;YAC1B,mBAAmB,OAAO,MAAM,GAAG,IAAI,eAAe,OAAO,MAAM,GAAG;QACxE;IACF;IAEA,iCAAiC;IACjC,aAAa,yBAAyB;QACpC,MAAM,MAAM,IAAI;QAChB,MAAM,aAAa,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;QAEhE,yDAAyD;QACzD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,QAAQ;oBACN,MAAM;wBACJ,WAAW;4BACT,KAAK;wBACP;oBACF;gBACF;YACF;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,sBAAsB;QACtB,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;YAC5C,IAAI;gBAAC;aAAO;YACZ,QAAQ;gBACN,IAAI;YACN;QACF;QAEA,OAAO;YACL,aAAa,YAAY,MAAM;YAC/B,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;YACnC,aAAa,YAAY,MAAM,CAAC,CAAC,KAAK;gBACpC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,MAAM,CAAC,EAAE;gBAC/B,OAAO;YACT,GAAG,CAAC;YACJ,gBAAgB,YACb,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,MAAM,EAChD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACZ,IAAI,KAAK,EAAE;oBACX,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC1C,aAAa,KAAK,MAAM,CAAC,MAAM;gBACjC,CAAC;QACL;IACF;IAEA,oCAAoC;IACpC,aAAa,wBAAwB;QACnC,4BAA4B;QAC5B,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,OAAO,CAAC;YACnD,IAAI;gBAAC;aAAY;YACjB,QAAQ;gBACN,IAAI;YACN;YACA,MAAM;gBACJ,UAAU;gBACV,WAAW;YACb;YACA,SAAS;gBACP,QAAQ;oBACN,IAAI;gBACN;YACF;YACA,MAAM;QACR;QAEA,sBAAsB;QACtB,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA,KAAM,GAAG,SAAS,EAAE,MAAM,CAAC;QAChE,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,IAAI;oBACF,IAAI;gBACN;YACF;QACF;QAEA,MAAM,qBAAqB,cAAc,GAAG,CAAC,CAAA;YAC3C,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,GAAG,SAAS;YACxD,OAAO;gBACL,WAAW,GAAG,SAAS;gBACvB,aAAa,SAAS,QAAQ;gBAC9B,kBAAkB,SAAS,aAAa;gBACxC,aAAa,GAAG,MAAM,CAAC,EAAE;gBACzB,eAAe,GAAG,IAAI,CAAC,QAAQ,IAAI;gBACnC,cAAc,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;YACjE;QACF;QAEA,OAAO;YACL,aAAa;YACb,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK;YACzC,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK;QAC9C;IACF;IAEA,wBAAwB;IACxB,aAAa,eAAe,OAAe,EAAE,EAAE;QAC7C,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;QAEjE,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC9C,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,KAAK;gBACP;YACF;YACA,SAAS;gBACP,OAAO;YACT;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,eAAe;QACf,MAAM,YAAY,IAAI;QAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,MAAM,OAAO,IAAI,KAAK,UAAU,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YAC/D,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD,UAAU,GAAG,CAAC,SAAS;gBAAE,QAAQ;gBAAG,SAAS;YAAE;QACjD;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,MAAM,UAAU,MAAM,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3D,MAAM,WAAW,UAAU,GAAG,CAAC,YAAY;gBAAE,QAAQ;gBAAG,SAAS;YAAE;YAEnE,MAAM,eAAe,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;gBAC5C,OAAO,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS;YAC9C,GAAG;YAEH,UAAU,GAAG,CAAC,SAAS;gBACrB,QAAQ,SAAS,MAAM,GAAG;gBAC1B,SAAS,SAAS,OAAO,GAAG;YAC9B;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,UAAU,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBAC5D;gBACA,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO;YACvB,CAAC;IACH;IAEA,mCAAmC;IACnC,aAAa,mBAAmB;QAC9B,MAAM,CACJ,aACA,aACA,oBACA,YACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,CAAC,oBAAoB,CAAC;YAC1B,IAAI,CAAC,sBAAsB;YAC3B,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,cAAc,CAAC;SACrB;QAED,yBAAyB;QACzB,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC;QACzD,MAAM,oBAAoB,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI;QACpF,MAAM,iBAAiB,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,KAAK,GAAG;QACrF,MAAM,eAAe,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI;QAE/E,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAClD,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,KAAK;gBACP;YACF;YACA,SAAS;gBACP,OAAO;YACT;QACF;QAEA,MAAM,wBAAwB,gBAAgB,MAAM,CAAC,CAAC,OAAO;YAC3D,OAAO,QAAQ,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,EAAG;QAC3F,GAAG;QAEH,MAAM,gBAAgB,wBAAwB,IAC1C,AAAC,CAAC,YAAY,YAAY,GAAG,qBAAqB,IAAI,wBAAyB,MAC/E;QAEJ,OAAO;YACL,SAAS;gBACP,GAAG,WAAW;gBACd,QAAQ;YACV;YACA,OAAO;YACP,UAAU;YACV,QAAQ;YACR,SAAS;gBACP,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;gBACrC,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK;gBACzC,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;gBACnC,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK;YAC9C;QACF;IACF;IAEA,qCAAqC;IACrC,aAAa,iBAAiB,IAA+C,EAAE,MAAsC,EAAE;QACrH,kFAAkF;QAClF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,EAAE,QAAQ;QAE5D,uCAAuC;QACvC,+CAA+C;QAC/C,kCAAkC;QAClC,kCAAkC;QAElC,OAAO;YACL;YACA;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF;AAEA,mCAAmC;AACnC,MAAM;IACJ,OAAe,QAAQ,IAAI,MAA6D;IAExF,OAAO,IAAI,GAAW,EAAE,IAAS,EAAE,aAAqB,CAAC,EAAE;QACzD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB,KAAK,aAAa,KAAK;QACzB;IACF;IAEA,OAAO,IAAI,GAAW,EAAE;QACtB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,OAAO,GAAG,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,OAAO,QAAQ;QACb,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;AACF", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/middleware/security.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport rateLimit from 'express-rate-limit';\nimport helmet from 'helmet';\nimport { z } from 'zod';\n\n// Rate limiting configuration\nconst rateLimitConfig = {\n  windowMs: 15 * 60 * 1000, // 15 minutes\n  max: 100, // limit each IP to 100 requests per windowMs\n  message: 'Too many requests from this IP, please try again later.',\n  standardHeaders: true,\n  legacyHeaders: false,\n};\n\n// Create rate limiter\nexport const createRateLimit = (options?: Partial<typeof rateLimitConfig>) => {\n  return rateLimit({\n    ...rateLimitConfig,\n    ...options,\n  });\n};\n\n// Strict rate limiting for auth endpoints\nexport const authRateLimit = createRateLimit({\n  windowMs: 15 * 60 * 1000, // 15 minutes\n  max: 5, // limit each IP to 5 requests per windowMs for auth\n  message: 'Too many authentication attempts, please try again later.',\n});\n\n// File upload rate limiting\nexport const uploadRateLimit = createRateLimit({\n  windowMs: 60 * 1000, // 1 minute\n  max: 10, // limit each IP to 10 uploads per minute\n  message: 'Too many file uploads, please try again later.',\n});\n\n// Input validation schemas\nexport const schemas = {\n  email: z.string().email().max(255),\n  password: z.string().min(8).max(128),\n  name: z.string().min(1).max(100).regex(/^[a-zA-ZÀ-ÿ\\s'-]+$/),\n  phone: z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/),\n  text: z.string().max(1000),\n  id: z.string().uuid(),\n  reference: z.string().min(1).max(50).regex(/^[a-zA-Z0-9-_]+$/),\n  url: z.string().url().max(500),\n};\n\n// XSS protection\nexport const sanitizeInput = (input: string): string => {\n  return input\n    .replace(/[<>]/g, '') // Remove < and >\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .replace(/script/gi, '') // Remove script tags\n    .trim();\n};\n\n// SQL injection protection\nexport const sanitizeForDatabase = (input: string): string => {\n  return input\n    .replace(/['\";\\\\]/g, '') // Remove dangerous SQL characters\n    .replace(/--/g, '') // Remove SQL comments\n    .replace(/\\/\\*/g, '') // Remove SQL block comments\n    .replace(/\\*\\//g, '')\n    .trim();\n};\n\n// CSRF token generation and validation\nexport const generateCSRFToken = (): string => {\n  return require('crypto').randomBytes(32).toString('hex');\n};\n\nexport const validateCSRFToken = (token: string, sessionToken: string): boolean => {\n  return token === sessionToken && token.length === 64;\n};\n\n// Security headers middleware\nexport const securityHeaders = (request: NextRequest) => {\n  const response = NextResponse.next();\n  \n  // Security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n  \n  // Content Security Policy\n  const csp = [\n    \"default-src 'self'\",\n    \"script-src 'self' 'unsafe-inline' 'unsafe-eval'\",\n    \"style-src 'self' 'unsafe-inline'\",\n    \"img-src 'self' data: blob:\",\n    \"font-src 'self'\",\n    \"connect-src 'self'\",\n    \"media-src 'self'\",\n    \"object-src 'none'\",\n    \"base-uri 'self'\",\n    \"form-action 'self'\",\n    \"frame-ancestors 'none'\",\n    \"upgrade-insecure-requests\"\n  ].join('; ');\n  \n  response.headers.set('Content-Security-Policy', csp);\n  \n  return response;\n};\n\n// Input validation middleware\nexport const validateInput = (schema: z.ZodSchema, data: any) => {\n  try {\n    return schema.parse(data);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`);\n    }\n    throw error;\n  }\n};\n\n// File upload security validation\nexport const validateFileUpload = (file: File) => {\n  const maxSize = 25 * 1024 * 1024; // 25MB\n  const allowedTypes = [\n    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',\n    'application/pdf', 'application/msword',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    'application/vnd.ms-excel',\n    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    'text/plain', 'text/csv'\n  ];\n  \n  const dangerousExtensions = [\n    'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'jsp'\n  ];\n  \n  // Check file size\n  if (file.size > maxSize) {\n    throw new Error('File too large. Maximum size is 25MB');\n  }\n  \n  // Check file type\n  if (!allowedTypes.includes(file.type)) {\n    throw new Error('File type not allowed');\n  }\n  \n  // Check file extension\n  const extension = file.name.split('.').pop()?.toLowerCase();\n  if (dangerousExtensions.includes(extension || '')) {\n    throw new Error('File extension not allowed for security reasons');\n  }\n  \n  return true;\n};\n\n// Authentication token validation\nexport const validateAuthToken = (token: string): boolean => {\n  if (!token || token.length < 10) {\n    return false;\n  }\n  \n  // Check for suspicious patterns\n  const suspiciousPatterns = [\n    /[<>]/g, // HTML tags\n    /javascript:/gi, // JavaScript protocol\n    /data:/gi, // Data protocol\n    /vbscript:/gi, // VBScript protocol\n  ];\n  \n  return !suspiciousPatterns.some(pattern => pattern.test(token));\n};\n\n// IP address validation and blocking\nconst blockedIPs = new Set<string>();\nconst suspiciousActivity = new Map<string, number>();\n\nexport const checkIPSecurity = (ip: string): boolean => {\n  // Check if IP is blocked\n  if (blockedIPs.has(ip)) {\n    return false;\n  }\n  \n  // Track suspicious activity\n  const attempts = suspiciousActivity.get(ip) || 0;\n  if (attempts > 50) { // Block after 50 suspicious attempts\n    blockedIPs.add(ip);\n    return false;\n  }\n  \n  return true;\n};\n\nexport const reportSuspiciousActivity = (ip: string) => {\n  const current = suspiciousActivity.get(ip) || 0;\n  suspiciousActivity.set(ip, current + 1);\n};\n\n// Password security validation\nexport const validatePasswordStrength = (password: string): boolean => {\n  const minLength = 8;\n  const hasUpperCase = /[A-Z]/.test(password);\n  const hasLowerCase = /[a-z]/.test(password);\n  const hasNumbers = /\\d/.test(password);\n  const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n  \n  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;\n};\n\n// Session security\nexport const generateSecureSessionId = (): string => {\n  return require('crypto').randomBytes(32).toString('hex');\n};\n\nexport const validateSessionSecurity = (sessionData: any): boolean => {\n  // Check session expiration\n  if (sessionData.expiresAt && new Date() > new Date(sessionData.expiresAt)) {\n    return false;\n  }\n  \n  // Check session integrity\n  if (!sessionData.userId || !sessionData.createdAt) {\n    return false;\n  }\n  \n  return true;\n};\n\n// Error handling that doesn't leak sensitive information\nexport const createSecureError = (message: string, statusCode: number = 400) => {\n  // Don't expose internal errors in production\n  const isProduction = process.env.NODE_ENV === 'production';\n  const safeMessage = isProduction ? 'An error occurred' : message;\n  \n  return NextResponse.json(\n    { error: safeMessage },\n    { status: statusCode }\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;AAKA,8BAA8B;AAC9B,MAAM,kBAAkB;IACtB,UAAU,KAAK,KAAK;IACpB,KAAK;IACL,SAAS;IACT,iBAAiB;IACjB,eAAe;AACjB;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,UAAU;QACf,GAAG,eAAe;QAClB,GAAG,OAAO;IACZ;AACF;AAGO,MAAM,gBAAgB,gBAAgB;IAC3C,UAAU,KAAK,KAAK;IACpB,KAAK;IACL,SAAS;AACX;AAGO,MAAM,kBAAkB,gBAAgB;IAC7C,UAAU,KAAK;IACf,KAAK;IACL,SAAS;AACX;AAGO,MAAM,UAAU;IACrB,OAAO,EAAE,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC;IAC9B,UAAU,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAChC,MAAM,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC;IACvC,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,EAAE,MAAM,GAAG,GAAG,CAAC;IACrB,IAAI,EAAE,MAAM,GAAG,IAAI;IACnB,WAAW,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC;IAC3C,KAAK,EAAE,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5B;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MACJ,OAAO,CAAC,SAAS,IAAI,iBAAiB;KACtC,OAAO,CAAC,iBAAiB,IAAI,8BAA8B;KAC3D,OAAO,CAAC,YAAY,IAAI,wBAAwB;KAChD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,IAAI;AACT;AAGO,MAAM,sBAAsB,CAAC;IAClC,OAAO,MACJ,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,OAAO,CAAC,OAAO,IAAI,sBAAsB;KACzC,OAAO,CAAC,SAAS,IAAI,4BAA4B;KACjD,OAAO,CAAC,SAAS,IACjB,IAAI;AACT;AAGO,MAAM,oBAAoB;IAC/B,OAAO,uEAAkB,WAAW,CAAC,IAAI,QAAQ,CAAC;AACpD;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,UAAU,gBAAgB,MAAM,MAAM,KAAK;AACpD;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI;IAElC,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IACzC,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,0BAA0B;IAC1B,MAAM,MAAM;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,SAAS,OAAO,CAAC,GAAG,CAAC,2BAA2B;IAEhD,OAAO;AACT;AAGO,MAAM,gBAAgB,CAAC,QAAqB;IACjD,IAAI;QACF,OAAO,OAAO,KAAK,CAAC;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,EAAE,QAAQ,EAAE;YAC/B,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;QACpF;QACA,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QACnB;QAAc;QAAa;QAAa;QAAa;QACrD;QAAmB;QACnB;QACA;QACA;QACA;QAAc;KACf;IAED,MAAM,sBAAsB;QAC1B;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAM;QAAO;QAAO;QAAO;KAC7E;IAED,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,MAAM,IAAI,MAAM;IAClB;IAEA,kBAAkB;IAClB,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,MAAM,IAAI,MAAM;IAClB;IAEA,uBAAuB;IACvB,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IAC9C,IAAI,oBAAoB,QAAQ,CAAC,aAAa,KAAK;QACjD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,IAAI;QAC/B,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;KACD;IAED,OAAO,CAAC,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAC1D;AAEA,qCAAqC;AACrC,MAAM,aAAa,IAAI;AACvB,MAAM,qBAAqB,IAAI;AAExB,MAAM,kBAAkB,CAAC;IAC9B,yBAAyB;IACzB,IAAI,WAAW,GAAG,CAAC,KAAK;QACtB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,WAAW,mBAAmB,GAAG,CAAC,OAAO;IAC/C,IAAI,WAAW,IAAI;QACjB,WAAW,GAAG,CAAC;QACf,OAAO;IACT;IAEA,OAAO;AACT;AAEO,MAAM,2BAA2B,CAAC;IACvC,MAAM,UAAU,mBAAmB,GAAG,CAAC,OAAO;IAC9C,mBAAmB,GAAG,CAAC,IAAI,UAAU;AACvC;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,YAAY;IAClB,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,aAAa,KAAK,IAAI,CAAC;IAC7B,MAAM,iBAAiB,yBAAyB,IAAI,CAAC;IAErD,OAAO,SAAS,MAAM,IAAI,aAAa,gBAAgB,gBAAgB,cAAc;AACvF;AAGO,MAAM,0BAA0B;IACrC,OAAO,uEAAkB,WAAW,CAAC,IAAI,QAAQ,CAAC;AACpD;AAEO,MAAM,0BAA0B,CAAC;IACtC,2BAA2B;IAC3B,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,YAAY,SAAS,GAAG;QACzE,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,YAAY,SAAS,EAAE;QACjD,OAAO;IACT;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAAC,SAAiB,aAAqB,GAAG;IACzE,6CAA6C;IAC7C,MAAM,eAAe,oDAAyB;IAC9C,MAAM,cAAc,6EAAqC;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO;IAAY,GACrB;QAAE,QAAQ;IAAW;AAEzB", "debugId": null}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/admin/dashboard/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth-options';\nimport { RealTimeDataService, DataCache } from '@/lib/realtime-data';\nimport { checkIPSecurity, createSecureError } from '@/middleware/security';\n\n// GET /api/admin/dashboard - Get dashboard statistics\nexport async function GET(req: NextRequest) {\n  try {\n    // Security check\n    const clientIP = req.headers.get('x-forwarded-for') ||\n                    req.headers.get('x-real-ip') ||\n                    'unknown';\n\n    if (!checkIPSecurity(clientIP)) {\n      return createSecureError('Access denied', 403);\n    }\n\n    // Vérifier l'authentification et les autorisations\n    const session = await getServerSession(authOptions);\n\n    if (!session || !session.user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Vérifier si l'utilisateur est un administrateur\n    const user = await prisma.user.findUnique({\n      where: { id: session.user.id },\n      select: { role: true }\n    });\n\n    if (!user || user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });\n    }\n\n    // Check cache first for performance\n    const cacheKey = 'admin-dashboard-data';\n    let dashboardData = DataCache.get(cacheKey);\n\n    if (!dashboardData) {\n      // Get real-time dashboard data\n      dashboardData = await RealTimeDataService.getDashboardData();\n\n      // Cache for 2 minutes\n      DataCache.set(cacheKey, dashboardData, 2);\n    }\n\n    // Format response to match existing frontend expectations\n    const response = {\n      totalClients: dashboardData.users.usersByRole.CLIENT || 0,\n      totalOrders: dashboardData.summary.totalQuotes,\n      totalProducts: dashboardData.summary.totalProducts,\n      totalRevenue: Math.round(dashboardData.revenue.totalRevenue),\n      totalCommercials: dashboardData.users.usersByRole.COMMERCIAL || 0,\n      totalSuppliers: await prisma.brand.count(), // Keep brands as suppliers\n      totalCategories: dashboardData.summary.totalCategories,\n      totalBrands: await prisma.brand.count(),\n\n      // Additional real-time data\n      revenueGrowth: dashboardData.revenue.growth,\n      activeUsers: dashboardData.users.activeUsers,\n      averageQuoteValue: Math.round(dashboardData.revenue.averageQuoteValue),\n      topProducts: dashboardData.products.topProducts.slice(0, 5),\n      salesTrends: dashboardData.trends.slice(-7), // Last 7 days\n\n      // Performance metrics\n      quotesThisMonth: dashboardData.revenue.quotesCount,\n      lastUpdated: new Date().toISOString(),\n    };\n\n    return NextResponse.json(response);\n  } catch (error: any) {\n    console.error('Error fetching dashboard data:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch dashboard data' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,iBAAiB;QACjB,MAAM,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,sBACjB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAChB;QAEhB,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;YAC9B,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB;QAC5C;QAEA,mDAAmD;QACnD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,EAAE;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,kDAAkD;QAClD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,IAAI,CAAC,EAAE;YAAC;YAC7B,QAAQ;gBAAE,MAAM;YAAK;QACvB;QAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAY,GAAG;gBAAE,QAAQ;YAAI;QACjE;QAEA,oCAAoC;QACpC,MAAM,WAAW;QACjB,IAAI,gBAAgB,gIAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QAElC,IAAI,CAAC,eAAe;YAClB,+BAA+B;YAC/B,gBAAgB,MAAM,gIAAA,CAAA,sBAAmB,CAAC,gBAAgB;YAE1D,sBAAsB;YACtB,gIAAA,CAAA,YAAS,CAAC,GAAG,CAAC,UAAU,eAAe;QACzC;QAEA,0DAA0D;QAC1D,MAAM,WAAW;YACf,cAAc,cAAc,KAAK,CAAC,WAAW,CAAC,MAAM,IAAI;YACxD,aAAa,cAAc,OAAO,CAAC,WAAW;YAC9C,eAAe,cAAc,OAAO,CAAC,aAAa;YAClD,cAAc,KAAK,KAAK,CAAC,cAAc,OAAO,CAAC,YAAY;YAC3D,kBAAkB,cAAc,KAAK,CAAC,WAAW,CAAC,UAAU,IAAI;YAChE,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;YACxC,iBAAiB,cAAc,OAAO,CAAC,eAAe;YACtD,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;YAErC,4BAA4B;YAC5B,eAAe,cAAc,OAAO,CAAC,MAAM;YAC3C,aAAa,cAAc,KAAK,CAAC,WAAW;YAC5C,mBAAmB,KAAK,KAAK,CAAC,cAAc,OAAO,CAAC,iBAAiB;YACrE,aAAa,cAAc,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG;YACzD,aAAa,cAAc,MAAM,CAAC,KAAK,CAAC,CAAC;YAEzC,sBAAsB;YACtB,iBAAiB,cAAc,OAAO,CAAC,WAAW;YAClD,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAiC,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}