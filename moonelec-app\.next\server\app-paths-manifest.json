{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/notifications/route": "app/api/notifications/route.js", "/api/brands/route": "app/api/brands/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/quotes/route": "app/api/quotes/route.js", "/api/products/route": "app/api/products/route.js", "/api/products/[id]/route": "app/api/products/[id]/route.js", "/account/quotes/page": "app/account/quotes/page.js", "/products/page": "app/products/page.js", "/page": "app/page.js", "/products/[id]/page": "app/products/[id]/page.js", "/admin/products/[id]/page": "app/admin/products/[id]/page.js", "/admin/products/new/page": "app/admin/products/new/page.js"}