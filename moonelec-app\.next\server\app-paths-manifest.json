{"/api/brands/route": "app/api/brands/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/quotes/route": "app/api/quotes/route.js", "/api/sales-reports/route": "app/api/sales-reports/route.js", "/page": "app/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/admin/quotes/page": "app/admin/quotes/page.js", "/admin/reports/page": "app/admin/reports/page.js"}