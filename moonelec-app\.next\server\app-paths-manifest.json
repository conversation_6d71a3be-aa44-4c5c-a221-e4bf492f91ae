{"/admin/quotes/page": "app/admin/quotes/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/chat/conversations/route": "app/api/chat/conversations/route.js", "/api/chat/messages/route": "app/api/chat/messages/route.js", "/api/chat/upload/route": "app/api/chat/upload/route.js", "/api/chat/users/route": "app/api/chat/users/route.js", "/api/quotes/route": "app/api/quotes/route.js", "/auth/signin/page": "app/auth/signin/page.js"}