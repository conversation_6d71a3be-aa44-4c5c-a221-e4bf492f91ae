# Database Configuration - SQLite for Development
DATABASE_URL="file:./dev.db"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# Application Configuration
NODE_ENV="development"

# File Upload Configuration
MAX_FILE_SIZE=26214400
UPLOAD_DIR="./public/uploads"

# Security Configuration
JWT_SECRET="your-jwt-secret-here-change-in-production"
ENCRYPTION_KEY="your-encryption-key-here-change-in-production"

# API Configuration
API_BASE_URL="http://localhost:3000/api"

# Email Configuration (Optional)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# External Services (Optional)
# OPENAI_API_KEY="your-openai-api-key"
# HUGGINGFACE_API_KEY="your-huggingface-api-key"

# Development Settings
NEXT_TELEMETRY_DISABLED=1
