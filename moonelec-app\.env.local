# Database Configuration - MySQL Production
DATABASE_URL="mysql://root@localhost:3306/moonelec_db"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="moonelec-dev-secret-2024-very-long-and-secure-key-for-development"

# Application Configuration
NODE_ENV="development"

# File Upload Configuration
MAX_FILE_SIZE=26214400
UPLOAD_DIR="./public/uploads"

# Security Configuration
JWT_SECRET="moonelec-jwt-secret-2024-development-key-very-secure"
ENCRYPTION_KEY="moonelec-encryption-key-2024-development-very-long-secure"

# API Configuration
API_BASE_URL="http://localhost:3000/api"

# Email Configuration (Optional)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# External Services (Optional)
# OPENAI_API_KEY="your-openai-api-key"
# HUGGINGFACE_API_KEY="your-huggingface-api-key"

# Development Settings
NEXT_TELEMETRY_DISABLED=1
