import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { findUserByUsername, verifyPassword } from '@/lib/auth';
import { NextAuthOptions } from 'next-auth';

console.log('🔧 Auth options module loaded');

console.log('🔧 Creating auth options...');

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: "Nom d'utilisateur", type: 'text' },
        password: { label: 'Mot de passe', type: 'password' },
      },
      async authorize(credentials) {
        console.log('🔍 AUTHORIZE FUNCTION CALLED!');
        console.log('🔍 Auth attempt:', { username: credentials?.username, hasPassword: !!credentials?.password });

        if (!credentials?.username || !credentials?.password) {
          console.log('❌ Missing credentials');
          return null;
        }

        try {
          // Rechercher l'utilisateur par nom d'utilisateur
          console.log('🔍 Looking for user:', credentials.username);
          const user = await findUserByUsername(credentials.username);

          // Vérifier si l'utilisateur existe
          if (!user) {
            console.log('❌ User not found:', credentials.username);
            return null;
          }

          console.log('✅ User found:', { id: user.id, username: user.username, role: user.role });

          // Vérifier le mot de passe
          console.log('🔍 Verifying password...');
          const isPasswordValid = await verifyPassword(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            console.log('❌ Invalid password for user:', credentials.username);
            return null;
          }

          console.log('✅ Password valid for user:', credentials.username);

          // Retourner les données de l'utilisateur sans le mot de passe
          const userObject = {
            id: user.id,
            email: user.email,
            username: user.username,
            name: `${user.firstname} ${user.lastname}`,
            firstname: user.firstname,
            lastname: user.lastname,
            role: user.role,
            clientId: user.client?.id,
            commercialId: user.commercial?.id,
            adminId: user.admin?.id,
          };

          console.log('✅ Returning user object:', userObject);
          return userObject;
        } catch (error) {
          console.error('❌ Auth error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.username = user.username;
        token.role = user.role;
        token.firstname = user.firstname;
        token.lastname = user.lastname;
        token.clientId = user.clientId;
        token.commercialId = user.commercialId;
        token.adminId = user.adminId;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.username = token.username as string;
        session.user.role = token.role as string;
        session.user.firstname = token.firstname as string;
        session.user.lastname = token.lastname as string;
        session.user.clientId = token.clientId as string | undefined;
        session.user.commercialId = token.commercialId as string | undefined;
        session.user.adminId = token.adminId as string | undefined;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 jours
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: true,
};
