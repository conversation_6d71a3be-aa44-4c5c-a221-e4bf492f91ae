import CredentialsProvider from 'next-auth/providers/credentials';
import { findUserByUsername, verifyPassword } from '@/lib/auth';
import { NextAuthOptions } from 'next-auth';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: "Nom d'utilisateur", type: 'text' },
        password: { label: 'Mot de passe', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          // Rechercher l'utilisateur par nom d'utilisateur
          const user = await findUserByUsername(credentials.username);

          // Vérifier si l'utilisateur existe
          if (!user) {
            return null;
          }

          // Vérifier le mot de passe
          const isPasswordValid = await verifyPassword(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            return null;
          }

          // Retourner les données de l'utilisateur sans le mot de passe
          return {
            id: user.id,
            email: user.email,
            username: user.username,
            name: `${user.firstname} ${user.lastname}`,
            firstname: user.firstname,
            lastname: user.lastname,
            role: user.role,
            clientId: user.client?.id,
            commercialId: user.commercial?.id,
            adminId: user.admin?.id,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.username = user.username;
        token.role = user.role;
        token.firstname = user.firstname;
        token.lastname = user.lastname;
        token.clientId = user.clientId;
        token.commercialId = user.commercialId;
        token.adminId = user.adminId;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.username = token.username as string;
        session.user.role = token.role as string;
        session.user.firstname = token.firstname as string;
        session.user.lastname = token.lastname as string;
        session.user.clientId = token.clientId as string | undefined;
        session.user.commercialId = token.commercialId as string | undefined;
        session.user.adminId = token.adminId as string | undefined;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 jours
  },
  secret: process.env.NEXTAUTH_SECRET,
};
