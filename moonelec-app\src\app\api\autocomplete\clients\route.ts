import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { prisma } from '@/lib/prisma';
import { checkIPSecurity, createSecureError } from '@/middleware/security';

// GET /api/autocomplete/clients - Smart client suggestions for forms
export async function GET(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    // Only COMMERCIAL and ADMIN users can access client data
    if (user.role !== 'COMMERCIAL' && user.role !== 'ADMIN') {
      return NextResponse.json({ 
        error: 'Access denied - only commercial and admin users can access client data' 
      }, { status: 403 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const field = url.searchParams.get('field') || 'denomination'; // denomination, name, city, email
    const limit = parseInt(url.searchParams.get('limit') || '10');

    if (!query || query.length < 2) {
      return NextResponse.json({
        suggestions: [],
        message: 'Query too short, minimum 2 characters required'
      });
    }

    console.log('🔍 Client autocomplete request:', {
      user: user.id,
      query,
      field,
      limit
    });

    let suggestions: any[] = [];

    try {
      switch (field) {
        case 'denomination':
          suggestions = await getDenominationSuggestions(query, limit, user);
          break;
        case 'name':
          suggestions = await getNameSuggestions(query, limit, user);
          break;
        case 'city':
          suggestions = await getCitySuggestions(query, limit, user);
          break;
        case 'email':
          suggestions = await getEmailSuggestions(query, limit, user);
          break;
        case 'all':
          suggestions = await getAllClientSuggestions(query, limit, user);
          break;
        default:
          suggestions = await getDenominationSuggestions(query, limit, user);
      }

      console.log('✅ Found client suggestions:', suggestions.length);

      return NextResponse.json({
        suggestions,
        query,
        field,
        count: suggestions.length,
        metadata: {
          searchType: 'client-autocomplete',
          responseTime: Date.now()
        }
      });

    } catch (searchError: any) {
      console.error('❌ Client search error:', searchError);
      return NextResponse.json({
        suggestions: [],
        error: 'Search failed',
        query,
        field
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Client autocomplete API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      suggestions: []
    }, { status: 500 });
  }
}

// Get denomination suggestions from sales reports and clients
async function getDenominationSuggestions(query: string, limit: number, user: any) {
  // Get unique denominations from sales reports
  const salesReports = await prisma.salesreport.findMany({
    where: {
      denomination: {
        contains: query,
        mode: 'insensitive'
      },
      // If user is COMMERCIAL, only show their reports
      ...(user.role === 'COMMERCIAL' && { commercialId: user.commercialId })
    },
    select: {
      denomination: true,
      name: true,
      city: true,
      visitDate: true
    },
    distinct: ['denomination'],
    take: limit,
    orderBy: {
      visitDate: 'desc'
    }
  });

  return salesReports.map(report => ({
    type: 'denomination',
    value: report.denomination,
    label: `${report.denomination} - ${report.city}`,
    details: {
      denomination: report.denomination,
      contactName: report.name,
      city: report.city,
      lastVisit: report.visitDate
    },
    source: 'sales_reports',
    matchType: 'contains'
  }));
}

// Get contact name suggestions
async function getNameSuggestions(query: string, limit: number, user: any) {
  const salesReports = await prisma.salesreport.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      },
      ...(user.role === 'COMMERCIAL' && { commercialId: user.commercialId })
    },
    select: {
      denomination: true,
      name: true,
      city: true,
      visitDate: true
    },
    distinct: ['name'],
    take: limit,
    orderBy: {
      visitDate: 'desc'
    }
  });

  return salesReports.map(report => ({
    type: 'name',
    value: report.name,
    label: `${report.name} (${report.denomination})`,
    details: {
      denomination: report.denomination,
      contactName: report.name,
      city: report.city,
      lastVisit: report.visitDate
    },
    source: 'sales_reports',
    matchType: 'contains'
  }));
}

// Get city suggestions
async function getCitySuggestions(query: string, limit: number, user: any) {
  const salesReports = await prisma.salesreport.findMany({
    where: {
      city: {
        contains: query,
        mode: 'insensitive'
      },
      ...(user.role === 'COMMERCIAL' && { commercialId: user.commercialId })
    },
    select: {
      city: true,
      denomination: true,
      visitDate: true
    },
    distinct: ['city'],
    take: limit,
    orderBy: {
      visitDate: 'desc'
    }
  });

  // Also get cities from user profiles
  const users = await prisma.user.findMany({
    where: {
      role: 'CLIENT',
      // Add city field search if it exists in user model
    },
    select: {
      id: true,
      firstname: true,
      lastname: true,
      email: true
    },
    take: Math.floor(limit / 2)
  });

  const reportSuggestions = salesReports.map(report => ({
    type: 'city',
    value: report.city,
    label: `${report.city}`,
    details: {
      city: report.city,
      lastVisit: report.visitDate,
      clientCount: 1 // Could be aggregated
    },
    source: 'sales_reports',
    matchType: 'contains'
  }));

  return reportSuggestions;
}

// Get email suggestions from users
async function getEmailSuggestions(query: string, limit: number, user: any) {
  const users = await prisma.user.findMany({
    where: {
      email: {
        contains: query,
        mode: 'insensitive'
      },
      role: 'CLIENT'
    },
    select: {
      id: true,
      email: true,
      firstname: true,
      lastname: true,
      client: {
        select: {
          id: true
        }
      }
    },
    take: limit,
    orderBy: {
      email: 'asc'
    }
  });

  return users.map(userRecord => ({
    type: 'email',
    value: userRecord.email,
    label: `${userRecord.email} (${userRecord.firstname} ${userRecord.lastname})`,
    details: {
      email: userRecord.email,
      name: `${userRecord.firstname} ${userRecord.lastname}`,
      clientId: userRecord.client?.id
    },
    source: 'users',
    matchType: 'contains'
  }));
}

// Get suggestions from all client fields
async function getAllClientSuggestions(query: string, limit: number, user: any) {
  const [denominations, names, cities] = await Promise.all([
    getDenominationSuggestions(query, Math.floor(limit / 3), user),
    getNameSuggestions(query, Math.floor(limit / 3), user),
    getCitySuggestions(query, Math.floor(limit / 3), user)
  ]);

  return [
    ...denominations,
    ...names,
    ...cities
  ].slice(0, limit);
}
