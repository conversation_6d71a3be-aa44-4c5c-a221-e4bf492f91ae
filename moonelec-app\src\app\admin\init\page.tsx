'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, FaUserShield, FaCheck, FaTimes } from 'react-icons/fa';

export default function AdminInitPage() {
  const [token, setToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!token) {
      setResult({
        success: false,
        message: 'Veuillez entrer un token',
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult({
          success: true,
          message: data.message || 'Administrateur initialisé avec succès',
        });
      } else {
        setResult({
          success: false,
          message: data.error || 'Une erreur est survenue',
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Une erreur est survenue lors de la communication avec le serveur',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
      >
        <div className="bg-primary p-6 text-white text-center">
          <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaUserShield className="text-4xl" />
          </div>
          <h1 className="text-2xl font-bold">Initialisation de l'Administrateur</h1>
          <p className="mt-2 text-white/80">
            Créez le compte administrateur par défaut pour Moonelec
          </p>
        </div>

        <div className="p-6">
          {result ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`p-4 rounded-lg mb-6 ${
                result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-full ${
                  result.success ? 'bg-green-200' : 'bg-red-200'
                } flex items-center justify-center mr-3`}>
                  {result.success ? <FaCheck /> : <FaTimes />}
                </div>
                <div>
                  <h3 className="font-semibold">
                    {result.success ? 'Succès' : 'Erreur'}
                  </h3>
                  <p>{result.message}</p>
                </div>
              </div>

              {result.success && (
                <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    <strong>Nom d'utilisateur:</strong> hicham.ezzamzami
                  </p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    <strong>Mot de passe:</strong> 123456
                  </p>
                  <p className="text-xs text-red-500 mt-2">
                    Veuillez changer ce mot de passe après votre première connexion!
                  </p>
                </div>
              )}

              <div className="mt-6 text-center">
                <button
                  onClick={() => setResult(null)}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                >
                  {result.success ? 'Aller à la connexion' : 'Réessayer'}
                </button>
              </div>
            </motion.div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="token" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Token de sécurité
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaKey className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="token"
                    value={token}
                    onChange={(e) => setToken(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                    placeholder="Entrez le token de sécurité"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Le token par défaut est "moonelec-admin-init-token"
                </p>
              </div>

              <motion.button
                type="submit"
                disabled={isLoading}
                className={`w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
                  isLoading ? 'opacity-70 cursor-not-allowed' : ''
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Initialisation en cours...
                  </>
                ) : (
                  'Initialiser l\'administrateur'
                )}
              </motion.button>
            </form>
          )}
        </div>
      </motion.div>
    </div>
  );
}
