{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["const warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,SAAS,IAAI;AACnB,SAAS,UAAU,OAAO;IACtB,OAAO,OAAO,GAAG,CAAC;AACtB;AACA,SAAS,SAAS,SAAS,EAAE,OAAO,EAAE,OAAO;IACzC,IAAI,aAAa,OAAO,GAAG,CAAC,UACxB;IACJ,QAAQ,IAAI,CAAC;IACb,IAAI,SACA,QAAQ,IAAI,CAAC;IACjB,OAAO,GAAG,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,MAAM,OAAO,CAAC,MAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/global-config.mjs"], "sourcesContent": ["const MotionGlobalConfig = {};\n\nexport { MotionGlobalConfig };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/array.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,GACvB,IAAI,IAAI,CAAC;AACjB;AACA,SAAS,WAAW,GAAG,EAAE,IAAI;IACzB,MAAM,QAAQ,IAAI,OAAO,CAAC;IAC1B,IAAI,QAAQ,CAAC,GACT,IAAI,MAAM,CAAC,OAAO;AAC1B;AACA,0BAA0B;AAC1B,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE,SAAS,EAAE,OAAO;IAC1C,MAAM,aAAa,YAAY,IAAI,IAAI,MAAM,GAAG,YAAY;IAC5D,IAAI,cAAc,KAAK,aAAa,IAAI,MAAM,EAAE;QAC5C,MAAM,WAAW,UAAU,IAAI,IAAI,MAAM,GAAG,UAAU;QACtD,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW;QACrC,IAAI,MAAM,CAAC,UAAU,GAAG;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/subscription-manager.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;IACA,IAAI,OAAO,EAAE;QACT,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;QAClC,OAAO,IAAM,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;IAChD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAClD,IAAI,CAAC,kBACD;QACJ,IAAI,qBAAqB,GAAG;YACxB;;aAEC,GACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;QAChC,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC;;;iBAGC,GACD,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBACrC,WAAW,QAAQ,GAAG,GAAG;YAC7B;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;IACpC;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/velocity-per-second.mjs"], "sourcesContent": ["/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AACA,SAAS,kBAAkB,QAAQ,EAAE,aAAa;IAC9C,OAAO,gBAAgB,WAAW,CAAC,OAAO,aAAa,IAAI;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["let warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "names": [], "mappings": ";;;;AAEI;AAFJ,IAAI,UAAU,KAAQ;AACtB,IAAI,YAAY,KAAQ;AACxB,wCAA2C;IACvC,UAAU,CAAC,OAAO;QACd,IAAI,CAAC,SAAS,OAAO,YAAY,aAAa;YAC1C,QAAQ,IAAI,CAAC;QACjB;IACJ;IACA,YAAY,CAAC,OAAO;QAChB,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/pipe.mjs"], "sourcesContent": ["/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,MAAM,mBAAmB,CAAC,GAAG,IAAM,CAAC,IAAM,EAAE,EAAE;AAC9C,MAAM,OAAO,CAAC,GAAG,eAAiB,aAAa,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC,KAAK,KAAK;IACrB,IAAI,IAAI,KACJ,OAAO;IACX,IAAI,IAAI,KACJ,OAAO;IACX,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GACD,sBAAsB;;;;AACtB,MAAM,wBAAwB,CAAC,UAAY,UAAU;AACrD,sBAAsB,GACtB,MAAM,wBAAwB,CAAC,eAAiB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs"], "sourcesContent": ["import { noop } from '../noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from Gaë<PERSON>eau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticiably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;;;;;;;;;;;;AAgBA,GACA,iEAAiE;AACjE,MAAM,aAAa,CAAC,GAAG,IAAI,KAAO,CAAC,CAAC,CAAC,MAAM,MAAM,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,IACvG;AACJ,MAAM,uBAAuB;AAC7B,MAAM,2BAA2B;AACjC,SAAS,gBAAgB,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG;IACxD,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI;IACR,GAAG;QACC,WAAW,aAAa,CAAC,aAAa,UAAU,IAAI;QACpD,WAAW,WAAW,UAAU,KAAK,OAAO;QAC5C,IAAI,WAAW,KAAK;YAChB,aAAa;QACjB,OACK;YACD,aAAa;QACjB;IACJ,QAAS,KAAK,GAAG,CAAC,YAAY,wBAC1B,EAAE,IAAI,yBAA0B;IACpC,OAAO;AACX;AACA,SAAS,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,qDAAqD;IACrD,IAAI,QAAQ,OAAO,QAAQ,KACvB,OAAO,yJAAA,CAAA,OAAI;IACf,MAAM,WAAW,CAAC,KAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK;IACxD,wDAAwD;IACxD,OAAO,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,IAAI,KAAK;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/ease.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = /*@__PURE__*/ cubicBezier(0.42, 0, 1, 1);\nconst easeOut = /*@__PURE__*/ cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = /*@__PURE__*/ cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG,GAAG;AACrD,MAAM,UAAU,WAAW,GAAG,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,GAAG,GAAG,MAAM;AACtD,MAAM,YAAY,WAAW,GAAG,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs"], "sourcesContent": ["const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n"], "names": [], "mappings": ";;;AAAA,MAAM,gBAAgB,CAAC;IACnB,OAAO,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI,CAAC,EAAE,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n"], "names": [], "mappings": "AAAA,oFAAoF;AACpF,iEAAiE;;;;AACjE,MAAM,eAAe,CAAC,SAAW,CAAC,IAAM,KAAK,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF,6BAA6B;;;;AAC7B,MAAM,gBAAgB,CAAC,SAAW,CAAC,IAAM,IAAI,OAAO,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/back.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,WAAW,GAAG,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM,MAAM;AAC5D,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,mLAAA,CAAA,gBAAa,AAAD,EAAE;AAC3C,MAAM,YAAY,WAAW,GAAG,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/anticipate.mjs"], "sourcesContent": ["import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/circ.mjs"], "sourcesContent": ["import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM,SAAS,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC;AAC7C,MAAM,UAAU,CAAA,GAAA,mLAAA,CAAA,gBAAa,AAAD,EAAE;AAC9B,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC,SAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/easing/utils/map.mjs"], "sourcesContent": ["import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`);\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`);\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,eAAe;IACjB,QAAQ,yJAAA,CAAA,OAAI;IACZ,QAAA,mKAAA,CAAA,SAAM;IACN,WAAA,mKAAA,CAAA,YAAS;IACT,SAAA,mKAAA,CAAA,UAAO;IACP,QAAA,mKAAA,CAAA,SAAM;IACN,WAAA,mKAAA,CAAA,YAAS;IACT,SAAA,mKAAA,CAAA,UAAO;IACP,QAAA,mKAAA,CAAA,SAAM;IACN,WAAA,mKAAA,CAAA,YAAS;IACT,SAAA,mKAAA,CAAA,UAAO;IACP,YAAA,yKAAA,CAAA,aAAU;AACd;AACA,MAAM,gBAAgB,CAAC;IACnB,OAAO,OAAO,WAAW;AAC7B;AACA,MAAM,6BAA6B,CAAC;IAChC,IAAI,CAAA,GAAA,kMAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;QAChC,kDAAkD;QAClD,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,WAAW,MAAM,KAAK,GAAG,CAAC,uDAAuD,CAAC;QAC5F,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG;QACzB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,IAAI,IAAI,IAAI;IACnC,OACK,IAAI,cAAc,aAAa;QAChC,yBAAyB;QACzB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,WAAW,KAAK,WAAW,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;QACvF,OAAO,YAAY,CAAC,WAAW;IACnC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/progress.mjs"], "sourcesContent": ["/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA,GACA,sBAAsB;;;AACtB,MAAM,WAAW,CAAC,MAAM,IAAI;IACxB,MAAM,mBAAmB,KAAK;IAC9B,OAAO,qBAAqB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/memo.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,SAAS,KAAK,QAAQ;IAClB,IAAI;IACJ,OAAO;QACH,IAAI,WAAW,WACX,SAAS;QACb,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/is-numerical-string.mjs"], "sourcesContent": ["/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,CAAC,IAAM,+BAA+B,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/motion-utils/dist/es/is-zero-value-string.mjs"], "sourcesContent": ["/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,CAAC,IAAM,cAAc,IAAI,CAAC", "ignoreList": [0], "debugId": null}}]}