const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Set environment variable for SQLite
process.env.DATABASE_URL = 'file:./dev.db';

const prisma = new PrismaClient();

async function testAuth() {
  try {
    console.log('🔍 Testing authentication...');

    // Test 1: Check if users exist
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        firstname: true,
        lastname: true,
        role: true,
      }
    });

    console.log('✅ Users found:', users.length);
    users.forEach(user => {
      console.log(`  - ${user.username} (${user.email}) - ${user.role}`);
    });

    // Test 2: Find admin user specifically
    const adminUser = await prisma.user.findUnique({
      where: { username: 'admin' },
      include: {
        client: true,
        commercial: true,
        admin: true,
      },
    });

    if (adminUser) {
      console.log('✅ Admin user found:', {
        id: adminUser.id,
        username: adminUser.username,
        email: adminUser.email,
        role: adminUser.role,
        hasAdminProfile: !!adminUser.admin,
      });

      // Test 3: Verify password
      const isPasswordValid = await bcrypt.compare('admin123', adminUser.password);
      console.log('✅ Password verification:', isPasswordValid ? 'VALID' : 'INVALID');

      if (!isPasswordValid) {
        console.log('❌ Password hash in DB:', adminUser.password);
        console.log('❌ Expected password: admin123');
        
        // Let's rehash the password and update it
        const newHash = await bcrypt.hash('admin123', 10);
        console.log('🔧 New hash generated:', newHash);
        
        await prisma.user.update({
          where: { id: adminUser.id },
          data: { password: newHash }
        });
        
        console.log('✅ Password updated in database');
        
        // Test again
        const updatedUser = await prisma.user.findUnique({
          where: { username: 'admin' }
        });
        
        const isNewPasswordValid = await bcrypt.compare('admin123', updatedUser.password);
        console.log('✅ New password verification:', isNewPasswordValid ? 'VALID' : 'INVALID');
      }
    } else {
      console.log('❌ Admin user not found');
    }

    // Test 4: Test all users' passwords
    console.log('\n🔍 Testing all user passwords...');
    for (const user of users) {
      const fullUser = await prisma.user.findUnique({
        where: { id: user.id }
      });
      
      const isValid = await bcrypt.compare('admin123', fullUser.password);
      console.log(`  - ${user.username}: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
      
      if (!isValid) {
        // Fix the password
        const newHash = await bcrypt.hash('admin123', 10);
        await prisma.user.update({
          where: { id: user.id },
          data: { password: newHash }
        });
        console.log(`    🔧 Fixed password for ${user.username}`);
      }
    }

    console.log('\n🎉 Authentication test completed!');

  } catch (error) {
    console.error('❌ Error testing auth:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuth();
