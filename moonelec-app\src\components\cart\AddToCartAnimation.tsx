'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

interface AddToCartAnimationProps {
  isAnimating: boolean;
  productImage: string | null;
  productName: string;
  startPosition: { x: number; y: number } | null;
  endPosition: { x: number; y: number } | null;
  onAnimationComplete: () => void;
}

export default function AddToCartAnimation({
  isAnimating,
  productImage,
  productName,
  startPosition,
  endPosition,
  onAnimationComplete
}: AddToCartAnimationProps) {
  const [animationKey, setAnimationKey] = useState(0);

  // Réinitialiser l'animation à chaque nouvelle demande
  useEffect(() => {
    if (isAnimating) {
      setAnimationKey(prev => prev + 1);
    }
  }, [isAnimating]);

  // Si les positions ne sont pas définies, ne pas afficher l'animation
  if (!isAnimating || !startPosition || !endPosition) {
    return null;
  }

  return (
    <AnimatePresence>
      {isAnimating && (
        <motion.div
          key={animationKey}
          initial={{ 
            opacity: 1, 
            scale: 1,
            x: startPosition.x, 
            y: startPosition.y,
            zIndex: 9999
          }}
          animate={{ 
            opacity: [1, 1, 0.8, 0],
            scale: [1, 0.8, 0.6, 0.3],
            x: [startPosition.x, startPosition.x + (endPosition.x - startPosition.x) * 0.1, startPosition.x + (endPosition.x - startPosition.x) * 0.4, endPosition.x],
            y: [startPosition.y, startPosition.y - 100, startPosition.y - 50, endPosition.y]
          }}
          transition={{ 
            duration: 0.8,
            ease: "easeInOut",
            times: [0, 0.3, 0.7, 1]
          }}
          onAnimationComplete={onAnimationComplete}
          className="fixed pointer-events-none"
          style={{ width: '60px', height: '60px' }}
        >
          <div className="w-full h-full bg-white dark:bg-gray-800 rounded-full shadow-lg overflow-hidden border-2 border-primary">
            {productImage ? (
              <Image
                src={productImage}
                alt={productName}
                fill
                style={{ objectFit: 'contain' }}
                className="p-1"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-primary text-xs font-bold">
                {productName.substring(0, 2).toUpperCase()}
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
