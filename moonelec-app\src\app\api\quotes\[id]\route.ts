import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getQuoteById, updateQuote, deleteQuote } from '@/lib/quotes';
import { QuoteStatus } from '@prisma/client';
import { authOptions } from '@/lib/auth-options';
import { extractId } from '@/lib/route-utils';

// GET /api/quotes/[id] - Récupérer un devis par son ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extraire l'ID de l'URL plutôt que des paramètres
    const url = req.url;
    const segments = url.split('/');
    const id = segments[segments.length - 1];

    const quote = await getQuoteById(id);

    if (!quote) {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    // Vérifier que l'utilisateur a le droit d'accéder à ce devis
    if (
      session.user.role === 'CLIENT' &&
      session.user.clientId !== quote.clientId
    ) {
      return NextResponse.json(
        { error: 'Unauthorized to access this quote' },
        { status: 403 }
      );
    }

    return NextResponse.json(quote);
  } catch (error: any) {
    console.error('Error fetching quote:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch quote' },
      { status: 500 }
    );
  }
}

// PATCH /api/quotes/[id] - Mettre à jour un devis
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extraire l'ID de l'URL plutôt que des paramètres
    const url = req.url;
    const segments = url.split('/');
    const id = segments[segments.length - 1];

    const body = await req.json();

    // Récupérer le devis pour vérifier les autorisations
    const quote = await getQuoteById(id);

    if (!quote) {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    // Vérifier les autorisations selon le rôle
    if (session.user.role === 'CLIENT') {
      // Les clients ne peuvent mettre à jour que leurs propres devis et seulement certains champs
      if (session.user.clientId !== quote.clientId) {
        return NextResponse.json(
          { error: 'Unauthorized to update this quote' },
          { status: 403 }
        );
      }

      // Les clients ne peuvent mettre à jour que les notes et le statut (uniquement pour accepter ou refuser)
      const allowedUpdates: any = {};

      if (body.notes !== undefined) {
        allowedUpdates.notes = body.notes;
      }

      if (body.status !== undefined) {
        // Les clients ne peuvent que accepter ou refuser un devis
        if (
          quote.status === QuoteStatus.PENDING &&
          (body.status === QuoteStatus.APPROVED || body.status === QuoteStatus.REJECTED)
        ) {
          allowedUpdates.status = body.status;
        } else {
          return NextResponse.json(
            { error: 'Unauthorized to change quote status' },
            { status: 403 }
          );
        }
      }

      // Mettre à jour le devis avec les champs autorisés
      const updatedQuote = await updateQuote(id, allowedUpdates);
      return NextResponse.json(updatedQuote);
    } else if (session.user.role === 'ADMIN' || session.user.role === 'COMMERCIAL') {
      // Les admins et commerciaux peuvent mettre à jour tous les champs
      const updatedQuote = await updateQuote(id, {
        status: body.status,
        notes: body.notes,
        validUntil: body.validUntil,
        totalAmount: body.totalAmount,
        pdfUrl: body.pdfUrl,
        items: body.items,
      });

      return NextResponse.json(updatedQuote);
    } else {
      return NextResponse.json(
        { error: 'Unauthorized to update quotes' },
        { status: 403 }
      );
    }
  } catch (error: any) {
    console.error('Error updating quote:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update quote' },
      { status: 500 }
    );
  }
}

// DELETE /api/quotes/[id] - Supprimer un devis
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Seuls les administrateurs peuvent supprimer des devis
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized to delete quotes' },
        { status: 403 }
      );
    }

    // Extraire l'ID de l'URL plutôt que des paramètres
    const url = req.url;
    const segments = url.split('/');
    const id = segments[segments.length - 1];

    // Vérifier que le devis existe
    const quote = await getQuoteById(id);

    if (!quote) {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    // Supprimer le devis
    await deleteQuote(id);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting quote:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete quote' },
      { status: 500 }
    );
  }
}
