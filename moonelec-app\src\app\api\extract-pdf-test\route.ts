import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import pdf from 'pdf-parse';
import { authOptions } from '@/lib/auth-options';

// Types pour les données extraites
interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
}

// POST /api/extract-pdf-test - Extract product data from PDF using simple text parsing (no AI)
export async function POST(req: NextRequest) {
  try {
    // Vérification de l'authentification
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Récupération du fichier PDF
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No PDF file provided' },
        { status: 400 }
      );
    }

    // Vérification du type de fichier
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    console.log('📄 Processing PDF:', file.name, 'Size:', file.size);

    // Conversion du fichier en buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extraction du texte du PDF
    console.log('🔍 Extracting text from PDF...');
    const pdfData = await pdf(buffer);
    const extractedText = pdfData.text;

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text could be extracted from the PDF' },
        { status: 400 }
      );
    }

    console.log('📝 Extracted text length:', extractedText.length);
    console.log('📝 First 500 characters:', extractedText.substring(0, 500));

    // Extraction simple sans IA (pour test)
    const lines = extractedText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    // Tentative d'extraction basique
    let productName = '';
    let reference = '';
    let description = '';
    const characteristics: Record<string, string> = {};

    // Recherche du nom du produit (souvent en première ligne ou après certains mots-clés)
    for (let i = 0; i < Math.min(10, lines.length); i++) {
      const line = lines[i];
      if (line.length > 5 && line.length < 100 && !line.includes('©') && !line.includes('www.')) {
        if (!productName && (line.includes('PRODUCT') || line.includes('PRODUIT') || i < 3)) {
          productName = line;
          break;
        }
      }
    }

    // Recherche de la référence
    for (const line of lines) {
      if (line.match(/ref|référence|reference|code|model|modèle/i)) {
        const refMatch = line.match(/([A-Z0-9\-\.]+)/);
        if (refMatch) {
          reference = refMatch[1];
          break;
        }
      }
    }

    // Recherche de caractéristiques techniques
    for (const line of lines) {
      // Tension
      if (line.match(/tension|voltage|volt/i)) {
        const voltMatch = line.match(/(\d+(?:\.\d+)?)\s*(?:v|volt)/i);
        if (voltMatch) {
          characteristics['Tension'] = voltMatch[1] + 'V';
        }
      }
      
      // Courant
      if (line.match(/courant|current|ampère|amp/i)) {
        const ampMatch = line.match(/(\d+(?:\.\d+)?)\s*(?:a|amp|ampère)/i);
        if (ampMatch) {
          characteristics['Courant'] = ampMatch[1] + 'A';
        }
      }
      
      // Puissance
      if (line.match(/puissance|power|watt/i)) {
        const wattMatch = line.match(/(\d+(?:\.\d+)?)\s*(?:w|watt)/i);
        if (wattMatch) {
          characteristics['Puissance'] = wattMatch[1] + 'W';
        }
      }
      
      // Dimensions
      if (line.match(/dimension|taille|size/i)) {
        const dimMatch = line.match(/(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)/i);
        if (dimMatch) {
          characteristics['Dimensions'] = `${dimMatch[1]} x ${dimMatch[2]}`;
        }
      }
    }

    // Description basique (premières lignes significatives)
    const descLines = lines.slice(0, 5).filter(line => 
      line.length > 20 && 
      !line.includes('©') && 
      !line.includes('www.') &&
      line !== productName
    );
    description = descLines.join(' ').substring(0, 200);

    // Valeurs par défaut si rien n'est trouvé
    if (!productName) productName = 'Produit extrait du PDF';
    if (!reference) reference = 'REF-' + Date.now();
    if (!description) description = 'Description extraite automatiquement du PDF';

    const extractedData: ExtractedProductData = {
      productName,
      reference,
      description,
      characteristics
    };

    console.log('✅ Extracted data (simple parsing):', extractedData);

    return NextResponse.json({
      success: true,
      data: extractedData,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        textLength: extractedText.length,
        extractedAt: new Date().toISOString(),
        method: 'simple-parsing'
      },
      debug: {
        firstLines: lines.slice(0, 10),
        extractedText: extractedText.substring(0, 1000)
      }
    });

  } catch (error) {
    console.error('❌ PDF extraction error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error during PDF extraction',
        debug: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/extract-pdf-test - Get test extraction info
export async function GET() {
  try {
    return NextResponse.json({
      available: true,
      method: 'simple-text-parsing',
      supportedFormats: ['application/pdf'],
      maxFileSize: '10MB',
      features: [
        'Basic product name extraction',
        'Reference number detection',
        'Simple characteristics extraction',
        'No AI required'
      ]
    });

  } catch (error) {
    console.error('Error getting test extraction info:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
