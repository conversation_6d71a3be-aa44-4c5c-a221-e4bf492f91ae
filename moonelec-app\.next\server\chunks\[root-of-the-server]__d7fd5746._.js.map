{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      console.log('🔐 No valid authorization header found');\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n    console.log('🔐 Mobile token received (length):', token.length);\n\n    // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET\n    let decoded: any;\n    try {\n      const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';\n      console.log('🔐 Trying JWT_SECRET for token verification');\n      decoded = jwt.verify(token, jwtSecret) as any;\n      console.log('🔐 Token decoded successfully with JWT_SECRET:', { userId: decoded.userId, role: decoded.role });\n    } catch (jwtError) {\n      console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');\n      try {\n        const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';\n        decoded = jwt.verify(token, nextAuthSecret) as any;\n        console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', { userId: decoded.userId, role: decoded.role });\n      } catch (nextAuthError) {\n        console.error('🔐 Both JWT secrets failed:', { jwtError: jwtError.message, nextAuthError: nextAuthError.message });\n        throw new Error('Invalid token signature');\n      }\n    }\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n      }\n    });\n\n    if (!user) {\n      console.log('🔐 User not found:', { found: !!user });\n      return null;\n    }\n\n    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n\n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') ||\n         userAgent.includes('Expo') ||\n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  // Always try to verify mobile token if Authorization header is present\n  const authHeader = request.headers.get('authorization');\n  if (authHeader?.startsWith('Bearer ')) {\n    return await verifyMobileToken(request);\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QACjE,QAAQ,GAAG,CAAC,sCAAsC,MAAM,MAAM;QAE9D,kFAAkF;QAClF,IAAI;QACJ,IAAI;YACF,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI;YAC5C,QAAQ,GAAG,CAAC;YACZ,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAC5B,QAAQ,GAAG,CAAC,kDAAkD;gBAAE,QAAQ,QAAQ,MAAM;gBAAE,MAAM,QAAQ,IAAI;YAAC;QAC7G,EAAE,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,eAAe,IAAI;gBACtD,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;gBAC5B,QAAQ,GAAG,CAAC,uDAAuD;oBAAE,QAAQ,QAAQ,MAAM;oBAAE,MAAM,QAAQ,IAAI;gBAAC;YAClH,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,+BAA+B;oBAAE,UAAU,SAAS,OAAO;oBAAE,eAAe,cAAc,OAAO;gBAAC;gBAChH,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QAEA,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,sBAAsB;gBAAE,OAAO,CAAC,CAAC;YAAK;YAClD,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,8CAA8C;YAAE,IAAI,KAAK,EAAE;YAAE,MAAM,KAAK,IAAI;QAAC;QACzF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,uEAAuE;IACvE,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,YAAY,WAAW,YAAY;QACrC,OAAO,MAAM,kBAAkB;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/chat/conversations/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { prisma } from '@/lib/prisma';\nimport { authOptions } from '@/lib/auth-options';\nimport { getMobileUserFromRequest } from '@/lib/mobile-auth';\n\n// GET /api/chat/conversations - Get all conversations for current user\nexport async function GET(req: NextRequest) {\n  try {\n    // Check for mobile authentication first\n    const mobileUser = await getMobileUserFromRequest(req);\n    const session = await getServerSession(authOptions);\n\n    if (!mobileUser && !session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const user = mobileUser || session?.user;\n    \n    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {\n      return NextResponse.json({ error: 'Access denied' }, { status: 403 });\n    }\n\n    let conversations;\n\n    if (user.role === 'ADMIN') {\n      // Admin can see all conversations\n      conversations = await prisma.chatconversation.findMany({\n        include: {\n          admin: {\n            include: {\n              user: {\n                select: {\n                  id: true,\n                  firstname: true,\n                  lastname: true,\n                  email: true,\n                }\n              }\n            }\n          },\n          commercial: {\n            include: {\n              user: {\n                select: {\n                  id: true,\n                  firstname: true,\n                  lastname: true,\n                  email: true,\n                }\n              }\n            }\n          },\n          messages: {\n            orderBy: { createdAt: 'desc' },\n            take: 1,\n          }\n        },\n        orderBy: { lastMessageAt: 'desc' }\n      });\n    } else {\n      // Commercial can only see their conversations\n      const commercial = await prisma.commercial.findUnique({\n        where: { userId: user.id }\n      });\n\n      if (!commercial) {\n        return NextResponse.json({ error: 'Commercial profile not found' }, { status: 404 });\n      }\n\n      conversations = await prisma.chatconversation.findMany({\n        where: { commercialId: commercial.id },\n        include: {\n          admin: {\n            include: {\n              user: {\n                select: {\n                  id: true,\n                  firstname: true,\n                  lastname: true,\n                  email: true,\n                }\n              }\n            }\n          },\n          commercial: {\n            include: {\n              user: {\n                select: {\n                  id: true,\n                  firstname: true,\n                  lastname: true,\n                  email: true,\n                }\n              }\n            }\n          },\n          messages: {\n            orderBy: { createdAt: 'desc' },\n            take: 1,\n          }\n        },\n        orderBy: { lastMessageAt: 'desc' }\n      });\n    }\n\n    return NextResponse.json({ conversations });\n\n  } catch (error) {\n    console.error('Error fetching conversations:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/chat/conversations - Create new conversation\nexport async function POST(req: NextRequest) {\n  try {\n    const mobileUser = await getMobileUserFromRequest(req);\n    const session = await getServerSession(authOptions);\n\n    if (!mobileUser && !session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const user = mobileUser || session?.user;\n    \n    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {\n      return NextResponse.json({ error: 'Access denied' }, { status: 403 });\n    }\n\n    const { participantId } = await req.json();\n\n    if (!participantId) {\n      return NextResponse.json({ error: 'Participant ID is required' }, { status: 400 });\n    }\n\n    let adminId: string;\n    let commercialId: string;\n\n    if (user.role === 'ADMIN') {\n      const admin = await prisma.admin.findUnique({\n        where: { userId: user.id }\n      });\n      \n      if (!admin) {\n        return NextResponse.json({ error: 'Admin profile not found' }, { status: 404 });\n      }\n      \n      adminId = admin.id;\n      commercialId = participantId;\n    } else {\n      const commercial = await prisma.commercial.findUnique({\n        where: { userId: user.id }\n      });\n      \n      if (!commercial) {\n        return NextResponse.json({ error: 'Commercial profile not found' }, { status: 404 });\n      }\n      \n      commercialId = commercial.id;\n      adminId = participantId;\n    }\n\n    // Check if conversation already exists\n    const existingConversation = await prisma.chatconversation.findUnique({\n      where: {\n        adminId_commercialId: {\n          adminId,\n          commercialId\n        }\n      }\n    });\n\n    if (existingConversation) {\n      return NextResponse.json({ conversation: existingConversation });\n    }\n\n    // Create new conversation\n    const conversation = await prisma.chatconversation.create({\n      data: {\n        adminId,\n        commercialId,\n      },\n      include: {\n        admin: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                firstname: true,\n                lastname: true,\n                email: true,\n              }\n            }\n          }\n        },\n        commercial: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                firstname: true,\n                lastname: true,\n                email: true,\n              }\n            }\n          }\n        }\n      }\n    });\n\n    return NextResponse.json({ conversation }, { status: 201 });\n\n  } catch (error) {\n    console.error('Error creating conversation:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,wCAAwC;QACxC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,cAAc,SAAS;QAEpC,IAAI,CAAC,QAAS,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,cAAe;YAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,IAAI;QAEJ,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,kCAAkC;YAClC,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBACrD,SAAS;oBACP,OAAO;wBACL,SAAS;4BACP,MAAM;gCACJ,QAAQ;oCACN,IAAI;oCACJ,WAAW;oCACX,UAAU;oCACV,OAAO;gCACT;4BACF;wBACF;oBACF;oBACA,YAAY;wBACV,SAAS;4BACP,MAAM;gCACJ,QAAQ;oCACN,IAAI;oCACJ,WAAW;oCACX,UAAU;oCACV,OAAO;gCACT;4BACF;wBACF;oBACF;oBACA,UAAU;wBACR,SAAS;4BAAE,WAAW;wBAAO;wBAC7B,MAAM;oBACR;gBACF;gBACA,SAAS;oBAAE,eAAe;gBAAO;YACnC;QACF,OAAO;YACL,8CAA8C;YAC9C,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;gBACpD,OAAO;oBAAE,QAAQ,KAAK,EAAE;gBAAC;YAC3B;YAEA,IAAI,CAAC,YAAY;gBACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA+B,GAAG;oBAAE,QAAQ;gBAAI;YACpF;YAEA,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBACrD,OAAO;oBAAE,cAAc,WAAW,EAAE;gBAAC;gBACrC,SAAS;oBACP,OAAO;wBACL,SAAS;4BACP,MAAM;gCACJ,QAAQ;oCACN,IAAI;oCACJ,WAAW;oCACX,UAAU;oCACV,OAAO;gCACT;4BACF;wBACF;oBACF;oBACA,YAAY;wBACV,SAAS;4BACP,MAAM;gCACJ,QAAQ;oCACN,IAAI;oCACJ,WAAW;oCACX,UAAU;oCACV,OAAO;gCACT;4BACF;wBACF;oBACF;oBACA,UAAU;wBACR,SAAS;4BAAE,WAAW;wBAAO;wBAC7B,MAAM;oBACR;gBACF;gBACA,SAAS;oBAAE,eAAe;gBAAO;YACnC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAc;IAE3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,cAAc,SAAS;QAEpC,IAAI,CAAC,QAAS,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,cAAe;YAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,IAAI,IAAI;QAExC,IAAI,CAAC,eAAe;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6B,GAAG;gBAAE,QAAQ;YAAI;QAClF;QAEA,IAAI;QACJ,IAAI;QAEJ,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC1C,OAAO;oBAAE,QAAQ,KAAK,EAAE;gBAAC;YAC3B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA0B,GAAG;oBAAE,QAAQ;gBAAI;YAC/E;YAEA,UAAU,MAAM,EAAE;YAClB,eAAe;QACjB,OAAO;YACL,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;gBACpD,OAAO;oBAAE,QAAQ,KAAK,EAAE;gBAAC;YAC3B;YAEA,IAAI,CAAC,YAAY;gBACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA+B,GAAG;oBAAE,QAAQ;gBAAI;YACpF;YAEA,eAAe,WAAW,EAAE;YAC5B,UAAU;QACZ;QAEA,uCAAuC;QACvC,MAAM,uBAAuB,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;YACpE,OAAO;gBACL,sBAAsB;oBACpB;oBACA;gBACF;YACF;QACF;QAEA,IAAI,sBAAsB;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,cAAc;YAAqB;QAChE;QAEA,0BAA0B;QAC1B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACxD,MAAM;gBACJ;gBACA;YACF;YACA,SAAS;gBACP,OAAO;oBACL,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;4BACT;wBACF;oBACF;gBACF;gBACA,YAAY;oBACV,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;4BACT;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAa,GAAG;YAAE,QAAQ;QAAI;IAE3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}