import { prisma } from './prisma';

/**
 * Génère un numéro de devis unique au format Q-YYYY-XXXX
 * où YYYY est l'année en cours et XXXX est un numéro séquentiel
 */
export async function generateQuoteNumber(): Promise<string> {
  const currentYear = new Date().getFullYear();
  
  // Trouver le dernier devis de l'année en cours
  const lastQuote = await prisma.quote.findFirst({
    where: {
      quoteNumber: {
        startsWith: `Q-${currentYear}-`,
      },
    },
    orderBy: {
      quoteNumber: 'desc',
    },
  });
  
  let sequentialNumber = 1;
  
  if (lastQuote) {
    // Extraire le numéro séquentiel du dernier devis
    const match = lastQuote.quoteNumber.match(/Q-\d{4}-(\d+)/);
    if (match && match[1]) {
      sequentialNumber = parseInt(match[1], 10) + 1;
    }
  }
  
  // Formater le numéro séquentiel avec des zéros en préfixe
  const formattedNumber = sequentialNumber.toString().padStart(4, '0');
  
  return `Q-${currentYear}-${formattedNumber}`;
}

/**
 * Génère un numéro de commande unique au format O-YYYY-XXXX
 * où YYYY est l'année en cours et XXXX est un numéro séquentiel
 */
export async function generateOrderNumber(): Promise<string> {
  const currentYear = new Date().getFullYear();
  
  // Trouver la dernière commande de l'année en cours
  const lastOrder = await prisma.order.findFirst({
    where: {
      orderNumber: {
        startsWith: `O-${currentYear}-`,
      },
    },
    orderBy: {
      orderNumber: 'desc',
    },
  });
  
  let sequentialNumber = 1;
  
  if (lastOrder) {
    // Extraire le numéro séquentiel de la dernière commande
    const match = lastOrder.orderNumber.match(/O-\d{4}-(\d+)/);
    if (match && match[1]) {
      sequentialNumber = parseInt(match[1], 10) + 1;
    }
  }
  
  // Formater le numéro séquentiel avec des zéros en préfixe
  const formattedNumber = sequentialNumber.toString().padStart(4, '0');
  
  return `O-${currentYear}-${formattedNumber}`;
}

/**
 * Formate un prix en MAD
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('fr-MA', {
    style: 'currency',
    currency: 'MAD',
    minimumFractionDigits: 2,
  }).format(price);
}

/**
 * Formate une date au format local
 */
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat('fr-MA', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(dateObj);
}

/**
 * Calcule la date d'expiration par défaut pour un devis (30 jours à partir d'aujourd'hui)
 */
export function getDefaultQuoteExpiryDate(): Date {
  const date = new Date();
  date.setDate(date.getDate() + 30);
  return date;
}
