{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/notifications.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { NotificationType } from '@prisma/client';\n\n/**\n * Crée une notification pour tous les administrateurs\n */\nexport async function createNotificationForAllAdmins(\n  type: NotificationType,\n  message: string,\n  quoteId?: string\n) {\n  try {\n    // Récupérer tous les administrateurs\n    const admins = await prisma.admin.findMany();\n\n    // Créer une notification pour chaque administrateur\n    const notifications = await Promise.all(\n      admins.map(admin =>\n        prisma.notification.create({\n          data: {\n            type,\n            message,\n            adminId: admin.id,\n            ...(quoteId && { quoteId }),\n          },\n        })\n      )\n    );\n\n    // Dans une application réelle, vous récupéreriez les abonnements push de chaque administrateur\n    // et vous enverriez une notification push à chacun d'eux\n    // Exemple :\n    /*\n    for (const admin of admins) {\n      // Récupérer les abonnements push de l'administrateur\n      const pushSubscriptions = await prisma.pushSubscription.findMany({\n        where: { adminId: admin.id },\n      });\n\n      // Envoyer une notification push à chaque abonnement\n      for (const subscription of pushSubscriptions) {\n        try {\n          await sendPushNotification(\n            JSON.parse(subscription.subscription),\n            {\n              id: notifications.find(n => n.adminId === admin.id)?.id,\n              type,\n              message,\n              quoteId,\n            }\n          );\n        } catch (error) {\n          console.error(`Erreur lors de l'envoi de la notification push à l'administrateur ${admin.id}:`, error);\n        }\n      }\n    }\n    */\n\n    return notifications;\n  } catch (error) {\n    console.error('Erreur lors de la création des notifications:', error);\n    throw error;\n  }\n}\n\n/**\n * Crée une notification pour un administrateur spécifique\n */\nexport async function createNotificationForAdmin(\n  adminId: string,\n  type: NotificationType,\n  message: string,\n  quoteId?: string\n) {\n  try {\n    const notification = await prisma.notification.create({\n      data: {\n        type,\n        message,\n        adminId,\n        ...(quoteId && { quoteId }),\n      },\n    });\n\n    return notification;\n  } catch (error) {\n    console.error('Erreur lors de la création de la notification:', error);\n    throw error;\n  }\n}\n\n/**\n * Récupère les notifications non lues pour un administrateur\n */\nexport async function getUnreadNotificationsForAdmin(adminId: string) {\n  try {\n    const notifications = await prisma.notification.findMany({\n      where: {\n        adminId,\n        isRead: false,\n      },\n      orderBy: {\n        createdAt: 'desc',\n      },\n      include: {\n        quote: {\n          select: {\n            quoteNumber: true,\n            client: {\n              include: {\n                user: {\n                  select: {\n                    firstname: true,\n                    lastname: true,\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    });\n\n    return notifications;\n  } catch (error) {\n    console.error('Erreur lors de la récupération des notifications:', error);\n    throw error;\n  }\n}\n\n/**\n * Marque une notification comme lue\n */\nexport async function markNotificationAsRead(notificationId: string) {\n  try {\n    const notification = await prisma.notification.update({\n      where: { id: notificationId },\n      data: { isRead: true },\n    });\n\n    return notification;\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour de la notification:', error);\n    throw error;\n  }\n}\n\n/**\n * Marque toutes les notifications d'un administrateur comme lues\n */\nexport async function markAllNotificationsAsRead(adminId: string) {\n  try {\n    const result = await prisma.notification.updateMany({\n      where: {\n        adminId,\n        isRead: false,\n      },\n      data: {\n        isRead: true,\n      },\n    });\n\n    return result;\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour des notifications:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAMO,eAAe,+BACpB,IAAsB,EACtB,OAAe,EACf,OAAgB;IAEhB,IAAI;QACF,qCAAqC;QACrC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAE1C,oDAAoD;QACpD,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,OAAO,GAAG,CAAC,CAAA,QACT,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACzB,MAAM;oBACJ;oBACA;oBACA,SAAS,MAAM,EAAE;oBACjB,GAAI,WAAW;wBAAE;oBAAQ,CAAC;gBAC5B;YACF;QAIJ,+FAA+F;QAC/F,yDAAyD;QACzD,YAAY;QACZ;;;;;;;;;;;;;;;;;;;;;;;;IAwBA,GAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,MAAM;IACR;AACF;AAKO,eAAe,2BACpB,OAAe,EACf,IAAsB,EACtB,OAAe,EACf,OAAgB;IAEhB,IAAI;QACF,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACpD,MAAM;gBACJ;gBACA;gBACA;gBACA,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,MAAM;IACR;AACF;AAKO,eAAe,+BAA+B,OAAe;IAClE,IAAI;QACF,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YACvD,OAAO;gBACL;gBACA,QAAQ;YACV;YACA,SAAS;gBACP,WAAW;YACb;YACA,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,aAAa;wBACb,QAAQ;4BACN,SAAS;gCACP,MAAM;oCACJ,QAAQ;wCACN,WAAW;wCACX,UAAU;oCACZ;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,cAAsB;IACjE,IAAI;QACF,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACpD,OAAO;gBAAE,IAAI;YAAe;YAC5B,MAAM;gBAAE,QAAQ;YAAK;QACvB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,MAAM;IACR;AACF;AAKO,eAAe,2BAA2B,OAAe;IAC9D,IAAI;QACF,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YAClD,OAAO;gBACL;gBACA,QAAQ;YACV;YACA,MAAM;gBACJ,QAAQ;YACV;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oDAAoD;QAClE,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/notifications/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth-options';\nimport { \n  getUnreadNotificationsForAdmin, \n  markNotificationAsRead, \n  markAllNotificationsAsRead \n} from '@/lib/notifications';\n\n// GET /api/notifications - Récupérer les notifications non lues\nexport async function GET(req: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Vérifier que l'utilisateur est un administrateur\n    if (session.user.role !== 'ADMIN') {\n      return NextResponse.json(\n        { error: 'Only administrators can access notifications' },\n        { status: 403 }\n      );\n    }\n\n    const adminId = session.user.adminId;\n\n    if (!adminId) {\n      return NextResponse.json(\n        { error: 'Admin ID not found in session' },\n        { status: 400 }\n      );\n    }\n\n    const notifications = await getUnreadNotificationsForAdmin(adminId);\n\n    return NextResponse.json({ notifications });\n  } catch (error: any) {\n    console.error('Error fetching notifications:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch notifications' },\n      { status: 500 }\n    );\n  }\n}\n\n// PATCH /api/notifications - Marquer une notification comme lue\nexport async function PATCH(req: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Vérifier que l'utilisateur est un administrateur\n    if (session.user.role !== 'ADMIN') {\n      return NextResponse.json(\n        { error: 'Only administrators can update notifications' },\n        { status: 403 }\n      );\n    }\n\n    const adminId = session.user.adminId;\n\n    if (!adminId) {\n      return NextResponse.json(\n        { error: 'Admin ID not found in session' },\n        { status: 400 }\n      );\n    }\n\n    const body = await req.json();\n    const { notificationId, markAll } = body;\n\n    if (markAll) {\n      // Marquer toutes les notifications comme lues\n      const result = await markAllNotificationsAsRead(adminId);\n      return NextResponse.json({ success: true, count: result.count });\n    } else if (notificationId) {\n      // Marquer une notification spécifique comme lue\n      const notification = await markNotificationAsRead(notificationId);\n      return NextResponse.json({ success: true, notification });\n    } else {\n      return NextResponse.json(\n        { error: 'notificationId or markAll is required' },\n        { status: 400 }\n      );\n    }\n  } catch (error: any) {\n    console.error('Error updating notification:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to update notification' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAOO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,mDAAmD;QACnD,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,QAAQ,IAAI,CAAC,OAAO;QAEpC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,6HAAA,CAAA,iCAA8B,AAAD,EAAE;QAE3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAc;IAC3C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAgC,GAC1D;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MAAM,GAAgB;IAC1C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,mDAAmD;QACnD,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,QAAQ,IAAI,CAAC,OAAO;QAEpC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;QAEpC,IAAI,SAAS;YACX,8CAA8C;YAC9C,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,6BAA0B,AAAD,EAAE;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAM,OAAO,OAAO,KAAK;YAAC;QAChE,OAAO,IAAI,gBAAgB;YACzB,gDAAgD;YAChD,MAAM,eAAe,MAAM,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAAE;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAM;YAAa;QACzD,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAgC,GAC1D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}