'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const isAuthenticated = status === 'authenticated';
  const isLoading = status === 'loading';
  const user = session?.user;

  const isClient = isAuthenticated && user?.role === 'CLIENT';
  const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';
  const isAdmin = isAuthenticated && user?.role === 'ADMIN';

  const login = async (username: string, password: string) => {
    const result = await signIn('credentials', {
      username,
      password,
      redirect: false,
    });

    return result;
  };

  const logout = async () => {
    await signOut({ redirect: false });
    router.push('/');
  };

  const redirectToLogin = () => {
    router.push('/auth/signin');
  };

  const redirectToDashboard = () => {
    if (isAdmin) {
      router.push('/admin/quotes');
    } else if (isCommercial) {
      router.push('/commercial/quotes');
    } else if (isClient) {
      router.push('/account/quotes');
    } else {
      router.push('/');
    }
  };

  return {
    session,
    status,
    user,
    isAuthenticated,
    isLoading,
    isClient,
    isCommercial,
    isAdmin,
    login,
    logout,
    redirectToLogin,
    redirectToDashboard,
  };
}
