'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSearch, FaTimes, FaSpinner, FaBox, FaTag, FaInfoCircle } from 'react-icons/fa';

interface AutocompleteSuggestion {
  type: string;
  value: string;
  label: string;
  product?: {
    id: string;
    reference: string;
    name: string;
    description?: string;
    price?: number;
    category?: string;
    brand?: string;
  };
  matchType?: string;
}

interface GoogleLikeAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  onSelect?: (suggestion: AutocompleteSuggestion) => void;
  placeholder?: string;
  autocompleteType: 'products' | 'clients';
  field?: string;
  className?: string;
  disabled?: boolean;
  minLength?: number;
  maxSuggestions?: number;
  showIcons?: boolean;
  showCategories?: boolean;
}

export default function GoogleLikeAutocomplete({
  value,
  onChange,
  onSelect,
  placeholder = 'Rechercher...',
  autocompleteType,
  field = 'reference',
  className = '',
  disabled = false,
  minLength = 2,
  maxSuggestions = 10,
  showIcons = true,
  showCategories = true
}: GoogleLikeAutocompleteProps) {
  const [suggestions, setSuggestions] = useState<AutocompleteSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isFocused, setIsFocused] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced search function
  const debouncedSearch = useCallback(
    (query: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        if (query.length >= minLength) {
          performSearch(query);
        } else {
          setSuggestions([]);
          setShowSuggestions(false);
        }
      }, 300);
    },
    [minLength]
  );

  // Perform search
  const performSearch = async (query: string) => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/autocomplete/${autocompleteType}?q=${encodeURIComponent(query)}&field=${field}&limit=${maxSuggestions}`);
      
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        setShowSuggestions((data.suggestions || []).length > 0);
        setSelectedIndex(-1);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error('Autocomplete search error:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    debouncedSearch(newValue);
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: AutocompleteSuggestion) => {
    onChange(suggestion.value);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    onSelect?.(suggestion);
    inputRef.current?.blur();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    if (value.length >= minLength && suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 200);
  };

  // Clear input
  const handleClear = () => {
    onChange('');
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Get icon for suggestion type
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'reference':
        return <FaTag className="text-blue-500" />;
      case 'name':
        return <FaBox className="text-green-500" />;
      case 'description':
        return <FaInfoCircle className="text-orange-500" />;
      default:
        return <FaBox className="text-gray-500" />;
    }
  };

  // Highlight matching text
  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-800 font-semibold">
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  return (
    <div className={`relative ${className}`}>
      {/* Input Container */}
      <div className={`relative flex items-center border rounded-lg transition-all duration-200 ${
        isFocused 
          ? 'border-primary ring-2 ring-primary/20 shadow-lg' 
          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        
        {/* Search Icon */}
        <div className="pl-3 pr-2">
          <FaSearch className="text-gray-400" />
        </div>

        {/* Input */}
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className="flex-1 py-3 px-2 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none"
          autoComplete="off"
        />

        {/* Loading Spinner */}
        {isLoading && (
          <div className="px-3">
            <FaSpinner className="animate-spin text-gray-400" />
          </div>
        )}

        {/* Clear Button */}
        {value && !isLoading && (
          <button
            type="button"
            onClick={handleClear}
            className="px-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <FaTimes />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && suggestions.length > 0 && (
          <motion.div
            ref={suggestionsRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl max-h-80 overflow-y-auto"
          >
            {suggestions.map((suggestion, index) => (
              <motion.div
                key={`${suggestion.type}-${suggestion.value}-${index}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.05 }}
                className={`px-4 py-3 cursor-pointer transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0 ${
                  index === selectedIndex
                    ? 'bg-primary/10 border-primary/20'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
                onClick={() => handleSuggestionSelect(suggestion)}
              >
                <div className="flex items-start space-x-3">
                  {/* Icon */}
                  {showIcons && (
                    <div className="flex-shrink-0 mt-1">
                      {getSuggestionIcon(suggestion.type)}
                    </div>
                  )}

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    {/* Main Label */}
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {highlightMatch(suggestion.label, value)}
                    </div>

                    {/* Product Details */}
                    {suggestion.product && (
                      <div className="mt-1 space-y-1">
                        {suggestion.product.description && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                            {highlightMatch(suggestion.product.description, value)}
                          </div>
                        )}
                        
                        <div className="flex items-center space-x-2 text-xs">
                          {suggestion.product.category && showCategories && (
                            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">
                              {suggestion.product.category}
                            </span>
                          )}
                          {suggestion.product.brand && (
                            <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                              {suggestion.product.brand}
                            </span>
                          )}
                          {suggestion.product.price && (
                            <span className="text-gray-600 dark:text-gray-400 font-medium">
                              {suggestion.product.price.toLocaleString('fr-FR')} MAD
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Match Type Badge */}
                  <div className="flex-shrink-0">
                    <span className={`px-2 py-1 text-xs rounded ${
                      suggestion.matchType === 'prefix' 
                        ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      {suggestion.type}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
