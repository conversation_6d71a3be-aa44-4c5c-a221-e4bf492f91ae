'use client';

import ModernHeader from '@/components/layout/ModernHeader';
import ModernHero from '@/components/home/<USER>';
import ModernCategoriesGrid from '@/components/home/<USER>';
import ModernFooter from '@/components/layout/ModernFooter';
import { motion } from 'framer-motion';
import {
  FaStar,
  FaQuoteLeft,
  FaCheckCircle,
  FaShippingFast,
  FaHeadset,
  FaAward,
  FaArrowRight
} from 'react-icons/fa';
import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Modern Header with Feature Bar */}
      <ModernHeader />

      {/* Hero Section */}
      <ModernHero />

      {/* Categories Grid */}
      <ModernCategoriesGrid />

      {/* Featured Products Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-charcoal mb-4 font-heading">
              Produits Vedettes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez notre sélection de produits les plus populaires,
              choisis pour leur qualité exceptionnelle et leur innovation.
            </p>
          </motion.div>

          {/* Product Grid Placeholder */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {[1, 2, 3, 4].map((item) => (
              <motion.div
                key={item}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: item * 0.1 }}
                viewport={{ once: true }}
                className="group bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              >
                <div className="aspect-square bg-light-gray group-hover:bg-gray-200 transition-colors duration-300 flex items-center justify-center">
                  <div className="w-20 h-20 bg-gray-300 rounded-lg"></div>
                </div>
                <div className="p-6">
                  <h3 className="font-semibold text-charcoal mb-2">Produit {item}</h3>
                  <p className="text-gray-600 text-sm mb-4">Description du produit électrique de haute qualité</p>
                  <div className="flex items-center justify-between">
                    <span className="text-moonelec-red font-bold">299 MAD</span>
                    <button className="btn btn-primary text-sm py-2 px-4">
                      Voir Détails
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center">
            <Link
              href="/products"
              className="btn btn-secondary inline-flex items-center space-x-2"
            >
              <span>Voir Tous les Produits</span>
              <FaArrowRight />
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-light-gray">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-charcoal mb-4 font-heading">
              Ce Que Disent Nos Clients
            </h2>
            <p className="text-xl text-gray-600">
              La satisfaction de nos clients est notre priorité absolue
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Ahmed Benali",
                role: "Électricien Professionnel",
                content: "Moonelec offre des produits de qualité exceptionnelle. Leur service client est remarquable et la livraison toujours rapide.",
                rating: 5
              },
              {
                name: "Fatima Zahra",
                role: "Architecte d'Intérieur",
                content: "Je recommande vivement Moonelec pour tous vos besoins électriques. Leur gamme de produits design est impressionnante.",
                rating: 5
              },
              {
                name: "Mohamed Alami",
                role: "Entrepreneur",
                content: "Partenaire de confiance depuis 5 ans. Moonelec comprend parfaitement les besoins des professionnels du bâtiment.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center mb-4">
                  <FaQuoteLeft className="text-moonelec-red text-2xl mr-4" />
                  <div className="flex space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <FaStar key={i} className="text-yellow-400" />
                    ))}
                  </div>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>
                <div>
                  <h4 className="font-semibold text-charcoal">{testimonial.name}</h4>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-moonelec-red to-moonelec-red-dark">
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-4 font-heading">
              Prêt à Transformer Votre Projet ?
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Contactez nos experts pour obtenir des conseils personnalisés
              et découvrir les meilleures solutions pour vos besoins électriques.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="btn bg-white text-moonelec-red hover:bg-gray-100 font-semibold py-3 px-8"
              >
                Nous Contacter
              </Link>
              <Link
                href="/products"
                className="btn bg-transparent border-2 border-white text-white hover:bg-white hover:text-moonelec-red font-semibold py-3 px-8"
              >
                Explorer Produits
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Modern Footer */}
      <ModernFooter />
    </div>
  );
}
