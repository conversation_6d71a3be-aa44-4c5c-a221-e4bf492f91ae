import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ChatConversation, ChatUser } from '../../types';
import ApiService from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import LoadingAnimation from '../../components/LoadingAnimation';

const ChatScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'conversations' | 'users'>('conversations');
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [availableUsers, setAvailableUsers] = useState<ChatUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      if (activeTab === 'conversations') {
        await loadConversations();
      } else {
        await loadAvailableUsers();
      }
    } catch (error) {
      console.error('Error loading chat data:', error);
      Alert.alert('Erreur', 'Impossible de charger les données du chat');
    } finally {
      setIsLoading(false);
    }
  };

  const loadConversations = async () => {
    try {
      const conversationsData = await ApiService.getChatConversations();
      setConversations(conversationsData);
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  const loadAvailableUsers = async () => {
    try {
      const usersData = await ApiService.getChatUsers();
      setAvailableUsers(usersData);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const startConversation = async (userId: string) => {
    try {
      const conversation = await ApiService.createChatConversation(userId);
      navigation.navigate('ChatConversation', { 
        conversationId: conversation.id,
        conversationName: getConversationName(conversation)
      });
    } catch (error) {
      console.error('Error starting conversation:', error);
      Alert.alert('Erreur', 'Impossible de démarrer la conversation');
    }
  };

  const openConversation = (conversation: ChatConversation) => {
    navigation.navigate('ChatConversation', {
      conversationId: conversation.id,
      conversationName: getConversationName(conversation)
    });
  };

  const getConversationName = (conversation: ChatConversation) => {
    if (user?.role === 'ADMIN') {
      return `${conversation.commercial.user.firstname} ${conversation.commercial.user.lastname}`;
    } else {
      return `${conversation.admin.user.firstname} ${conversation.admin.user.lastname}`;
    }
  };

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Aujourd\'hui';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hier';
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
      });
    }
  };

  const renderConversation = ({ item }: { item: ChatConversation }) => (
    <TouchableOpacity
      style={styles.conversationCard}
      onPress={() => openConversation(item)}
    >
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Ionicons 
            name="person" 
            size={24} 
            color="#006db7" 
          />
        </View>
      </View>
      
      <View style={styles.conversationInfo}>
        <View style={styles.conversationHeader}>
          <Text style={styles.conversationName}>
            {getConversationName(item)}
          </Text>
          <Text style={styles.conversationTime}>
            {formatTime(item.lastMessageAt)}
          </Text>
        </View>
        
        <View style={styles.conversationFooter}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage || 'Aucun message'}
          </Text>
          <Text style={styles.conversationDate}>
            {formatDate(item.lastMessageAt)}
          </Text>
        </View>
      </View>
      
      <View style={styles.conversationAction}>
        <Ionicons name="chevron-forward" size={20} color="#666" />
      </View>
    </TouchableOpacity>
  );

  const renderUser = ({ item }: { item: ChatUser }) => (
    <TouchableOpacity
      style={styles.userCard}
      onPress={() => startConversation(item.id)}
    >
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Ionicons 
            name="person" 
            size={24} 
            color="#006db7" 
          />
        </View>
        <View style={[styles.roleIndicator, { 
          backgroundColor: item.role === 'ADMIN' ? '#ed1c24' : '#006db7' 
        }]}>
          <Ionicons 
            name={item.role === 'ADMIN' ? 'shield' : 'briefcase'} 
            size={12} 
            color="#fff" 
          />
        </View>
      </View>
      
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{item.name}</Text>
        <Text style={styles.userRole}>
          {item.role === 'ADMIN' ? 'Administrateur' : 'Commercial'}
        </Text>
        <Text style={styles.userEmail}>{item.email}</Text>
      </View>
      
      <View style={styles.userAction}>
        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => startConversation(item.id)}
        >
          <Ionicons name="chatbubble" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name={activeTab === 'conversations' ? 'chatbubbles-outline' : 'people-outline'} 
        size={64} 
        color="#ccc" 
      />
      <Text style={styles.emptyText}>
        {activeTab === 'conversations' 
          ? 'Aucune conversation' 
          : 'Aucun utilisateur disponible'
        }
      </Text>
      <Text style={styles.emptySubtext}>
        {activeTab === 'conversations'
          ? 'Commencez une nouvelle conversation'
          : 'Les utilisateurs apparaîtront ici'
        }
      </Text>
    </View>
  );

  if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={64} color="#ed1c24" />
          <Text style={styles.errorText}>Accès non autorisé</Text>
          <Text style={styles.errorSubtext}>
            Le chat n'est disponible que pour les administrateurs et commerciaux
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header avec tabs */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>💬 Chat Moonelec</Text>
        
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'conversations' && styles.activeTab]}
            onPress={() => setActiveTab('conversations')}
          >
            <Ionicons 
              name="chatbubbles" 
              size={20} 
              color={activeTab === 'conversations' ? '#006db7' : '#666'} 
            />
            <Text style={[styles.tabText, activeTab === 'conversations' && styles.activeTabText]}>
              Conversations
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.tab, activeTab === 'users' && styles.activeTab]}
            onPress={() => setActiveTab('users')}
          >
            <Ionicons 
              name="people" 
              size={20} 
              color={activeTab === 'users' ? '#006db7' : '#666'} 
            />
            <Text style={[styles.tabText, activeTab === 'users' && styles.activeTabText]}>
              Utilisateurs
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <LoadingAnimation size={60} color="#006db7" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      ) : (
        <FlatList
          data={activeTab === 'conversations' ? conversations : availableUsers}
          renderItem={activeTab === 'conversations' ? renderConversation : renderUser}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    paddingTop: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: -1,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#006db7',
  },
  tabText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#006db7',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  conversationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  roleIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  conversationInfo: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  conversationTime: {
    fontSize: 12,
    color: '#666',
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    marginRight: 8,
  },
  conversationDate: {
    fontSize: 12,
    color: '#999',
  },
  conversationAction: {
    marginLeft: 8,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  userRole: {
    fontSize: 14,
    color: '#006db7',
    fontWeight: '600',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 12,
    color: '#666',
  },
  userAction: {
    marginLeft: 8,
  },
  chatButton: {
    backgroundColor: '#006db7',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ed1c24',
    marginTop: 16,
    textAlign: 'center',
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default ChatScreen;
