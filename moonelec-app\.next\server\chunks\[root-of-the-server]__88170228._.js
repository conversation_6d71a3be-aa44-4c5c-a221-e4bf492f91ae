module.exports = {

"[project]/.next-internal/server/app/api/auth/mobile/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[project]/src/middleware/security.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkAuthRateLimit": (()=>checkAuthRateLimit),
    "checkIPSecurity": (()=>checkIPSecurity),
    "checkRateLimit": (()=>checkRateLimit),
    "checkUploadRateLimit": (()=>checkUploadRateLimit),
    "createSecureError": (()=>createSecureError),
    "generateCSRFToken": (()=>generateCSRFToken),
    "generateSecureSessionId": (()=>generateSecureSessionId),
    "reportSuspiciousActivity": (()=>reportSuspiciousActivity),
    "sanitizeForDatabase": (()=>sanitizeForDatabase),
    "sanitizeInput": (()=>sanitizeInput),
    "schemas": (()=>schemas),
    "securityHeaders": (()=>securityHeaders),
    "validateAuthToken": (()=>validateAuthToken),
    "validateCSRFToken": (()=>validateCSRFToken),
    "validateFileUpload": (()=>validateFileUpload),
    "validateInput": (()=>validateInput),
    "validatePasswordStrength": (()=>validatePasswordStrength),
    "validateSessionSecurity": (()=>validateSessionSecurity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
;
;
// Native rate limiting implementation for Next.js
class RateLimiter {
    requests = new Map();
    check(ip, maxRequests = 100, windowMs = 15 * 60 * 1000) {
        const now = Date.now();
        const record = this.requests.get(ip);
        if (!record || now > record.resetTime) {
            this.requests.set(ip, {
                count: 1,
                resetTime: now + windowMs
            });
            return true;
        }
        if (record.count >= maxRequests) {
            return false;
        }
        record.count++;
        return true;
    }
    cleanup() {
        const now = Date.now();
        for (const [ip, record] of this.requests.entries()){
            if (now > record.resetTime) {
                this.requests.delete(ip);
            }
        }
    }
}
// Global rate limiter instances
const generalRateLimit = new RateLimiter();
const authRateLimit = new RateLimiter();
const uploadRateLimit = new RateLimiter();
const checkRateLimit = (ip)=>{
    return generalRateLimit.check(ip, 100, 15 * 60 * 1000); // 100 requests per 15 minutes
};
const checkAuthRateLimit = (ip)=>{
    return authRateLimit.check(ip, 5, 15 * 60 * 1000); // 5 auth attempts per 15 minutes
};
const checkUploadRateLimit = (ip)=>{
    return uploadRateLimit.check(ip, 10, 60 * 1000); // 10 uploads per minute
};
// Cleanup rate limit records periodically
setInterval(()=>{
    generalRateLimit.cleanup();
    authRateLimit.cleanup();
    uploadRateLimit.cleanup();
}, 5 * 60 * 1000); // Cleanup every 5 minutes
const schemas = {
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email().max(255),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(8).max(128),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1).max(100).regex(/^[a-zA-ZÀ-ÿ\s'-]+$/),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^[\+]?[1-9][\d]{0,15}$/),
    text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().max(1000),
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid(),
    reference: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1).max(50).regex(/^[a-zA-Z0-9-_]+$/),
    url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().max(500)
};
const sanitizeInput = (input)=>{
    return input.replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .trim();
};
const sanitizeForDatabase = (input)=>{
    return input.replace(/['";\\]/g, '') // Remove dangerous SQL characters
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove SQL block comments
    .replace(/\*\//g, '').trim();
};
const generateCSRFToken = ()=>{
    return __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)").randomBytes(32).toString('hex');
};
const validateCSRFToken = (token, sessionToken)=>{
    return token === sessionToken && token.length === 64;
};
const securityHeaders = (request)=>{
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    // Content Security Policy
    const csp = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: blob:",
        "font-src 'self'",
        "connect-src 'self'",
        "media-src 'self'",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'",
        "upgrade-insecure-requests"
    ].join('; ');
    response.headers.set('Content-Security-Policy', csp);
    return response;
};
const validateInput = (schema, data)=>{
    try {
        return schema.parse(data);
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            throw new Error(`Validation error: ${error.errors.map((e)=>e.message).join(', ')}`);
        }
        throw error;
    }
};
const validateFileUpload = (file)=>{
    const maxSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv'
    ];
    const dangerousExtensions = [
        'exe',
        'bat',
        'cmd',
        'com',
        'pif',
        'scr',
        'vbs',
        'js',
        'jar',
        'php',
        'asp',
        'jsp'
    ];
    // Check file size
    if (file.size > maxSize) {
        throw new Error('File too large. Maximum size is 25MB');
    }
    // Check file type
    if (!allowedTypes.includes(file.type)) {
        throw new Error('File type not allowed');
    }
    // Check file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (dangerousExtensions.includes(extension || '')) {
        throw new Error('File extension not allowed for security reasons');
    }
    return true;
};
const validateAuthToken = (token)=>{
    if (!token || token.length < 10) {
        return false;
    }
    // Check for suspicious patterns
    const suspiciousPatterns = [
        /[<>]/g,
        /javascript:/gi,
        /data:/gi,
        /vbscript:/gi
    ];
    return !suspiciousPatterns.some((pattern)=>pattern.test(token));
};
// IP address validation and blocking
const blockedIPs = new Set();
const suspiciousActivity = new Map();
const checkIPSecurity = (ip)=>{
    // Check if IP is blocked
    if (blockedIPs.has(ip)) {
        return false;
    }
    // Track suspicious activity
    const attempts = suspiciousActivity.get(ip) || 0;
    if (attempts > 50) {
        blockedIPs.add(ip);
        return false;
    }
    return true;
};
const reportSuspiciousActivity = (ip)=>{
    const current = suspiciousActivity.get(ip) || 0;
    suspiciousActivity.set(ip, current + 1);
};
const validatePasswordStrength = (password)=>{
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
};
const generateSecureSessionId = ()=>{
    return __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)").randomBytes(32).toString('hex');
};
const validateSessionSecurity = (sessionData)=>{
    // Check session expiration
    if (sessionData.expiresAt && new Date() > new Date(sessionData.expiresAt)) {
        return false;
    }
    // Check session integrity
    if (!sessionData.userId || !sessionData.createdAt) {
        return false;
    }
    return true;
};
const createSecureError = (message, statusCode = 400)=>{
    // Don't expose internal errors in production
    const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
    const safeMessage = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : message;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        error: safeMessage
    }, {
        status: statusCode
    });
};
}}),
"[project]/src/app/api/auth/mobile/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/middleware/security.ts [app-route] (ecmascript)");
;
;
;
;
;
async function POST(request) {
    try {
        // Get client IP for security checks
        const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
        // Check IP security
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkIPSecurity"])(clientIP)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSecureError"])('Access denied', 403);
        }
        const body = await request.json();
        // Validate and sanitize input
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateInput"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemas"].email.or(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemas"].name), body.username);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateInput"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemas"].password, body.password);
        } catch (validationError) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reportSuspiciousActivity"])(clientIP);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSecureError"])('Invalid input format', 400);
        }
        const username = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeInput"])(body.username);
        const password = body.password; // Don't sanitize password as it may contain special chars
        console.log('📱 Mobile login attempt for:', username);
        if (!username || !password) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reportSuspiciousActivity"])(clientIP);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Username and password are required'
            }, {
                status: 400
            });
        }
        // Find user by username or email
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findFirst({
            where: {
                OR: [
                    {
                        username: username
                    },
                    {
                        email: username
                    }
                ]
            }
        });
        if (!user) {
            console.log('❌ User not found:', username);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid credentials'
            }, {
                status: 401
            });
        }
        // Verify password
        const isValidPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, user.password);
        if (!isValidPassword) {
            console.log('❌ Invalid password for user:', username);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid credentials'
            }, {
                status: 401
            });
        }
        // Generate JWT token for mobile app using JWT_SECRET
        const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
        const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign({
            userId: user.id,
            username: user.username,
            email: user.email,
            role: user.role
        }, jwtSecret, {
            expiresIn: '7d'
        } // Token valid for 7 days
        );
        // Prepare user data (exclude password)
        const userData = {
            id: user.id,
            username: user.username,
            email: user.email,
            firstname: user.firstname,
            lastname: user.lastname,
            role: user.role,
            isActive: user.isActive
        };
        console.log('✅ Mobile login successful for:', username, 'Role:', user.role);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            user: userData,
            token: token,
            message: 'Login successful'
        });
    } catch (error) {
        console.error('❌ Mobile login error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    try {
        // Get client IP for security checks
        const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
        // Check IP security
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkIPSecurity"])(clientIP)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSecureError"])('Access denied', 403);
        }
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reportSuspiciousActivity"])(clientIP);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'No token provided'
            }, {
                status: 401
            });
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        // Validate token format for security
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateAuthToken"])(token)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reportSuspiciousActivity"])(clientIP);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$middleware$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSecureError"])('Invalid token format', 401);
        }
        // Verify JWT token using JWT_SECRET
        const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, jwtSecret);
        // Get fresh user data from database
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: decoded.userId
            },
            select: {
                id: true,
                username: true,
                email: true,
                firstname: true,
                lastname: true,
                role: true,
                isActive: true
            }
        });
        if (!user || !user.isActive) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'User not found or inactive'
            }, {
                status: 401
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            user: user
        });
    } catch (error) {
        console.error('❌ Token verification error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Invalid token'
        }, {
            status: 401
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__88170228._.js.map