import { prisma } from './prisma';

// Real-time data calculation functions
export class RealTimeDataService {
  
  // Calculate real revenue from quotes and orders
  static async calculateRealRevenue(timeframe: 'day' | 'week' | 'month' | 'year' = 'month') {
    const now = new Date();
    let startDate: Date;

    switch (timeframe) {
      case 'day':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
    }

    // Calculate revenue from quotes
    const quotes = await prisma.quote.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: now,
        },
      },
      include: {
        items: true,
      },
    });

    const totalRevenue = quotes.reduce((total, quote) => {
      const quoteTotal = quote.items.reduce((quoteSum, item) => {
        return quoteSum + (item.quantity * item.unitPrice);
      }, 0);
      return total + quoteTotal;
    }, 0);

    return {
      totalRevenue,
      quotesCount: quotes.length,
      averageQuoteValue: quotes.length > 0 ? totalRevenue / quotes.length : 0,
    };
  }

  // Get real user activity metrics
  static async getUserActivityMetrics() {
    const now = new Date();
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Active users in last 30 days (based on quote creation)
    const activeUsers = await prisma.user.findMany({
      where: {
        quotes: {
          some: {
            createdAt: {
              gte: last30Days,
            },
          },
        },
      },
      include: {
        _count: {
          select: {
            quotes: true,
          },
        },
      },
    });

    // Total users by role
    const usersByRole = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        id: true,
      },
    });

    return {
      activeUsers: activeUsers.length,
      totalUsers: await prisma.user.count(),
      usersByRole: usersByRole.reduce((acc, item) => {
        acc[item.role] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
      topActiveUsers: activeUsers
        .sort((a, b) => b._count.quotes - a._count.quotes)
        .slice(0, 5)
        .map(user => ({
          id: user.id,
          name: `${user.firstname} ${user.lastname}`,
          quotesCount: user._count.quotes,
        })),
    };
  }

  // Get real product performance data
  static async getProductPerformance() {
    // Products with most quotes
    const productQuotes = await prisma.quoteItem.groupBy({
      by: ['productId'],
      _count: {
        id: true,
      },
      _sum: {
        quantity: true,
        unitPrice: true,
      },
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: 10,
    });

    // Get product details
    const productIds = productQuotes.map(pq => pq.productId).filter(Boolean);
    const products = await prisma.product.findMany({
      where: {
        id: {
          in: productIds as string[],
        },
      },
    });

    const productPerformance = productQuotes.map(pq => {
      const product = products.find(p => p.id === pq.productId);
      return {
        productId: pq.productId,
        productName: product?.name || 'Unknown Product',
        productReference: product?.reference || 'N/A',
        quotesCount: pq._count.id,
        totalQuantity: pq._sum.quantity || 0,
        totalRevenue: (pq._sum.quantity || 0) * (pq._sum.unitPrice || 0),
      };
    });

    return {
      topProducts: productPerformance,
      totalProducts: await prisma.product.count(),
      totalCategories: await prisma.category.count(),
    };
  }

  // Get real sales trends
  static async getSalesTrends(days: number = 30) {
    const now = new Date();
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    const dailyQuotes = await prisma.quote.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: now,
        },
      },
      include: {
        items: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Group by day
    const dailyData = new Map<string, { quotes: number; revenue: number }>();
    
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
      const dateKey = date.toISOString().split('T')[0];
      dailyData.set(dateKey, { quotes: 0, revenue: 0 });
    }

    dailyQuotes.forEach(quote => {
      const dateKey = quote.createdAt.toISOString().split('T')[0];
      const existing = dailyData.get(dateKey) || { quotes: 0, revenue: 0 };
      
      const quoteRevenue = quote.items.reduce((sum, item) => {
        return sum + (item.quantity * item.unitPrice);
      }, 0);

      dailyData.set(dateKey, {
        quotes: existing.quotes + 1,
        revenue: existing.revenue + quoteRevenue,
      });
    });

    return Array.from(dailyData.entries()).map(([date, data]) => ({
      date,
      quotes: data.quotes,
      revenue: data.revenue,
    }));
  }

  // Get comprehensive dashboard data
  static async getDashboardData() {
    const [
      revenueData,
      userMetrics,
      productPerformance,
      salesTrends,
    ] = await Promise.all([
      this.calculateRealRevenue('month'),
      this.getUserActivityMetrics(),
      this.getProductPerformance(),
      this.getSalesTrends(30),
    ]);

    // Calculate growth rates
    const lastMonthRevenue = await this.calculateRealRevenue('month');
    const currentMonthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const lastMonthStart = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
    const lastMonthEnd = new Date(new Date().getFullYear(), new Date().getMonth(), 0);

    const lastMonthQuotes = await prisma.quote.findMany({
      where: {
        createdAt: {
          gte: lastMonthStart,
          lte: lastMonthEnd,
        },
      },
      include: {
        items: true,
      },
    });

    const lastMonthRevenueTotal = lastMonthQuotes.reduce((total, quote) => {
      return total + quote.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    }, 0);

    const revenueGrowth = lastMonthRevenueTotal > 0 
      ? ((revenueData.totalRevenue - lastMonthRevenueTotal) / lastMonthRevenueTotal) * 100 
      : 0;

    return {
      revenue: {
        ...revenueData,
        growth: revenueGrowth,
      },
      users: userMetrics,
      products: productPerformance,
      trends: salesTrends,
      summary: {
        totalQuotes: await prisma.quote.count(),
        totalProducts: await prisma.product.count(),
        totalUsers: await prisma.user.count(),
        totalCategories: await prisma.category.count(),
      },
    };
  }

  // Real-time data change notification
  static async notifyDataChange(type: 'quote' | 'product' | 'user' | 'category', action: 'create' | 'update' | 'delete') {
    // This would integrate with WebSocket or Server-Sent Events for real-time updates
    console.log(`📊 Data change notification: ${type} ${action}`);
    
    // In a real implementation, you would:
    // 1. Emit WebSocket event to connected clients
    // 2. Update cached dashboard data
    // 3. Trigger real-time UI updates
    
    return {
      type,
      action,
      timestamp: new Date().toISOString(),
    };
  }
}

// Cache management for performance
class DataCache {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  static set(key: string, data: any, ttlMinutes: number = 5) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000,
    });
  }

  static get(key: string) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  static clear() {
    this.cache.clear();
  }
}

export { DataCache };
