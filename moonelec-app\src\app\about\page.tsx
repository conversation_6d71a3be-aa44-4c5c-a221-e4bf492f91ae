'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { FaHistory, FaUsers, FaWarehouse, FaGlobe, FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa';
import CountUp from 'react-countup';
import PageLayout from '@/components/layout/PageLayout';

export default function AboutPage() {
  return (
    <PageLayout>
      <main className="pt-24 pb-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-[#0a1f2f] to-[#0a2a3f] text-white py-20 relative overflow-hidden">
        {/* Background Light Effect */}
        <motion.div
          className="absolute top-0 left-0 w-full h-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.2 }}
          transition={{ duration: 1.5 }}
        >
          <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-blue-500 rounded-full filter blur-[100px] opacity-20"></div>
          <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-primary rounded-full filter blur-[100px] opacity-20"></div>
        </motion.div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <motion.h1
              className="text-4xl md:text-5xl font-bold mb-6"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              À Propos de <span className="text-primary">Moonelec</span>
            </motion.h1>

            <motion.div
              className="w-20 h-1 bg-blue-500 mx-auto mb-8"
              initial={{ width: 0 }}
              animate={{ width: 80 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            ></motion.div>

            <motion.p
              className="text-lg text-gray-300 mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Découvrez l'histoire et l'expertise de Moonelec, votre partenaire de confiance dans la distribution de matériel électrique depuis plus de trois décennies.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="py-24 relative overflow-hidden">
        {/* Background Decoration */}
        <div className="absolute top-1/4 right-0 w-72 h-72 bg-blue-500/5 rounded-full"></div>
        <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-primary/5 rounded-full"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Image Side */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: true, amount: 0.3 }}
              className="relative"
            >
              <motion.div
                className="absolute -top-10 -left-10 w-20 h-20 border-t-2 border-l-2 border-primary"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              ></motion.div>

              <motion.div
                className="absolute -bottom-10 -right-10 w-20 h-20 border-b-2 border-r-2 border-primary"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              ></motion.div>

              <div className="relative w-full h-[450px] rounded-lg overflow-hidden shadow-2xl">
                <Image
                  src="https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?q=80&w=2070&auto=format&fit=crop"
                  alt="Entrepôt Moonelec"
                  fill
                  style={{ objectFit: 'cover' }}
                  className="rounded-lg"
                />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-tr from-primary/40 to-transparent rounded-lg"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 1, delay: 0.5 }}
                  viewport={{ once: true }}
                ></motion.div>
              </div>

              {/* Experience Badge */}
              <motion.div
                className="absolute -bottom-8 -right-8 bg-white dark:bg-[#1a1a1a] p-8 rounded-lg shadow-2xl border border-gray-100 dark:border-gray-800"
                initial={{ scale: 0.8, opacity: 0, y: 20 }}
                whileInView={{ scale: 1, opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.6, type: "spring" }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                <div className="text-center">
                  <div className="text-5xl font-bold text-primary mb-2">
                    <CountUp
                      start={0}
                      end={new Date().getFullYear() - 1990}
                      duration={3}
                      suffix=" ans"
                    />
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">d'expérience</p>
                </div>
              </motion.div>
            </motion.div>

            {/* Text Content */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: true, amount: 0.3 }}
            >
              <motion.h2
                className="text-3xl md:text-4xl font-semibold mb-6 text-gray-800 dark:text-white"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                Notre <span className="text-primary">Histoire</span> et Notre <span className="text-primary">Expertise</span>
              </motion.h2>

              <motion.div
                className="w-20 h-1 bg-primary mb-8"
                initial={{ width: 0 }}
                whileInView={{ width: 80 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              ></motion.div>

              <motion.p
                className="text-gray-600 dark:text-gray-300 mb-6"
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                La société Moonelec, est spécialisée dans la distribution de matériel électrique depuis 1990. Forte de plus de 300 000 références produits, elle offre une gamme complète de matériel électrique dans les secteurs résidentiel, tertiaire et industriel.
              </motion.p>

              <motion.p
                className="text-gray-600 dark:text-gray-300 mb-8"
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
              >
                Ainsi elle met à disposition tout son savoir-faire et sa parfaite connaissance des produits. Afin d'accompagner de plus près ses clients dans la réalisation de leurs projets.
              </motion.p>

              <motion.div
                className="p-6 mb-10 border-l-4 border-primary bg-gray-50 dark:bg-gray-800 shadow-md"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                viewport={{ once: true }}
                whileHover={{ x: 5 }}
              >
                <p className="text-gray-600 dark:text-gray-300 font-medium italic">
                  "Parce qu'elle sait tirer profit de son expérience tout en ayant une vision de son activité résolument tournée vers l'avenir, est sans cesse à la recherche des innovations qui feront de demain un monde connecté."
                </p>
              </motion.div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-6 mt-8">
                <StatItem
                  icon={<FaHistory />}
                  value={1990}
                  label="Fondée en"
                />
                <StatItem
                  icon={<FaUsers />}
                  value={5000}
                  label="Clients satisfaits"
                  plus
                />
                <StatItem
                  icon={<FaWarehouse />}
                  value={300000}
                  label="Références produits"
                  plus
                />
                <StatItem
                  icon={<FaGlobe />}
                  value={15}
                  label="Pays desservis"
                  plus
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Google Maps and Contact */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        {/* Background Decoration */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/10 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary/10 rounded-full translate-y-1/2 -translate-x-1/2"></div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center max-w-3xl mx-auto mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, amount: 0.3 }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-semibold mb-4 text-gray-800 dark:text-white"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true, amount: 0.3 }}
            >
              Nous <span className="text-primary">Trouver</span>
            </motion.h2>

            <motion.div
              className="w-20 h-1 bg-blue-500 mx-auto mb-6"
              initial={{ width: 0 }}
              whileInView={{ width: 80 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true, amount: 0.3 }}
            ></motion.div>

            <motion.p
              className="text-gray-600 dark:text-gray-300"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true, amount: 0.3 }}
            >
              Visitez notre siège social à Casablanca ou contactez-nous pour plus d'informations.
            </motion.p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true, amount: 0.3 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700"
            >
              <motion.h3
                className="text-xl font-semibold mb-6 text-gray-800 dark:text-white border-b border-primary pb-3"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                CONTACT INFO
              </motion.h3>

              <ul className="space-y-6">
                <motion.li
                  className="flex items-start"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  viewport={{ once: true }}
                  whileHover={{ x: 5 }}
                >
                  <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-lg mr-4 mt-1 shadow-md">
                    <FaMapMarkerAlt />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-white">Adresse</h4>
                    <p className="text-gray-600 dark:text-gray-300">Derb El Youssoufía, Rue 78, N°89, Bd El Fida - Casablanca-Maroc</p>
                  </div>
                </motion.li>

                <motion.li
                  className="flex items-start"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  viewport={{ once: true }}
                  whileHover={{ x: 5 }}
                >
                  <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-lg mr-4 mt-1 shadow-md">
                    <FaPhone />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-white">Téléphone</h4>
                    <p className="text-gray-600 dark:text-gray-300">+212 522 80 80 80</p>
                  </div>
                </motion.li>

                <motion.li
                  className="flex items-start"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  viewport={{ once: true }}
                  whileHover={{ x: 5 }}
                >
                  <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 text-lg mr-4 mt-1 shadow-md">
                    <FaEnvelope />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-white">Email</h4>
                    <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
                  </div>
                </motion.li>
              </ul>
            </motion.div>

            {/* Google Maps */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true, amount: 0.3 }}
              className="lg:col-span-2 rounded-lg overflow-hidden shadow-xl border border-gray-200 dark:border-gray-700"
            >
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3324.248652850733!2d-7.592269024303939!3d33.57289267334126!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xda7cd4f2c01591d%3A0xcf9fcbc174c738d4!2sMoonelec%20Sarl!5e0!3m2!1sfr!2sma!4v1747827546343!5m2!1sfr!2sma"
                width="100%"
                height="450"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="w-full h-full"
              ></iframe>
            </motion.div>
          </div>
        </div>
      </section>
    </main>
    </PageLayout>
  );
}

function StatItem({
  icon,
  value,
  label,
  plus = false
}: {
  icon: React.ReactNode;
  value: number;
  label: string;
  plus?: boolean;
}) {
  return (
    <motion.div
      className="flex items-start bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md border border-gray-100 dark:border-gray-700"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{
        y: -5,
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
      }}
    >
      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl mr-4 mt-1 shadow-inner">
        {icon}
      </div>
      <div>
        <div className="text-2xl font-bold text-gray-800 dark:text-white">
          <CountUp
            start={0}
            end={value}
            duration={2.5}
            separator=" "
            suffix={plus ? "+" : ""}
            useEasing={true}
          />
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{label}</p>
      </div>
    </motion.div>
  );
}
