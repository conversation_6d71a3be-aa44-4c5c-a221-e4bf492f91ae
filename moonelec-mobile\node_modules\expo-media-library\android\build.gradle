plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '17.1.6'

android {
  namespace "expo.modules.medialibrary"
  defaultConfig {
    versionCode 37
    versionName "17.1.6"
  }
}

dependencies {
  implementation "androidx.annotation:annotation:1.2.0"
  api "androidx.exifinterface:exifinterface:1.3.3"

  if (project.findProject(':expo-modules-test-core')) {
    testImplementation project(':expo-modules-test-core')
  }
  testImplementation "org.robolectric:robolectric:4.10"
}
