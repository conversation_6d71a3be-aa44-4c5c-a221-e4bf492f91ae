import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';

const ProfileScreen: React.FC = () => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { text: 'Déconnexion', style: 'destructive', onPress: logout },
      ]
    );
  };

  const ProfileItem: React.FC<{
    icon: keyof typeof Ionicons.glyphMap;
    label: string;
    value: string;
  }> = ({ icon, label, value }) => (
    <View style={styles.profileItem}>
      <Ionicons name={icon} size={24} color="#006db7" style={styles.profileIcon} />
      <View style={styles.profileText}>
        <Text style={styles.profileLabel}>{label}</Text>
        <Text style={styles.profileValue}>{value}</Text>
      </View>
    </View>
  );

  const ActionItem: React.FC<{
    icon: keyof typeof Ionicons.glyphMap;
    label: string;
    onPress: () => void;
    color?: string;
  }> = ({ icon, label, onPress, color = '#333' }) => (
    <TouchableOpacity style={styles.actionItem} onPress={onPress}>
      <Ionicons name={icon} size={24} color={color} style={styles.actionIcon} />
      <Text style={[styles.actionLabel, { color }]}>{label}</Text>
      <Ionicons name="chevron-forward" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>
            {user?.firstname?.charAt(0)}{user?.lastname?.charAt(0)}
          </Text>
        </View>
        <Text style={styles.userName}>
          {user?.firstname} {user?.lastname}
        </Text>
        <Text style={styles.userRole}>{user?.role}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Informations personnelles</Text>
        
        <ProfileItem
          icon="person-outline"
          label="Nom d'utilisateur"
          value={user?.username || ''}
        />
        
        <ProfileItem
          icon="mail-outline"
          label="Email"
          value={user?.email || ''}
        />
        
        <ProfileItem
          icon="business-outline"
          label="Rôle"
          value={user?.role || ''}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <ActionItem
          icon="settings-outline"
          label="Paramètres"
          onPress={() => {/* Navigate to settings */}}
        />
        
        <ActionItem
          icon="help-circle-outline"
          label="Aide et support"
          onPress={() => {/* Navigate to help */}}
        />

        <ActionItem
          icon="bug-outline"
          label="Diagnostics"
          onPress={() => navigation.navigate('Diagnostics')}
        />

        <ActionItem
          icon="information-circle-outline"
          label="À propos"
          onPress={() => {/* Navigate to about */}}
        />
      </View>

      <View style={styles.section}>
        <ActionItem
          icon="log-out-outline"
          label="Déconnexion"
          onPress={handleLogout}
          color="#F44336"
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#006db7',
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  userRole: {
    fontSize: 16,
    color: '#e3f2fd',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  profileIcon: {
    marginRight: 16,
  },
  profileText: {
    flex: 1,
  },
  profileLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  profileValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  actionIcon: {
    marginRight: 16,
  },
  actionLabel: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ProfileScreen;
