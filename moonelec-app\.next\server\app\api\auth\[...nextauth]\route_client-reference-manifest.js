globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/auth/[...nextauth]/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ui/ToastProvider.tsx":{"*":{"id":"(ssr)/./src/components/ui/ToastProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/AuthContext.tsx":{"*":{"id":"(ssr)/./src/context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/CartContext.tsx":{"*":{"id":"(ssr)/./src/context/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/ThemeContext.tsx":{"*":{"id":"(ssr)/./src/context/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/signin/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/layout.tsx":{"*":{"id":"(ssr)/./src/app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/quotes/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/quotes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/clients/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/account/quotes/page.tsx":{"*":{"id":"(ssr)/./src/app/account/quotes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.tsx":{"*":{"id":"(ssr)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/new/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/products/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\components\\ui\\ToastProvider.tsx":{"id":"(app-pages-browser)/./src/components/ui/ToastProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/context/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\CartContext.tsx":{"id":"(app-pages-browser)/./src/context/CartContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\ThemeContext.tsx":{"id":"(app-pages-browser)/./src/context/ThemeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\auth\\signin\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\layout.tsx":{"id":"(app-pages-browser)/./src/app/admin/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\quotes\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/quotes/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\clients\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/clients/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\account\\quotes\\page.tsx":{"id":"(app-pages-browser)/./src/app/account/quotes/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\products\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/new/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\products\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\products\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/products/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\api\\auth\\[...nextauth]\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ToastProvider.tsx":{"*":{"id":"(rsc)/./src/components/ui/ToastProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/AuthContext.tsx":{"*":{"id":"(rsc)/./src/context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/CartContext.tsx":{"*":{"id":"(rsc)/./src/context/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/ThemeContext.tsx":{"*":{"id":"(rsc)/./src/context/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/signin/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/layout.tsx":{"*":{"id":"(rsc)/./src/app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/quotes/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/quotes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/clients/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/account/quotes/page.tsx":{"*":{"id":"(rsc)/./src/app/account/quotes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.tsx":{"*":{"id":"(rsc)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/new/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/products/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}