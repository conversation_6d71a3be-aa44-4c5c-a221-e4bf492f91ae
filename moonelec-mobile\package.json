{"name": "moonelec-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "axios": "^1.9.0", "expo": "~53.0.9", "expo-av": "^15.1.4", "expo-camera": "^16.1.6", "expo-document-picker": "^13.1.5", "expo-image-picker": "^16.1.4", "expo-media-library": "^17.1.6", "expo-notifications": "^0.31.2", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "lucide-react": "^0.511.0", "moonelec-mobile": "file:", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.10.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}