import { prisma } from './prisma';

// Get all categories
export async function getCategories(options?: {
  search?: string;
  includeProducts?: boolean;
  skip?: number;
  take?: number;
}) {
  const { search, includeProducts = false, skip = 0, take = 50 } = options || {};

  const where = search
    ? {
        OR: [
          { name: { contains: search } },
          { description: { contains: search } },
        ],
      }
    : {};

  const [categories, total] = await Promise.all([
    prisma.category.findMany({
      where,
      include: {
        product: includeProducts,
      },
      skip,
      take,
      orderBy: {
        name: 'asc',
      },
    }),
    prisma.category.count({ where }),
  ]);

  return { categories, total };
}

// Get a single category by ID
export async function getCategoryById(id: string, includeProducts: boolean = false) {
  return prisma.category.findUnique({
    where: { id },
    include: {
      product: includeProducts,
    },
  });
}

// Create a new category
export async function createCategory(data: {
  name: string;
  description?: string;
  image?: string;
}) {
  // Check if a category with the same name already exists
  const existingCategory = await prisma.category.findFirst({
    where: {
      name: data.name,
    },
  });

  if (existingCategory) {
    throw new Error(`Une catégorie avec le nom ${data.name} existe déjà`);
  }

  return prisma.category.create({
    data,
  });
}

// Update an existing category
export async function updateCategory(
  id: string,
  data: {
    name?: string;
    description?: string;
    image?: string;
  }
) {
  // If name is being updated, check if it already exists
  if (data.name) {
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: data.name,
        id: { not: id },
      },
    });

    if (existingCategory) {
      throw new Error(`Une catégorie avec le nom ${data.name} existe déjà`);
    }
  }

  return prisma.category.update({
    where: { id },
    data,
  });
}

// Delete a category
export async function deleteCategory(id: string) {
  // First, update all products in this category to have null categoryId
  await prisma.product.updateMany({
    where: { categoryId: id },
    data: { categoryId: null },
  });

  // Then delete the category
  return prisma.category.delete({
    where: { id },
  });
}

// Get category with product count
export async function getCategoriesWithProductCount() {
  const categories = await prisma.category.findMany({
    orderBy: {
      name: 'asc',
    },
  });

  const categoriesWithCount = await Promise.all(
    categories.map(async (category) => {
      const count = await prisma.product.count({
        where: { categoryId: category.id },
      });
      return {
        ...category,
        productCount: count,
      };
    })
  );

  return categoriesWithCount;
}
