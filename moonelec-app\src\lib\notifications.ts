import { prisma } from './prisma';
import { NotificationType } from '@prisma/client';

/**
 * Crée une notification pour tous les administrateurs
 */
export async function createNotificationForAllAdmins(
  type: NotificationType,
  message: string,
  quoteId?: string
) {
  try {
    // Récupérer tous les administrateurs
    const admins = await prisma.admin.findMany();

    // Créer une notification pour chaque administrateur
    const notifications = await Promise.all(
      admins.map(admin =>
        prisma.notification.create({
          data: {
            type,
            message,
            adminId: admin.id,
            ...(quoteId && { quoteId }),
          },
        })
      )
    );

    // Dans une application réelle, vous récupéreriez les abonnements push de chaque administrateur
    // et vous enverriez une notification push à chacun d'eux
    // Exemple :
    /*
    for (const admin of admins) {
      // Récupérer les abonnements push de l'administrateur
      const pushSubscriptions = await prisma.pushSubscription.findMany({
        where: { adminId: admin.id },
      });

      // Envoyer une notification push à chaque abonnement
      for (const subscription of pushSubscriptions) {
        try {
          await sendPushNotification(
            JSON.parse(subscription.subscription),
            {
              id: notifications.find(n => n.adminId === admin.id)?.id,
              type,
              message,
              quoteId,
            }
          );
        } catch (error) {
          console.error(`Erreur lors de l'envoi de la notification push à l'administrateur ${admin.id}:`, error);
        }
      }
    }
    */

    return notifications;
  } catch (error) {
    console.error('Erreur lors de la création des notifications:', error);
    throw error;
  }
}

/**
 * Crée une notification pour un administrateur spécifique
 */
export async function createNotificationForAdmin(
  adminId: string,
  type: NotificationType,
  message: string,
  quoteId?: string
) {
  try {
    const notification = await prisma.notification.create({
      data: {
        type,
        message,
        adminId,
        ...(quoteId && { quoteId }),
      },
    });

    return notification;
  } catch (error) {
    console.error('Erreur lors de la création de la notification:', error);
    throw error;
  }
}

/**
 * Récupère les notifications non lues pour un administrateur
 */
export async function getUnreadNotificationsForAdmin(adminId: string) {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        adminId,
        isRead: false,
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        quote: {
          select: {
            quoteNumber: true,
            client: {
              include: {
                user: {
                  select: {
                    firstname: true,
                    lastname: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return notifications;
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    throw error;
  }
}

/**
 * Marque une notification comme lue
 */
export async function markNotificationAsRead(notificationId: string) {
  try {
    const notification = await prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true },
    });

    return notification;
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la notification:', error);
    throw error;
  }
}

/**
 * Marque toutes les notifications d'un administrateur comme lues
 */
export async function markAllNotificationsAsRead(adminId: string) {
  try {
    const result = await prisma.notification.updateMany({
      where: {
        adminId,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    return result;
  } catch (error) {
    console.error('Erreur lors de la mise à jour des notifications:', error);
    throw error;
  }
}
