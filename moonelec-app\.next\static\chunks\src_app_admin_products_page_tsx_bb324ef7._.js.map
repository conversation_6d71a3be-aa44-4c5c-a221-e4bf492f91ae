{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { FaPlus, FaSearch, FaFilter, FaEdit, FaTrash, FaEye, FaFileUpload, FaSpinner, FaExclamationTriangle, FaTags, FaCopyright } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\n// Types\ninterface Product {\n  id: string;\n  reference: string;\n  name: string;\n  description: string;\n  mainImage: string | null;\n  categoryId: string | null;\n  category?: {\n    id: string;\n    name: string;\n  };\n  productImages?: {\n    id: string;\n    url: string;\n  }[];\n}\n\ninterface Category {\n  id: string;\n  name: string;\n}\n\nexport default function AdminProductsPage() {\n  const router = useRouter();\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [isDeleting, setIsDeleting] = useState<string | null>(null);\n  const [products, setProducts] = useState<Product[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalProducts, setTotalProducts] = useState(0);\n  const [totalPages, setTotalPages] = useState(1);\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  const productsPerPage = 10;\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n\n        // Fetch categories\n        const categoriesResponse = await fetch('/api/categories');\n        if (!categoriesResponse.ok) {\n          throw new Error('Failed to fetch categories');\n        }\n        const categoriesData = await categoriesResponse.json();\n        setCategories(categoriesData.categories);\n\n        // Fetch products\n        await fetchProducts();\n      } catch (err: any) {\n        console.error('Error fetching data:', err);\n        setError(err.message || 'An error occurred while fetching data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Fetch products with filters\n  const fetchProducts = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      if (searchTerm) params.append('search', searchTerm);\n      if (selectedCategory) params.append('categoryId', selectedCategory);\n      params.append('skip', String((currentPage - 1) * productsPerPage));\n      params.append('take', String(productsPerPage));\n\n      const response = await fetch(`/api/products?${params.toString()}`);\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch products');\n      }\n\n      const data = await response.json();\n      setProducts(data.products);\n      setTotalProducts(data.total);\n      setTotalPages(Math.ceil(data.total / productsPerPage));\n    } catch (err: any) {\n      console.error('Error fetching products:', err);\n      setError(err.message || 'An error occurred while fetching products');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle search and filter changes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setCurrentPage(1); // Reset to first page when filters change\n      fetchProducts();\n    }, 500); // Debounce search\n\n    return () => clearTimeout(timer);\n  }, [searchTerm, selectedCategory]);\n\n  // Handle page changes\n  useEffect(() => {\n    fetchProducts();\n  }, [currentPage]);\n\n  const handleDeleteProduct = async (productId: string) => {\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      return;\n    }\n\n    try {\n      setIsDeleting(productId);\n\n      const response = await fetch(`/api/products/${productId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to delete product');\n      }\n\n      // Refresh the product list\n      fetchProducts();\n    } catch (err: any) {\n      console.error('Error deleting product:', err);\n      setError(err.message || 'An error occurred while deleting the product');\n    } finally {\n      setIsDeleting(null);\n    }\n  };\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0\">\n            Gestion des Produits\n          </h1>\n          <div className=\"flex flex-col sm:flex-row gap-3\">\n            <Link href=\"/admin/products/import\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"flex items-center justify-center gap-2 px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 transition-colors\"\n              >\n                <FaFileUpload />\n                <span>Importer depuis PDF</span>\n              </motion.button>\n            </Link>\n            <Link href=\"/admin/products/new\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"flex items-center justify-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors\"\n              >\n                <FaPlus />\n                <span>Ajouter un produit</span>\n              </motion.button>\n            </Link>\n            <Link href=\"/admin/categories\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n              >\n                <FaTags />\n                <span>Catégories</span>\n              </motion.button>\n            </Link>\n            <Link href=\"/admin/brands\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n              >\n                <FaCopyright />\n                <span>Marques</span>\n              </motion.button>\n            </Link>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center\">\n            <FaExclamationTriangle className=\"mr-2\" />\n            <span>{error}</span>\n          </div>\n        )}\n\n        {/* Search and Filter */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6\">\n          <div className=\"flex flex-col md:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <FaSearch className=\"text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                placeholder=\"Rechercher par référence, nom ou description...\"\n              />\n            </div>\n            <div className=\"w-full md:w-64\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <FaFilter className=\"text-gray-400\" />\n                </div>\n                <select\n                  value={selectedCategory || ''}\n                  onChange={(e) => setSelectedCategory(e.target.value || null)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                >\n                  <option value=\"\">Toutes les catégories</option>\n                  {categories.map((category) => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Products Table */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\">\n          {isLoading && products.length === 0 ? (\n            <div className=\"flex items-center justify-center py-12\">\n              <FaSpinner className=\"animate-spin text-4xl text-primary\" />\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Image\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Référence\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Nom\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Catégorie\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                  {products.length > 0 ? (\n                    products.map((product) => (\n                      <tr key={product.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"w-12 h-12 relative rounded overflow-hidden bg-gray-100 dark:bg-gray-700\">\n                            {product.mainImage ? (\n                              <Image\n                                src={product.mainImage}\n                                alt={product.name}\n                                fill\n                                style={{ objectFit: 'cover' }}\n                              />\n                            ) : (\n                              <div className=\"flex items-center justify-center h-full text-gray-400\">\n                                N/A\n                              </div>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                          {product.reference}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300\">\n                          {product.name}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300\">\n                          {product.category?.name || 'Non catégorisé'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => router.push(`/admin/products/${product.id}`)}\n                              className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                            >\n                              <FaEye />\n                            </button>\n                            <button\n                              onClick={() => router.push(`/admin/products/${product.id}/edit`)}\n                              className=\"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300\"\n                            >\n                              <FaEdit />\n                            </button>\n                            <button\n                              onClick={() => handleDeleteProduct(product.id)}\n                              disabled={isDeleting === product.id}\n                              className={`text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 ${\n                                isDeleting === product.id ? 'opacity-50 cursor-not-allowed' : ''\n                              }`}\n                            >\n                              {isDeleting === product.id ? <FaSpinner className=\"animate-spin\" /> : <FaTrash />}\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan={5} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                        {isLoading ? 'Chargement...' : 'Aucun produit trouvé'}\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"flex justify-center mt-6\">\n            <nav className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n                disabled={currentPage === 1 || isLoading}\n                className={`px-3 py-1 rounded-md ${\n                  currentPage === 1 || isLoading\n                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n              >\n                Précédent\n              </button>\n\n              {Array.from({ length: Math.min(5, totalPages) }).map((_, index) => {\n                // Show pages around current page\n                let pageToShow = currentPage;\n                if (totalPages <= 5) {\n                  pageToShow = index + 1;\n                } else {\n                  const offset = Math.floor(5 / 2);\n                  if (currentPage <= offset) {\n                    pageToShow = index + 1;\n                  } else if (currentPage >= totalPages - offset) {\n                    pageToShow = totalPages - 4 + index;\n                  } else {\n                    pageToShow = currentPage - offset + index;\n                  }\n                }\n\n                return (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentPage(pageToShow)}\n                    disabled={isLoading}\n                    className={`px-3 py-1 rounded-md ${\n                      currentPage === pageToShow\n                        ? 'bg-primary text-white'\n                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                    }`}\n                  >\n                    {pageToShow}\n                  </button>\n                );\n              })}\n\n              <button\n                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\n                disabled={currentPage === totalPages || isLoading}\n                className={`px-3 py-1 rounded-md ${\n                  currentPage === totalPages || isLoading\n                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n              >\n                Suivant\n              </button>\n            </nav>\n          </div>\n        )}\n\n        {/* Total count */}\n        <div className=\"mt-4 text-center text-sm text-gray-500 dark:text-gray-400\">\n          {totalProducts > 0 && (\n            <p>\n              Affichage de {Math.min(productsPerPage, products.length)} produits sur {totalProducts} au total\n            </p>\n          )}\n        </div>\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAkCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;yDAAY;oBAChB,IAAI;wBACF,aAAa;wBACb,SAAS;wBAET,mBAAmB;wBACnB,MAAM,qBAAqB,MAAM,MAAM;wBACvC,IAAI,CAAC,mBAAmB,EAAE,EAAE;4BAC1B,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;wBACpD,cAAc,eAAe,UAAU;wBAEvC,iBAAiB;wBACjB,MAAM;oBACR,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,SAAS,IAAI,OAAO,IAAI;oBAC1B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,IAAI;YACF,aAAa;YACb,SAAS;YAET,yBAAyB;YACzB,MAAM,SAAS,IAAI;YACnB,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;YACxC,IAAI,kBAAkB,OAAO,MAAM,CAAC,cAAc;YAClD,OAAO,MAAM,CAAC,QAAQ,OAAO,CAAC,cAAc,CAAC,IAAI;YACjD,OAAO,MAAM,CAAC,QAAQ,OAAO;YAE7B,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;YAEjE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ;YACzB,iBAAiB,KAAK,KAAK;YAC3B,cAAc,KAAK,IAAI,CAAC,KAAK,KAAK,GAAG;QACvC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,QAAQ;qDAAW;oBACvB,eAAe,IAAI,0CAA0C;oBAC7D;gBACF;oDAAG,MAAM,kBAAkB;YAE3B;+CAAO,IAAM,aAAa;;QAC5B;sCAAG;QAAC;QAAY;KAAiB;IAEjC,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAY;IAEhB,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,oDAAoD;YAC/D;QACF;QAEA,IAAI;YACF,cAAc;YAEd,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACzD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,2IAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,eAAY;;;;;0DACb,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,SAAM;;;;;0DACP,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,SAAM;;;;;0DACP,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,cAAW;;;;;0DACZ,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAOb,uBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iJAAA,CAAA,wBAAqB;4BAAC,WAAU;;;;;;sCACjC,6LAAC;sCAAM;;;;;;;;;;;;8BAKX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAGhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CACC,OAAO,oBAAoB;4CAC3B,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK,IAAI;4CACvD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWpC,6LAAC;oBAAI,WAAU;8BACZ,aAAa,SAAS,MAAM,KAAK,kBAChC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;6CAGvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,6LAAC;oCAAM,WAAU;8CACd,SAAS,MAAM,GAAG,IACjB,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,SAAS,iBAChB,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,QAAQ,SAAS;4DACtB,KAAK,QAAQ,IAAI;4DACjB,IAAI;4DACJ,OAAO;gEAAE,WAAW;4DAAQ;;;;;iFAG9B,6LAAC;4DAAI,WAAU;sEAAwD;;;;;;;;;;;;;;;;8DAM7E,6LAAC;oDAAG,WAAU;8DACX,QAAQ,SAAS;;;;;;8DAEpB,6LAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;8DAEf,6LAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ,EAAE,QAAQ;;;;;;8DAE7B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;gEAC1D,WAAU;0EAEV,cAAA,6LAAC,iJAAA,CAAA,QAAK;;;;;;;;;;0EAER,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;gEAC/D,WAAU;0EAEV,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;0EAET,6LAAC;gEACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;gEAC7C,UAAU,eAAe,QAAQ,EAAE;gEACnC,WAAW,CAAC,0EAA0E,EACpF,eAAe,QAAQ,EAAE,GAAG,kCAAkC,IAC9D;0EAED,eAAe,QAAQ,EAAE,iBAAG,6LAAC,iJAAA,CAAA,YAAS;oEAAC,WAAU;;;;;yFAAoB,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;2CA/C7E,QAAQ,EAAE;;;;kEAsDrB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS;4CAAG,WAAU;sDACvB,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAW9C,aAAa,mBACZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gCACxD,UAAU,gBAAgB,KAAK;gCAC/B,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,KAAK,YACjB,iDACA,+CACJ;0CACH;;;;;;4BAIA,MAAM,IAAI,CAAC;gCAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;4BAAY,GAAG,GAAG,CAAC,CAAC,GAAG;gCACvD,iCAAiC;gCACjC,IAAI,aAAa;gCACjB,IAAI,cAAc,GAAG;oCACnB,aAAa,QAAQ;gCACvB,OAAO;oCACL,MAAM,SAAS,KAAK,KAAK,CAAC,IAAI;oCAC9B,IAAI,eAAe,QAAQ;wCACzB,aAAa,QAAQ;oCACvB,OAAO,IAAI,eAAe,aAAa,QAAQ;wCAC7C,aAAa,aAAa,IAAI;oCAChC,OAAO;wCACL,aAAa,cAAc,SAAS;oCACtC;gCACF;gCAEA,qBACE,6LAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,aACZ,0BACA,+CACJ;8CAED;mCATI;;;;;4BAYX;0CAEA,6LAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;gCACjE,UAAU,gBAAgB,cAAc;gCACxC,WAAW,CAAC,qBAAqB,EAC/B,gBAAgB,cAAc,YAC1B,iDACA,+CACJ;0CACH;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,mBACf,6LAAC;;4BAAE;4BACa,KAAK,GAAG,CAAC,iBAAiB,SAAS,MAAM;4BAAE;4BAAe;4BAAc;;;;;;;;;;;;;;;;;;;;;;;AAOpG;GA/XwB;;QACP,qIAAA,CAAA,YAAS;QACP,0HAAA,CAAA,UAAO;;;KAFF", "debugId": null}}]}