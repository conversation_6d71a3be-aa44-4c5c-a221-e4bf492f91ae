'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaDownload, FaEnvelope, FaPrint } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from 'react-hot-toast';
import { generateMoonelecPdfWithLogo } from '@/components/pdf/MoonelecPdfGeneratorWithLogo';

// Types
interface QuoteItem {
  id: string;
  productId: string;
  productReference: string;
  productName: string;
  quantity: number;
  unitPrice: number;
}

interface Quote {
  id: string;
  quoteNumber: string;
  clientName: string;
  companyName: string;
  clientEmail: string;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  totalAmount: number;
  createdAt: string;
  validUntil: string;
  notes: string;
  items: QuoteItem[];
}

export default function QuotePdfPage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [quote, setQuote] = useState<Quote | null>(null);
  const pdfContentRef = useRef<HTMLDivElement>(null);

  // Données simulées pour le devis
  const mockQuote: Quote = {
    id: '2',
    quoteNumber: 'Q-2023-002',
    clientName: 'Fatima Zahra',
    companyName: 'Entreprise XYZ',
    clientEmail: '<EMAIL>',
    status: 'APPROVED',
    totalAmount: 8750,
    createdAt: '2023-07-10',
    validUntil: '2023-08-10',
    notes: 'Livraison gratuite pour toute commande supérieure à 5000 MAD.',
    items: [
      {
        id: '1',
        productId: '1',
        productReference: 'LED-A60-10W',
        productName: 'Ampoule LED A60 10W E27 Blanc Chaud 2700K',
        quantity: 50,
        unitPrice: 35
      },
      {
        id: '2',
        productId: '2',
        productReference: 'SPOT-GU10-5W',
        productName: 'Spot LED GU10 5W Blanc Neutre 4000K',
        quantity: 30,
        unitPrice: 45
      },
      {
        id: '3',
        productId: '3',
        productReference: 'CABLE-3G1.5',
        productName: 'Câble électrique 3G1.5 mm² - 100m',
        quantity: 5,
        unitPrice: 850
      }
    ]
  };

  useEffect(() => {
    // Simuler le chargement des données
    const timer = setTimeout(() => {
      setQuote(mockQuote);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleSendEmail = async () => {
    setIsSending(true);

    try {
      // Simuler l'envoi d'email
      console.log('Envoi du devis par email à:', quote?.clientEmail);

      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1500));

      alert(`Le devis a été envoyé avec succès à ${quote?.clientEmail}`);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      alert('Une erreur est survenue lors de l\'envoi de l\'email');
    } finally {
      setIsSending(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header - Non-printable */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 print:hidden">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Devis {quote?.quoteNumber}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Client: {quote?.clientName} - {quote?.companyName}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <Link href="/admin/quotes">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Retour à la liste
              </motion.button>
            </Link>
            <motion.button
              onClick={handlePrint}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <FaPrint />
              Imprimer
            </motion.button>
            <motion.button
              onClick={handleSendEmail}
              disabled={isSending}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2 ${
                isSending ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {isSending ? (
                <>
                  <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Envoi...
                </>
              ) : (
                <>
                  <FaEnvelope />
                  Envoyer par email
                </>
              )}
            </motion.button>
            <motion.button
              onClick={() => generateMoonelecPdfWithLogo(quote)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center gap-2"
            >
              <FaDownload />
              Télécharger
            </motion.button>
          </div>
        </div>

        {/* PDF Content */}
        <div ref={pdfContentRef} className="bg-white rounded-lg shadow-md p-8 max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-start mb-8 border-b pb-6">
            <div>
              <div className="relative w-48 h-16">
                <Image
                  src="/images/logo/logo-moonelec.png"
                  alt="Moonelec Logo"
                  fill
                  style={{ objectFit: 'contain', objectPosition: 'left' }}
                />
              </div>
              <div className="mt-4 text-sm text-gray-600">
                <p>Derb El Youssoufía, Rue 78, N°89,</p>
                <p>Bd El Fida - Casablanca-Maroc</p>
                <p>Tél: +212 522 80 80 80</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
            <div className="text-right">
              <h2 className="text-2xl font-bold text-primary">DEVIS</h2>
              <p className="text-gray-600 mt-1">N° {quote?.quoteNumber}</p>
              <p className="text-gray-600">Date: {quote?.createdAt}</p>
              <p className="text-gray-600">Valide jusqu'au: {quote?.validUntil}</p>
            </div>
          </div>

          {/* Client Information */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-2">Client</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <p className="font-semibold">{quote?.companyName}</p>
              <p>À l'attention de: {quote?.clientName}</p>
              <p>Email: {quote?.clientEmail}</p>
            </div>
          </div>

          {/* Quote Items */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-2">Détails du devis</h3>
            <table className="min-w-full border">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 border text-left">Référence</th>
                  <th className="py-2 px-4 border text-left">Description</th>
                  <th className="py-2 px-4 border text-center">Quantité</th>
                  <th className="py-2 px-4 border text-right">Prix unitaire (MAD)</th>
                  <th className="py-2 px-4 border text-right">Total (MAD)</th>
                </tr>
              </thead>
              <tbody>
                {quote?.items.map((item) => (
                  <tr key={item.id} className="border-b">
                    <td className="py-2 px-4 border text-sm">{item.productReference}</td>
                    <td className="py-2 px-4 border text-sm">{item.productName}</td>
                    <td className="py-2 px-4 border text-center text-sm">{item.quantity}</td>
                    <td className="py-2 px-4 border text-right text-sm">
                      {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(item.unitPrice)}
                    </td>
                    <td className="py-2 px-4 border text-right text-sm">
                      {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(item.quantity * item.unitPrice)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="bg-gray-100">
                  <td colSpan={4} className="py-2 px-4 border text-right font-semibold">Total:</td>
                  <td className="py-2 px-4 border text-right font-bold">
                    {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(quote?.totalAmount || 0)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          {/* Notes */}
          {quote?.notes && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-2">Notes</h3>
              <div className="border rounded-lg p-4 bg-gray-50 text-sm text-gray-600">
                {quote.notes}
              </div>
            </div>
          )}

          {/* Terms and Conditions */}
          <div className="mb-8 text-sm text-gray-600">
            <h3 className="text-lg font-semibold mb-2">Conditions générales</h3>
            <ul className="list-disc pl-5 space-y-1">
              <li>Ce devis est valable jusqu'à la date indiquée ci-dessus.</li>
              <li>Les prix sont indiqués en Dirhams Marocains (MAD) et sont hors taxes.</li>
              <li>Les délais de livraison sont donnés à titre indicatif.</li>
              <li>Le paiement doit être effectué selon les modalités convenues.</li>
              <li>Toute commande implique l'acceptation de nos conditions générales de vente.</li>
            </ul>
          </div>

          {/* Signature */}
          <div className="flex justify-between items-start mt-12">
            <div>
              <p className="font-semibold">Pour Moonelec</p>
              <div className="mt-8 border-t border-gray-300 pt-2 w-48">
                <p className="text-sm text-gray-600">Signature et cachet</p>
              </div>
            </div>
            <div>
              <p className="font-semibold">Pour le client</p>
              <div className="mt-8 border-t border-gray-300 pt-2 w-48">
                <p className="text-sm text-gray-600">Signature et cachet</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RouteGuard>
  );
}
