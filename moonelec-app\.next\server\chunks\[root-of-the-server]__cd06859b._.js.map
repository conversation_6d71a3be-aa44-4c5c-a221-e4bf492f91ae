{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/commercials.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { createCommercialUser, hashPassword } from './auth';\nimport { UserRole } from '@prisma/client';\n\n// Obtenir tous les commerciaux avec pagination et recherche\nexport async function getCommercials(options?: {\n  search?: string;\n  skip?: number;\n  take?: number;\n}) {\n  const { search, skip = 0, take = 50 } = options || {};\n\n  const where = search\n    ? {\n        user: {\n          OR: [\n            { firstname: { contains: search } },\n            { lastname: { contains: search } },\n            { email: { contains: search } },\n            { username: { contains: search } },\n          ],\n        },\n      }\n    : {};\n\n  const [commercials, total] = await Promise.all([\n    prisma.commercial.findMany({\n      where,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            username: true,\n            firstname: true,\n            lastname: true,\n            telephone: true,\n            createdAt: true,\n            updatedAt: true,\n            role: true,\n          },\n        },\n        commercialclient: {\n          include: {\n            client: {\n              include: {\n                user: {\n                  select: {\n                    firstname: true,\n                    lastname: true,\n                    email: true,\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n      skip,\n      take,\n      orderBy: {\n        user: {\n          lastname: 'asc',\n        },\n      },\n    }),\n    prisma.commercial.count({ where }),\n  ]);\n\n  return { commercials, total };\n}\n\n// Obtenir un commercial par son ID\nexport async function getCommercialById(id: string) {\n  return prisma.commercial.findUnique({\n    where: { id },\n    include: {\n      user: {\n        select: {\n          id: true,\n          email: true,\n          username: true,\n          firstname: true,\n          lastname: true,\n          telephone: true,\n          createdAt: true,\n          updatedAt: true,\n          role: true,\n        },\n      },\n      commercialclient: {\n        include: {\n          client: {\n            include: {\n              user: {\n                select: {\n                  firstname: true,\n                  lastname: true,\n                  email: true,\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n  });\n}\n\n// Créer un nouveau commercial\nexport async function createCommercial(data: {\n  email: string;\n  username: string;\n  password: string;\n  firstname: string;\n  lastname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  try {\n    const result = await createCommercialUser(data);\n    return result;\n  } catch (error) {\n    console.error('Error creating commercial:', error);\n    throw error;\n  }\n}\n\n// Mettre à jour un commercial existant\nexport async function updateCommercial(\n  id: string,\n  data: {\n    email?: string;\n    firstname?: string;\n    lastname?: string;\n    telephone?: string;\n    profile_photo?: string;\n  }\n) {\n  const { email, firstname, lastname, telephone, profile_photo } = data;\n\n  // Trouver le commercial pour obtenir l'ID de l'utilisateur\n  const commercial = await prisma.commercial.findUnique({\n    where: { id },\n    include: { user: true },\n  });\n\n  if (!commercial) {\n    throw new Error('Commercial not found');\n  }\n\n  return prisma.$transaction(async (tx) => {\n    // Mettre à jour les informations de l'utilisateur\n    if (email || firstname || lastname || telephone) {\n      await tx.user.update({\n        where: { id: commercial.userId },\n        data: {\n          ...(email && { email }),\n          ...(firstname && { firstname }),\n          ...(lastname && { lastname }),\n          ...(telephone && { telephone }),\n        },\n      });\n    }\n\n    // Mettre à jour les informations spécifiques au commercial\n    if (profile_photo !== undefined) {\n      await tx.commercial.update({\n        where: { id },\n        data: {\n          profile_photo,\n        },\n      });\n    }\n\n    // Récupérer le commercial mis à jour\n    return tx.commercial.findUnique({\n      where: { id },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            username: true,\n            firstname: true,\n            lastname: true,\n            telephone: true,\n            createdAt: true,\n            updatedAt: true,\n            role: true,\n          },\n        },\n      },\n    });\n  });\n}\n\n// Supprimer un commercial\nexport async function deleteCommercial(id: string) {\n  // Trouver le commercial pour obtenir l'ID de l'utilisateur\n  const commercial = await prisma.commercial.findUnique({\n    where: { id },\n    include: { user: true },\n  });\n\n  if (!commercial) {\n    throw new Error('Commercial not found');\n  }\n\n  return prisma.$transaction(async (tx) => {\n    // Supprimer d'abord les relations avec les clients\n    await tx.commercialclient.deleteMany({\n      where: { commercialId: id },\n    });\n\n    // Supprimer le commercial\n    await tx.commercial.delete({\n      where: { id },\n    });\n\n    // Supprimer l'utilisateur associé\n    return tx.user.delete({\n      where: { id: commercial.userId },\n    });\n  });\n}\n\n// Assigner un client à un commercial\nexport async function assignClientToCommercial(commercialId: string, clientId: string) {\n  return prisma.commercialclient.create({\n    data: {\n      commercialId,\n      clientId,\n    },\n  });\n}\n\n// Retirer un client d'un commercial\nexport async function removeClientFromCommercial(commercialId: string, clientId: string) {\n  return prisma.commercialclient.delete({\n    where: {\n      commercialId_clientId: {\n        commercialId,\n        clientId,\n      },\n    },\n  });\n}\n\n// Obtenir les clients d'un commercial\nexport async function getCommercialClients(commercialId: string) {\n  const commercialClients = await prisma.commercialclient.findMany({\n    where: { commercialId },\n    include: {\n      client: {\n        include: {\n          user: {\n            select: {\n              id: true,\n              email: true,\n              username: true,\n              firstname: true,\n              lastname: true,\n              telephone: true,\n            },\n          },\n        },\n      },\n    },\n  });\n\n  return commercialClients.map((cc) => cc.client);\n}\n\n// Obtenir les statistiques d'un commercial\nexport async function getCommercialStats(commercialId: string) {\n  // Nombre de clients\n  const clientCount = await prisma.commercialclient.count({\n    where: { commercialId },\n  });\n\n  // Nombre de commandes des clients de ce commercial\n  const clients = await prisma.commercialclient.findMany({\n    where: { commercialId },\n    select: { clientId: true },\n  });\n\n  const clientIds = clients.map((c) => c.clientId);\n\n  const orderCount = await prisma.order.count({\n    where: {\n      clientId: { in: clientIds },\n    },\n  });\n\n  // Montant total des commandes\n  const orders = await prisma.order.findMany({\n    where: {\n      clientId: { in: clientIds },\n    },\n    select: { totalAmount: true },\n  });\n\n  const totalAmount = orders.reduce((sum, order) => sum + order.totalAmount, 0);\n\n  return {\n    clientCount,\n    orderCount,\n    totalAmount,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAIO,eAAe,eAAe,OAIpC;IACC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAEpD,MAAM,QAAQ,SACV;QACE,MAAM;YACJ,IAAI;gBACF;oBAAE,WAAW;wBAAE,UAAU;oBAAO;gBAAE;gBAClC;oBAAE,UAAU;wBAAE,UAAU;oBAAO;gBAAE;gBACjC;oBAAE,OAAO;wBAAE,UAAU;oBAAO;gBAAE;gBAC9B;oBAAE,UAAU;wBAAE,UAAU;oBAAO;gBAAE;aAClC;QACH;IACF,IACA,CAAC;IAEL,MAAM,CAAC,aAAa,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC7C,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzB;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,MAAM;oBACR;gBACF;gBACA,kBAAkB;oBAChB,SAAS;wBACP,QAAQ;4BACN,SAAS;gCACP,MAAM;oCACJ,QAAQ;wCACN,WAAW;wCACX,UAAU;wCACV,OAAO;oCACT;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;YACA;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,UAAU;gBACZ;YACF;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAAE;QAAM;KACjC;IAED,OAAO;QAAE;QAAa;IAAM;AAC9B;AAGO,eAAe,kBAAkB,EAAU;IAChD,OAAO,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;QAClC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,MAAM;gBACJ,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,WAAW;oBACX,WAAW;oBACX,WAAW;oBACX,MAAM;gBACR;YACF;YACA,kBAAkB;gBAChB,SAAS;oBACP,QAAQ;wBACN,SAAS;4BACP,MAAM;gCACJ,QAAQ;oCACN,WAAW;oCACX,UAAU;oCACV,OAAO;gCACT;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF;AAGO,eAAe,iBAAiB,IAQtC;IACC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,eAAe,iBACpB,EAAU,EACV,IAMC;IAED,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;IAEjE,2DAA2D;IAC3D,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;QACpD,OAAO;YAAE;QAAG;QACZ,SAAS;YAAE,MAAM;QAAK;IACxB;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,kDAAkD;QAClD,IAAI,SAAS,aAAa,YAAY,WAAW;YAC/C,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBACnB,OAAO;oBAAE,IAAI,WAAW,MAAM;gBAAC;gBAC/B,MAAM;oBACJ,GAAI,SAAS;wBAAE;oBAAM,CAAC;oBACtB,GAAI,aAAa;wBAAE;oBAAU,CAAC;oBAC9B,GAAI,YAAY;wBAAE;oBAAS,CAAC;oBAC5B,GAAI,aAAa;wBAAE;oBAAU,CAAC;gBAChC;YACF;QACF;QAEA,2DAA2D;QAC3D,IAAI,kBAAkB,WAAW;YAC/B,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBACzB,OAAO;oBAAE;gBAAG;gBACZ,MAAM;oBACJ;gBACF;YACF;QACF;QAEA,qCAAqC;QACrC,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC;YAC9B,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,MAAM;oBACR;gBACF;YACF;QACF;IACF;AACF;AAGO,eAAe,iBAAiB,EAAU;IAC/C,2DAA2D;IAC3D,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,UAAU,CAAC;QACpD,OAAO;YAAE;QAAG;QACZ,SAAS;YAAE,MAAM;QAAK;IACxB;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,mDAAmD;QACnD,MAAM,GAAG,gBAAgB,CAAC,UAAU,CAAC;YACnC,OAAO;gBAAE,cAAc;YAAG;QAC5B;QAEA,0BAA0B;QAC1B,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACzB,OAAO;gBAAE;YAAG;QACd;QAEA,kCAAkC;QAClC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YACpB,OAAO;gBAAE,IAAI,WAAW,MAAM;YAAC;QACjC;IACF;AACF;AAGO,eAAe,yBAAyB,YAAoB,EAAE,QAAgB;IACnF,OAAO,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACpC,MAAM;YACJ;YACA;QACF;IACF;AACF;AAGO,eAAe,2BAA2B,YAAoB,EAAE,QAAgB;IACrF,OAAO,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACpC,OAAO;YACL,uBAAuB;gBACrB;gBACA;YACF;QACF;IACF;AACF;AAGO,eAAe,qBAAqB,YAAoB;IAC7D,MAAM,oBAAoB,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAC/D,OAAO;YAAE;QAAa;QACtB,SAAS;YACP,QAAQ;gBACN,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,UAAU;4BACV,WAAW;wBACb;oBACF;gBACF;YACF;QACF;IACF;IAEA,OAAO,kBAAkB,GAAG,CAAC,CAAC,KAAO,GAAG,MAAM;AAChD;AAGO,eAAe,mBAAmB,YAAoB;IAC3D,oBAAoB;IACpB,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;QACtD,OAAO;YAAE;QAAa;IACxB;IAEA,mDAAmD;IACnD,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QACrD,OAAO;YAAE;QAAa;QACtB,QAAQ;YAAE,UAAU;QAAK;IAC3B;IAEA,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,QAAQ;IAE/C,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;QAC1C,OAAO;YACL,UAAU;gBAAE,IAAI;YAAU;QAC5B;IACF;IAEA,8BAA8B;IAC9B,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACzC,OAAO;YACL,UAAU;gBAAE,IAAI;YAAU;QAC5B;QACA,QAAQ;YAAE,aAAa;QAAK;IAC9B;IAEA,MAAM,cAAc,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;IAE3E,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/commercials/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getCommercials, createCommercial } from '@/lib/commercials';\nimport { findUserByEmail, findUserByUsername } from '@/lib/auth';\n\n// GET /api/commercials - Récupérer tous les commerciaux\nexport async function GET(req: NextRequest) {\n  try {\n    const searchParams = req.nextUrl.searchParams;\n    const search = searchParams.get('search') || undefined;\n    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;\n    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;\n\n    const { commercials, total } = await getCommercials({\n      search,\n      skip,\n      take,\n    });\n\n    return NextResponse.json({ commercials, total });\n  } catch (error: any) {\n    console.error('Error fetching commercials:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch commercials' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/commercials - Créer un nouveau commercial\nexport async function POST(req: NextRequest) {\n  try {\n    const body = await req.json();\n    const {\n      email,\n      username,\n      password,\n      firstname,\n      lastname,\n      telephone,\n      profile_photo,\n    } = body;\n\n    // Vérifier que tous les champs requis sont présents\n    if (!email || !username || !password || !firstname || !lastname) {\n      return NextResponse.json(\n        { error: 'Email, username, password, firstname, and lastname are required' },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier si l'email est déjà utilisé\n    const existingEmail = await findUserByEmail(email);\n    if (existingEmail) {\n      return NextResponse.json(\n        { error: 'Email is already in use' },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier si le nom d'utilisateur est déjà utilisé\n    const existingUsername = await findUserByUsername(username);\n    if (existingUsername) {\n      return NextResponse.json(\n        { error: 'Username is already in use' },\n        { status: 400 }\n      );\n    }\n\n    // Créer le commercial\n    const result = await createCommercial({\n      email,\n      username,\n      password,\n      firstname,\n      lastname,\n      telephone,\n      profile_photo,\n    });\n\n    // Retourner une réponse de succès sans le mot de passe\n    return NextResponse.json(\n      {\n        commercial: {\n          id: result.commercial.id,\n          user: {\n            id: result.user.id,\n            email: result.user.email,\n            username: result.user.username,\n            firstname: result.user.firstname,\n            lastname: result.user.lastname,\n            telephone: result.user.telephone,\n            role: result.user.role,\n          },\n          profile_photo: result.commercial.profile_photo,\n        },\n      },\n      { status: 201 }\n    );\n  } catch (error: any) {\n    console.error('Error creating commercial:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to create commercial' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAE9E,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;YAClD;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;YAAa;QAAM;IAChD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA8B,GACxD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACd,GAAG;QAEJ,oDAAoD;QACpD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkE,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,MAAM,gBAAgB,MAAM,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QAC5C,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oDAAoD;QACpD,MAAM,mBAAmB,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QAClD,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE;YACpC;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,uDAAuD;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,YAAY;gBACV,IAAI,OAAO,UAAU,CAAC,EAAE;gBACxB,MAAM;oBACJ,IAAI,OAAO,IAAI,CAAC,EAAE;oBAClB,OAAO,OAAO,IAAI,CAAC,KAAK;oBACxB,UAAU,OAAO,IAAI,CAAC,QAAQ;oBAC9B,WAAW,OAAO,IAAI,CAAC,SAAS;oBAChC,UAAU,OAAO,IAAI,CAAC,QAAQ;oBAC9B,WAAW,OAAO,IAAI,CAAC,SAAS;oBAChC,MAAM,OAAO,IAAI,CAAC,IAAI;gBACxB;gBACA,eAAe,OAAO,UAAU,CAAC,aAAa;YAChD;QACF,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA8B,GACxD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}