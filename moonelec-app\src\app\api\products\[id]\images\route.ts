import { NextRequest, NextResponse } from 'next/server';
import { addProductImages, deleteAllProductImages } from '@/lib/products';

// POST /api/products/[id]/images - Add images to a product
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await req.json();
    const { images } = body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json(
        { error: 'Images array is required' },
        { status: 400 }
      );
    }

    // Validate each image has a URL
    for (const image of images) {
      if (!image.url) {
        return NextResponse.json(
          { error: 'Each image must have a URL' },
          { status: 400 }
        );
      }
    }

    // Add images to the product
    await addProductImages(params.id, images);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error adding product images:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to add product images' },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id]/images - Delete all images for a product
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await deleteAllProductImages(params.id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting product images:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete product images' },
      { status: 500 }
    );
  }
}
