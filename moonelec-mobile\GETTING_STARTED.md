# Guide de Démarrage Rapide - Moonelec Mobile

## 🚀 Démarrage en 5 minutes

### 1. Prérequis
- Node.js 18+ installé
- Expo CLI installé (`npm install -g @expo/cli`)
- Application web Moonelec en cours d'exécution
- Appareil mobile ou émulateur

### 2. Installation
```bash
cd moonelec-mobile
npm install
```

### 3. Configuration API
Modifiez l'URL dans `src/services/api.ts` :
```typescript
// Remplacez par l'IP de votre machine
const BASE_URL = 'http://*************:3000/api';
```

### 4. Lancement
```bash
npm start
```

### 5. Test sur appareil
- Scannez le QR code avec l'app Expo Go
- Ou utilisez un émulateur Android/iOS

## 📱 Fonctionnalités Testables

### Authentification
- **Login**: Utilisez les identifiants de votre app web
- **Rôles**: CLIENT, COMMERCIAL, ADMIN

### Navigation
- **Onglets**: Acc<PERSON><PERSON>, <PERSON>duits, <PERSON><PERSON>, Rapports (commerciaux), Profil
- **<PERSON><PERSON><PERSON>s détaillé<PERSON>**: Produit, <PERSON><PERSON>, Rapport

### Fonctionnalités par Rôle

#### CLIENT
✅ Consulter le catalogue produits
✅ Rechercher des produits
✅ Voir les détails produits
✅ Consulter ses devis
✅ Créer un nouveau devis

#### COMMERCIAL
✅ Toutes les fonctionnalités CLIENT
✅ Créer des rapports de visite
✅ Ajouter photos/vidéos/audio
✅ Gérer ses rapports

#### ADMIN
✅ Toutes les fonctionnalités
✅ Vue d'ensemble dashboard
✅ Accès complet aux données

## 🔧 Résolution de Problèmes

### Erreur de connexion API
1. Vérifiez que l'app web est démarrée
2. Vérifiez l'URL dans `api.ts`
3. Testez l'URL dans un navigateur

### Problème de permissions
1. Autorisez caméra/microphone dans les paramètres
2. Redémarrez l'application

### Erreur de build
1. Supprimez `node_modules` et `package-lock.json`
2. Relancez `npm install`

## 📋 Checklist de Test

### Authentification
- [ ] Login avec identifiants valides
- [ ] Gestion des erreurs de login
- [ ] Déconnexion

### Navigation
- [ ] Navigation entre onglets
- [ ] Navigation vers détails
- [ ] Bouton retour

### Produits
- [ ] Liste des produits
- [ ] Recherche produits
- [ ] Détail produit
- [ ] Images produit

### Devis
- [ ] Liste des devis
- [ ] Création de devis
- [ ] Ajout de produits au devis
- [ ] Statuts des devis

### Rapports (Commerciaux)
- [ ] Liste des rapports
- [ ] Création de rapport
- [ ] Ajout de photos
- [ ] Enregistrement audio
- [ ] Upload de documents

### Interface
- [ ] Design responsive
- [ ] Couleurs Moonelec (#006db7)
- [ ] Icônes appropriées
- [ ] Messages d'erreur clairs

## 🎯 Prochaines Étapes

1. **Testez toutes les fonctionnalités** avec différents rôles
2. **Configurez l'URL de production** pour le déploiement
3. **Personnalisez les assets** (logo, splash screen)
4. **Ajoutez des tests automatisés**
5. **Préparez le déploiement** sur les stores

## 📞 Support

En cas de problème :
1. Consultez les logs dans la console Expo
2. Vérifiez la connectivité réseau
3. Testez l'API avec Postman/curl
4. Contactez l'équipe de développement

---

**Félicitations ! 🎉**
Votre application mobile Moonelec est maintenant prête à être utilisée et testée.
