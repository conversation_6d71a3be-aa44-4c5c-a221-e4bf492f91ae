'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaSearch, Fa<PERSON>ilter, FaEye, FaEdit, FaTrash, FaPlus, FaUser, FaBuilding, FaMapMarkerAlt, FaClock } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';

// Types
interface Meeting {
  id: string;
  title: string;
  date: string;
  time: string;
  client: {
    id: string;
    name: string;
    company: string;
  };
  location: string;
  status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED' | 'POSTPONED';
  notes: string;
}

export default function CommercialMeetingsPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [viewMode, setViewMode] = useState<'upcoming' | 'past' | 'all'>('upcoming');

  // Données simulées pour les rendez-vous
  const mockMeetings: Meeting[] = [
    {
      id: '1',
      title: 'Présentation des nouveaux produits',
      date: '18/07/2023',
      time: '10:00',
      client: {
        id: '1',
        name: 'Ahmed Benani',
        company: 'Société ABC'
      },
      location: 'Siège de la société ABC, Casablanca',
      status: 'SCHEDULED',
      notes: 'Préparer les catalogues des nouvelles gammes LED et domotique.'
    },
    {
      id: '2',
      title: 'Suivi de commande',
      date: '20/07/2023',
      time: '14:30',
      client: {
        id: '2',
        name: 'Fatima Zahra',
        company: 'Entreprise XYZ'
      },
      location: 'Bureaux XYZ, Rabat',
      status: 'SCHEDULED',
      notes: 'Discuter des délais de livraison pour la commande ORD-12344.'
    },
    {
      id: '3',
      title: 'Négociation contrat annuel',
      date: '25/07/2023',
      time: '11:00',
      client: {
        id: '3',
        name: 'Mohammed Alami',
        company: 'Groupe MNO'
      },
      location: 'Siège MNO, Tanger',
      status: 'SCHEDULED',
      notes: 'Préparer une offre spéciale pour le contrat annuel.'
    },
    {
      id: '4',
      title: 'Démonstration produits domotiques',
      date: '10/06/2023',
      time: '15:00',
      client: {
        id: '4',
        name: 'Karim Idrissi',
        company: 'Société DEF'
      },
      location: 'Showroom Moonelec, Casablanca',
      status: 'COMPLETED',
      notes: 'Le client a montré un intérêt particulier pour les systèmes de sécurité connectés.'
    },
    {
      id: '5',
      title: 'Réunion projet d\'installation',
      date: '05/06/2023',
      time: '09:30',
      client: {
        id: '5',
        name: 'Nadia Tazi',
        company: 'Entreprise GHI'
      },
      location: 'Chantier GHI, Marrakech',
      status: 'CANCELLED',
      notes: 'Annulé par le client en raison de retards sur le chantier.'
    }
  ];

  useEffect(() => {
    // Simuler le chargement des données
    const timer = setTimeout(() => {
      setMeetings(mockMeetings);
      setIsLoading(false);
      setTotalPages(Math.ceil(mockMeetings.length / 10));
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filtrer les rendez-vous en fonction de la recherche, du statut et du mode d'affichage
  const filteredMeetings = meetings.filter(meeting => {
    const matchesSearch = 
      meeting.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meeting.client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meeting.client.company.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus ? meeting.status === selectedStatus : true;
    
    // Filtrer par mode d'affichage (à venir, passés, tous)
    const today = new Date();
    const meetingDate = new Date(meeting.date.split('/').reverse().join('-'));
    
    if (viewMode === 'upcoming') {
      return matchesSearch && matchesStatus && meetingDate >= today;
    } else if (viewMode === 'past') {
      return matchesSearch && matchesStatus && meetingDate < today;
    } else {
      return matchesSearch && matchesStatus;
    }
  });

  // Trier les rendez-vous par date (les plus proches en premier)
  const sortedMeetings = [...filteredMeetings].sort((a, b) => {
    const dateA = new Date(a.date.split('/').reverse().join('-'));
    const dateB = new Date(b.date.split('/').reverse().join('-'));
    
    if (viewMode === 'past') {
      return dateB.getTime() - dateA.getTime(); // Plus récents en premier pour les passés
    } else {
      return dateA.getTime() - dateB.getTime(); // Plus proches en premier pour les à venir
    }
  });

  // Pagination
  const meetingsPerPage = 10;
  const paginatedMeetings = sortedMeetings.slice(
    (currentPage - 1) * meetingsPerPage,
    currentPage * meetingsPerPage
  );

  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'POSTPONED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Fonction pour obtenir le libellé du statut en français
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'SCHEDULED':
        return 'Planifié';
      case 'COMPLETED':
        return 'Terminé';
      case 'CANCELLED':
        return 'Annulé';
      case 'POSTPONED':
        return 'Reporté';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['COMMERCIAL']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            Gestion des Rendez-vous
          </h1>
          <Link href="/commercial/meetings/new">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              <FaPlus />
              <span>Planifier un rendez-vous</span>
            </motion.button>
          </Link>
        </div>

        {/* View Mode Tabs */}
        <div className="flex mb-6 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setViewMode('upcoming')}
            className={`py-2 px-4 font-medium text-sm ${
              viewMode === 'upcoming'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            À venir
          </button>
          <button
            onClick={() => setViewMode('past')}
            className={`py-2 px-4 font-medium text-sm ${
              viewMode === 'past'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Passés
          </button>
          <button
            onClick={() => setViewMode('all')}
            className={`py-2 px-4 font-medium text-sm ${
              viewMode === 'all'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Tous
          </button>
        </div>

        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Rechercher par titre, client ou entreprise..."
              />
            </div>
            <div className="w-full md:w-64">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaFilter className="text-gray-400" />
                </div>
                <select
                  value={selectedStatus || ''}
                  onChange={(e) => setSelectedStatus(e.target.value || null)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="">Tous les statuts</option>
                  <option value="SCHEDULED">Planifié</option>
                  <option value="COMPLETED">Terminé</option>
                  <option value="CANCELLED">Annulé</option>
                  <option value="POSTPONED">Reporté</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Meetings List */}
        {paginatedMeetings.length > 0 ? (
          <div className="space-y-4">
            {paginatedMeetings.map((meeting) => (
              <motion.div
                key={meeting.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div className="flex items-start mb-2 md:mb-0">
                      <div className="flex-shrink-0 w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                        <FaCalendarAlt size={20} />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                          {meeting.title}
                        </h3>
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                          <FaUser className="mr-1" size={12} />
                          <span className="mr-2">{meeting.client.name}</span>
                          <FaBuilding className="mr-1" size={12} />
                          <span>{meeting.client.company}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(meeting.status)}`}>
                        {getStatusLabel(meeting.status)}
                      </span>
                      <Link href={`/commercial/meetings/${meeting.id}`}>
                        <span className="p-2 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 cursor-pointer">
                          <FaEye title="Voir les détails" />
                        </span>
                      </Link>
                      {meeting.status === 'SCHEDULED' && (
                        <>
                          <Link href={`/commercial/meetings/${meeting.id}/edit`}>
                            <span className="p-2 text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 cursor-pointer">
                              <FaEdit title="Modifier" />
                            </span>
                          </Link>
                          <button
                            onClick={() => {
                              if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
                                // Logique d'annulation
                                console.log('Annulation du rendez-vous', meeting.id);
                              }
                            }}
                            className="p-2 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <FaTrash title="Annuler" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                        <FaClock />
                      </div>
                      <div>
                        <p className="font-medium">Date et heure</p>
                        <p>{meeting.date} à {meeting.time}</p>
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center text-green-600 dark:text-green-400 mr-3">
                        <FaMapMarkerAlt />
                      </div>
                      <div>
                        <p className="font-medium">Lieu</p>
                        <p>{meeting.location}</p>
                      </div>
                    </div>
                  </div>
                  {meeting.notes && (
                    <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md text-sm text-gray-600 dark:text-gray-300">
                      <p className="font-medium mb-1">Notes:</p>
                      <p>{meeting.notes}</p>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div className="flex justify-center mb-4">
              <FaCalendarAlt className="text-5xl text-gray-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              Aucun rendez-vous trouvé
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {viewMode === 'upcoming' 
                ? "Vous n'avez pas de rendez-vous à venir." 
                : viewMode === 'past' 
                  ? "Vous n'avez pas de rendez-vous passés." 
                  : "Aucun rendez-vous ne correspond à vos critères de recherche."}
            </p>
            <Link href="/commercial/meetings/new">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Planifier un rendez-vous
              </motion.button>
            </Link>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Précédent
              </button>
              
              {Array.from({ length: totalPages }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index + 1)}
                  className={`px-3 py-1 rounded-md ${
                    currentPage === index + 1
                      ? 'bg-primary text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {index + 1}
                </button>
              ))}
              
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Suivant
              </button>
            </nav>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
