"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!******************************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernCategoriesGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight,FaCog,FaHome,FaIndustry,FaLightbulb,FaPlug,FaShieldAlt!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst featuredCategories = [\n    {\n        id: '1',\n        name: 'Éclairage LED',\n        description: 'Solutions d\\'éclairage modernes et économes en énergie',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 26,\n            columnNumber: 11\n        }, undefined),\n        productCount: 156,\n        href: '/products?category=eclairage',\n        gradient: 'from-yellow-400/80 to-orange-500/80'\n    },\n    {\n        id: '2',\n        name: 'Prises & Interrupteurs',\n        description: 'Gamme complète de prises et interrupteurs design',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaPlug, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 36,\n            columnNumber: 11\n        }, undefined),\n        productCount: 89,\n        href: '/products?category=prises',\n        gradient: 'from-blue-400/80 to-indigo-500/80'\n    },\n    {\n        id: '3',\n        name: 'Protection Électrique',\n        description: 'Dispositifs de protection et sécurité électrique',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaShieldAlt, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 46,\n            columnNumber: 11\n        }, undefined),\n        productCount: 67,\n        href: '/products?category=protection',\n        gradient: 'from-green-400/80 to-emerald-500/80'\n    },\n    {\n        id: '4',\n        name: 'Câbles & Conducteurs',\n        description: 'Câbles électriques haute qualité pour tous usages',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCog, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 56,\n            columnNumber: 11\n        }, undefined),\n        productCount: 134,\n        href: '/products?category=cables',\n        gradient: 'from-purple-400/80 to-violet-500/80'\n    },\n    {\n        id: '5',\n        name: 'Domotique',\n        description: 'Solutions intelligentes pour la maison connectée',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHome, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 66,\n            columnNumber: 11\n        }, undefined),\n        productCount: 45,\n        href: '/products?category=domotique',\n        gradient: 'from-cyan-400/80 to-blue-500/80'\n    },\n    {\n        id: '6',\n        name: 'Industriel',\n        description: 'Équipements électriques pour applications industrielles',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaIndustry, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 76,\n            columnNumber: 11\n        }, undefined),\n        productCount: 78,\n        href: '/products?category=industriel',\n        gradient: 'from-red-400/80 to-rose-500/80'\n    }\n];\nfunction ModernCategoriesGrid() {\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernCategoriesGrid.useEffect\": ()=>{\n            const loadCategories = {\n                \"ModernCategoriesGrid.useEffect.loadCategories\": async ()=>{\n                    try {\n                        const response = await fetch('/api/categories');\n                        if (response.ok) {\n                            const data = await response.json();\n                            const realCategories = (data.categories || []).slice(0, 6).map({\n                                \"ModernCategoriesGrid.useEffect.loadCategories.realCategories\": (cat, index)=>({\n                                        id: cat.id,\n                                        name: cat.name,\n                                        description: \"Solutions \".concat(cat.name.toLowerCase(), \" de haute qualit\\xe9\"),\n                                        image: cat.image || '/images/hero/placeholder.svg',\n                                        icon: featuredCategories[index % featuredCategories.length].icon,\n                                        productCount: Math.floor(Math.random() * 200) + 50,\n                                        href: \"/products?category=\".concat(cat.id),\n                                        gradient: featuredCategories[index % featuredCategories.length].gradient\n                                    })\n                            }[\"ModernCategoriesGrid.useEffect.loadCategories.realCategories\"]);\n                            // If we have real categories, use them, otherwise fallback to featured\n                            if (realCategories.length > 0) {\n                                setCategories(realCategories);\n                            } else {\n                                setCategories(featuredCategories);\n                            }\n                        } else {\n                            setCategories(featuredCategories);\n                        }\n                    } catch (error) {\n                        console.error('Error loading categories:', error);\n                        setCategories(featuredCategories); // Fallback\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ModernCategoriesGrid.useEffect.loadCategories\"];\n            loadCategories();\n        }\n    }[\"ModernCategoriesGrid.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-light-gray\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        ...Array(7)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-gray-200 rounded-lg mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-light-gray\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"lg:col-span-1 bg-white rounded-xl p-8 shadow-lg flex flex-col justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-charcoal mb-4 font-heading\",\n                                            children: \"Cat\\xe9gories les Plus Populaires\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed\",\n                                            children: \"D\\xe9couvrez notre s\\xe9lection de produits \\xe9lectriques de haute qualit\\xe9, organis\\xe9s par cat\\xe9gories pour faciliter votre recherche.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-moonelec-red rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Plus de 500 produits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-electric-blue rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Livraison rapide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    className: \"group inline-flex items-center justify-center space-x-2 bg-moonelec-red hover:bg-moonelec-red-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Explorer Produits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {\n                                            className: \"group-hover:translate-x-1 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: category.href,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-64 bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-cover bg-center bg-no-repeat\",\n                                                            style: {\n                                                                backgroundImage: \"url(\".concat(category.image, \")\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t \".concat(category.gradient, \" opacity-80 group-hover:opacity-90 transition-opacity duration-300\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 h-full flex flex-col justify-between p-6 text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 bg-white/20 backdrop-blur-sm rounded-lg\",\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium opacity-90\",\n                                                                        children: [\n                                                                            category.productCount,\n                                                                            \" produits\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold mb-2 group-hover:text-white transition-colors\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-90 leading-relaxed\",\n                                                                    children: category.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 px-4 py-2 rounded-lg transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Voir produits\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {\n                                                                        className: \"text-xs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 border-2 border-transparent group-hover:border-white/30 rounded-xl transition-all duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, category.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                    children: [\n                        {\n                            number: '500+',\n                            label: 'Produits'\n                        },\n                        {\n                            number: '50+',\n                            label: 'Marques'\n                        },\n                        {\n                            number: '1000+',\n                            label: 'Clients Satisfaits'\n                        },\n                        {\n                            number: '24/7',\n                            label: 'Support'\n                        }\n                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-moonelec-red mb-2\",\n                                    children: stat.number\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 font-medium\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernCategoriesGrid, \"Rt0Mh744vHF1SR1qL5bOrIka3Sw=\");\n_c = ModernCategoriesGrid;\nvar _c;\n$RefreshReg$(_c, \"ModernCategoriesGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});