'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: ReactNode;
  className?: string;
  animate?: boolean;
  delay?: number;
  onClick?: () => void;
  hoverEffect?: boolean;
}

export default function Card({
  children,
  className = '',
  animate = false,
  delay = 0,
  onClick,
  hoverEffect = true
}: CardProps) {
  const baseStyles = 'bg-white dark:bg-[#1a1a1a] rounded-lg shadow-md overflow-hidden';
  
  if (animate) {
    return (
      <motion.div
        className={`${baseStyles} ${className}`}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: '-50px' }}
        transition={{ duration: 0.5, delay }}
        onClick={onClick}
        whileHover={hoverEffect ? { y: -5, transition: { duration: 0.2 } } : {}}
      >
        {children}
      </motion.div>
    );
  }
  
  return (
    <div 
      className={`${baseStyles} ${className} ${hoverEffect ? 'transition-transform hover:-translate-y-1 duration-200' : ''}`}
      onClick={onClick}
    >
      {children}
    </div>
  );
}
