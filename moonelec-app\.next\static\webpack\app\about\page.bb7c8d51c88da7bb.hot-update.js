"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/layout/ModernHeader.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ModernHeader.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaCheckCircle,FaChevronDown,FaGlobe,FaSearch,FaShieldAlt,FaShippingFast,FaShoppingCart,FaTimes,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ShopMegaMenu = (param)=>{\n    let { isOpen, onClose } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: -10\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -10\n            },\n            transition: {\n                duration: 0.2\n            },\n            className: \"absolute top-full left-0 w-full bg-white shadow-xl border-t border-gray-100 z-50\",\n            onMouseLeave: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Cat\\xe9gories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=eclairage\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"\\xc9clairage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=cables\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"C\\xe2bles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=prises\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Prises & Interrupteurs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=protection\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Protection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Marques\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=schneider\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Schneider Electric\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=legrand\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Legrand\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=abb\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"ABB\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=siemens\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Siemens\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/residential\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"R\\xe9sidentiel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/commercial\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Commercial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/industrial\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Industriel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/smart-home\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Maison Intelligente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-light-gray p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-2\",\n                                    children: \"Produit Vedette\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-white rounded-md mb-3 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"Nouveau syst\\xe8me d'\\xe9clairage LED intelligent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/featured\",\n                                    className: \"btn btn-primary text-sm py-2 px-4\",\n                                    children: \"D\\xe9couvrir\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 39,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n            lineNumber: 31,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ShopMegaMenu;\nconst FeatureBar = ()=>{\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaShippingFast, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 96,\n                columnNumber: 13\n            }, undefined),\n            title: \"Livraison Gratuite\",\n            description: \"Sur toutes commandes de plus de 500 MAD\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaCheckCircle, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 101,\n                columnNumber: 13\n            }, undefined),\n            title: \"Garantie 30 Jours\",\n            description: \"Garantie de remboursement de 30 jours\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaGlobe, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 106,\n                columnNumber: 13\n            }, undefined),\n            title: \"Livraison Nationale\",\n            description: \"Livraison dans tout le Maroc\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaShieldAlt, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 111,\n                columnNumber: 13\n            }, undefined),\n            title: \"Paiement Sécurisé\",\n            description: \"100% sécurisé et protégé\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-t border-gray-100 py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-center space-x-4 group cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 group-hover:text-moonelec-red transition-colors duration-300\",\n                                children: feature.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-charcoal text-sm\",\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = FeatureBar;\nfunction ModernHeader() {\n    _s();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isShopMenuOpen, setIsShopMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { user, signOut } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"ModernHeader.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"ModernHeader.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernHeader.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ModernHeader.useEffect\"];\n        }\n    }[\"ModernHeader.useEffect\"], []);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(\"/products?search=\".concat(encodeURIComponent(searchQuery.trim())));\n            setSearchQuery('');\n        }\n    };\n    const navigation = [\n        {\n            name: 'Accueil',\n            href: '/'\n        },\n        {\n            name: 'À Propos',\n            href: '/about'\n        },\n        {\n            name: 'Boutique',\n            href: '/products',\n            hasDropdown: true,\n            onMouseEnter: ()=>setIsShopMenuOpen(true),\n            onMouseLeave: ()=>setIsShopMenuOpen(false)\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-40 transition-all duration-300 \".concat(isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-white'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-12 h-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: \"/images/logo/logo-moonelec.png\",\n                                                alt: \"Moonelec Logo\",\n                                                fill: true,\n                                                className: \"object-contain\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-charcoal font-heading\",\n                                            children: \"Moonelec\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: item.onMouseEnter,\n                                            onMouseLeave: item.onMouseLeave,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"flex items-center space-x-1 font-medium transition-colors duration-200 \".concat(pathname === item.href ? 'text-moonelec-red' : 'text-charcoal hover:text-moonelec-red'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.hasDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaChevronDown, {\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSearch,\n                                            className: \"hidden md:flex\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Rechercher des produits...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"w-64 pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaSearch, {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(user.role.toLowerCase(), \"/dashboard\"),\n                                                    className: \"p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>signOut(),\n                                                    className: \"text-sm text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"D\\xe9connexion\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaUser, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/cart\",\n                                            className: \"relative p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaShoppingCart, {\n                                                    className: \"text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-moonelec-red text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                            className: \"lg:hidden p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaTimes, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaBars, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 71\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopMegaMenu, {\n                        isOpen: isShopMenuOpen,\n                        onClose: ()=>setIsShopMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"lg:hidden bg-white border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearch,\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Rechercher...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaSearch, {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"block py-2 font-medium transition-colors \".concat(pathname === item.href ? 'text-moonelec-red' : 'text-charcoal hover:text-moonelec-red'),\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: item.name\n                                            }, item.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureBar, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ModernHeader, \"QMPdWmtEOIcEQfKEgdl9NvgxJoA=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c2 = ModernHeader;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ShopMegaMenu\");\n$RefreshReg$(_c1, \"FeatureBar\");\n$RefreshReg$(_c2, \"ModernHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/ModernHeader.tsx\n"));

/***/ })

});