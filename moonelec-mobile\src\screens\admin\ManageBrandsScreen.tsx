import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { Brand } from '../../types';
import WebApiService from '../../services/webApi';
import LoadingAnimation from '../../components/LoadingAnimation';
import ImagePickerComponent from '../../components/ImagePicker';

const ManageBrandsScreen: React.FC = () => {
  const { user } = useAuth();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Form state
  const [newBrand, setNewBrand] = useState({
    name: '',
    description: '',
    logo: '',
  });

  useEffect(() => {
    if (user?.role !== 'ADMIN') {
      Alert.alert('Accès refusé', 'Cette fonctionnalité est réservée aux administrateurs');
      return;
    }
    loadBrands();
  }, [user]);

  const loadBrands = async () => {
    try {
      console.log('📦 Loading brands with WebAPI...');
      const brandsData = await WebApiService.getBrands();
      setBrands(brandsData);
      console.log('✅ Brands loaded successfully:', brandsData.length);
    } catch (error) {
      console.error('❌ Error loading brands:', error);
      Alert.alert('Erreur', 'Impossible de charger les marques');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBrands();
    setRefreshing(false);
  };

  const createBrand = async () => {
    if (!newBrand.name.trim()) {
      Alert.alert('Erreur', 'Le nom de la marque est requis');
      return;
    }

    setIsCreating(true);
    try {
      console.log('📦 Creating brand with WebAPI:', newBrand.name);

      const createdBrand = await WebApiService.createBrand({
        name: newBrand.name.trim(),
        description: newBrand.description.trim() || undefined,
        logo: newBrand.logo || undefined,
      });

      setBrands(prev => [createdBrand, ...prev]);
      setShowCreateModal(false);
      resetForm();
      Alert.alert('Succès', 'Marque créée avec succès');
      console.log('✅ Brand created successfully');
    } catch (error) {
      console.error('❌ Error creating brand:', error);
      Alert.alert('Erreur', error instanceof Error ? error.message : 'Erreur lors de la création');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setNewBrand({
      name: '',
      description: '',
      logo: '',
    });
  };

  const deleteBrand = async (brandId: string, brandName: string) => {
    Alert.alert(
      'Confirmer la suppression',
      `Êtes-vous sûr de vouloir supprimer la marque "${brandName}" ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('📦 Deleting brand with WebAPI:', brandId);
              await WebApiService.deleteBrand(brandId);

              setBrands(prev => prev.filter(brand => brand.id !== brandId));
              Alert.alert('Succès', 'Marque supprimée avec succès');
              console.log('✅ Brand deleted successfully');
            } catch (error) {
              console.error('❌ Error deleting brand:', error);
              Alert.alert('Erreur', 'Erreur lors de la suppression');
            }
          },
        },
      ]
    );
  };

  const filteredBrands = brands.filter(brand =>
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderBrand = ({ item }: { item: Brand }) => (
    <View style={styles.brandCard}>
      <View style={styles.brandInfo}>
        <Text style={styles.brandName}>{item.name}</Text>
        {item.description && (
          <Text style={styles.brandDescription} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        <Text style={styles.brandMeta}>
          Créée le {new Date(item.createdAt).toLocaleDateString('fr-FR')}
        </Text>
      </View>

      <View style={styles.brandActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => {
            Alert.alert('Info', 'Fonctionnalité d\'édition à venir');
          }}
        >
          <Ionicons name="pencil" size={16} color="#fff" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => deleteBrand(item.id, item.name)}
        >
          <Ionicons name="trash" size={16} color="#fff" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="storefront-outline" size={64} color="#ccc" />
      <Text style={styles.emptyText}>Aucune marque</Text>
      <Text style={styles.emptySubtext}>Créez votre première marque</Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingAnimation size={60} color="#006db7" />
        <Text style={styles.loadingText}>Chargement des marques...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🏪 Gestion des Marques</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateModal(true)}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher une marque..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Liste des marques */}
      <FlatList
        data={filteredBrands}
        renderItem={renderBrand}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />

      {/* Modal de création */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Nouvelle Marque</Text>
            <TouchableOpacity
              onPress={() => {
                setShowCreateModal(false);
                resetForm();
              }}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Nom de la marque *</Text>
              <TextInput
                style={styles.textInput}
                value={newBrand.name}
                onChangeText={(text) => setNewBrand(prev => ({ ...prev, name: text }))}
                placeholder="Ex: Schneider Electric, Legrand, etc."
                maxLength={100}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Logo de la marque</Text>
              <ImagePickerComponent
                onImageSelected={(imageUri) => setNewBrand(prev => ({ ...prev, logo: imageUri }))}
                currentImage={newBrand.logo}
                placeholder="Ajouter un logo"
                style={styles.imagePicker}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description (optionnel)</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={newBrand.description}
                onChangeText={(text) => setNewBrand(prev => ({ ...prev, description: text }))}
                placeholder="Description de la marque..."
                multiline
                numberOfLines={3}
                maxLength={500}
              />
            </View>

            <TouchableOpacity
              style={[styles.createButton, !newBrand.name.trim() && styles.createButtonDisabled]}
              onPress={createBrand}
              disabled={!newBrand.name.trim() || isCreating}
            >
              {isCreating ? (
                <LoadingAnimation size={20} color="#fff" />
              ) : (
                <Text style={styles.createButtonText}>Créer la marque</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#006db7',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  brandCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  brandDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  brandMeta: {
    fontSize: 12,
    color: '#999',
  },
  brandActions: {
    flexDirection: 'row',
    marginLeft: 12,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  editButton: {
    backgroundColor: '#006db7',
  },
  deleteButton: {
    backgroundColor: '#ed1c24',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  createButton: {
    backgroundColor: '#006db7',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  imagePicker: {
    alignSelf: 'flex-start',
  },
});

export default ManageBrandsScreen;
