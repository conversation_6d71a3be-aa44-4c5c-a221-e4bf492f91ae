'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import ModernHeader from '@/components/layout/ModernHeader';
import ModernProductsPage from '@/components/products/ModernProductsPage';
import ModernFooter from '@/components/layout/ModernFooter';

interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  mainImage: string | null;
  category: {
    id: string;
    name: string;
  } | null;
  brand: {
    id: string;
    name: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
}

interface Brand {
  id: string;
  name: string;
}

export default function ProductsPage() {
  const searchParams = useSearchParams();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        // Load categories and brands
        const [categoriesRes, brandsRes, productsRes] = await Promise.all([
          fetch('/api/categories'),
          fetch('/api/brands'),
          fetch('/api/products?take=50')
        ]);

        if (categoriesRes.ok) {
          const categoriesData = await categoriesRes.json();
          setCategories(categoriesData.categories || []);
        }

        if (brandsRes.ok) {
          const brandsData = await brandsRes.json();
          setBrands(brandsData.brands || []);
        }

        if (productsRes.ok) {
          const productsData = await productsRes.json();
          // Transform products to match the expected format
          const transformedProducts = (productsData.products || []).map((product: any) => ({
            ...product,
            price: Math.floor(Math.random() * 1000) + 100, // Mock price
            rating: Math.floor(Math.random() * 5) + 1, // Mock rating
            isNew: Math.random() > 0.8,
            isOnSale: Math.random() > 0.7,
            originalPrice: Math.random() > 0.7 ? Math.floor(Math.random() * 1000) + 200 : undefined
          }));
          setProducts(transformedProducts);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-light-gray">
        <ModernHeader />
        <div className="container mx-auto px-6 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-moonelec-red mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des produits...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <ModernHeader />
      <ModernProductsPage
        initialProducts={products}
        categories={categories}
        brands={brands}
        searchParams={{
          search: searchParams.get('search') || undefined,
          category: searchParams.get('category') || undefined,
          brand: searchParams.get('brand') || undefined,
          page: searchParams.get('page') || undefined,
        }}
      />
      <ModernFooter />
    </div>
  );
}
