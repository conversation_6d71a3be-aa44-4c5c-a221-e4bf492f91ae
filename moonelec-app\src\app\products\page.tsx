'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>aTag, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaBox, FaLightbulb, FaPlug, FaWrench } from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import PageLayout from '@/components/layout/PageLayout';
import { useCart } from '@/context/CartContext';

// Types
interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  characteristics: Record<string, any>;
  mainImage: string | null;
  category: {
    id: string;
    name: string;
  } | null;
  brand: {
    id: string;
    name: string;
  } | null;
  productImages: {
    id: string;
    url: string;
    alt: string;
    order: number;
  }[];
}

interface Category {
  id: string;
  name: string;
}

interface Brand {
  id: string;
  name: string;
}

export default function ProductsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { addItem } = useCart();

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fonction pour ajouter un produit au panier
  const addToCart = (product: Product, quantity: number) => {
    addItem(product, quantity);

    // Afficher une notification ou un message de confirmation
    alert(`${product.name} ajouté au panier`);
  };

  // Filtres
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [selectedBrand, setSelectedBrand] = useState(searchParams.get('brand') || '');
  const [showFilters, setShowFilters] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const productsPerPage = 12;

  useEffect(() => {
    // Récupérer les paramètres de l'URL
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const brand = searchParams.get('brand') || '';
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;

    setSearchTerm(search);
    setSelectedCategory(category);
    setSelectedBrand(brand);
    setCurrentPage(page);

    // Charger les données
    fetchCategories();
    fetchBrands();
    fetchProducts(search, category, brand, page);
  }, [searchParams]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      const data = await response.json();
      setCategories(data.categories);
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await fetch('/api/brands');
      if (!response.ok) {
        throw new Error('Failed to fetch brands');
      }
      const data = await response.json();
      setBrands(data.brands);
    } catch (err) {
      console.error('Error fetching brands:', err);
    }
  };

  const fetchProducts = async (search: string, category: string, brand: string, page: number) => {
    try {
      setIsLoading(true);
      setError(null);

      const skip = (page - 1) * productsPerPage;

      let url = `/api/products?skip=${skip}&take=${productsPerPage}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (category) url += `&categoryId=${encodeURIComponent(category)}`;
      if (brand) url += `&brandId=${encodeURIComponent(brand)}`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();
      setProducts(data.products);
      setTotalProducts(data.total);
    } catch (err: any) {
      console.error('Error fetching products:', err);
      setError(err.message || 'An error occurred while fetching products');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateUrlAndFetch();
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleBrandChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedBrand(e.target.value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateUrlAndFetch(page);
  };

  const updateUrlAndFetch = (page: number = 1) => {
    // Construire les paramètres de recherche
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (selectedCategory) params.set('category', selectedCategory);
    if (selectedBrand) params.set('brand', selectedBrand);
    if (page > 1) params.set('page', page.toString());

    // Mettre à jour l'URL
    router.push(`/products?${params.toString()}`);
  };

  const handleApplyFilters = () => {
    setCurrentPage(1);
    updateUrlAndFetch();
    setShowFilters(false);
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedBrand('');
    setCurrentPage(1);
    router.push('/products');
  };

  // Calculer le nombre total de pages
  const totalPages = Math.ceil(totalProducts / productsPerPage);

  // Générer les numéros de page à afficher
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Afficher toutes les pages si leur nombre est inférieur ou égal à maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Toujours inclure la première page
      pageNumbers.push(1);

      // Calculer les pages du milieu
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Ajuster si nous sommes près du début ou de la fin
      if (currentPage <= 2) {
        endPage = 4;
      } else if (currentPage >= totalPages - 1) {
        startPage = totalPages - 3;
      }

      // Ajouter des points de suspension si nécessaire
      if (startPage > 2) {
        pageNumbers.push('...');
      }

      // Ajouter les pages du milieu
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // Ajouter des points de suspension si nécessaire
      if (endPage < totalPages - 1) {
        pageNumbers.push('...');
      }

      // Toujours inclure la dernière page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  return (
    <PageLayout showLoading={false}>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-blue-900 to-indigo-900 py-20 mb-12">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 bg-black opacity-50"></div>
          <div className="absolute inset-0 bg-grid-white/[0.05]"></div>
          <div className="absolute h-full w-full bg-gradient-to-t from-blue-900/80 to-transparent"></div>

          {/* Animated Light Bulbs */}
          <motion.div
            className="absolute top-10 left-1/4 text-yellow-300 opacity-70"
            animate={{
              y: [0, -10, 0],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            <FaLightbulb size={30} />
          </motion.div>

          <motion.div
            className="absolute top-20 right-1/3 text-yellow-300 opacity-50"
            animate={{
              y: [0, -15, 0],
              opacity: [0.5, 0.8, 0.5]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          >
            <FaLightbulb size={20} />
          </motion.div>

          <motion.div
            className="absolute bottom-10 right-1/4 text-yellow-300 opacity-60"
            animate={{
              y: [0, -12, 0],
              opacity: [0.6, 0.9, 0.6]
            }}
            transition={{
              duration: 3.5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 0.5
            }}
          >
            <FaLightbulb size={25} />
          </motion.div>

          <motion.div
            className="absolute top-1/2 left-1/5 text-blue-300 opacity-40"
            animate={{
              y: [0, -8, 0],
              opacity: [0.4, 0.7, 0.4]
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1.5
            }}
          >
            <FaPlug size={20} />
          </motion.div>

          <motion.div
            className="absolute bottom-1/3 left-1/3 text-blue-300 opacity-30"
            animate={{
              y: [0, -10, 0],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          >
            <FaWrench size={22} />
          </motion.div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Catalogue de Produits
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Découvrez notre gamme complète de produits électriques de haute qualité pour tous vos projets
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 pb-16">

      {/* Search and Filters */}
      <div className="mb-12">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
          <div className="flex flex-col md:flex-row gap-6 items-center">
            <form onSubmit={handleSearch} className="flex-1 w-full">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Rechercher par nom, référence ou description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-5 py-4 pl-12 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-base"
                />
                <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg" />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 px-5 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors font-medium"
                >
                  Rechercher
                </button>
              </div>
            </form>

            <div className="md:w-auto w-full">
              <motion.button
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                onClick={() => setShowFilters(!showFilters)}
                className="w-full md:w-auto px-6 py-4 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors flex items-center justify-center font-medium"
              >
                <FaFilter className="mr-2" />
                {showFilters ? 'Masquer les filtres' : 'Afficher les filtres'}
              </motion.button>
            </div>
          </div>

        {/* Filters Panel */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Filtrer les produits</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Catégorie
                </label>
                <div className="relative">
                  <select
                    id="category"
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                  >
                    <option value="">Toutes les catégories</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                    <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>

                {/* Category Pills */}
                <div className="mt-3 flex flex-wrap gap-2">
                  {categories.slice(0, 5).map((category) => (
                    <button
                      key={category.id}
                      onClick={() => {
                        setSelectedCategory(selectedCategory === category.id ? '' : category.id);
                      }}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label htmlFor="brand" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Marque
                </label>
                <div className="relative">
                  <select
                    id="brand"
                    value={selectedBrand}
                    onChange={handleBrandChange}
                    className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                  >
                    <option value="">Toutes les marques</option>
                    {brands.map((brand) => (
                      <option key={brand.id} value={brand.id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                    <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>

                {/* Brand Pills */}
                <div className="mt-3 flex flex-wrap gap-2">
                  {brands.slice(0, 5).map((brand) => (
                    <button
                      key={brand.id}
                      onClick={() => {
                        setSelectedBrand(selectedBrand === brand.id ? '' : brand.id);
                      }}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        selectedBrand === brand.id
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {brand.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <motion.button
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                onClick={handleResetFilters}
                className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium"
              >
                Réinitialiser les filtres
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                onClick={handleApplyFilters}
                className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium"
              >
                Appliquer les filtres
              </motion.button>
            </div>
          </motion.div>
        )}
        </div>
      </div>

      {/* Active Filters */}
      {(searchTerm || selectedCategory || selectedBrand) && (
        <div className="mb-8">
          <div className="bg-blue-50 dark:bg-blue-900/10 rounded-xl p-4 border border-blue-100 dark:border-blue-900/30">
            <div className="flex flex-wrap items-center gap-3">
              <span className="text-blue-700 dark:text-blue-300 font-medium">Filtres actifs:</span>

              {searchTerm && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg text-sm flex items-center shadow-sm border border-gray-100 dark:border-gray-700"
                >
                  <FaSearch className="mr-2 text-blue-500" />
                  <span className="mr-2 font-medium">{searchTerm}</span>
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      updateUrlAndFetch();
                    }}
                    className="ml-1 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  >
                    &times;
                  </button>
                </motion.div>
              )}

              {selectedCategory && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg text-sm flex items-center shadow-sm border border-gray-100 dark:border-gray-700"
                >
                  <FaTag className="mr-2 text-blue-500" />
                  <span className="mr-2 font-medium">
                    {categories.find(c => c.id === selectedCategory)?.name || 'Catégorie'}
                  </span>
                  <button
                    onClick={() => {
                      setSelectedCategory('');
                      updateUrlAndFetch();
                    }}
                    className="ml-1 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  >
                    &times;
                  </button>
                </motion.div>
              )}

              {selectedBrand && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg text-sm flex items-center shadow-sm border border-gray-100 dark:border-gray-700"
                >
                  <FaCopyright className="mr-2 text-purple-500" />
                  <span className="mr-2 font-medium">
                    {brands.find(b => b.id === selectedBrand)?.name || 'Marque'}
                  </span>
                  <button
                    onClick={() => {
                      setSelectedBrand('');
                      updateUrlAndFetch();
                    }}
                    className="ml-1 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  >
                    &times;
                  </button>
                </motion.div>
              )}

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleResetFilters}
                className="px-4 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-300 rounded-lg text-sm font-medium hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Effacer tous les filtres
              </motion.button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-5 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-xl border border-red-100 dark:border-red-900/30 flex items-center shadow-sm"
          >
            <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mr-4">
              <FaExclamationTriangle className="text-red-500 dark:text-red-300 text-xl" />
            </div>
            <div>
              <h3 className="font-semibold text-red-800 dark:text-red-200 mb-1">Erreur lors du chargement des produits</h3>
              <p>{error}</p>
            </div>
          </motion.div>
        </div>
      )}

      {/* Products Grid */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="relative w-20 h-20">
            <div className="absolute top-0 left-0 w-full h-full border-8 border-gray-200 dark:border-gray-700 rounded-full"></div>
            <div className="absolute top-0 left-0 w-full h-full border-8 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
          </div>
          <p className="mt-4 text-gray-500 dark:text-gray-400 text-lg">Chargement des produits...</p>
        </div>
      ) : products.length > 0 ? (
        <div className="mb-12">
          <div className="mb-6 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              {totalProducts} produit{totalProducts > 1 ? 's' : ''} trouvé{totalProducts > 1 ? 's' : ''}
            </h2>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Page {currentPage} sur {totalPages}
            </div>
          </div>

          <motion.div
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1
                }
              }
            }}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8"
          >
            {products.map((product) => (
              <ProductCard key={product.id} product={product} addToCart={addToCart} />
            ))}
          </motion.div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-10 text-center my-12 border border-gray-100 dark:border-gray-700">
          <div className="flex justify-center mb-6">
            <div className="bg-gray-100 dark:bg-gray-700 p-5 rounded-full">
              <FaSearch className="text-4xl text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-3">
            Aucun produit trouvé
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
            Nous n'avons trouvé aucun produit correspondant à vos critères de recherche. Essayez de modifier vos filtres ou d'effectuer une nouvelle recherche.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleResetFilters}
            className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium"
          >
            Voir tous les produits
          </motion.button>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8 mb-12">
          <nav className="flex items-center">
            <div className="flex items-center bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
              <motion.button
                whileHover={{ backgroundColor: 'rgba(243, 244, 246, 1)' }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-5 py-3 flex items-center justify-center ${
                  currentPage === 1
                    ? 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                } border-r border-gray-200 dark:border-gray-700 transition-colors`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Précédent
              </motion.button>

              <div className="hidden md:flex">
                {getPageNumbers().map((page, index) => (
                  typeof page === 'number' ? (
                    <motion.button
                      key={index}
                      whileHover={{ backgroundColor: currentPage === page ? undefined : 'rgba(243, 244, 246, 1)' }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handlePageChange(page)}
                      className={`w-12 h-12 flex items-center justify-center font-medium text-sm border-r border-gray-200 dark:border-gray-700 ${
                        currentPage === page
                          ? 'bg-primary text-white'
                          : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                      } transition-colors`}
                    >
                      {page}
                    </motion.button>
                  ) : (
                    <span key={index} className="w-12 h-12 flex items-center justify-center text-gray-500 dark:text-gray-400 border-r border-gray-200 dark:border-gray-700">
                      {page}
                    </span>
                  )
                ))}
              </div>

              <div className="md:hidden px-4 py-3 flex items-center justify-center font-medium text-gray-700 dark:text-gray-200 border-r border-gray-200 dark:border-gray-700">
                Page {currentPage} sur {totalPages}
              </div>

              <motion.button
                whileHover={{ backgroundColor: 'rgba(243, 244, 246, 1)' }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-5 py-3 flex items-center justify-center ${
                  currentPage === totalPages
                    ? 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                } transition-colors`}
              >
                Suivant
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.button>
            </div>
          </nav>
        </div>
      )}
      </div>
    </PageLayout>
  );
}

interface ProductCardProps {
  product: Product;
  addToCart: (product: Product, quantity: number) => void;
}

function ProductCard({ product, addToCart }: ProductCardProps) {
  return (
    <motion.div
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
      }}
      whileHover={{ y: -8, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)' }}
      transition={{ duration: 0.2 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-100 dark:border-gray-700 group"
    >
      <Link href={`/products/${product.id}`} className="block h-full">
        <div className="relative h-56 bg-gray-100 dark:bg-gray-700 overflow-hidden">
          {product.mainImage ? (
            <>
              <Image
                src={product.mainImage}
                alt={product.name}
                fill
                style={{ objectFit: 'contain' }}
                className="p-4 transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-400">
              <FaBox size={48} />
            </div>
          )}

          {/* Action buttons */}
          <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-y-4 group-hover:translate-y-0">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white dark:bg-gray-800 text-primary px-4 py-2 rounded-full shadow-md flex items-center text-sm font-medium"
            >
              <FaEye className="mr-2" />
              Voir le produit
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-primary text-white px-4 py-2 rounded-full shadow-md flex items-center text-sm font-medium"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Ajouter au panier
                addToCart(product, 1);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Ajouter
            </motion.div>
          </div>
        </div>

        <div className="p-5">
          <div className="flex flex-wrap gap-2 mb-3">
            {product.category && (
              <span className="px-2 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300 rounded-full text-xs flex items-center font-medium">
                <FaTag className="mr-1" size={10} />
                {product.category.name}
              </span>
            )}

            {product.brand && (
              <span className="px-2 py-1 bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-300 rounded-full text-xs flex items-center font-medium">
                <FaCopyright className="mr-1" size={10} />
                {product.brand.name}
              </span>
            )}
          </div>

          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2 line-clamp-2 group-hover:text-primary transition-colors">
            {product.name}
          </h3>

          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            <span className="font-medium">Réf:</span> {product.reference}
          </p>

          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-4">
            {product.description}
          </p>

          {/* Caractéristiques principales */}
          <div className="mb-4 space-y-2">
            {product.characteristics && Object.entries(product.characteristics).slice(0, 3).map(([key, value]) => (
              <div key={key} className="flex items-start text-xs">
                <div className="w-3 h-3 rounded-full bg-blue-100 dark:bg-blue-900/30 flex-shrink-0 mt-0.5 mr-2"></div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300 capitalize">{key.replace(/_/g, ' ')}: </span>
                  <span className="text-gray-600 dark:text-gray-400">{String(value)}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="pt-3 border-t border-gray-100 dark:border-gray-700 flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-green-500 mr-1.5"></div>
              <span className="text-xs text-green-600 dark:text-green-400 font-medium">En stock</span>
            </div>

            <span className="text-primary text-sm font-medium flex items-center">
              Voir les détails
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </span>
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
