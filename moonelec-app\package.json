{"name": "moonelec-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio", "init:admin": "node scripts/init-admin.mjs", "db:reset": "node scripts/reset-db-and-create-client.js"}, "dependencies": {"@huggingface/inference": "^3.15.0", "@prisma/client": "^6.8.2", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/pdfkit": "^0.13.9", "@types/react-datepicker": "^7.0.0", "@xenova/transformers": "^2.17.2", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "date-fns": "^4.1.0", "expo-document-picker": "^13.1.5", "framer-motion": "^12.12.1", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jspdf-html2canvas": "^1.5.2", "moonelec-app": "file:", "multer": "^2.0.0", "mysql2": "^3.14.1", "next": "15.3.2", "next-auth": "^4.24.11", "openai": "^4.103.0", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf2pic": "^3.2.0", "pdfkit": "^0.17.1", "prisma": "^6.8.2", "react": "^19.0.0", "react-countup": "^6.5.3", "react-datepicker": "^8.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.49"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "node-fetch": "^3.3.2", "pdfjs-dist": "^5.2.133", "tailwindcss": "^4.1.7", "typescript": "^5"}}