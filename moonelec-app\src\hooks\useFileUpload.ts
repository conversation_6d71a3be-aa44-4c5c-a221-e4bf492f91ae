import { useState } from 'react';

interface UploadOptions {
  directory?: string;
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
}

export function useFileUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);

  const uploadFile = async (file: File, options: UploadOptions = {}) => {
    const { directory = 'uploads', onSuccess, onError } = options;
    
    setIsUploading(true);
    setProgress(0);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('directory', directory);
      
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 200);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      clearInterval(progressInterval);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload file');
      }
      
      const data = await response.json();
      setProgress(100);
      
      if (onSuccess) {
        onSuccess(data.url);
      }
      
      return data.url;
    } catch (err: any) {
      setError(err);
      
      if (onError) {
        onError(err);
      }
      
      throw err;
    } finally {
      setIsUploading(false);
    }
  };

  const uploadBase64 = async (base64Data: string, options: UploadOptions = {}) => {
    const { directory = 'uploads', onSuccess, onError } = options;
    
    setIsUploading(true);
    setProgress(0);
    setError(null);
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 200);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          base64: base64Data,
          directory,
        }),
      });
      
      clearInterval(progressInterval);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload file');
      }
      
      const data = await response.json();
      setProgress(100);
      
      if (onSuccess) {
        onSuccess(data.url);
      }
      
      return data.url;
    } catch (err: any) {
      setError(err);
      
      if (onError) {
        onError(err);
      }
      
      throw err;
    } finally {
      setIsUploading(false);
    }
  };

  return {
    uploadFile,
    uploadBase64,
    isUploading,
    progress,
    error,
  };
}
