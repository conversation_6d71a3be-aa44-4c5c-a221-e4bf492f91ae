'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaArrowLeft, FaEdit, FaTrash, FaFilePdf, Fa<PERSON><PERSON>ner, FaExclamationTriangle, FaCheck, FaTimes, FaExchangeAlt } from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import { QuoteStatus } from '@prisma/client';
import { formatDate, formatPrice } from '@/lib/utils';

interface QuoteItem {
  id: string;
  quantity: number;
  unitPrice: number | null;
  product: {
    id: string;
    name: string;
    reference: string;
    mainImage: string | null;
    category?: {
      id: string;
      name: string;
    } | null;
    brand?: {
      id: string;
      name: string;
    } | null;
  };
}

interface Quote {
  id: string;
  quoteNumber: string;
  status: QuoteStatus;
  createdAt: string;
  updatedAt: string;
  totalAmount: number | null;
  validUntil: string | null;
  pdfUrl: string | null;
  notes: string | null;
  createdByAdminId: string | null;
  client: {
    id: string;
    company_name: string | null;
    user: {
      firstname: string;
      lastname: string;
      email: string;
      telephone: string | null;
    };
  };
  quoteItems: QuoteItem[];
}

export default function AdminQuoteDetailPage() {
  const router = useRouter();
  const { id } = useParams();
  const { user } = useAuth();
  
  const [quote, setQuote] = useState<Quote | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Charger les détails du devis
  useEffect(() => {
    const fetchQuote = async () => {
      try {
        setIsLoading(true);
        
        const response = await fetch(`/api/quotes/${id}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Devis non trouvé');
          } else {
            throw new Error('Erreur lors du chargement du devis');
          }
        }
        
        const data = await response.json();
        setQuote(data);
      } catch (error) {
        console.error('Error fetching quote:', error);
        setError('Impossible de charger les détails du devis. Veuillez réessayer plus tard.');
      } finally {
        setIsLoading(false);
      }
    };
    
    if (id) {
      fetchQuote();
    }
  }, [id]);
  
  // Fonction pour obtenir la couleur en fonction du statut
  const getStatusColor = (status: QuoteStatus) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'CONVERTED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };
  
  // Fonction pour obtenir le libellé en français du statut
  const getStatusLabel = (status: QuoteStatus) => {
    switch (status) {
      case 'DRAFT':
        return 'Brouillon';
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvé';
      case 'REJECTED':
        return 'Refusé';
      case 'EXPIRED':
        return 'Expiré';
      case 'CONVERTED':
        return 'Converti';
      default:
        return status;
    }
  };
  
  // Fonction pour approuver un devis
  const handleApproveQuote = async () => {
    if (!quote) return;
    
    if (!confirm('Êtes-vous sûr de vouloir approuver ce devis ?')) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/quotes/${quote.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'APPROVED' }),
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors de l\'approbation du devis');
      }
      
      // Mettre à jour le statut du devis
      setQuote({ ...quote, status: 'APPROVED' });
    } catch (error) {
      console.error('Error approving quote:', error);
      alert('Impossible d\'approuver le devis. Veuillez réessayer plus tard.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Fonction pour rejeter un devis
  const handleRejectQuote = async () => {
    if (!quote) return;
    
    if (!confirm('Êtes-vous sûr de vouloir rejeter ce devis ?')) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/quotes/${quote.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'REJECTED' }),
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors du rejet du devis');
      }
      
      // Mettre à jour le statut du devis
      setQuote({ ...quote, status: 'REJECTED' });
    } catch (error) {
      console.error('Error rejecting quote:', error);
      alert('Impossible de rejeter le devis. Veuillez réessayer plus tard.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Fonction pour supprimer un devis
  const handleDeleteQuote = async () => {
    if (!quote) return;
    
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce devis ? Cette action est irréversible.')) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/quotes/${quote.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors de la suppression du devis');
      }
      
      // Rediriger vers la liste des devis
      router.push('/admin/quotes');
    } catch (error) {
      console.error('Error deleting quote:', error);
      alert('Impossible de supprimer le devis. Veuillez réessayer plus tard.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <Link href="/admin/quotes">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light mb-2"
              >
                <FaArrowLeft className="mr-2" />
                Retour à la liste
              </motion.button>
            </Link>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Détails du Devis
            </h1>
          </div>
          
          {quote && (
            <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
              {quote.status === 'DRAFT' && (
                <Link href={`/admin/quotes/${quote.id}/edit`}>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <FaEdit className="mr-2" />
                    Modifier
                  </motion.button>
                </Link>
              )}
              
              {quote.pdfUrl && (
                <a href={quote.pdfUrl} target="_blank" rel="noopener noreferrer">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
                  >
                    <FaFilePdf className="mr-2" />
                    PDF
                  </motion.button>
                </a>
              )}
              
              {(quote.status === 'DRAFT' || quote.status === 'PENDING') && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleDeleteQuote}
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
                >
                  {isSubmitting ? (
                    <FaSpinner className="animate-spin mr-2" />
                  ) : (
                    <FaTrash className="mr-2" />
                  )}
                  Supprimer
                </motion.button>
              )}
            </div>
          )}
        </div>
        
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-6 rounded-xl border border-red-100 dark:border-red-900/30 flex items-center mb-6 shadow-sm">
            <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mr-4">
              <FaExclamationTriangle className="text-red-500 dark:text-red-300 text-xl" />
            </div>
            <div>
              <h3 className="font-semibold text-red-800 dark:text-red-200 text-lg mb-1">Erreur</h3>
              <p>{error}</p>
            </div>
          </div>
        ) : !quote ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div className="flex justify-center mb-4">
              <FaExclamationTriangle className="text-5xl text-yellow-500" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              Devis non trouvé
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Le devis que vous recherchez n'existe pas ou a été supprimé.
            </p>
            <Link href="/admin/quotes">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Retour à la liste des devis
              </motion.button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Informations du devis */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-1">
                        {quote.quoteNumber}
                      </h2>
                      <p className="text-gray-500 dark:text-gray-400">
                        Créé le {formatDate(quote.createdAt)}
                      </p>
                    </div>
                    <span className={`mt-2 md:mt-0 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(quote.status)}`}>
                      {getStatusLabel(quote.status)}
                    </span>
                  </div>
                </div>
                
                {/* Informations du client */}
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
                    Informations du client
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mb-1">Nom</p>
                      <p className="text-gray-800 dark:text-white font-medium">
                        {quote.client.user.firstname} {quote.client.user.lastname}
                      </p>
                    </div>
                    
                    {quote.client.company_name && (
                      <div>
                        <p className="text-gray-500 dark:text-gray-400 text-sm mb-1">Entreprise</p>
                        <p className="text-gray-800 dark:text-white font-medium">
                          {quote.client.company_name}
                        </p>
                      </div>
                    )}
                    
                    <div>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mb-1">Email</p>
                      <p className="text-gray-800 dark:text-white font-medium">
                        {quote.client.user.email}
                      </p>
                    </div>
                    
                    {quote.client.user.telephone && (
                      <div>
                        <p className="text-gray-500 dark:text-gray-400 text-sm mb-1">Téléphone</p>
                        <p className="text-gray-800 dark:text-white font-medium">
                          {quote.client.user.telephone}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  {quote.createdByAdminId && (
                    <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md">
                      <p className="text-sm">
                        Ce devis a été créé par un administrateur pour le compte du client.
                      </p>
                    </div>
                  )}
                </div>
                
                {/* Produits */}
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
                    Produits
                  </h3>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Produit
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Référence
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Quantité
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Prix unitaire
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Total
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {quote.quoteItems.map((item) => (
                          <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 relative rounded overflow-hidden bg-gray-100 dark:bg-gray-700">
                                  {item.product.mainImage ? (
                                    <Image
                                      src={item.product.mainImage}
                                      alt={item.product.name}
                                      fill
                                      style={{ objectFit: 'cover' }}
                                    />
                                  ) : (
                                    <div className="flex items-center justify-center h-full text-gray-400">
                                      N/A
                                    </div>
                                  )}
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                                    {item.product.name}
                                  </div>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {item.product.category && (
                                      <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-full text-xs">
                                        {item.product.category.name}
                                      </span>
                                    )}
                                    {item.product.brand && (
                                      <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-full text-xs">
                                        {item.product.brand.name}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {item.product.reference}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {item.quantity}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                              {item.unitPrice ? formatPrice(item.unitPrice) : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white text-right">
                              {item.unitPrice ? formatPrice(item.unitPrice * item.quantity) : '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                {/* Notes */}
                {quote.notes && (
                  <div className="p-6 border-t border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                      Notes
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                      {quote.notes}
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Résumé et actions */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
                  Résumé
                </h3>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Numéro de devis</span>
                    <span className="text-gray-900 dark:text-white font-medium">{quote.quoteNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Date de création</span>
                    <span className="text-gray-900 dark:text-white">{formatDate(quote.createdAt)}</span>
                  </div>
                  {quote.validUntil && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Valide jusqu'au</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(quote.validUntil)}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Statut</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(quote.status)}`}>
                      {getStatusLabel(quote.status)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Nombre d'articles</span>
                    <span className="text-gray-900 dark:text-white">{quote.quoteItems.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Quantité totale</span>
                    <span className="text-gray-900 dark:text-white">
                      {quote.quoteItems.reduce((sum, item) => sum + item.quantity, 0)}
                    </span>
                  </div>
                  
                  {quote.totalAmount !== null && (
                    <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-800 dark:text-gray-200 font-semibold">Montant total</span>
                        <span className="text-xl font-bold text-primary">
                          {formatPrice(quote.totalAmount)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Actions */}
                {quote.status === 'PENDING' && (
                  <div className="space-y-3">
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={handleApproveQuote}
                      disabled={isSubmitting}
                      className="w-full py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center justify-center"
                    >
                      {isSubmitting ? (
                        <FaSpinner className="animate-spin mr-2" />
                      ) : (
                        <FaCheck className="mr-2" />
                      )}
                      Approuver
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={handleRejectQuote}
                      disabled={isSubmitting}
                      className="w-full py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center justify-center"
                    >
                      {isSubmitting ? (
                        <FaSpinner className="animate-spin mr-2" />
                      ) : (
                        <FaTimes className="mr-2" />
                      )}
                      Rejeter
                    </motion.button>
                  </div>
                )}
                
                {quote.status === 'APPROVED' && (
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => router.push(`/admin/orders/new?quoteId=${quote.id}`)}
                    className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
                  >
                    <FaExchangeAlt className="mr-2" />
                    Convertir en commande
                  </motion.button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
