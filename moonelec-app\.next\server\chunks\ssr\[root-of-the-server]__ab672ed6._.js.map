{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5bd12f4c.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_5bd12f4c-module__QuOMRq__className\",\n  \"variable\": \"inter_5bd12f4c-module__QuOMRq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5bd12f4c.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_932525c1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_932525c1-module__h_mYaW__className\",\n  \"variable\": \"jetbrains_mono_932525c1-module__h_mYaW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_932525c1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22variable%22:%22--font-jetbrains-mono%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22]}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_b9cf1356.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_b9cf1356-module__5UDsjW__className\",\n  \"variable\": \"poppins_b9cf1356-module__5UDsjW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_b9cf1356.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22variable%22:%22--font-poppins%22,%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22]}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/context/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/context/AuthContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/AuthContext.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/context/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/context/AuthContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/AuthContext.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/context/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/CartContext.tsx <module evaluation>\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/CartContext.tsx <module evaluation>\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6DACA", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/context/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/CartContext.tsx\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/CartContext.tsx\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yCACA", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/context/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/ThemeContext.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/ThemeContext.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8DACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/context/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/ThemeContext.tsx\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/ThemeContext.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0CACA", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/ToastProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/ToastProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/ToastProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/ToastProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/ToastProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/ToastProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/reportReminders.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { v4 as uuidv4 } from 'uuid';\n\n// Check for incomplete reports and send reminders\nexport async function checkIncompleteReports() {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  \n  // Get all commercials\n  const commercials = await prisma.commercial.findMany({\n    include: {\n      user: true,\n    },\n  });\n  \n  const results = [];\n  \n  for (const commercial of commercials) {\n    // Check if the commercial has submitted a report today\n    const report = await prisma.salesreport.findFirst({\n      where: {\n        commercialId: commercial.id,\n        submittedAt: {\n          gte: today,\n        },\n        isCompleted: true,\n      },\n    });\n    \n    if (!report) {\n      // Commercial hasn't submitted a report today\n      // Check if we need to send a reminder (every 2 hours)\n      const lastReminder = await prisma.salesreport.findFirst({\n        where: {\n          commercialId: commercial.id,\n          lastReminder: {\n            not: null,\n          },\n          isCompleted: false,\n        },\n        orderBy: {\n          lastReminder: 'desc',\n        },\n      });\n      \n      const twoHoursAgo = new Date();\n      twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);\n      \n      if (!lastReminder || (lastReminder.lastReminder && lastReminder.lastReminder < twoHoursAgo)) {\n        // Create or update a reminder report\n        const reportId = lastReminder ? lastReminder.id : uuidv4();\n        \n        const reminderReport = await prisma.salesreport.upsert({\n          where: {\n            id: reportId,\n          },\n          update: {\n            lastReminder: new Date(),\n          },\n          create: {\n            id: reportId,\n            commercialId: commercial.id,\n            need: '',\n            visitDate: today,\n            denomination: '',\n            name: '',\n            visitPurpose: '',\n            city: '',\n            lastReminder: new Date(),\n            isCompleted: false,\n          },\n        });\n        \n        // Create notifications for admins\n        const admins = await prisma.admin.findMany();\n        \n        for (const admin of admins) {\n          await prisma.notification.create({\n            data: {\n              id: uuidv4(),\n              type: 'REPORT_REMINDER',\n              message: `${commercial.user.firstname} ${commercial.user.lastname} has not submitted their daily report yet.`,\n              adminId: admin.id,\n              salesReportId: reminderReport.id,\n              isRead: false,\n            },\n          });\n        }\n        \n        results.push({\n          commercial,\n          reminderReport,\n          needsReminder: true,\n        });\n      } else {\n        results.push({\n          commercial,\n          needsReminder: false,\n        });\n      }\n    } else {\n      results.push({\n        commercial,\n        report,\n        needsReminder: false,\n      });\n    }\n  }\n  \n  return results;\n}\n\n// Schedule the check to run every 2 hours\nexport function scheduleReportReminders() {\n  // Only run in production to avoid multiple instances in development\n  if (process.env.NODE_ENV === 'production') {\n    // Initial check\n    checkIncompleteReports();\n    \n    // Schedule checks every 2 hours\n    setInterval(checkIncompleteReports, 2 * 60 * 60 * 1000);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IAExB,sBAAsB;IACtB,MAAM,cAAc,MAAM,oHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QACnD,SAAS;YACP,MAAM;QACR;IACF;IAEA,MAAM,UAAU,EAAE;IAElB,KAAK,MAAM,cAAc,YAAa;QACpC,uDAAuD;QACvD,MAAM,SAAS,MAAM,oHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;YAChD,OAAO;gBACL,cAAc,WAAW,EAAE;gBAC3B,aAAa;oBACX,KAAK;gBACP;gBACA,aAAa;YACf;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,6CAA6C;YAC7C,sDAAsD;YACtD,MAAM,eAAe,MAAM,oHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBACtD,OAAO;oBACL,cAAc,WAAW,EAAE;oBAC3B,cAAc;wBACZ,KAAK;oBACP;oBACA,aAAa;gBACf;gBACA,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,MAAM,cAAc,IAAI;YACxB,YAAY,QAAQ,CAAC,YAAY,QAAQ,KAAK;YAE9C,IAAI,CAAC,gBAAiB,aAAa,YAAY,IAAI,aAAa,YAAY,GAAG,aAAc;gBAC3F,qCAAqC;gBACrC,MAAM,WAAW,eAAe,aAAa,EAAE,GAAG,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;gBAEvD,MAAM,iBAAiB,MAAM,oHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;oBACrD,OAAO;wBACL,IAAI;oBACN;oBACA,QAAQ;wBACN,cAAc,IAAI;oBACpB;oBACA,QAAQ;wBACN,IAAI;wBACJ,cAAc,WAAW,EAAE;wBAC3B,MAAM;wBACN,WAAW;wBACX,cAAc;wBACd,MAAM;wBACN,cAAc;wBACd,MAAM;wBACN,cAAc,IAAI;wBAClB,aAAa;oBACf;gBACF;gBAEA,kCAAkC;gBAClC,MAAM,SAAS,MAAM,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;gBAE1C,KAAK,MAAM,SAAS,OAAQ;oBAC1B,MAAM,oHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;wBAC/B,MAAM;4BACJ,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;4BACT,MAAM;4BACN,SAAS,GAAG,WAAW,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,QAAQ,CAAC,0CAA0C,CAAC;4BAC7G,SAAS,MAAM,EAAE;4BACjB,eAAe,eAAe,EAAE;4BAChC,QAAQ;wBACV;oBACF;gBACF;gBAEA,QAAQ,IAAI,CAAC;oBACX;oBACA;oBACA,eAAe;gBACjB;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX;oBACA,eAAe;gBACjB;YACF;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;gBACX;gBACA;gBACA,eAAe;YACjB;QACF;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,oEAAoE;IACpE,uCAA2C;;IAM3C;AACF", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/startupTasks.ts"], "sourcesContent": ["import { scheduleReportReminders } from './reportReminders';\n\n// Run startup tasks\nexport function runStartupTasks() {\n  // Schedule report reminders\n  scheduleReportReminders();\n  \n  console.log('Startup tasks completed');\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,SAAS;IACd,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD;IAEtB,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter, JetBrains_Mono, Poppins } from \"next/font/google\";\nimport \"./globals.css\";\nimport AuthProvider from \"@/context/AuthContext\";\nimport { CartProvider } from \"@/context/CartContext\";\nimport { ThemeProvider } from \"@/context/ThemeContext\";\nimport ToastProvider from \"@/components/ui/ToastProvider\";\nimport { runStartupTasks } from '@/lib/startupTasks';\n\n// Run startup tasks (only on server)\nif (typeof window === 'undefined') {\n  runStartupTasks();\n}\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\", \"800\"],\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  variable: \"--font-jetbrains-mono\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n});\n\nconst poppins = Poppins({\n  variable: \"--font-poppins\",\n  subsets: [\"latin\"],\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\", \"800\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Moonelec - Distribution de Matériel Électrique\",\n  description: \"Moonelec, spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits dans les secteurs résidentiel, tertiaire et industriel.\",\n  manifest: \"/manifest.json\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"fr\">\n      <body\n        className={`${inter.variable} ${jetbrainsMono.variable} ${poppins.variable} antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300`}\n        suppressHydrationWarning\n      >\n        <ThemeProvider>\n          <AuthProvider>\n            <CartProvider>\n              <ToastProvider />\n              {children}\n            </CartProvider>\n          </AuthProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,qCAAqC;AACrC,wCAAmC;IACjC,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;AAChB;AAoBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,sGAAsG,CAAC;YAClL,wBAAwB;sBAExB,cAAA,8OAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,8HAAA,CAAA,UAAY;8BACX,cAAA,8OAAC,8HAAA,CAAA,eAAY;;0CACX,8OAAC,yIAAA,CAAA,UAAa;;;;;4BACb;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/uuid/dist/esm/native.js"], "sourcesContent": ["import { randomUUID } from 'crypto';\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,YAAA,qGAAA,CAAA,aAAU;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/uuid/dist/esm/rng.js"], "sourcesContent": ["import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW;AACjC,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACpB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACjC,CAAA,GAAA,qGAAA,CAAA,iBAAc,AAAD,EAAE;QACf,UAAU;IACd;IACA,OAAO,UAAU,KAAK,CAAC,SAAU,WAAW;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/uuid/dist/esm/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/uuid/dist/esm/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,4IAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/uuid/dist/esm/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAC9B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/uuid/dist/esm/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;IAC5B;IACA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAA,GAAA,0IAAA,CAAA,UAAG,AAAD;IACpD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,GAAG,wBAAwB,CAAC;QAC3F;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}]}