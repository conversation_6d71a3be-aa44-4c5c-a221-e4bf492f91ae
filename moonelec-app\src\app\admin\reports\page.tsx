'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  <PERSON>aSearch,
  FaFilter,
  FaEye,
  FaTrash,
  FaFileExcel,
  FaFileCsv,
  FaFilePdf,
  FaSpinner,
  FaCalendarAlt,
  FaUser,
  FaMapMarkerAlt,
  FaDownload
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import DateRangePicker, { DateFilterMode } from '@/components/ui/DateRangePicker';
import WhatsAppAudioPlayer from '@/components/ui/WhatsAppAudioPlayer';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import Image from 'next/image';

// Types
interface SalesReport {
  id: string;
  commercialId: string;
  need: string;
  articleRef?: string;
  comment?: string;
  visitDate: string;
  denomination: string;
  images?: string;
  imagesArray?: string[];
  name: string;
  visitPurpose: string;
  complaint?: string;
  city: string;
  videoUrl?: string;
  audioUrl?: string;
  pdfUrl?: string;
  submittedAt: string;
  commercial: {
    user: {
      firstname: string;
      lastname: string;
      email: string;
    }
  }
}

// Column definition for the draggable columns
interface Column {
  id: string;
  label: string;
  accessor: (report: SalesReport) => React.ReactNode;
  width?: string;
}

// Draggable column component
const DraggableColumn = ({ column, index, moveColumn }: {
  column: Column;
  index: number;
  moveColumn: (dragIndex: number, hoverIndex: number) => void;
}) => {
  const ref = useRef<HTMLTableCellElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'COLUMN',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'COLUMN',
    hover(item: { index: number }, monitor) {
      if (!ref.current) {
        return;
      }

      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }

      moveColumn(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  return (
    <th
      ref={ref}
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-move ${
        isDragging ? 'opacity-50 bg-gray-100 dark:bg-gray-600' : ''
      }`}
      style={{ width: column.width }}
    >
      {column.label}
    </th>
  );
};

export default function AdminReportsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [reports, setReports] = useState<SalesReport[]>([]);
  const [totalReports, setTotalReports] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [reportsPerPage, setReportsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [dateFilterMode, setDateFilterMode] = useState<DateFilterMode>('range');
  const [selectedCommercial, setSelectedCommercial] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [isExporting, setIsExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState<'excel' | 'csv' | 'pdf'>('excel');

  // State for expanded row
  const [expandedRowId, setExpandedRowId] = useState<string | null>(null);

  // Define columns for the table (in French)
  const [columns, setColumns] = useState<Column[]>([
    {
      id: 'id',
      label: 'ID',
      accessor: (report) => (
        <button
          onClick={() => setExpandedRowId(expandedRowId === report.id ? null : report.id)}
          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 font-mono text-sm underline"
          title="Cliquez pour voir les détails"
        >
          {report.id.substring(0, 8)}...
        </button>
      ),
      width: '8%',
    },
    {
      id: 'need',
      label: 'Besoin',
      accessor: (report) => (
        <div className="max-w-xs">
          <span title={report.need}>
            {report.need.length > 50 ? `${report.need.substring(0, 50)}...` : report.need}
          </span>
        </div>
      ),
      width: '15%',
    },
    {
      id: 'articleRef',
      label: 'Référence Article',
      accessor: (report) => report.articleRef || '-',
      width: '10%',
    },
    {
      id: 'comment',
      label: 'Commentaire',
      accessor: (report) => (
        <div className="max-w-xs">
          <span title={report.comment || ''}>
            {report.comment ? (
              report.comment.length > 30 ? `${report.comment.substring(0, 30)}...` : report.comment
            ) : '-'}
          </span>
        </div>
      ),
      width: '12%',
    },
    {
      id: 'visitDate',
      label: 'Date de Visite',
      accessor: (report) => new Date(report.visitDate).toLocaleDateString('fr-FR'),
      width: '10%',
    },
    {
      id: 'denomination',
      label: 'Dénomination',
      accessor: (report) => (
        <div className="max-w-xs">
          <span title={report.denomination}>
            {report.denomination.length > 20 ? `${report.denomination.substring(0, 20)}...` : report.denomination}
          </span>
        </div>
      ),
      width: '12%',
    },
    {
      id: 'images',
      label: 'Images',
      accessor: (report) => (
        <div className="text-center">
          {report.imagesArray && report.imagesArray.length > 0 ? (
            <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              📷 {report.imagesArray.length}
            </span>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
      width: '8%',
    },
    {
      id: 'name',
      label: 'Nom',
      accessor: (report) => (
        <div className="max-w-xs">
          <span title={report.name}>
            {report.name.length > 15 ? `${report.name.substring(0, 15)}...` : report.name}
          </span>
        </div>
      ),
      width: '10%',
    },
    {
      id: 'visitPurpose',
      label: 'Objet de la Visite',
      accessor: (report) => (
        <div className="max-w-xs">
          <span title={report.visitPurpose}>
            {report.visitPurpose.length > 30 ? `${report.visitPurpose.substring(0, 30)}...` : report.visitPurpose}
          </span>
        </div>
      ),
      width: '12%',
    },
    {
      id: 'complaint',
      label: 'Réclamation',
      accessor: (report) => (
        <div className="max-w-xs">
          <span title={report.complaint || ''}>
            {report.complaint ? (
              report.complaint.length > 30 ? `${report.complaint.substring(0, 30)}...` : report.complaint
            ) : '-'}
          </span>
        </div>
      ),
      width: '12%',
    },
    {
      id: 'city',
      label: 'Ville',
      accessor: (report) => report.city,
      width: '8%',
    },
    {
      id: 'video',
      label: 'Vidéo',
      accessor: (report) => (
        <div className="text-center">
          {report.videoUrl ? (
            <span className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
              🎥
            </span>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
      width: '6%',
    },
    {
      id: 'audio',
      label: 'Audio',
      accessor: (report) => (
        <div className="text-center">
          {report.audioUrl ? (
            <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
              🎵
            </span>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
      width: '6%',
    },
  ]);

  // Function to move columns (for drag and drop)
  const moveColumn = (dragIndex: number, hoverIndex: number) => {
    const draggedColumn = columns[dragIndex];
    const newColumns = [...columns];
    newColumns.splice(dragIndex, 1);
    newColumns.splice(hoverIndex, 0, draggedColumn);
    setColumns(newColumns);
  };

  // Fetch reports
  const fetchReports = async () => {
    setIsLoading(true);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('skip', ((currentPage - 1) * reportsPerPage).toString());
      params.append('take', reportsPerPage.toString());

      // Handle date filtering based on mode
      if (dateFilterMode === 'range') {
        if (startDate) {
          params.append('startDate', startDate.toISOString());
        }

        if (endDate) {
          params.append('endDate', endDate.toISOString());
        }
      } else if (dateFilterMode === 'specific' && selectedDates.length > 0) {
        // For specific dates, we'll send them as individual date parameters
        selectedDates.forEach((date, index) => {
          params.append(`specificDate${index}`, date.toISOString());
        });
        params.append('specificDatesCount', selectedDates.length.toString());
      }

      if (selectedCommercial) {
        params.append('commercialId', selectedCommercial);
      }

      if (city) {
        params.append('city', city);
      }

      const response = await fetch(`/api/sales-reports?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch reports');
      }

      const data = await response.json();
      setReports(data.reports);
      setTotalReports(data.total);
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete report
  const handleDeleteReport = async (id: string) => {
    if (!confirm('Are you sure you want to delete this report?')) {
      return;
    }

    try {
      const response = await fetch(`/api/sales-reports/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete report');
      }

      // Refresh the reports list
      fetchReports();
    } catch (error) {
      console.error('Error deleting report:', error);
    }
  };

  // Handle export
  const handleExport = async (format: 'excel' | 'csv' | 'pdf') => {
    setIsExporting(true);
    setExportFormat(format);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('format', format);

      // Handle date filtering based on mode
      if (dateFilterMode === 'range') {
        if (startDate) {
          params.append('startDate', startDate.toISOString());
        }

        if (endDate) {
          params.append('endDate', endDate.toISOString());
        }
      } else if (dateFilterMode === 'specific' && selectedDates.length > 0) {
        // For specific dates, we'll send them as individual date parameters
        selectedDates.forEach((date, index) => {
          params.append(`specificDate${index}`, date.toISOString());
        });
        params.append('specificDatesCount', selectedDates.length.toString());
      }

      if (selectedCommercial) {
        params.append('commercialId', selectedCommercial);
      }

      if (city) {
        params.append('city', city);
      }

      // Fetch the export
      const response = await fetch(`/api/sales-reports/export?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to export reports');
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sales-reports.${format === 'excel' ? 'xlsx' : format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting reports:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // Apply filters
  const applyFilters = () => {
    setCurrentPage(1);
    fetchReports();
    setIsFilterOpen(false);
  };

  // Reset filters
  const resetFilters = () => {
    setStartDate(null);
    setEndDate(null);
    setSelectedDates([]);
    setDateFilterMode('range');
    setSelectedCommercial('');
    setCity('');
    setCurrentPage(1);
    fetchReports();
    setIsFilterOpen(false);
  };

  // Fetch reports on initial load and when filters change
  useEffect(() => {
    if (user) {
      fetchReports();
    }
  }, [user, currentPage, reportsPerPage]);

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <DndProvider backend={HTML5Backend}>
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
              Rapports de l'Équipe Commerciale
            </h1>
            <div className="flex flex-col sm:flex-row gap-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                <FaFilter />
                <span>Filtrer</span>
              </motion.button>

              <div className="flex gap-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleExport('excel')}
                  disabled={isExporting}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExporting && exportFormat === 'excel' ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <FaFileExcel />
                  )}
                  <span>Excel</span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleExport('csv')}
                  disabled={isExporting}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExporting && exportFormat === 'csv' ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <FaFileCsv />
                  )}
                  <span>CSV</span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleExport('pdf')}
                  disabled={isExporting}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExporting && exportFormat === 'pdf' ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <FaFilePdf />
                  )}
                  <span>PDF</span>
                </motion.button>
              </div>
            </div>
          </div>

          {/* Filter Panel */}
          {isFilterOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6"
            >
              <h2 className="text-lg font-semibold mb-4">Filtrer les Rapports</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                {/* Date Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <FaCalendarAlt className="inline mr-2" />
                    Filtre de Date
                  </label>
                  <DateRangePicker
                    startDate={startDate}
                    endDate={endDate}
                    selectedDates={selectedDates}
                    mode={dateFilterMode}
                    onStartDateChange={setStartDate}
                    onEndDateChange={setEndDate}
                    onSelectedDatesChange={setSelectedDates}
                    onModeChange={setDateFilterMode}
                  />
                </div>

                {/* Commercial */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <FaUser className="inline mr-2" />
                    Commercial
                  </label>
                  <select
                    value={selectedCommercial}
                    onChange={(e) => setSelectedCommercial(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Tous les Commerciaux</option>
                    {/* We would populate this from the API in a real implementation */}
                    <option value="commercial1">Commercial 1</option>
                    <option value="commercial2">Commercial 2</option>
                  </select>
                </div>

                {/* City */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <FaMapMarkerAlt className="inline mr-2" />
                    Ville
                  </label>
                  <input
                    type="text"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    placeholder="Filtrer par ville"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  onClick={resetFilters}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Réinitialiser
                </button>
                <button
                  onClick={applyFilters}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
                >
                  Appliquer les Filtres
                </button>
              </div>
            </motion.div>
          )}

          {/* Reports Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            {isLoading && reports.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <FaSpinner className="animate-spin text-4xl text-primary" />
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      {columns.map((column, index) => (
                        <DraggableColumn
                          key={column.id}
                          column={column}
                          index={index}
                          moveColumn={moveColumn}
                        />
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {reports.length > 0 ? (
                      reports.map((report) => (
                        <>
                          <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            {columns.map((column) => (
                              <td key={column.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                {column.accessor(report)}
                              </td>
                            ))}
                          </tr>

                          {/* Expandable Detail Panel */}
                          {expandedRowId === report.id && (
                            <tr>
                              <td colSpan={columns.length} className="px-6 py-6 bg-gray-50 dark:bg-gray-900">
                                <div className="space-y-6">
                                  {/* Images Section */}
                                  {report.imagesArray && report.imagesArray.length > 0 && (
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                                        Images ({report.imagesArray.length})
                                      </h4>
                                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                        {report.imagesArray.map((imageUrl, index) => (
                                          <div key={index} className="relative group">
                                            <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                                              <Image
                                                src={imageUrl}
                                                alt={`Image ${index + 1}`}
                                                fill
                                                style={{ objectFit: 'cover' }}
                                                className="w-full h-full"
                                              />
                                            </div>
                                            <a
                                              href={imageUrl}
                                              download={`image-${index + 1}.jpg`}
                                              className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                              title="Télécharger l'image"
                                            >
                                              <FaDownload size={12} />
                                            </a>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Video Section */}
                                  {report.videoUrl && (
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                                        Vidéo
                                      </h4>
                                      <div className="bg-black rounded-lg overflow-hidden max-w-2xl">
                                        <video
                                          controls
                                          className="w-full h-auto"
                                          preload="metadata"
                                        >
                                          <source src={report.videoUrl} type="video/mp4" />
                                          <source src={report.videoUrl} type="video/webm" />
                                          <source src={report.videoUrl} type="video/ogg" />
                                          Votre navigateur ne supporte pas la lecture vidéo.
                                        </video>
                                      </div>
                                      <div className="mt-2">
                                        <a
                                          href={report.videoUrl}
                                          download={`video-${report.id}.mp4`}
                                          className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                                        >
                                          <FaDownload className="mr-2" size={14} />
                                          Télécharger la vidéo
                                        </a>
                                      </div>
                                    </div>
                                  )}

                                  {/* Audio Section */}
                                  {report.audioUrl && (
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                                        Audio
                                      </h4>
                                      <WhatsAppAudioPlayer audioUrl={report.audioUrl} />
                                    </div>
                                  )}

                                  {/* PDF Section */}
                                  {report.pdfUrl && (
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                                        Document PDF
                                      </h4>
                                      <div className="flex items-center space-x-4">
                                        <div className="flex items-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                          <div className="w-12 h-12 bg-red-100 dark:bg-red-800 rounded-lg flex items-center justify-center mr-3">
                                            <span className="text-red-600 dark:text-red-400 font-bold text-sm">PDF</span>
                                          </div>
                                          <div>
                                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                                              Document PDF
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                              Cliquez pour télécharger
                                            </p>
                                          </div>
                                        </div>
                                        <a
                                          href={report.pdfUrl}
                                          download={`document-${report.id}.pdf`}
                                          className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                                        >
                                          <FaDownload className="mr-2" size={14} />
                                          Télécharger le PDF
                                        </a>
                                      </div>
                                    </div>
                                  )}

                                  {/* Commercial Info */}
                                  <div>
                                    <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                                      Informations du Commercial
                                    </h4>
                                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                          <p className="text-sm text-gray-500 dark:text-gray-400">Nom complet</p>
                                          <p className="font-medium text-gray-900 dark:text-white">
                                            {report.commercial.user.firstname} {report.commercial.user.lastname}
                                          </p>
                                        </div>
                                        <div>
                                          <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                                          <p className="font-medium text-gray-900 dark:text-white">
                                            {report.commercial.user.email}
                                          </p>
                                        </div>
                                        <div>
                                          <p className="text-sm text-gray-500 dark:text-gray-400">Date de soumission</p>
                                          <p className="font-medium text-gray-900 dark:text-white">
                                            {new Date(report.submittedAt).toLocaleDateString('fr-FR', {
                                              year: 'numeric',
                                              month: 'long',
                                              day: 'numeric',
                                              hour: '2-digit',
                                              minute: '2-digit',
                                            })}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          )}
                        </>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={columns.length} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                          Aucun rapport trouvé
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalReports > 0 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Affichage de {Math.min((currentPage - 1) * reportsPerPage + 1, totalReports)} à {Math.min(currentPage * reportsPerPage, totalReports)} sur {totalReports} rapports
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Précédent
                </button>
                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage * reportsPerPage >= totalReports}
                  className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Suivant
                </button>
              </div>
            </div>
          )}
        </div>
      </DndProvider>
    </RouteGuard>
  );
}
