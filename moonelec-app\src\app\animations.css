/* Animation pour le fade-in */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation pour le fade-out */
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Animation pour le light-beam */
@keyframes lightBeam {
  0% {
    opacity: 0;
    transform: translateX(-50%) scaleY(0);
  }
  20% {
    opacity: 0.8;
    transform: translateX(-50%) scaleY(1);
  }
  80% {
    opacity: 0.8;
    transform: translateX(-50%) scaleY(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) scaleY(1);
  }
}

/* Animation pour le slide-up */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Animation pour le slide-left */
@keyframes slideLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation pour le slide-right */
@keyframes slideRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation pour le pulse */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

/* Animation pour le pulse lent */
@keyframes pulseSlow {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

/* Classes d'animation */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out forwards;
}

.animate-fade-out {
  animation: fadeOut 0.8s ease-in-out forwards;
}

.animate-light-beam {
  animation: lightBeam 3s ease-in-out;
  animation-fill-mode: forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-in-out forwards;
}

.animate-slide-left {
  animation: slideLeft 0.8s ease-in-out forwards;
}

.animate-slide-right {
  animation: slideRight 0.8s ease-in-out forwards;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulseSlow 3s ease-in-out infinite;
}

/* Classes de délai */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-600 {
  animation-delay: 0.6s;
}

.delay-700 {
  animation-delay: 0.7s;
}

.delay-800 {
  animation-delay: 0.8s;
}

.delay-900 {
  animation-delay: 0.9s;
}

.delay-1000 {
  animation-delay: 1s;
}

/* Classes de transition */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-600 {
  transition-duration: 600ms;
}

.duration-800 {
  transition-duration: 800ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

/* Classes de transformation */
.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:scale-110:hover {
  transform: scale(1.1);
}

.active\:scale-90:active {
  transform: scale(0.9);
}

.active\:scale-95:active {
  transform: scale(0.95);
}
