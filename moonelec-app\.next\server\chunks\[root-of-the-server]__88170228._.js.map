{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/middleware/security.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { z } from 'zod';\n\n// Native rate limiting implementation for Next.js\nclass RateLimiter {\n  private requests = new Map<string, { count: number; resetTime: number }>();\n\n  check(ip: string, maxRequests: number = 100, windowMs: number = 15 * 60 * 1000): boolean {\n    const now = Date.now();\n    const record = this.requests.get(ip);\n\n    if (!record || now > record.resetTime) {\n      this.requests.set(ip, { count: 1, resetTime: now + windowMs });\n      return true;\n    }\n\n    if (record.count >= maxRequests) {\n      return false;\n    }\n\n    record.count++;\n    return true;\n  }\n\n  cleanup() {\n    const now = Date.now();\n    for (const [ip, record] of this.requests.entries()) {\n      if (now > record.resetTime) {\n        this.requests.delete(ip);\n      }\n    }\n  }\n}\n\n// Global rate limiter instances\nconst generalRateLimit = new RateLimiter();\nconst authRateLimit = new RateLimiter();\nconst uploadRateLimit = new RateLimiter();\n\n// Rate limiting functions\nexport const checkRateLimit = (ip: string): boolean => {\n  return generalRateLimit.check(ip, 100, 15 * 60 * 1000); // 100 requests per 15 minutes\n};\n\nexport const checkAuthRateLimit = (ip: string): boolean => {\n  return authRateLimit.check(ip, 5, 15 * 60 * 1000); // 5 auth attempts per 15 minutes\n};\n\nexport const checkUploadRateLimit = (ip: string): boolean => {\n  return uploadRateLimit.check(ip, 10, 60 * 1000); // 10 uploads per minute\n};\n\n// Cleanup rate limit records periodically\nsetInterval(() => {\n  generalRateLimit.cleanup();\n  authRateLimit.cleanup();\n  uploadRateLimit.cleanup();\n}, 5 * 60 * 1000); // Cleanup every 5 minutes\n\n// Input validation schemas\nexport const schemas = {\n  email: z.string().email().max(255),\n  password: z.string().min(8).max(128),\n  name: z.string().min(1).max(100).regex(/^[a-zA-ZÀ-ÿ\\s'-]+$/),\n  phone: z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/),\n  text: z.string().max(1000),\n  id: z.string().uuid(),\n  reference: z.string().min(1).max(50).regex(/^[a-zA-Z0-9-_]+$/),\n  url: z.string().url().max(500),\n};\n\n// XSS protection\nexport const sanitizeInput = (input: string): string => {\n  return input\n    .replace(/[<>]/g, '') // Remove < and >\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .replace(/script/gi, '') // Remove script tags\n    .trim();\n};\n\n// SQL injection protection\nexport const sanitizeForDatabase = (input: string): string => {\n  return input\n    .replace(/['\";\\\\]/g, '') // Remove dangerous SQL characters\n    .replace(/--/g, '') // Remove SQL comments\n    .replace(/\\/\\*/g, '') // Remove SQL block comments\n    .replace(/\\*\\//g, '')\n    .trim();\n};\n\n// CSRF token generation and validation\nexport const generateCSRFToken = (): string => {\n  return require('crypto').randomBytes(32).toString('hex');\n};\n\nexport const validateCSRFToken = (token: string, sessionToken: string): boolean => {\n  return token === sessionToken && token.length === 64;\n};\n\n// Security headers middleware\nexport const securityHeaders = (request: NextRequest) => {\n  const response = NextResponse.next();\n  \n  // Security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n  \n  // Content Security Policy\n  const csp = [\n    \"default-src 'self'\",\n    \"script-src 'self' 'unsafe-inline' 'unsafe-eval'\",\n    \"style-src 'self' 'unsafe-inline'\",\n    \"img-src 'self' data: blob:\",\n    \"font-src 'self'\",\n    \"connect-src 'self'\",\n    \"media-src 'self'\",\n    \"object-src 'none'\",\n    \"base-uri 'self'\",\n    \"form-action 'self'\",\n    \"frame-ancestors 'none'\",\n    \"upgrade-insecure-requests\"\n  ].join('; ');\n  \n  response.headers.set('Content-Security-Policy', csp);\n  \n  return response;\n};\n\n// Input validation middleware\nexport const validateInput = (schema: z.ZodSchema, data: any) => {\n  try {\n    return schema.parse(data);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`);\n    }\n    throw error;\n  }\n};\n\n// File upload security validation\nexport const validateFileUpload = (file: File) => {\n  const maxSize = 25 * 1024 * 1024; // 25MB\n  const allowedTypes = [\n    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',\n    'application/pdf', 'application/msword',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    'application/vnd.ms-excel',\n    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    'text/plain', 'text/csv'\n  ];\n  \n  const dangerousExtensions = [\n    'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'jsp'\n  ];\n  \n  // Check file size\n  if (file.size > maxSize) {\n    throw new Error('File too large. Maximum size is 25MB');\n  }\n  \n  // Check file type\n  if (!allowedTypes.includes(file.type)) {\n    throw new Error('File type not allowed');\n  }\n  \n  // Check file extension\n  const extension = file.name.split('.').pop()?.toLowerCase();\n  if (dangerousExtensions.includes(extension || '')) {\n    throw new Error('File extension not allowed for security reasons');\n  }\n  \n  return true;\n};\n\n// Authentication token validation\nexport const validateAuthToken = (token: string): boolean => {\n  if (!token || token.length < 10) {\n    return false;\n  }\n  \n  // Check for suspicious patterns\n  const suspiciousPatterns = [\n    /[<>]/g, // HTML tags\n    /javascript:/gi, // JavaScript protocol\n    /data:/gi, // Data protocol\n    /vbscript:/gi, // VBScript protocol\n  ];\n  \n  return !suspiciousPatterns.some(pattern => pattern.test(token));\n};\n\n// IP address validation and blocking\nconst blockedIPs = new Set<string>();\nconst suspiciousActivity = new Map<string, number>();\n\nexport const checkIPSecurity = (ip: string): boolean => {\n  // Check if IP is blocked\n  if (blockedIPs.has(ip)) {\n    return false;\n  }\n  \n  // Track suspicious activity\n  const attempts = suspiciousActivity.get(ip) || 0;\n  if (attempts > 50) { // Block after 50 suspicious attempts\n    blockedIPs.add(ip);\n    return false;\n  }\n  \n  return true;\n};\n\nexport const reportSuspiciousActivity = (ip: string) => {\n  const current = suspiciousActivity.get(ip) || 0;\n  suspiciousActivity.set(ip, current + 1);\n};\n\n// Password security validation\nexport const validatePasswordStrength = (password: string): boolean => {\n  const minLength = 8;\n  const hasUpperCase = /[A-Z]/.test(password);\n  const hasLowerCase = /[a-z]/.test(password);\n  const hasNumbers = /\\d/.test(password);\n  const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n  \n  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;\n};\n\n// Session security\nexport const generateSecureSessionId = (): string => {\n  return require('crypto').randomBytes(32).toString('hex');\n};\n\nexport const validateSessionSecurity = (sessionData: any): boolean => {\n  // Check session expiration\n  if (sessionData.expiresAt && new Date() > new Date(sessionData.expiresAt)) {\n    return false;\n  }\n  \n  // Check session integrity\n  if (!sessionData.userId || !sessionData.createdAt) {\n    return false;\n  }\n  \n  return true;\n};\n\n// Error handling that doesn't leak sensitive information\nexport const createSecureError = (message: string, statusCode: number = 400) => {\n  // Don't expose internal errors in production\n  const isProduction = process.env.NODE_ENV === 'production';\n  const safeMessage = isProduction ? 'An error occurred' : message;\n  \n  return NextResponse.json(\n    { error: safeMessage },\n    { status: statusCode }\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAAA;;;AAEA,kDAAkD;AAClD,MAAM;IACI,WAAW,IAAI,MAAoD;IAE3E,MAAM,EAAU,EAAE,cAAsB,GAAG,EAAE,WAAmB,KAAK,KAAK,IAAI,EAAW;QACvF,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAEjC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI;gBAAE,OAAO;gBAAG,WAAW,MAAM;YAAS;YAC5D,OAAO;QACT;QAEA,IAAI,OAAO,KAAK,IAAI,aAAa;YAC/B,OAAO;QACT;QAEA,OAAO,KAAK;QACZ,OAAO;IACT;IAEA,UAAU;QACR,MAAM,MAAM,KAAK,GAAG;QACpB,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAI;YAClD,IAAI,MAAM,OAAO,SAAS,EAAE;gBAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvB;QACF;IACF;AACF;AAEA,gCAAgC;AAChC,MAAM,mBAAmB,IAAI;AAC7B,MAAM,gBAAgB,IAAI;AAC1B,MAAM,kBAAkB,IAAI;AAGrB,MAAM,iBAAiB,CAAC;IAC7B,OAAO,iBAAiB,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,OAAO,8BAA8B;AACxF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,cAAc,KAAK,CAAC,IAAI,GAAG,KAAK,KAAK,OAAO,iCAAiC;AACtF;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,gBAAgB,KAAK,CAAC,IAAI,IAAI,KAAK,OAAO,wBAAwB;AAC3E;AAEA,0CAA0C;AAC1C,YAAY;IACV,iBAAiB,OAAO;IACxB,cAAc,OAAO;IACrB,gBAAgB,OAAO;AACzB,GAAG,IAAI,KAAK,OAAO,0BAA0B;AAGtC,MAAM,UAAU;IACrB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC;IAC9B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAChC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC;IACvC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI;IACnB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC;IAC3C,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5B;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MACJ,OAAO,CAAC,SAAS,IAAI,iBAAiB;KACtC,OAAO,CAAC,iBAAiB,IAAI,8BAA8B;KAC3D,OAAO,CAAC,YAAY,IAAI,wBAAwB;KAChD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,IAAI;AACT;AAGO,MAAM,sBAAsB,CAAC;IAClC,OAAO,MACJ,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,OAAO,CAAC,OAAO,IAAI,sBAAsB;KACzC,OAAO,CAAC,SAAS,IAAI,4BAA4B;KACjD,OAAO,CAAC,SAAS,IACjB,IAAI;AACT;AAGO,MAAM,oBAAoB;IAC/B,OAAO,uEAAkB,WAAW,CAAC,IAAI,QAAQ,CAAC;AACpD;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,UAAU,gBAAgB,MAAM,MAAM,KAAK;AACpD;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI;IAElC,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IACzC,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,0BAA0B;IAC1B,MAAM,MAAM;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,SAAS,OAAO,CAAC,GAAG,CAAC,2BAA2B;IAEhD,OAAO;AACT;AAGO,MAAM,gBAAgB,CAAC,QAAqB;IACjD,IAAI;QACF,OAAO,OAAO,KAAK,CAAC;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;QACpF;QACA,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QACnB;QAAc;QAAa;QAAa;QAAa;QACrD;QAAmB;QACnB;QACA;QACA;QACA;QAAc;KACf;IAED,MAAM,sBAAsB;QAC1B;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAM;QAAO;QAAO;QAAO;KAC7E;IAED,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,MAAM,IAAI,MAAM;IAClB;IAEA,kBAAkB;IAClB,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,MAAM,IAAI,MAAM;IAClB;IAEA,uBAAuB;IACvB,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IAC9C,IAAI,oBAAoB,QAAQ,CAAC,aAAa,KAAK;QACjD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,IAAI;QAC/B,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;KACD;IAED,OAAO,CAAC,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAC1D;AAEA,qCAAqC;AACrC,MAAM,aAAa,IAAI;AACvB,MAAM,qBAAqB,IAAI;AAExB,MAAM,kBAAkB,CAAC;IAC9B,yBAAyB;IACzB,IAAI,WAAW,GAAG,CAAC,KAAK;QACtB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,WAAW,mBAAmB,GAAG,CAAC,OAAO;IAC/C,IAAI,WAAW,IAAI;QACjB,WAAW,GAAG,CAAC;QACf,OAAO;IACT;IAEA,OAAO;AACT;AAEO,MAAM,2BAA2B,CAAC;IACvC,MAAM,UAAU,mBAAmB,GAAG,CAAC,OAAO;IAC9C,mBAAmB,GAAG,CAAC,IAAI,UAAU;AACvC;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,YAAY;IAClB,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,aAAa,KAAK,IAAI,CAAC;IAC7B,MAAM,iBAAiB,yBAAyB,IAAI,CAAC;IAErD,OAAO,SAAS,MAAM,IAAI,aAAa,gBAAgB,gBAAgB,cAAc;AACvF;AAGO,MAAM,0BAA0B;IACrC,OAAO,uEAAkB,WAAW,CAAC,IAAI,QAAQ,CAAC;AACpD;AAEO,MAAM,0BAA0B,CAAC;IACtC,2BAA2B;IAC3B,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,YAAY,SAAS,GAAG;QACzE,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,YAAY,SAAS,EAAE;QACjD,OAAO;IACT;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAAC,SAAiB,aAAqB,GAAG;IACzE,6CAA6C;IAC7C,MAAM,eAAe,oDAAyB;IAC9C,MAAM,cAAc,6EAAqC;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO;IAAY,GACrB;QAAE,QAAQ;IAAW;AAEzB", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/auth/mobile/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { prisma } from '@/lib/prisma';\nimport {\n  validateInput,\n  schemas,\n  sanitizeInput,\n  checkIPSecurity,\n  reportSuspiciousActivity,\n  createSecureError,\n  validateAuthToken\n} from '@/middleware/security';\n\n// POST /api/auth/mobile/login\nexport async function POST(request: NextRequest) {\n  try {\n    // Get client IP for security checks\n    const clientIP = request.headers.get('x-forwarded-for') ||\n                    request.headers.get('x-real-ip') ||\n                    'unknown';\n\n    // Check IP security\n    if (!checkIPSecurity(clientIP)) {\n      return createSecureError('Access denied', 403);\n    }\n\n    const body = await request.json();\n\n    // Validate and sanitize input\n    try {\n      validateInput(schemas.email.or(schemas.name), body.username);\n      validateInput(schemas.password, body.password);\n    } catch (validationError: any) {\n      reportSuspiciousActivity(clientIP);\n      return createSecureError('Invalid input format', 400);\n    }\n\n    const username = sanitizeInput(body.username);\n    const password = body.password; // Don't sanitize password as it may contain special chars\n\n    console.log('📱 Mobile login attempt for:', username);\n\n    if (!username || !password) {\n      reportSuspiciousActivity(clientIP);\n      return NextResponse.json(\n        { success: false, error: 'Username and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Find user by username or email\n    const user = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { username: username },\n          { email: username }\n        ]\n      }\n    });\n\n    if (!user) {\n      console.log('❌ User not found:', username);\n      return NextResponse.json(\n        { success: false, error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, user.password);\n    if (!isValidPassword) {\n      console.log('❌ Invalid password for user:', username);\n      return NextResponse.json(\n        { success: false, error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n\n    // Generate JWT token for mobile app using JWT_SECRET\n    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';\n    const token = jwt.sign(\n      {\n        userId: user.id,\n        username: user.username,\n        email: user.email,\n        role: user.role,\n      },\n      jwtSecret,\n      { expiresIn: '7d' } // Token valid for 7 days\n    );\n\n    // Prepare user data (exclude password)\n    const userData = {\n      id: user.id,\n      username: user.username,\n      email: user.email,\n      firstname: user.firstname,\n      lastname: user.lastname,\n      role: user.role,\n      isActive: user.isActive,\n    };\n\n    console.log('✅ Mobile login successful for:', username, 'Role:', user.role);\n\n    return NextResponse.json({\n      success: true,\n      user: userData,\n      token: token,\n      message: 'Login successful'\n    });\n\n  } catch (error) {\n    console.error('❌ Mobile login error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// GET /api/auth/mobile/verify - Verify JWT token\nexport async function GET(request: NextRequest) {\n  try {\n    // Get client IP for security checks\n    const clientIP = request.headers.get('x-forwarded-for') ||\n                    request.headers.get('x-real-ip') ||\n                    'unknown';\n\n    // Check IP security\n    if (!checkIPSecurity(clientIP)) {\n      return createSecureError('Access denied', 403);\n    }\n\n    const authHeader = request.headers.get('authorization');\n\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      reportSuspiciousActivity(clientIP);\n      return NextResponse.json(\n        { success: false, error: 'No token provided' },\n        { status: 401 }\n      );\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n\n    // Validate token format for security\n    if (!validateAuthToken(token)) {\n      reportSuspiciousActivity(clientIP);\n      return createSecureError('Invalid token format', 401);\n    }\n\n    // Verify JWT token using JWT_SECRET\n    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';\n    const decoded = jwt.verify(token, jwtSecret) as any;\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n        isActive: true,\n      }\n    });\n\n    if (!user || !user.isActive) {\n      return NextResponse.json(\n        { success: false, error: 'User not found or inactive' },\n        { status: 401 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      user: user\n    });\n\n  } catch (error) {\n    console.error('❌ Token verification error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Invalid token' },\n      { status: 401 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAWO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,oCAAoC;QACpC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACrB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;QAEhB,oBAAoB;QACpB,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;YAC9B,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB;QAC5C;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,8BAA8B;QAC9B,IAAI;YACF,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,+HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,CAAC,+HAAA,CAAA,UAAO,CAAC,IAAI,GAAG,KAAK,QAAQ;YAC3D,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,+HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,KAAK,QAAQ;QAC/C,EAAE,OAAO,iBAAsB;YAC7B,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE;YACzB,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,wBAAwB;QACnD;QAEA,MAAM,WAAW,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,QAAQ;QAC5C,MAAM,WAAW,KAAK,QAAQ,EAAE,0DAA0D;QAE1F,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqC,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;oBACF;wBAAE,UAAU;oBAAS;oBACrB;wBAAE,OAAO;oBAAS;iBACnB;YACH;QACF;QAEA,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,qBAAqB;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;QACpE,IAAI,CAAC,iBAAiB;YACpB,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC3E,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,QAAQ,KAAK,EAAE;YACf,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB,GACA,WACA;YAAE,WAAW;QAAK,EAAE,yBAAyB;;QAG/C,uCAAuC;QACvC,MAAM,WAAW;YACf,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ;QACzB;QAEA,QAAQ,GAAG,CAAC,kCAAkC,UAAU,SAAS,KAAK,IAAI;QAE1E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,oCAAoC;QACpC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACrB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;QAEhB,oBAAoB;QACpB,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;YAC9B,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB;QAC5C;QAEA,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QAEjE,qCAAqC;QACrC,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;YAC7B,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE;YACzB,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,wBAAwB;QACnD;QAEA,oCAAoC;QACpC,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC3E,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAElC,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA6B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAgB,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}