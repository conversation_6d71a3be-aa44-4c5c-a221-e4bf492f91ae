'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Logo from '@/components/ui/Logo';
import LoginForm from '@/components/auth/LoginForm';
import Card from '@/components/ui/Card';
import { useAuth } from '@/hooks/useAuth';

export default function SignInPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, redirectToDashboard } = useAuth();

  useEffect(() => {
    // Si l'utilisateur est déjà authentifié, le rediriger vers son tableau de bord
    if (!isLoading && isAuthenticated) {
      redirectToDashboard();
    }
  }, [isLoading, isAuthenticated, redirectToDashboard]);

  // Ne pas afficher la page si l'utilisateur est déjà authentifié
  if (isLoading || isAuthenticated) {
    return null;
  }
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side - Form */}
      <div className="w-full md:w-1/2 flex flex-col justify-center items-center p-8 md:p-16">
        <div className="w-full max-w-md">
          <div className="mb-8 flex justify-center md:justify-start">
            <Logo width={180} height={60} />
          </div>

          <LoginForm />
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="hidden md:block md:w-1/2 relative bg-primary">
        <div className="absolute inset-0 bg-gradient-to-br from-primary to-primary/70"></div>
        <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-16">
          <h2 className="text-3xl font-bold mb-6 text-center">
            Bienvenue chez Moonelec
          </h2>
          <p className="text-lg text-center mb-8">
            Accédez à votre compte pour découvrir notre gamme complète de produits électriques et bénéficier de nos services exclusifs.
          </p>
          <div className="grid grid-cols-2 gap-6 w-full max-w-md">
            {[
              { title: "+300 000", description: "Références produits" },
              { title: "Depuis 1990", description: "À votre service" },
              { title: "Livraison rapide", description: "Partout en France" },
              { title: "Support expert", description: "À votre écoute" }
            ].map((item, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-sm p-4 border-none">
                <h3 className="font-semibold mb-2">{item.title}</h3>
                <p className="text-sm">{item.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
