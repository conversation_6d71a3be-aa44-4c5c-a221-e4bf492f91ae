import { jsPDF } from 'jspdf';
import { toast } from 'react-hot-toast';
import { imageToBase64 } from '@/utils/imageUtils';

// Types
interface QuoteItem {
  id: string;
  productReference: string;
  productName: string;
  quantity: number;
  unitPrice: number;
}

interface Quote {
  id: string;
  quoteNumber: string;
  createdAt: string;
  validUntil: string;
  clientName: string;
  clientEmail: string;
  companyName: string;
  items: QuoteItem[];
  totalAmount: number;
  notes?: string;
  status: string;
}

/**
 * Génère un PDF pour un devis Moonelec avec le logo
 * Cette fonction utilise l'API Canvas pour convertir le logo en base64 et l'inclure dans le PDF
 * @param quote Devis à générer en PDF
 */
export const generateMoonelecPdfWithLogo = async (quote: Quote | null) => {
  try {
    toast.loading('Génération du PDF en cours...');
    
    // Créer un nouveau document PDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    // Définir les marges et dimensions
    const pageWidth = doc.internal.pageSize.getWidth();
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);
    let y = margin;
    
    try {
      // Convertir le logo en base64
      const logoBase64 = await imageToBase64('/images/logo/logo-moonelec.png');
      
      // Ajouter le logo au PDF
      doc.addImage(logoBase64, 'PNG', margin, y - 10, 40, 15);
      
      // Décaler le y pour les informations suivantes
      y += 10;
    } catch (logoError) {
      console.error('Erreur lors du chargement du logo:', logoError);
      
      // Fallback: Ajouter le titre MOONELEC avec couleurs
      doc.setFont("helvetica", "bold");
      doc.setFontSize(22);
      doc.setTextColor(255, 0, 0); // Rouge pour "MOON"
      doc.text("MOON", margin, y);
      doc.setTextColor(0, 102, 204); // Bleu pour "ELEC"
      doc.text("ELEC", margin + 30, y);
    }
    
    // Ajouter les informations de l'entreprise
    doc.setFont("helvetica", "normal");
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    y += 10;
    doc.text("Derb El Youssoufía, Rue 78, N°89,", margin, y);
    y += 5;
    doc.text("Bd El Fida - Casablanca-Maroc", margin, y);
    y += 5;
    doc.text("Tél: +212 522 80 80 80", margin, y);
    y += 5;
    doc.text("Email: <EMAIL>", margin, y);
    
    // Ajouter les informations du devis
    doc.setFont("helvetica", "bold");
    doc.setFontSize(18);
    doc.setTextColor(0, 102, 204);
    doc.text("DEVIS", pageWidth - margin, 30, { align: "right" });
    
    doc.setFont("helvetica", "normal");
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text(`N° ${quote?.quoteNumber || ''}`, pageWidth - margin, 40, { align: "right" });
    doc.text(`Date: ${quote?.createdAt || ''}`, pageWidth - margin, 45, { align: "right" });
    doc.text(`Valide jusqu'au: ${quote?.validUntil || ''}`, pageWidth - margin, 50, { align: "right" });
    
    // Ajouter les informations du client
    y = 70;
    doc.setFont("helvetica", "bold");
    doc.setFontSize(12);
    doc.text("Client", margin, y);
    
    doc.setFont("helvetica", "normal");
    doc.setFontSize(10);
    y += 8;
    doc.text(`${quote?.companyName || ''}`, margin, y);
    y += 5;
    doc.text(`À l'attention de: ${quote?.clientName || ''}`, margin, y);
    y += 5;
    doc.text(`Email: ${quote?.clientEmail || ''}`, margin, y);
    
    // Ajouter le titre des détails du devis
    y += 15;
    doc.setFont("helvetica", "bold");
    doc.setFontSize(12);
    doc.text("Détails du devis", margin, y);
    
    // Ajouter l'en-tête du tableau
    y += 8;
    const tableTop = y;
    const colWidth = [25, 70, 15, 30, 30]; // Largeurs des colonnes
    
    // Dessiner l'en-tête du tableau
    doc.setFillColor(245, 245, 245);
    doc.rect(margin, y, contentWidth, 8, 'F');
    
    doc.setFont("helvetica", "bold");
    doc.setFontSize(9);
    doc.text("Référence", margin + 3, y + 5);
    doc.text("Description", margin + colWidth[0] + 3, y + 5);
    doc.text("Qté", margin + colWidth[0] + colWidth[1] + 3, y + 5);
    doc.text("Prix unitaire", margin + colWidth[0] + colWidth[1] + colWidth[2] + 3, y + 5);
    doc.text("Total", margin + colWidth[0] + colWidth[1] + colWidth[2] + colWidth[3] + 3, y + 5);
    
    // Dessiner les lignes du tableau
    y += 8;
    let totalAmount = 0;
    
    // Utiliser les données du devis ou des données d'exemple
    const items = quote?.items && quote.items.length > 0
      ? quote.items
      : [
          { id: '1', productReference: 'LED-A60-10W', productName: 'Ampoule LED A60 10W E27 Blanc Chaud 2700K', quantity: 50, unitPrice: 35 },
          { id: '2', productReference: 'SPOT-GU10-5W', productName: 'Spot LED GU10 5W Blanc Neutre 4000K', quantity: 30, unitPrice: 45 },
          { id: '3', productReference: 'CABLE-3G1.5', productName: 'Câble électrique 3G1.5 mm² - 100m', quantity: 5, unitPrice: 850 }
        ];
    
    items.forEach((item, index) => {
      // Alterner les couleurs de fond
      if (index % 2 === 1) {
        doc.setFillColor(252, 252, 252);
        doc.rect(margin, y, contentWidth, 8, 'F');
      }
      
      // Ajouter les données
      doc.setFont("helvetica", "normal");
      doc.text(item.productReference || '', margin + 3, y + 5);
      
      // Limiter la longueur du nom du produit
      const productName = item.productName || '';
      const truncatedName = productName.length > 40 
        ? productName.substring(0, 37) + '...' 
        : productName;
      doc.text(truncatedName, margin + colWidth[0] + 3, y + 5);
      
      doc.text(item.quantity.toString(), margin + colWidth[0] + colWidth[1] + 3, y + 5);
      
      // Formater le prix unitaire
      const unitPrice = item.unitPrice.toFixed(2).replace('.', ',');
      doc.text(unitPrice + " MAD", margin + colWidth[0] + colWidth[1] + colWidth[2] + 3, y + 5);
      
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
      totalAmount += itemTotal;
      
      // Formater le total
      const totalFormatted = itemTotal.toFixed(2).replace('.', ',');
      doc.text(totalFormatted + " MAD", margin + contentWidth - 3, y + 5, { align: 'right' });
      
      y += 8;
    });
    
    // Dessiner les bordures du tableau
    doc.setDrawColor(200, 200, 200);
    doc.line(margin, tableTop, margin, y); // Ligne gauche
    doc.line(margin + contentWidth, tableTop, margin + contentWidth, y); // Ligne droite
    doc.line(margin, tableTop, margin + contentWidth, tableTop); // Ligne haut
    doc.line(margin, y, margin + contentWidth, y); // Ligne bas
    
    // Lignes verticales intérieures
    doc.line(margin + colWidth[0], tableTop, margin + colWidth[0], y);
    doc.line(margin + colWidth[0] + colWidth[1], tableTop, margin + colWidth[0] + colWidth[1], y);
    doc.line(margin + colWidth[0] + colWidth[1] + colWidth[2], tableTop, margin + colWidth[0] + colWidth[1] + colWidth[2], y);
    doc.line(margin + colWidth[0] + colWidth[1] + colWidth[2] + colWidth[3], tableTop, margin + colWidth[0] + colWidth[1] + colWidth[2] + colWidth[3], y);
    
    // Ligne horizontale après l'en-tête
    doc.line(margin, tableTop + 8, margin + contentWidth, tableTop + 8);
    
    // Ajouter le total
    y += 2;
    doc.setFont("helvetica", "bold");
    doc.setFontSize(10);
    doc.text("Total:", margin + colWidth[0] + colWidth[1] + colWidth[2] + colWidth[3] - 3, y + 5, { align: 'right' });
    
    const finalTotal = (quote?.totalAmount || totalAmount).toFixed(2).replace('.', ',');
    doc.text(finalTotal + " MAD", margin + contentWidth - 3, y + 5, { align: 'right' });
    
    // Ajouter les notes si elles existent
    if (quote?.notes) {
      y += 15;
      doc.setFont("helvetica", "bold");
      doc.setFontSize(12);
      doc.text("Notes", margin, y);
      
      y += 8;
      doc.setFont("helvetica", "normal");
      doc.setFontSize(9);
      doc.setFillColor(249, 249, 249);
      doc.rect(margin, y, contentWidth, 20, 'F');
      
      // Ajouter le texte des notes avec retour à la ligne automatique
      const splitNotes = doc.splitTextToSize(quote.notes, contentWidth - 10);
      doc.text(splitNotes, margin + 5, y + 5);
      
      y += 25;
    }
    
    // Ajouter les conditions générales
    y += 10;
    doc.setFont("helvetica", "bold");
    doc.setFontSize(12);
    doc.text("Conditions générales", margin, y);
    
    y += 8;
    doc.setFont("helvetica", "normal");
    doc.setFontSize(9);
    const conditions = [
      "• Ce devis est valable jusqu'à la date indiquée ci-dessus.",
      "• Les prix sont indiqués en Dirhams Marocains (MAD) et sont hors taxes.",
      "• Les délais de livraison sont donnés à titre indicatif.",
      "• Le paiement doit être effectué selon les modalités convenues.",
      "• Toute commande implique l'acceptation de nos conditions générales de vente."
    ];
    
    conditions.forEach(condition => {
      doc.text(condition, margin + 5, y);
      y += 5;
    });
    
    // Ajouter les signatures
    y += 20;
    doc.setFont("helvetica", "bold");
    doc.text("Pour Moonelec", margin + 30, y, { align: 'center' });
    doc.text("Pour le client", margin + contentWidth - 30, y, { align: 'center' });
    
    y += 20;
    doc.setDrawColor(0, 0, 0);
    doc.line(margin, y, margin + 60, y);
    doc.line(margin + contentWidth - 60, y, margin + contentWidth, y);
    
    y += 5;
    doc.setFont("helvetica", "normal");
    doc.setFontSize(8);
    doc.text("Signature et cachet", margin + 30, y, { align: 'center' });
    doc.text("Signature et cachet", margin + contentWidth - 30, y, { align: 'center' });
    
    // Sauvegarder le PDF
    doc.save(`devis_${quote?.quoteNumber || 'moonelec'}.pdf`);
    
    // Afficher un message de succès
    toast.dismiss();
    toast.success('PDF généré avec succès!');
  } catch (error) {
    console.error('Erreur lors de la génération du PDF:', error);
    toast.dismiss();
    toast.error('Erreur lors de la génération du PDF. Veuillez réessayer.');
  }
};
