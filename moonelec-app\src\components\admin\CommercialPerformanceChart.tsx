'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaUserTie } from 'react-icons/fa';

interface CommercialData {
  id: string;
  name: string;
  sales: number;
  clients: number;
  color: string;
}

export default function CommercialPerformanceChart() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Données simulées pour le graphique
  const commercials: CommercialData[] = [
    { id: '1', name: '<PERSON>', sales: 450000, clients: 24, color: '#4F46E5' },
    { id: '2', name: '<PERSON><PERSON>', sales: 380000, clients: 18, color: '#10B981' },
    { id: '3', name: '<PERSON><PERSON>', sales: 320000, clients: 15, color: '#F59E0B' },
    { id: '4', name: '<PERSON>', sales: 290000, clients: 12, color: '#EF4444' },
    { id: '5', name: '<PERSON>', sales: 250000, clients: 10, color: '#8B5CF6' },
  ];
  
  // Trouver la valeur maximale pour l'échelle
  const maxSales = Math.max(...commercials.map(c => c.sales));
  
  // Calculer la largeur des barres en pourcentage
  const getBarWidth = (value: number) => {
    return (value / maxSales) * 100;
  };
  
  if (!mounted) {
    return <div className="h-80 bg-gray-100 dark:bg-gray-700 animate-pulse rounded-lg"></div>;
  }
  
  return (
    <div className="h-80 overflow-y-auto">
      <div className="space-y-6">
        {commercials.map((commercial, index) => (
          <div key={commercial.id} className="relative">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mr-3">
                <FaUserTie style={{ color: commercial.color }} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-800 dark:text-white">{commercial.name}</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">{commercial.clients} clients</p>
              </div>
              <div className="ml-auto text-right">
                <p className="text-sm font-medium text-gray-800 dark:text-white">
                  {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(commercial.sales)}
                </p>
              </div>
            </div>
            
            <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${getBarWidth(commercial.sales)}%` }}
                transition={{ duration: 1, delay: index * 0.1 }}
                className="h-full rounded-full"
                style={{ backgroundColor: commercial.color }}
              ></motion.div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
