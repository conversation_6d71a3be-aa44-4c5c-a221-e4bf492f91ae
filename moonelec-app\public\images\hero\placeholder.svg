<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1A1A1A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E10600;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0072CE;stop-opacity:0.6" />
    </linearGradient>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="url(#heroGradient)"/>
  
  <!-- Grid Pattern -->
  <rect width="1920" height="1080" fill="url(#grid)"/>
  
  <!-- Electrical Elements -->
  <!-- Light Bulb -->
  <g transform="translate(300, 200)">
    <circle cx="0" cy="0" r="30" fill="rgba(255, 255, 255, 0.1)" stroke="rgba(255, 255, 255, 0.3)" stroke-width="2"/>
    <path d="M-15,-10 L15,-10 M-10,-5 L10,-5 M-5,0 L5,0 M-10,5 L10,5 M-15,10 L15,10" stroke="rgba(255, 255, 255, 0.5)" stroke-width="1"/>
  </g>
  
  <!-- Plug -->
  <g transform="translate(1500, 300)">
    <rect x="-20" y="-15" width="40" height="30" rx="5" fill="rgba(255, 255, 255, 0.1)" stroke="rgba(255, 255, 255, 0.3)" stroke-width="2"/>
    <circle cx="-8" cy="-5" r="3" fill="rgba(255, 255, 255, 0.5)"/>
    <circle cx="8" cy="-5" r="3" fill="rgba(255, 255, 255, 0.5)"/>
  </g>
  
  <!-- Cable -->
  <path d="M200,800 Q400,700 600,750 T1000,800 Q1200,850 1400,800" stroke="rgba(255, 255, 255, 0.2)" stroke-width="8" fill="none"/>
  
  <!-- Switch -->
  <g transform="translate(1200, 600)">
    <rect x="-25" y="-20" width="50" height="40" rx="8" fill="rgba(255, 255, 255, 0.1)" stroke="rgba(255, 255, 255, 0.3)" stroke-width="2"/>
    <rect x="-15" y="-10" width="30" height="8" rx="4" fill="rgba(255, 255, 255, 0.4)"/>
  </g>
  
  <!-- Circuit Lines -->
  <g stroke="rgba(255, 255, 255, 0.15)" stroke-width="2" fill="none">
    <path d="M100,400 L300,400 L300,600 L500,600"/>
    <path d="M500,600 L700,600 L700,400 L900,400"/>
    <path d="M900,400 L1100,400 L1100,200 L1300,200"/>
    <circle cx="300" cy="400" r="4" fill="rgba(255, 255, 255, 0.3)"/>
    <circle cx="700" cy="600" r="4" fill="rgba(255, 255, 255, 0.3)"/>
    <circle cx="1100" cy="400" r="4" fill="rgba(255, 255, 255, 0.3)"/>
  </g>
  
  <!-- Floating Particles -->
  <g fill="rgba(255, 255, 255, 0.3)">
    <circle cx="150" cy="150" r="2"/>
    <circle cx="800" cy="100" r="1.5"/>
    <circle cx="1600" cy="200" r="2.5"/>
    <circle cx="400" cy="900" r="1"/>
    <circle cx="1200" cy="950" r="2"/>
    <circle cx="1700" cy="800" r="1.5"/>
  </g>
  
  <!-- Brand Elements -->
  <g transform="translate(960, 540)">
    <!-- Moonelec Logo Placeholder -->
    <circle cx="0" cy="0" r="60" fill="rgba(225, 6, 0, 0.2)" stroke="rgba(225, 6, 0, 0.5)" stroke-width="3"/>
    <text x="0" y="8" text-anchor="middle" fill="rgba(255, 255, 255, 0.8)" font-family="Arial, sans-serif" font-size="36" font-weight="bold">M</text>
  </g>
</svg>
