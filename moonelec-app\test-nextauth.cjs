const fetch = require('node-fetch');

async function testNextAuth() {
  try {
    console.log('🔍 Testing NextAuth endpoints...');

    // Test 1: Get CSRF token
    console.log('\n1. Getting CSRF token...');
    const csrfResponse = await fetch('http://localhost:3000/api/auth/csrf');
    const csrfData = await csrfResponse.json();
    console.log('✅ CSRF response:', csrfData);

    // Test 2: Get providers
    console.log('\n2. Getting providers...');
    const providersResponse = await fetch('http://localhost:3000/api/auth/providers');
    const providersData = await providersResponse.json();
    console.log('✅ Providers response:', providersData);

    // Test 3: Try login with correct credentials
    console.log('\n3. Testing login...');
    const loginData = new URLSearchParams({
      username: 'admin',
      password: 'admin123',
      csrfToken: csrfData.csrfToken,
      callbackUrl: 'http://localhost:3000',
      json: 'true'
    });

    const loginResponse = await fetch('http://localhost:3000/api/auth/callback/credentials', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': `next-auth.csrf-token=${csrfData.csrfToken}`
      },
      body: loginData.toString(),
      redirect: 'manual'
    });

    console.log('Login response status:', loginResponse.status);
    console.log('Login response headers:', Object.fromEntries(loginResponse.headers.entries()));
    
    if (loginResponse.status === 200) {
      const loginResult = await loginResponse.json();
      console.log('✅ Login successful:', loginResult);
    } else {
      const errorText = await loginResponse.text();
      console.log('❌ Login failed:', errorText);
    }

  } catch (error) {
    console.error('❌ Error testing NextAuth:', error);
  }
}

testNextAuth();
