import { NextRequest, NextResponse } from 'next/server';
import { findUserByUsername, verifyPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Test login endpoint called');
    
    const body = await request.json();
    const { username, password } = body;

    console.log('🔍 Login attempt:', { username, hasPassword: !!password });

    if (!username || !password) {
      console.log('❌ Missing credentials');
      return NextResponse.json({ error: 'Missing credentials' }, { status: 400 });
    }

    // Test the same logic as in auth-options
    console.log('🔍 Looking for user:', username);
    const user = await findUserByUsername(username);

    if (!user) {
      console.log('❌ User not found:', username);
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    console.log('✅ User found:', { id: user.id, username: user.username, role: user.role });

    console.log('🔍 Verifying password...');
    const isPasswordValid = await verifyPassword(password, user.password);

    if (!isPasswordValid) {
      console.log('❌ Invalid password for user:', username);
      return NextResponse.json({ error: 'Invalid password' }, { status: 401 });
    }

    console.log('✅ Password valid for user:', username);

    const userObject = {
      id: user.id,
      email: user.email,
      username: user.username,
      name: `${user.firstname} ${user.lastname}`,
      firstname: user.firstname,
      lastname: user.lastname,
      role: user.role,
      clientId: user.client?.id,
      commercialId: user.commercial?.id,
      adminId: user.admin?.id,
    };

    console.log('✅ Login successful:', userObject);

    return NextResponse.json({
      success: true,
      user: userObject,
      message: 'Login successful'
    });

  } catch (error) {
    console.error('❌ Test login error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
