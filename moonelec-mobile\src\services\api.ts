import axios, { AxiosInstance } from 'axios';
import * as SecureStore from 'expo-secure-store';
import {
  User,
  Product,
  Category,
  Brand,
  Quote,
  SalesReport,
  ProductsResponse,
  QuotesResponse,
  SalesReportsResponse,
  SalesReportInput,
  LoginForm
} from '../types';

// Base URL for the API - Update this to match your web app's API
// For development, use your local IP address instead of localhost
const BASE_URL = 'http://localhost:3000/api'; // Change this to your actual API URL

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include authentication
    this.api.interceptors.request.use(
      async (config) => {
        // Try to get JWT token first (for mobile endpoint)
        const authToken = await SecureStore.getItemAsync('authToken');
        if (authToken) {
          config.headers.Authorization = `Bearer ${authToken}`;
        } else {
          // Fallback to session cookies (for NextAuth)
          const sessionCookies = await SecureStore.getItemAsync('sessionCookies');
          if (sessionCookies) {
            config.headers.Cookie = sessionCookies;
          }
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Session expired, clear it
          await SecureStore.deleteItemAsync('sessionToken');
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth Methods - Direct authentication for mobile
  async login(credentials: LoginForm): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      console.log('🔐 Attempting login with:', { username: credentials.username });

      // For mobile, we'll use a direct authentication endpoint
      // This should be a custom endpoint in your web app that returns a JWT or session token
      const response = await this.api.post('/auth/mobile/login', {
        username: credentials.username,
        password: credentials.password,
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      console.log('📱 Login response:', response.status, response.data);

      if (response.data.success && response.data.user) {
        // Store the session token or JWT
        if (response.data.token) {
          await SecureStore.setItemAsync('authToken', response.data.token);
        }
        await SecureStore.setItemAsync('user', JSON.stringify(response.data.user));
        return { success: true, user: response.data.user };
      }

      return { success: false, error: response.data.error || 'Login failed' };
    } catch (error: any) {
      console.error('❌ Login error:', error.response?.data || error.message);

      // If the mobile endpoint doesn't exist, fallback to NextAuth
      if (error.response?.status === 404) {
        return this.fallbackNextAuthLogin(credentials);
      }

      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Network error'
      };
    }
  }

  // Fallback to NextAuth for backward compatibility
  private async fallbackNextAuthLogin(credentials: LoginForm): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      console.log('🔄 Fallback to NextAuth login');

      // Create a cookie jar to handle session cookies
      const cookieJar: string[] = [];

      // First, get CSRF token
      const csrfResponse = await this.api.get('/auth/csrf');
      const csrfToken = csrfResponse.data.csrfToken;

      // Extract cookies from CSRF response
      const setCookieHeader = csrfResponse.headers['set-cookie'];
      if (setCookieHeader) {
        setCookieHeader.forEach((cookie: string) => {
          cookieJar.push(cookie.split(';')[0]);
        });
      }

      // Then authenticate with cookies
      const authResponse = await this.api.post('/auth/callback/credentials', {
        username: credentials.username,
        password: credentials.password,
        csrfToken,
        callbackUrl: '/',
        json: true
      }, {
        headers: {
          'Cookie': cookieJar.join('; '),
          'Content-Type': 'application/x-www-form-urlencoded',
        }
      });

      // Extract session cookies
      const authSetCookieHeader = authResponse.headers['set-cookie'];
      if (authSetCookieHeader) {
        authSetCookieHeader.forEach((cookie: string) => {
          const cookiePair = cookie.split(';')[0];
          if (cookiePair.includes('next-auth.session-token')) {
            cookieJar.push(cookiePair);
          }
        });
      }

      // Get session with cookies
      const sessionResponse = await this.api.get('/auth/session', {
        headers: {
          'Cookie': cookieJar.join('; '),
        }
      });

      if (sessionResponse.data.user) {
        // Store cookies for future requests
        await SecureStore.setItemAsync('sessionCookies', cookieJar.join('; '));
        await SecureStore.setItemAsync('user', JSON.stringify(sessionResponse.data.user));
        return { success: true, user: sessionResponse.data.user };
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error: any) {
      console.error('❌ NextAuth fallback error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error || 'Authentication failed'
      };
    }
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/signout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear all stored authentication data
      await SecureStore.deleteItemAsync('authToken');
      await SecureStore.deleteItemAsync('sessionCookies');
      await SecureStore.deleteItemAsync('user');
    }
  }

  async getSession(): Promise<{ user?: User }> {
    try {
      // First try to get user from local storage
      const storedUser = await SecureStore.getItemAsync('user');
      if (storedUser) {
        return { user: JSON.parse(storedUser) };
      }

      // If no stored user, try to get session from server
      const response = await this.api.get('/auth/session');
      if (response.data.user) {
        await SecureStore.setItemAsync('user', JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error) {
      console.error('Session error:', error);
      return {};
    }
  }

  // Product Methods - Using existing API endpoints
  async getProducts(params?: {
    categoryId?: string;
    brandId?: string;
    search?: string;
    skip?: number;
    take?: number;
  }): Promise<ProductsResponse> {
    try {
      const response = await this.api.get('/products', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch products');
    }
  }

  async getProduct(id: string): Promise<Product> {
    try {
      const response = await this.api.get(`/products/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch product');
    }
  }

  // Category Methods
  async getCategories(): Promise<Category[]> {
    try {
      const response = await this.api.get('/categories');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch categories');
    }
  }

  // Brand Methods
  async getBrands(): Promise<Brand[]> {
    try {
      const response = await this.api.get('/brands');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch brands');
    }
  }

  // Quote Methods - Using existing API endpoints
  async getQuotes(params?: {
    search?: string;
    status?: string;
    page?: number;
    limit?: number;
    clientId?: string;
  }): Promise<QuotesResponse> {
    try {
      const response = await this.api.get('/quotes', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch quotes');
    }
  }

  async getQuote(id: string): Promise<Quote> {
    try {
      const response = await this.api.get(`/quotes/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch quote');
    }
  }

  async createQuote(quoteData: {
    items: { productId: string; quantity: number }[];
    notes?: string;
    clientId?: string;
  }): Promise<{ quote: Quote }> {
    try {
      const response = await this.api.post('/quotes', quoteData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create quote');
    }
  }

  // Sales Report Methods (for commercials) - Using existing API endpoints
  async getSalesReports(params?: {
    skip?: number;
    take?: number;
    startDate?: string;
    endDate?: string;
    commercialId?: string;
    city?: string;
  }): Promise<SalesReportsResponse> {
    try {
      const response = await this.api.get('/sales-reports', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch sales reports');
    }
  }

  async getSalesReport(id: string): Promise<SalesReport> {
    try {
      const response = await this.api.get(`/sales-reports/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to fetch sales report');
    }
  }

  async createSalesReport(reportData: SalesReportInput): Promise<{ success: boolean; report: SalesReport }> {
    try {
      const formData = new FormData();

      // Add form fields
      formData.append('need', reportData.need);
      formData.append('visitDate', reportData.visitDate.toISOString());
      formData.append('denomination', reportData.denomination);
      formData.append('name', reportData.name);
      formData.append('visitPurpose', reportData.visitPurpose);
      formData.append('city', reportData.city);

      if (reportData.articleRef) formData.append('articleRef', reportData.articleRef);
      if (reportData.comment) formData.append('comment', reportData.comment);
      if (reportData.complaint) formData.append('complaint', reportData.complaint);

      // Add image URLs
      reportData.images.forEach((url) => {
        formData.append('imageUrls', url);
      });

      if (reportData.videoUrl) formData.append('videoUrl', reportData.videoUrl);
      if (reportData.audioUrl) formData.append('audioUrl', reportData.audioUrl);
      if (reportData.pdfUrl) formData.append('pdfUrl', reportData.pdfUrl);

      const response = await this.api.post('/sales-reports', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to create sales report');
    }
  }

  async uploadFile(file: any, type: 'image' | 'video' | 'audio' | 'pdf'): Promise<{ url: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await this.api.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to upload file');
    }
  }
}

export default new ApiService();
