"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/restructure";
exports.ids = ["vendor-chunks/restructure"];
exports.modules = {

/***/ "(rsc)/./node_modules/restructure/index.js":
/*!*******************************************!*\
  !*** ./node_modules/restructure/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Array: () => (/* reexport safe */ _src_Array_js__WEBPACK_IMPORTED_MODULE_2__.Array),\n/* harmony export */   Bitfield: () => (/* reexport safe */ _src_Bitfield_js__WEBPACK_IMPORTED_MODULE_4__.Bitfield),\n/* harmony export */   Boolean: () => (/* reexport safe */ _src_Boolean_js__WEBPACK_IMPORTED_MODULE_5__.Boolean),\n/* harmony export */   Buffer: () => (/* reexport safe */ _src_Buffer_js__WEBPACK_IMPORTED_MODULE_6__.Buffer),\n/* harmony export */   DecodeStream: () => (/* reexport safe */ _src_DecodeStream_js__WEBPACK_IMPORTED_MODULE_1__.DecodeStream),\n/* harmony export */   EncodeStream: () => (/* reexport safe */ _src_EncodeStream_js__WEBPACK_IMPORTED_MODULE_0__.EncodeStream),\n/* harmony export */   Enum: () => (/* reexport safe */ _src_Enum_js__WEBPACK_IMPORTED_MODULE_7__.Enum),\n/* harmony export */   Fixed: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.Fixed),\n/* harmony export */   LazyArray: () => (/* reexport safe */ _src_LazyArray_js__WEBPACK_IMPORTED_MODULE_3__.LazyArray),\n/* harmony export */   Number: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.Number),\n/* harmony export */   Optional: () => (/* reexport safe */ _src_Optional_js__WEBPACK_IMPORTED_MODULE_8__.Optional),\n/* harmony export */   Pointer: () => (/* reexport safe */ _src_Pointer_js__WEBPACK_IMPORTED_MODULE_15__.Pointer),\n/* harmony export */   PropertyDescriptor: () => (/* reexport safe */ _src_utils_js__WEBPACK_IMPORTED_MODULE_13__.PropertyDescriptor),\n/* harmony export */   Reserved: () => (/* reexport safe */ _src_Reserved_js__WEBPACK_IMPORTED_MODULE_9__.Reserved),\n/* harmony export */   String: () => (/* reexport safe */ _src_String_js__WEBPACK_IMPORTED_MODULE_10__.String),\n/* harmony export */   Struct: () => (/* reexport safe */ _src_Struct_js__WEBPACK_IMPORTED_MODULE_11__.Struct),\n/* harmony export */   VersionedStruct: () => (/* reexport safe */ _src_VersionedStruct_js__WEBPACK_IMPORTED_MODULE_12__.VersionedStruct),\n/* harmony export */   VoidPointer: () => (/* reexport safe */ _src_Pointer_js__WEBPACK_IMPORTED_MODULE_15__.VoidPointer),\n/* harmony export */   double: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.double),\n/* harmony export */   doublebe: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.doublebe),\n/* harmony export */   doublele: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.doublele),\n/* harmony export */   fixed16: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.fixed16),\n/* harmony export */   fixed16be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.fixed16be),\n/* harmony export */   fixed16le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.fixed16le),\n/* harmony export */   fixed32: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.fixed32),\n/* harmony export */   fixed32be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.fixed32be),\n/* harmony export */   fixed32le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.fixed32le),\n/* harmony export */   float: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.float),\n/* harmony export */   floatbe: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.floatbe),\n/* harmony export */   floatle: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.floatle),\n/* harmony export */   int16: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int16),\n/* harmony export */   int16be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int16be),\n/* harmony export */   int16le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int16le),\n/* harmony export */   int24: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int24),\n/* harmony export */   int24be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int24be),\n/* harmony export */   int24le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int24le),\n/* harmony export */   int32: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int32),\n/* harmony export */   int32be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int32be),\n/* harmony export */   int32le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int32le),\n/* harmony export */   int8: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.int8),\n/* harmony export */   resolveLength: () => (/* reexport safe */ _src_utils_js__WEBPACK_IMPORTED_MODULE_13__.resolveLength),\n/* harmony export */   uint16: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint16),\n/* harmony export */   uint16be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint16be),\n/* harmony export */   uint16le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint16le),\n/* harmony export */   uint24: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint24),\n/* harmony export */   uint24be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint24be),\n/* harmony export */   uint24le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint24le),\n/* harmony export */   uint32: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint32),\n/* harmony export */   uint32be: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint32be),\n/* harmony export */   uint32le: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint32le),\n/* harmony export */   uint8: () => (/* reexport safe */ _src_Number_js__WEBPACK_IMPORTED_MODULE_14__.uint8)\n/* harmony export */ });\n/* harmony import */ var _src_EncodeStream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/EncodeStream.js */ \"(rsc)/./node_modules/restructure/src/EncodeStream.js\");\n/* harmony import */ var _src_DecodeStream_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/DecodeStream.js */ \"(rsc)/./node_modules/restructure/src/DecodeStream.js\");\n/* harmony import */ var _src_Array_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/Array.js */ \"(rsc)/./node_modules/restructure/src/Array.js\");\n/* harmony import */ var _src_LazyArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/LazyArray.js */ \"(rsc)/./node_modules/restructure/src/LazyArray.js\");\n/* harmony import */ var _src_Bitfield_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./src/Bitfield.js */ \"(rsc)/./node_modules/restructure/src/Bitfield.js\");\n/* harmony import */ var _src_Boolean_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/Boolean.js */ \"(rsc)/./node_modules/restructure/src/Boolean.js\");\n/* harmony import */ var _src_Buffer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./src/Buffer.js */ \"(rsc)/./node_modules/restructure/src/Buffer.js\");\n/* harmony import */ var _src_Enum_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./src/Enum.js */ \"(rsc)/./node_modules/restructure/src/Enum.js\");\n/* harmony import */ var _src_Optional_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./src/Optional.js */ \"(rsc)/./node_modules/restructure/src/Optional.js\");\n/* harmony import */ var _src_Reserved_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./src/Reserved.js */ \"(rsc)/./node_modules/restructure/src/Reserved.js\");\n/* harmony import */ var _src_String_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./src/String.js */ \"(rsc)/./node_modules/restructure/src/String.js\");\n/* harmony import */ var _src_Struct_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./src/Struct.js */ \"(rsc)/./node_modules/restructure/src/Struct.js\");\n/* harmony import */ var _src_VersionedStruct_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./src/VersionedStruct.js */ \"(rsc)/./node_modules/restructure/src/VersionedStruct.js\");\n/* harmony import */ var _src_utils_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./src/utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n/* harmony import */ var _src_Number_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./src/Number.js */ \"(rsc)/./node_modules/restructure/src/Number.js\");\n/* harmony import */ var _src_Pointer_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./src/Pointer.js */ \"(rsc)/./node_modules/restructure/src/Pointer.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtRDtBQUNBO0FBQ2Q7QUFDUTtBQUNGO0FBQ0Y7QUFDRjtBQUNKO0FBQ1E7QUFDQTtBQUNKO0FBQ0E7QUFDa0I7O0FBRTFCO0FBQ0M7QUFDQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxQcm9qZWN0c1xcTW9vbmVsZWNBcHBcXG1vb25lbGVjLWFwcFxcbm9kZV9tb2R1bGVzXFxyZXN0cnVjdHVyZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtFbmNvZGVTdHJlYW19IGZyb20gJy4vc3JjL0VuY29kZVN0cmVhbS5qcyc7XG5leHBvcnQge0RlY29kZVN0cmVhbX0gZnJvbSAnLi9zcmMvRGVjb2RlU3RyZWFtLmpzJztcbmV4cG9ydCB7QXJyYXl9IGZyb20gJy4vc3JjL0FycmF5LmpzJztcbmV4cG9ydCB7TGF6eUFycmF5fSBmcm9tICcuL3NyYy9MYXp5QXJyYXkuanMnO1xuZXhwb3J0IHtCaXRmaWVsZH0gZnJvbSAnLi9zcmMvQml0ZmllbGQuanMnO1xuZXhwb3J0IHtCb29sZWFufSBmcm9tICcuL3NyYy9Cb29sZWFuLmpzJztcbmV4cG9ydCB7QnVmZmVyfSBmcm9tICcuL3NyYy9CdWZmZXIuanMnO1xuZXhwb3J0IHtFbnVtfSBmcm9tICcuL3NyYy9FbnVtLmpzJztcbmV4cG9ydCB7T3B0aW9uYWx9IGZyb20gJy4vc3JjL09wdGlvbmFsLmpzJztcbmV4cG9ydCB7UmVzZXJ2ZWR9IGZyb20gJy4vc3JjL1Jlc2VydmVkLmpzJztcbmV4cG9ydCB7U3RyaW5nfSBmcm9tICcuL3NyYy9TdHJpbmcuanMnO1xuZXhwb3J0IHtTdHJ1Y3R9IGZyb20gJy4vc3JjL1N0cnVjdC5qcyc7XG5leHBvcnQge1ZlcnNpb25lZFN0cnVjdH0gZnJvbSAnLi9zcmMvVmVyc2lvbmVkU3RydWN0LmpzJztcblxuZXhwb3J0ICogZnJvbSAnLi9zcmMvdXRpbHMuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9zcmMvTnVtYmVyLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vc3JjL1BvaW50ZXIuanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Array.js":
/*!***********************************************!*\
  !*** ./node_modules/restructure/src/Array.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Array: () => (/* binding */ ArrayT)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n/* harmony import */ var _Number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Number.js */ \"(rsc)/./node_modules/restructure/src/Number.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n\n\n\n\nclass ArrayT extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(type, length, lengthType = 'count') {\n    super();\n    this.type = type;\n    this.length = length;\n    this.lengthType = lengthType;\n  }\n\n  decode(stream, parent) {\n    let length;\n    const { pos } = stream;\n\n    const res = [];\n    let ctx = parent;\n\n    if (this.length != null) {\n      length = _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, stream, parent);\n    }\n\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      // define hidden properties\n      Object.defineProperties(res, {\n        parent:         { value: parent },\n        _startOffset:   { value: pos },\n        _currentOffset: { value: 0, writable: true },\n        _length:        { value: length }\n      });\n\n      ctx = res;\n    }\n\n    if ((length == null) || (this.lengthType === 'bytes')) {\n      const target = (length != null) ?\n        stream.pos + length\n      : (parent != null ? parent._length : undefined) ?\n        parent._startOffset + parent._length\n      :\n        stream.length;\n\n      while (stream.pos < target) {\n        res.push(this.type.decode(stream, ctx));\n      }\n\n    } else {\n      for (let i = 0, end = length; i < end; i++) {\n        res.push(this.type.decode(stream, ctx));\n      }\n    }\n\n    return res;\n  }\n\n  size(array, ctx, includePointers = true) {\n    if (!array) {\n      return this.type.size(null, ctx) * _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, null, ctx);\n    }\n\n    let size = 0;\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      size += this.length.size();\n      ctx = {parent: ctx, pointerSize: 0};\n    }\n\n    for (let item of array) {\n      size += this.type.size(item, ctx);\n    }\n\n    if (ctx && includePointers && this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      size += ctx.pointerSize;\n    }\n    \n    return size;\n  }\n\n  encode(stream, array, parent) {\n    let ctx = parent;\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      ctx = {\n        pointers: [],\n        startOffset: stream.pos,\n        parent\n      };\n\n      ctx.pointerOffset = stream.pos + this.size(array, ctx, false);\n      this.length.encode(stream, array.length);\n    }\n\n    for (let item of array) {\n      this.type.encode(stream, item, ctx);\n    }\n\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      let i = 0;\n      while (i < ctx.pointers.length) {\n        const ptr = ctx.pointers[i++];\n        ptr.type.encode(stream, ptr.val, ptr.parent);\n      }\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Array.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Base.js":
/*!**********************************************!*\
  !*** ./node_modules/restructure/src/Base.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Base: () => (/* binding */ Base)\n/* harmony export */ });\n/* harmony import */ var _DecodeStream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DecodeStream.js */ \"(rsc)/./node_modules/restructure/src/DecodeStream.js\");\n/* harmony import */ var _EncodeStream_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EncodeStream.js */ \"(rsc)/./node_modules/restructure/src/EncodeStream.js\");\n\n\n\nclass Base {\n  fromBuffer(buffer) {\n    let stream = new _DecodeStream_js__WEBPACK_IMPORTED_MODULE_0__.DecodeStream(buffer);\n    return this.decode(stream);\n  }\n\n  toBuffer(value) {\n    let size = this.size(value);\n    let buffer = new Uint8Array(size);\n    let stream = new _EncodeStream_js__WEBPACK_IMPORTED_MODULE_1__.EncodeStream(buffer);\n    this.encode(stream, value);\n    return buffer;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0Jhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStDO0FBQ0E7O0FBRXhDO0FBQ1A7QUFDQSxxQkFBcUIsMERBQVk7QUFDakM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsMERBQVk7QUFDakM7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xccmVzdHJ1Y3R1cmVcXHNyY1xcQmFzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0RlY29kZVN0cmVhbX0gZnJvbSAnLi9EZWNvZGVTdHJlYW0uanMnO1xuaW1wb3J0IHtFbmNvZGVTdHJlYW19IGZyb20gJy4vRW5jb2RlU3RyZWFtLmpzJztcblxuZXhwb3J0IGNsYXNzIEJhc2Uge1xuICBmcm9tQnVmZmVyKGJ1ZmZlcikge1xuICAgIGxldCBzdHJlYW0gPSBuZXcgRGVjb2RlU3RyZWFtKGJ1ZmZlcik7XG4gICAgcmV0dXJuIHRoaXMuZGVjb2RlKHN0cmVhbSk7XG4gIH1cblxuICB0b0J1ZmZlcih2YWx1ZSkge1xuICAgIGxldCBzaXplID0gdGhpcy5zaXplKHZhbHVlKTtcbiAgICBsZXQgYnVmZmVyID0gbmV3IFVpbnQ4QXJyYXkoc2l6ZSk7XG4gICAgbGV0IHN0cmVhbSA9IG5ldyBFbmNvZGVTdHJlYW0oYnVmZmVyKTtcbiAgICB0aGlzLmVuY29kZShzdHJlYW0sIHZhbHVlKTtcbiAgICByZXR1cm4gYnVmZmVyO1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Base.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Bitfield.js":
/*!**************************************************!*\
  !*** ./node_modules/restructure/src/Bitfield.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bitfield: () => (/* binding */ Bitfield)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n\n\nclass Bitfield extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(type, flags = []) {\n    super();\n    this.type = type;\n    this.flags = flags;\n  }\n\n  decode(stream) {\n    const val = this.type.decode(stream);\n\n    const res = {};\n    for (let i = 0; i < this.flags.length; i++) {\n      const flag = this.flags[i];\n      if (flag != null) {\n        res[flag] = !!(val & (1 << i));\n      }\n    }\n\n    return res;\n  }\n\n  size() {\n    return this.type.size();\n  }\n\n  encode(stream, keys) {\n    let val = 0;\n    for (let i = 0; i < this.flags.length; i++) {\n      const flag = this.flags[i];\n      if (flag != null) {\n        if (keys[flag]) { val |= (1 << i); }\n      }\n    }\n\n    return this.type.encode(stream, val);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0JpdGZpZWxkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCOztBQUV4Qix1QkFBdUIsMENBQUk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0Esb0JBQW9CLHVCQUF1QjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxvQkFBb0IsdUJBQXVCO0FBQzNDO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xccmVzdHJ1Y3R1cmVcXHNyY1xcQml0ZmllbGQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtCYXNlfSBmcm9tICcuL0Jhc2UuanMnO1xuXG5leHBvcnQgY2xhc3MgQml0ZmllbGQgZXh0ZW5kcyBCYXNlIHtcbiAgY29uc3RydWN0b3IodHlwZSwgZmxhZ3MgPSBbXSkge1xuICAgIHN1cGVyKCk7XG4gICAgdGhpcy50eXBlID0gdHlwZTtcbiAgICB0aGlzLmZsYWdzID0gZmxhZ3M7XG4gIH1cblxuICBkZWNvZGUoc3RyZWFtKSB7XG4gICAgY29uc3QgdmFsID0gdGhpcy50eXBlLmRlY29kZShzdHJlYW0pO1xuXG4gICAgY29uc3QgcmVzID0ge307XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZsYWdzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBmbGFnID0gdGhpcy5mbGFnc1tpXTtcbiAgICAgIGlmIChmbGFnICE9IG51bGwpIHtcbiAgICAgICAgcmVzW2ZsYWddID0gISEodmFsICYgKDEgPDwgaSkpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiByZXM7XG4gIH1cblxuICBzaXplKCkge1xuICAgIHJldHVybiB0aGlzLnR5cGUuc2l6ZSgpO1xuICB9XG5cbiAgZW5jb2RlKHN0cmVhbSwga2V5cykge1xuICAgIGxldCB2YWwgPSAwO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5mbGFncy5sZW5ndGg7IGkrKykge1xuICAgICAgY29uc3QgZmxhZyA9IHRoaXMuZmxhZ3NbaV07XG4gICAgICBpZiAoZmxhZyAhPSBudWxsKSB7XG4gICAgICAgIGlmIChrZXlzW2ZsYWddKSB7IHZhbCB8PSAoMSA8PCBpKTsgfVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB0aGlzLnR5cGUuZW5jb2RlKHN0cmVhbSwgdmFsKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Bitfield.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Boolean.js":
/*!*************************************************!*\
  !*** ./node_modules/restructure/src/Boolean.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Boolean: () => (/* binding */ BooleanT),\n/* harmony export */   BooleanT: () => (/* binding */ BooleanT)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n\n\nclass BooleanT extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(type) {\n    super();\n    this.type = type;\n  }\n\n  decode(stream, parent) {\n    return !!this.type.decode(stream, parent);\n  }\n\n  size(val, parent) {\n    return this.type.size(val, parent);\n  }\n\n  encode(stream, val, parent) {\n    return this.type.encode(stream, +val, parent);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0Jvb2xlYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUV4Qix1QkFBdUIsMENBQUk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFNkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xccmVzdHJ1Y3R1cmVcXHNyY1xcQm9vbGVhbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0Jhc2V9IGZyb20gJy4vQmFzZS5qcyc7XG5cbmV4cG9ydCBjbGFzcyBCb29sZWFuVCBleHRlbmRzIEJhc2Uge1xuICBjb25zdHJ1Y3Rvcih0eXBlKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICB9XG5cbiAgZGVjb2RlKHN0cmVhbSwgcGFyZW50KSB7XG4gICAgcmV0dXJuICEhdGhpcy50eXBlLmRlY29kZShzdHJlYW0sIHBhcmVudCk7XG4gIH1cblxuICBzaXplKHZhbCwgcGFyZW50KSB7XG4gICAgcmV0dXJuIHRoaXMudHlwZS5zaXplKHZhbCwgcGFyZW50KTtcbiAgfVxuXG4gIGVuY29kZShzdHJlYW0sIHZhbCwgcGFyZW50KSB7XG4gICAgcmV0dXJuIHRoaXMudHlwZS5lbmNvZGUoc3RyZWFtLCArdmFsLCBwYXJlbnQpO1xuICB9XG59XG5cbmV4cG9ydCB7Qm9vbGVhblQgYXMgQm9vbGVhbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Boolean.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Buffer.js":
/*!************************************************!*\
  !*** ./node_modules/restructure/src/Buffer.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Buffer: () => (/* binding */ BufferT),\n/* harmony export */   BufferT: () => (/* binding */ BufferT)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n/* harmony import */ var _Number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Number.js */ \"(rsc)/./node_modules/restructure/src/Number.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n\n\n\n\nclass BufferT extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(length) {\n    super();\n    this.length = length;\n  }\n  \n  decode(stream, parent) {\n    const length = _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, stream, parent);\n    return stream.readBuffer(length);\n  }\n\n  size(val, parent) {\n    if (!val) {\n      return _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, null, parent);\n    }\n\n    let len = val.length;\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      len += this.length.size();\n    }\n\n    return len;\n  }\n\n  encode(stream, buf, parent) {\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      this.length.encode(stream, buf.length);\n    }\n\n    return stream.writeBuffer(buf);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0J1ZmZlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUErQjtBQUNlO0FBQ1Y7O0FBRTdCLHNCQUFzQiwwQ0FBSTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb0RBQW1CO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsb0RBQW1CO0FBQ2hDOztBQUVBO0FBQ0EsK0JBQStCLDhDQUFPO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLCtCQUErQiw4Q0FBTztBQUN0QztBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFMkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xccmVzdHJ1Y3R1cmVcXHNyY1xcQnVmZmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QmFzZX0gZnJvbSAnLi9CYXNlLmpzJztcbmltcG9ydCB7TnVtYmVyIGFzIE51bWJlclR9IGZyb20gJy4vTnVtYmVyLmpzJztcbmltcG9ydCAqIGFzIHV0aWxzIGZyb20gJy4vdXRpbHMuanMnO1xuXG5leHBvcnQgY2xhc3MgQnVmZmVyVCBleHRlbmRzIEJhc2Uge1xuICBjb25zdHJ1Y3RvcihsZW5ndGgpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMubGVuZ3RoID0gbGVuZ3RoO1xuICB9XG4gIFxuICBkZWNvZGUoc3RyZWFtLCBwYXJlbnQpIHtcbiAgICBjb25zdCBsZW5ndGggPSB1dGlscy5yZXNvbHZlTGVuZ3RoKHRoaXMubGVuZ3RoLCBzdHJlYW0sIHBhcmVudCk7XG4gICAgcmV0dXJuIHN0cmVhbS5yZWFkQnVmZmVyKGxlbmd0aCk7XG4gIH1cblxuICBzaXplKHZhbCwgcGFyZW50KSB7XG4gICAgaWYgKCF2YWwpIHtcbiAgICAgIHJldHVybiB1dGlscy5yZXNvbHZlTGVuZ3RoKHRoaXMubGVuZ3RoLCBudWxsLCBwYXJlbnQpO1xuICAgIH1cblxuICAgIGxldCBsZW4gPSB2YWwubGVuZ3RoO1xuICAgIGlmICh0aGlzLmxlbmd0aCBpbnN0YW5jZW9mIE51bWJlclQpIHtcbiAgICAgIGxlbiArPSB0aGlzLmxlbmd0aC5zaXplKCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGxlbjtcbiAgfVxuXG4gIGVuY29kZShzdHJlYW0sIGJ1ZiwgcGFyZW50KSB7XG4gICAgaWYgKHRoaXMubGVuZ3RoIGluc3RhbmNlb2YgTnVtYmVyVCkge1xuICAgICAgdGhpcy5sZW5ndGguZW5jb2RlKHN0cmVhbSwgYnVmLmxlbmd0aCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHN0cmVhbS53cml0ZUJ1ZmZlcihidWYpO1xuICB9XG59XG5cbmV4cG9ydCB7QnVmZmVyVCBhcyBCdWZmZXJ9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Buffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/DecodeStream.js":
/*!******************************************************!*\
  !*** ./node_modules/restructure/src/DecodeStream.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeStream: () => (/* binding */ DecodeStream)\n/* harmony export */ });\n// Node back-compat.\nconst ENCODING_MAPPING = {\n  utf16le: 'utf-16le',\n  ucs2: 'utf-16le',\n  utf16be: 'utf-16be'\n}\n\nclass DecodeStream {\n  constructor(buffer) {\n    this.buffer = buffer;\n    this.view = new DataView(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    this.pos = 0;\n    this.length = this.buffer.length;\n  }\n\n  readString(length, encoding = 'ascii') {\n    encoding = ENCODING_MAPPING[encoding] || encoding;\n\n    let buf = this.readBuffer(length);\n    try {\n      let decoder = new TextDecoder(encoding);\n      return decoder.decode(buf);\n    } catch (err) {\n      return buf;\n    }\n  }\n\n  readBuffer(length) {\n    return this.buffer.slice(this.pos, (this.pos += length));\n  }\n\n  readUInt24BE() {\n    return (this.readUInt16BE() << 8) + this.readUInt8();\n  }\n\n  readUInt24LE() {\n    return this.readUInt16LE() + (this.readUInt8() << 16);\n  }\n\n  readInt24BE() {\n    return (this.readInt16BE() << 8) + this.readUInt8();\n  }\n\n  readInt24LE() {\n    return this.readUInt16LE() + (this.readInt8() << 16);\n  }\n}\n\nDecodeStream.TYPES = {\n  UInt8: 1,\n  UInt16: 2,\n  UInt24: 3,\n  UInt32: 4,\n  Int8: 1,\n  Int16: 2,\n  Int24: 3,\n  Int32: 4,\n  Float: 4,\n  Double: 8\n};\n\nfor (let key of Object.getOwnPropertyNames(DataView.prototype)) {\n  if (key.slice(0, 3) === 'get') {\n    let type = key.slice(3).replace('Ui', 'UI');\n    if (type === 'Float32') {\n      type = 'Float';\n    } else if (type === 'Float64') {\n      type = 'Double';\n    }\n    let bytes = DecodeStream.TYPES[type];\n    DecodeStream.prototype['read' + type + (bytes === 1 ? '' : 'BE')] = function () {\n      const ret = this.view[key](this.pos, false);\n      this.pos += bytes;\n      return ret;\n    };\n\n    if (bytes !== 1) {\n      DecodeStream.prototype['read' + type + 'LE'] = function () {\n        const ret = this.view[key](this.pos, true);\n        this.pos += bytes;\n        return ret;\n      };\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/DecodeStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/EncodeStream.js":
/*!******************************************************!*\
  !*** ./node_modules/restructure/src/EncodeStream.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EncodeStream: () => (/* binding */ EncodeStream)\n/* harmony export */ });\n/* harmony import */ var _DecodeStream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DecodeStream.js */ \"(rsc)/./node_modules/restructure/src/DecodeStream.js\");\n\n\nconst textEncoder = new TextEncoder();\nconst isBigEndian = new Uint8Array(new Uint16Array([0x1234]).buffer)[0] == 0x12;\n\nclass EncodeStream {\n  constructor(buffer) {\n    this.buffer = buffer;\n    this.view = new DataView(this.buffer.buffer, this.buffer.byteOffset, this.buffer.byteLength);\n    this.pos = 0;\n  }\n\n  writeBuffer(buffer) {\n    this.buffer.set(buffer, this.pos);\n    this.pos += buffer.length;\n  }\n\n  writeString(string, encoding = 'ascii') {\n    let buf;\n    switch (encoding) {\n      case 'utf16le':\n      case 'utf16-le':\n      case 'ucs2': // node treats this the same as utf16.\n        buf = stringToUtf16(string, isBigEndian);\n        break;\n\n      case 'utf16be':\n      case 'utf16-be':\n        buf = stringToUtf16(string, !isBigEndian);\n        break;\n\n      case 'utf8':\n        buf = textEncoder.encode(string);\n        break;\n\n      case 'ascii':\n        buf = stringToAscii(string);\n        break;\n\n      default:\n        throw new Error(`Unsupported encoding: ${encoding}`);\n    }\n\n    this.writeBuffer(buf);\n  }\n\n  writeUInt24BE(val) {\n    this.buffer[this.pos++] = (val >>> 16) & 0xff;\n    this.buffer[this.pos++] = (val >>> 8) & 0xff;\n    this.buffer[this.pos++] = val & 0xff;\n  }\n\n  writeUInt24LE(val) {\n    this.buffer[this.pos++] = val & 0xff;\n    this.buffer[this.pos++] = (val >>> 8) & 0xff;\n    this.buffer[this.pos++] = (val >>> 16) & 0xff;\n  }\n\n  writeInt24BE(val) {\n    if (val >= 0) {\n      this.writeUInt24BE(val);\n    } else {\n      this.writeUInt24BE(val + 0xffffff + 1);\n    }\n  }\n\n  writeInt24LE(val) {\n    if (val >= 0) {\n      this.writeUInt24LE(val);\n    } else {\n      this.writeUInt24LE(val + 0xffffff + 1);\n    }\n  }\n\n  fill(val, length) {\n    if (length < this.buffer.length) {\n      this.buffer.fill(val, this.pos, this.pos + length);\n      this.pos += length;\n    } else {\n      const buf = new Uint8Array(length);\n      buf.fill(val);\n      this.writeBuffer(buf);\n    }\n  }\n}\n\nfunction stringToUtf16(string, swap) {\n  let buf = new Uint16Array(string.length);\n  for (let i = 0; i < string.length; i++) {\n    let code = string.charCodeAt(i);\n    if (swap) {\n      code = (code >> 8) | ((code & 0xff) << 8);\n    }\n    buf[i] = code;\n  }\n  return new Uint8Array(buf.buffer);\n}\n\nfunction stringToAscii(string) {\n  let buf = new Uint8Array(string.length);\n  for (let i = 0; i < string.length; i++) {\n    // Match node.js behavior - encoding allows 8-bit rather than 7-bit.\n    buf[i] = string.charCodeAt(i);\n  }\n  return buf;\n}\n\nfor (let key of Object.getOwnPropertyNames(DataView.prototype)) {\n  if (key.slice(0, 3) === 'set') {\n    let type = key.slice(3).replace('Ui', 'UI');\n    if (type === 'Float32') {\n      type = 'Float';\n    } else if (type === 'Float64') {\n      type = 'Double';\n    }\n    let bytes = _DecodeStream_js__WEBPACK_IMPORTED_MODULE_0__.DecodeStream.TYPES[type];\n    EncodeStream.prototype['write' + type + (bytes === 1 ? '' : 'BE')] = function (value) {\n      this.view[key](this.pos, value, false);\n      this.pos += bytes;\n    };\n\n    if (bytes !== 1) {\n      EncodeStream.prototype['write' + type + 'LE'] = function (value) {\n        this.view[key](this.pos, value, true);\n        this.pos += bytes;\n      };\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0VuY29kZVN0cmVhbS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQzs7QUFFL0M7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaURBQWlELFNBQVM7QUFDMUQ7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0JBQWtCLG1CQUFtQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsbUJBQW1CO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsZ0JBQWdCLDBEQUFZO0FBQzVCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xccmVzdHJ1Y3R1cmVcXHNyY1xcRW5jb2RlU3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7RGVjb2RlU3RyZWFtfSBmcm9tICcuL0RlY29kZVN0cmVhbS5qcyc7XG5cbmNvbnN0IHRleHRFbmNvZGVyID0gbmV3IFRleHRFbmNvZGVyKCk7XG5jb25zdCBpc0JpZ0VuZGlhbiA9IG5ldyBVaW50OEFycmF5KG5ldyBVaW50MTZBcnJheShbMHgxMjM0XSkuYnVmZmVyKVswXSA9PSAweDEyO1xuXG5leHBvcnQgY2xhc3MgRW5jb2RlU3RyZWFtIHtcbiAgY29uc3RydWN0b3IoYnVmZmVyKSB7XG4gICAgdGhpcy5idWZmZXIgPSBidWZmZXI7XG4gICAgdGhpcy52aWV3ID0gbmV3IERhdGFWaWV3KHRoaXMuYnVmZmVyLmJ1ZmZlciwgdGhpcy5idWZmZXIuYnl0ZU9mZnNldCwgdGhpcy5idWZmZXIuYnl0ZUxlbmd0aCk7XG4gICAgdGhpcy5wb3MgPSAwO1xuICB9XG5cbiAgd3JpdGVCdWZmZXIoYnVmZmVyKSB7XG4gICAgdGhpcy5idWZmZXIuc2V0KGJ1ZmZlciwgdGhpcy5wb3MpO1xuICAgIHRoaXMucG9zICs9IGJ1ZmZlci5sZW5ndGg7XG4gIH1cblxuICB3cml0ZVN0cmluZyhzdHJpbmcsIGVuY29kaW5nID0gJ2FzY2lpJykge1xuICAgIGxldCBidWY7XG4gICAgc3dpdGNoIChlbmNvZGluZykge1xuICAgICAgY2FzZSAndXRmMTZsZSc6XG4gICAgICBjYXNlICd1dGYxNi1sZSc6XG4gICAgICBjYXNlICd1Y3MyJzogLy8gbm9kZSB0cmVhdHMgdGhpcyB0aGUgc2FtZSBhcyB1dGYxNi5cbiAgICAgICAgYnVmID0gc3RyaW5nVG9VdGYxNihzdHJpbmcsIGlzQmlnRW5kaWFuKTtcbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ3V0ZjE2YmUnOlxuICAgICAgY2FzZSAndXRmMTYtYmUnOlxuICAgICAgICBidWYgPSBzdHJpbmdUb1V0ZjE2KHN0cmluZywgIWlzQmlnRW5kaWFuKTtcbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ3V0ZjgnOlxuICAgICAgICBidWYgPSB0ZXh0RW5jb2Rlci5lbmNvZGUoc3RyaW5nKTtcbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ2FzY2lpJzpcbiAgICAgICAgYnVmID0gc3RyaW5nVG9Bc2NpaShzdHJpbmcpO1xuICAgICAgICBicmVhaztcblxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbnN1cHBvcnRlZCBlbmNvZGluZzogJHtlbmNvZGluZ31gKTtcbiAgICB9XG5cbiAgICB0aGlzLndyaXRlQnVmZmVyKGJ1Zik7XG4gIH1cblxuICB3cml0ZVVJbnQyNEJFKHZhbCkge1xuICAgIHRoaXMuYnVmZmVyW3RoaXMucG9zKytdID0gKHZhbCA+Pj4gMTYpICYgMHhmZjtcbiAgICB0aGlzLmJ1ZmZlclt0aGlzLnBvcysrXSA9ICh2YWwgPj4+IDgpICYgMHhmZjtcbiAgICB0aGlzLmJ1ZmZlclt0aGlzLnBvcysrXSA9IHZhbCAmIDB4ZmY7XG4gIH1cblxuICB3cml0ZVVJbnQyNExFKHZhbCkge1xuICAgIHRoaXMuYnVmZmVyW3RoaXMucG9zKytdID0gdmFsICYgMHhmZjtcbiAgICB0aGlzLmJ1ZmZlclt0aGlzLnBvcysrXSA9ICh2YWwgPj4+IDgpICYgMHhmZjtcbiAgICB0aGlzLmJ1ZmZlclt0aGlzLnBvcysrXSA9ICh2YWwgPj4+IDE2KSAmIDB4ZmY7XG4gIH1cblxuICB3cml0ZUludDI0QkUodmFsKSB7XG4gICAgaWYgKHZhbCA+PSAwKSB7XG4gICAgICB0aGlzLndyaXRlVUludDI0QkUodmFsKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy53cml0ZVVJbnQyNEJFKHZhbCArIDB4ZmZmZmZmICsgMSk7XG4gICAgfVxuICB9XG5cbiAgd3JpdGVJbnQyNExFKHZhbCkge1xuICAgIGlmICh2YWwgPj0gMCkge1xuICAgICAgdGhpcy53cml0ZVVJbnQyNExFKHZhbCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMud3JpdGVVSW50MjRMRSh2YWwgKyAweGZmZmZmZiArIDEpO1xuICAgIH1cbiAgfVxuXG4gIGZpbGwodmFsLCBsZW5ndGgpIHtcbiAgICBpZiAobGVuZ3RoIDwgdGhpcy5idWZmZXIubGVuZ3RoKSB7XG4gICAgICB0aGlzLmJ1ZmZlci5maWxsKHZhbCwgdGhpcy5wb3MsIHRoaXMucG9zICsgbGVuZ3RoKTtcbiAgICAgIHRoaXMucG9zICs9IGxlbmd0aDtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgYnVmID0gbmV3IFVpbnQ4QXJyYXkobGVuZ3RoKTtcbiAgICAgIGJ1Zi5maWxsKHZhbCk7XG4gICAgICB0aGlzLndyaXRlQnVmZmVyKGJ1Zik7XG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIHN0cmluZ1RvVXRmMTYoc3RyaW5nLCBzd2FwKSB7XG4gIGxldCBidWYgPSBuZXcgVWludDE2QXJyYXkoc3RyaW5nLmxlbmd0aCk7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyaW5nLmxlbmd0aDsgaSsrKSB7XG4gICAgbGV0IGNvZGUgPSBzdHJpbmcuY2hhckNvZGVBdChpKTtcbiAgICBpZiAoc3dhcCkge1xuICAgICAgY29kZSA9IChjb2RlID4+IDgpIHwgKChjb2RlICYgMHhmZikgPDwgOCk7XG4gICAgfVxuICAgIGJ1ZltpXSA9IGNvZGU7XG4gIH1cbiAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGJ1Zi5idWZmZXIpO1xufVxuXG5mdW5jdGlvbiBzdHJpbmdUb0FzY2lpKHN0cmluZykge1xuICBsZXQgYnVmID0gbmV3IFVpbnQ4QXJyYXkoc3RyaW5nLmxlbmd0aCk7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyaW5nLmxlbmd0aDsgaSsrKSB7XG4gICAgLy8gTWF0Y2ggbm9kZS5qcyBiZWhhdmlvciAtIGVuY29kaW5nIGFsbG93cyA4LWJpdCByYXRoZXIgdGhhbiA3LWJpdC5cbiAgICBidWZbaV0gPSBzdHJpbmcuY2hhckNvZGVBdChpKTtcbiAgfVxuICByZXR1cm4gYnVmO1xufVxuXG5mb3IgKGxldCBrZXkgb2YgT2JqZWN0LmdldE93blByb3BlcnR5TmFtZXMoRGF0YVZpZXcucHJvdG90eXBlKSkge1xuICBpZiAoa2V5LnNsaWNlKDAsIDMpID09PSAnc2V0Jykge1xuICAgIGxldCB0eXBlID0ga2V5LnNsaWNlKDMpLnJlcGxhY2UoJ1VpJywgJ1VJJyk7XG4gICAgaWYgKHR5cGUgPT09ICdGbG9hdDMyJykge1xuICAgICAgdHlwZSA9ICdGbG9hdCc7XG4gICAgfSBlbHNlIGlmICh0eXBlID09PSAnRmxvYXQ2NCcpIHtcbiAgICAgIHR5cGUgPSAnRG91YmxlJztcbiAgICB9XG4gICAgbGV0IGJ5dGVzID0gRGVjb2RlU3RyZWFtLlRZUEVTW3R5cGVdO1xuICAgIEVuY29kZVN0cmVhbS5wcm90b3R5cGVbJ3dyaXRlJyArIHR5cGUgKyAoYnl0ZXMgPT09IDEgPyAnJyA6ICdCRScpXSA9IGZ1bmN0aW9uICh2YWx1ZSkge1xuICAgICAgdGhpcy52aWV3W2tleV0odGhpcy5wb3MsIHZhbHVlLCBmYWxzZSk7XG4gICAgICB0aGlzLnBvcyArPSBieXRlcztcbiAgICB9O1xuXG4gICAgaWYgKGJ5dGVzICE9PSAxKSB7XG4gICAgICBFbmNvZGVTdHJlYW0ucHJvdG90eXBlWyd3cml0ZScgKyB0eXBlICsgJ0xFJ10gPSBmdW5jdGlvbiAodmFsdWUpIHtcbiAgICAgICAgdGhpcy52aWV3W2tleV0odGhpcy5wb3MsIHZhbHVlLCB0cnVlKTtcbiAgICAgICAgdGhpcy5wb3MgKz0gYnl0ZXM7XG4gICAgICB9O1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/EncodeStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Enum.js":
/*!**********************************************!*\
  !*** ./node_modules/restructure/src/Enum.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Enum: () => (/* binding */ Enum)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n\n\nclass Enum extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(type, options = []) {\n    super();\n    this.type = type;\n    this.options = options;\n  }\n  \n  decode(stream) {\n    const index = this.type.decode(stream);\n    return this.options[index] || index;\n  }\n\n  size() {\n    return this.type.size();\n  }\n\n  encode(stream, val) {\n    const index = this.options.indexOf(val);\n    if (index === -1) {\n      throw new Error(`Unknown option in enum: ${val}`);\n    }\n\n    return this.type.encode(stream, index);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0VudW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7O0FBRXhCLG1CQUFtQiwwQ0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaURBQWlELElBQUk7QUFDckQ7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXHJlc3RydWN0dXJlXFxzcmNcXEVudW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtCYXNlfSBmcm9tICcuL0Jhc2UuanMnO1xuXG5leHBvcnQgY2xhc3MgRW51bSBleHRlbmRzIEJhc2Uge1xuICBjb25zdHJ1Y3Rvcih0eXBlLCBvcHRpb25zID0gW10pIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMudHlwZSA9IHR5cGU7XG4gICAgdGhpcy5vcHRpb25zID0gb3B0aW9ucztcbiAgfVxuICBcbiAgZGVjb2RlKHN0cmVhbSkge1xuICAgIGNvbnN0IGluZGV4ID0gdGhpcy50eXBlLmRlY29kZShzdHJlYW0pO1xuICAgIHJldHVybiB0aGlzLm9wdGlvbnNbaW5kZXhdIHx8IGluZGV4O1xuICB9XG5cbiAgc2l6ZSgpIHtcbiAgICByZXR1cm4gdGhpcy50eXBlLnNpemUoKTtcbiAgfVxuXG4gIGVuY29kZShzdHJlYW0sIHZhbCkge1xuICAgIGNvbnN0IGluZGV4ID0gdGhpcy5vcHRpb25zLmluZGV4T2YodmFsKTtcbiAgICBpZiAoaW5kZXggPT09IC0xKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gb3B0aW9uIGluIGVudW06ICR7dmFsfWApO1xuICAgIH1cblxuICAgIHJldHVybiB0aGlzLnR5cGUuZW5jb2RlKHN0cmVhbSwgaW5kZXgpO1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Enum.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/LazyArray.js":
/*!***************************************************!*\
  !*** ./node_modules/restructure/src/LazyArray.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LazyArray: () => (/* binding */ LazyArray)\n/* harmony export */ });\n/* harmony import */ var _Array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Array.js */ \"(rsc)/./node_modules/restructure/src/Array.js\");\n/* harmony import */ var _Number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Number.js */ \"(rsc)/./node_modules/restructure/src/Number.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n\n\n\n\nclass LazyArray extends _Array_js__WEBPACK_IMPORTED_MODULE_0__.Array {\n  decode(stream, parent) {\n    const { pos } = stream;\n    const length = _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, stream, parent);\n\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      parent = {\n        parent,\n        _startOffset: pos,\n        _currentOffset: 0,\n        _length: length\n      };\n    }\n\n    const res = new LazyArrayValue(this.type, length, stream, parent);\n\n    stream.pos += length * this.type.size(null, parent);\n    return res;\n  }\n\n  size(val, ctx) {\n    if (val instanceof LazyArrayValue) {\n      val = val.toArray();\n    }\n\n    return super.size(val, ctx);\n  }\n\n  encode(stream, val, ctx) {\n    if (val instanceof LazyArrayValue) {\n      val = val.toArray();\n    }\n\n    return super.encode(stream, val, ctx);\n  }\n}\n\nclass LazyArrayValue {\n  constructor(type, length, stream, ctx) {\n    this.type = type;\n    this.length = length;\n    this.stream = stream;\n    this.ctx = ctx;\n    this.base = this.stream.pos;\n    this.items = [];\n  }\n\n  get(index) {\n    if ((index < 0) || (index >= this.length)) {\n      return undefined;\n    }\n\n    if (this.items[index] == null) {\n      const { pos } = this.stream;\n      this.stream.pos = this.base + (this.type.size(null, this.ctx) * index);\n      this.items[index] = this.type.decode(this.stream, this.ctx);\n      this.stream.pos = pos;\n    }\n\n    return this.items[index];\n  }\n\n  toArray() {\n    const result = [];\n    for (let i = 0, end = this.length; i < end; i++) {\n      result.push(this.get(i));\n    }\n    return result;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL0xhenlBcnJheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTJDO0FBQ0c7QUFDVjs7QUFFN0Isd0JBQXdCLDRDQUFNO0FBQ3JDO0FBQ0EsWUFBWSxNQUFNO0FBQ2xCLG1CQUFtQixvREFBbUI7O0FBRXRDLCtCQUErQiw4Q0FBTztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsY0FBYyxNQUFNO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHVDQUF1QyxTQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXG5vZGVfbW9kdWxlc1xccmVzdHJ1Y3R1cmVcXHNyY1xcTGF6eUFycmF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QXJyYXkgYXMgQXJyYXlUfSBmcm9tICcuL0FycmF5LmpzJztcbmltcG9ydCB7TnVtYmVyIGFzIE51bWJlclR9IGZyb20gJy4vTnVtYmVyLmpzJztcbmltcG9ydCAqIGFzIHV0aWxzIGZyb20gJy4vdXRpbHMuanMnO1xuXG5leHBvcnQgY2xhc3MgTGF6eUFycmF5IGV4dGVuZHMgQXJyYXlUIHtcbiAgZGVjb2RlKHN0cmVhbSwgcGFyZW50KSB7XG4gICAgY29uc3QgeyBwb3MgfSA9IHN0cmVhbTtcbiAgICBjb25zdCBsZW5ndGggPSB1dGlscy5yZXNvbHZlTGVuZ3RoKHRoaXMubGVuZ3RoLCBzdHJlYW0sIHBhcmVudCk7XG5cbiAgICBpZiAodGhpcy5sZW5ndGggaW5zdGFuY2VvZiBOdW1iZXJUKSB7XG4gICAgICBwYXJlbnQgPSB7XG4gICAgICAgIHBhcmVudCxcbiAgICAgICAgX3N0YXJ0T2Zmc2V0OiBwb3MsXG4gICAgICAgIF9jdXJyZW50T2Zmc2V0OiAwLFxuICAgICAgICBfbGVuZ3RoOiBsZW5ndGhcbiAgICAgIH07XG4gICAgfVxuXG4gICAgY29uc3QgcmVzID0gbmV3IExhenlBcnJheVZhbHVlKHRoaXMudHlwZSwgbGVuZ3RoLCBzdHJlYW0sIHBhcmVudCk7XG5cbiAgICBzdHJlYW0ucG9zICs9IGxlbmd0aCAqIHRoaXMudHlwZS5zaXplKG51bGwsIHBhcmVudCk7XG4gICAgcmV0dXJuIHJlcztcbiAgfVxuXG4gIHNpemUodmFsLCBjdHgpIHtcbiAgICBpZiAodmFsIGluc3RhbmNlb2YgTGF6eUFycmF5VmFsdWUpIHtcbiAgICAgIHZhbCA9IHZhbC50b0FycmF5KCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHN1cGVyLnNpemUodmFsLCBjdHgpO1xuICB9XG5cbiAgZW5jb2RlKHN0cmVhbSwgdmFsLCBjdHgpIHtcbiAgICBpZiAodmFsIGluc3RhbmNlb2YgTGF6eUFycmF5VmFsdWUpIHtcbiAgICAgIHZhbCA9IHZhbC50b0FycmF5KCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHN1cGVyLmVuY29kZShzdHJlYW0sIHZhbCwgY3R4KTtcbiAgfVxufVxuXG5jbGFzcyBMYXp5QXJyYXlWYWx1ZSB7XG4gIGNvbnN0cnVjdG9yKHR5cGUsIGxlbmd0aCwgc3RyZWFtLCBjdHgpIHtcbiAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICAgIHRoaXMubGVuZ3RoID0gbGVuZ3RoO1xuICAgIHRoaXMuc3RyZWFtID0gc3RyZWFtO1xuICAgIHRoaXMuY3R4ID0gY3R4O1xuICAgIHRoaXMuYmFzZSA9IHRoaXMuc3RyZWFtLnBvcztcbiAgICB0aGlzLml0ZW1zID0gW107XG4gIH1cblxuICBnZXQoaW5kZXgpIHtcbiAgICBpZiAoKGluZGV4IDwgMCkgfHwgKGluZGV4ID49IHRoaXMubGVuZ3RoKSkge1xuICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG5cbiAgICBpZiAodGhpcy5pdGVtc1tpbmRleF0gPT0gbnVsbCkge1xuICAgICAgY29uc3QgeyBwb3MgfSA9IHRoaXMuc3RyZWFtO1xuICAgICAgdGhpcy5zdHJlYW0ucG9zID0gdGhpcy5iYXNlICsgKHRoaXMudHlwZS5zaXplKG51bGwsIHRoaXMuY3R4KSAqIGluZGV4KTtcbiAgICAgIHRoaXMuaXRlbXNbaW5kZXhdID0gdGhpcy50eXBlLmRlY29kZSh0aGlzLnN0cmVhbSwgdGhpcy5jdHgpO1xuICAgICAgdGhpcy5zdHJlYW0ucG9zID0gcG9zO1xuICAgIH1cblxuICAgIHJldHVybiB0aGlzLml0ZW1zW2luZGV4XTtcbiAgfVxuXG4gIHRvQXJyYXkoKSB7XG4gICAgY29uc3QgcmVzdWx0ID0gW107XG4gICAgZm9yIChsZXQgaSA9IDAsIGVuZCA9IHRoaXMubGVuZ3RoOyBpIDwgZW5kOyBpKyspIHtcbiAgICAgIHJlc3VsdC5wdXNoKHRoaXMuZ2V0KGkpKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/LazyArray.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Number.js":
/*!************************************************!*\
  !*** ./node_modules/restructure/src/Number.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fixed: () => (/* binding */ Fixed),\n/* harmony export */   Number: () => (/* binding */ NumberT),\n/* harmony export */   double: () => (/* binding */ double),\n/* harmony export */   doublebe: () => (/* binding */ doublebe),\n/* harmony export */   doublele: () => (/* binding */ doublele),\n/* harmony export */   fixed16: () => (/* binding */ fixed16),\n/* harmony export */   fixed16be: () => (/* binding */ fixed16be),\n/* harmony export */   fixed16le: () => (/* binding */ fixed16le),\n/* harmony export */   fixed32: () => (/* binding */ fixed32),\n/* harmony export */   fixed32be: () => (/* binding */ fixed32be),\n/* harmony export */   fixed32le: () => (/* binding */ fixed32le),\n/* harmony export */   float: () => (/* binding */ float),\n/* harmony export */   floatbe: () => (/* binding */ floatbe),\n/* harmony export */   floatle: () => (/* binding */ floatle),\n/* harmony export */   int16: () => (/* binding */ int16),\n/* harmony export */   int16be: () => (/* binding */ int16be),\n/* harmony export */   int16le: () => (/* binding */ int16le),\n/* harmony export */   int24: () => (/* binding */ int24),\n/* harmony export */   int24be: () => (/* binding */ int24be),\n/* harmony export */   int24le: () => (/* binding */ int24le),\n/* harmony export */   int32: () => (/* binding */ int32),\n/* harmony export */   int32be: () => (/* binding */ int32be),\n/* harmony export */   int32le: () => (/* binding */ int32le),\n/* harmony export */   int8: () => (/* binding */ int8),\n/* harmony export */   uint16: () => (/* binding */ uint16),\n/* harmony export */   uint16be: () => (/* binding */ uint16be),\n/* harmony export */   uint16le: () => (/* binding */ uint16le),\n/* harmony export */   uint24: () => (/* binding */ uint24),\n/* harmony export */   uint24be: () => (/* binding */ uint24be),\n/* harmony export */   uint24le: () => (/* binding */ uint24le),\n/* harmony export */   uint32: () => (/* binding */ uint32),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint32le: () => (/* binding */ uint32le),\n/* harmony export */   uint8: () => (/* binding */ uint8)\n/* harmony export */ });\n/* harmony import */ var _DecodeStream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DecodeStream.js */ \"(rsc)/./node_modules/restructure/src/DecodeStream.js\");\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n\n\n\nclass NumberT extends _Base_js__WEBPACK_IMPORTED_MODULE_1__.Base {\n  constructor(type, endian = 'BE') {\n    super();\n    this.type = type;\n    this.endian = endian;\n    this.fn = this.type;\n    if (this.type[this.type.length - 1] !== '8') {\n      this.fn += this.endian;\n    }\n  }\n\n  size() {\n    return _DecodeStream_js__WEBPACK_IMPORTED_MODULE_0__.DecodeStream.TYPES[this.type];\n  }\n\n  decode(stream) {\n    return stream[`read${this.fn}`]();\n  }\n\n  encode(stream, val) {\n    return stream[`write${this.fn}`](val);\n  }\n}\n\n\n\nconst uint8 = new NumberT('UInt8');\nconst uint16be = new NumberT('UInt16', 'BE');\nconst uint16 = uint16be;\nconst uint16le = new NumberT('UInt16', 'LE');\nconst uint24be = new NumberT('UInt24', 'BE');\nconst uint24 = uint24be;\nconst uint24le = new NumberT('UInt24', 'LE');\nconst uint32be = new NumberT('UInt32', 'BE');\nconst uint32 = uint32be;\nconst uint32le = new NumberT('UInt32', 'LE');\nconst int8 = new NumberT('Int8');\nconst int16be = new NumberT('Int16', 'BE');\nconst int16 = int16be;\nconst int16le = new NumberT('Int16', 'LE');\nconst int24be = new NumberT('Int24', 'BE');\nconst int24 = int24be;\nconst int24le = new NumberT('Int24', 'LE');\nconst int32be = new NumberT('Int32', 'BE');\nconst int32 = int32be;\nconst int32le = new NumberT('Int32', 'LE');\nconst floatbe = new NumberT('Float', 'BE');\nconst float = floatbe;\nconst floatle = new NumberT('Float', 'LE');\nconst doublebe = new NumberT('Double', 'BE');\nconst double = doublebe;\nconst doublele = new NumberT('Double', 'LE');\n\nclass Fixed extends NumberT {\n  constructor(size, endian, fracBits = size >> 1) {\n    super(`Int${size}`, endian);\n    this._point = 1 << fracBits;\n  }\n\n  decode(stream) {\n    return super.decode(stream) / this._point;\n  }\n\n  encode(stream, val) {\n    return super.encode(stream, (val * this._point) | 0);\n  }\n}\n\nconst fixed16be = new Fixed(16, 'BE');\nconst fixed16 = fixed16be;\nconst fixed16le = new Fixed(16, 'LE');\nconst fixed32be = new Fixed(32, 'BE');\nconst fixed32 = fixed32be;\nconst fixed32le = new Fixed(32, 'LE');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Number.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Optional.js":
/*!**************************************************!*\
  !*** ./node_modules/restructure/src/Optional.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Optional: () => (/* binding */ Optional)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n\n\nclass Optional extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(type, condition = true) {\n    super();\n    this.type = type;\n    this.condition = condition;\n  }\n\n  decode(stream, parent) {\n    let { condition } = this;\n    if (typeof condition === 'function') {\n      condition = condition.call(parent, parent);\n    }\n\n    if (condition) {\n      return this.type.decode(stream, parent);\n    }\n  }\n\n  size(val, parent) {\n    let { condition } = this;\n    if (typeof condition === 'function') {\n      condition = condition.call(parent, parent);\n    }\n\n    if (condition) {\n      return this.type.size(val, parent);\n    } else {\n      return 0;\n    }\n  }\n\n  encode(stream, val, parent) {\n    let { condition } = this;\n    if (typeof condition === 'function') {\n      condition = condition.call(parent, parent);\n    }\n\n    if (condition) {\n      return this.type.encode(stream, val, parent);\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL09wdGlvbmFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCOztBQUV4Qix1QkFBdUIsMENBQUk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFVBQVUsWUFBWTtBQUN0QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVLFlBQVk7QUFDdEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVSxZQUFZO0FBQ3RCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXHJlc3RydWN0dXJlXFxzcmNcXE9wdGlvbmFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QmFzZX0gZnJvbSAnLi9CYXNlLmpzJztcblxuZXhwb3J0IGNsYXNzIE9wdGlvbmFsIGV4dGVuZHMgQmFzZSB7XG4gIGNvbnN0cnVjdG9yKHR5cGUsIGNvbmRpdGlvbiA9IHRydWUpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMudHlwZSA9IHR5cGU7XG4gICAgdGhpcy5jb25kaXRpb24gPSBjb25kaXRpb247XG4gIH1cblxuICBkZWNvZGUoc3RyZWFtLCBwYXJlbnQpIHtcbiAgICBsZXQgeyBjb25kaXRpb24gfSA9IHRoaXM7XG4gICAgaWYgKHR5cGVvZiBjb25kaXRpb24gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgIGNvbmRpdGlvbiA9IGNvbmRpdGlvbi5jYWxsKHBhcmVudCwgcGFyZW50KTtcbiAgICB9XG5cbiAgICBpZiAoY29uZGl0aW9uKSB7XG4gICAgICByZXR1cm4gdGhpcy50eXBlLmRlY29kZShzdHJlYW0sIHBhcmVudCk7XG4gICAgfVxuICB9XG5cbiAgc2l6ZSh2YWwsIHBhcmVudCkge1xuICAgIGxldCB7IGNvbmRpdGlvbiB9ID0gdGhpcztcbiAgICBpZiAodHlwZW9mIGNvbmRpdGlvbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgY29uZGl0aW9uID0gY29uZGl0aW9uLmNhbGwocGFyZW50LCBwYXJlbnQpO1xuICAgIH1cblxuICAgIGlmIChjb25kaXRpb24pIHtcbiAgICAgIHJldHVybiB0aGlzLnR5cGUuc2l6ZSh2YWwsIHBhcmVudCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgfVxuXG4gIGVuY29kZShzdHJlYW0sIHZhbCwgcGFyZW50KSB7XG4gICAgbGV0IHsgY29uZGl0aW9uIH0gPSB0aGlzO1xuICAgIGlmICh0eXBlb2YgY29uZGl0aW9uID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBjb25kaXRpb24gPSBjb25kaXRpb24uY2FsbChwYXJlbnQsIHBhcmVudCk7XG4gICAgfVxuXG4gICAgaWYgKGNvbmRpdGlvbikge1xuICAgICAgcmV0dXJuIHRoaXMudHlwZS5lbmNvZGUoc3RyZWFtLCB2YWwsIHBhcmVudCk7XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Optional.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Pointer.js":
/*!*************************************************!*\
  !*** ./node_modules/restructure/src/Pointer.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pointer: () => (/* binding */ Pointer),\n/* harmony export */   VoidPointer: () => (/* binding */ VoidPointer)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n\n\n\nclass Pointer extends _Base_js__WEBPACK_IMPORTED_MODULE_1__.Base {\n  constructor(offsetType, type, options = {}) {\n    super();\n    this.offsetType = offsetType;\n    this.type = type;\n    this.options = options;\n    if (this.type === 'void') { this.type = null; }\n    if (this.options.type == null) { this.options.type = 'local'; }\n    if (this.options.allowNull == null) { this.options.allowNull = true; }\n    if (this.options.nullValue == null) { this.options.nullValue = 0; }\n    if (this.options.lazy == null) { this.options.lazy = false; }\n    if (this.options.relativeTo) {\n      if (typeof this.options.relativeTo !== 'function') {\n        throw new Error('relativeTo option must be a function');\n      }\n      this.relativeToGetter = options.relativeTo;\n    }\n  }\n\n  decode(stream, ctx) {\n    const offset = this.offsetType.decode(stream, ctx);\n\n    // handle NULL pointers\n    if ((offset === this.options.nullValue) && this.options.allowNull) {\n      return null;\n    }\n\n    let relative;\n    switch (this.options.type) {\n      case 'local':     relative = ctx._startOffset; break;\n      case 'immediate': relative = stream.pos - this.offsetType.size(); break;\n      case 'parent':    relative = ctx.parent._startOffset; break;\n      default:\n        var c = ctx;\n        while (c.parent) {\n          c = c.parent;\n        }\n\n        relative = c._startOffset || 0;\n    }\n\n    if (this.options.relativeTo) {\n      relative += this.relativeToGetter(ctx);\n    }\n\n    const ptr = offset + relative;\n\n    if (this.type != null) {\n      let val = null;\n      const decodeValue = () => {\n        if (val != null) { return val; }\n\n        const { pos } = stream;\n        stream.pos = ptr;\n        val = this.type.decode(stream, ctx);\n        stream.pos = pos;\n        return val;\n      };\n\n      // If this is a lazy pointer, define a getter to decode only when needed.\n      // This obviously only works when the pointer is contained by a Struct.\n      if (this.options.lazy) {\n        return new _utils_js__WEBPACK_IMPORTED_MODULE_0__.PropertyDescriptor({\n          get: decodeValue});\n      }\n\n      return decodeValue();\n    } else {\n      return ptr;\n    }\n  }\n\n  size(val, ctx) {\n    const parent = ctx;\n    switch (this.options.type) {\n      case 'local': case 'immediate':\n        break;\n      case 'parent':\n        ctx = ctx.parent;\n        break;\n      default: // global\n        while (ctx.parent) {\n          ctx = ctx.parent;\n        }\n    }\n\n    let { type } = this;\n    if (type == null) {\n      if (!(val instanceof VoidPointer)) {\n        throw new Error(\"Must be a VoidPointer\");\n      }\n\n      ({ type } = val);\n      val = val.value;\n    }\n\n    if (val && ctx) {\n      // Must be written as two separate lines rather than += in case `type.size` mutates ctx.pointerSize.\n      let size = type.size(val, parent);\n      ctx.pointerSize += size;\n    }\n\n    return this.offsetType.size();\n  }\n\n  encode(stream, val, ctx) {\n    let relative;\n    const parent = ctx;\n    if ((val == null)) {\n      this.offsetType.encode(stream, this.options.nullValue);\n      return;\n    }\n\n    switch (this.options.type) {\n      case 'local':\n        relative = ctx.startOffset;\n        break;\n      case 'immediate':\n        relative = stream.pos + this.offsetType.size(val, parent);\n        break;\n      case 'parent':\n        ctx = ctx.parent;\n        relative = ctx.startOffset;\n        break;\n      default: // global\n        relative = 0;\n        while (ctx.parent) {\n          ctx = ctx.parent;\n        }\n    }\n\n    if (this.options.relativeTo) {\n      relative += this.relativeToGetter(parent.val);\n    }\n\n    this.offsetType.encode(stream, ctx.pointerOffset - relative);\n\n    let { type } = this;\n    if (type == null) {\n      if (!(val instanceof VoidPointer)) {\n        throw new Error(\"Must be a VoidPointer\");\n      }\n\n      ({ type } = val);\n      val = val.value;\n    }\n\n    ctx.pointers.push({\n      type,\n      val,\n      parent\n    });\n\n    return ctx.pointerOffset += type.size(val, parent);\n  }\n}\n\n// A pointer whose type is determined at decode time\nclass VoidPointer {\n  constructor(type, value) {\n    this.type = type;\n    this.value = value;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Pointer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Reserved.js":
/*!**************************************************!*\
  !*** ./node_modules/restructure/src/Reserved.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Reserved: () => (/* binding */ Reserved)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n\n\n\nclass Reserved extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(type, count = 1) {\n    super();\n    this.type = type;\n    this.count = count;\n  }\n  decode(stream, parent) {\n    stream.pos += this.size(null, parent);\n    return undefined;\n  }\n\n  size(data, parent) {\n    const count = _utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveLength(this.count, null, parent);\n    return this.type.size() * count;\n  }\n\n  encode(stream, val, parent) {\n    return stream.fill(0, this.size(val, parent));\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL1Jlc2VydmVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUNLOztBQUU3Qix1QkFBdUIsMENBQUk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLG9EQUFtQjtBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXHJlc3RydWN0dXJlXFxzcmNcXFJlc2VydmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QmFzZX0gZnJvbSAnLi9CYXNlLmpzJztcbmltcG9ydCAqIGFzIHV0aWxzIGZyb20gJy4vdXRpbHMuanMnO1xuXG5leHBvcnQgY2xhc3MgUmVzZXJ2ZWQgZXh0ZW5kcyBCYXNlIHtcbiAgY29uc3RydWN0b3IodHlwZSwgY291bnQgPSAxKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICAgIHRoaXMuY291bnQgPSBjb3VudDtcbiAgfVxuICBkZWNvZGUoc3RyZWFtLCBwYXJlbnQpIHtcbiAgICBzdHJlYW0ucG9zICs9IHRoaXMuc2l6ZShudWxsLCBwYXJlbnQpO1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cblxuICBzaXplKGRhdGEsIHBhcmVudCkge1xuICAgIGNvbnN0IGNvdW50ID0gdXRpbHMucmVzb2x2ZUxlbmd0aCh0aGlzLmNvdW50LCBudWxsLCBwYXJlbnQpO1xuICAgIHJldHVybiB0aGlzLnR5cGUuc2l6ZSgpICogY291bnQ7XG4gIH1cblxuICBlbmNvZGUoc3RyZWFtLCB2YWwsIHBhcmVudCkge1xuICAgIHJldHVybiBzdHJlYW0uZmlsbCgwLCB0aGlzLnNpemUodmFsLCBwYXJlbnQpKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Reserved.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/String.js":
/*!************************************************!*\
  !*** ./node_modules/restructure/src/String.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   String: () => (/* binding */ StringT)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n/* harmony import */ var _Number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Number.js */ \"(rsc)/./node_modules/restructure/src/Number.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n\n\n\n\nclass StringT extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(length, encoding = 'ascii') {\n    super();\n    this.length = length;\n    this.encoding = encoding;\n  }\n\n  decode(stream, parent) {\n    let length, pos;\n\n    let { encoding } = this;\n    if (typeof encoding === 'function') {\n      encoding = encoding.call(parent, parent) || 'ascii';\n    }\n    let width = encodingWidth(encoding);\n\n    if (this.length != null) {\n      length = _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, stream, parent);\n    } else {\n      let buffer;\n      ({buffer, length, pos} = stream);\n\n      while ((pos < length - width + 1) &&\n        (buffer[pos] !== 0x00 ||\n        (width === 2 && buffer[pos+1] !== 0x00)\n        )) {\n        pos += width;\n      }\n\n      length = pos - stream.pos;\n    }\n\n\n    const string = stream.readString(length, encoding);\n\n    if ((this.length == null) && (stream.pos < stream.length)) {\n      stream.pos+=width;\n    }\n\n    return string;\n  }\n\n  size(val, parent) {\n    // Use the defined value if no value was given\n    if (val === undefined || val === null) {\n      return _utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveLength(this.length, null, parent);\n    }\n\n    let { encoding } = this;\n    if (typeof encoding === 'function') {\n      encoding = encoding.call(parent != null ? parent.val : undefined, parent != null ? parent.val : undefined) || 'ascii';\n    }\n\n    if (encoding === 'utf16be') {\n      encoding = 'utf16le';\n    }\n\n    let size = byteLength(val, encoding);\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      size += this.length.size();\n    }\n\n    if ((this.length == null)) {\n      size += encodingWidth(encoding);\n    }\n\n    return size;\n  }\n\n  encode(stream, val, parent) {\n    let { encoding } = this;\n    if (typeof encoding === 'function') {\n      encoding = encoding.call(parent != null ? parent.val : undefined, parent != null ? parent.val : undefined) || 'ascii';\n    }\n\n    if (this.length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_1__.Number) {\n      this.length.encode(stream, byteLength(val, encoding));\n    }\n\n    stream.writeString(val, encoding);\n\n    if ((this.length == null)) {\n      return encodingWidth(encoding) == 2 ?\n        stream.writeUInt16LE(0x0000) :\n        stream.writeUInt8(0x00);\n    }\n  }\n}\n\nfunction encodingWidth(encoding) {\n  switch(encoding) {\n    case 'ascii':\n    case 'utf8': // utf8 is a byte-based encoding for zero-term string\n      return 1;\n    case 'utf16le':\n    case 'utf16-le':\n    case 'utf-16be':\n    case 'utf-16le':\n    case 'utf16be':\n    case 'utf16-be':\n    case 'ucs2':\n      return 2;\n    default:\n      //TODO: assume all other encodings are 1-byters\n      //throw new Error('Unknown encoding ' + encoding);\n      return 1;\n  }\n}\n\nfunction byteLength(string, encoding) {\n  switch (encoding) {\n    case 'ascii':\n      return string.length;\n    case 'utf8':\n      let len = 0;\n      for (let i = 0; i < string.length; i++) {\n        let c = string.charCodeAt(i);\n\n        if (c >= 0xd800 && c <= 0xdbff && i < string.length - 1) {\n          let c2 = string.charCodeAt(++i);\n          if ((c2 & 0xfc00) === 0xdc00) {\n            c = ((c & 0x3ff) << 10) + (c2 & 0x3ff) + 0x10000;\n          } else {\n            // unmatched surrogate.\n            i--;\n          }\n        }\n\n        if ((c & 0xffffff80) === 0) {\n          len++;\n        } else if ((c & 0xfffff800) === 0) {\n          len += 2;\n        } else if ((c & 0xffff0000) === 0) {\n          len += 3;\n        } else if ((c & 0xffe00000) === 0) {\n          len += 4;\n        }\n      }\n      return len;\n    case 'utf16le':\n    case 'utf16-le':\n    case 'utf16be':\n    case 'utf16-be':\n    case 'ucs2':\n      return string.length * 2;\n    default:\n      throw new Error('Unknown encoding ' + encoding);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/String.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/Struct.js":
/*!************************************************!*\
  !*** ./node_modules/restructure/src/Struct.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Struct: () => (/* binding */ Struct)\n/* harmony export */ });\n/* harmony import */ var _Base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base.js */ \"(rsc)/./node_modules/restructure/src/Base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/restructure/src/utils.js\");\n\n\n\nclass Struct extends _Base_js__WEBPACK_IMPORTED_MODULE_0__.Base {\n  constructor(fields = {}) {\n    super();\n    this.fields = fields;\n  }\n\n  decode(stream, parent, length = 0) {\n    const res = this._setup(stream, parent, length);\n    this._parseFields(stream, res, this.fields);\n\n    if (this.process != null) {\n      this.process.call(res, stream);\n    }\n    return res;\n  }\n\n  _setup(stream, parent, length) {\n    const res = {};\n\n    // define hidden properties\n    Object.defineProperties(res, {\n      parent:         { value: parent },\n      _startOffset:   { value: stream.pos },\n      _currentOffset: { value: 0, writable: true },\n      _length:        { value: length }\n    });\n\n    return res;\n  }\n\n  _parseFields(stream, res, fields) {\n    for (let key in fields) {\n      var val;\n      const type = fields[key];\n      if (typeof type === 'function') {\n        val = type.call(res, res);\n      } else {\n        val = type.decode(stream, res);\n      }\n\n      if (val !== undefined) {\n        if (val instanceof _utils_js__WEBPACK_IMPORTED_MODULE_1__.PropertyDescriptor) {\n          Object.defineProperty(res, key, val);\n        } else {\n          res[key] = val;\n        }\n      }\n\n      res._currentOffset = stream.pos - res._startOffset;\n    }\n\n  }\n\n  size(val, parent, includePointers = true) {\n    if (val == null) { val = {}; }\n    const ctx = {\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    if (this.preEncode != null) {\n      this.preEncode.call(val);\n    }\n\n    let size = 0;\n    for (let key in this.fields) {\n      const type = this.fields[key];\n      if (type.size != null) {\n        size += type.size(val[key], ctx);\n      }\n    }\n\n    if (includePointers) {\n      size += ctx.pointerSize;\n    }\n\n    return size;\n  }\n\n  encode(stream, val, parent) {\n    let type;\n    if (this.preEncode != null) {\n      this.preEncode.call(val, stream);\n    }\n\n    const ctx = {\n      pointers: [],\n      startOffset: stream.pos,\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    ctx.pointerOffset = stream.pos + this.size(val, ctx, false);\n\n    for (let key in this.fields) {\n      type = this.fields[key];\n      if (type.encode != null) {\n        type.encode(stream, val[key], ctx);\n      }\n    }\n\n    let i = 0;\n    while (i < ctx.pointers.length) {\n      const ptr = ctx.pointers[i++];\n      ptr.type.encode(stream, ptr.val, ptr.parent);\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/Struct.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/VersionedStruct.js":
/*!*********************************************************!*\
  !*** ./node_modules/restructure/src/VersionedStruct.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VersionedStruct: () => (/* binding */ VersionedStruct)\n/* harmony export */ });\n/* harmony import */ var _Struct_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Struct.js */ \"(rsc)/./node_modules/restructure/src/Struct.js\");\n\n\nconst getPath = (object, pathArray) => {\n  return pathArray.reduce((prevObj, key) => prevObj && prevObj[key], object);\n};\n\nclass VersionedStruct extends _Struct_js__WEBPACK_IMPORTED_MODULE_0__.Struct {\n  constructor(type, versions = {}) {\n    super();\n    this.type = type;\n    this.versions = versions;\n    if (typeof type === 'string') {\n      this.versionPath = type.split('.');\n    }\n  }\n\n  decode(stream, parent, length = 0) {\n    const res = this._setup(stream, parent, length);\n\n    if (typeof this.type === 'string') {\n      res.version = getPath(parent, this.versionPath);\n    } else {\n      res.version = this.type.decode(stream);\n    }\n\n    if (this.versions.header) {\n      this._parseFields(stream, res, this.versions.header);\n    }\n\n    const fields = this.versions[res.version];\n    if ((fields == null)) {\n      throw new Error(`Unknown version ${res.version}`);\n    }\n\n    if (fields instanceof VersionedStruct) {\n      return fields.decode(stream, parent);\n    }\n\n    this._parseFields(stream, res, fields);\n\n    if (this.process != null) {\n      this.process.call(res, stream);\n    }\n    return res;\n  }\n\n  size(val, parent, includePointers = true) {\n    let key, type;\n    if (!val) {\n      throw new Error('Not a fixed size');\n    }\n\n    if (this.preEncode != null) {\n      this.preEncode.call(val);\n    }\n\n    const ctx = {\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    let size = 0;\n    if (typeof this.type !== 'string') {\n      size += this.type.size(val.version, ctx);\n    }\n\n    if (this.versions.header) {\n      for (key in this.versions.header) {\n        type = this.versions.header[key];\n        if (type.size != null) {\n          size += type.size(val[key], ctx);\n        }\n      }\n    }\n\n    const fields = this.versions[val.version];\n    if ((fields == null)) {\n      throw new Error(`Unknown version ${val.version}`);\n    }\n\n    for (key in fields) {\n      type = fields[key];\n      if (type.size != null) {\n        size += type.size(val[key], ctx);\n      }\n    }\n\n    if (includePointers) {\n      size += ctx.pointerSize;\n    }\n\n    return size;\n  }\n\n  encode(stream, val, parent) {\n    let key, type;\n    if (this.preEncode != null) {\n      this.preEncode.call(val, stream);\n    }\n\n    const ctx = {\n      pointers: [],\n      startOffset: stream.pos,\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    ctx.pointerOffset = stream.pos + this.size(val, ctx, false);\n\n    if (typeof this.type !== 'string') {\n      this.type.encode(stream, val.version);\n    }\n\n    if (this.versions.header) {\n      for (key in this.versions.header) {\n        type = this.versions.header[key];\n        if (type.encode != null) {\n          type.encode(stream, val[key], ctx);\n        }\n      }\n    }\n\n    const fields = this.versions[val.version];\n    for (key in fields) {\n      type = fields[key];\n      if (type.encode != null) {\n        type.encode(stream, val[key], ctx);\n      }\n    }\n\n    let i = 0;\n    while (i < ctx.pointers.length) {\n      const ptr = ctx.pointers[i++];\n      ptr.type.encode(stream, ptr.val, ptr.parent);\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL1ZlcnNpb25lZFN0cnVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtQzs7QUFFbkM7QUFDQTtBQUNBOztBQUVPLDhCQUE4Qiw4Q0FBTTtBQUMzQyxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx5Q0FBeUMsWUFBWTtBQUNyRDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHlDQUF5QyxZQUFZO0FBQ3JEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXHJlc3RydWN0dXJlXFxzcmNcXFZlcnNpb25lZFN0cnVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1N0cnVjdH0gZnJvbSAnLi9TdHJ1Y3QuanMnO1xuXG5jb25zdCBnZXRQYXRoID0gKG9iamVjdCwgcGF0aEFycmF5KSA9PiB7XG4gIHJldHVybiBwYXRoQXJyYXkucmVkdWNlKChwcmV2T2JqLCBrZXkpID0+IHByZXZPYmogJiYgcHJldk9ialtrZXldLCBvYmplY3QpO1xufTtcblxuZXhwb3J0IGNsYXNzIFZlcnNpb25lZFN0cnVjdCBleHRlbmRzIFN0cnVjdCB7XG4gIGNvbnN0cnVjdG9yKHR5cGUsIHZlcnNpb25zID0ge30pIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMudHlwZSA9IHR5cGU7XG4gICAgdGhpcy52ZXJzaW9ucyA9IHZlcnNpb25zO1xuICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHRoaXMudmVyc2lvblBhdGggPSB0eXBlLnNwbGl0KCcuJyk7XG4gICAgfVxuICB9XG5cbiAgZGVjb2RlKHN0cmVhbSwgcGFyZW50LCBsZW5ndGggPSAwKSB7XG4gICAgY29uc3QgcmVzID0gdGhpcy5fc2V0dXAoc3RyZWFtLCBwYXJlbnQsIGxlbmd0aCk7XG5cbiAgICBpZiAodHlwZW9mIHRoaXMudHlwZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHJlcy52ZXJzaW9uID0gZ2V0UGF0aChwYXJlbnQsIHRoaXMudmVyc2lvblBhdGgpO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXMudmVyc2lvbiA9IHRoaXMudHlwZS5kZWNvZGUoc3RyZWFtKTtcbiAgICB9XG5cbiAgICBpZiAodGhpcy52ZXJzaW9ucy5oZWFkZXIpIHtcbiAgICAgIHRoaXMuX3BhcnNlRmllbGRzKHN0cmVhbSwgcmVzLCB0aGlzLnZlcnNpb25zLmhlYWRlcik7XG4gICAgfVxuXG4gICAgY29uc3QgZmllbGRzID0gdGhpcy52ZXJzaW9uc1tyZXMudmVyc2lvbl07XG4gICAgaWYgKChmaWVsZHMgPT0gbnVsbCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVW5rbm93biB2ZXJzaW9uICR7cmVzLnZlcnNpb259YCk7XG4gICAgfVxuXG4gICAgaWYgKGZpZWxkcyBpbnN0YW5jZW9mIFZlcnNpb25lZFN0cnVjdCkge1xuICAgICAgcmV0dXJuIGZpZWxkcy5kZWNvZGUoc3RyZWFtLCBwYXJlbnQpO1xuICAgIH1cblxuICAgIHRoaXMuX3BhcnNlRmllbGRzKHN0cmVhbSwgcmVzLCBmaWVsZHMpO1xuXG4gICAgaWYgKHRoaXMucHJvY2VzcyAhPSBudWxsKSB7XG4gICAgICB0aGlzLnByb2Nlc3MuY2FsbChyZXMsIHN0cmVhbSk7XG4gICAgfVxuICAgIHJldHVybiByZXM7XG4gIH1cblxuICBzaXplKHZhbCwgcGFyZW50LCBpbmNsdWRlUG9pbnRlcnMgPSB0cnVlKSB7XG4gICAgbGV0IGtleSwgdHlwZTtcbiAgICBpZiAoIXZhbCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdOb3QgYSBmaXhlZCBzaXplJyk7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMucHJlRW5jb2RlICE9IG51bGwpIHtcbiAgICAgIHRoaXMucHJlRW5jb2RlLmNhbGwodmFsKTtcbiAgICB9XG5cbiAgICBjb25zdCBjdHggPSB7XG4gICAgICBwYXJlbnQsXG4gICAgICB2YWwsXG4gICAgICBwb2ludGVyU2l6ZTogMFxuICAgIH07XG5cbiAgICBsZXQgc2l6ZSA9IDA7XG4gICAgaWYgKHR5cGVvZiB0aGlzLnR5cGUgIT09ICdzdHJpbmcnKSB7XG4gICAgICBzaXplICs9IHRoaXMudHlwZS5zaXplKHZhbC52ZXJzaW9uLCBjdHgpO1xuICAgIH1cblxuICAgIGlmICh0aGlzLnZlcnNpb25zLmhlYWRlcikge1xuICAgICAgZm9yIChrZXkgaW4gdGhpcy52ZXJzaW9ucy5oZWFkZXIpIHtcbiAgICAgICAgdHlwZSA9IHRoaXMudmVyc2lvbnMuaGVhZGVyW2tleV07XG4gICAgICAgIGlmICh0eXBlLnNpemUgIT0gbnVsbCkge1xuICAgICAgICAgIHNpemUgKz0gdHlwZS5zaXplKHZhbFtrZXldLCBjdHgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgZmllbGRzID0gdGhpcy52ZXJzaW9uc1t2YWwudmVyc2lvbl07XG4gICAgaWYgKChmaWVsZHMgPT0gbnVsbCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVW5rbm93biB2ZXJzaW9uICR7dmFsLnZlcnNpb259YCk7XG4gICAgfVxuXG4gICAgZm9yIChrZXkgaW4gZmllbGRzKSB7XG4gICAgICB0eXBlID0gZmllbGRzW2tleV07XG4gICAgICBpZiAodHlwZS5zaXplICE9IG51bGwpIHtcbiAgICAgICAgc2l6ZSArPSB0eXBlLnNpemUodmFsW2tleV0sIGN0eCk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGluY2x1ZGVQb2ludGVycykge1xuICAgICAgc2l6ZSArPSBjdHgucG9pbnRlclNpemU7XG4gICAgfVxuXG4gICAgcmV0dXJuIHNpemU7XG4gIH1cblxuICBlbmNvZGUoc3RyZWFtLCB2YWwsIHBhcmVudCkge1xuICAgIGxldCBrZXksIHR5cGU7XG4gICAgaWYgKHRoaXMucHJlRW5jb2RlICE9IG51bGwpIHtcbiAgICAgIHRoaXMucHJlRW5jb2RlLmNhbGwodmFsLCBzdHJlYW0pO1xuICAgIH1cblxuICAgIGNvbnN0IGN0eCA9IHtcbiAgICAgIHBvaW50ZXJzOiBbXSxcbiAgICAgIHN0YXJ0T2Zmc2V0OiBzdHJlYW0ucG9zLFxuICAgICAgcGFyZW50LFxuICAgICAgdmFsLFxuICAgICAgcG9pbnRlclNpemU6IDBcbiAgICB9O1xuXG4gICAgY3R4LnBvaW50ZXJPZmZzZXQgPSBzdHJlYW0ucG9zICsgdGhpcy5zaXplKHZhbCwgY3R4LCBmYWxzZSk7XG5cbiAgICBpZiAodHlwZW9mIHRoaXMudHlwZSAhPT0gJ3N0cmluZycpIHtcbiAgICAgIHRoaXMudHlwZS5lbmNvZGUoc3RyZWFtLCB2YWwudmVyc2lvbik7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMudmVyc2lvbnMuaGVhZGVyKSB7XG4gICAgICBmb3IgKGtleSBpbiB0aGlzLnZlcnNpb25zLmhlYWRlcikge1xuICAgICAgICB0eXBlID0gdGhpcy52ZXJzaW9ucy5oZWFkZXJba2V5XTtcbiAgICAgICAgaWYgKHR5cGUuZW5jb2RlICE9IG51bGwpIHtcbiAgICAgICAgICB0eXBlLmVuY29kZShzdHJlYW0sIHZhbFtrZXldLCBjdHgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgZmllbGRzID0gdGhpcy52ZXJzaW9uc1t2YWwudmVyc2lvbl07XG4gICAgZm9yIChrZXkgaW4gZmllbGRzKSB7XG4gICAgICB0eXBlID0gZmllbGRzW2tleV07XG4gICAgICBpZiAodHlwZS5lbmNvZGUgIT0gbnVsbCkge1xuICAgICAgICB0eXBlLmVuY29kZShzdHJlYW0sIHZhbFtrZXldLCBjdHgpO1xuICAgICAgfVxuICAgIH1cblxuICAgIGxldCBpID0gMDtcbiAgICB3aGlsZSAoaSA8IGN0eC5wb2ludGVycy5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IHB0ciA9IGN0eC5wb2ludGVyc1tpKytdO1xuICAgICAgcHRyLnR5cGUuZW5jb2RlKHN0cmVhbSwgcHRyLnZhbCwgcHRyLnBhcmVudCk7XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/VersionedStruct.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/restructure/src/utils.js":
/*!***********************************************!*\
  !*** ./node_modules/restructure/src/utils.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyDescriptor: () => (/* binding */ PropertyDescriptor),\n/* harmony export */   resolveLength: () => (/* binding */ resolveLength)\n/* harmony export */ });\n/* harmony import */ var _Number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Number.js */ \"(rsc)/./node_modules/restructure/src/Number.js\");\n\n\nfunction resolveLength(length, stream, parent) {\n  let res;\n  if (typeof length === 'number') {\n    res = length;\n\n  } else if (typeof length === 'function') {\n    res = length.call(parent, parent);\n\n  } else if (parent && (typeof length === 'string')) {\n    res = parent[length];\n\n  } else if (stream && length instanceof _Number_js__WEBPACK_IMPORTED_MODULE_0__.Number) {\n    res = length.decode(stream);\n  }\n\n  if (isNaN(res)) {\n    throw new Error('Not a fixed size');\n  }\n\n  return res;\n};\n\nclass PropertyDescriptor {\n  constructor(opts = {}) {\n    this.enumerable = true;\n    this.configurable = true;\n\n    for (let key in opts) {\n      const val = opts[key];\n      this[key] = val;\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzdHJ1Y3R1cmUvc3JjL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qzs7QUFFdkM7QUFDUDtBQUNBO0FBQ0E7O0FBRUEsSUFBSTtBQUNKOztBQUVBLElBQUk7QUFDSjs7QUFFQSxJQUFJLHFDQUFxQyw4Q0FBTztBQUNoRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVPO0FBQ1AsdUJBQXVCO0FBQ3ZCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxub2RlX21vZHVsZXNcXHJlc3RydWN0dXJlXFxzcmNcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7TnVtYmVyIGFzIE51bWJlclR9IGZyb20gJy4vTnVtYmVyLmpzJztcblxuZXhwb3J0IGZ1bmN0aW9uIHJlc29sdmVMZW5ndGgobGVuZ3RoLCBzdHJlYW0sIHBhcmVudCkge1xuICBsZXQgcmVzO1xuICBpZiAodHlwZW9mIGxlbmd0aCA9PT0gJ251bWJlcicpIHtcbiAgICByZXMgPSBsZW5ndGg7XG5cbiAgfSBlbHNlIGlmICh0eXBlb2YgbGVuZ3RoID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmVzID0gbGVuZ3RoLmNhbGwocGFyZW50LCBwYXJlbnQpO1xuXG4gIH0gZWxzZSBpZiAocGFyZW50ICYmICh0eXBlb2YgbGVuZ3RoID09PSAnc3RyaW5nJykpIHtcbiAgICByZXMgPSBwYXJlbnRbbGVuZ3RoXTtcblxuICB9IGVsc2UgaWYgKHN0cmVhbSAmJiBsZW5ndGggaW5zdGFuY2VvZiBOdW1iZXJUKSB7XG4gICAgcmVzID0gbGVuZ3RoLmRlY29kZShzdHJlYW0pO1xuICB9XG5cbiAgaWYgKGlzTmFOKHJlcykpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vdCBhIGZpeGVkIHNpemUnKTtcbiAgfVxuXG4gIHJldHVybiByZXM7XG59O1xuXG5leHBvcnQgY2xhc3MgUHJvcGVydHlEZXNjcmlwdG9yIHtcbiAgY29uc3RydWN0b3Iob3B0cyA9IHt9KSB7XG4gICAgdGhpcy5lbnVtZXJhYmxlID0gdHJ1ZTtcbiAgICB0aGlzLmNvbmZpZ3VyYWJsZSA9IHRydWU7XG5cbiAgICBmb3IgKGxldCBrZXkgaW4gb3B0cykge1xuICAgICAgY29uc3QgdmFsID0gb3B0c1trZXldO1xuICAgICAgdGhpc1trZXldID0gdmFsO1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/restructure/src/utils.js\n");

/***/ })

};
;