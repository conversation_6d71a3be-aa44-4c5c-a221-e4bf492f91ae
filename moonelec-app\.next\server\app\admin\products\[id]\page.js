/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/products/[id]/page";
exports.ids = ["app/admin/products/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/layout.tsx */ \"(rsc)/./src/app/admin/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/[id]/page.tsx */ \"(rsc)/./src/app/admin/products/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'products',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/products/[id]/page\",\n        pathname: \"/admin/products/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/layout.tsx */ \"(rsc)/./src/app/admin/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FzdXMlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVjdHMlNUMlNUNNb29uZWxlY0FwcCU1QyU1Q21vb25lbGVjLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQStIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBc3VzXFxcXERlc2t0b3BcXFxcUHJvamVjdHNcXFxcTW9vbmVsZWNBcHBcXFxcbW9vbmVsZWMtYXBwXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/[id]/page.tsx */ \"(rsc)/./src/app/admin/products/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FzdXMlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVjdHMlNUMlNUNNb29uZWxlY0FwcCU1QyU1Q21vb25lbGVjLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcHJvZHVjdHMlNUMlNUMlNUJpZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBNkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFzdXNcXFxcRGVza3RvcFxcXFxQcm9qZWN0c1xcXFxNb29uZWxlY0FwcFxcXFxtb29uZWxlYy1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxwcm9kdWN0c1xcXFxbaWRdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ToastProvider.tsx */ \"(rsc)/./src/components/ui/ToastProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(rsc)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/CartContext.tsx */ \"(rsc)/./src/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ThemeContext.tsx */ \"(rsc)/./src/context/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/layout.tsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/admin/products/[id]/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/admin/products/[id]/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\admin\\products\\[id]\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3903e87dedb1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzOTAzZTg3ZGVkYjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/CartContext */ \"(rsc)/./src/context/CartContext.tsx\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/ThemeContext */ \"(rsc)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var _components_ui_ToastProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ToastProvider */ \"(rsc)/./src/components/ui/ToastProvider.tsx\");\n/* harmony import */ var _lib_startupTasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/startupTasks */ \"(rsc)/./src/lib/startupTasks.ts\");\n\n\n\n\n\n\n\n// Run startup tasks (only on server)\nif (true) {\n    (0,_lib_startupTasks__WEBPACK_IMPORTED_MODULE_6__.runStartupTasks)();\n}\nconst metadata = {\n    title: \"Moonelec - Distribution de Matériel Électrique\",\n    description: \"Moonelec, spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits dans les secteurs résidentiel, tertiaire et industriel.\",\n    manifest: \"/manifest.json\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300 font-sans\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_CartContext__WEBPACK_IMPORTED_MODULE_3__.CartProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ToastProvider__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/ToastProvider.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ToastProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\ToastProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\components\\ui\\ToastProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\AuthContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\AuthContext.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./src/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ CartProvider),
/* harmony export */   useCart: () => (/* binding */ useCart)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CartProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\CartContext.tsx",
"CartProvider",
);const useCart = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\CartContext.tsx",
"useCart",
);

/***/ }),

/***/ "(rsc)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/reportReminders.ts":
/*!************************************!*\
  !*** ./src/lib/reportReminders.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIncompleteReports: () => (/* binding */ checkIncompleteReports),\n/* harmony export */   scheduleReportReminders: () => (/* binding */ scheduleReportReminders)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n// Check for incomplete reports and send reminders\nasync function checkIncompleteReports() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    // Get all commercials\n    const commercials = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.commercial.findMany({\n        include: {\n            user: true\n        }\n    });\n    const results = [];\n    for (const commercial of commercials){\n        // Check if the commercial has submitted a report today\n        const report = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findFirst({\n            where: {\n                commercialId: commercial.id,\n                submittedAt: {\n                    gte: today\n                },\n                isCompleted: true\n            }\n        });\n        if (!report) {\n            // Commercial hasn't submitted a report today\n            // Check if we need to send a reminder (every 2 hours)\n            const lastReminder = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findFirst({\n                where: {\n                    commercialId: commercial.id,\n                    lastReminder: {\n                        not: null\n                    },\n                    isCompleted: false\n                },\n                orderBy: {\n                    lastReminder: 'desc'\n                }\n            });\n            const twoHoursAgo = new Date();\n            twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);\n            if (!lastReminder || lastReminder.lastReminder && lastReminder.lastReminder < twoHoursAgo) {\n                // Create or update a reminder report\n                const reportId = lastReminder ? lastReminder.id : (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const reminderReport = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.upsert({\n                    where: {\n                        id: reportId\n                    },\n                    update: {\n                        lastReminder: new Date()\n                    },\n                    create: {\n                        id: reportId,\n                        commercialId: commercial.id,\n                        need: '',\n                        visitDate: today,\n                        denomination: '',\n                        name: '',\n                        visitPurpose: '',\n                        city: '',\n                        lastReminder: new Date(),\n                        isCompleted: false\n                    }\n                });\n                // Create notifications for admins\n                const admins = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.admin.findMany();\n                for (const admin of admins){\n                    await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n                        data: {\n                            id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                            type: 'REPORT_REMINDER',\n                            message: `${commercial.user.firstname} ${commercial.user.lastname} has not submitted their daily report yet.`,\n                            adminId: admin.id,\n                            salesReportId: reminderReport.id,\n                            isRead: false\n                        }\n                    });\n                }\n                results.push({\n                    commercial,\n                    reminderReport,\n                    needsReminder: true\n                });\n            } else {\n                results.push({\n                    commercial,\n                    needsReminder: false\n                });\n            }\n        } else {\n            results.push({\n                commercial,\n                report,\n                needsReminder: false\n            });\n        }\n    }\n    return results;\n}\n// Schedule the check to run every 2 hours\nfunction scheduleReportReminders() {\n    // Only run in production to avoid multiple instances in development\n    if (false) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/reportReminders.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/startupTasks.ts":
/*!*********************************!*\
  !*** ./src/lib/startupTasks.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runStartupTasks: () => (/* binding */ runStartupTasks)\n/* harmony export */ });\n/* harmony import */ var _reportReminders__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reportReminders */ \"(rsc)/./src/lib/reportReminders.ts\");\n\n// Run startup tasks\nfunction runStartupTasks() {\n    // Schedule report reminders\n    (0,_reportReminders__WEBPACK_IMPORTED_MODULE_0__.scheduleReportReminders)();\n    console.log('Startup tasks completed');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0YXJ0dXBUYXNrcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0RDtBQUU1RCxvQkFBb0I7QUFDYixTQUFTQztJQUNkLDRCQUE0QjtJQUM1QkQseUVBQXVCQTtJQUV2QkUsUUFBUUMsR0FBRyxDQUFDO0FBQ2QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxzdGFydHVwVGFza3MudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2NoZWR1bGVSZXBvcnRSZW1pbmRlcnMgfSBmcm9tICcuL3JlcG9ydFJlbWluZGVycyc7XG5cbi8vIFJ1biBzdGFydHVwIHRhc2tzXG5leHBvcnQgZnVuY3Rpb24gcnVuU3RhcnR1cFRhc2tzKCkge1xuICAvLyBTY2hlZHVsZSByZXBvcnQgcmVtaW5kZXJzXG4gIHNjaGVkdWxlUmVwb3J0UmVtaW5kZXJzKCk7XG4gIFxuICBjb25zb2xlLmxvZygnU3RhcnR1cCB0YXNrcyBjb21wbGV0ZWQnKTtcbn1cbiJdLCJuYW1lcyI6WyJzY2hlZHVsZVJlcG9ydFJlbWluZGVycyIsInJ1blN0YXJ0dXBUYXNrcyIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/startupTasks.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/layout.tsx */ \"(ssr)/./src/app/admin/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FzdXMlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVjdHMlNUMlNUNNb29uZWxlY0FwcCU1QyU1Q21vb25lbGVjLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQStIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBc3VzXFxcXERlc2t0b3BcXFxcUHJvamVjdHNcXFxcTW9vbmVsZWNBcHBcXFxcbW9vbmVsZWMtYXBwXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW5cXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/[id]/page.tsx */ \"(ssr)/./src/app/admin/products/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FzdXMlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVjdHMlNUMlNUNNb29uZWxlY0FwcCU1QyU1Q21vb25lbGVjLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcHJvZHVjdHMlNUMlNUMlNUJpZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBNkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFzdXNcXFxcRGVza3RvcFxcXFxQcm9qZWN0c1xcXFxNb29uZWxlY0FwcFxcXFxtb29uZWxlYy1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxwcm9kdWN0c1xcXFxbaWRdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ToastProvider.tsx */ \"(ssr)/./src/components/ui/ToastProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/CartContext.tsx */ \"(ssr)/./src/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ThemeContext.tsx */ \"(ssr)/./src/context/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/layout.tsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _components_admin_AdminSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminSidebar */ \"(ssr)/./src/components/admin/AdminSidebar.tsx\");\n/* harmony import */ var _components_admin_AdminHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/AdminHeader */ \"(ssr)/./src/components/admin/AdminHeader.tsx\");\n/* harmony import */ var _components_auth_RouteGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/RouteGuard */ \"(ssr)/./src/components/auth/RouteGuard.tsx\");\n/* harmony import */ var _components_chat_ChatWindow__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/ChatWindow */ \"(ssr)/./src/components/chat/ChatWindow.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction AdminLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isAdmin } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RouteGuard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            'ADMIN'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-100 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.main, {\n                            className: \"flex-1 overflow-y-auto p-4 md:p-6\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatWindow__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/products/[id]/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/admin/products/[id]/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowLeft,FaEdit,FaExclamationTriangle,FaSpinner,FaTrash!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_auth_RouteGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/RouteGuard */ \"(ssr)/./src/components/auth/RouteGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction ProductDetailPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const productId = params.id;\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailPage.useEffect\": ()=>{\n            const fetchProduct = {\n                \"ProductDetailPage.useEffect.fetchProduct\": async ()=>{\n                    try {\n                        const response = await fetch(`/api/products/${productId}`);\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch product');\n                        }\n                        const data = await response.json();\n                        setProduct(data);\n                    } catch (err) {\n                        console.error('Error fetching product:', err);\n                        setError(err.message || 'An error occurred while fetching the product');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ProductDetailPage.useEffect.fetchProduct\"];\n            fetchProduct();\n        }\n    }[\"ProductDetailPage.useEffect\"], [\n        productId\n    ]);\n    const handleDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            const response = await fetch(`/api/products/${productId}`, {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete product');\n            }\n            router.push('/admin/products');\n        } catch (err) {\n            console.error('Error deleting product:', err);\n            setError(err.message || 'An error occurred while deleting the product');\n            setIsDeleting(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                className: \"animate-spin text-4xl text-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 dark:bg-red-900/20 p-6 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-red-600 dark:text-red-400 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaExclamationTriangle, {\n                                className: \"text-2xl mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Erreur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 dark:text-red-400 mb-4\",\n                        children: error || \"Le produit n'a pas pu être chargé\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/admin/products\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors\",\n                            children: \"Retour \\xe0 la liste des produits\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RouteGuard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            'ADMIN'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4 md:mb-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"mr-4 p-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaArrowLeft, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-800 dark:text-white\",\n                                    children: \"D\\xe9tails du produit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: `/admin/products/${productId}/edit`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaEdit, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Modifier\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center\",\n                                    onClick: ()=>setShowDeleteConfirm(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaTrash, {\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Supprimer\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 dark:text-white mb-4\",\n                                        children: \"Images du produit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 mb-4\",\n                                        children: selectedImage || product.mainImage || product.productimage.length > 0 && product.productimage[0].url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: selectedImage || product.mainImage || product.productimage[0].url,\n                                            alt: product.name,\n                                            fill: true,\n                                            style: {\n                                                objectFit: 'contain'\n                                            },\n                                            className: \"w-full h-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Aucune image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    product.productimage.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-5 gap-2\",\n                                        children: product.productimage.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 cursor-pointer ${selectedImage === image.url ? 'ring-2 ring-primary' : ''}`,\n                                                onClick: ()=>setSelectedImage(image.url),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: image.url,\n                                                    alt: image.alt || product.name,\n                                                    fill: true,\n                                                    style: {\n                                                        objectFit: 'cover'\n                                                    },\n                                                    className: \"w-full h-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, image.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t md:border-t-0 md:border-l border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-800 dark:text-white mb-2\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 mb-2\",\n                                                children: [\n                                                    \"R\\xe9f\\xe9rence: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: product.reference\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"Cat\\xe9gorie: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: product.category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 32\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            product.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"Marque: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: product.brand.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 dark:text-white mb-2\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 whitespace-pre-line\",\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    Object.keys(product.characteristics).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 dark:text-white mb-2\",\n                                                children: \"Caract\\xe9ristiques\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    className: \"grid grid-cols-1 gap-x-4 gap-y-2\",\n                                                    children: Object.entries(product.characteristics).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                    children: key\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-sm text-gray-900 dark:text-white\",\n                                                                    children: value\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 dark:text-white mb-2\",\n                                                children: \"Informations suppl\\xe9mentaires\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                    className: \"grid grid-cols-1 gap-x-4 gap-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                    children: \"Date de cr\\xe9ation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-sm text-gray-900 dark:text-white\",\n                                                                    children: new Date(product.createdAt).toLocaleDateString('fr-FR')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                    children: \"Derni\\xe8re mise \\xe0 jour\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-sm text-gray-900 dark:text-white\",\n                                                                    children: new Date(product.updatedAt).toLocaleDateString('fr-FR')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                showDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-800 dark:text-white mb-4\",\n                                children: \"Confirmer la suppression\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                                children: [\n                                    \"\\xcates-vous s\\xfbr de vouloir supprimer le produit \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 63\n                                    }, this),\n                                    \" ? Cette action est irr\\xe9versible.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\",\n                                        onClick: ()=>setShowDeleteConfirm(false),\n                                        disabled: isDeleting,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center\",\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        children: isDeleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSpinner, {\n                                                    className: \"animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Suppression...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaEdit_FaExclamationTriangle_FaSpinner_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaTrash, {\n                                                    className: \"mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Supprimer\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/products/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/AdminHeader.tsx":
/*!**********************************************!*\
  !*** ./src/components/admin/AdminHeader.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaBell,FaCog,FaMoon,FaSearch,FaSignOutAlt,FaSun,FaUserCircle!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/ThemeContext */ \"(ssr)/./src/context/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AdminHeader() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { actualTheme, toggleTheme } = (0,_context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNotificationsOpen, setIsNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const profileRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const notificationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Notifications simulées\n    const notifications = [\n        {\n            id: 1,\n            message: 'Nouvelle commande #12345',\n            time: 'Il y a 5 minutes',\n            isRead: false\n        },\n        {\n            id: 2,\n            message: 'Nouveau client inscrit',\n            time: 'Il y a 30 minutes',\n            isRead: false\n        },\n        {\n            id: 3,\n            message: 'Mise à jour du stock terminée',\n            time: 'Il y a 2 heures',\n            isRead: true\n        }\n    ];\n    // Gérer le clic en dehors des menus déroulants\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminHeader.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AdminHeader.useEffect.handleClickOutside\": (event)=>{\n                    if (profileRef.current && !profileRef.current.contains(event.target)) {\n                        setIsProfileOpen(false);\n                    }\n                    if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n                        setIsNotificationsOpen(false);\n                    }\n                }\n            }[\"AdminHeader.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"AdminHeader.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"AdminHeader.useEffect\"];\n        }\n    }[\"AdminHeader.useEffect\"], []);\n    const handleLogout = async ()=>{\n        await logout();\n        router.push('/');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-4 py-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-64 md:w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                className: \"text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            className: \"block w-full pl-10 pr-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\",\n                            placeholder: \"Rechercher...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                            whileHover: {\n                                scale: 1.1\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            onClick: toggleTheme,\n                            className: \"p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                            \"aria-label\": actualTheme === 'dark' ? 'Passer en mode clair' : 'Passer en mode sombre',\n                            children: actualTheme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSun, {\n                                className: \"text-yellow-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 39\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMoon, {\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 79\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            ref: notificationsRef,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    onClick: ()=>setIsNotificationsOpen(!isNotificationsOpen),\n                                    className: \"p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBell, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        notifications.some((n)=>!n.isRead) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                    children: isNotificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 border-b border-gray-200 dark:border-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800 dark:text-white\",\n                                                    children: \"Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-96 overflow-y-auto\",\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: `p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 ${!notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-800 dark:text-white\",\n                                                                            children: notification.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                            lineNumber: 116,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                            lineNumber: 120,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                                    children: notification.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, notification.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 text-center text-gray-500 dark:text-gray-400\",\n                                                    children: \"Aucune notification\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 text-center border-t border-gray-200 dark:border-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-sm text-primary hover:underline\",\n                                                    children: \"Marquer tout comme lu\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            ref: profileRef,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                    className: \"flex items-center space-x-2 focus:outline-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden\",\n                                            children: user?.adminId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUserCircle, {\n                                                    className: \"w-full h-full text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUserCircle, {\n                                                className: \"w-full h-full text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                            children: [\n                                                user?.firstname,\n                                                \" \",\n                                                user?.lastname\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                    children: isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 border-b border-gray-200 dark:border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-800 dark:text-white\",\n                                                        children: [\n                                                            user?.firstname,\n                                                            \" \",\n                                                            user?.lastname\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: user?.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push('/admin/profile'),\n                                                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUserCircle, {\n                                                                    className: \"mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Mon profil\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push('/admin/settings'),\n                                                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {\n                                                                    className: \"mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Param\\xe8tres\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"border-t border-gray-200 dark:border-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaCog_FaMoon_FaSearch_FaSignOutAlt_FaSun_FaUserCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSignOutAlt, {\n                                                                    className: \"mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"D\\xe9connexion\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminHeader.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/AdminHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/AdminSidebar.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/AdminSidebar.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaBox,FaChartBar,FaChevronLeft,FaChevronRight,FaClipboardList,FaCog,FaCopyright,FaShoppingCart,FaTachometerAlt,FaTags,FaUserTie,FaUsers!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AdminSidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const menuItems = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTachometerAlt, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, this),\n            label: 'Tableau de bord',\n            href: '/admin/dashboard'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUsers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, this),\n            label: 'Clients',\n            href: '/admin/clients'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUserTie, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, this),\n            label: 'Commerciaux',\n            href: '/admin/commercials'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaShoppingCart, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 31,\n                columnNumber: 13\n            }, this),\n            label: 'Devis',\n            href: '/admin/quotes'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBox, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, this),\n            label: 'Produits',\n            href: '/admin/products'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTags, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 33,\n                columnNumber: 13\n            }, this),\n            label: 'Catégories',\n            href: '/admin/categories'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCopyright, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, this),\n            label: 'Marques',\n            href: '/admin/brands'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaClipboardList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 35,\n                columnNumber: 13\n            }, this),\n            label: 'Rapports',\n            href: '/admin/reports'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChartBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            label: 'Statistiques',\n            href: '/admin/statistics'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this),\n            label: 'Paramètres',\n            href: '/admin/settings'\n        }\n    ];\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        className: `bg-[#0a1f2f] text-white ${isCollapsed ? 'w-20' : 'w-64'} h-screen flex flex-col transition-all duration-300 shadow-lg z-20`,\n        animate: {\n            width: isCollapsed ? 80 : 256\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 flex justify-between items-center border-b border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                src: \"/images/logo/logo-moonelec.png\",\n                                alt: \"Moonelec Logo\",\n                                width: 150,\n                                height: 40,\n                                className: \"h-10 w-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/images/logo/logo-icon.png\",\n                            alt: \"Moonelec Icon\",\n                            width: 32,\n                            height: 32,\n                            className: \"h-8 w-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded-full hover:bg-gray-700\",\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronRight, {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBox_FaChartBar_FaChevronLeft_FaChevronRight_FaClipboardList_FaCog_FaCopyright_FaShoppingCart_FaTachometerAlt_FaTags_FaUserTie_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronLeft, {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 57\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 overflow-y-auto py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2 px-2\",\n                    children: menuItems.map((item, index)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.li, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.3,\n                                delay: index * 0.05\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: `flex items-center p-3 rounded-lg transition-all ${isActive ? 'bg-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                        children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                            initial: {\n                                                opacity: 0,\n                                                width: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                width: 'auto'\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                width: 0\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"ml-3 whitespace-nowrap overflow-hidden\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 17\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-700 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                    children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Moonelec\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/AdminSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/RouteGuard.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/RouteGuard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RouteGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_shared_LoadingAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/LoadingAnimation */ \"(ssr)/./src/components/shared/LoadingAnimation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction RouteGuard({ children, allowedRoles = [] }) {\n    const { isAuthenticated, isLoading, user, redirectToLogin } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RouteGuard.useEffect\": ()=>{\n            // Si le chargement est terminé et que l'utilisateur n'est pas authentifié\n            if (!isLoading && !isAuthenticated) {\n                // Stocker le chemin actuel pour rediriger après la connexion\n                sessionStorage.setItem('redirectAfterLogin', pathname);\n                redirectToLogin();\n            }\n        }\n    }[\"RouteGuard.useEffect\"], [\n        isLoading,\n        isAuthenticated,\n        pathname,\n        redirectToLogin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RouteGuard.useEffect\": ()=>{\n            // Si l'utilisateur est authentifié mais n'a pas le rôle requis\n            if (isAuthenticated && allowedRoles.length > 0 && user?.role && !allowedRoles.includes(user.role)) {\n                // Rediriger vers la page d'accueil ou une page d'erreur\n                window.location.href = '/unauthorized';\n            }\n        }\n    }[\"RouteGuard.useEffect\"], [\n        isAuthenticated,\n        allowedRoles,\n        user\n    ]);\n    // Afficher un écran de chargement pendant la vérification\n    if (isLoading || !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_LoadingAnimation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            isLoading: true,\n            onLoadingComplete: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n            lineNumber: 45,\n            columnNumber: 12\n        }, this);\n    }\n    // Si l'utilisateur est authentifié et a le rôle requis (ou aucun rôle n'est requis)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/RouteGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatWindow.tsx":
/*!********************************************!*\
  !*** ./src/components/chat/ChatWindow.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ChatWindow() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('conversations');\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableUsers, setAvailableUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConversation, setSelectedConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            messagesEndRef.current?.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    // Load conversations and users on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            if (session?.user && isOpen) {\n                loadConversations();\n                loadAvailableUsers();\n            }\n        }\n    }[\"ChatWindow.useEffect\"], [\n        session,\n        isOpen\n    ]);\n    // Load messages when conversation is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            if (selectedConversation) {\n                loadMessages(selectedConversation);\n            }\n        }\n    }[\"ChatWindow.useEffect\"], [\n        selectedConversation\n    ]);\n    const loadConversations = async ()=>{\n        try {\n            const response = await fetch('/api/chat/conversations');\n            if (response.ok) {\n                const data = await response.json();\n                setConversations(data.conversations);\n            }\n        } catch (error) {\n            console.error('Error loading conversations:', error);\n        }\n    };\n    const loadAvailableUsers = async ()=>{\n        try {\n            const response = await fetch('/api/chat/users');\n            if (response.ok) {\n                const data = await response.json();\n                setAvailableUsers(data.users);\n            }\n        } catch (error) {\n            console.error('Error loading users:', error);\n        }\n    };\n    const loadMessages = async (conversationId)=>{\n        try {\n            setLoading(true);\n            const response = await fetch(`/api/chat/messages?conversationId=${conversationId}`);\n            if (response.ok) {\n                const data = await response.json();\n                setMessages(data.messages);\n            }\n        } catch (error) {\n            console.error('Error loading messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedConversation) return;\n        try {\n            const response = await fetch('/api/chat/messages', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    conversationId: selectedConversation,\n                    content: newMessage,\n                    messageType: 'TEXT'\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setMessages((prev)=>[\n                        ...prev,\n                        data.message\n                    ]);\n                setNewMessage('');\n                loadConversations(); // Refresh conversations to update last message\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n        }\n    };\n    const handleFileUpload = async (event)=>{\n        const file = event.target.files?.[0];\n        if (!file || !selectedConversation) return;\n        setIsUploading(true);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('conversationId', selectedConversation);\n            const response = await fetch('/api/chat/upload', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                // Send a file message\n                const messageResponse = await fetch('/api/chat/messages', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        conversationId: selectedConversation,\n                        content: `📎 ${data.file.fileName}`,\n                        messageType: file.type.startsWith('image/') ? 'IMAGE' : 'FILE',\n                        fileUrl: data.file.fileUrl,\n                        fileName: data.file.fileName\n                    })\n                });\n                if (messageResponse.ok) {\n                    const messageData = await messageResponse.json();\n                    setMessages((prev)=>[\n                            ...prev,\n                            messageData.message\n                        ]);\n                    loadConversations();\n                }\n            } else {\n                const errorData = await response.json();\n                alert(`Erreur d'upload: ${errorData.error}`);\n            }\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            alert('Erreur lors de l\\'upload du fichier');\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        }\n    };\n    const triggerFileUpload = ()=>{\n        fileInputRef.current?.click();\n    };\n    const startConversation = async (userId)=>{\n        try {\n            const response = await fetch('/api/chat/conversations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    participantId: userId\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setSelectedConversation(data.conversation.id);\n                setActiveTab('conversations');\n                loadConversations();\n            }\n        } catch (error) {\n            console.error('Error starting conversation:', error);\n        }\n    };\n    const formatTime = (dateString)=>{\n        return new Date(dateString).toLocaleTimeString('fr-FR', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const getConversationName = (conversation)=>{\n        if (session?.user?.role === 'ADMIN') {\n            return `${conversation.commercial.user.firstname} ${conversation.commercial.user.lastname}`;\n        } else {\n            return `${conversation.admin.user.firstname} ${conversation.admin.user.lastname}`;\n        }\n    };\n    if (!session?.user || session.user.role !== 'ADMIN' && session.user.role !== 'COMMERCIAL') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-2xl\",\n                    children: \"\\uD83D\\uDCAC\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-24 right-6 w-96 h-[500px] bg-white border border-gray-200 rounded-lg shadow-xl z-50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold\",\n                                children: \"\\uD83D\\uDCAC Chat Moonelec\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"text-white hover:text-gray-200 text-xl\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('conversations'),\n                                className: `flex-1 p-3 text-sm font-medium ${activeTab === 'conversations' ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-800'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline mr-2\",\n                                        children: \"\\uD83D\\uDCAC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Conversations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: `flex-1 p-3 text-sm font-medium ${activeTab === 'users' ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-800'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline mr-2\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Utilisateurs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: [\n                            activeTab === 'conversations' && !selectedConversation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 h-full overflow-y-auto\",\n                                children: conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-500 mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-5xl mb-4 text-gray-300\",\n                                            children: \"\\uD83D\\uDCAC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Aucune conversation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: \"Commencez une nouvelle conversation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setSelectedConversation(conversation.id),\n                                            className: \"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: getConversationName(conversation)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 25\n                                                }, this),\n                                                conversation.lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 mt-1 truncate\",\n                                                    children: conversation.lastMessage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 27\n                                                }, this),\n                                                conversation.lastMessageAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                    children: formatTime(conversation.lastMessageAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, conversation.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 h-full overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: availableUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>startConversation(user.id),\n                                            className: \"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: user.role\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: user.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this),\n                            selectedConversation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 p-4 overflow-y-auto\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center text-gray-500\",\n                                            children: \"Chargement...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex ${message.senderId === session?.user?.id ? 'justify-end' : 'justify-start'}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `max-w-xs px-3 py-2 rounded-lg text-sm ${message.senderId === session?.user?.id ? 'bg-blue-600 text-white' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white'}`,\n                                                            children: [\n                                                                message.messageType === 'IMAGE' && message.fileUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: message.fileUrl,\n                                                                            alt: message.fileName || 'Image',\n                                                                            className: \"max-w-full h-auto rounded mb-2\",\n                                                                            style: {\n                                                                                maxHeight: '200px'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: message.content\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 31\n                                                                }, this) : message.messageType === 'FILE' && message.fileUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: message.fileUrl,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: `inline-flex items-center space-x-2 p-2 rounded border ${message.senderId === session?.user?.id ? 'border-blue-300 bg-blue-500' : 'border-gray-300 bg-gray-50 dark:bg-gray-600 dark:border-gray-500'}`,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"\\uD83D\\uDCCE\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                                    lineNumber: 376,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"underline\",\n                                                                                    children: message.fileName || 'Fichier'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                                    lineNumber: 377,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-2\",\n                                                                            children: message.content\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `text-xs mt-1 ${message.senderId === session?.user?.id ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`,\n                                                                    children: formatTime(message.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, message.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 25\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border-t\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    ref: fileInputRef,\n                                                    onChange: handleFileUpload,\n                                                    className: \"hidden\",\n                                                    accept: \"image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt,.csv\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: triggerFileUpload,\n                                                    disabled: isUploading,\n                                                    className: \"bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors\",\n                                                    title: \"Joindre un fichier\",\n                                                    children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"⏳\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"\\uD83D\\uDCCE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                                    placeholder: \"Tapez votre message...\",\n                                                    className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendMessage,\n                                                    disabled: !newMessage.trim(),\n                                                    className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\chat\\\\ChatWindow.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatWindow.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shared/LoadingAnimation.tsx":
/*!****************************************************!*\
  !*** ./src/components/shared/LoadingAnimation.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LoadingAnimation({ isLoading = true, onLoadingComplete }) {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(isLoading);\n    const [fadeOut, setFadeOut] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"LoadingAnimation.useEffect\": ()=>{\n            if (isLoading) {\n                // Réduire le délai initial à 300ms\n                const timer = setTimeout({\n                    \"LoadingAnimation.useEffect.timer\": ()=>{\n                        setFadeOut(true);\n                        // Attendre la fin de l'animation de fade out avant de cacher complètement\n                        const hideTimer = setTimeout({\n                            \"LoadingAnimation.useEffect.timer.hideTimer\": ()=>{\n                                setLoading(false);\n                                if (onLoadingComplete) onLoadingComplete();\n                            }\n                        }[\"LoadingAnimation.useEffect.timer.hideTimer\"], 300); // Durée de l'animation de fade out réduite\n                        return ({\n                            \"LoadingAnimation.useEffect.timer\": ()=>clearTimeout(hideTimer)\n                        })[\"LoadingAnimation.useEffect.timer\"];\n                    }\n                }[\"LoadingAnimation.useEffect.timer\"], 300); // Délai initial réduit\n                return ({\n                    \"LoadingAnimation.useEffect\": ()=>clearTimeout(timer)\n                })[\"LoadingAnimation.useEffect\"];\n            } else {\n                // Si isLoading est false dès le départ, cacher immédiatement\n                setLoading(false);\n            }\n        }\n    }[\"LoadingAnimation.useEffect\"], [\n        isLoading,\n        onLoadingComplete\n    ]);\n    if (!loading) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-49aeff7aba30e605\" + \" \" + `fixed inset-0 z-50 flex items-center justify-center bg-[#1a1a1a] transition-opacity duration-500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"49aeff7aba30e605\",\n                children: \".loading-svg #ISpzKj7McrRg{opacity:0;-webkit-animation:opacitypath 1s ease-in-out forwards;-moz-animation:opacitypath 1s ease-in-out forwards;-o-animation:opacitypath 1s ease-in-out forwards;animation:opacitypath 1s ease-in-out forwards}.loading-svg #IMb5qOBkYi8y2{opacity:0;-webkit-animation:opacitypath2 1.2s ease-in-out forwards;-moz-animation:opacitypath2 1.2s ease-in-out forwards;-o-animation:opacitypath2 1.2s ease-in-out forwards;animation:opacitypath2 1.2s ease-in-out forwards}.loading-svg #IMb5qOBkYi8y{-webkit-filter:drop-shadow(3px 3px 2px rgba(255,255,255,.7));filter:drop-shadow(3px 3px 2px rgba(255,255,255,.7));-webkit-animation:animationshadow 2s ease-in-out infinite;-moz-animation:animationshadow 2s ease-in-out infinite;-o-animation:animationshadow 2s ease-in-out infinite;animation:animationshadow 2s ease-in-out infinite}.loading-svg #eGByPCsADLe4{-webkit-transform-origin:center;-moz-transform-origin:center;-ms-transform-origin:center;-o-transform-origin:center;transform-origin:center;-webkit-animation:movementAnimation 1s ease-in-out forwards;-moz-animation:movementAnimation 1s ease-in-out forwards;-o-animation:movementAnimation 1s ease-in-out forwards;animation:movementAnimation 1s ease-in-out forwards}.loading-svg #rOMyCEsDLSe1{stroke-dasharray:300;stroke-dashoffset:300;-webkit-animation:animationpath 1s ease-in-out forwards;-moz-animation:animationpath 1s ease-in-out forwards;-o-animation:animationpath 1s ease-in-out forwards;animation:animationpath 1s ease-in-out forwards}@-webkit-keyframes animationpath{0%{stroke-dashoffset:300}33%{stroke-dashoffset:225}66%{stroke-dashoffset:182}99%{stroke-dashoffset:120}100%{stroke-dashoffset:99}}@-moz-keyframes animationpath{0%{stroke-dashoffset:300}33%{stroke-dashoffset:225}66%{stroke-dashoffset:182}99%{stroke-dashoffset:120}100%{stroke-dashoffset:99}}@-o-keyframes animationpath{0%{stroke-dashoffset:300}33%{stroke-dashoffset:225}66%{stroke-dashoffset:182}99%{stroke-dashoffset:120}100%{stroke-dashoffset:99}}@keyframes animationpath{0%{stroke-dashoffset:300}33%{stroke-dashoffset:225}66%{stroke-dashoffset:182}99%{stroke-dashoffset:120}100%{stroke-dashoffset:99}}@-webkit-keyframes opacitypath{80%{opacity:0}100%{opacity:1}}@-moz-keyframes opacitypath{80%{opacity:0}100%{opacity:1}}@-o-keyframes opacitypath{80%{opacity:0}100%{opacity:1}}@keyframes opacitypath{80%{opacity:0}100%{opacity:1}}@-webkit-keyframes opacitypath2{80%{opacity:0}100%{opacity:1}}@-moz-keyframes opacitypath2{80%{opacity:0}100%{opacity:1}}@-o-keyframes opacitypath2{80%{opacity:0}100%{opacity:1}}@keyframes opacitypath2{80%{opacity:0}100%{opacity:1}}@-webkit-keyframes movementAnimation{0%{-webkit-transform:translate(-115px,58px);transform:translate(-115px,58px)}33.33%{-webkit-transform:translate(-65px,5px);transform:translate(-65px,5px)}66.66%{-webkit-transform:translate(-34px,31px);transform:translate(-34px,31px)}99.99%{-webkit-transform:translate(25px,-23px);transform:translate(25px,-23px)}100%{-webkit-transform:translate(25px,-23px);transform:translate(25px,-23px)}}@-moz-keyframes movementAnimation{0%{-moz-transform:translate(-115px,58px);transform:translate(-115px,58px)}33.33%{-moz-transform:translate(-65px,5px);transform:translate(-65px,5px)}66.66%{-moz-transform:translate(-34px,31px);transform:translate(-34px,31px)}99.99%{-moz-transform:translate(25px,-23px);transform:translate(25px,-23px)}100%{-moz-transform:translate(25px,-23px);transform:translate(25px,-23px)}}@-o-keyframes movementAnimation{0%{-o-transform:translate(-115px,58px);transform:translate(-115px,58px)}33.33%{-o-transform:translate(-65px,5px);transform:translate(-65px,5px)}66.66%{-o-transform:translate(-34px,31px);transform:translate(-34px,31px)}99.99%{-o-transform:translate(25px,-23px);transform:translate(25px,-23px)}100%{-o-transform:translate(25px,-23px);transform:translate(25px,-23px)}}@keyframes movementAnimation{0%{-webkit-transform:translate(-115px,58px);-moz-transform:translate(-115px,58px);-o-transform:translate(-115px,58px);transform:translate(-115px,58px)}33.33%{-webkit-transform:translate(-65px,5px);-moz-transform:translate(-65px,5px);-o-transform:translate(-65px,5px);transform:translate(-65px,5px)}66.66%{-webkit-transform:translate(-34px,31px);-moz-transform:translate(-34px,31px);-o-transform:translate(-34px,31px);transform:translate(-34px,31px)}99.99%{-webkit-transform:translate(25px,-23px);-moz-transform:translate(25px,-23px);-o-transform:translate(25px,-23px);transform:translate(25px,-23px)}100%{-webkit-transform:translate(25px,-23px);-moz-transform:translate(25px,-23px);-o-transform:translate(25px,-23px);transform:translate(25px,-23px)}}@-webkit-keyframes animationshadow{0%{-webkit-filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7));filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}50%{-webkit-filter:drop-shadow(4px -8px 14px rgba(255,255,255,.7));filter:drop-shadow(4px -8px 14px rgba(255,255,255,.7))}100%{-webkit-filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7));filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}}@-moz-keyframes animationshadow{0%{filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}50%{filter:drop-shadow(4px -8px 14px rgba(255,255,255,.7))}100%{filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}}@-o-keyframes animationshadow{0%{filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}50%{filter:drop-shadow(4px -8px 14px rgba(255,255,255,.7))}100%{filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}}@keyframes animationshadow{0%{-webkit-filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7));filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}50%{-webkit-filter:drop-shadow(4px -8px 14px rgba(255,255,255,.7));filter:drop-shadow(4px -8px 14px rgba(255,255,255,.7))}100%{-webkit-filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7));filter:drop-shadow(-10px 0px 10px rgba(255,255,255,.7))}}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                id: \"eGByPCsADLe1\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n                viewBox: \"0 0 264.58333 264.58333\",\n                width: \"120\",\n                height: \"120\",\n                className: \"jsx-49aeff7aba30e605\" + \" \" + \"loading-svg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        id: \"ISpzKj7McrRg\",\n                        d: \"M122.48,78.945c-19.782763,0-38.755276,7.858672-52.743802,21.847198s-21.847198,32.961039-21.847198,52.743802s7.858672,38.755276,21.847198,52.743802s32.961039,21.847198,52.743802,21.847198s38.755276-7.858672,52.743802-21.847198s21.847198-32.961039,21.847198-52.743802c0-41.195472-33.395528-74.591-74.591-74.591Zm-14.202,4.7329c-20.059262,13.206865-32.143233,35.606447-32.165,59.623c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c4.775729-.058974,9.533518-.596562,14.202-1.6047-11.659545,7.704127-25.322088,11.820586-39.297,11.84-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.085181-33.925568,24.011087-63.115508,57.26-69.858v-.0003Z\",\n                        transform: \"translate(25.238-21.244)\",\n                        fill: \"#006db7\",\n                        className: \"jsx-49aeff7aba30e605\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        id: \"IMb5qOBkYi8y2\",\n                        className: \"jsx-49aeff7aba30e605\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            id: \"IMb5qOBkYi8y\",\n                            d: \"M108.28,83.678c-33.248913,6.742492-57.174819,35.932432-57.26,69.858c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c13.974912-.019414,27.637455-4.135873,39.297-11.84-4.668482,1.008138-9.426271,1.545726-14.202,1.6047-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.021767-24.016553,12.105738-46.416135,32.165-59.623v.0003Z\",\n                            transform: \"translate(25.238-21.244)\",\n                            fill: \"#fff\",\n                            className: \"jsx-49aeff7aba30e605\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        id: \"eGByPCsADLe4\",\n                        d: \"M131.51,121l40.383.30632.2758,43.221\",\n                        transform: \"translate(-122.494724 62.610263)\",\n                        fill: \"none\",\n                        stroke: \"#ed1c24\",\n                        strokeWidth: \"24.133\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        className: \"jsx-49aeff7aba30e605\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        id: \"rOMyCEsDLSe1\",\n                        d: \"M29.098,200.61l53.006-53.126l30.765,31.282l58.655-57.504\",\n                        transform: \"translate(25.238-21.244)\",\n                        fill: \"none\",\n                        stroke: \"#ed1c24\",\n                        strokeWidth: \"24.133\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeDasharray: \"480\",\n                        className: \"jsx-49aeff7aba30e605\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n                lineNumber: 141,\n                columnNumber: 11\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\shared\\\\LoadingAnimation.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shared/LoadingAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ToastProvider.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ToastProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n            duration: 3000,\n            style: {\n                background: '#363636',\n                color: '#fff'\n            },\n            success: {\n                style: {\n                    background: '#22c55e'\n                }\n            },\n            error: {\n                style: {\n                    background: '#ef4444'\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Ub2FzdFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUUwQztBQUUzQixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0Qsb0RBQU9BO1FBQ05FLFVBQVM7UUFDVEMsY0FBYztZQUNaQyxVQUFVO1lBQ1ZDLE9BQU87Z0JBQ0xDLFlBQVk7Z0JBQ1pDLE9BQU87WUFDVDtZQUNBQyxTQUFTO2dCQUNQSCxPQUFPO29CQUNMQyxZQUFZO2dCQUNkO1lBQ0Y7WUFDQUcsT0FBTztnQkFDTEosT0FBTztvQkFDTEMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7Ozs7OztBQUdOIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxUb2FzdFByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUb2FzdFByb3ZpZGVyKCkge1xuICByZXR1cm4gKFxuICAgIDxUb2FzdGVyXG4gICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgZHVyYXRpb246IDMwMDAsXG4gICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJyMzNjM2MzYnLFxuICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgIH0sXG4gICAgICAgIHN1Y2Nlc3M6IHtcbiAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJyMyMmM1NWUnLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZWY0NDQ0JyxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfX1cbiAgICAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRvYXN0ZXIiLCJUb2FzdFByb3ZpZGVyIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwic3VjY2VzcyIsImVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dC9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtEO0FBR25DLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUEyQjtJQUN4RSxxQkFBTyw4REFBQ0YsNERBQWVBO2tCQUFFRTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGNvbnRleHRcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiA8U2Vzc2lvblByb3ZpZGVyPntjaGlsZHJlbn08L1Nlc3Npb25Qcm92aWRlcj47XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./src/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CartProvider({ children }) {\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCartOpen, setIsCartOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Charger le panier depuis le localStorage au montage du composant\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            const storedCart = localStorage.getItem('cart');\n            const storedNotes = localStorage.getItem('cartNotes');\n            if (storedCart) {\n                try {\n                    setItems(JSON.parse(storedCart));\n                } catch (error) {\n                    console.error('Error parsing cart from localStorage:', error);\n                    setItems([]);\n                }\n            }\n            if (storedNotes) {\n                setNotes(storedNotes);\n            }\n            setIsInitialized(true);\n        }\n    }[\"CartProvider.useEffect\"], []);\n    // Sauvegarder le panier dans le localStorage à chaque modification\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            if (isInitialized) {\n                localStorage.setItem('cart', JSON.stringify(items));\n                localStorage.setItem('cartNotes', notes);\n            }\n        }\n    }[\"CartProvider.useEffect\"], [\n        items,\n        notes,\n        isInitialized\n    ]);\n    // Ajouter un produit au panier\n    const addItem = (product, quantity)=>{\n        setItems((prevItems)=>{\n            // Vérifier si le produit est déjà dans le panier\n            const existingItemIndex = prevItems.findIndex((item)=>item.productId === product.id);\n            if (existingItemIndex !== -1) {\n                // Mettre à jour la quantité si le produit existe déjà\n                const updatedItems = [\n                    ...prevItems\n                ];\n                updatedItems[existingItemIndex].quantity += quantity;\n                return updatedItems;\n            } else {\n                // Ajouter un nouveau produit au panier\n                return [\n                    ...prevItems,\n                    {\n                        id: `${product.id}_${Date.now()}`,\n                        productId: product.id,\n                        name: product.name,\n                        reference: product.reference,\n                        description: product.description,\n                        image: product.mainImage,\n                        quantity,\n                        category: product.category,\n                        brand: product.brand\n                    }\n                ];\n            }\n        });\n    };\n    // Mettre à jour la quantité d'un produit dans le panier\n    const updateItemQuantity = (itemId, quantity)=>{\n        if (quantity <= 0) {\n            removeItem(itemId);\n            return;\n        }\n        setItems((prevItems)=>prevItems.map((item)=>item.id === itemId ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    // Supprimer un produit du panier\n    const removeItem = (itemId)=>{\n        setItems((prevItems)=>prevItems.filter((item)=>item.id !== itemId));\n    };\n    // Vider le panier\n    const clearCart = ()=>{\n        setItems([]);\n        setNotes('');\n    };\n    // Nombre total d'articles dans le panier\n    const itemCount = items.reduce((total, item)=>total + item.quantity, 0);\n    // Fonctions pour gérer l'ouverture et la fermeture du panneau coulissant\n    const openCart = ()=>setIsCartOpen(true);\n    const closeCart = ()=>setIsCartOpen(false);\n    const toggleCart = ()=>setIsCartOpen((prev)=>!prev);\n    // Modifier la fonction addItem pour ouvrir automatiquement le panneau\n    const handleAddItem = (product, quantity)=>{\n        addItem(product, quantity);\n        openCart(); // Ouvrir le panneau lorsqu'un produit est ajouté\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            items,\n            notes,\n            addItem: handleAddItem,\n            updateItemQuantity,\n            removeItem,\n            clearCart,\n            setNotes,\n            itemCount,\n            isCartOpen,\n            openCart,\n            closeCart,\n            toggleCart\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\CartContext.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\nfunction useCart() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [actualTheme, setActualTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setTheme(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update actual theme based on theme setting and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const updateActualTheme = {\n                \"ThemeProvider.useEffect.updateActualTheme\": ()=>{\n                    let newActualTheme;\n                    if (theme === 'system') {\n                        newActualTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    } else {\n                        newActualTheme = theme;\n                    }\n                    setActualTheme(newActualTheme);\n                    // Update document class for Tailwind dark mode\n                    const root = document.documentElement;\n                    if (newActualTheme === 'dark') {\n                        root.classList.add('dark');\n                    } else {\n                        root.classList.remove('dark');\n                    }\n                    // Update data attribute for custom CSS targeting\n                    root.setAttribute('data-theme', newActualTheme);\n                }\n            }[\"ThemeProvider.useEffect.updateActualTheme\"];\n            updateActualTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateActualTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const handleSetTheme = (newTheme)=>{\n        setTheme(newTheme);\n        if (mounted) {\n            localStorage.setItem('theme', newTheme);\n        }\n    };\n    const toggleTheme = ()=>{\n        const newTheme = actualTheme === 'light' ? 'dark' : 'light';\n        handleSetTheme(newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            actualTheme,\n            setTheme: handleSetTheme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\ThemeContext.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nfunction useAuth() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const isAuthenticated = status === 'authenticated';\n    const isLoading = status === 'loading';\n    const user = session?.user;\n    const isClient = isAuthenticated && user?.role === 'CLIENT';\n    const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';\n    const isAdmin = isAuthenticated && user?.role === 'ADMIN';\n    const login = async (username, password)=>{\n        const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signIn)('credentials', {\n            username,\n            password,\n            redirect: false\n        });\n        return result;\n    };\n    const logout = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signOut)({\n            redirect: false\n        });\n        router.push('/');\n    };\n    const redirectToLogin = ()=>{\n        router.push('/auth/signin');\n    };\n    const redirectToDashboard = ()=>{\n        if (isAdmin) {\n            router.push('/admin/quotes');\n        } else if (isCommercial) {\n            router.push('/commercial/quotes');\n        } else if (isClient) {\n            router.push('/account/quotes');\n        } else {\n            router.push('/');\n        }\n    };\n    return {\n        session,\n        status,\n        user,\n        isAuthenticated,\n        isLoading,\n        isClient,\n        isCommercial,\n        isAdmin,\n        login,\n        logout,\n        redirectToLogin,\n        redirectToDashboard\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-icons","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/styled-jsx","vendor-chunks/react-hot-toast","vendor-chunks/motion-utils","vendor-chunks/uuid","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&page=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();