'use client';

import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaFacebook, FaTwitter, FaLinkedin, FaInstagram, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#0a1f2f] dark:bg-gray-950 text-white pt-16 pb-8 transition-colors duration-300">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-4">
              <Image
                src="/images/logo/logo-moonelec.png"
                alt="Moonelec Logo"
                width={180}
                height={60}
                className="mr-2"
              />
            </div>
            <p className="text-gray-300 dark:text-gray-400 mb-4">
              Spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits dans les secteurs résidentiel, tertiaire et industriel.
            </p>
            <div className="flex space-x-4">
              <SocialIcon icon={<FaFacebook />} href="https://facebook.com" />
              <SocialIcon icon={<FaTwitter />} href="https://twitter.com" />
              <SocialIcon icon={<FaLinkedin />} href="https://linkedin.com" />
              <SocialIcon icon={<FaInstagram />} href="https://instagram.com" />
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-4 border-b border-blue-500 pb-2 inline-block">
              Liens Rapides
            </h3>
            <ul className="space-y-2">
              <FooterLink href="/about">À Propos</FooterLink>
              <FooterLink href="/#services">Nos Services</FooterLink>
              <FooterLink href="/#products">Nos Produits</FooterLink>
              <FooterLink href="/#brands">Nos Marques</FooterLink>
              <FooterLink href="/#contact">Contact</FooterLink>
            </ul>
          </motion.div>

          {/* Services */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-4 border-b border-blue-500 pb-2 inline-block">
              Nos Services
            </h3>
            <ul className="space-y-2">
              <FooterLink href="/services/residential">Matériel Résidentiel</FooterLink>
              <FooterLink href="/services/commercial">Matériel Tertiaire</FooterLink>
              <FooterLink href="/services/industrial">Matériel Industriel</FooterLink>
              <FooterLink href="/services/consulting">Conseil Technique</FooterLink>
              <FooterLink href="/services/installation">Installation</FooterLink>
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-4 border-b border-blue-500 pb-2 inline-block">
              Contact
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <FaMapMarkerAlt className="text-blue-400 mt-1 mr-3" />
                <span>Derb El Youssoufía, Rue 78, N°89, Bd El Fida - Casablanca-Maroc</span>
              </li>
              <li className="flex items-center">
                <FaPhone className="text-blue-400 mr-3" />
                <span>+212 522 80 80 80</span>
              </li>
              <li className="flex items-center">
                <FaEnvelope className="text-blue-400 mr-3" />
                <span><EMAIL></span>
              </li>
            </ul>
          </motion.div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-700 dark:border-gray-800 mt-12 pt-8 text-center text-gray-400 dark:text-gray-500">
          <p>© {currentYear} Moonelec S.A.R.L. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  );
}

function SocialIcon({ icon, href }: { icon: React.ReactNode; href: string }) {
  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="w-10 h-10 rounded-full bg-blue-600 dark:bg-blue-700 flex items-center justify-center hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
    >
      {icon}
    </a>
  );
}

function FooterLink({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <li>
      <Link
        href={href}
        className="text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 hover:pl-2 transition-all duration-300"
      >
        {children}
      </Link>
    </li>
  );
}
