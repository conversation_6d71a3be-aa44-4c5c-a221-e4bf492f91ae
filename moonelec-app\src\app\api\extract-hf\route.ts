// src/app/api/extract-hf/route.ts
import { NextRequest, NextResponse } from 'next/server'

// ⚠️ Ce handler tourne en Node.js
export const runtime = 'nodejs'

// Importez directement le build Node.js de PDF.js
import { getDocument } from 'pdfjs-dist/legacy/build/pdf.node.js'

const HF_URL   = 'https://api-inference.huggingface.co/models/google/flan-t5-small'
const HF_TOKEN = '*************************************'  // pour test ; en prod, utilisez process.env

export async function POST(req: NextRequest) {
  try {
    // 1) Récupérer le PDF
    const form = await req.formData()
    const file = form.get('file') as File | null
    if (!file || file.type !== 'application/pdf') {
      return NextResponse.json({ error: 'PDF manquant ou invalide' }, { status: 400 })
    }

    // 2) Lire en Uint8Array (Node build requiert Uint8Array)
    const uint8 = new Uint8Array(await file.arrayBuffer())

    // 3) Parser via pdf.node.js (pas de worker)
    const pdf     = await getDocument({ data: uint8 }).promise
    let fullText  = ''
    for (let i = 1; i <= pdf.numPages; i++) {
      const page    = await pdf.getPage(i)
      const content = await page.getTextContent()
      fullText += content.items.map((it: any) => it.str).join(' ') + '\n'
    }

    // 4) Construire le prompt
    const systemPrompt = `
You are a product sheet parser.
Extract from the following text a JSON object with:
- "productName": string
- "reference": string or array of strings
- "description": string
- "characteristics": object mapping attribute names to values
Respond *only* with the JSON.
`
    const payload = { inputs: systemPrompt + '\n\n' + fullText, parameters: { max_new_tokens: 512 } }

    // 5) Appeler Hugging Face HTTP direct
    const hfRes = await fetch(HF_URL, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${HF_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    })
    if (!hfRes.ok) {
      const txt = await hfRes.text()
      throw new Error(`HF Error ${hfRes.status}: ${txt}`)
    }

    const hfJson   = await hfRes.json()
    const generated = Array.isArray(hfJson) ? hfJson[0].generated_text : hfJson.generated_text
    const match     = generated.match(/\{[\s\S]*\}/)
    if (!match) throw new Error('Aucun JSON trouvé dans la réponse de l’IA')

    const data = JSON.parse(match[0])
    return NextResponse.json({ data })

  } catch (err: any) {
    console.error('Extraction erreur:', err)
    return NextResponse.json({ error: err.message || 'Erreur interne' }, { status: 500 })
  }
}
