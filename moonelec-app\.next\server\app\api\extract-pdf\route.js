const CHUNK_PUBLIC_PATH = "server/app/api/extract-pdf/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_formdata-node_lib_esm_fileFromPath_1e623b03.js");
runtime.loadChunk("server/chunks/node_modules_next_a583aadb._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_35ecac8a._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3be._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a80197._.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_100_build_pdf_worker_52c9fc57.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_100_build_pdf_71c3c8b3.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_88_build_pdf_worker_ea871991.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_88_build_pdf_36252b84.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_9_426_build_pdf_worker_c153a02e.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_9_426_build_pdf_fe79cad9.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v2_0_550_build_pdf_worker_4a586fdb.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v2_0_550_build_pdf_8d84ce2e.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf-parse_30edfd6e.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_index_f716ad8d.js");
runtime.loadChunk("server/chunks/node_modules_openai_a822ecb7._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_9ede8929._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__ec629e95._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/extract-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
