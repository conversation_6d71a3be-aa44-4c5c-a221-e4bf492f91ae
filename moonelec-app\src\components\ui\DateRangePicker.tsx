'use client';

import { useState, useEffect } from 'react';
import { FaCalendarAlt, FaTimes, FaCheck } from 'react-icons/fa';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

// Custom styles for the date picker
const customStyles = `
  .react-datepicker__day--selected.react-datepicker__day--keyboard-selected {
    background-color: #3b82f6 !important;
    color: white !important;
  }

  .react-datepicker__day--highlighted {
    background-color: #dbeafe !important;
    color: #1e40af !important;
  }

  .react-datepicker__day--highlighted:hover {
    background-color: #3b82f6 !important;
    color: white !important;
  }
`;

export type DateFilterMode = 'range' | 'specific';

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  selectedDates: Date[];
  mode: DateFilterMode;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  onSelectedDatesChange: (dates: Date[]) => void;
  onModeChange: (mode: DateFilterMode) => void;
}

export default function DateRangePicker({
  startDate,
  endDate,
  selectedDates,
  mode,
  onStartDateChange,
  onEndDateChange,
  onSelectedDatesChange,
  onModeChange,
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempSelectedDates, setTempSelectedDates] = useState<Date[]>(selectedDates);

  // Update temp dates when selectedDates prop changes
  useEffect(() => {
    setTempSelectedDates(selectedDates);
  }, [selectedDates]);

  // Handle date selection in specific dates mode
  const handleDateClick = (date: Date) => {
    if (mode === 'specific') {
      const dateString = date.toDateString();
      const isSelected = tempSelectedDates.some(d => d.toDateString() === dateString);

      if (isSelected) {
        // Remove date if already selected
        setTempSelectedDates(prev => prev.filter(d => d.toDateString() !== dateString));
      } else {
        // Add date if not selected
        setTempSelectedDates(prev => [...prev, date]);
      }
    }
  };

  // Apply the selected dates
  const applySelection = () => {
    if (mode === 'specific') {
      onSelectedDatesChange(tempSelectedDates);
    }
    setIsOpen(false);
  };

  // Cancel selection and revert to original
  const cancelSelection = () => {
    setTempSelectedDates(selectedDates);
    setIsOpen(false);
  };

  // Clear all selections
  const clearAll = () => {
    if (mode === 'range') {
      onStartDateChange(null);
      onEndDateChange(null);
    } else {
      setTempSelectedDates([]);
      onSelectedDatesChange([]);
    }
  };

  // Custom day class name function for highlighting selected dates
  const getDayClassName = (date: Date) => {
    const baseClasses = "react-datepicker__day";

    if (mode === 'specific') {
      const isSelected = tempSelectedDates.some(d => d.toDateString() === date.toDateString());
      if (isSelected) {
        return `${baseClasses} react-datepicker__day--selected react-datepicker__day--keyboard-selected`;
      }
    }

    return baseClasses;
  };

  // Format display text
  const getDisplayText = () => {
    if (mode === 'range') {
      if (startDate && endDate) {
        return `${startDate.toLocaleDateString('fr-FR')} - ${endDate.toLocaleDateString('fr-FR')}`;
      } else if (startDate) {
        return `From ${startDate.toLocaleDateString('fr-FR')}`;
      } else if (endDate) {
        return `Until ${endDate.toLocaleDateString('fr-FR')}`;
      }
      return 'Sélectionner une plage de dates';
    } else {
      if (selectedDates.length === 0) {
        return 'Sélectionner des dates spécifiques';
      } else if (selectedDates.length === 1) {
        return selectedDates[0].toLocaleDateString('fr-FR');
      } else {
        return `${selectedDates.length} dates sélectionnées`;
      }
    }
  };

  return (
    <div className="relative">
      {/* Inject custom styles */}
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />

      {/* Mode Toggle */}
      <div className="flex mb-2 bg-gray-100 dark:bg-gray-700 rounded-md p-1">
        <button
          type="button"
          onClick={() => onModeChange('range')}
          className={`flex-1 px-3 py-1 text-sm rounded transition-colors ${
            mode === 'range'
              ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
          }`}
        >
          Plage de Dates
        </button>
        <button
          type="button"
          onClick={() => onModeChange('specific')}
          className={`flex-1 px-3 py-1 text-sm rounded transition-colors ${
            mode === 'specific'
              ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
          }`}
        >
          Dates Spécifiques
        </button>
      </div>

      {/* Date Input Display */}
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white text-left bg-white dark:bg-gray-700"
        >
          <span className={selectedDates.length === 0 && !startDate && !endDate ? 'text-gray-400' : ''}>
            {getDisplayText()}
          </span>
        </button>
        <FaCalendarAlt className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />

        {/* Clear button */}
        {((mode === 'range' && (startDate || endDate)) || (mode === 'specific' && selectedDates.length > 0)) && (
          <button
            type="button"
            onClick={clearAll}
            className="absolute right-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FaTimes size={12} />
          </button>
        )}
      </div>

      {/* Calendar Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-50">
          {mode === 'range' ? (
            // Date Range Mode
            <div className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date de Début
                  </label>
                  <DatePicker
                    selected={startDate}
                    onChange={onStartDateChange}
                    selectsStart
                    startDate={startDate}
                    endDate={endDate}
                    inline
                    dateFormat="dd/MM/yyyy"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date de Fin
                  </label>
                  <DatePicker
                    selected={endDate}
                    onChange={onEndDateChange}
                    selectsEnd
                    startDate={startDate}
                    endDate={endDate}
                    minDate={startDate}
                    inline
                    dateFormat="dd/MM/yyyy"
                  />
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
                >
                  Terminé
                </button>
              </div>
            </div>
          ) : (
            // Specific Dates Mode
            <div className="p-4">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Sélectionner Plusieurs Dates
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Cliquez sur les dates pour les sélectionner/désélectionner
                </p>
              </div>

              <DatePicker
                selected={null}
                onChange={handleDateClick}
                inline
                dateFormat="dd/MM/yyyy"
                dayClassName={getDayClassName}
                highlightDates={tempSelectedDates}
              />

              {/* Selected dates display */}
              {tempSelectedDates.length > 0 && (
                <div className="mt-4 max-h-32 overflow-y-auto">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Dates Sélectionnées ({tempSelectedDates.length}):
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {tempSelectedDates
                      .sort((a, b) => a.getTime() - b.getTime())
                      .map((date, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 bg-primary text-white text-xs rounded-md"
                        >
                          {date.toLocaleDateString('fr-FR')}
                          <button
                            type="button"
                            onClick={() => handleDateClick(date)}
                            className="ml-1 hover:text-gray-200"
                          >
                            <FaTimes size={10} />
                          </button>
                        </span>
                      ))}
                  </div>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex justify-between mt-4">
                <button
                  type="button"
                  onClick={() => setTempSelectedDates([])}
                  className="px-3 py-1 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                >
                  Tout Effacer
                </button>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={cancelSelection}
                    className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Annuler
                  </button>
                  <button
                    type="button"
                    onClick={applySelection}
                    className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary-dark flex items-center gap-1"
                  >
                    <FaCheck size={12} />
                    Appliquer
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
