import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Modal,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ApiService from '../services/api';

interface AutocompleteSuggestion {
  type: string;
  value: string;
  label: string;
  product?: any;
  details?: any;
  source?: string;
  matchType?: string;
}

interface AutocompleteInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSelectSuggestion?: (suggestion: AutocompleteSuggestion) => void;
  placeholder?: string;
  autocompleteType: 'products' | 'clients';
  field?: string; // reference, name, description, etc.
  style?: any;
  disabled?: boolean;
  minLength?: number;
  maxSuggestions?: number;
}

const AutocompleteInput: React.FC<AutocompleteInputProps> = ({
  value,
  onChangeText,
  onSelectSuggestion,
  placeholder = 'Tapez pour rechercher...',
  autocompleteType,
  field = 'reference',
  style,
  disabled = false,
  minLength = 2,
  maxSuggestions = 10
}) => {
  const [suggestions, setSuggestions] = useState<AutocompleteSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [inputFocused, setInputFocused] = useState(false);
  
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const inputRef = useRef<TextInput>(null);

  // Debounced search function
  const searchSuggestions = async (query: string) => {
    if (query.length < minLength) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);
    
    try {
      console.log('🔍 Searching autocomplete:', { autocompleteType, field, query });
      
      const response = await ApiService.get(`/autocomplete/${autocompleteType}`, {
        q: query,
        field,
        limit: maxSuggestions
      });

      if (response.suggestions) {
        setSuggestions(response.suggestions);
        setShowSuggestions(response.suggestions.length > 0);
        console.log('✅ Found suggestions:', response.suggestions.length);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error('❌ Autocomplete search error:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle text change with debouncing
  const handleTextChange = (text: string) => {
    onChangeText(text);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for search
    searchTimeoutRef.current = setTimeout(() => {
      searchSuggestions(text);
    }, 300); // 300ms debounce
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: AutocompleteSuggestion) => {
    onChangeText(suggestion.value);
    setShowSuggestions(false);
    setSuggestions([]);
    
    if (onSelectSuggestion) {
      onSelectSuggestion(suggestion);
    }

    // Dismiss keyboard
    Keyboard.dismiss();
    inputRef.current?.blur();
  };

  // Handle input focus
  const handleFocus = () => {
    setInputFocused(true);
    if (value.length >= minLength && suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Handle input blur
  const handleBlur = () => {
    setInputFocused(false);
    // Delay hiding suggestions to allow for selection
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Clear search timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Render suggestion item
  const renderSuggestionItem = ({ item }: { item: AutocompleteSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.suggestionContent}>
        <View style={styles.suggestionHeader}>
          <Text style={styles.suggestionLabel} numberOfLines={1}>
            {item.label}
          </Text>
          <View style={[styles.suggestionBadge, { backgroundColor: getSuggestionColor(item.type) }]}>
            <Text style={styles.suggestionBadgeText}>{item.type.toUpperCase()}</Text>
          </View>
        </View>
        
        {item.product && (
          <Text style={styles.suggestionDetails} numberOfLines={1}>
            {item.product.description || item.product.category || 'Produit'}
          </Text>
        )}
        
        {item.details && (
          <Text style={styles.suggestionDetails} numberOfLines={1}>
            {item.details.city || item.details.contactName || 'Détails'}
          </Text>
        )}
      </View>
      
      <Ionicons name="chevron-forward" size={16} color="#666" />
    </TouchableOpacity>
  );

  // Get color for suggestion type
  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'reference': return '#4CAF50';
      case 'name': return '#2196F3';
      case 'description': return '#FF9800';
      case 'denomination': return '#9C27B0';
      case 'city': return '#607D8B';
      case 'email': return '#795548';
      default: return '#757575';
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={[styles.input, inputFocused && styles.inputFocused]}
          value={value}
          onChangeText={handleTextChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          placeholderTextColor="#999"
          editable={!disabled}
          autoCapitalize="none"
          autoCorrect={false}
        />
        
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#007AFF" />
          </View>
        )}
        
        {value.length > 0 && !isLoading && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => {
              onChangeText('');
              setSuggestions([]);
              setShowSuggestions(false);
            }}
          >
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>

      {/* Suggestions Modal */}
      <Modal
        visible={showSuggestions && suggestions.length > 0}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSuggestions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSuggestions(false)}
        >
          <View style={styles.suggestionsContainer}>
            <View style={styles.suggestionsHeader}>
              <Text style={styles.suggestionsTitle}>
                Suggestions ({suggestions.length})
              </Text>
              <TouchableOpacity
                onPress={() => setShowSuggestions(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={suggestions}
              renderItem={renderSuggestionItem}
              keyExtractor={(item, index) => `${item.type}-${index}`}
              style={styles.suggestionsList}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  input: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  inputFocused: {
    borderColor: '#007AFF',
  },
  loadingContainer: {
    paddingHorizontal: 12,
  },
  clearButton: {
    paddingHorizontal: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 20,
    maxHeight: '70%',
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  suggestionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  suggestionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  suggestionsList: {
    maxHeight: 300,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  suggestionLabel: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginRight: 8,
  },
  suggestionBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  suggestionBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#fff',
  },
  suggestionDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
});

export default AutocompleteInput;
