'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaSave, FaFilePdf, FaTimes, FaPlus, FaTrash, FaCalculator } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';
import Image from 'next/image';

// Types
interface QuoteItem {
  id: string;
  productId: string;
  productReference: string;
  productName: string;
  productImage: string | null;
  quantity: number;
  unitPrice: number;
}

interface Quote {
  id: string;
  quoteNumber: string;
  clientName: string;
  companyName: string;
  clientEmail: string;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  totalAmount: number;
  createdAt: string;
  validUntil: string;
  notes: string;
  items: QuoteItem[];
}

export default function EditQuotePage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [quote, setQuote] = useState<Quote | null>(null);
  const [notes, setNotes] = useState('');
  const [validUntil, setValidUntil] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Nous allons récupérer les données réelles du devis depuis l'API

  useEffect(() => {
    // Récupérer les données du devis depuis l'API
    const fetchQuote = async () => {
      try {
        const quoteId = params.id;
        if (!quoteId) {
          throw new Error('ID du devis non spécifié');
        }

        const response = await fetch(`/api/quotes/${quoteId}`);

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération du devis');
        }

        const data = await response.json();
        console.log('Données du devis reçues:', JSON.stringify(data, null, 2));

        // Vérifier si les données sont valides
        if (!data || !data.id) {
          throw new Error('Données du devis invalides');
        }

        // Formater les données pour correspondre à notre interface Quote
        const formattedQuote: Quote = {
          id: data.id,
          quoteNumber: data.quoteNumber || `Q-${data.id.substring(0, 8)}`,
          clientName: data.client && data.client.user ?
            `${data.client.user.firstname || ''} ${data.client.user.lastname || ''}`.trim() || 'Client inconnu' :
            'Client inconnu',
          companyName: data.client?.company_name || 'N/A',
          clientEmail: data.client?.user?.email || 'N/A',
          status: data.status || 'DRAFT',
          totalAmount: data.totalAmount || 0,
          createdAt: data.createdAt ? new Date(data.createdAt).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR'),
          validUntil: data.validUntil ? new Date(data.validUntil).toISOString().split('T')[0] : '',
          notes: data.notes || '',
          items: Array.isArray(data.quoteItems) ? data.quoteItems.map((item: any) => ({
            id: item.id,
            productId: item.product?.id || item.productId,
            productReference: item.product?.reference || item.productReference || 'N/A',
            productName: item.product?.name || item.productName || 'Produit inconnu',
            productImage: item.product?.mainImage || item.product?.image || item.productImage || null,
            quantity: item.quantity || 1,
            unitPrice: item.unitPrice || 0
          })) : []
        };

        setQuote(formattedQuote);
        setNotes(formattedQuote.notes);
        setValidUntil(formattedQuote.validUntil);
      } catch (error) {
        console.error('Erreur lors de la récupération du devis:', error);
        // Afficher un message d'erreur à l'utilisateur
        alert('Erreur lors de la récupération du devis. Veuillez réessayer.');
        // Rediriger vers la liste des devis
        router.push('/admin/quotes');
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuote();
  }, [params.id, router]);

  const updateItemUnitPrice = (itemId: string, price: number) => {
    if (!quote) return;

    const updatedItems = quote.items.map(item => {
      if (item.id === itemId) {
        return { ...item, unitPrice: price };
      }
      return item;
    });

    setQuote({ ...quote, items: updatedItems });
  };

  const calculateTotalAmount = () => {
    if (!quote) return 0;

    const total = quote.items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice);
    }, 0);

    setQuote({ ...quote, totalAmount: total });
  };

  const removeItem = (itemId: string) => {
    if (!quote) return;

    const updatedItems = quote.items.filter(item => item.id !== itemId);
    setQuote({ ...quote, items: updatedItems });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!validUntil) {
      newErrors.validUntil = 'La date de validité est requise';
    }

    // Vérifier que tous les prix unitaires sont définis
    const missingPrices = quote?.items.some(item => item.unitPrice <= 0);
    if (missingPrices) {
      newErrors.prices = 'Tous les prix unitaires doivent être définis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent, asDraft: boolean = false) => {
    e.preventDefault();

    if (!asDraft && !validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculer le montant total
      calculateTotalAmount();

      if (!quote) {
        throw new Error('Données du devis non disponibles');
      }

      // Calculer le montant total
      const totalAmount = quote.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

      // Formater la date de validité
      let formattedValidUntil = null;
      if (validUntil) {
        // S'assurer que la date est au format ISO complet
        formattedValidUntil = new Date(`${validUntil}T00:00:00.000Z`).toISOString();
      }

      // Préparer les données à envoyer
      const quoteData = {
        id: quote.id,
        notes,
        validUntil: formattedValidUntil,
        status: asDraft ? 'DRAFT' : 'PENDING',
        totalAmount,
        items: quote.items.map(item => ({
          id: item.id,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice
        }))
      };

      console.log('Données à envoyer:', quoteData);

      // Envoyer les données à l'API
      const response = await fetch(`/api/quotes/${quote.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erreur lors de la mise à jour du devis');
      }

      // Rediriger vers la liste des devis
      router.push('/admin/quotes');
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du devis:', error);
      alert(`Erreur lors de la mise à jour du devis: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const generatePdf = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculer le montant total
      calculateTotalAmount();

      if (!quote) {
        throw new Error('Données du devis non disponibles');
      }

      // Calculer le montant total
      const totalAmount = quote.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

      // Formater la date de validité
      let formattedValidUntil = null;
      if (validUntil) {
        // S'assurer que la date est au format ISO complet
        formattedValidUntil = new Date(`${validUntil}T00:00:00.000Z`).toISOString();
      }

      // Mettre à jour le devis avant de générer le PDF
      const quoteData = {
        id: quote.id,
        notes,
        validUntil: formattedValidUntil,
        status: quote.status,
        totalAmount,
        items: quote.items.map(item => ({
          id: item.id,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice
        }))
      };

      // Envoyer les données à l'API
      const updateResponse = await fetch(`/api/quotes/${quote.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Erreur lors de la mise à jour du devis');
      }

      // Générer le PDF
      const pdfResponse = await fetch(`/api/quotes/${quote.id}/pdf`, {
        method: 'GET',
      });

      if (!pdfResponse.ok) {
        const errorData = await pdfResponse.json();
        throw new Error(errorData.error || 'Erreur lors de la génération du PDF');
      }

      // Télécharger le PDF
      const blob = await pdfResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `devis-${quote.quoteNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error: any) {
      console.error('Erreur lors de la génération du PDF:', error);
      alert(`Erreur lors de la génération du PDF: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Éditer le devis {quote?.quoteNumber}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Client: {quote?.clientName} - {quote?.companyName}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <Link href="/admin/quotes">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Retour à la liste
              </motion.button>
            </Link>
            <motion.button
              onClick={calculateTotalAmount}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <FaCalculator />
              Calculer le total
            </motion.button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={(e) => handleSubmit(e, false)} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          {/* Client Information */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Informations du client
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Nom du client</p>
                <p className="text-gray-800 dark:text-white">{quote?.clientName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Entreprise</p>
                <p className="text-gray-800 dark:text-white">{quote?.companyName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                <p className="text-gray-800 dark:text-white">{quote?.clientEmail}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Date de création</p>
                <p className="text-gray-800 dark:text-white">{quote?.createdAt}</p>
              </div>
            </div>
          </div>

          {/* Quote Items */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Articles du devis
            </h2>

            {errors.prices && (
              <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                {errors.prices}
              </div>
            )}

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Produit
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Référence
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Quantité
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Prix unitaire (MAD)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Total (MAD)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {quote?.items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 relative rounded overflow-hidden bg-gray-100 dark:bg-gray-700">
                            {item.productImage ? (
                              <Image
                                src={item.productImage}
                                alt={item.productName}
                                fill
                                style={{ objectFit: 'cover' }}
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full text-gray-400">
                                N/A
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {item.productName}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {item.productReference}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.unitPrice}
                          onChange={(e) => updateItemUnitPrice(item.id, parseFloat(e.target.value) || 0)}
                          className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(item.quantity * item.unitPrice)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          type="button"
                          onClick={() => removeItem(item.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <FaTrash />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-white">
                      Total:
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white">
                      {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(
                        quote?.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || 0
                      )}
                    </td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="validUntil" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date de validité <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="validUntil"
                value={validUntil}
                onChange={(e) => setValidUntil(e.target.value)}
                className={`w-full px-4 py-2 border ${
                  errors.validUntil ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {errors.validUntil && (
                <p className="mt-1 text-sm text-red-500">{errors.validUntil}</p>
              )}
            </div>
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Ajouter des notes ou des conditions spéciales..."
              ></textarea>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-wrap justify-end gap-3">
            <motion.button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              disabled={isSubmitting}
              className={`px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors ${
                isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Enregistrer comme brouillon
            </motion.button>

            <motion.button
              type="submit"
              disabled={isSubmitting}
              className={`px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors ${
                isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2 inline-block"></div>
                  Traitement...
                </>
              ) : (
                'Finaliser et envoyer'
              )}
            </motion.button>

            <motion.button
              type="button"
              onClick={generatePdf}
              disabled={isSubmitting}
              className={`px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2 ${
                isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FaFilePdf />
              Générer PDF
            </motion.button>
          </div>
        </form>
      </div>
    </RouteGuard>
  );
}
