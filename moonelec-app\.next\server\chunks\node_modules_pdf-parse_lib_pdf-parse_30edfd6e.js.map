{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/node_modules/pdf-parse/lib/pdf-parse.js"], "sourcesContent": ["var PDFJS = null;\n\nfunction render_page(pageData) {\n    //check documents https://mozilla.github.io/pdf.js/\n    //ret.text = ret.text ? ret.text : \"\";\n\n    let render_options = {\n        //replaces all occurrences of whitespace with standard spaces (0x20). The default value is `false`.\n        normalizeWhitespace: false,\n        //do not attempt to combine same line TextItem's. The default value is `false`.\n        disableCombineTextItems: false\n    }\n\n    return pageData.getTextContent(render_options)\n        .then(function(textContent) {\n            let lastY, text = '';\n            //https://github.com/mozilla/pdf.js/issues/8963\n            //https://github.com/mozilla/pdf.js/issues/2140\n            //https://gist.github.com/hubgit/600ec0c224481e910d2a0f883a7b98e3\n            //https://gist.github.com/hubgit/600ec0c224481e910d2a0f883a7b98e3\n            for (let item of textContent.items) {\n                if (lastY == item.transform[5] || !lastY){\n                    text += item.str;\n                }  \n                else{\n                    text += '\\n' + item.str;\n                }    \n                lastY = item.transform[5];\n            }            \n            //let strings = textContent.items.map(item => item.str);\n            //let text = strings.join(\"\\n\");\n            //text = text.replace(/[ ]+/ig,\" \");\n            //ret.text = `${ret.text} ${text} \\n\\n`;\n            return text;\n        });\n}\n\nconst DEFAULT_OPTIONS = {\n    pagerender: render_page,\n    max: 0,\n    //check https://mozilla.github.io/pdf.js/getting_started/\n    version: 'v1.10.100'\n}\n\nasync function PDF(dataBuffer, options) {\n    var isDebugMode = false;\n\n    let ret = {\n        numpages: 0,\n        numrender: 0,\n        info: null,\n        metadata: null,\n        text: \"\",\n        version: null\n    };\n\n    if (typeof options == 'undefined') options = DEFAULT_OPTIONS;\n    if (typeof options.pagerender != 'function') options.pagerender = DEFAULT_OPTIONS.pagerender;\n    if (typeof options.max != 'number') options.max = DEFAULT_OPTIONS.max;\n    if (typeof options.version != 'string') options.version = DEFAULT_OPTIONS.version;\n    if (options.version == 'default') options.version = DEFAULT_OPTIONS.version;\n\n    PDFJS = PDFJS ? PDFJS : require(`./pdf.js/${options.version}/build/pdf.js`);\n\n    ret.version = PDFJS.version;\n\n    // Disable workers to avoid yet another cross-origin issue (workers need\n    // the URL of the script to be loaded, and dynamically loading a cross-origin\n    // script does not work).\n    PDFJS.disableWorker = true;\n    let doc = await PDFJS.getDocument(dataBuffer);\n    ret.numpages = doc.numPages;\n\n    let metaData = await doc.getMetadata().catch(function(err) {\n        return null;\n    });\n\n    ret.info = metaData ? metaData.info : null;\n    ret.metadata = metaData ? metaData.metadata : null;\n\n    let counter = options.max <= 0 ? doc.numPages : options.max;\n    counter = counter > doc.numPages ? doc.numPages : counter;\n\n    ret.text = \"\";\n\n    for (var i = 1; i <= counter; i++) {\n        let pageText = await doc.getPage(i).then(pageData => options.pagerender(pageData)).catch((err)=>{\n            // todo log err using debug\n            debugger;\n            return \"\";\n        });\n\n        ret.text = `${ret.text}\\n\\n${pageText}`;\n    }\n\n    ret.numrender = counter;\n    doc.destroy();\n\n    return ret;\n}\n\nmodule.exports = PDF;\n"], "names": [], "mappings": "AAAA,IAAI,QAAQ;AAEZ,SAAS,YAAY,QAAQ;IACzB,mDAAmD;IACnD,sCAAsC;IAEtC,IAAI,iBAAiB;QACjB,mGAAmG;QACnG,qBAAqB;QACrB,+EAA+E;QAC/E,yBAAyB;IAC7B;IAEA,OAAO,SAAS,cAAc,CAAC,gBAC1B,IAAI,CAAC,SAAS,WAAW;QACtB,IAAI,OAAO,OAAO;QAClB,+CAA+C;QAC/C,+CAA+C;QAC/C,iEAAiE;QACjE,iEAAiE;QACjE,KAAK,IAAI,QAAQ,YAAY,KAAK,CAAE;YAChC,IAAI,SAAS,KAAK,SAAS,CAAC,EAAE,IAAI,CAAC,OAAM;gBACrC,QAAQ,KAAK,GAAG;YACpB,OACI;gBACA,QAAQ,OAAO,KAAK,GAAG;YAC3B;YACA,QAAQ,KAAK,SAAS,CAAC,EAAE;QAC7B;QACA,wDAAwD;QACxD,gCAAgC;QAChC,oCAAoC;QACpC,wCAAwC;QACxC,OAAO;IACX;AACR;AAEA,MAAM,kBAAkB;IACpB,YAAY;IACZ,KAAK;IACL,yDAAyD;IACzD,SAAS;AACb;AAEA,eAAe,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,cAAc;IAElB,IAAI,MAAM;QACN,UAAU;QACV,WAAW;QACX,MAAM;QACN,UAAU;QACV,MAAM;QACN,SAAS;IACb;IAEA,IAAI,OAAO,WAAW,aAAa,UAAU;IAC7C,IAAI,OAAO,QAAQ,UAAU,IAAI,YAAY,QAAQ,UAAU,GAAG,gBAAgB,UAAU;IAC5F,IAAI,OAAO,QAAQ,GAAG,IAAI,UAAU,QAAQ,GAAG,GAAG,gBAAgB,GAAG;IACrE,IAAI,OAAO,QAAQ,OAAO,IAAI,UAAU,QAAQ,OAAO,GAAG,gBAAgB,OAAO;IACjF,IAAI,QAAQ,OAAO,IAAI,WAAW,QAAQ,OAAO,GAAG,gBAAgB,OAAO;IAE3E,QAAQ,QAAQ;;;;;;;;;;;;;;;;;OAAgB,CAAC,SAAS,EAAE,QAAQ,OAAO,CAAC,aAAa,CAAC;IAE1E,IAAI,OAAO,GAAG,MAAM,OAAO;IAE3B,wEAAwE;IACxE,6EAA6E;IAC7E,yBAAyB;IACzB,MAAM,aAAa,GAAG;IACtB,IAAI,MAAM,MAAM,MAAM,WAAW,CAAC;IAClC,IAAI,QAAQ,GAAG,IAAI,QAAQ;IAE3B,IAAI,WAAW,MAAM,IAAI,WAAW,GAAG,KAAK,CAAC,SAAS,GAAG;QACrD,OAAO;IACX;IAEA,IAAI,IAAI,GAAG,WAAW,SAAS,IAAI,GAAG;IACtC,IAAI,QAAQ,GAAG,WAAW,SAAS,QAAQ,GAAG;IAE9C,IAAI,UAAU,QAAQ,GAAG,IAAI,IAAI,IAAI,QAAQ,GAAG,QAAQ,GAAG;IAC3D,UAAU,UAAU,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG;IAElD,IAAI,IAAI,GAAG;IAEX,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK;QAC/B,IAAI,WAAW,MAAM,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,CAAA,WAAY,QAAQ,UAAU,CAAC,WAAW,KAAK,CAAC,CAAC;YACtF,2BAA2B;YAC3B,QAAS;YACT,OAAO;QACX;QAEA,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,UAAU;IAC3C;IAEA,IAAI,SAAS,GAAG;IAChB,IAAI,OAAO;IAEX,OAAO;AACX;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}