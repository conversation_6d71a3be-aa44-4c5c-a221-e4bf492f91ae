'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
}

interface ExtractionResult {
  success: boolean;
  data: ExtractedProductData;
  metadata: {
    fileName: string;
    fileSize: number;
    textLength: number;
    extractedAt: string;
    method?: string;
  };
  debug?: any;
}

export default function PdfTestPage() {
  const { user } = useAuth();
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ExtractionResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [method, setMethod] = useState<'ai' | 'simple'>('simple');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setResult(null);
      setError(null);
    }
  };

  const testExtraction = async () => {
    if (!file) {
      setError('Veuillez sélectionner un fichier PDF');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const endpoint = method === 'ai' ? '/api/extract-pdf' : '/api/extract-pdf-test';
      
      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erreur lors de l\'extraction');
      }

      setResult(data);
    } catch (err) {
      console.error('Extraction error:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'extraction');
    } finally {
      setLoading(false);
    }
  };

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🧪 Test d'Extraction PDF</h1>
            <p className="text-gray-600 mt-2">
              Testez l'extraction de données depuis des PDF avec différentes méthodes
            </p>
          </div>
          <Link href="/admin/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
            >
              Retour aux produits
            </motion.button>
          </Link>
        </div>

        {/* Configuration */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-bold mb-4">Configuration du test</h2>
          
          {/* Sélection de la méthode */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Méthode d'extraction
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="simple"
                  checked={method === 'simple'}
                  onChange={(e) => setMethod(e.target.value as 'ai' | 'simple')}
                  className="mr-2"
                />
                <span>Extraction simple (sans IA)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="ai"
                  checked={method === 'ai'}
                  onChange={(e) => setMethod(e.target.value as 'ai' | 'simple')}
                  className="mr-2"
                />
                <span>Extraction IA (OpenAI)</span>
              </label>
            </div>
          </div>

          {/* Sélection de fichier */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fichier PDF
            </label>
            <input
              type="file"
              accept="application/pdf"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
            {file && (
              <p className="text-sm text-green-600 mt-2">
                ✅ Fichier sélectionné: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
              </p>
            )}
          </div>

          {/* Bouton de test */}
          <button
            onClick={testExtraction}
            disabled={!file || loading}
            className={`px-6 py-3 rounded-lg font-medium transition-all ${
              !file || loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Test en cours...</span>
              </div>
            ) : (
              `🚀 Tester l'extraction ${method === 'ai' ? '(IA)' : '(Simple)'}`
            )}
          </button>
        </div>

        {/* Erreur */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-red-800 mb-2">❌ Erreur</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Résultats */}
        {result && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 className="text-xl font-bold text-green-800 mb-4">
              ✅ Extraction réussie ({result.metadata.method || method})
            </h2>
            
            {/* Métadonnées */}
            <div className="mb-6 p-4 bg-white rounded border">
              <h3 className="font-semibold mb-2">📊 Métadonnées</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Fichier:</strong> {result.metadata.fileName}</div>
                <div><strong>Taille:</strong> {(result.metadata.fileSize / 1024 / 1024).toFixed(2)} MB</div>
                <div><strong>Texte extrait:</strong> {result.metadata.textLength} caractères</div>
                <div><strong>Méthode:</strong> {result.metadata.method || method}</div>
              </div>
            </div>

            {/* Données extraites */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="p-4 bg-white rounded border">
                <h3 className="font-semibold mb-3">📋 Informations générales</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>Nom:</strong> {result.data.productName}</div>
                  <div><strong>Référence:</strong> {
                    Array.isArray(result.data.reference) 
                      ? result.data.reference.join(', ')
                      : result.data.reference
                  }</div>
                </div>
              </div>

              <div className="p-4 bg-white rounded border">
                <h3 className="font-semibold mb-3">⚙️ Caractéristiques</h3>
                <div className="space-y-1 text-sm">
                  {Object.entries(result.data.characteristics || {}).map(([key, value]) => (
                    <div key={key}><strong>{key}:</strong> {value}</div>
                  ))}
                  {Object.keys(result.data.characteristics || {}).length === 0 && (
                    <div className="text-gray-500">Aucune caractéristique extraite</div>
                  )}
                </div>
              </div>
            </div>

            <div className="p-4 bg-white rounded border">
              <h3 className="font-semibold mb-2">📝 Description</h3>
              <p className="text-sm text-gray-700">{result.data.description}</p>
            </div>

            {/* Debug info */}
            {result.debug && (
              <details className="mt-6">
                <summary className="cursor-pointer font-semibold text-gray-700">
                  🔍 Informations de débogage
                </summary>
                <div className="mt-2 p-4 bg-gray-100 rounded text-xs">
                  <pre>{JSON.stringify(result.debug, null, 2)}</pre>
                </div>
              </details>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h2 className="text-lg font-bold text-blue-900 mb-3">💡 Instructions</h2>
          <div className="space-y-2 text-sm text-blue-800">
            <p>• <strong>Extraction simple:</strong> Utilise des expressions régulières pour extraire les données de base</p>
            <p>• <strong>Extraction IA:</strong> Utilise OpenAI GPT-3.5 pour une analyse intelligente du contenu</p>
            <p>• <strong>Formats supportés:</strong> PDF uniquement (max 10MB)</p>
            <p>• <strong>Qualité optimale:</strong> PDF avec texte sélectionnable (pas d'images scannées)</p>
          </div>
        </div>
      </div>
    </RouteGuard>
  );
}
