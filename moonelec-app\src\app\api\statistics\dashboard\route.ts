import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { prisma } from '@/lib/prisma';
import { checkIPSecurity, createSecureError } from '@/middleware/security';

// GET /api/statistics/dashboard - Real database statistics with graphs data
export async function GET(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || '30'; // days
    const includeGraphs = url.searchParams.get('graphs') === 'true';

    console.log('📊 Statistics request:', {
      user: user.id,
      role: user.role,
      period,
      includeGraphs
    });

    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(period));

      // Get role-specific statistics
      let statistics;
      
      if (user.role === 'ADMIN') {
        statistics = await getAdminStatistics(startDate, endDate, includeGraphs);
      } else if (user.role === 'COMMERCIAL') {
        statistics = await getCommercialStatistics(user, startDate, endDate, includeGraphs);
      } else if (user.role === 'CLIENT') {
        statistics = await getClientStatistics(user, startDate, endDate, includeGraphs);
      } else {
        return NextResponse.json({ error: 'Invalid user role' }, { status: 403 });
      }

      console.log('✅ Statistics generated successfully');

      return NextResponse.json({
        statistics,
        metadata: {
          period: parseInt(period),
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          userRole: user.role,
          includeGraphs,
          generatedAt: new Date().toISOString()
        }
      });

    } catch (statsError: any) {
      console.error('❌ Statistics generation error:', statsError);
      return NextResponse.json({
        error: 'Failed to generate statistics',
        details: statsError.message
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Statistics API error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// Admin statistics - comprehensive overview
async function getAdminStatistics(startDate: Date, endDate: Date, includeGraphs: boolean) {
  const [
    totalUsers,
    totalProducts,
    totalCategories,
    totalBrands,
    totalQuotes,
    totalSalesReports,
    recentActivity,
    userGrowth,
    salesPerformance,
    productPopularity
  ] = await Promise.all([
    // Basic counts
    prisma.user.count(),
    prisma.product.count(),
    prisma.category.count(),
    prisma.brand.count(),
    prisma.quote.count(),
    prisma.salesreport.count(),
    
    // Recent activity
    getRecentActivity(startDate, endDate),
    
    // Growth data for graphs
    includeGraphs ? getUserGrowthData(startDate, endDate) : null,
    includeGraphs ? getSalesPerformanceData(startDate, endDate) : null,
    includeGraphs ? getProductPopularityData() : null
  ]);

  // Calculate revenue from quotes
  const revenueData = await calculateTotalRevenue(startDate, endDate);

  return {
    overview: {
      totalUsers,
      totalProducts,
      totalCategories,
      totalBrands,
      totalQuotes,
      totalSalesReports,
      totalRevenue: revenueData.total,
      averageQuoteValue: revenueData.average
    },
    recentActivity,
    graphs: includeGraphs ? {
      userGrowth,
      salesPerformance,
      productPopularity,
      revenue: revenueData.timeline
    } : null,
    insights: await generateAdminInsights(startDate, endDate)
  };
}

// Commercial statistics - sales focused
async function getCommercialStatistics(user: any, startDate: Date, endDate: Date, includeGraphs: boolean) {
  // Get commercial profile
  const commercial = await prisma.commercial.findUnique({
    where: { userId: user.id }
  });

  if (!commercial) {
    throw new Error('Commercial profile not found');
  }

  const [
    myQuotes,
    myReports,
    myClients,
    myRevenue,
    recentQuotes,
    performanceData
  ] = await Promise.all([
    // Basic counts for this commercial
    prisma.quote.count({
      where: {
        createdByAdminId: commercial.id,
        createdAt: { gte: startDate, lte: endDate }
      }
    }),
    
    prisma.salesreport.count({
      where: {
        commercialId: commercial.id,
        submittedAt: { gte: startDate, lte: endDate }
      }
    }),
    
    prisma.user.count({
      where: {
        role: 'CLIENT',
        createdAt: { gte: startDate, lte: endDate }
      }
    }),
    
    // Revenue calculation
    calculateCommercialRevenue(commercial.id, startDate, endDate),
    
    // Recent quotes
    getRecentQuotes(commercial.id, 5),
    
    // Performance data for graphs
    includeGraphs ? getCommercialPerformanceData(commercial.id, startDate, endDate) : null
  ]);

  return {
    overview: {
      myQuotes,
      myReports,
      myClients,
      myRevenue: myRevenue.total,
      averageQuoteValue: myRevenue.average,
      conversionRate: myQuotes > 0 ? Math.round((myRevenue.total / myQuotes) * 100) / 100 : 0
    },
    recentQuotes,
    graphs: includeGraphs ? {
      performance: performanceData,
      revenue: myRevenue.timeline
    } : null,
    insights: await generateCommercialInsights(commercial.id, startDate, endDate)
  };
}

// Client statistics - personal overview
async function getClientStatistics(user: any, startDate: Date, endDate: Date, includeGraphs: boolean) {
  const [
    myQuotes,
    totalSpent,
    recentQuotes,
    favoriteProducts
  ] = await Promise.all([
    // Quotes count
    prisma.quote.count({
      where: {
        clientId: user.id,
        createdAt: { gte: startDate, lte: endDate }
      }
    }),
    
    // Total spending
    calculateClientSpending(user.id, startDate, endDate),
    
    // Recent quotes
    getRecentClientQuotes(user.id, 5),
    
    // Favorite products
    getFavoriteProducts(user.id)
  ]);

  return {
    overview: {
      myQuotes,
      totalSpent: totalSpent.total,
      averageQuote: totalSpent.average,
      pendingQuotes: await prisma.quote.count({
        where: {
          clientId: user.id,
          status: 'PENDING'
        }
      })
    },
    recentQuotes,
    favoriteProducts,
    graphs: includeGraphs ? {
      spending: totalSpent.timeline
    } : null,
    insights: await generateClientInsights(user.id, startDate, endDate)
  };
}

// Helper functions for data calculation
async function getRecentActivity(startDate: Date, endDate: Date) {
  const recentQuotes = await prisma.quote.findMany({
    where: {
      createdAt: { gte: startDate, lte: endDate }
    },
    take: 10,
    orderBy: { createdAt: 'desc' },
    include: {
      client: {
        include: {
          user: { select: { firstname: true, lastname: true } }
        }
      }
    }
  });

  return recentQuotes.map(quote => ({
    id: quote.id,
    type: 'quote',
    description: `Devis ${quote.quoteNumber}`,
    client: quote.client?.user ? `${quote.client.user.firstname} ${quote.client.user.lastname}` : 'Client inconnu',
    date: quote.createdAt,
    status: quote.status,
    amount: quote.totalAmount
  }));
}

async function calculateTotalRevenue(startDate: Date, endDate: Date) {
  const quotes = await prisma.quote.findMany({
    where: {
      createdAt: { gte: startDate, lte: endDate },
      status: { in: ['APPROVED', 'CONVERTED'] }
    },
    include: { quoteitem: true }
  });

  const total = quotes.reduce((sum, quote) => {
    const quoteTotal = quote.quoteitem.reduce((itemSum: number, item: any) => {
      return itemSum + (item.quantity * item.unitPrice);
    }, 0);
    return sum + quoteTotal;
  }, 0);

  const average = quotes.length > 0 ? total / quotes.length : 0;

  // Generate timeline data for graphs
  const timeline = await generateRevenueTimeline(startDate, endDate);

  return { total, average, timeline };
}

async function generateRevenueTimeline(startDate: Date, endDate: Date) {
  // Generate daily revenue data for the period
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const timeline = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    
    const dayStart = new Date(date);
    dayStart.setHours(0, 0, 0, 0);
    
    const dayEnd = new Date(date);
    dayEnd.setHours(23, 59, 59, 999);

    const dayRevenue = await prisma.quote.aggregate({
      where: {
        createdAt: { gte: dayStart, lte: dayEnd },
        status: { in: ['APPROVED', 'CONVERTED'] }
      },
      _sum: { totalAmount: true }
    });

    timeline.push({
      date: date.toISOString().split('T')[0],
      revenue: dayRevenue._sum.totalAmount || 0
    });
  }

  return timeline;
}

// User growth data for graphs
async function getUserGrowthData(startDate: Date, endDate: Date) {
  const usersByDay = await prisma.user.groupBy({
    by: ['createdAt'],
    where: {
      createdAt: { gte: startDate, lte: endDate }
    },
    _count: { id: true }
  });

  return usersByDay.map(day => ({
    date: day.createdAt.toISOString().split('T')[0],
    users: day._count.id
  }));
}

// Sales performance data
async function getSalesPerformanceData(startDate: Date, endDate: Date) {
  const salesByDay = await prisma.quote.groupBy({
    by: ['createdAt'],
    where: {
      createdAt: { gte: startDate, lte: endDate },
      status: { in: ['APPROVED', 'CONVERTED'] }
    },
    _count: { id: true },
    _sum: { totalAmount: true }
  });

  return salesByDay.map(day => ({
    date: day.createdAt.toISOString().split('T')[0],
    sales: day._count.id,
    revenue: day._sum.totalAmount || 0
  }));
}

// Product popularity data
async function getProductPopularityData() {
  const productStats = await prisma.quoteitem.groupBy({
    by: ['productId'],
    _count: { id: true },
    _sum: { quantity: true },
    orderBy: { _count: { id: 'desc' } },
    take: 10
  });

  const productIds = productStats.map(stat => stat.productId).filter(Boolean);
  const products = await prisma.product.findMany({
    where: { id: { in: productIds as string[] } },
    select: { id: true, name: true, reference: true }
  });

  return productStats.map(stat => {
    const product = products.find(p => p.id === stat.productId);
    return {
      productId: stat.productId,
      productName: product?.name || 'Unknown',
      productReference: product?.reference || 'N/A',
      orderCount: stat._count.id,
      totalQuantity: stat._sum.quantity || 0
    };
  });
}

// Generate admin insights
async function generateAdminInsights(startDate: Date, endDate: Date) {
  const [topCommercial, growthStats] = await Promise.all([
    // Find top performing commercial
    prisma.salesreport.groupBy({
      by: ['commercialId'],
      where: { submittedAt: { gte: startDate, lte: endDate } },
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
      take: 1
    }),

    // Calculate growth rate
    prisma.user.count({
      where: { createdAt: { gte: startDate, lte: endDate } }
    })
  ]);

  return {
    topPerformingCommercial: topCommercial[0]?.commercialId || 'None',
    newUsersThisPeriod: growthStats,
    recommendations: [
      'Focus on client retention strategies',
      'Expand product catalog in popular categories',
      'Improve commercial training programs'
    ]
  };
}

// Calculate commercial revenue
async function calculateCommercialRevenue(commercialId: string, startDate: Date, endDate: Date) {
  const quotes = await prisma.quote.findMany({
    where: {
      createdByAdminId: commercialId,
      createdAt: { gte: startDate, lte: endDate },
      status: { in: ['APPROVED', 'CONVERTED'] }
    },
    include: { quoteitem: true }
  });

  const total = quotes.reduce((sum, quote) => {
    const quoteTotal = quote.quoteitem.reduce((itemSum: number, item: any) => {
      return itemSum + (item.quantity * item.unitPrice);
    }, 0);
    return sum + quoteTotal;
  }, 0);

  const average = quotes.length > 0 ? total / quotes.length : 0;
  const timeline = await generateCommercialRevenueTimeline(commercialId, startDate, endDate);

  return { total, average, timeline };
}

// Get recent quotes for commercial
async function getRecentQuotes(commercialId: string, limit: number) {
  return await prisma.quote.findMany({
    where: { createdByAdminId: commercialId },
    take: limit,
    orderBy: { createdAt: 'desc' },
    include: {
      client: {
        include: {
          user: { select: { firstname: true, lastname: true } }
        }
      }
    }
  });
}

// Commercial performance data
async function getCommercialPerformanceData(commercialId: string, startDate: Date, endDate: Date) {
  const performance = await prisma.quote.groupBy({
    by: ['createdAt'],
    where: {
      createdByAdminId: commercialId,
      createdAt: { gte: startDate, lte: endDate }
    },
    _count: { id: true },
    _sum: { totalAmount: true }
  });

  return performance.map(day => ({
    date: day.createdAt.toISOString().split('T')[0],
    quotes: day._count.id,
    revenue: day._sum.totalAmount || 0
  }));
}

// Generate commercial insights
async function generateCommercialInsights(commercialId: string, startDate: Date, endDate: Date) {
  const reportCount = await prisma.salesreport.count({
    where: {
      commercialId,
      submittedAt: { gte: startDate, lte: endDate }
    }
  });

  return {
    reportsThisPeriod: reportCount,
    recommendations: [
      'Increase client visit frequency',
      'Focus on high-value prospects',
      'Improve quote conversion rate'
    ]
  };
}

// Calculate client spending
async function calculateClientSpending(clientId: string, startDate: Date, endDate: Date) {
  const quotes = await prisma.quote.findMany({
    where: {
      clientId,
      createdAt: { gte: startDate, lte: endDate },
      status: { in: ['APPROVED', 'CONVERTED'] }
    },
    include: { quoteitem: true }
  });

  const total = quotes.reduce((sum, quote) => {
    const quoteTotal = quote.quoteitem.reduce((itemSum: number, item: any) => {
      return itemSum + (item.quantity * item.unitPrice);
    }, 0);
    return sum + quoteTotal;
  }, 0);

  const average = quotes.length > 0 ? total / quotes.length : 0;
  const timeline = await generateClientSpendingTimeline(clientId, startDate, endDate);

  return { total, average, timeline };
}

// Get recent client quotes
async function getRecentClientQuotes(clientId: string, limit: number) {
  return await prisma.quote.findMany({
    where: { clientId },
    take: limit,
    orderBy: { createdAt: 'desc' },
    include: { quoteitem: { take: 3, include: { product: { select: { name: true } } } } }
  });
}

// Get favorite products for client
async function getFavoriteProducts(clientId: string) {
  const productCounts = await prisma.quoteitem.groupBy({
    by: ['productId'],
    where: {
      quote: { clientId }
    },
    _count: { id: true },
    _sum: { quantity: true },
    orderBy: { _count: { id: 'desc' } },
    take: 5
  });

  const productIds = productCounts.map(p => p.productId).filter(Boolean);
  const products = await prisma.product.findMany({
    where: { id: { in: productIds as string[] } },
    select: { id: true, name: true, reference: true }
  });

  return productCounts.map(count => {
    const product = products.find(p => p.id === count.productId);
    return {
      product: product || { name: 'Unknown Product', reference: 'N/A' },
      orderCount: count._count.id,
      totalQuantity: count._sum.quantity || 0
    };
  });
}

// Generate client insights
async function generateClientInsights(clientId: string, startDate: Date, endDate: Date) {
  const quoteCount = await prisma.quote.count({
    where: {
      clientId,
      createdAt: { gte: startDate, lte: endDate }
    }
  });

  return {
    quotesThisPeriod: quoteCount,
    recommendations: [
      'Explore new product categories',
      'Consider bulk orders for discounts',
      'Schedule regular consultations'
    ]
  };
}

// Helper timeline generators
async function generateCommercialRevenueTimeline(commercialId: string, startDate: Date, endDate: Date) {
  // Similar to generateRevenueTimeline but filtered by commercial
  return [];
}

async function generateClientSpendingTimeline(clientId: string, startDate: Date, endDate: Date) {
  // Similar to generateRevenueTimeline but filtered by client
  return [];
}
