'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aEye, FaEdit, FaTrash, FaFilePdf, FaCheck, FaTimes, FaPlus } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';

// Types
interface Quote {
  id: string;
  quoteNumber: string;
  clientName: string;
  companyName: string;
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  totalAmount: number;
  createdAt: string;
  validUntil: string;
}

export default function AdminQuotesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // Liste vide pour les devis
  const mockQuotes: Quote[] = [];

  useEffect(() => {
    // Fonction pour récupérer les devis depuis l'API
    const fetchQuotes = async () => {
      try {
        // Construire l'URL avec les paramètres de recherche et de filtrage
        let url = '/api/quotes?dashboard=true';

        if (searchTerm) {
          url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        if (selectedStatus) {
          url += `&status=${encodeURIComponent(selectedStatus)}`;
        }

        url += `&page=${currentPage}&limit=10`;

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des devis');
        }

        const data = await response.json();

        // Formater les données pour correspondre à notre interface Quote
        const formattedQuotes = data.quotes.map((quote: any) => ({
          id: quote.id,
          quoteNumber: quote.quoteNumber,
          clientName: quote.client ? `${quote.client.firstname} ${quote.client.lastname}` : 'Client inconnu',
          companyName: quote.client?.company_name || 'N/A',
          status: quote.status,
          totalAmount: quote.totalAmount || 0,
          createdAt: new Date(quote.createdAt).toLocaleDateString('fr-FR'),
          validUntil: quote.validUntil ? new Date(quote.validUntil).toLocaleDateString('fr-FR') : 'N/A'
        }));

        setQuotes(formattedQuotes);
        setTotalPages(data.totalPages || 1);
      } catch (error) {
        console.error('Erreur:', error);
        // En cas d'erreur, utiliser un tableau vide
        setQuotes([]);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    };

    // Appeler la fonction de récupération
    fetchQuotes();
  }, [searchTerm, selectedStatus, currentPage]);

  // Nous n'avons plus besoin de filtrer localement car l'API s'en charge
  // Utiliser directement les quotes récupérées de l'API
  const paginatedQuotes = quotes;

  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'CONVERTED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Fonction pour obtenir le libellé du statut en français
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'Brouillon';
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvé';
      case 'REJECTED':
        return 'Rejeté';
      case 'EXPIRED':
        return 'Expiré';
      case 'CONVERTED':
        return 'Converti';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            Gestion des Devis
          </h1>
          <Link href="/admin/quotes/new">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center"
            >
              <FaPlus className="mr-2" />
              Nouveau Devis
            </motion.button>
          </Link>
        </div>

        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Rechercher par numéro, client ou entreprise..."
              />
            </div>
            <div className="w-full md:w-64">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaFilter className="text-gray-400" />
                </div>
                <select
                  value={selectedStatus || ''}
                  onChange={(e) => setSelectedStatus(e.target.value || null)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="">Tous les statuts</option>
                  <option value="DRAFT">Brouillon</option>
                  <option value="PENDING">En attente</option>
                  <option value="APPROVED">Approuvé</option>
                  <option value="REJECTED">Rejeté</option>
                  <option value="EXPIRED">Expiré</option>
                  <option value="CONVERTED">Converti</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Quotes Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    N° Devis
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Montant
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedQuotes.length > 0 ? (
                  paginatedQuotes.map((quote) => (
                    <tr key={quote.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {quote.quoteNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">{quote.clientName}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{quote.companyName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">{quote.createdAt}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Valide jusqu'au {quote.validUntil}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {quote.status === 'DRAFT' ? (
                          <span className="text-gray-500 dark:text-gray-400">Non défini</span>
                        ) : (
                          new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'MAD' }).format(quote.totalAmount)
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(quote.status)}`}>
                          {getStatusLabel(quote.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/quotes/${quote.id}`)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                            title="Voir"
                          >
                            <FaEye />
                          </button>

                          {quote.status === 'DRAFT' && (
                            <button
                              onClick={() => router.push(`/admin/quotes/${quote.id}/edit`)}
                              className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                              title="Modifier"
                            >
                              <FaEdit />
                            </button>
                          )}

                          {quote.status !== 'DRAFT' && (
                            <button
                              onClick={() => router.push(`/admin/quotes/${quote.id}/pdf`)}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="Télécharger PDF"
                            >
                              <FaFilePdf />
                            </button>
                          )}

                          {quote.status === 'PENDING' && (
                            <>
                              <button
                                onClick={() => {
                                  if (confirm('Êtes-vous sûr de vouloir approuver ce devis ?')) {
                                    // Logique d'approbation
                                    console.log('Approbation du devis', quote.id);
                                  }
                                }}
                                className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                title="Approuver"
                              >
                                <FaCheck />
                              </button>

                              <button
                                onClick={() => {
                                  if (confirm('Êtes-vous sûr de vouloir rejeter ce devis ?')) {
                                    // Logique de rejet
                                    console.log('Rejet du devis', quote.id);
                                  }
                                }}
                                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                title="Rejeter"
                              >
                                <FaTimes />
                              </button>
                            </>
                          )}

                          {(quote.status === 'DRAFT' || quote.status === 'PENDING') && (
                            <button
                              onClick={() => {
                                if (confirm('Êtes-vous sûr de vouloir supprimer ce devis ?')) {
                                  // Logique de suppression
                                  console.log('Suppression du devis', quote.id);
                                }
                              }}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              title="Supprimer"
                            >
                              <FaTrash />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      Aucun devis trouvé
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Précédent
              </button>

              {Array.from({ length: totalPages }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index + 1)}
                  className={`px-3 py-1 rounded-md ${
                    currentPage === index + 1
                      ? 'bg-primary text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {index + 1}
                </button>
              ))}

              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Suivant
              </button>
            </nav>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
