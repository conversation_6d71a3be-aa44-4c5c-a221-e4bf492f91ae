/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/brands/route";
exports.ids = ["app/api/brands/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrands%2Froute&page=%2Fapi%2Fbrands%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrands%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrands%2Froute&page=%2Fapi%2Fbrands%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrands%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_brands_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/brands/route.ts */ \"(rsc)/./src/app/api/brands/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/brands/route\",\n        pathname: \"/api/brands\",\n        filename: \"route\",\n        bundlePath: \"app/api/brands/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\brands\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_brands_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrands%2Froute&page=%2Fapi%2Fbrands%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrands%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/brands/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/brands/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_brands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/brands */ \"(rsc)/./src/lib/brands.ts\");\n\n\n// GET /api/brands - Get all brands\nasync function GET(req) {\n    try {\n        const searchParams = req.nextUrl.searchParams;\n        const search = searchParams.get('search') || undefined;\n        const includeProducts = searchParams.get('includeProducts') === 'true';\n        const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')) : undefined;\n        const take = searchParams.has('take') ? parseInt(searchParams.get('take')) : undefined;\n        const withCount = searchParams.get('withCount') === 'true';\n        // Utiliser les données de la base de données\n        if (withCount) {\n            const brands = await (0,_lib_brands__WEBPACK_IMPORTED_MODULE_1__.getBrandsWithProductCount)();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                brands,\n                total: brands.length\n            });\n        } else {\n            const { brands, total } = await (0,_lib_brands__WEBPACK_IMPORTED_MODULE_1__.getBrands)({\n                search,\n                includeProducts,\n                skip,\n                take\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                brands,\n                total\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching brands:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch brands'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/brands - Create a new brand\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        const { name, image } = body;\n        // Validate required fields\n        if (!name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name is required'\n            }, {\n                status: 400\n            });\n        }\n        // Create the brand\n        const brand = await (0,_lib_brands__WEBPACK_IMPORTED_MODULE_1__.createBrand)({\n            name,\n            image\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(brand, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating brand:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to create brand'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/brands/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/brands.ts":
/*!***************************!*\
  !*** ./src/lib/brands.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrand: () => (/* binding */ createBrand),\n/* harmony export */   deleteBrand: () => (/* binding */ deleteBrand),\n/* harmony export */   getBrandById: () => (/* binding */ getBrandById),\n/* harmony export */   getBrands: () => (/* binding */ getBrands),\n/* harmony export */   getBrandsWithProductCount: () => (/* binding */ getBrandsWithProductCount),\n/* harmony export */   updateBrand: () => (/* binding */ updateBrand)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// Get all brands\nasync function getBrands(options) {\n    const { search, includeProducts = false, skip = 0, take = 50 } = options || {};\n    const where = search ? {\n        OR: [\n            {\n                name: {\n                    contains: search\n                }\n            }\n        ]\n    } : {};\n    const [brands, total] = await Promise.all([\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.findMany({\n            where,\n            include: {\n                product: includeProducts\n            },\n            skip,\n            take,\n            orderBy: {\n                name: 'asc'\n            }\n        }),\n        _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.count({\n            where\n        })\n    ]);\n    return {\n        brands,\n        total\n    };\n}\n// Get a single brand by ID\nasync function getBrandById(id, includeProducts = false) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.findUnique({\n        where: {\n            id\n        },\n        include: {\n            product: includeProducts\n        }\n    });\n}\n// Create a new brand\nasync function createBrand(data) {\n    // Check if a brand with the same name already exists\n    const existingBrand = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.findFirst({\n        where: {\n            name: data.name\n        }\n    });\n    if (existingBrand) {\n        throw new Error(`Une marque avec le nom ${data.name} existe déjà`);\n    }\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.create({\n        data: {\n            id: crypto.randomUUID(),\n            name: data.name,\n            image: data.image,\n            updatedAt: new Date()\n        }\n    });\n}\n// Update an existing brand\nasync function updateBrand(id, data) {\n    // If name is being updated, check if it already exists\n    if (data.name) {\n        const existingBrand = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.findFirst({\n            where: {\n                name: data.name,\n                id: {\n                    not: id\n                }\n            }\n        });\n        if (existingBrand) {\n            throw new Error(`Une marque avec le nom ${data.name} existe déjà`);\n        }\n    }\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.update({\n        where: {\n            id\n        },\n        data: {\n            ...data,\n            updatedAt: new Date()\n        }\n    });\n}\n// Delete a brand\nasync function deleteBrand(id) {\n    // First, update all products in this brand to have null brandId\n    await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.updateMany({\n        where: {\n            brandId: id\n        },\n        data: {\n            brandId: null\n        }\n    });\n    // Then delete the brand\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.delete({\n        where: {\n            id\n        }\n    });\n}\n// Get brands with product count\nasync function getBrandsWithProductCount() {\n    const brands = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.brand.findMany({\n        orderBy: {\n            name: 'asc'\n        }\n    });\n    const brandsWithCount = await Promise.all(brands.map(async (brand)=>{\n        const count = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.count({\n            where: {\n                brandId: brand.id\n            }\n        });\n        return {\n            ...brand,\n            productCount: count\n        };\n    }));\n    return brandsWithCount;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/brands.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrands%2Froute&page=%2Fapi%2Fbrands%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrands%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();