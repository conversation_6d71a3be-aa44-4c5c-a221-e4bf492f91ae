import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { checkIncompleteReports } from '@/lib/salesReports';
import { prisma } from '@/lib/prisma';
import { v4 as uuidv4 } from 'uuid';

// POST /api/sales-reports/check-reminders - Check for incomplete reports and send reminders
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // This endpoint can be called by a cron job, so we don't require authentication
    // But if a user is authenticated, they must be an admin
    if (session && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Only administrators can manually check for reminders' },
        { status: 403 }
      );
    }

    // Check for incomplete reports
    const results = await checkIncompleteReports();
    
    // Create notifications for admins
    const admins = await prisma.admin.findMany();
    const notifications = [];
    
    for (const result of results) {
      if (result.needsReminder) {
        // Create a notification for each admin
        for (const admin of admins) {
          const notification = await prisma.notification.create({
            data: {
              id: uuidv4(),
              type: 'REPORT_REMINDER',
              message: `${result.commercial.user.firstname} ${result.commercial.user.lastname} has not submitted their daily report yet.`,
              adminId: admin.id,
              salesReportId: result.reminderReport.id,
              isRead: false,
            },
          });
          
          notifications.push(notification);
        }
      }
    }

    return NextResponse.json({ 
      success: true, 
      results, 
      notificationsSent: notifications.length,
    });
  } catch (error: any) {
    console.error('Error checking for incomplete reports:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to check for incomplete reports' },
      { status: 500 }
    );
  }
}
