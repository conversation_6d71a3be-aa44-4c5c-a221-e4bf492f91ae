{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/ai-service.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\n// Types for AI responses\nexport interface ExtractedProduct {\n  productName: string;\n  reference: string;\n  description: string;\n  characteristics: Record<string, string>;\n}\n\n// AI Service that supports both OpenAI and Ollama\nexport class AIService {\n  private openai?: OpenAI;\n  private ollamaBaseUrl: string;\n  private ollamaModel: string;\n  private useOllama: boolean;\n\n  constructor() {\n    this.ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';\n    this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.2:3b';\n    \n    // Check if OpenAI is configured\n    const openaiKey = process.env.OPENAI_API_KEY;\n    this.useOllama = !openaiKey || openaiKey === '' || openaiKey === 'your-openai-api-key-here';\n    \n    if (!this.useOllama) {\n      this.openai = new OpenAI({\n        apiKey: openaiKey,\n      });\n    }\n\n    console.log(`🤖 AI Service initialized: ${this.useOllama ? 'Ollama' : 'OpenAI'}`);\n  }\n\n  // Check if AI service is available\n  async isAvailable(): Promise<{ available: boolean; service: string; error?: string }> {\n    if (this.useOllama) {\n      try {\n        const response = await fetch(`${this.ollamaBaseUrl}/api/tags`);\n        if (response.ok) {\n          const data = await response.json();\n          const hasModel = data.models?.some((model: any) => model.name.includes(this.ollamaModel.split(':')[0]));\n          \n          if (!hasModel) {\n            return {\n              available: false,\n              service: 'Ollama',\n              error: `Model ${this.ollamaModel} not found. Please install it with: ollama pull ${this.ollamaModel}`\n            };\n          }\n          \n          return { available: true, service: 'Ollama' };\n        } else {\n          return {\n            available: false,\n            service: 'Ollama',\n            error: 'Ollama server not running. Please start Ollama first.'\n          };\n        }\n      } catch (error) {\n        return {\n          available: false,\n          service: 'Ollama',\n          error: 'Cannot connect to Ollama. Please install and start Ollama.'\n        };\n      }\n    } else {\n      try {\n        await this.openai!.models.list();\n        return { available: true, service: 'OpenAI' };\n      } catch (error) {\n        return {\n          available: false,\n          service: 'OpenAI',\n          error: 'Invalid OpenAI API key'\n        };\n      }\n    }\n  }\n\n  // Extract product data from text using AI\n  async extractProductData(text: string): Promise<ExtractedProduct[]> {\n    const prompt = this.createExtractionPrompt(text);\n\n    if (this.useOllama) {\n      return this.extractWithOllama(prompt);\n    } else {\n      return this.extractWithOpenAI(prompt);\n    }\n  }\n\n  // Create the extraction prompt\n  private createExtractionPrompt(text: string): string {\n    return `Analyze the following product catalog text and extract product information. For each product found, provide ONLY the following information in JSON format:\n\n{\n  \"products\": [\n    {\n      \"productName\": \"exact product name\",\n      \"reference\": \"product reference/model number\",\n      \"description\": \"brief product description\",\n      \"characteristics\": {\n        \"key1\": \"value1\",\n        \"key2\": \"value2\"\n      }\n    }\n  ]\n}\n\nRules:\n- Extract only clear, factual information\n- Use the exact product names as written\n- Include technical specifications in characteristics\n- If no clear reference is found, use the product name\n- Return valid JSON only, no additional text\n\nText to analyze:\n${text}`;\n  }\n\n  // Extract using Ollama (local Llama model)\n  private async extractWithOllama(prompt: string): Promise<ExtractedProduct[]> {\n    try {\n      console.log('🦙 Using Ollama for extraction...');\n      \n      const response = await fetch(`${this.ollamaBaseUrl}/api/generate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.ollamaModel,\n          prompt: prompt,\n          stream: false,\n          options: {\n            temperature: 0.1,\n            top_p: 0.9,\n            max_tokens: 2000,\n          }\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Ollama API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.response;\n\n      return this.parseAIResponse(content);\n    } catch (error) {\n      console.error('❌ Ollama extraction error:', error);\n      throw new Error(`Ollama extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  // Extract using OpenAI\n  private async extractWithOpenAI(prompt: string): Promise<ExtractedProduct[]> {\n    try {\n      console.log('🤖 Using OpenAI for extraction...');\n      \n      const completion = await this.openai!.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        messages: [\n          {\n            role: 'system',\n            content: 'You are a product data extraction specialist. Extract product information and return only valid JSON.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.1,\n        max_tokens: 2000,\n      });\n\n      const content = completion.choices[0]?.message?.content;\n      if (!content) {\n        throw new Error('No response from OpenAI');\n      }\n\n      return this.parseAIResponse(content);\n    } catch (error) {\n      console.error('❌ OpenAI extraction error:', error);\n      throw new Error(`OpenAI extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  // Parse AI response and extract products\n  private parseAIResponse(content: string): ExtractedProduct[] {\n    try {\n      // Clean the response to extract JSON\n      let jsonStr = content.trim();\n      \n      // Find JSON block if wrapped in markdown\n      const jsonMatch = jsonStr.match(/```(?:json)?\\s*(\\{[\\s\\S]*\\})\\s*```/);\n      if (jsonMatch) {\n        jsonStr = jsonMatch[1];\n      }\n      \n      // Try to find JSON object\n      const startIndex = jsonStr.indexOf('{');\n      const lastIndex = jsonStr.lastIndexOf('}');\n      \n      if (startIndex !== -1 && lastIndex !== -1) {\n        jsonStr = jsonStr.substring(startIndex, lastIndex + 1);\n      }\n\n      const parsed = JSON.parse(jsonStr);\n      const products = parsed.products || [parsed];\n\n      return products.map((product: any) => ({\n        productName: product.productName || product.name || 'Produit sans nom',\n        reference: product.reference || product.ref || product.productName || 'REF-AUTO',\n        description: product.description || 'Description non disponible',\n        characteristics: product.characteristics || product.specs || {}\n      }));\n\n    } catch (error) {\n      console.error('❌ Failed to parse AI response:', error);\n      console.log('Raw response:', content);\n      \n      // Fallback: try to extract basic info with regex\n      return this.fallbackExtraction(content);\n    }\n  }\n\n  // Fallback extraction using simple text parsing\n  private fallbackExtraction(text: string): ExtractedProduct[] {\n    console.log('🔄 Using fallback extraction...');\n    \n    // Simple regex patterns to find product-like information\n    const lines = text.split('\\n').filter(line => line.trim().length > 0);\n    const products: ExtractedProduct[] = [];\n    \n    for (const line of lines) {\n      if (line.length > 10 && line.length < 200) {\n        products.push({\n          productName: line.trim(),\n          reference: `REF-${Date.now()}-${products.length}`,\n          description: 'Extrait automatiquement du document',\n          characteristics: {}\n        });\n      }\n    }\n\n    return products.slice(0, 5); // Limit to 5 products\n  }\n}\n\n// Export singleton instance\nexport const aiService = new AIService();\n"], "names": [], "mappings": ";;;;AAAA;;AAWO,MAAM;IACH,OAAgB;IAChB,cAAsB;IACtB,YAAoB;IACpB,UAAmB;IAE3B,aAAc;QACZ,IAAI,CAAC,aAAa,GAAG,QAAQ,GAAG,CAAC,eAAe,IAAI;QACpD,IAAI,CAAC,WAAW,GAAG,QAAQ,GAAG,CAAC,YAAY,IAAI;QAE/C,gCAAgC;QAChC,MAAM,YAAY,QAAQ,GAAG,CAAC,cAAc;QAC5C,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa,cAAc,MAAM,cAAc;QAEjE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAA,CAAA,UAAM,CAAC;gBACvB,QAAQ;YACV;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,SAAS,GAAG,WAAW,UAAU;IAClF;IAEA,mCAAmC;IACnC,MAAM,cAAgF;QACpF,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC7D,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,WAAW,KAAK,MAAM,EAAE,KAAK,CAAC,QAAe,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAErG,IAAI,CAAC,UAAU;wBACb,OAAO;4BACL,WAAW;4BACX,SAAS;4BACT,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,gDAAgD,EAAE,IAAI,CAAC,WAAW,EAAE;wBACvG;oBACF;oBAEA,OAAO;wBAAE,WAAW;wBAAM,SAAS;oBAAS;gBAC9C,OAAO;oBACL,OAAO;wBACL,WAAW;wBACX,SAAS;wBACT,OAAO;oBACT;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;oBACL,WAAW;oBACX,SAAS;oBACT,OAAO;gBACT;YACF;QACF,OAAO;YACL,IAAI;gBACF,MAAM,IAAI,CAAC,MAAM,CAAE,MAAM,CAAC,IAAI;gBAC9B,OAAO;oBAAE,WAAW;oBAAM,SAAS;gBAAS;YAC9C,EAAE,OAAO,OAAO;gBACd,OAAO;oBACL,WAAW;oBACX,SAAS;oBACT,OAAO;gBACT;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,mBAAmB,IAAY,EAA+B;QAClE,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;QAE3C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,OAAO;YACL,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC;IACF;IAEA,+BAA+B;IACvB,uBAAuB,IAAY,EAAU;QACnD,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBZ,EAAE,MAAM;IACN;IAEA,2CAA2C;IAC3C,MAAc,kBAAkB,MAAc,EAA+B;QAC3E,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,WAAW;oBACvB,QAAQ;oBACR,QAAQ;oBACR,SAAS;wBACP,aAAa;wBACb,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,MAAM,EAAE;YACxD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,QAAQ;YAE7B,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACzG;IACF;IAEA,uBAAuB;IACvB,MAAc,kBAAkB,MAAc,EAA+B;QAC3E,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC5D,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;YAChD,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACzG;IACF;IAEA,yCAAyC;IACjC,gBAAgB,OAAe,EAAsB;QAC3D,IAAI;YACF,qCAAqC;YACrC,IAAI,UAAU,QAAQ,IAAI;YAE1B,yCAAyC;YACzC,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,UAAU,SAAS,CAAC,EAAE;YACxB;YAEA,0BAA0B;YAC1B,MAAM,aAAa,QAAQ,OAAO,CAAC;YACnC,MAAM,YAAY,QAAQ,WAAW,CAAC;YAEtC,IAAI,eAAe,CAAC,KAAK,cAAc,CAAC,GAAG;gBACzC,UAAU,QAAQ,SAAS,CAAC,YAAY,YAAY;YACtD;YAEA,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,MAAM,WAAW,OAAO,QAAQ,IAAI;gBAAC;aAAO;YAE5C,OAAO,SAAS,GAAG,CAAC,CAAC,UAAiB,CAAC;oBACrC,aAAa,QAAQ,WAAW,IAAI,QAAQ,IAAI,IAAI;oBACpD,WAAW,QAAQ,SAAS,IAAI,QAAQ,GAAG,IAAI,QAAQ,WAAW,IAAI;oBACtE,aAAa,QAAQ,WAAW,IAAI;oBACpC,iBAAiB,QAAQ,eAAe,IAAI,QAAQ,KAAK,IAAI,CAAC;gBAChE,CAAC;QAEH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,iDAAiD;YACjD,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC;IACF;IAEA,gDAAgD;IACxC,mBAAmB,IAAY,EAAsB;QAC3D,QAAQ,GAAG,CAAC;QAEZ,yDAAyD;QACzD,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACnE,MAAM,WAA+B,EAAE;QAEvC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,KAAK;gBACzC,SAAS,IAAI,CAAC;oBACZ,aAAa,KAAK,IAAI;oBACtB,WAAW,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS,MAAM,EAAE;oBACjD,aAAa;oBACb,iBAAiB,CAAC;gBACpB;YACF;QACF;QAEA,OAAO,SAAS,KAAK,CAAC,GAAG,IAAI,sBAAsB;IACrD;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/extract-pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth-options';\nimport { aiService } from '@/lib/ai-service';\n\n// Types pour les données extraites\ninterface ExtractedProductData {\n  productName: string;\n  reference: string | string[];\n  description: string;\n  characteristics: Record<string, string>;\n}\n\n// POST /api/extract-pdf - Extract product data from PDF using AI\nexport async function POST(req: NextRequest) {\n  try {\n    // Vérification de l'authentification\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Check if AI service is available\n    const aiStatus = await aiService.isAvailable();\n    if (!aiStatus.available) {\n      return NextResponse.json(\n        {\n          error: `AI service not available: ${aiStatus.error}`,\n          service: aiStatus.service,\n          configured: false\n        },\n        { status: 503 }\n      );\n    }\n\n    console.log(`🤖 Using AI service: ${aiStatus.service}`);\n\n    // Récupération du fichier PDF\n    const formData = await req.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No PDF file provided' },\n        { status: 400 }\n      );\n    }\n\n    // Vérification du type de fichier\n    if (file.type !== 'application/pdf') {\n      return NextResponse.json(\n        { error: 'File must be a PDF' },\n        { status: 400 }\n      );\n    }\n\n    // Vérification de la taille du fichier (max 10MB)\n    if (file.size > 10 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'File size must be less than 10MB' },\n        { status: 400 }\n      );\n    }\n\n    console.log('📄 Processing PDF:', file.name, 'Size:', file.size);\n\n    // Conversion du fichier en buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Extraction du texte du PDF\n    console.log('🔍 Extracting text from PDF...');\n\n    let extractedText = '';\n\n    try {\n      // Use pdfjs-dist for PDF parsing\n      const pdfjsLib = await import('pdfjs-dist');\n\n      // Load the PDF document\n      const loadingTask = pdfjsLib.getDocument({\n        data: buffer,\n        useSystemFonts: true,\n      });\n\n      const pdfDocument = await loadingTask.promise;\n\n      // Extract text from each page\n      for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {\n        const page = await pdfDocument.getPage(pageNum);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items\n          .map((item: any) => item.str)\n          .join(' ');\n        extractedText += pageText + '\\n';\n      }\n\n    } catch (pdfError) {\n      console.error('❌ PDF parsing error:', pdfError);\n      return NextResponse.json(\n        { error: 'Failed to parse PDF file. Please ensure it contains readable text.' },\n        { status: 400 }\n      );\n    }\n\n    if (!extractedText || extractedText.trim().length === 0) {\n      return NextResponse.json(\n        { error: 'No text could be extracted from the PDF' },\n        { status: 400 }\n      );\n    }\n\n    console.log('📝 Extracted text length:', extractedText.length);\n\n    // Appel au service IA (Ollama ou OpenAI)\n    console.log('🤖 Calling AI service for extraction...');\n    let extractedProducts;\n    try {\n      extractedProducts = await aiService.extractProductData(extractedText);\n    } catch (aiError) {\n      console.error('❌ AI Service Error:', aiError);\n      const errorMessage = aiError instanceof Error ? aiError.message : 'Unknown AI error';\n      return NextResponse.json(\n        { error: `AI extraction failed: ${errorMessage}` },\n        { status: 500 }\n      );\n    }\n\n    if (!extractedProducts || extractedProducts.length === 0) {\n      return NextResponse.json(\n        { error: 'No products could be extracted from the PDF' },\n        { status: 400 }\n      );\n    }\n\n    // Use the first extracted product (already parsed by AI service)\n    const extractedData = extractedProducts[0];\n    console.log('🤖 AI extracted product:', extractedData.productName);\n\n    // Validation des données extraites\n    if (!extractedData.productName || !extractedData.description) {\n      return NextResponse.json(\n        { error: 'Insufficient product data extracted' },\n        { status: 400 }\n      );\n    }\n\n    console.log('✅ Successfully extracted product data:', extractedData.productName);\n\n    return NextResponse.json({\n      success: true,\n      data: extractedData,\n      metadata: {\n        fileName: file.name,\n        fileSize: file.size,\n        textLength: extractedText.length,\n        extractedAt: new Date().toISOString(),\n      }\n    });\n\n  } catch (error) {\n    console.error('❌ PDF extraction error:', error);\n\n    if (error instanceof Error) {\n      // Erreurs spécifiques d'OpenAI\n      if (error.message.includes('API key')) {\n        return NextResponse.json(\n          { error: 'OpenAI API configuration error' },\n          { status: 500 }\n        );\n      }\n\n      // Erreurs de parsing PDF\n      if (error.message.includes('pdf')) {\n        return NextResponse.json(\n          { error: 'Failed to parse PDF file' },\n          { status: 400 }\n        );\n      }\n    }\n\n    return NextResponse.json(\n      { error: 'Internal server error during PDF extraction' },\n      { status: 500 }\n    );\n  }\n}\n\n// GET /api/extract-pdf - Get extraction status/info\nexport async function GET() {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Check AI service availability\n    const aiStatus = await aiService.isAvailable();\n\n    return NextResponse.json({\n      available: aiStatus.available,\n      service: aiStatus.service,\n      error: aiStatus.error,\n      supportedFormats: ['application/pdf'],\n      maxFileSize: '10MB',\n      features: [\n        'Product name extraction',\n        'Reference number extraction',\n        'Technical characteristics extraction',\n        'Category and brand detection',\n        'Free local AI with Ollama/Llama',\n        'OpenAI fallback support'\n      ]\n    });\n\n  } catch (error) {\n    console.error('Error getting extraction info:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAWO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,qCAAqC;QACrC,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,mCAAmC;QACnC,MAAM,WAAW,MAAM,6HAAA,CAAA,YAAS,CAAC,WAAW;QAC5C,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,CAAC,0BAA0B,EAAE,SAAS,KAAK,EAAE;gBACpD,SAAS,SAAS,OAAO;gBACzB,YAAY;YACd,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,OAAO,EAAE;QAEtD,8BAA8B;QAC9B,MAAM,WAAW,MAAM,IAAI,QAAQ;QACnC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,sBAAsB,KAAK,IAAI,EAAE,SAAS,KAAK,IAAI;QAE/D,kCAAkC;QAClC,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,6BAA6B;QAC7B,QAAQ,GAAG,CAAC;QAEZ,IAAI,gBAAgB;QAEpB,IAAI;YACF,iCAAiC;YACjC,MAAM,WAAW;YAEjB,wBAAwB;YACxB,MAAM,cAAc,SAAS,WAAW,CAAC;gBACvC,MAAM;gBACN,gBAAgB;YAClB;YAEA,MAAM,cAAc,MAAM,YAAY,OAAO;YAE7C,8BAA8B;YAC9B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,QAAQ,EAAE,UAAW;gBAChE,MAAM,OAAO,MAAM,YAAY,OAAO,CAAC;gBACvC,MAAM,cAAc,MAAM,KAAK,cAAc;gBAC7C,MAAM,WAAW,YAAY,KAAK,CAC/B,GAAG,CAAC,CAAC,OAAc,KAAK,GAAG,EAC3B,IAAI,CAAC;gBACR,iBAAiB,WAAW;YAC9B;QAEF,EAAE,OAAO,UAAU;YACjB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqE,GAC9E;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,6BAA6B,cAAc,MAAM;QAE7D,yCAAyC;QACzC,QAAQ,GAAG,CAAC;QACZ,IAAI;QACJ,IAAI;YACF,oBAAoB,MAAM,6HAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC;QACzD,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,mBAAmB,QAAQ,QAAQ,OAAO,GAAG;YAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,sBAAsB,EAAE,cAAc;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG;YACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8C,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iEAAiE;QACjE,MAAM,gBAAgB,iBAAiB,CAAC,EAAE;QAC1C,QAAQ,GAAG,CAAC,4BAA4B,cAAc,WAAW;QAEjE,mCAAmC;QACnC,IAAI,CAAC,cAAc,WAAW,IAAI,CAAC,cAAc,WAAW,EAAE;YAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,0CAA0C,cAAc,WAAW;QAE/E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,UAAU;gBACR,UAAU,KAAK,IAAI;gBACnB,UAAU,KAAK,IAAI;gBACnB,YAAY,cAAc,MAAM;gBAChC,aAAa,IAAI,OAAO,WAAW;YACrC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,IAAI,iBAAiB,OAAO;YAC1B,+BAA+B;YAC/B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiC,GAC1C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA2B,GACpC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8C,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,gCAAgC;QAChC,MAAM,WAAW,MAAM,6HAAA,CAAA,YAAS,CAAC,WAAW;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW,SAAS,SAAS;YAC7B,SAAS,SAAS,OAAO;YACzB,OAAO,SAAS,KAAK;YACrB,kBAAkB;gBAAC;aAAkB;YACrC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}