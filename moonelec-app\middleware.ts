import { NextRequest, NextResponse } from 'next/server';
// Temporarily disable with<PERSON>uth to fix build error
// import { withAuth } from 'next-auth/middleware';

// CORS configuration for mobile app
const corsOptions = {
  'Access-Control-Allow-Origin': '*', // In production, specify your mobile app's origin
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400', // 24 hours
};

// Security headers
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
};

function corsMiddleware(request: NextRequest) {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    const response = new NextResponse(null, { status: 200 });
    
    // Add CORS headers
    Object.entries(corsOptions).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  }

  // Continue with the request
  const response = NextResponse.next();

  // Add CORS headers to all responses
  Object.entries(corsOptions).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Add security headers (except for API routes to avoid conflicts)
  if (!request.nextUrl.pathname.startsWith('/api/')) {
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
  }

  return response;
}

// API routes that don't require authentication
const publicApiRoutes = [
  '/api/auth/mobile',
  '/api/mobile/test',
  '/api/test',
  '/api/health',
];

// API routes that require mobile authentication
const mobileApiRoutes = [
  '/api/mobile/',
  '/api/chat/',
  '/api/upload',
  '/api/realtime/',
];

function shouldBypassAuth(pathname: string): boolean {
  return publicApiRoutes.some(route => pathname.startsWith(route));
}

function isMobileApiRoute(pathname: string): boolean {
  return mobileApiRoutes.some(route => pathname.startsWith(route));
}

export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log('🔒 Middleware processing:', {
    method: request.method,
    pathname,
    userAgent: request.headers.get('user-agent')?.substring(0, 50),
    hasAuth: !!request.headers.get('authorization'),
  });

  // Apply CORS for all requests
  const corsResponse = corsMiddleware(request);
  if (corsResponse.status === 200 && request.method === 'OPTIONS') {
    return corsResponse;
  }

  // Handle API routes
  if (pathname.startsWith('/api/')) {
    // Public API routes - no auth required
    if (shouldBypassAuth(pathname)) {
      console.log('🔓 Public API route, bypassing auth:', pathname);
      return corsResponse;
    }

    // Mobile API routes - handle mobile auth differently
    if (isMobileApiRoute(pathname)) {
      console.log('📱 Mobile API route, allowing through:', pathname);
      return corsResponse;
    }

    // Other API routes - let them handle their own auth
    return corsResponse;
  }

  // For non-API routes, just return the CORS response for now
  // TODO: Re-enable NextAuth middleware after fixing build issues
  console.log('🔐 Middleware for non-API route:', pathname);
  return corsResponse;
}

// Configure which routes this middleware runs on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};

// Additional utility functions for debugging
export function logRequest(request: NextRequest) {
  console.log('📝 Request details:', {
    method: request.method,
    url: request.url,
    pathname: request.nextUrl.pathname,
    headers: {
      authorization: request.headers.get('authorization') ? 'Bearer [PRESENT]' : 'Not provided',
      contentType: request.headers.get('content-type'),
      userAgent: request.headers.get('user-agent')?.substring(0, 100),
      origin: request.headers.get('origin'),
      referer: request.headers.get('referer'),
    },
    ip: request.headers.get('x-forwarded-for') || 
        request.headers.get('x-real-ip') || 
        'unknown',
  });
}

// Health check function
export function createHealthResponse() {
  return NextResponse.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    middleware: 'active',
    cors: 'enabled',
    security: 'enabled',
  });
}
