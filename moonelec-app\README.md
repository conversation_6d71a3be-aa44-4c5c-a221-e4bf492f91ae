# Moonelec -- Application Web de Distribution de Matériel Électrique

![Moonelec Logo](public/images/logo/logo-moonelec.png)

Moonelec est une application web complète pour une entreprise de distribution de matériel électrique, offrant une plateforme moderne pour la gestion des produits, des clients, des devis et des rapports commerciaux.

## 🚀 État Actuel du Projet

L'application Moonelec est maintenant une solution complète et fonctionnelle avec toutes les fonctionnalités principales implémentées :

### ✅ Fonctionnalités Complètes

#### 🏠 Interface Utilisateur Moderne

- **Design Élégant** : Interface utilisateur moderne avec animations fluides et effets visuels
- **Mode Sombre/Clair** : Support complet des thèmes avec transition automatique
- **Responsive Design** : Adaptation parfaite à tous les appareils (desktop, tablette, mobile)
- **Animations Avancées** : Effets d'ampoule sur les marques, animations de transition, et interactions fluides
- **Navigation Intuitive** : Navbar et footer cohérents sur toutes les pages

#### 🔐 Authentification et Gestion des Utilisateurs

- **Système d'Authentification Complet** : NextAuth.js avec support MySQL
- **Trois Rôles Utilisateurs** :
  - **CLIENT** : Parcours produits, création de panier, demande de devis
  - **COMMERCIAL** : Soumission de rapports quotidiens, gestion des visites clients
  - **ADMIN** : Accès complet à toutes les fonctionnalités de gestion
- **Profils Utilisateurs** : Gestion complète des informations personnelles et professionnelles

#### 🛍️ Catalogue de Produits Avancé

- **Affichage Dynamique** : Présentation des produits avec images, descriptions détaillées et caractéristiques
- **Filtrage Intelligent** : Recherche par nom, catégorie, marque, prix, etc.
- **Gestion des Catégories** : Organisation hiérarchique des produits
- **Gestion des Marques** : Association des produits à des marques avec images
- **Import PDF Intelligent** : Extraction automatique des données produits depuis des fichiers PDF

#### 🛒 Panier et Système de Devis

- **Panier Interactif** : Ajout/suppression avec animations "fly to cart"
- **Panneau Coulissant** : Interface de panier non-intrusive
- **Demande de Devis Simplifiée** : Processus optimisé pour les clients
- **Génération PDF** : Création automatique de devis professionnels au format PDF
- **Notifications en Temps Réel** : Alertes pour les nouvelles demandes

#### 👨‍💼 Administration Complète

- **Tableau de Bord Dynamique** : Statistiques en temps réel et vue d'ensemble
- **Gestion des Clients** :
  - Création, modification, suppression de comptes clients
  - Vue détaillée des profils clients
  - Export des données (Excel, CSV, PDF)
  - Recherche et filtrage avancés
- **Gestion des Commandes** : Suivi complet des demandes de devis
- **Gestion des Produits** : CRUD complet avec support d'images et caractéristiques
- **Gestion des Marques et Catégories** : Interface d'administration complète

#### 📊 Système de Rapports Commerciaux

- **Rapports Quotidiens** : Interface pour les commerciaux avec tous les champs requis :
  - Besoin, Référence Article, Commentaire, Date de Visite
  - Dénomination, Nom, Objet de la Visite, Réclamation, Ville
  - Support multimédia (images, vidéos, audio, PDF)
- **Panneau d'Administration des Rapports** :
  - Vue tabulaire avec colonnes en français
  - Filtrage par dates (plages ou dates spécifiques)
  - Réorganisation des colonnes par glisser-déposer
  - Panneau de détails extensible avec lecteur audio style WhatsApp
  - Export professionnel (Excel, CSV, PDF avec logo Moonelec)
- **Système de Rappels** : Vérifications automatiques toutes les 2 heures pour les rapports manquants
- **Notifications** : Alertes navigateur pour les administrateurs

#### 🎨 Fonctionnalités Visuelles Avancées

- **Effets d'Ampoule** : Animation des marques avec éclairage dynamique
- **Lecteur Audio WhatsApp** : Interface familière pour les enregistrements audio
- **Galeries d'Images** : Affichage professionnel des images avec téléchargement
- **Lecteur Vidéo Intégré** : Support vidéo complet avec contrôles
- **Export PDF Professionnel** : Documents avec logo Moonelec et mise en page soignée

### 🔧 Fonctionnalités Techniques

#### 📱 Export et Intégration

- **Export Multi-Format** : Excel, CSV, PDF pour tous les modules
- **Import PDF Intelligent** : Extraction automatique des données produits
- **API REST Complète** : Endpoints pour toutes les fonctionnalités
- **Gestion des Fichiers** : Upload et stockage sécurisé des médias

#### 🔔 Notifications et Rappels

- **Notifications Navigateur** : Alertes en temps réel même onglet fermé
- **Système de Rappels** : Vérifications automatiques pour les rapports manquants
- **Alertes Administrateur** : Notifications pour les actions importantes

#### 🎯 Filtrage et Recherche Avancés

- **Filtres de Dates Duaux** : Plages de dates ou dates spécifiques multiples
- **Recherche Globale** : Recherche dans tous les champs pertinents
- **Filtres Combinés** : Combinaison de plusieurs critères de filtrage

## 🛠️ Technologies Utilisées

### Frontend
- **Next.js 14** : Framework React avec App Router et Server Components
- **React 18** : Bibliothèque UI avec hooks et contextes
- **TypeScript** : Typage statique pour une meilleure robustesse
- **TailwindCSS** : Framework CSS utilitaire pour un design moderne
- **Framer Motion** : Animations fluides et interactions avancées

### Backend
- **API Routes Next.js** : Endpoints RESTful intégrés
- **Prisma ORM** : Gestion de base de données type-safe
- **NextAuth.js** : Authentification sécurisée avec sessions
- **Bcrypt** : Hachage sécurisé des mots de passe

### Base de Données
- **MySQL** : Base de données relationnelle robuste
- **Prisma Client** : ORM moderne avec migrations automatiques

### Fonctionnalités Avancées
- **Upload de Fichiers** : Gestion multimédia (images, vidéos, audio, PDF)
- **Export Multi-Format** : XLSX, CSV, PDF avec mise en page professionnelle
- **Notifications Push** : Alertes navigateur en temps réel
- **Extraction PDF** : Analyse intelligente de documents

### Outils de Développement
- **ESLint** : Linting et qualité de code
- **Prettier** : Formatage automatique du code
- **Git** : Contrôle de version avec GitHub

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- MySQL 8.0+
- Git

### Installation

```bash
# Cloner le repository
git clone https://github.com/TheGh0stHicham/moonelec.git
cd moonelec-app

# Installer les dépendances
npm install

# Configurer les variables d'environnement
cp .env.example .env.local
# Éditer .env.local avec vos configurations

# Configurer la base de données
npx prisma migrate dev
npx prisma generate

# Créer un compte administrateur (optionnel)
npm run seed

# Lancer le serveur de développement
npm run dev
```

### Variables d'Environnement Requises

```env
DATABASE_URL="mysql://user:password@localhost:3306/moonelec"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

Ouvrez [http://localhost:3000](http://localhost:3000) dans votre navigateur pour accéder à l'application.

## 📁 Structure du Projet

```bash
moonelec-app/
├── src/
│   ├── app/                    # App Router Next.js 14
│   │   ├── admin/             # Pages d'administration
│   │   ├── api/               # API Routes
│   │   ├── auth/              # Pages d'authentification
│   │   ├── products/          # Pages produits
│   │   └── ...
│   ├── components/            # Composants React
│   │   ├── admin/             # Composants admin
│   │   ├── auth/              # Composants authentification
│   │   ├── ui/                # Composants UI réutilisables
│   │   └── ...
│   ├── context/               # Contextes React
│   │   ├── CartContext.tsx    # Gestion du panier
│   │   └── ThemeContext.tsx   # Gestion des thèmes
│   ├── hooks/                 # Hooks personnalisés
│   ├── lib/                   # Utilitaires et services
│   │   ├── auth-options.ts    # Configuration NextAuth
│   │   ├── prisma.ts          # Client Prisma
│   │   └── ...
│   └── types/                 # Types TypeScript
├── prisma/                    # Configuration Prisma
│   ├── schema.prisma          # Schéma de base de données
│   └── migrations/            # Migrations
├── public/                    # Fichiers statiques
│   ├── images/                # Images et logos
│   └── uploads/               # Fichiers uploadés
└── ...
```

## 🔐 Système d'Authentification

L'application utilise un système de rôles à trois niveaux :

### 👤 CLIENT
- **Accès** : Catalogue produits, panier, demandes de devis
- **Fonctionnalités** :
  - Navigation et recherche de produits
  - Ajout au panier avec animations
  - Demande de devis personnalisés
  - Suivi des demandes

### 👨‍💼 COMMERCIAL
- **Accès** : Interface commerciale, rapports quotidiens
- **Fonctionnalités** :
  - Soumission de rapports de visite
  - Upload de médias (photos, vidéos, audio)
  - Gestion des réclamations clients
  - Suivi des objectifs

### 🔧 ADMIN
- **Accès** : Toutes les fonctionnalités de l'application
- **Fonctionnalités** :
  - Gestion complète des utilisateurs et clients
  - Administration des produits, catégories, marques
  - Suivi des devis et commandes
  - Analyse des rapports commerciaux
  - Export de données et statistiques

## 🎯 Fonctionnalités Clés

### 📊 Tableau de Bord Administrateur
- Vue d'ensemble en temps réel
- Statistiques de vente et performance
- Alertes et notifications importantes
- Accès rapide aux fonctions principales

### 🛒 Système de Panier Avancé
- Interface coulissante non-intrusive
- Animations "fly to cart" fluides
- Calculs automatiques de totaux
- Sauvegarde de session

### 📈 Rapports Commerciaux
- Formulaires complets avec validation
- Support multimédia intégré
- Système de rappels automatiques
- Export professionnel avec logo

### 🔍 Recherche et Filtrage
- Recherche en temps réel
- Filtres multiples combinables
- Tri par pertinence
- Pagination optimisée

## 📱 Responsive Design

L'application est entièrement responsive et optimisée pour :
- **Desktop** : Interface complète avec toutes les fonctionnalités
- **Tablette** : Adaptation de l'interface pour écrans moyens
- **Mobile** : Interface tactile optimisée avec navigation simplifiée

## 🚀 Performance et Optimisation

- **Server-Side Rendering** : Chargement rapide des pages
- **Image Optimization** : Compression et formats modernes
- **Code Splitting** : Chargement progressif des composants
- **Caching** : Mise en cache intelligente des données

## 📝 Licence

Ce projet est la propriété de **Moonelec**. Tous droits réservés.

---

*Développé avec ❤️ pour Moonelec - Distribution de Matériel Électrique*