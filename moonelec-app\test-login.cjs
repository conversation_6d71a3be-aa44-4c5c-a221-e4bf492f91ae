const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Set environment variable for SQLite
process.env.DATABASE_URL = 'file:./dev.db';

const prisma = new PrismaClient();

async function testLogin() {
  try {
    console.log('🔍 Testing login process...');

    // Simulate the exact login process from auth-options.ts
    const credentials = {
      username: 'admin',
      password: 'admin123'
    };

    console.log('1. Looking for user by username:', credentials.username);

    // Find user by username
    const user = await prisma.user.findUnique({
      where: { username: credentials.username },
      include: {
        client: true,
        commercial: true,
        admin: true,
      },
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    });

    console.log('2. Verifying password...');
    const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
    
    if (!isPasswordValid) {
      console.log('❌ Invalid password');
      return;
    }

    console.log('✅ Password is valid');

    console.log('3. Building user object...');
    
    // Build the user object that would be returned
    const userObject = {
      id: user.id,
      email: user.email,
      username: user.username,
      firstname: user.firstname,
      lastname: user.lastname,
      telephone: user.telephone,
      role: user.role,
      clientId: user.client?.id || null,
      commercialId: user.commercial?.id || null,
      adminId: user.admin?.id || null,
    };

    console.log('✅ User object built:', userObject);

    console.log('🎉 Login process would succeed!');

    // Test all users
    console.log('\n🔍 Testing all users...');
    const allUsers = await prisma.user.findMany({
      include: {
        client: true,
        commercial: true,
        admin: true,
      },
    });

    for (const testUser of allUsers) {
      const isValid = await bcrypt.compare('admin123', testUser.password);
      console.log(`  - ${testUser.username}: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
      
      if (isValid) {
        const userObj = {
          id: testUser.id,
          email: testUser.email,
          username: testUser.username,
          firstname: testUser.firstname,
          lastname: testUser.lastname,
          role: testUser.role,
          clientId: testUser.client?.id || null,
          commercialId: testUser.commercial?.id || null,
          adminId: testUser.admin?.id || null,
        };
        console.log(`    User object: ${JSON.stringify(userObj, null, 2)}`);
      }
    }

  } catch (error) {
    console.error('❌ Error testing login:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogin();
