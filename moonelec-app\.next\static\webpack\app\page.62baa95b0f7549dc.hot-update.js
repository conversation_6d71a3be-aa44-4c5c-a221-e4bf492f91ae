"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!******************************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernCategoriesGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight,FaCog,FaHome,FaIndustry,FaLightbulb,FaPlug,FaShieldAlt!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst featuredCategories = [\n    {\n        id: '1',\n        name: 'Éclairage LED',\n        description: 'Solutions d\\'éclairage modernes et économes en énergie',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 26,\n            columnNumber: 11\n        }, undefined),\n        productCount: 156,\n        href: '/products?category=eclairage',\n        gradient: 'from-yellow-400/80 to-orange-500/80'\n    },\n    {\n        id: '2',\n        name: 'Prises & Interrupteurs',\n        description: 'Gamme complète de prises et interrupteurs design',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaPlug, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 36,\n            columnNumber: 11\n        }, undefined),\n        productCount: 89,\n        href: '/products?category=prises',\n        gradient: 'from-blue-400/80 to-indigo-500/80'\n    },\n    {\n        id: '3',\n        name: 'Protection Électrique',\n        description: 'Dispositifs de protection et sécurité électrique',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaShieldAlt, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 46,\n            columnNumber: 11\n        }, undefined),\n        productCount: 67,\n        href: '/products?category=protection',\n        gradient: 'from-green-400/80 to-emerald-500/80'\n    },\n    {\n        id: '4',\n        name: 'Câbles & Conducteurs',\n        description: 'Câbles électriques haute qualité pour tous usages',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCog, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 56,\n            columnNumber: 11\n        }, undefined),\n        productCount: 134,\n        href: '/products?category=cables',\n        gradient: 'from-purple-400/80 to-violet-500/80'\n    },\n    {\n        id: '5',\n        name: 'Domotique',\n        description: 'Solutions intelligentes pour la maison connectée',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHome, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 66,\n            columnNumber: 11\n        }, undefined),\n        productCount: 45,\n        href: '/products?category=domotique',\n        gradient: 'from-cyan-400/80 to-blue-500/80'\n    },\n    {\n        id: '6',\n        name: 'Industriel',\n        description: 'Équipements électriques pour applications industrielles',\n        image: '/images/hero/placeholder.svg',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaIndustry, {\n            className: \"text-2xl\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 76,\n            columnNumber: 11\n        }, undefined),\n        productCount: 78,\n        href: '/products?category=industriel',\n        gradient: 'from-red-400/80 to-rose-500/80'\n    }\n];\nfunction ModernCategoriesGrid() {\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernCategoriesGrid.useEffect\": ()=>{\n            // Simulate API call - replace with actual API call\n            const loadCategories = {\n                \"ModernCategoriesGrid.useEffect.loadCategories\": async ()=>{\n                    try {\n                        // For now, use featured categories\n                        // In production, fetch from API: const response = await fetch('/api/categories');\n                        setCategories(featuredCategories);\n                    } catch (error) {\n                        console.error('Error loading categories:', error);\n                        setCategories(featuredCategories); // Fallback\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ModernCategoriesGrid.useEffect.loadCategories\"];\n            loadCategories();\n        }\n    }[\"ModernCategoriesGrid.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-light-gray\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        ...Array(7)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-gray-200 rounded-lg mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-light-gray\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"lg:col-span-1 bg-white rounded-xl p-8 shadow-lg flex flex-col justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-charcoal mb-4 font-heading\",\n                                            children: \"Cat\\xe9gories les Plus Populaires\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed\",\n                                            children: \"D\\xe9couvrez notre s\\xe9lection de produits \\xe9lectriques de haute qualit\\xe9, organis\\xe9s par cat\\xe9gories pour faciliter votre recherche.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-moonelec-red rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Plus de 500 produits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-electric-blue rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Livraison rapide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    className: \"group inline-flex items-center justify-center space-x-2 bg-moonelec-red hover:bg-moonelec-red-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Explorer Produits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {\n                                            className: \"group-hover:translate-x-1 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: category.href,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-64 bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-cover bg-center bg-no-repeat\",\n                                                            style: {\n                                                                backgroundImage: \"url(\".concat(category.image, \")\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t \".concat(category.gradient, \" opacity-80 group-hover:opacity-90 transition-opacity duration-300\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 h-full flex flex-col justify-between p-6 text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 bg-white/20 backdrop-blur-sm rounded-lg\",\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium opacity-90\",\n                                                                        children: [\n                                                                            category.productCount,\n                                                                            \" produits\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold mb-2 group-hover:text-white transition-colors\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-90 leading-relaxed\",\n                                                                    children: category.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 px-4 py-2 rounded-lg transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Voir produits\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaCog_FaHome_FaIndustry_FaLightbulb_FaPlug_FaShieldAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {\n                                                                        className: \"text-xs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 border-2 border-transparent group-hover:border-white/30 rounded-xl transition-all duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this)\n                                }, category.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                    children: [\n                        {\n                            number: '500+',\n                            label: 'Produits'\n                        },\n                        {\n                            number: '50+',\n                            label: 'Marques'\n                        },\n                        {\n                            number: '1000+',\n                            label: 'Clients Satisfaits'\n                        },\n                        {\n                            number: '24/7',\n                            label: 'Support'\n                        }\n                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-moonelec-red mb-2\",\n                                    children: stat.number\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 font-medium\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\home\\\\ModernCategoriesGrid.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernCategoriesGrid, \"Rt0Mh744vHF1SR1qL5bOrIka3Sw=\");\n_c = ModernCategoriesGrid;\nvar _c;\n$RefreshReg$(_c, \"ModernCategoriesGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});