'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaEdit, FaTrash, FaArrowLeft, Fa<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import Image from 'next/image';
import Link from 'next/link';
import RouteGuard from '@/components/auth/RouteGuard';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
  order: number;
}

interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  characteristics: Record<string, any>;
  mainImage: string | null;
  createdAt: string;
  updatedAt: string;
  category: {
    id: string;
    name: string;
  } | null;
  productimage: ProductImage[];
}

export default function ProductDetailPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const response = await fetch(`/api/products/${productId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch product');
        }

        const data = await response.json();
        setProduct(data);
      } catch (err: any) {
        console.error('Error fetching product:', err);
        setError(err.message || 'An error occurred while fetching the product');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete product');
      }

      router.push('/admin/products');
    } catch (err: any) {
      console.error('Error deleting product:', err);
      setError(err.message || 'An error occurred while deleting the product');
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <FaSpinner className="animate-spin text-4xl text-primary" />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
          <div className="flex items-center text-red-600 dark:text-red-400 mb-4">
            <FaExclamationTriangle className="text-2xl mr-2" />
            <h2 className="text-xl font-semibold">Erreur</h2>
          </div>
          <p className="text-red-600 dark:text-red-400 mb-4">
            {error || "Le produit n'a pas pu être chargé"}
          </p>
          <Link href="/admin/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              Retour à la liste des produits
            </motion.button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex items-center mb-4 md:mb-0">
            <Link href="/admin/products">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="mr-4 p-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                <FaArrowLeft />
              </motion.button>
            </Link>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Détails du produit
            </h1>
          </div>
          <div className="flex space-x-2">
            <Link href={`/admin/products/${productId}/edit`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center"
              >
                <FaEdit className="mr-2" />
                Modifier
              </motion.button>
            </Link>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
              onClick={() => setShowDeleteConfirm(true)}
            >
              <FaTrash className="mr-2" />
              Supprimer
            </motion.button>
          </div>
        </div>

        {/* Product Details */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Images */}
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                Images du produit
              </h2>

              <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 mb-4">
                {selectedImage || product.mainImage || (product.productimage.length > 0 && product.productimage[0].url) ? (
                  <Image
                    src={selectedImage || product.mainImage || product.productimage[0].url}
                    alt={product.name}
                    fill
                    style={{ objectFit: 'contain' }}
                    className="w-full h-full"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <span className="text-gray-400">Aucune image</span>
                  </div>
                )}
              </div>

              {product.productimage.length > 0 && (
                <div className="grid grid-cols-5 gap-2">
                  {product.productimage.map((image) => (
                    <div
                      key={image.id}
                      className={`relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 cursor-pointer ${
                        selectedImage === image.url ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => setSelectedImage(image.url)}
                    >
                      <Image
                        src={image.url}
                        alt={image.alt || product.name}
                        fill
                        style={{ objectFit: 'cover' }}
                        className="w-full h-full"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Product Information */}
            <div className="p-6 border-t md:border-t-0 md:border-l border-gray-200 dark:border-gray-700">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                  {product.name}
                </h2>
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  Référence: <span className="font-semibold">{product.reference}</span>
                </p>
                {product.category && (
                  <p className="text-gray-500 dark:text-gray-400">
                    Catégorie: <span className="font-semibold">{product.category.name}</span>
                  </p>
                )}
                {product.brand && (
                  <p className="text-gray-500 dark:text-gray-400">
                    Marque: <span className="font-semibold">{product.brand.name}</span>
                  </p>
                )}
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                  Description
                </h3>
                <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                  {product.description}
                </p>
              </div>

              {Object.keys(product.characteristics).length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                    Caractéristiques
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <dl className="grid grid-cols-1 gap-x-4 gap-y-2">
                      {Object.entries(product.characteristics).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">{key}</dt>
                          <dd className="text-sm text-gray-900 dark:text-white">{value}</dd>
                        </div>
                      ))}
                    </dl>
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                  Informations supplémentaires
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <dl className="grid grid-cols-1 gap-x-4 gap-y-2">
                    <div className="flex justify-between">
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Date de création</dt>
                      <dd className="text-sm text-gray-900 dark:text-white">
                        {new Date(product.createdAt).toLocaleDateString('fr-FR')}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Dernière mise à jour</dt>
                      <dd className="text-sm text-gray-900 dark:text-white">
                        {new Date(product.updatedAt).toLocaleDateString('fr-FR')}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6"
            >
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Confirmer la suppression
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Êtes-vous sûr de vouloir supprimer le produit <span className="font-semibold">{product.name}</span> ? Cette action est irréversible.
              </p>
              <div className="flex justify-end space-x-3">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isDeleting}
                >
                  Annuler
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
                  onClick={handleDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Suppression...
                    </>
                  ) : (
                    <>
                      <FaTrash className="mr-2" />
                      Supprimer
                    </>
                  )}
                </motion.button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
