import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import ApiService from '../services/api';
import WebApiService from '../services/webApi';
import { SimpleLogo } from '../components/AnimatedLogo';
import { ElectricIcon } from '../components/ElectricIcons';
import { ElectricBackgroundAdvanced } from '../components/ElectricEffects';

const { width } = Dimensions.get('window');

interface DashboardStats {
  totalProducts: number;
  totalQuotes: number;
  pendingQuotes: number;
  totalReports?: number;
}

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalQuotes: 0,
    pendingQuotes: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    loadDashboardData();
    startAnimations();
  }, []);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      console.log('📊 Loading dashboard data with WebAPI...');

      // Try WebAPI first for better authentication
      try {
        const dashboardData = await WebApiService.getDashboardData();

        // Load pending quotes count separately
        const pendingQuotesResponse = await WebApiService.getQuotes({
          status: 'PENDING',
          limit: 1
        });

        let reportsCount = 0;
        if (user?.role === UserRole.COMMERCIAL) {
          const reportsResponse = await ApiService.getSalesReports({ take: 1 });
          reportsCount = reportsResponse.total;
        }

        setStats({
          totalProducts: dashboardData.stats.totalProducts || 0,
          totalQuotes: dashboardData.stats.totalQuotes || 0,
          pendingQuotes: pendingQuotesResponse.total || 0,
          totalReports: reportsCount,
        });

        console.log('✅ Dashboard data loaded successfully with WebAPI');
      } catch (webApiError) {
        console.log('⚠️ WebAPI failed, falling back to original API:', webApiError);

        // Fallback to original API
        const productsResponse = await ApiService.getProducts({ take: 1 });
        const quotesResponse = await ApiService.getQuotes({ limit: 1 });
        const pendingQuotesResponse = await ApiService.getQuotes({
          status: 'PENDING',
          limit: 1
        });

        let reportsCount = 0;
        if (user?.role === UserRole.COMMERCIAL) {
          const reportsResponse = await ApiService.getSalesReports({ take: 1 });
          reportsCount = reportsResponse.total;
        }

        setStats({
          totalProducts: productsResponse.total,
          totalQuotes: quotesResponse.total,
          pendingQuotes: pendingQuotesResponse.total,
          totalReports: reportsCount,
        });

        console.log('✅ Dashboard data loaded with fallback API');
      }
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    index: number;
  }> = ({ title, value, icon, color, index }) => {
    const cardAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      Animated.timing(cardAnim, {
        toValue: 1,
        duration: 500,
        delay: index * 100,
        useNativeDriver: true,
      }).start();
    }, []);

    return (
      <Animated.View
        style={[
          styles.statCard,
          { borderLeftColor: color },
          {
            opacity: cardAnim,
            transform: [{
              translateY: cardAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              })
            }]
          }
        ]}
      >
        <View style={styles.statContent}>
          <View style={styles.statText}>
            <Text style={styles.statValue}>{value}</Text>
            <Text style={styles.statTitle}>{title}</Text>
          </View>
          <View style={[styles.statIconContainer, { backgroundColor: `${color}15` }]}>
            <Ionicons name={icon} size={32} color={color} />
          </View>
        </View>
      </Animated.View>
    );
  };

  const QuickAction: React.FC<{
    title: string;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    onPress: () => void;
    index: number;
  }> = ({ title, icon, color, onPress, index }) => {
    const actionAnim = useRef(new Animated.Value(0)).current;
    const scaleValue = useRef(new Animated.Value(1)).current;

    useEffect(() => {
      Animated.timing(actionAnim, {
        toValue: 1,
        duration: 400,
        delay: index * 80,
        useNativeDriver: true,
      }).start();
    }, []);

    const handlePressIn = () => {
      Animated.spring(scaleValue, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View
        style={{
          opacity: actionAnim,
          transform: [{
            translateY: actionAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0],
            })
          }, {
            scale: scaleValue,
          }]
        }}
      >
        <TouchableOpacity
          style={styles.quickAction}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.8}
        >
          <View style={[styles.quickActionIcon, { backgroundColor: color }]}>
            <Ionicons name={icon} size={24} color="#fff" />
          </View>
          <Text style={styles.quickActionText}>{title}</Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header avec gradient et logo */}
      <Animated.View
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>
              {getGreeting()}, {user?.firstname}!
            </Text>
            <Text style={styles.subtitle}>
              {user?.role === UserRole.CLIENT && 'Espace Client'}
              {user?.role === UserRole.COMMERCIAL && 'Espace Commercial'}
              {user?.role === UserRole.ADMIN && 'Espace Administrateur'}
            </Text>
          </View>
          <View style={styles.headerRight}>
            <SimpleLogo size={50} />
          </View>
        </View>

        {/* Effets électriques avancés */}
        <ElectricBackgroundAdvanced />
      </Animated.View>

      {/* Section statistiques */}
      <Animated.View
        style={[
          styles.statsContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Text style={styles.sectionTitle}>📊 Aperçu</Text>

        <StatCard
          title="Produits"
          value={stats.totalProducts}
          icon="grid-outline"
          color="#4CAF50"
          index={0}
        />

        <StatCard
          title="Devis"
          value={stats.totalQuotes}
          icon="document-text-outline"
          color="#006db7"
          index={1}
        />

        <StatCard
          title="Devis en attente"
          value={stats.pendingQuotes}
          icon="time-outline"
          color="#FF9800"
          index={2}
        />

        {user?.role === UserRole.COMMERCIAL && (
          <StatCard
            title="Rapports de visite"
            value={stats.totalReports || 0}
            icon="clipboard-outline"
            color="#9C27B0"
            index={3}
          />
        )}
      </Animated.View>

      {/* Actions rapides */}
      <Animated.View
        style={[
          styles.quickActionsContainer,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <Text style={styles.sectionTitle}>⚡ Actions rapides</Text>

        <View style={styles.quickActionsGrid}>
          <QuickAction
            title="Voir les produits"
            icon="grid"
            color="#4CAF50"
            onPress={() => navigation.navigate('Products')}
            index={0}
          />

          <QuickAction
            title="Catégories"
            icon="folder"
            color="#FF9800"
            onPress={() => navigation.navigate('Categories')}
            index={1}
          />

          <QuickAction
            title="Mes devis"
            icon="document-text"
            color="#006db7"
            onPress={() => navigation.navigate('Quotes')}
            index={2}
          />

          {user?.role === UserRole.COMMERCIAL && (
            <QuickAction
              title="Nouveau rapport"
              icon="add-circle"
              color="#9C27B0"
              onPress={() => navigation.navigate('CreateSalesReport')}
              index={3}
            />
          )}

          {user?.role === UserRole.ADMIN && (
            <>
              <QuickAction
                title="Gérer catégories"
                icon="folder-open"
                color="#e74c3c"
                onPress={() => navigation.navigate('ManageCategories')}
                index={3}
              />

              <QuickAction
                title="Gérer produits"
                icon="cube"
                color="#f39c12"
                onPress={() => navigation.navigate('ManageProducts')}
                index={4}
              />
            </>
          )}

          <QuickAction
            title="Mon profil"
            icon="person"
            color="#607D8B"
            onPress={() => navigation.navigate('Profile')}
            index={user?.role === UserRole.ADMIN ? 5 : user?.role === UserRole.COMMERCIAL ? 4 : 3}
          />

          {/* Test button for debugging */}
          <QuickAction
            title="Test API"
            icon="flask"
            color="#8e44ad"
            onPress={() => navigation.navigate('TestConnection')}
            index={user?.role === UserRole.ADMIN ? 6 : user?.role === UserRole.COMMERCIAL ? 5 : 4}
          />
        </View>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#006db7',
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    position: 'relative',
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    marginLeft: 20,
  },
  greeting: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    fontStyle: 'italic',
  },
  headerDecorations: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  decoration1: {
    position: 'absolute',
    top: '20%',
    right: '10%',
  },
  decoration2: {
    position: 'absolute',
    top: '60%',
    left: '5%',
  },
  decoration3: {
    position: 'absolute',
    top: '40%',
    right: '25%',
  },
  statsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderLeftWidth: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  statContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statText: {
    flex: 1,
  },
  statValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
  },
  statTitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 6,
    fontWeight: '500',
  },
  statIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActionsContainer: {
    padding: 20,
    paddingTop: 0,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    width: (width - 60) / 2,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default HomeScreen;
