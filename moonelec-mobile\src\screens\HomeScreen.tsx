import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import ApiService from '../services/api';

interface DashboardStats {
  totalProducts: number;
  totalQuotes: number;
  pendingQuotes: number;
  totalReports?: number;
}

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalQuotes: 0,
    pendingQuotes: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Load products count
      const productsResponse = await ApiService.getProducts({ take: 1 });

      // Load quotes count
      const quotesResponse = await ApiService.getQuotes({ limit: 1 });

      // Load pending quotes count
      const pendingQuotesResponse = await ApiService.getQuotes({
        status: 'PENDING',
        limit: 1
      });

      let reportsCount = 0;
      if (user?.role === UserRole.COMMERCIAL) {
        const reportsResponse = await ApiService.getSalesReports({ take: 1 });
        reportsCount = reportsResponse.total;
      }

      setStats({
        totalProducts: productsResponse.total,
        totalQuotes: quotesResponse.total,
        pendingQuotes: pendingQuotesResponse.total,
        totalReports: reportsCount,
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statContent}>
        <View style={styles.statText}>
          <Text style={styles.statValue}>{value}</Text>
          <Text style={styles.statTitle}>{title}</Text>
        </View>
        <Ionicons name={icon} size={32} color={color} />
      </View>
    </View>
  );

  const QuickAction: React.FC<{
    title: string;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    onPress: () => void;
  }> = ({ title, icon, color, onPress }) => (
    <TouchableOpacity style={styles.quickAction} onPress={onPress}>
      <View style={[styles.quickActionIcon, { backgroundColor: color }]}>
        <Ionicons name={icon} size={24} color="#fff" />
      </View>
      <Text style={styles.quickActionText}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.greeting}>
          {getGreeting()}, {user?.firstname}!
        </Text>
        <Text style={styles.subtitle}>
          {user?.role === UserRole.CLIENT && 'Espace Client'}
          {user?.role === UserRole.COMMERCIAL && 'Espace Commercial'}
          {user?.role === UserRole.ADMIN && 'Espace Administrateur'}
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>Aperçu</Text>

        <StatCard
          title="Produits"
          value={stats.totalProducts}
          icon="grid-outline"
          color="#4CAF50"
        />

        <StatCard
          title="Devis"
          value={stats.totalQuotes}
          icon="document-text-outline"
          color="#2196F3"
        />

        <StatCard
          title="Devis en attente"
          value={stats.pendingQuotes}
          icon="time-outline"
          color="#FF9800"
        />

        {user?.role === UserRole.COMMERCIAL && (
          <StatCard
            title="Rapports de visite"
            value={stats.totalReports || 0}
            icon="clipboard-outline"
            color="#9C27B0"
          />
        )}
      </View>

      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Actions rapides</Text>

        <View style={styles.quickActionsGrid}>
          <QuickAction
            title="Voir les produits"
            icon="grid"
            color="#4CAF50"
            onPress={() => navigation.navigate('Products')}
          />

          <QuickAction
            title="Catégories"
            icon="folder"
            color="#FF9800"
            onPress={() => navigation.navigate('Categories')}
          />

          <QuickAction
            title="Mes devis"
            icon="document-text"
            color="#2196F3"
            onPress={() => navigation.navigate('Quotes')}
          />

          {user?.role === UserRole.COMMERCIAL && (
            <QuickAction
              title="Nouveau rapport"
              icon="add-circle"
              color="#9C27B0"
              onPress={() => navigation.navigate('CreateSalesReport')}
            />
          )}

          <QuickAction
            title="Mon profil"
            icon="person"
            color="#607D8B"
            onPress={() => navigation.navigate('Profile')}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#006db7',
    padding: 20,
    paddingTop: 40,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#e3f2fd',
  },
  statsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statText: {
    flex: 1,
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  statTitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  quickActionsContainer: {
    padding: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
});

export default HomeScreen;
