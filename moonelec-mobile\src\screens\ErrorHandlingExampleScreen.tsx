import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { handleError, handleSuccess, handleWarning, handleLoading, dismissLoading, showConfirmation } from '../utils/errorHandler';
import ApiService from '../services/api';

// Example screen showing how to use French error handling in mobile app
const ErrorHandlingExampleScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  // Example: API call with error handling
  const handleApiCall = async () => {
    handleLoading('Chargement des produits...');
    setIsLoading(true);

    try {
      // This will automatically show French error messages if it fails
      const response = await ApiService.get('/products', {}, true);
      
      dismissLoading();
      handleSuccess('SAVE_SUCCESS', 'Produits chargés avec succès !');
      console.log('Products loaded:', response);
    } catch (error) {
      dismissLoading();
      // Error is already handled by ApiService with French messages
      console.error('Failed to load products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Example: File upload with error handling
  const handleFileUpload = async () => {
    try {
      // Simulate file selection
      const mockFile = {
        uri: 'file://path/to/file.pdf',
        type: 'application/pdf',
        name: 'document.pdf',
        size: 30 * 1024 * 1024, // 30MB - too large
      };

      // This will trigger FILE_TOO_LARGE error
      await ApiService.uploadFile('/upload', mockFile, {}, true);
      handleSuccess('UPLOAD_SUCCESS', 'Fichier téléchargé avec succès !');
    } catch (error) {
      // Error is already handled by ApiService
      console.error('Upload failed:', error);
    }
  };

  // Example: Form validation with French errors
  const handleFormSubmit = async () => {
    try {
      const formData = {
        email: 'invalid-email', // Invalid email format
        password: '123', // Too short
      };

      // Validate required fields
      if (!formData.email.includes('@')) {
        handleError({ message: 'INVALID_EMAIL' }, 'Validation du formulaire');
        return;
      }

      if (formData.password.length < 8) {
        handleError({ message: 'PASSWORD_TOO_SHORT' }, 'Validation du formulaire');
        return;
      }

      // Submit form
      const result = await ApiService.post('/users', formData, true);
      handleSuccess('SAVE_SUCCESS', 'Utilisateur créé avec succès !');
      console.log('User created:', result);
    } catch (error) {
      // Error is already handled by ApiService
      console.error('Form submission failed:', error);
    }
  };

  // Example: Delete with confirmation
  const handleDelete = async () => {
    showConfirmation(
      'Confirmer la suppression',
      'Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.',
      async () => {
        try {
          await ApiService.delete('/items/test-item-123', true);
          handleSuccess('DELETE_SUCCESS', 'Élément supprimé avec succès !');
        } catch (error) {
          // Error is already handled by ApiService
          console.error('Delete failed:', error);
        }
      },
      () => {
        console.log('Delete cancelled');
      }
    );
  };

  // Example: Network error simulation
  const simulateNetworkError = () => {
    const networkError = {
      message: 'Network request failed',
      code: 'NETWORK_ERROR'
    };
    handleError(networkError, 'Test de réseau');
  };

  // Example: Different error types
  const simulateErrors = () => {
    // Unauthorized error
    setTimeout(() => {
      handleError({ status: 401 }, 'Test d\'authentification');
    }, 1000);

    // Validation error
    setTimeout(() => {
      handleError({ status: 422, message: 'Validation failed' }, 'Test de validation');
    }, 2000);

    // Server error
    setTimeout(() => {
      handleError({ status: 500 }, 'Test du serveur');
    }, 3000);
  };

  // Example: Permission error
  const simulatePermissionError = () => {
    handleError(
      { message: 'Camera permission denied' },
      'Permissions',
      'Permission d\'accès à la caméra requise pour prendre des photos.'
    );
  };

  const ExampleButton: React.FC<{
    title: string;
    onPress: () => void;
    color?: string;
    icon?: string;
    disabled?: boolean;
  }> = ({ title, onPress, color = '#3b82f6', icon, disabled = false }) => (
    <TouchableOpacity
      style={[styles.button, { backgroundColor: disabled ? '#9ca3af' : color }]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <View style={styles.buttonContent}>
        {icon && (
          <Ionicons name={icon as any} size={20} color="#ffffff" style={styles.buttonIcon} />
        )}
        <Text style={styles.buttonText}>{title}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Exemples de Gestion d'Erreurs</Text>
        <Text style={styles.subtitle}>Messages en français avec Toast</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Appels API</Text>
        <ExampleButton
          title={isLoading ? 'Chargement...' : 'Charger les Produits'}
          onPress={handleApiCall}
          color="#3b82f6"
          icon="download"
          disabled={isLoading}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Téléchargement de Fichier</Text>
        <ExampleButton
          title="Tester Téléchargement (Fichier Trop Gros)"
          onPress={handleFileUpload}
          color="#8b5cf6"
          icon="cloud-upload"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Validation de Formulaire</Text>
        <ExampleButton
          title="Tester Validation (Email Invalide)"
          onPress={handleFormSubmit}
          color="#10b981"
          icon="checkmark-circle"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Suppression avec Confirmation</Text>
        <ExampleButton
          title="Supprimer un Élément"
          onPress={handleDelete}
          color="#ef4444"
          icon="trash"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Tests d'Erreurs</Text>
        <View style={styles.buttonRow}>
          <ExampleButton
            title="Erreur Réseau"
            onPress={simulateNetworkError}
            color="#f59e0b"
            icon="wifi"
          />
          <ExampleButton
            title="Erreurs Multiples"
            onPress={simulateErrors}
            color="#8b5cf6"
            icon="alert-circle"
          />
        </View>
        <ExampleButton
          title="Erreur de Permission"
          onPress={simulatePermissionError}
          color="#f97316"
          icon="camera"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Messages de Succès</Text>
        <View style={styles.buttonRow}>
          <ExampleButton
            title="Connexion Réussie"
            onPress={() => handleSuccess('LOGIN_SUCCESS')}
            color="#10b981"
            icon="log-in"
          />
          <ExampleButton
            title="Sauvegarde Réussie"
            onPress={() => handleSuccess('SAVE_SUCCESS', 'Données sauvegardées avec succès !')}
            color="#10b981"
            icon="save"
          />
        </View>
      </View>

      <View style={styles.infoBox}>
        <Text style={styles.infoTitle}>Comment utiliser :</Text>
        <Text style={styles.infoText}>
          • handleError(error, context, customMessage) - Affiche une erreur en français{'\n'}
          • handleSuccess(messageKey, customMessage) - Affiche un message de succès{'\n'}
          • handleWarning(messageKey, customMessage) - Affiche un avertissement{'\n'}
          • showConfirmation(title, message, onConfirm) - Dialogue de confirmation{'\n'}
          • ApiService avec gestion d'erreurs automatique
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  section: {
    marginBottom: 25,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 15,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  infoBox: {
    backgroundColor: '#eff6ff',
    borderRadius: 12,
    padding: 20,
    marginTop: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#3b82f6',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#1e40af',
    lineHeight: 20,
  },
});

export default ErrorHandlingExampleScreen;
