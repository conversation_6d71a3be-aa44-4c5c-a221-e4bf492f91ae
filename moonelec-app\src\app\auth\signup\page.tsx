'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Logo from '@/components/ui/Logo';
import SignUpForm from '@/components/auth/SignUpForm';
import Card from '@/components/ui/Card';
import { useAuth } from '@/hooks/useAuth';

export default function SignUpPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, redirectToDashboard } = useAuth();

  useEffect(() => {
    // Si l'utilisateur est déjà authentifié, le rediriger vers son tableau de bord
    if (!isLoading && isAuthenticated) {
      redirectToDashboard();
    }
  }, [isLoading, isAuthenticated, redirectToDashboard]);

  // Ne pas afficher la page si l'utilisateur est déjà authentifié
  if (isLoading || isAuthenticated) {
    return null;
  }
  const benefits = [
    "Accès à plus de 300 000 références produits",
    "Suivi de commandes en temps réel",
    "Devis personnalisés pour vos projets",
    "Support technique dédié",
    "Offres exclusives réservées aux membres"
  ];

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side - Image */}
      <div className="hidden md:block md:w-1/2 relative bg-secondary">
        <div className="absolute inset-0 bg-gradient-to-br from-secondary to-secondary/70"></div>
        <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-16">
          <h2 className="text-3xl font-bold mb-6 text-center">
            Rejoignez la Communauté Moonelec
          </h2>
          <p className="text-lg text-center mb-8">
            Créez votre compte pour accéder à nos services exclusifs et découvrir notre vaste gamme de produits électriques de qualité.
          </p>
          <div className="space-y-6 w-full max-w-md">
            <Card className="bg-white/10 backdrop-blur-sm p-6 border-none">
              <h3 className="font-semibold mb-4 text-xl">Avantages de l'inscription</h3>
              <ul className="space-y-3">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">✓</span>
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </Card>
          </div>
        </div>
      </div>

      {/* Right Side - Form */}
      <div className="w-full md:w-1/2 flex flex-col justify-center items-center p-8 md:p-16">
        <div className="w-full max-w-md">
          <div className="mb-8 flex justify-center md:justify-start">
            <Logo width={180} height={60} />
          </div>

          <SignUpForm />
        </div>
      </div>
    </div>
  );
}
