import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { aiService } from '@/lib/ai-service';

// Types pour les données extraites
interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
}

// POST /api/extract-pdf - Extract product data from PDF using AI
export async function POST(req: NextRequest) {
  try {
    // Vérification de l'authentification
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if AI service is available
    const aiStatus = await aiService.isAvailable();
    if (!aiStatus.available) {
      return NextResponse.json(
        {
          error: `AI service not available: ${aiStatus.error}`,
          service: aiStatus.service,
          configured: false
        },
        { status: 503 }
      );
    }

    console.log(`🤖 Using AI service: ${aiStatus.service}`);

    // Récupération du fichier PDF
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No PDF file provided' },
        { status: 400 }
      );
    }

    // Vérification du type de fichier
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Vérification de la taille du fichier (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    console.log('📄 Processing PDF:', file.name, 'Size:', file.size);

    // Conversion du fichier en buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extraction du texte du PDF
    console.log('🔍 Extracting text from PDF...');

    let extractedText = '';

    try {
      // Use pdfjs-dist for PDF parsing
      const pdfjsLib = await import('pdfjs-dist');

      // Load the PDF document
      const loadingTask = pdfjsLib.getDocument({
        data: buffer,
        useSystemFonts: true,
      });

      const pdfDocument = await loadingTask.promise;

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
        const page = await pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        extractedText += pageText + '\n';
      }

    } catch (pdfError) {
      console.error('❌ PDF parsing error:', pdfError);
      return NextResponse.json(
        { error: 'Failed to parse PDF file. Please ensure it contains readable text.' },
        { status: 400 }
      );
    }

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text could be extracted from the PDF' },
        { status: 400 }
      );
    }

    console.log('📝 Extracted text length:', extractedText.length);

    // Appel au service IA (Ollama ou OpenAI)
    console.log('🤖 Calling AI service for extraction...');
    let extractedProducts;
    try {
      extractedProducts = await aiService.extractProductData(extractedText);
    } catch (aiError) {
      console.error('❌ AI Service Error:', aiError);
      const errorMessage = aiError instanceof Error ? aiError.message : 'Unknown AI error';
      return NextResponse.json(
        { error: `AI extraction failed: ${errorMessage}` },
        { status: 500 }
      );
    }

    if (!extractedProducts || extractedProducts.length === 0) {
      return NextResponse.json(
        { error: 'No products could be extracted from the PDF' },
        { status: 400 }
      );
    }

    // Use the first extracted product (already parsed by AI service)
    const extractedData = extractedProducts[0];
    console.log('🤖 AI extracted product:', extractedData.productName);

    // Validation des données extraites
    if (!extractedData.productName || !extractedData.description) {
      return NextResponse.json(
        { error: 'Insufficient product data extracted' },
        { status: 400 }
      );
    }

    console.log('✅ Successfully extracted product data:', extractedData.productName);

    return NextResponse.json({
      success: true,
      data: extractedData,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        textLength: extractedText.length,
        extractedAt: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('❌ PDF extraction error:', error);

    if (error instanceof Error) {
      // Erreurs spécifiques d'OpenAI
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'OpenAI API configuration error' },
          { status: 500 }
        );
      }

      // Erreurs de parsing PDF
      if (error.message.includes('pdf')) {
        return NextResponse.json(
          { error: 'Failed to parse PDF file' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error during PDF extraction' },
      { status: 500 }
    );
  }
}

// GET /api/extract-pdf - Get extraction status/info
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check AI service availability
    const aiStatus = await aiService.isAvailable();

    return NextResponse.json({
      available: aiStatus.available,
      service: aiStatus.service,
      error: aiStatus.error,
      supportedFormats: ['application/pdf'],
      maxFileSize: '10MB',
      features: [
        'Product name extraction',
        'Reference number extraction',
        'Technical characteristics extraction',
        'Category and brand detection',
        'Free local AI with Ollama/Llama',
        'OpenAI fallback support'
      ]
    });

  } catch (error) {
    console.error('Error getting extraction info:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
