import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import pdf from 'pdf-parse';
import OpenAI from 'openai';
import { authOptions } from '@/lib/auth-options';

// Types pour les données extraites
interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
}

// Configuration OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// POST /api/extract-pdf - Extract product data from PDF using AI
export async function POST(req: NextRequest) {
  try {
    // Vérification de l'authentification
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Vérification de la clé API OpenAI
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Récupération du fichier PDF
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No PDF file provided' },
        { status: 400 }
      );
    }

    // Vérification du type de fichier
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Vérification de la taille du fichier (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    console.log('📄 Processing PDF:', file.name, 'Size:', file.size);

    // Conversion du fichier en buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extraction du texte du PDF
    console.log('🔍 Extracting text from PDF...');
    const pdfData = await pdf(buffer);
    const extractedText = pdfData.text;

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text could be extracted from the PDF' },
        { status: 400 }
      );
    }

    console.log('📝 Extracted text length:', extractedText.length);

    // Prompt pour l'IA
    const systemPrompt = `
You are a product sheet parser. Extract product information from the provided text and return ONLY a valid JSON object.

Required JSON format:
{
  "productName": "string",
  "reference": "string or array of strings",
  "description": "string",
  "characteristics": { "key": "value", ... }
}

Rules:
- Return ONLY the JSON object, no other text
- Do not wrap in markdown code blocks
- If multiple references exist, use an array
- Include all technical specifications in characteristics
- Use descriptive keys for characteristics
- If information is missing, use empty string or empty object
`;

    const userPrompt = `Texte extrait du PDF :\n\n${extractedText}`;

    // Appel à l'API OpenAI
    console.log('🤖 Calling OpenAI API...');
    let completion;
    try {
      completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 1000,
        temperature: 0.1, // Faible température pour plus de précision
      });
    } catch (openaiError) {
      console.error('❌ OpenAI API Error:', openaiError);
      const errorMessage = openaiError instanceof Error ? openaiError.message : 'Unknown OpenAI error';
      return NextResponse.json(
        { error: `OpenAI API Error: ${errorMessage}` },
        { status: 500 }
      );
    }

    const aiResponse = completion.choices[0]?.message?.content;

    if (!aiResponse) {
      return NextResponse.json(
        { error: 'No response from AI' },
        { status: 500 }
      );
    }

    console.log('🤖 AI Response:', aiResponse);

    // Extraction du JSON de la réponse
    let extractedData: ExtractedProductData;
    try {
      // Nettoyer la réponse (enlever les espaces, retours à la ligne au début/fin)
      let cleanedResponse = aiResponse.trim();

      // Si la réponse commence par ```json, on l'enlève
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      }

      // Si la réponse commence par ```, on l'enlève
      if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Recherche du JSON dans la réponse
      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error('❌ No JSON found in AI response. Full response:', aiResponse);
        return NextResponse.json(
          {
            error: 'No valid JSON found in AI response',
            debug: {
              aiResponse: aiResponse.substring(0, 500) + (aiResponse.length > 500 ? '...' : ''),
              cleanedResponse: cleanedResponse.substring(0, 500) + (cleanedResponse.length > 500 ? '...' : '')
            }
          },
          { status: 500 }
        );
      }

      const jsonString = jsonMatch[0];
      console.log('🔍 Extracted JSON string:', jsonString);

      extractedData = JSON.parse(jsonString);

      // Validation basique des données extraites
      if (!extractedData.productName && !extractedData.description) {
        throw new Error('Invalid extracted data: missing required fields');
      }

    } catch (parseError) {
      console.error('❌ JSON parsing error:', parseError);
      console.error('❌ AI Response that failed to parse:', aiResponse);
      return NextResponse.json(
        {
          error: 'Failed to parse AI response as JSON',
          debug: {
            parseError: parseError instanceof Error ? parseError.message : 'Unknown error',
            aiResponse: aiResponse.substring(0, 500) + (aiResponse.length > 500 ? '...' : '')
          }
        },
        { status: 500 }
      );
    }

    // Validation des données extraites
    if (!extractedData.productName || !extractedData.description) {
      return NextResponse.json(
        { error: 'Insufficient product data extracted' },
        { status: 400 }
      );
    }

    console.log('✅ Successfully extracted product data:', extractedData.productName);

    return NextResponse.json({
      success: true,
      data: extractedData,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        textLength: extractedText.length,
        extractedAt: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('❌ PDF extraction error:', error);

    if (error instanceof Error) {
      // Erreurs spécifiques d'OpenAI
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'OpenAI API configuration error' },
          { status: 500 }
        );
      }

      // Erreurs de parsing PDF
      if (error.message.includes('pdf')) {
        return NextResponse.json(
          { error: 'Failed to parse PDF file' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error during PDF extraction' },
      { status: 500 }
    );
  }
}

// GET /api/extract-pdf - Get extraction status/info
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json({
      available: !!process.env.OPENAI_API_KEY,
      supportedFormats: ['application/pdf'],
      maxFileSize: '10MB',
      features: [
        'Product name extraction',
        'Reference number extraction',
        'Technical characteristics extraction',
        'Category and brand detection',
        'Price extraction'
      ]
    });

  } catch (error) {
    console.error('Error getting extraction info:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
