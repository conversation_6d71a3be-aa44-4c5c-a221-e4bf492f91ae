import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import pdf from 'pdf-parse';
import OpenAI from 'openai';
import { authOptions } from '@/lib/auth-options';

// Types pour les données extraites
interface ExtractedProductData {
  productName: string;
  reference: string | string[];
  description: string;
  characteristics: Record<string, string>;
  category?: string;
  brand?: string;
  price?: number;
}

// Configuration OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// POST /api/extract-pdf - Extract product data from PDF using AI
export async function POST(req: NextRequest) {
  try {
    // Vérification de l'authentification
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Vérification de la clé API OpenAI
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Récupération du fichier PDF
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No PDF file provided' },
        { status: 400 }
      );
    }

    // Vérification du type de fichier
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Vérification de la taille du fichier (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    console.log('📄 Processing PDF:', file.name, 'Size:', file.size);

    // Conversion du fichier en buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extraction du texte du PDF
    console.log('🔍 Extracting text from PDF...');
    const pdfData = await pdf(buffer);
    const extractedText = pdfData.text;

    if (!extractedText || extractedText.trim().length === 0) {
      return NextResponse.json(
        { error: 'No text could be extracted from the PDF' },
        { status: 400 }
      );
    }

    console.log('📝 Extracted text length:', extractedText.length);

    // Prompt pour l'IA
    const systemPrompt = `
Tu es un expert en extraction de données de fiches techniques de produits électriques.
Analyse le texte suivant extrait d'un PDF et extrais UNIQUEMENT les informations sous format JSON.

Format de réponse requis (JSON uniquement, sans commentaire) :
{
  "productName": "nom du produit",
  "reference": "référence du produit ou array de références",
  "description": "description détaillée du produit",
  "characteristics": {
    "tension": "valeur",
    "courant": "valeur",
    "puissance": "valeur",
    "dimensions": "valeur",
    "poids": "valeur",
    "matériau": "valeur",
    "certification": "valeur",
    "température": "valeur",
    "protection": "valeur"
  },
  "category": "catégorie du produit si mentionnée",
  "brand": "marque du produit si mentionnée",
  "price": "prix en euros si mentionné (nombre uniquement)"
}

Instructions importantes :
- Extrais TOUTES les caractéristiques techniques trouvées
- Si plusieurs références existent, utilise un array
- Pour les caractéristiques, utilise des clés descriptives en français
- Si une information n'est pas trouvée, omets la clé
- Réponds UNIQUEMENT avec le JSON, sans texte supplémentaire
`;

    const userPrompt = `Texte extrait du PDF :\n\n${extractedText}`;

    // Appel à l'API OpenAI
    console.log('🤖 Calling OpenAI API...');
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: 1000,
      temperature: 0.1, // Faible température pour plus de précision
    });

    const aiResponse = completion.choices[0]?.message?.content;

    if (!aiResponse) {
      return NextResponse.json(
        { error: 'No response from AI' },
        { status: 500 }
      );
    }

    console.log('🤖 AI Response:', aiResponse);

    // Extraction du JSON de la réponse
    let extractedData: ExtractedProductData;
    try {
      // Recherche du JSON dans la réponse
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      extractedData = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      console.error('❌ JSON parsing error:', parseError);
      return NextResponse.json(
        { error: 'Failed to parse AI response as JSON' },
        { status: 500 }
      );
    }

    // Validation des données extraites
    if (!extractedData.productName || !extractedData.description) {
      return NextResponse.json(
        { error: 'Insufficient product data extracted' },
        { status: 400 }
      );
    }

    console.log('✅ Successfully extracted product data:', extractedData.productName);

    return NextResponse.json({
      success: true,
      data: extractedData,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        textLength: extractedText.length,
        extractedAt: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('❌ PDF extraction error:', error);
    
    if (error instanceof Error) {
      // Erreurs spécifiques d'OpenAI
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'OpenAI API configuration error' },
          { status: 500 }
        );
      }
      
      // Erreurs de parsing PDF
      if (error.message.includes('pdf')) {
        return NextResponse.json(
          { error: 'Failed to parse PDF file' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error during PDF extraction' },
      { status: 500 }
    );
  }
}

// GET /api/extract-pdf - Get extraction status/info
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json({
      available: !!process.env.OPENAI_API_KEY,
      supportedFormats: ['application/pdf'],
      maxFileSize: '10MB',
      features: [
        'Product name extraction',
        'Reference number extraction',
        'Technical characteristics extraction',
        'Category and brand detection',
        'Price extraction'
      ]
    });

  } catch (error) {
    console.error('Error getting extraction info:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
