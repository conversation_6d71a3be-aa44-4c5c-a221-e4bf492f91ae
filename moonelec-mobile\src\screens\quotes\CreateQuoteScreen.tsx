import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Product, RootStackParamList } from '../../types';
import ApiService from '../../services/api';

type CreateQuoteRouteProp = RouteProp<RootStackParamList, 'CreateQuote'>;

interface QuoteItem {
  product: Product;
  quantity: number;
}

const CreateQuoteScreen: React.FC = () => {
  const route = useRoute<CreateQuoteRouteProp>();
  const navigation = useNavigation();
  const preselectedProduct = route.params?.preselectedProduct;

  const [items, setItems] = useState<QuoteItem[]>([]);
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (preselectedProduct) {
      loadPreselectedProduct();
    }
  }, [preselectedProduct]);

  const loadPreselectedProduct = async () => {
    if (!preselectedProduct) return;

    try {
      const product = await ApiService.getProduct(preselectedProduct.productId);
      setItems([{
        product,
        quantity: preselectedProduct.quantity
      }]);
    } catch (error) {
      console.error('Error loading preselected product:', error);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await ApiService.getProducts({
        search: searchQuery || undefined,
        take: 50,
      });
      setProducts(response.products);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const addProduct = (product: Product) => {
    const existingIndex = items.findIndex(item => item.product.id === product.id);

    if (existingIndex >= 0) {
      // Product already exists, increase quantity
      const newItems = [...items];
      newItems[existingIndex].quantity += 1;
      setItems(newItems);
    } else {
      // Add new product
      setItems([...items, { product, quantity: 1 }]);
    }

    setShowProductModal(false);
  };

  const updateQuantity = (index: number, quantity: number) => {
    if (quantity <= 0) {
      removeItem(index);
      return;
    }

    const newItems = [...items];
    newItems[index].quantity = quantity;
    setItems(newItems);
  };

  const removeItem = (index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);
  };

  const handleCreateQuote = async () => {
    if (items.length === 0) {
      Alert.alert('Erreur', 'Veuillez ajouter au moins un produit au devis');
      return;
    }

    try {
      setIsLoading(true);

      const quoteData = {
        items: items.map(item => ({
          productId: item.product.id,
          quantity: item.quantity
        })),
        notes: notes.trim() || undefined
      };

      const response = await ApiService.createQuote(quoteData);

      Alert.alert(
        'Succès',
        'Le devis a été créé avec succès',
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.goBack();
              // Optionally navigate to the created quote
              // navigation.navigate('QuoteDetail', { quoteId: response.quote.id });
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error creating quote:', error);
      Alert.alert('Erreur', 'Impossible de créer le devis');
    } finally {
      setIsLoading(false);
    }
  };

  const renderQuoteItem = ({ item, index }: { item: QuoteItem; index: number }) => (
    <View style={styles.quoteItem}>
      <View style={styles.productInfo}>
        {item.product.mainImage ? (
          <Image source={{ uri: item.product.mainImage }} style={styles.productImage} />
        ) : (
          <View style={styles.productImagePlaceholder}>
            <Ionicons name="image-outline" size={24} color="#ccc" />
          </View>
        )}

        <View style={styles.productDetails}>
          <Text style={styles.productName} numberOfLines={2}>
            {item.product.name}
          </Text>
          <Text style={styles.productReference}>Réf: {item.product.reference}</Text>
        </View>
      </View>

      <View style={styles.quantityControls}>
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(index, item.quantity - 1)}
        >
          <Ionicons name="remove" size={16} color="#006db7" />
        </TouchableOpacity>

        <Text style={styles.quantityText}>{item.quantity}</Text>

        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(index, item.quantity + 1)}
        >
          <Ionicons name="add" size={16} color="#006db7" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => removeItem(index)}
        >
          <Ionicons name="trash-outline" size={16} color="#F44336" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderProductItem = ({ item }: { item: Product }) => (
    <TouchableOpacity
      style={styles.productModalItem}
      onPress={() => addProduct(item)}
    >
      {item.mainImage ? (
        <Image source={{ uri: item.mainImage }} style={styles.modalProductImage} />
      ) : (
        <View style={styles.modalProductImagePlaceholder}>
          <Ionicons name="image-outline" size={20} color="#ccc" />
        </View>
      )}

      <View style={styles.modalProductInfo}>
        <Text style={styles.modalProductName} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={styles.modalProductReference}>Réf: {item.reference}</Text>
      </View>

      <Ionicons name="add-circle" size={24} color="#006db7" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quote Items */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Produits ({items.length})</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => {
                setShowProductModal(true);
                loadProducts();
              }}
            >
              <Ionicons name="add" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          {items.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="document-text-outline" size={48} color="#ccc" />
              <Text style={styles.emptyText}>Aucun produit ajouté</Text>
              <Text style={styles.emptySubtext}>Appuyez sur + pour ajouter des produits</Text>
            </View>
          ) : (
            <FlatList
              data={items}
              renderItem={renderQuoteItem}
              keyExtractor={(item, index) => `${item.product.id}-${index}`}
              scrollEnabled={false}
            />
          )}
        </View>

        {/* Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes (optionnel)</Text>
          <TextInput
            style={styles.notesInput}
            value={notes}
            onChangeText={setNotes}
            placeholder="Ajoutez des notes pour ce devis..."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      {/* Create Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.createButton, isLoading && styles.createButtonDisabled]}
          onPress={handleCreateQuote}
          disabled={isLoading || items.length === 0}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name="document-text" size={20} color="#fff" />
              <Text style={styles.createButtonText}>Créer le devis</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Product Selection Modal */}
      <Modal
        visible={showProductModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Ajouter un produit</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowProductModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalSearchContainer}>
            <TextInput
              style={styles.modalSearchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Rechercher un produit..."
              onSubmitEditing={loadProducts}
              returnKeyType="search"
            />
            <TouchableOpacity style={styles.modalSearchButton} onPress={loadProducts}>
              <Ionicons name="search" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          <FlatList
            data={products}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            style={styles.modalProductList}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 8,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#006db7',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  quoteItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  productInfo: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  productImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  productImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  productReference: {
    fontSize: 12,
    color: '#666',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#006db7',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginHorizontal: 12,
    minWidth: 20,
    textAlign: 'center',
  },
  removeButton: {
    marginLeft: 12,
    padding: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    minHeight: 100,
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  createButton: {
    backgroundColor: '#006db7',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCloseButton: {
    padding: 8,
  },
  modalSearchContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
  },
  modalSearchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    marginRight: 8,
  },
  modalSearchButton: {
    backgroundColor: '#006db7',
    borderRadius: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalProductList: {
    flex: 1,
    padding: 16,
  },
  productModalItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modalProductImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  modalProductImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  modalProductInfo: {
    flex: 1,
  },
  modalProductName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  modalProductReference: {
    fontSize: 14,
    color: '#666',
  },
});

export default CreateQuoteScreen;
