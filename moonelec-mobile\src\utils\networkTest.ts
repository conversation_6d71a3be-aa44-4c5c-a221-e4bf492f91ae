import { getApiUrl } from '../config/api';

// Utilitaire pour tester la connectivité réseau
export const testNetworkConnectivity = async (baseUrl?: string): Promise<boolean> => {
  try {
    // Use dynamic API URL if no baseUrl provided
    const testUrl = baseUrl || getApiUrl().replace('/api', '');
    console.log('🌐 Testing network connectivity to:', testUrl);

    const response = await fetch(`${testUrl}/api/mobile/test`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      timeout: 5000,
    });

    console.log('✅ Network test response:', response.status);
    return response.ok;
  } catch (error) {
    console.error('❌ Network test failed:', error);
    return false;
  }
};

export const getNetworkInfo = () => {
  console.log('📱 Network Info:');
  console.log('   - User Agent:', navigator.userAgent);
  console.log('   - Platform:', navigator.platform);
  console.log('   - Online:', navigator.onLine);
};
