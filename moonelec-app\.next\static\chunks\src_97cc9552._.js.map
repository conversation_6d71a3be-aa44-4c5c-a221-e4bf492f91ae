{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useFileUpload.ts"], "sourcesContent": ["import { useState } from 'react';\n\ninterface UploadOptions {\n  directory?: string;\n  onSuccess?: (url: string) => void;\n  onError?: (error: Error) => void;\n}\n\nexport function useFileUpload() {\n  const [isUploading, setIsUploading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [error, setError] = useState<Error | null>(null);\n\n  const uploadFile = async (file: File, options: UploadOptions = {}) => {\n    const { directory = 'uploads', onSuccess, onError } = options;\n    \n    setIsUploading(true);\n    setProgress(0);\n    setError(null);\n    \n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('directory', directory);\n      \n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setProgress((prev) => {\n          const newProgress = prev + Math.random() * 10;\n          return newProgress > 90 ? 90 : newProgress;\n        });\n      }, 200);\n      \n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n      \n      clearInterval(progressInterval);\n      \n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to upload file');\n      }\n      \n      const data = await response.json();\n      setProgress(100);\n      \n      if (onSuccess) {\n        onSuccess(data.url);\n      }\n      \n      return data.url;\n    } catch (err: any) {\n      setError(err);\n      \n      if (onError) {\n        onError(err);\n      }\n      \n      throw err;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const uploadBase64 = async (base64Data: string, options: UploadOptions = {}) => {\n    const { directory = 'uploads', onSuccess, onError } = options;\n    \n    setIsUploading(true);\n    setProgress(0);\n    setError(null);\n    \n    try {\n      // Simulate progress\n      const progressInterval = setInterval(() => {\n        setProgress((prev) => {\n          const newProgress = prev + Math.random() * 10;\n          return newProgress > 90 ? 90 : newProgress;\n        });\n      }, 200);\n      \n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          base64: base64Data,\n          directory,\n        }),\n      });\n      \n      clearInterval(progressInterval);\n      \n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to upload file');\n      }\n      \n      const data = await response.json();\n      setProgress(100);\n      \n      if (onSuccess) {\n        onSuccess(data.url);\n      }\n      \n      return data.url;\n    } catch (err: any) {\n      setError(err);\n      \n      if (onError) {\n        onError(err);\n      }\n      \n      throw err;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return {\n    uploadFile,\n    uploadBase64,\n    isUploading,\n    progress,\n    error,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAQO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,aAAa,OAAO,MAAY,UAAyB,CAAC,CAAC;QAC/D,MAAM,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEtD,eAAe;QACf,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,aAAa;YAE7B,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,YAAY,CAAC;oBACX,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK;oBAC3C,OAAO,cAAc,KAAK,KAAK;gBACjC;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,cAAc;YAEd,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;YAEZ,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;YACpB;YAEA,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS;YAET,IAAI,SAAS;gBACX,QAAQ;YACV;YAEA,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,OAAO,YAAoB,UAAyB,CAAC,CAAC;QACzE,MAAM,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEtD,eAAe;QACf,YAAY;QACZ,SAAS;QAET,IAAI;YACF,oBAAoB;YACpB,MAAM,mBAAmB,YAAY;gBACnC,YAAY,CAAC;oBACX,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK;oBAC3C,OAAO,cAAc,KAAK,KAAK;gBACjC;YACF,GAAG;YAEH,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;gBACF;YACF;YAEA,cAAc;YAEd,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;YAEZ,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;YACpB;YAEA,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,KAAU;YACjB,SAAS;YAET,IAAI,SAAS;gBACX,QAAQ;YACV;YAEA,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GAxHgB", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { FaUpload, FaImage, FaTrash, FaSpinner } from 'react-icons/fa';\nimport { useFileUpload } from '@/hooks/useFileUpload';\n\ninterface ImageUploadProps {\n  initialImage?: string;\n  onImageChange: (imageUrl: string | null) => void;\n  directory?: string;\n  className?: string;\n  aspectRatio?: 'square' | '16/9' | '4/3' | 'auto';\n  maxSizeMB?: number;\n  label?: string;\n  required?: boolean;\n}\n\nexport default function ImageUpload({\n  initialImage,\n  onImageChange,\n  directory = 'uploads',\n  className = '',\n  aspectRatio = 'square',\n  maxSizeMB = 5,\n  label = 'Image',\n  required = false,\n}: ImageUploadProps) {\n  const [imageUrl, setImageUrl] = useState<string | null>(initialImage || null);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { uploadFile, isUploading, progress } = useFileUpload();\n\n  const aspectRatioClass = {\n    square: 'aspect-square',\n    '16/9': 'aspect-video',\n    '4/3': 'aspect-[4/3]',\n    'auto': '',\n  }[aspectRatio];\n\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n\n    const file = files[0];\n    \n    // Check file size\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSizeMB) {\n      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);\n      return;\n    }\n    \n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image');\n      return;\n    }\n    \n    setError(null);\n    \n    try {\n      const url = await uploadFile(file, {\n        directory,\n        onSuccess: (url) => {\n          setImageUrl(url);\n          onImageChange(url);\n        },\n        onError: (err) => {\n          setError(err.message);\n        },\n      });\n    } catch (err) {\n      // Error is handled by the hook\n    }\n  };\n\n  const handleRemoveImage = () => {\n    setImageUrl(null);\n    onImageChange(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    \n    const files = e.dataTransfer.files;\n    if (!files || files.length === 0) return;\n    \n    const file = files[0];\n    \n    // Check file size\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSizeMB) {\n      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);\n      return;\n    }\n    \n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      setError('Veuillez sélectionner un fichier image');\n      return;\n    }\n    \n    setError(null);\n    \n    try {\n      const url = await uploadFile(file, {\n        directory,\n        onSuccess: (url) => {\n          setImageUrl(url);\n          onImageChange(url);\n        },\n        onError: (err) => {\n          setError(err.message);\n        },\n      });\n    } catch (err) {\n      // Error is handled by the hook\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n  };\n\n  return (\n    <div className={className}>\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n        {label} {required && <span className=\"text-red-500\">*</span>}\n      </label>\n      \n      <div\n        className={`relative border-2 border-dashed rounded-lg ${\n          error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n        } ${aspectRatioClass} overflow-hidden`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n      >\n        {imageUrl ? (\n          <div className=\"relative w-full h-full\">\n            <Image\n              src={imageUrl}\n              alt=\"Uploaded image\"\n              fill\n              style={{ objectFit: 'cover' }}\n              className=\"w-full h-full\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center\">\n              <motion.button\n                type=\"button\"\n                onClick={handleRemoveImage}\n                className=\"p-2 bg-red-600 text-white rounded-full\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <FaTrash />\n              </motion.button>\n            </div>\n          </div>\n        ) : (\n          <div className=\"flex flex-col items-center justify-center p-6 h-full\">\n            {isUploading ? (\n              <div className=\"text-center\">\n                <FaSpinner className=\"animate-spin text-3xl text-primary mx-auto mb-2\" />\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Téléchargement en cours... {Math.round(progress)}%\n                </p>\n              </div>\n            ) : (\n              <>\n                <FaImage className=\"text-3xl text-gray-400 mb-2\" />\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 text-center mb-2\">\n                  Glissez-déposez une image ici ou cliquez pour sélectionner\n                </p>\n                <p className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n                  Formats acceptés: JPG, PNG, GIF, WebP (max {maxSizeMB} MB)\n                </p>\n              </>\n            )}\n          </div>\n        )}\n        \n        <input\n          type=\"file\"\n          ref={fileInputRef}\n          onChange={handleFileChange}\n          accept=\"image/*\"\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n          disabled={isUploading}\n        />\n      </div>\n      \n      {error && (\n        <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAmBe,SAAS,YAAY,EAClC,YAAY,EACZ,aAAa,EACb,YAAY,SAAS,EACrB,YAAY,EAAE,EACd,cAAc,QAAQ,EACtB,YAAY,CAAC,EACb,QAAQ,OAAO,EACf,WAAW,KAAK,EACC;;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,gBAAgB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE1D,MAAM,mBAAmB;QACvB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,CAAC,CAAC,YAAY;IAEd,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,WAAW;YAC1B,SAAS,CAAC,0CAA0C,EAAE,UAAU,GAAG,CAAC;YACpE;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,MAAM;gBACjC;gBACA,WAAW,CAAC;oBACV,YAAY;oBACZ,cAAc;gBAChB;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;QACZ,+BAA+B;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,WAAW;YAC1B,SAAS,CAAC,0CAA0C,EAAE,UAAU,GAAG,CAAC;YACpE;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,WAAW,MAAM;gBACjC;gBACA,WAAW,CAAC;oBACV,YAAY;oBACZ,cAAc;gBAChB;gBACA,SAAS,CAAC;oBACR,SAAS,IAAI,OAAO;gBACtB;YACF;QACF,EAAE,OAAO,KAAK;QACZ,+BAA+B;QACjC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAW;;0BACd,6LAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,6LAAC;gBACC,WAAW,CAAC,2CAA2C,EACrD,QAAQ,mBAAmB,uCAC5B,CAAC,EAAE,iBAAiB,gBAAgB,CAAC;gBACtC,QAAQ;gBACR,YAAY;;oBAEX,yBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,OAAO;oCAAE,WAAW;gCAAQ;gCAC5B,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEvB,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;6CAKd,6LAAC;wBAAI,WAAU;kCACZ,4BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAE,WAAU;;wCAA2C;wCAC1B,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;iDAIrD;;8CACE,6LAAC,iJAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAA4D;;;;;;8CAGzE,6LAAC;oCAAE,WAAU;;wCAAuD;wCACtB;wCAAU;;;;;;;;;;;;;;kCAOhE,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,UAAU;wBACV,QAAO;wBACP,WAAU;wBACV,UAAU;;;;;;;;;;;;YAIb,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;GAtLwB;;QAawB,gIAAA,CAAA,gBAAa;;;KAbrC", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/ui/MultiImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { FaUpload, FaImage, FaTrash, Fa<PERSON><PERSON><PERSON>, Fa<PERSON>rrowUp, FaArrowDown } from 'react-icons/fa';\nimport { useFileUpload } from '@/hooks/useFileUpload';\n\ninterface ImageItem {\n  url: string;\n  alt?: string;\n  order?: number;\n}\n\ninterface MultiImageUploadProps {\n  initialImages?: ImageItem[];\n  onImagesChange: (images: ImageItem[]) => void;\n  directory?: string;\n  className?: string;\n  maxImages?: number;\n  maxSizeMB?: number;\n  label?: string;\n  required?: boolean;\n}\n\nexport default function MultiImageUpload({\n  initialImages = [],\n  onImagesChange,\n  directory = 'uploads',\n  className = '',\n  maxImages = 10,\n  maxSizeMB = 5,\n  label = 'Images',\n  required = false,\n}: MultiImageUploadProps) {\n  const [images, setImages] = useState<ImageItem[]>(initialImages);\n  const [error, setError] = useState<string | null>(null);\n  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { uploadFile, isUploading, progress } = useFileUpload();\n\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n\n    // Check if adding these files would exceed the maximum\n    if (images.length + files.length > maxImages) {\n      setError(`Vous ne pouvez pas ajouter plus de ${maxImages} images`);\n      return;\n    }\n\n    setError(null);\n\n    // Process each file\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      \n      // Check file size\n      const fileSizeMB = file.size / (1024 * 1024);\n      if (fileSizeMB > maxSizeMB) {\n        setError(`La taille du fichier ${file.name} dépasse la limite de ${maxSizeMB} MB`);\n        continue;\n      }\n      \n      // Check file type\n      if (!file.type.startsWith('image/')) {\n        setError(`Le fichier ${file.name} n'est pas une image`);\n        continue;\n      }\n      \n      try {\n        setUploadingIndex(images.length + i);\n        \n        const url = await uploadFile(file, {\n          directory,\n          onSuccess: (url) => {\n            setImages(prev => {\n              const newImages = [...prev, { url, alt: file.name, order: prev.length }];\n              onImagesChange(newImages);\n              return newImages;\n            });\n          },\n          onError: (err) => {\n            setError(`Erreur lors du téléchargement de ${file.name}: ${err.message}`);\n          },\n        });\n      } catch (err) {\n        // Error is handled by the hook\n      }\n    }\n    \n    setUploadingIndex(null);\n    \n    // Reset the file input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleRemoveImage = (index: number) => {\n    setImages(prev => {\n      const newImages = prev.filter((_, i) => i !== index).map((img, i) => ({\n        ...img,\n        order: i,\n      }));\n      onImagesChange(newImages);\n      return newImages;\n    });\n  };\n\n  const handleMoveImage = (index: number, direction: 'up' | 'down') => {\n    if (\n      (direction === 'up' && index === 0) ||\n      (direction === 'down' && index === images.length - 1)\n    ) {\n      return;\n    }\n\n    setImages(prev => {\n      const newImages = [...prev];\n      const targetIndex = direction === 'up' ? index - 1 : index + 1;\n      \n      // Swap the images\n      [newImages[index], newImages[targetIndex]] = [newImages[targetIndex], newImages[index]];\n      \n      // Update the order\n      newImages.forEach((img, i) => {\n        img.order = i;\n      });\n      \n      onImagesChange(newImages);\n      return newImages;\n    });\n  };\n\n  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    \n    const files = e.dataTransfer.files;\n    if (!files || files.length === 0) return;\n    \n    // Check if adding these files would exceed the maximum\n    if (images.length + files.length > maxImages) {\n      setError(`Vous ne pouvez pas ajouter plus de ${maxImages} images`);\n      return;\n    }\n\n    setError(null);\n\n    // Process each file\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      \n      // Check file size\n      const fileSizeMB = file.size / (1024 * 1024);\n      if (fileSizeMB > maxSizeMB) {\n        setError(`La taille du fichier ${file.name} dépasse la limite de ${maxSizeMB} MB`);\n        continue;\n      }\n      \n      // Check file type\n      if (!file.type.startsWith('image/')) {\n        setError(`Le fichier ${file.name} n'est pas une image`);\n        continue;\n      }\n      \n      try {\n        setUploadingIndex(images.length + i);\n        \n        const url = await uploadFile(file, {\n          directory,\n          onSuccess: (url) => {\n            setImages(prev => {\n              const newImages = [...prev, { url, alt: file.name, order: prev.length }];\n              onImagesChange(newImages);\n              return newImages;\n            });\n          },\n          onError: (err) => {\n            setError(`Erreur lors du téléchargement de ${file.name}: ${err.message}`);\n          },\n        });\n      } catch (err) {\n        // Error is handled by the hook\n      }\n    }\n    \n    setUploadingIndex(null);\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n  };\n\n  return (\n    <div className={className}>\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n        {label} {required && <span className=\"text-red-500\">*</span>}\n      </label>\n      \n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-4\">\n        {images.map((image, index) => (\n          <div key={index} className=\"relative aspect-square rounded-lg overflow-hidden border border-gray-300 dark:border-gray-600\">\n            <Image\n              src={image.url}\n              alt={image.alt || `Image ${index + 1}`}\n              fill\n              style={{ objectFit: 'cover' }}\n              className=\"w-full h-full\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2\">\n              <motion.button\n                type=\"button\"\n                onClick={() => handleMoveImage(index, 'up')}\n                className=\"p-2 bg-blue-600 text-white rounded-full disabled:opacity-50\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                disabled={index === 0}\n              >\n                <FaArrowUp />\n              </motion.button>\n              <motion.button\n                type=\"button\"\n                onClick={() => handleMoveImage(index, 'down')}\n                className=\"p-2 bg-blue-600 text-white rounded-full disabled:opacity-50\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                disabled={index === images.length - 1}\n              >\n                <FaArrowDown />\n              </motion.button>\n              <motion.button\n                type=\"button\"\n                onClick={() => handleRemoveImage(index)}\n                className=\"p-2 bg-red-600 text-white rounded-full\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <FaTrash />\n              </motion.button>\n            </div>\n          </div>\n        ))}\n        \n        {/* Upload placeholder */}\n        {images.length < maxImages && (\n          <div\n            className={`relative aspect-square border-2 border-dashed rounded-lg ${\n              error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n            } overflow-hidden`}\n            onDrop={handleDrop}\n            onDragOver={handleDragOver}\n          >\n            <div className=\"flex flex-col items-center justify-center p-4 h-full\">\n              {uploadingIndex !== null ? (\n                <div className=\"text-center\">\n                  <FaSpinner className=\"animate-spin text-2xl text-primary mx-auto mb-2\" />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Téléchargement... {Math.round(progress)}%\n                  </p>\n                </div>\n              ) : (\n                <>\n                  <FaImage className=\"text-2xl text-gray-400 mb-2\" />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 text-center\">\n                    Ajouter une image\n                  </p>\n                </>\n              )}\n            </div>\n            \n            <input\n              type=\"file\"\n              ref={fileInputRef}\n              onChange={handleFileChange}\n              accept=\"image/*\"\n              multiple\n              className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n              disabled={uploadingIndex !== null}\n            />\n          </div>\n        )}\n      </div>\n      \n      <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n        {images.length} / {maxImages} images (max {maxSizeMB} MB par image)\n      </div>\n      \n      {error && (\n        <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAyBe,SAAS,iBAAiB,EACvC,gBAAgB,EAAE,EAClB,cAAc,EACd,YAAY,SAAS,EACrB,YAAY,EAAE,EACd,YAAY,EAAE,EACd,YAAY,CAAC,EACb,QAAQ,QAAQ,EAChB,WAAW,KAAK,EACM;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE1D,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,uDAAuD;QACvD,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM,GAAG,WAAW;YAC5C,SAAS,CAAC,mCAAmC,EAAE,UAAU,OAAO,CAAC;YACjE;QACF;QAEA,SAAS;QAET,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,kBAAkB;YAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;YAC3C,IAAI,aAAa,WAAW;gBAC1B,SAAS,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,sBAAsB,EAAE,UAAU,GAAG,CAAC;gBACjF;YACF;YAEA,kBAAkB;YAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,SAAS,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC;gBACtD;YACF;YAEA,IAAI;gBACF,kBAAkB,OAAO,MAAM,GAAG;gBAElC,MAAM,MAAM,MAAM,WAAW,MAAM;oBACjC;oBACA,WAAW,CAAC;wBACV,UAAU,CAAA;4BACR,MAAM,YAAY;mCAAI;gCAAM;oCAAE;oCAAK,KAAK,KAAK,IAAI;oCAAE,OAAO,KAAK,MAAM;gCAAC;6BAAE;4BACxE,eAAe;4BACf,OAAO;wBACT;oBACF;oBACA,SAAS,CAAC;wBACR,SAAS,CAAC,iCAAiC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE;oBAC1E;gBACF;YACF,EAAE,OAAO,KAAK;YACZ,+BAA+B;YACjC;QACF;QAEA,kBAAkB;QAElB,uBAAuB;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,CAAA;YACR,MAAM,YAAY,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,IAAM,CAAC;oBACpE,GAAG,GAAG;oBACN,OAAO;gBACT,CAAC;YACD,eAAe;YACf,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC,OAAe;QACtC,IACE,AAAC,cAAc,QAAQ,UAAU,KAChC,cAAc,UAAU,UAAU,OAAO,MAAM,GAAG,GACnD;YACA;QACF;QAEA,UAAU,CAAA;YACR,MAAM,YAAY;mBAAI;aAAK;YAC3B,MAAM,cAAc,cAAc,OAAO,QAAQ,IAAI,QAAQ;YAE7D,kBAAkB;YAClB,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,YAAY,CAAC,GAAG;gBAAC,SAAS,CAAC,YAAY;gBAAE,SAAS,CAAC,MAAM;aAAC;YAEvF,mBAAmB;YACnB,UAAU,OAAO,CAAC,CAAC,KAAK;gBACtB,IAAI,KAAK,GAAG;YACd;YAEA,eAAe;YACf,OAAO;QACT;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,uDAAuD;QACvD,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM,GAAG,WAAW;YAC5C,SAAS,CAAC,mCAAmC,EAAE,UAAU,OAAO,CAAC;YACjE;QACF;QAEA,SAAS;QAET,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,kBAAkB;YAClB,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;YAC3C,IAAI,aAAa,WAAW;gBAC1B,SAAS,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,sBAAsB,EAAE,UAAU,GAAG,CAAC;gBACjF;YACF;YAEA,kBAAkB;YAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,SAAS,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC;gBACtD;YACF;YAEA,IAAI;gBACF,kBAAkB,OAAO,MAAM,GAAG;gBAElC,MAAM,MAAM,MAAM,WAAW,MAAM;oBACjC;oBACA,WAAW,CAAC;wBACV,UAAU,CAAA;4BACR,MAAM,YAAY;mCAAI;gCAAM;oCAAE;oCAAK,KAAK,KAAK,IAAI;oCAAE,OAAO,KAAK,MAAM;gCAAC;6BAAE;4BACxE,eAAe;4BACf,OAAO;wBACT;oBACF;oBACA,SAAS,CAAC;wBACR,SAAS,CAAC,iCAAiC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,OAAO,EAAE;oBAC1E;gBACF;YACF,EAAE,OAAO,KAAK;YACZ,+BAA+B;YACjC;QACF;QAEA,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAW;;0BACd,6LAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,GAAG;oCACd,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG;oCACtC,IAAI;oCACJ,OAAO;wCAAE,WAAW;oCAAQ;oCAC5B,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS,IAAM,gBAAgB,OAAO;4CACtC,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,UAAU,UAAU;sDAEpB,cAAA,6LAAC,iJAAA,CAAA,YAAS;;;;;;;;;;sDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS,IAAM,gBAAgB,OAAO;4CACtC,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,UAAU,UAAU,OAAO,MAAM,GAAG;sDAEpC,cAAA,6LAAC,iJAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;sDAEvB,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;2BApCJ;;;;;oBA2CX,OAAO,MAAM,GAAG,2BACf,6LAAC;wBACC,WAAW,CAAC,yDAAyD,EACnE,QAAQ,mBAAmB,uCAC5B,gBAAgB,CAAC;wBAClB,QAAQ;wBACR,YAAY;;0CAEZ,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,qBAClB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAE,WAAU;;gDAA2C;gDACnC,KAAK,KAAK,CAAC;gDAAU;;;;;;;;;;;;yDAI5C;;sDACE,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;;;;;;;;0CAO1E,6LAAC;gCACC,MAAK;gCACL,KAAK;gCACL,UAAU;gCACV,QAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,MAAM;oBAAC;oBAAI;oBAAU;oBAAc;oBAAU;;;;;;;YAGtD,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;GA5QwB;;QAcwB,gIAAA,CAAA,gBAAa;;;KAdrC", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/products/import/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { FaUpload, FaFilePdf, FaCheck, FaTimes, FaSave, FaImage, Fa<PERSON><PERSON>ner, FaExclamationTriangle } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport Link from 'next/link';\nimport ImageUpload from '@/components/ui/ImageUpload';\nimport MultiImageUpload from '@/components/ui/MultiImageUpload';\n\ninterface ExtractedData {\n  reference: string;\n  name?: string;\n  description: string;\n  characteristics: Record<string, string>;\n}\n\ninterface FormData extends ExtractedData {\n  categoryId: string;\n  brandId: string;\n  mainImage: string;\n  images: { url: string; alt?: string; order?: number }[];\n}\n\nexport default function ImportProductPage() {\n  const router = useRouter();\n  const { user } = useAuth();\n  const [isUploading, setIsUploading] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [extractedData, setExtractedData] = useState<ExtractedData | null>(null);\n  const [formData, setFormData] = useState<FormData>({\n    reference: '',\n    name: '',\n    description: '',\n    characteristics: {},\n    categoryId: '',\n    brandId: '',\n    mainImage: '',\n    images: []\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);\n  const [brands, setBrands] = useState<{ id: string; name: string }[]>([]);\n  const [isLoadingData, setIsLoadingData] = useState(true);\n\n  // Fetch categories and brands on component mount\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch categories\n        const categoriesResponse = await fetch('/api/categories');\n        if (!categoriesResponse.ok) {\n          throw new Error('Failed to fetch categories');\n        }\n        const categoriesData = await categoriesResponse.json();\n        setCategories(categoriesData.categories);\n\n        // Fetch brands\n        const brandsResponse = await fetch('/api/brands');\n        if (!brandsResponse.ok) {\n          throw new Error('Failed to fetch brands');\n        }\n        const brandsData = await brandsResponse.json();\n        setBrands(brandsData.brands);\n      } catch (err) {\n        console.error('Error fetching data:', err);\n      } finally {\n        setIsLoadingData(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n\n    const file = files[0];\n    if (file.type !== 'application/pdf') {\n      setErrors({ file: 'Veuillez sélectionner un fichier PDF' });\n      return;\n    }\n\n    setSelectedFile(file);\n    setErrors({});\n  };\n\n  const handleUpload = async () => {\n    if (!selectedFile) {\n      setErrors({ file: 'Veuillez sélectionner un fichier PDF' });\n      return;\n    }\n\n    setIsUploading(true);\n    setErrors({});\n    setExtractedData(null); // Réinitialiser les données extraites\n    setFormData({\n      reference: '',\n      name: '',\n      description: '',\n      characteristics: {},\n      categoryId: '',\n      brandId: '',\n      mainImage: '',\n      images: []\n    });\n\n    try {\n      // Créer un FormData pour envoyer le fichier\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n\n      // Simuler le temps de téléchargement\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      setIsProcessing(true);\n\n      // Envoyer le fichier à l'API d'extraction de PDF avec Hugging Face\n      const response = await fetch('/api/extract-hf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      // Récupérer les données de la réponse\n      const result = await response.json();\n\n      // Vérifier s'il y a une erreur\n      if (!response.ok) {\n        throw new Error(result.error || 'Erreur lors de l\\'extraction des données du PDF');\n      }\n\n      // Vérifier si l'extraction a échoué\n      if (result.error || result.data === null) {\n        setErrors({ upload: result.error || 'Impossible d\\'extraire des données du PDF. Veuillez vérifier que le PDF contient les informations nécessaires.' });\n        return;\n      }\n\n      // Récupérer les données extraites\n      const extractedData: ExtractedData = {\n        reference: result.data.reference || '',\n        name: result.data.productName || '',\n        description: result.data.description || '',\n        characteristics: result.data.characteristics || {}\n      };\n\n      // Vérifier la source des données\n      const source = result.data.source || 'unknown';\n      console.log(`Source des données extraites: ${source}`);\n\n      // Vérifier si les données extraites sont complètes\n      const isComplete =\n        extractedData.reference &&\n        extractedData.name &&\n        extractedData.description &&\n        Object.keys(extractedData.characteristics).length > 0;\n\n      // Si les données sont incomplètes, afficher un avertissement\n      if (!isComplete) {\n        setErrors({\n          upload: 'Certaines données n\\'ont pas pu être extraites du PDF. Veuillez compléter les champs manquants manuellement.'\n        });\n      }\n\n      // Mettre à jour l'état avec les données extraites\n      setExtractedData(extractedData);\n\n      // Afficher un message de succès avec la source des données\n      const successMessage = source === 'IA'\n        ? 'Données extraites avec succès par l\\'IA !'\n        : source === 'fallback'\n          ? 'Données générées automatiquement à partir du nom du fichier !'\n          : source.includes('règles')\n            ? 'Données extraites avec succès par analyse du nom de fichier !'\n            : 'Données extraites avec succès !';\n\n      console.log(successMessage);\n\n      // Afficher un avertissement si un warning est présent dans la réponse\n      if (result.warning) {\n        console.warn(result.warning);\n        setErrors({\n          upload: result.warning\n        });\n      }\n\n      setFormData({\n        ...extractedData,\n        categoryId: '',\n        brandId: '',\n        mainImage: '',\n        images: []\n      });\n    } catch (error: any) {\n      console.error('Erreur lors du traitement du PDF:', error);\n      setErrors({ upload: error.message || 'Une erreur est survenue lors du traitement du PDF' });\n    } finally {\n      setIsUploading(false);\n      setIsProcessing(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error for this field\n    if (errors[name]) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[name];\n        return newErrors;\n      });\n    }\n  };\n\n  const handleMainImageChange = (imageUrl: string | null) => {\n    setFormData(prev => ({ ...prev, mainImage: imageUrl || '' }));\n  };\n\n  const handleImagesChange = (images: { url: string; alt?: string; order?: number }[]) => {\n    setFormData(prev => ({ ...prev, images }));\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.categoryId) {\n      newErrors.categoryId = 'La catégorie est requise';\n    }\n\n    if (!formData.brandId) {\n      newErrors.brandId = 'La marque est requise';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!extractedData || !validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Create the product using the API\n      const response = await fetch('/api/products', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          reference: formData.reference,\n          name: formData.name,\n          description: formData.description,\n          characteristics: formData.characteristics,\n          mainImage: formData.mainImage || null,\n          categoryId: formData.categoryId || null,\n          brandId: formData.brandId || null,\n          images: formData.images,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to create product');\n      }\n\n      // Redirect to the product list\n      router.push('/admin/products');\n    } catch (err: any) {\n      console.error('Error creating product:', err);\n      setErrors({ submit: err.message || 'Une erreur est survenue lors de la création du produit' });\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n            Importer un produit depuis un PDF\n          </h1>\n          <Link href=\"/admin/products\">\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\"\n            >\n              Retour à la liste\n            </motion.button>\n          </Link>\n        </div>\n\n        {/* Error Message */}\n        {errors.submit && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center\">\n            <FaExclamationTriangle className=\"mr-2\" />\n            <span>{errors.submit}</span>\n          </div>\n        )}\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n          {!extractedData ? (\n            <div className=\"max-w-md mx-auto\">\n              <div className=\"text-center mb-8\">\n                <div className=\"w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <FaFilePdf className=\"text-4xl text-primary\" />\n                </div>\n                <h2 className=\"text-xl font-semibold text-gray-800 dark:text-white\">\n                  Téléchargez un fichier PDF\n                </h2>\n                <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                  Notre système extraira automatiquement les informations du produit à partir du PDF.\n                </p>\n              </div>\n\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Fichier PDF\n                  </label>\n                  <div className=\"flex items-center justify-center w-full\">\n                    <label className=\"flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600\">\n                      <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                        <FaUpload className=\"w-8 h-8 mb-3 text-gray-400\" />\n                        <p className=\"mb-2 text-sm text-gray-500 dark:text-gray-400\">\n                          <span className=\"font-semibold\">Cliquez pour télécharger</span> ou glissez-déposez\n                        </p>\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400\">PDF uniquement</p>\n                      </div>\n                      <input\n                        type=\"file\"\n                        accept=\".pdf\"\n                        className=\"hidden\"\n                        onChange={handleFileChange}\n                      />\n                    </label>\n                  </div>\n                  {errors.file && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.file}</p>\n                  )}\n                </div>\n\n                {selectedFile && (\n                  <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <FaFilePdf className=\"text-red-500 mr-2\" />\n                      <span className=\"text-gray-700 dark:text-gray-300\">{selectedFile.name}</span>\n                    </div>\n                    <button\n                      type=\"button\"\n                      onClick={() => setSelectedFile(null)}\n                      className=\"text-red-500 hover:text-red-700\"\n                    >\n                      <FaTimes />\n                    </button>\n                  </div>\n                )}\n\n                <motion.button\n                  type=\"button\"\n                  onClick={handleUpload}\n                  disabled={!selectedFile || isUploading || isProcessing}\n                  className={`w-full flex items-center justify-center gap-2 px-4 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors ${\n                    (!selectedFile || isUploading || isProcessing) ? 'opacity-70 cursor-not-allowed' : ''\n                  }`}\n                  whileHover={!isUploading && !isProcessing ? { scale: 1.05 } : {}}\n                  whileTap={!isUploading && !isProcessing ? { scale: 0.95 } : {}}\n                >\n                  {isUploading ? (\n                    <>\n                      <FaSpinner className=\"animate-spin mr-2\" />\n                      Téléchargement...\n                    </>\n                  ) : isProcessing ? (\n                    <>\n                      <FaSpinner className=\"animate-spin mr-2\" />\n                      Traitement du PDF...\n                    </>\n                  ) : (\n                    <>\n                      <FaUpload />\n                      Télécharger et traiter\n                    </>\n                  )}\n                </motion.button>\n\n                {errors.upload && (\n                  <div className=\"p-4 bg-red-50 text-red-700 rounded-lg\">\n                    <p className=\"font-semibold mb-2\">Message :</p>\n                    <p>{errors.upload}</p>\n                    {typeof errors.upload === 'string' && errors.upload.includes('automatiquement') ? (\n                      <p className=\"mt-2\">Les données ont été générées à partir du nom du fichier. Vous pouvez les modifier si nécessaire.</p>\n                    ) : (\n                      <p className=\"mt-2\">Si les données ne sont pas correctes, vous pouvez les modifier manuellement ou essayer avec un autre fichier PDF.</p>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          ) : (\n            <form onSubmit={handleSubmit}>\n              <div className=\"mb-6\">\n                <div className=\"flex items-center justify-center w-full p-4 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg\">\n                  <FaCheck className=\"mr-2\" />\n                  <span>Les données ont été extraites avec succès du PDF. Veuillez compléter les informations manquantes si nécessaire.</span>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                {/* Reference */}\n                <div>\n                  <label htmlFor=\"reference\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Référence\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"reference\"\n                    name=\"reference\"\n                    value={formData.reference}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                  />\n                </div>\n\n                {/* Name */}\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Nom\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                  />\n                </div>\n\n                {/* Category */}\n                <div>\n                  <label htmlFor=\"categoryId\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Catégorie <span className=\"text-red-500\">*</span>\n                  </label>\n                  <select\n                    id=\"categoryId\"\n                    name=\"categoryId\"\n                    value={formData.categoryId}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      errors.categoryId ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  >\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    {categories.map((category) => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.categoryId && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.categoryId}</p>\n                  )}\n                </div>\n\n                {/* Brand */}\n                <div>\n                  <label htmlFor=\"brandId\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Marque <span className=\"text-red-500\">*</span>\n                  </label>\n                  <select\n                    id=\"brandId\"\n                    name=\"brandId\"\n                    value={formData.brandId}\n                    onChange={handleInputChange}\n                    className={`w-full px-4 py-2 border ${\n                      errors.brandId ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}\n                  >\n                    <option value=\"\">Sélectionner une marque</option>\n                    {brands.map((brand) => (\n                      <option key={brand.id} value={brand.id}>\n                        {brand.name}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.brandId && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.brandId}</p>\n                  )}\n                </div>\n\n                {/* Main Image */}\n                <div>\n                  <ImageUpload\n                    initialImage={formData.mainImage}\n                    onImageChange={handleMainImageChange}\n                    directory=\"products\"\n                    label=\"Image principale\"\n                    aspectRatio=\"square\"\n                  />\n                </div>\n              </div>\n\n              {/* Description */}\n              <div className=\"mb-6\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  id=\"description\"\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleInputChange}\n                  rows={4}\n                  className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n                ></textarea>\n              </div>\n\n              {/* Characteristics */}\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Caractéristiques\n                </label>\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <table className=\"w-full\">\n                    <tbody>\n                      {Object.entries(formData.characteristics).map(([key, value]) => (\n                        <tr key={key} className=\"border-b border-gray-200 dark:border-gray-600\">\n                          <td className=\"py-2 px-4 font-medium text-gray-700 dark:text-gray-300\">\n                            {key}\n                          </td>\n                          <td className=\"py-2 px-4 text-gray-600 dark:text-gray-400\">\n                            {value}\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Product Images */}\n              <div className=\"mb-6\">\n                <MultiImageUpload\n                  initialImages={formData.images}\n                  onImagesChange={handleImagesChange}\n                  directory=\"products\"\n                  label=\"Images du produit\"\n                  maxImages={10}\n                />\n              </div>\n\n              {/* Submit Button */}\n              <div className=\"mt-8 flex justify-end\">\n                <motion.button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className={`flex items-center justify-center gap-2 px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors ${\n                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {isSubmitting ? (\n                    <>\n                      <FaSpinner className=\"animate-spin mr-2\" />\n                      Enregistrement...\n                    </>\n                  ) : (\n                    <>\n                      <FaSave className=\"mr-2\" />\n                      Enregistrer le produit\n                    </>\n                  )}\n                </motion.button>\n              </div>\n            </form>\n          )}\n        </div>\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AA0Be,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,WAAW;QACX,MAAM;QACN,aAAa;QACb,iBAAiB,CAAC;QAClB,YAAY;QACZ,SAAS;QACT,WAAW;QACX,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC,EAAE;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC,EAAE;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;yDAAY;oBAChB,IAAI;wBACF,mBAAmB;wBACnB,MAAM,qBAAqB,MAAM,MAAM;wBACvC,IAAI,CAAC,mBAAmB,EAAE,EAAE;4BAC1B,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;wBACpD,cAAc,eAAe,UAAU;wBAEvC,eAAe;wBACf,MAAM,iBAAiB,MAAM,MAAM;wBACnC,IAAI,CAAC,eAAe,EAAE,EAAE;4BACtB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,aAAa,MAAM,eAAe,IAAI;wBAC5C,UAAU,WAAW,MAAM;oBAC7B,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,wBAAwB;oBACxC,SAAU;wBACR,iBAAiB;oBACnB;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,UAAU;gBAAE,MAAM;YAAuC;YACzD;QACF;QAEA,gBAAgB;QAChB,UAAU,CAAC;IACb;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;YACjB,UAAU;gBAAE,MAAM;YAAuC;YACzD;QACF;QAEA,eAAe;QACf,UAAU,CAAC;QACX,iBAAiB,OAAO,sCAAsC;QAC9D,YAAY;YACV,WAAW;YACX,MAAM;YACN,aAAa;YACb,iBAAiB,CAAC;YAClB,YAAY;YACZ,SAAS;YACT,WAAW;YACX,QAAQ,EAAE;QACZ;QAEA,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,qCAAqC;YACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;YAEhB,mEAAmE;YACnE,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,MAAM;YACR;YAEA,sCAAsC;YACtC,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,+BAA+B;YAC/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,oCAAoC;YACpC,IAAI,OAAO,KAAK,IAAI,OAAO,IAAI,KAAK,MAAM;gBACxC,UAAU;oBAAE,QAAQ,OAAO,KAAK,IAAI;gBAAiH;gBACrJ;YACF;YAEA,kCAAkC;YAClC,MAAM,gBAA+B;gBACnC,WAAW,OAAO,IAAI,CAAC,SAAS,IAAI;gBACpC,MAAM,OAAO,IAAI,CAAC,WAAW,IAAI;gBACjC,aAAa,OAAO,IAAI,CAAC,WAAW,IAAI;gBACxC,iBAAiB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC;YACnD;YAEA,iCAAiC;YACjC,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,IAAI;YACrC,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ;YAErD,mDAAmD;YACnD,MAAM,aACJ,cAAc,SAAS,IACvB,cAAc,IAAI,IAClB,cAAc,WAAW,IACzB,OAAO,IAAI,CAAC,cAAc,eAAe,EAAE,MAAM,GAAG;YAEtD,6DAA6D;YAC7D,IAAI,CAAC,YAAY;gBACf,UAAU;oBACR,QAAQ;gBACV;YACF;YAEA,kDAAkD;YAClD,iBAAiB;YAEjB,2DAA2D;YAC3D,MAAM,iBAAiB,WAAW,OAC9B,8CACA,WAAW,aACT,kEACA,OAAO,QAAQ,CAAC,YACd,kEACA;YAER,QAAQ,GAAG,CAAC;YAEZ,sEAAsE;YACtE,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,IAAI,CAAC,OAAO,OAAO;gBAC3B,UAAU;oBACR,QAAQ,OAAO,OAAO;gBACxB;YACF;YAEA,YAAY;gBACV,GAAG,aAAa;gBAChB,YAAY;gBACZ,SAAS;gBACT,WAAW;gBACX,QAAQ,EAAE;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,UAAU;gBAAE,QAAQ,MAAM,OAAO,IAAI;YAAoD;QAC3F,SAAU;YACR,eAAe;YACf,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,6BAA6B;QAC7B,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,KAAK;gBACtB,OAAO;YACT;QACF;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW,YAAY;YAAG,CAAC;IAC7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAO,CAAC;IAC1C;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,iBAAiB,CAAC,gBAAgB;YACrC;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,mCAAmC;YACnC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW,SAAS,SAAS;oBAC7B,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;oBACjC,iBAAiB,SAAS,eAAe;oBACzC,WAAW,SAAS,SAAS,IAAI;oBACjC,YAAY,SAAS,UAAU,IAAI;oBACnC,SAAS,SAAS,OAAO,IAAI;oBAC7B,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,+BAA+B;YAC/B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,UAAU;gBAAE,QAAQ,IAAI,OAAO,IAAI;YAAyD;YAC5F,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,2IAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;;;;;;gBAOJ,OAAO,MAAM,kBACZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iJAAA,CAAA,wBAAqB;4BAAC,WAAU;;;;;;sCACjC,6LAAC;sCAAM,OAAO,MAAM;;;;;;;;;;;;8BAIxB,6LAAC;oBAAI,WAAU;8BACZ,CAAC,8BACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAKvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iJAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;wEAA+B;;;;;;;8EAEjE,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;sEAE1D,6LAAC;4DACC,MAAK;4DACL,QAAO;4DACP,WAAU;4DACV,UAAU;;;;;;;;;;;;;;;;;4CAIf,OAAO,IAAI,kBACV,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI;;;;;;;;;;;;oCAIxD,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iJAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAoC,aAAa,IAAI;;;;;;;;;;;;0DAEvE,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DAEV,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kDAKd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,gBAAgB,eAAe;wCAC1C,WAAW,CAAC,iIAAiI,EAC3I,AAAC,CAAC,gBAAgB,eAAe,eAAgB,kCAAkC,IACnF;wCACF,YAAY,CAAC,eAAe,CAAC,eAAe;4CAAE,OAAO;wCAAK,IAAI,CAAC;wCAC/D,UAAU,CAAC,eAAe,CAAC,eAAe;4CAAE,OAAO;wCAAK,IAAI,CAAC;kDAE5D,4BACC;;8DACE,6LAAC,iJAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAsB;;2DAG3C,6BACF;;8DACE,6LAAC,iJAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAsB;;yEAI7C;;8DACE,6LAAC,iJAAA,CAAA,WAAQ;;;;;gDAAG;;;;;;;;oCAMjB,OAAO,MAAM,kBACZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;0DAAG,OAAO,MAAM;;;;;;4CAChB,OAAO,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,CAAC,QAAQ,CAAC,mCAC3D,6LAAC;gDAAE,WAAU;0DAAO;;;;;qEAEpB,6LAAC;gDAAE,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;;;6CAO9B,6LAAC;wBAAK,UAAU;;0CACd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAIV,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAY,WAAU;0DAAkE;;;;;;0DAGvG,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,SAAS;gDACzB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAKd,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAAkE;;;;;;0DAGlG,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAKd,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAa,WAAU;;oDAAkE;kEAC5F,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAE3C,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,UAAU;gDAC1B,UAAU;gDACV,WAAW,CAAC,wBAAwB,EAClC,OAAO,UAAU,GAAG,mBAAmB,uCACxC,wHAAwH,CAAC;;kEAE1H,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;4CAK3B,OAAO,UAAU,kBAChB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,UAAU;;;;;;;;;;;;kDAK/D,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAU,WAAU;;oDAAkE;kEAC5F,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAExC,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,WAAW,CAAC,wBAAwB,EAClC,OAAO,OAAO,GAAG,mBAAmB,uCACrC,wHAAwH,CAAC;;kEAE1H,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;4DAAsB,OAAO,MAAM,EAAE;sEACnC,MAAM,IAAI;2DADA,MAAM,EAAE;;;;;;;;;;;4CAKxB,OAAO,OAAO,kBACb,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,OAAO;;;;;;;;;;;;kDAK5D,6LAAC;kDACC,cAAA,6LAAC,0IAAA,CAAA,UAAW;4CACV,cAAc,SAAS,SAAS;4CAChC,eAAe;4CACf,WAAU;4CACV,OAAM;4CACN,aAAY;;;;;;;;;;;;;;;;;0CAMlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAkE;;;;;;kDAGzG,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,MAAM;wCACN,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;0DACE,OAAO,OAAO,CAAC,SAAS,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACzD,6LAAC;wDAAa,WAAU;;0EACtB,6LAAC;gEAAG,WAAU;0EACX;;;;;;0EAEH,6LAAC;gEAAG,WAAU;0EACX;;;;;;;uDALI;;;;;;;;;;;;;;;;;;;;;;;;;;0CAenB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+IAAA,CAAA,UAAgB;oCACf,eAAe,SAAS,MAAM;oCAC9B,gBAAgB;oCAChB,WAAU;oCACV,OAAM;oCACN,WAAW;;;;;;;;;;;0CAKf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,0HAA0H,EACpI,eAAe,kCAAkC,IACjD;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,6BACC;;0DACE,6LAAC,iJAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAsB;;qEAI7C;;0DACE,6LAAC,iJAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjD;GAxjBwB;;QACP,qIAAA,CAAA,YAAS;QACP,0HAAA,CAAA,UAAO;;;KAFF", "debugId": null}}]}