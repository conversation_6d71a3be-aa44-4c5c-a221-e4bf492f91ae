import { prisma } from './prisma';

// Get all brands
export async function getBrands(options?: {
  search?: string;
  includeProducts?: boolean;
  skip?: number;
  take?: number;
}) {
  const { search, includeProducts = false, skip = 0, take = 50 } = options || {};

  const where = search
    ? {
        OR: [
          { name: { contains: search } },
        ],
      }
    : {};

  const [brands, total] = await Promise.all([
    prisma.brand.findMany({
      where,
      include: {
        product: includeProducts,
      },
      skip,
      take,
      orderBy: {
        name: 'asc',
      },
    }),
    prisma.brand.count({ where }),
  ]);

  return { brands, total };
}

// Get a single brand by ID
export async function getBrandById(id: string, includeProducts: boolean = false) {
  return prisma.brand.findUnique({
    where: { id },
    include: {
      product: includeProducts,
    },
  });
}

// Create a new brand
export async function createBrand(data: {
  name: string;
  image?: string;
}) {
  // Check if a brand with the same name already exists
  const existingBrand = await prisma.brand.findFirst({
    where: {
      name: data.name,
    },
  });

  if (existingBrand) {
    throw new Error(`Une marque avec le nom ${data.name} existe déjà`);
  }

  return prisma.brand.create({
    data: {
      id: crypto.randomUUID(),
      name: data.name,
      image: data.image,
      updatedAt: new Date(),
    },
  });
}

// Update an existing brand
export async function updateBrand(
  id: string,
  data: {
    name?: string;
    image?: string;
  }
) {
  // If name is being updated, check if it already exists
  if (data.name) {
    const existingBrand = await prisma.brand.findFirst({
      where: {
        name: data.name,
        id: { not: id },
      },
    });

    if (existingBrand) {
      throw new Error(`Une marque avec le nom ${data.name} existe déjà`);
    }
  }

  return prisma.brand.update({
    where: { id },
    data: {
      ...data,
      updatedAt: new Date(),
    },
  });
}

// Delete a brand
export async function deleteBrand(id: string) {
  // First, update all products in this brand to have null brandId
  await prisma.product.updateMany({
    where: { brandId: id },
    data: { brandId: null },
  });

  // Then delete the brand
  return prisma.brand.delete({
    where: { id },
  });
}

// Get brands with product count
export async function getBrandsWithProductCount() {
  const brands = await prisma.brand.findMany({
    orderBy: {
      name: 'asc',
    },
  });

  const brandsWithCount = await Promise.all(
    brands.map(async (brand) => {
      const count = await prisma.product.count({
        where: { brandId: brand.id },
      });
      return {
        ...brand,
        productCount: count,
      };
    })
  );

  return brandsWithCount;
}
