/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/dashboard/route";
exports.ids = ["app/api/admin/dashboard/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_admin_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/dashboard/route.ts */ \"(rsc)/./src/app/api/admin/dashboard/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/dashboard/route\",\n        pathname: \"/api/admin/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/dashboard/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\api\\\\admin\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Asus_Desktop_Projects_MoonelecApp_moonelec_app_src_app_api_admin_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/dashboard/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/admin/dashboard/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-options */ \"(rsc)/./src/lib/auth-options.ts\");\n/* harmony import */ var _lib_realtime_data__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/realtime-data */ \"(rsc)/./src/lib/realtime-data.ts\");\n/* harmony import */ var _middleware_security__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/middleware/security */ \"(rsc)/./src/middleware/security.ts\");\n\n\n\n\n\n\n// GET /api/admin/dashboard - Get dashboard statistics\nasync function GET(req) {\n    try {\n        // Security check\n        const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';\n        if (!(0,_middleware_security__WEBPACK_IMPORTED_MODULE_5__.checkIPSecurity)(clientIP)) {\n            return (0,_middleware_security__WEBPACK_IMPORTED_MODULE_5__.createSecureError)('Access denied', 403);\n        }\n        // Vérifier l'authentification et les autorisations\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        if (!session || !session.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Vérifier si l'utilisateur est un administrateur\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            select: {\n                role: true\n            }\n        });\n        if (!user || user.role !== 'ADMIN') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Forbidden'\n            }, {\n                status: 403\n            });\n        }\n        // Check cache first for performance\n        const cacheKey = 'admin-dashboard-data';\n        let dashboardData = _lib_realtime_data__WEBPACK_IMPORTED_MODULE_4__.DataCache.get(cacheKey);\n        if (!dashboardData) {\n            // Get real-time dashboard data\n            dashboardData = await _lib_realtime_data__WEBPACK_IMPORTED_MODULE_4__.RealTimeDataService.getDashboardData();\n            // Cache for 2 minutes\n            _lib_realtime_data__WEBPACK_IMPORTED_MODULE_4__.DataCache.set(cacheKey, dashboardData, 2);\n        }\n        // Format response to match existing frontend expectations\n        const response = {\n            totalClients: dashboardData.users.usersByRole.CLIENT || 0,\n            totalOrders: dashboardData.summary.totalQuotes,\n            totalProducts: dashboardData.summary.totalProducts,\n            totalRevenue: Math.round(dashboardData.revenue.totalRevenue),\n            totalCommercials: dashboardData.users.usersByRole.COMMERCIAL || 0,\n            totalSuppliers: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.brand.count(),\n            totalCategories: dashboardData.summary.totalCategories,\n            totalBrands: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.brand.count(),\n            // Additional real-time data\n            revenueGrowth: dashboardData.revenue.growth,\n            activeUsers: dashboardData.users.activeUsers,\n            averageQuoteValue: Math.round(dashboardData.revenue.averageQuoteValue),\n            topProducts: dashboardData.products.topProducts.slice(0, 5),\n            salesTrends: dashboardData.trends.slice(-7),\n            // Performance metrics\n            quotesThisMonth: dashboardData.revenue.quotesCount,\n            lastUpdated: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch dashboard data'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth-options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconsole.log('🔧 Auth options module loaded');\nconsole.log('🔧 Creating auth options...');\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                username: {\n                    label: \"Nom d'utilisateur\",\n                    type: 'text'\n                },\n                password: {\n                    label: 'Mot de passe',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                console.log('🔍 AUTHORIZE FUNCTION CALLED!');\n                console.log('🔍 Auth attempt:', {\n                    username: credentials?.username,\n                    hasPassword: !!credentials?.password\n                });\n                if (!credentials?.username || !credentials?.password) {\n                    console.log('❌ Missing credentials');\n                    return null;\n                }\n                try {\n                    // Rechercher l'utilisateur par nom d'utilisateur\n                    console.log('🔍 Looking for user:', credentials.username);\n                    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.findUserByUsername)(credentials.username);\n                    // Vérifier si l'utilisateur existe\n                    if (!user) {\n                        console.log('❌ User not found:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ User found:', {\n                        id: user.id,\n                        username: user.username,\n                        role: user.role\n                    });\n                    // Vérifier le mot de passe\n                    console.log('🔍 Verifying password...');\n                    const isPasswordValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log('❌ Invalid password for user:', credentials.username);\n                        return null;\n                    }\n                    console.log('✅ Password valid for user:', credentials.username);\n                    // Retourner les données de l'utilisateur sans le mot de passe\n                    const userObject = {\n                        id: user.id,\n                        email: user.email,\n                        username: user.username,\n                        name: `${user.firstname} ${user.lastname}`,\n                        firstname: user.firstname,\n                        lastname: user.lastname,\n                        role: user.role,\n                        clientId: user.client?.id,\n                        commercialId: user.commercial?.id,\n                        adminId: user.admin?.id\n                    };\n                    console.log('✅ Returning user object:', userObject);\n                    return userObject;\n                } catch (error) {\n                    console.error('❌ Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.username = user.username;\n                token.role = user.role;\n                token.firstname = user.firstname;\n                token.lastname = user.lastname;\n                token.clientId = user.clientId;\n                token.commercialId = user.commercialId;\n                token.adminId = user.adminId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.id;\n                session.user.username = token.username;\n                session.user.role = token.role;\n                session.user.firstname = token.firstname;\n                session.user.lastname = token.lastname;\n                session.user.clientId = token.clientId;\n                session.user.commercialId = token.commercialId;\n                session.user.adminId = token.adminId;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signOut: '/auth/signout',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   createClientUser: () => (/* binding */ createClientUser),\n/* harmony export */   createCommercialUser: () => (/* binding */ createCommercialUser),\n/* harmony export */   findUserByEmail: () => (/* binding */ findUserByEmail),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Fonction pour hacher un mot de passe\nasync function hashPassword(password) {\n    const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].genSalt(10);\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, salt);\n}\n// Fonction pour vérifier un mot de passe\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\n// Fonction pour créer un utilisateur client\nasync function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.CLIENT\n            }\n        });\n        // Créer le profil client associé\n        const client = await tx.client.create({\n            data: {\n                userId: user.id,\n                company_name\n            }\n        });\n        return {\n            user,\n            client\n        };\n    });\n}\n// Fonction pour créer un utilisateur commercial\nasync function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.COMMERCIAL\n            }\n        });\n        // Créer le profil commercial associé\n        const commercial = await tx.commercial.create({\n            data: {\n                userId: user.id,\n                profile_photo\n            }\n        });\n        return {\n            user,\n            commercial\n        };\n    });\n}\n// Fonction pour créer un utilisateur administrateur\nasync function createAdminUser({ email, username, password, lastname, firstname, telephone }) {\n    const hashedPassword = await hashPassword(password);\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Créer l'utilisateur de base\n        const user = await tx.user.create({\n            data: {\n                email,\n                username,\n                password: hashedPassword,\n                lastname,\n                firstname,\n                telephone,\n                role: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN\n            }\n        });\n        // Créer le profil admin associé\n        const admin = await tx.admin.create({\n            data: {\n                userId: user.id\n            }\n        });\n        return {\n            user,\n            admin\n        };\n    });\n}\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nasync function findUserByUsername(username) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            username\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n// Fonction pour trouver un utilisateur par son email\nasync function findUserByEmail(email) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n        where: {\n            email\n        },\n        include: {\n            client: true,\n            commercial: true,\n            admin: true\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDSTtBQUNRO0FBRTFDLHVDQUF1QztBQUNoQyxlQUFlRyxhQUFhQyxRQUFnQjtJQUNqRCxNQUFNQyxPQUFPLE1BQU1MLHdEQUFjLENBQUM7SUFDbEMsT0FBT0EscURBQVcsQ0FBQ0ksVUFBVUM7QUFDL0I7QUFFQSx5Q0FBeUM7QUFDbEMsZUFBZUcsZUFDcEJKLFFBQWdCLEVBQ2hCSyxjQUFzQjtJQUV0QixPQUFPVCx3REFBYyxDQUFDSSxVQUFVSztBQUNsQztBQUVBLDRDQUE0QztBQUNyQyxlQUFlRSxpQkFBaUIsRUFDckNDLEtBQUssRUFDTEMsUUFBUSxFQUNSVCxRQUFRLEVBQ1JVLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxTQUFTLEVBQ1RDLFlBQVksRUFTYjtJQUNDLE1BQU1SLGlCQUFpQixNQUFNTixhQUFhQztJQUUxQyxPQUFPSCwyQ0FBTUEsQ0FBQ2lCLFlBQVksQ0FBQyxPQUFPQztRQUNoQyw4QkFBOEI7UUFDOUIsTUFBTUMsT0FBTyxNQUFNRCxHQUFHQyxJQUFJLENBQUNDLE1BQU0sQ0FBQztZQUNoQ0MsTUFBTTtnQkFDSlY7Z0JBQ0FDO2dCQUNBVCxVQUFVSztnQkFDVks7Z0JBQ0FDO2dCQUNBQztnQkFDQU8sTUFBTXJCLG9EQUFRQSxDQUFDc0IsTUFBTTtZQUN2QjtRQUNGO1FBRUEsaUNBQWlDO1FBQ2pDLE1BQU1DLFNBQVMsTUFBTU4sR0FBR00sTUFBTSxDQUFDSixNQUFNLENBQUM7WUFDcENDLE1BQU07Z0JBQ0pJLFFBQVFOLEtBQUtPLEVBQUU7Z0JBQ2ZWO1lBQ0Y7UUFDRjtRQUVBLE9BQU87WUFBRUc7WUFBTUs7UUFBTztJQUN4QjtBQUNGO0FBRUEsZ0RBQWdEO0FBQ3pDLGVBQWVHLHFCQUFxQixFQUN6Q2hCLEtBQUssRUFDTEMsUUFBUSxFQUNSVCxRQUFRLEVBQ1JVLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxTQUFTLEVBQ1RhLGFBQWEsRUFTZDtJQUNDLE1BQU1wQixpQkFBaUIsTUFBTU4sYUFBYUM7SUFFMUMsT0FBT0gsMkNBQU1BLENBQUNpQixZQUFZLENBQUMsT0FBT0M7UUFDaEMsOEJBQThCO1FBQzlCLE1BQU1DLE9BQU8sTUFBTUQsR0FBR0MsSUFBSSxDQUFDQyxNQUFNLENBQUM7WUFDaENDLE1BQU07Z0JBQ0pWO2dCQUNBQztnQkFDQVQsVUFBVUs7Z0JBQ1ZLO2dCQUNBQztnQkFDQUM7Z0JBQ0FPLE1BQU1yQixvREFBUUEsQ0FBQzRCLFVBQVU7WUFDM0I7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNQyxhQUFhLE1BQU1aLEdBQUdZLFVBQVUsQ0FBQ1YsTUFBTSxDQUFDO1lBQzVDQyxNQUFNO2dCQUNKSSxRQUFRTixLQUFLTyxFQUFFO2dCQUNmRTtZQUNGO1FBQ0Y7UUFFQSxPQUFPO1lBQUVUO1lBQU1XO1FBQVc7SUFDNUI7QUFDRjtBQUVBLG9EQUFvRDtBQUM3QyxlQUFlQyxnQkFBZ0IsRUFDcENwQixLQUFLLEVBQ0xDLFFBQVEsRUFDUlQsUUFBUSxFQUNSVSxRQUFRLEVBQ1JDLFNBQVMsRUFDVEMsU0FBUyxFQVFWO0lBQ0MsTUFBTVAsaUJBQWlCLE1BQU1OLGFBQWFDO0lBRTFDLE9BQU9ILDJDQUFNQSxDQUFDaUIsWUFBWSxDQUFDLE9BQU9DO1FBQ2hDLDhCQUE4QjtRQUM5QixNQUFNQyxPQUFPLE1BQU1ELEdBQUdDLElBQUksQ0FBQ0MsTUFBTSxDQUFDO1lBQ2hDQyxNQUFNO2dCQUNKVjtnQkFDQUM7Z0JBQ0FULFVBQVVLO2dCQUNWSztnQkFDQUM7Z0JBQ0FDO2dCQUNBTyxNQUFNckIsb0RBQVFBLENBQUMrQixLQUFLO1lBQ3RCO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTUMsUUFBUSxNQUFNZixHQUFHZSxLQUFLLENBQUNiLE1BQU0sQ0FBQztZQUNsQ0MsTUFBTTtnQkFDSkksUUFBUU4sS0FBS08sRUFBRTtZQUNqQjtRQUNGO1FBRUEsT0FBTztZQUFFUDtZQUFNYztRQUFNO0lBQ3ZCO0FBQ0Y7QUFFQSxpRUFBaUU7QUFDMUQsZUFBZUMsbUJBQW1CdEIsUUFBZ0I7SUFDdkQsT0FBT1osMkNBQU1BLENBQUNtQixJQUFJLENBQUNnQixVQUFVLENBQUM7UUFDNUJDLE9BQU87WUFBRXhCO1FBQVM7UUFDbEJ5QixTQUFTO1lBQ1BiLFFBQVE7WUFDUk0sWUFBWTtZQUNaRyxPQUFPO1FBQ1Q7SUFDRjtBQUNGO0FBRUEscURBQXFEO0FBQzlDLGVBQWVLLGdCQUFnQjNCLEtBQWE7SUFDakQsT0FBT1gsMkNBQU1BLENBQUNtQixJQUFJLENBQUNnQixVQUFVLENBQUM7UUFDNUJDLE9BQU87WUFBRXpCO1FBQU07UUFDZjBCLFNBQVM7WUFDUGIsUUFBUTtZQUNSTSxZQUFZO1lBQ1pHLE9BQU87UUFDVDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnO1xuaW1wb3J0IHsgVXNlclJvbGUgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbi8vIEZvbmN0aW9uIHBvdXIgaGFjaGVyIHVuIG1vdCBkZSBwYXNzZVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhc2hQYXNzd29yZChwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgY29uc3Qgc2FsdCA9IGF3YWl0IGJjcnlwdC5nZW5TYWx0KDEwKTtcbiAgcmV0dXJuIGJjcnlwdC5oYXNoKHBhc3N3b3JkLCBzYWx0KTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciB2w6lyaWZpZXIgdW4gbW90IGRlIHBhc3NlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5UGFzc3dvcmQoXG4gIHBhc3N3b3JkOiBzdHJpbmcsXG4gIGhhc2hlZFBhc3N3b3JkOiBzdHJpbmdcbik6IFByb21pc2U8Ym9vbGVhbj4ge1xuICByZXR1cm4gYmNyeXB0LmNvbXBhcmUocGFzc3dvcmQsIGhhc2hlZFBhc3N3b3JkKTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciBjcsOpZXIgdW4gdXRpbGlzYXRldXIgY2xpZW50XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQ2xpZW50VXNlcih7XG4gIGVtYWlsLFxuICB1c2VybmFtZSxcbiAgcGFzc3dvcmQsXG4gIGxhc3RuYW1lLFxuICBmaXJzdG5hbWUsXG4gIHRlbGVwaG9uZSxcbiAgY29tcGFueV9uYW1lLFxufToge1xuICBlbWFpbDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBsYXN0bmFtZTogc3RyaW5nO1xuICBmaXJzdG5hbWU6IHN0cmluZztcbiAgdGVsZXBob25lPzogc3RyaW5nO1xuICBjb21wYW55X25hbWU/OiBzdHJpbmc7XG59KSB7XG4gIGNvbnN0IGhhc2hlZFBhc3N3b3JkID0gYXdhaXQgaGFzaFBhc3N3b3JkKHBhc3N3b3JkKTtcblxuICByZXR1cm4gcHJpc21hLiR0cmFuc2FjdGlvbihhc3luYyAodHgpID0+IHtcbiAgICAvLyBDcsOpZXIgbCd1dGlsaXNhdGV1ciBkZSBiYXNlXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IHR4LnVzZXIuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgZW1haWwsXG4gICAgICAgIHVzZXJuYW1lLFxuICAgICAgICBwYXNzd29yZDogaGFzaGVkUGFzc3dvcmQsXG4gICAgICAgIGxhc3RuYW1lLFxuICAgICAgICBmaXJzdG5hbWUsXG4gICAgICAgIHRlbGVwaG9uZSxcbiAgICAgICAgcm9sZTogVXNlclJvbGUuQ0xJRU5ULFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIENyw6llciBsZSBwcm9maWwgY2xpZW50IGFzc29jacOpXG4gICAgY29uc3QgY2xpZW50ID0gYXdhaXQgdHguY2xpZW50LmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgICAgY29tcGFueV9uYW1lLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIHJldHVybiB7IHVzZXIsIGNsaWVudCB9O1xuICB9KTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciBjcsOpZXIgdW4gdXRpbGlzYXRldXIgY29tbWVyY2lhbFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNvbW1lcmNpYWxVc2VyKHtcbiAgZW1haWwsXG4gIHVzZXJuYW1lLFxuICBwYXNzd29yZCxcbiAgbGFzdG5hbWUsXG4gIGZpcnN0bmFtZSxcbiAgdGVsZXBob25lLFxuICBwcm9maWxlX3Bob3RvLFxufToge1xuICBlbWFpbDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBsYXN0bmFtZTogc3RyaW5nO1xuICBmaXJzdG5hbWU6IHN0cmluZztcbiAgdGVsZXBob25lPzogc3RyaW5nO1xuICBwcm9maWxlX3Bob3RvPzogc3RyaW5nO1xufSkge1xuICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZCk7XG5cbiAgcmV0dXJuIHByaXNtYS4kdHJhbnNhY3Rpb24oYXN5bmMgKHR4KSA9PiB7XG4gICAgLy8gQ3LDqWVyIGwndXRpbGlzYXRldXIgZGUgYmFzZVxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCB0eC51c2VyLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICB1c2VybmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6IGhhc2hlZFBhc3N3b3JkLFxuICAgICAgICBsYXN0bmFtZSxcbiAgICAgICAgZmlyc3RuYW1lLFxuICAgICAgICB0ZWxlcGhvbmUsXG4gICAgICAgIHJvbGU6IFVzZXJSb2xlLkNPTU1FUkNJQUwsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgLy8gQ3LDqWVyIGxlIHByb2ZpbCBjb21tZXJjaWFsIGFzc29jacOpXG4gICAgY29uc3QgY29tbWVyY2lhbCA9IGF3YWl0IHR4LmNvbW1lcmNpYWwuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgICBwcm9maWxlX3Bob3RvLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIHJldHVybiB7IHVzZXIsIGNvbW1lcmNpYWwgfTtcbiAgfSk7XG59XG5cbi8vIEZvbmN0aW9uIHBvdXIgY3LDqWVyIHVuIHV0aWxpc2F0ZXVyIGFkbWluaXN0cmF0ZXVyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQWRtaW5Vc2VyKHtcbiAgZW1haWwsXG4gIHVzZXJuYW1lLFxuICBwYXNzd29yZCxcbiAgbGFzdG5hbWUsXG4gIGZpcnN0bmFtZSxcbiAgdGVsZXBob25lLFxufToge1xuICBlbWFpbDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBsYXN0bmFtZTogc3RyaW5nO1xuICBmaXJzdG5hbWU6IHN0cmluZztcbiAgdGVsZXBob25lPzogc3RyaW5nO1xufSkge1xuICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZCk7XG5cbiAgcmV0dXJuIHByaXNtYS4kdHJhbnNhY3Rpb24oYXN5bmMgKHR4KSA9PiB7XG4gICAgLy8gQ3LDqWVyIGwndXRpbGlzYXRldXIgZGUgYmFzZVxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCB0eC51c2VyLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICB1c2VybmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6IGhhc2hlZFBhc3N3b3JkLFxuICAgICAgICBsYXN0bmFtZSxcbiAgICAgICAgZmlyc3RuYW1lLFxuICAgICAgICB0ZWxlcGhvbmUsXG4gICAgICAgIHJvbGU6IFVzZXJSb2xlLkFETUlOLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIENyw6llciBsZSBwcm9maWwgYWRtaW4gYXNzb2Npw6lcbiAgICBjb25zdCBhZG1pbiA9IGF3YWl0IHR4LmFkbWluLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICByZXR1cm4geyB1c2VyLCBhZG1pbiB9O1xuICB9KTtcbn1cblxuLy8gRm9uY3Rpb24gcG91ciB0cm91dmVyIHVuIHV0aWxpc2F0ZXVyIHBhciBzb24gbm9tIGQndXRpbGlzYXRldXJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBmaW5kVXNlckJ5VXNlcm5hbWUodXNlcm5hbWU6IHN0cmluZykge1xuICByZXR1cm4gcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgd2hlcmU6IHsgdXNlcm5hbWUgfSxcbiAgICBpbmNsdWRlOiB7XG4gICAgICBjbGllbnQ6IHRydWUsXG4gICAgICBjb21tZXJjaWFsOiB0cnVlLFxuICAgICAgYWRtaW46IHRydWUsXG4gICAgfSxcbiAgfSk7XG59XG5cbi8vIEZvbmN0aW9uIHBvdXIgdHJvdXZlciB1biB1dGlsaXNhdGV1ciBwYXIgc29uIGVtYWlsXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZmluZFVzZXJCeUVtYWlsKGVtYWlsOiBzdHJpbmcpIHtcbiAgcmV0dXJuIHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgIHdoZXJlOiB7IGVtYWlsIH0sXG4gICAgaW5jbHVkZToge1xuICAgICAgY2xpZW50OiB0cnVlLFxuICAgICAgY29tbWVyY2lhbDogdHJ1ZSxcbiAgICAgIGFkbWluOiB0cnVlLFxuICAgIH0sXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbImJjcnlwdCIsInByaXNtYSIsIlVzZXJSb2xlIiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJzYWx0IiwiZ2VuU2FsdCIsImhhc2giLCJ2ZXJpZnlQYXNzd29yZCIsImhhc2hlZFBhc3N3b3JkIiwiY29tcGFyZSIsImNyZWF0ZUNsaWVudFVzZXIiLCJlbWFpbCIsInVzZXJuYW1lIiwibGFzdG5hbWUiLCJmaXJzdG5hbWUiLCJ0ZWxlcGhvbmUiLCJjb21wYW55X25hbWUiLCIkdHJhbnNhY3Rpb24iLCJ0eCIsInVzZXIiLCJjcmVhdGUiLCJkYXRhIiwicm9sZSIsIkNMSUVOVCIsImNsaWVudCIsInVzZXJJZCIsImlkIiwiY3JlYXRlQ29tbWVyY2lhbFVzZXIiLCJwcm9maWxlX3Bob3RvIiwiQ09NTUVSQ0lBTCIsImNvbW1lcmNpYWwiLCJjcmVhdGVBZG1pblVzZXIiLCJBRE1JTiIsImFkbWluIiwiZmluZFVzZXJCeVVzZXJuYW1lIiwiZmluZFVuaXF1ZSIsIndoZXJlIiwiaW5jbHVkZSIsImZpbmRVc2VyQnlFbWFpbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/realtime-data.ts":
/*!**********************************!*\
  !*** ./src/lib/realtime-data.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataCache: () => (/* binding */ DataCache),\n/* harmony export */   RealTimeDataService: () => (/* binding */ RealTimeDataService)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// Real-time data calculation functions\nclass RealTimeDataService {\n    // Calculate real revenue from quotes and orders\n    static async calculateRealRevenue(timeframe = 'month') {\n        const now = new Date();\n        let startDate;\n        switch(timeframe){\n            case 'day':\n                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                break;\n            case 'week':\n                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                break;\n            case 'month':\n                startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n                break;\n            case 'year':\n                startDate = new Date(now.getFullYear(), 0, 1);\n                break;\n        }\n        // Calculate revenue from quotes\n        const quotes = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findMany({\n            where: {\n                createdAt: {\n                    gte: startDate,\n                    lte: now\n                }\n            },\n            include: {\n                items: true\n            }\n        });\n        const totalRevenue = quotes.reduce((total, quote)=>{\n            const quoteTotal = quote.items.reduce((quoteSum, item)=>{\n                return quoteSum + item.quantity * item.unitPrice;\n            }, 0);\n            return total + quoteTotal;\n        }, 0);\n        return {\n            totalRevenue,\n            quotesCount: quotes.length,\n            averageQuoteValue: quotes.length > 0 ? totalRevenue / quotes.length : 0\n        };\n    }\n    // Get real user activity metrics\n    static async getUserActivityMetrics() {\n        const now = new Date();\n        const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        // Active users in last 30 days (based on quote creation)\n        const activeUsers = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                quotes: {\n                    some: {\n                        createdAt: {\n                            gte: last30Days\n                        }\n                    }\n                }\n            },\n            include: {\n                _count: {\n                    select: {\n                        quotes: true\n                    }\n                }\n            }\n        });\n        // Total users by role\n        const usersByRole = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.groupBy({\n            by: [\n                'role'\n            ],\n            _count: {\n                id: true\n            }\n        });\n        return {\n            activeUsers: activeUsers.length,\n            totalUsers: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count(),\n            usersByRole: usersByRole.reduce((acc, item)=>{\n                acc[item.role] = item._count.id;\n                return acc;\n            }, {}),\n            topActiveUsers: activeUsers.sort((a, b)=>b._count.quotes - a._count.quotes).slice(0, 5).map((user)=>({\n                    id: user.id,\n                    name: `${user.firstname} ${user.lastname}`,\n                    quotesCount: user._count.quotes\n                }))\n        };\n    }\n    // Get real product performance data\n    static async getProductPerformance() {\n        // Products with most quotes\n        const productQuotes = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quoteItem.groupBy({\n            by: [\n                'productId'\n            ],\n            _count: {\n                id: true\n            },\n            _sum: {\n                quantity: true,\n                unitPrice: true\n            },\n            orderBy: {\n                _count: {\n                    id: 'desc'\n                }\n            },\n            take: 10\n        });\n        // Get product details\n        const productIds = productQuotes.map((pq)=>pq.productId).filter(Boolean);\n        const products = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.findMany({\n            where: {\n                id: {\n                    in: productIds\n                }\n            }\n        });\n        const productPerformance = productQuotes.map((pq)=>{\n            const product = products.find((p)=>p.id === pq.productId);\n            return {\n                productId: pq.productId,\n                productName: product?.name || 'Unknown Product',\n                productReference: product?.reference || 'N/A',\n                quotesCount: pq._count.id,\n                totalQuantity: pq._sum.quantity || 0,\n                totalRevenue: (pq._sum.quantity || 0) * (pq._sum.unitPrice || 0)\n            };\n        });\n        return {\n            topProducts: productPerformance,\n            totalProducts: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.count(),\n            totalCategories: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.category.count()\n        };\n    }\n    // Get real sales trends\n    static async getSalesTrends(days = 30) {\n        const now = new Date();\n        const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);\n        const dailyQuotes = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findMany({\n            where: {\n                createdAt: {\n                    gte: startDate,\n                    lte: now\n                }\n            },\n            include: {\n                items: true\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n        // Group by day\n        const dailyData = new Map();\n        for(let i = 0; i < days; i++){\n            const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);\n            const dateKey = date.toISOString().split('T')[0];\n            dailyData.set(dateKey, {\n                quotes: 0,\n                revenue: 0\n            });\n        }\n        dailyQuotes.forEach((quote)=>{\n            const dateKey = quote.createdAt.toISOString().split('T')[0];\n            const existing = dailyData.get(dateKey) || {\n                quotes: 0,\n                revenue: 0\n            };\n            const quoteRevenue = quote.items.reduce((sum, item)=>{\n                return sum + item.quantity * item.unitPrice;\n            }, 0);\n            dailyData.set(dateKey, {\n                quotes: existing.quotes + 1,\n                revenue: existing.revenue + quoteRevenue\n            });\n        });\n        return Array.from(dailyData.entries()).map(([date, data])=>({\n                date,\n                quotes: data.quotes,\n                revenue: data.revenue\n            }));\n    }\n    // Get comprehensive dashboard data\n    static async getDashboardData() {\n        const [revenueData, userMetrics, productPerformance, salesTrends] = await Promise.all([\n            this.calculateRealRevenue('month'),\n            this.getUserActivityMetrics(),\n            this.getProductPerformance(),\n            this.getSalesTrends(30)\n        ]);\n        // Calculate growth rates\n        const lastMonthRevenue = await this.calculateRealRevenue('month');\n        const currentMonthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);\n        const lastMonthStart = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);\n        const lastMonthEnd = new Date(new Date().getFullYear(), new Date().getMonth(), 0);\n        const lastMonthQuotes = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.findMany({\n            where: {\n                createdAt: {\n                    gte: lastMonthStart,\n                    lte: lastMonthEnd\n                }\n            },\n            include: {\n                items: true\n            }\n        });\n        const lastMonthRevenueTotal = lastMonthQuotes.reduce((total, quote)=>{\n            return total + quote.items.reduce((sum, item)=>sum + item.quantity * item.unitPrice, 0);\n        }, 0);\n        const revenueGrowth = lastMonthRevenueTotal > 0 ? (revenueData.totalRevenue - lastMonthRevenueTotal) / lastMonthRevenueTotal * 100 : 0;\n        return {\n            revenue: {\n                ...revenueData,\n                growth: revenueGrowth\n            },\n            users: userMetrics,\n            products: productPerformance,\n            trends: salesTrends,\n            summary: {\n                totalQuotes: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.quote.count(),\n                totalProducts: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.product.count(),\n                totalUsers: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count(),\n                totalCategories: await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.category.count()\n            }\n        };\n    }\n    // Real-time data change notification\n    static async notifyDataChange(type, action) {\n        // This would integrate with WebSocket or Server-Sent Events for real-time updates\n        console.log(`📊 Data change notification: ${type} ${action}`);\n        // In a real implementation, you would:\n        // 1. Emit WebSocket event to connected clients\n        // 2. Update cached dashboard data\n        // 3. Trigger real-time UI updates\n        return {\n            type,\n            action,\n            timestamp: new Date().toISOString()\n        };\n    }\n}\n// Cache management for performance\nclass DataCache {\n    static{\n        this.cache = new Map();\n    }\n    static set(key, data, ttlMinutes = 5) {\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            ttl: ttlMinutes * 60 * 1000\n        });\n    }\n    static get(key) {\n        const cached = this.cache.get(key);\n        if (!cached) return null;\n        if (Date.now() - cached.timestamp > cached.ttl) {\n            this.cache.delete(key);\n            return null;\n        }\n        return cached.data;\n    }\n    static clear() {\n        this.cache.clear();\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/realtime-data.ts\n");

/***/ }),

/***/ "(rsc)/./src/middleware/security.ts":
/*!************************************!*\
  !*** ./src/middleware/security.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   checkIPSecurity: () => (/* binding */ checkIPSecurity),\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   checkUploadRateLimit: () => (/* binding */ checkUploadRateLimit),\n/* harmony export */   createSecureError: () => (/* binding */ createSecureError),\n/* harmony export */   generateCSRFToken: () => (/* binding */ generateCSRFToken),\n/* harmony export */   generateSecureSessionId: () => (/* binding */ generateSecureSessionId),\n/* harmony export */   reportSuspiciousActivity: () => (/* binding */ reportSuspiciousActivity),\n/* harmony export */   sanitizeForDatabase: () => (/* binding */ sanitizeForDatabase),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   schemas: () => (/* binding */ schemas),\n/* harmony export */   securityHeaders: () => (/* binding */ securityHeaders),\n/* harmony export */   validateAuthToken: () => (/* binding */ validateAuthToken),\n/* harmony export */   validateCSRFToken: () => (/* binding */ validateCSRFToken),\n/* harmony export */   validateFileUpload: () => (/* binding */ validateFileUpload),\n/* harmony export */   validateInput: () => (/* binding */ validateInput),\n/* harmony export */   validatePasswordStrength: () => (/* binding */ validatePasswordStrength),\n/* harmony export */   validateSessionSecurity: () => (/* binding */ validateSessionSecurity)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n// Native rate limiting implementation for Next.js\nclass RateLimiter {\n    check(ip, maxRequests = 100, windowMs = 15 * 60 * 1000) {\n        const now = Date.now();\n        const record = this.requests.get(ip);\n        if (!record || now > record.resetTime) {\n            this.requests.set(ip, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return true;\n        }\n        if (record.count >= maxRequests) {\n            return false;\n        }\n        record.count++;\n        return true;\n    }\n    cleanup() {\n        const now = Date.now();\n        for (const [ip, record] of this.requests.entries()){\n            if (now > record.resetTime) {\n                this.requests.delete(ip);\n            }\n        }\n    }\n    constructor(){\n        this.requests = new Map();\n    }\n}\n// Global rate limiter instances\nconst generalRateLimit = new RateLimiter();\nconst authRateLimit = new RateLimiter();\nconst uploadRateLimit = new RateLimiter();\n// Rate limiting functions\nconst checkRateLimit = (ip)=>{\n    return generalRateLimit.check(ip, 100, 15 * 60 * 1000); // 100 requests per 15 minutes\n};\nconst checkAuthRateLimit = (ip)=>{\n    return authRateLimit.check(ip, 5, 15 * 60 * 1000); // 5 auth attempts per 15 minutes\n};\nconst checkUploadRateLimit = (ip)=>{\n    return uploadRateLimit.check(ip, 10, 60 * 1000); // 10 uploads per minute\n};\n// Cleanup rate limit records periodically\nsetInterval(()=>{\n    generalRateLimit.cleanup();\n    authRateLimit.cleanup();\n    uploadRateLimit.cleanup();\n}, 5 * 60 * 1000); // Cleanup every 5 minutes\n// Input validation schemas\nconst schemas = {\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().email().max(255),\n    password: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(6).max(128),\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1).max(100).regex(/^[a-zA-ZÀ-ÿ\\s'-]+$/),\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1).max(100).regex(/^[a-zA-Z0-9À-ÿ\\s'._-]+$/),\n    phone: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/),\n    text: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(1000),\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().uuid(),\n    reference: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1).max(50).regex(/^[a-zA-Z0-9-_]+$/),\n    url: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().url().max(500)\n};\n// XSS protection\nconst sanitizeInput = (input)=>{\n    return input.replace(/[<>]/g, '') // Remove < and >\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .replace(/script/gi, '') // Remove script tags\n    .trim();\n};\n// SQL injection protection\nconst sanitizeForDatabase = (input)=>{\n    return input.replace(/['\";\\\\]/g, '') // Remove dangerous SQL characters\n    .replace(/--/g, '') // Remove SQL comments\n    .replace(/\\/\\*/g, '') // Remove SQL block comments\n    .replace(/\\*\\//g, '').trim();\n};\n// CSRF token generation and validation\nconst generateCSRFToken = ()=>{\n    return (__webpack_require__(/*! crypto */ \"crypto\").randomBytes)(32).toString('hex');\n};\nconst validateCSRFToken = (token, sessionToken)=>{\n    return token === sessionToken && token.length === 64;\n};\n// Security headers middleware\nconst securityHeaders = (request)=>{\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    // Security headers\n    response.headers.set('X-Content-Type-Options', 'nosniff');\n    response.headers.set('X-Frame-Options', 'DENY');\n    response.headers.set('X-XSS-Protection', '1; mode=block');\n    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n    // Content Security Policy\n    const csp = [\n        \"default-src 'self'\",\n        \"script-src 'self' 'unsafe-inline' 'unsafe-eval'\",\n        \"style-src 'self' 'unsafe-inline'\",\n        \"img-src 'self' data: blob:\",\n        \"font-src 'self'\",\n        \"connect-src 'self'\",\n        \"media-src 'self'\",\n        \"object-src 'none'\",\n        \"base-uri 'self'\",\n        \"form-action 'self'\",\n        \"frame-ancestors 'none'\",\n        \"upgrade-insecure-requests\"\n    ].join('; ');\n    response.headers.set('Content-Security-Policy', csp);\n    return response;\n};\n// Input validation middleware\nconst validateInput = (schema, data)=>{\n    try {\n        return schema.parse(data);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodError) {\n            throw new Error(`Validation error: ${error.errors.map((e)=>e.message).join(', ')}`);\n        }\n        throw error;\n    }\n};\n// File upload security validation\nconst validateFileUpload = (file)=>{\n    const maxSize = 25 * 1024 * 1024; // 25MB\n    const allowedTypes = [\n        'image/jpeg',\n        'image/jpg',\n        'image/png',\n        'image/gif',\n        'image/webp',\n        'application/pdf',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'application/vnd.ms-excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'text/plain',\n        'text/csv'\n    ];\n    const dangerousExtensions = [\n        'exe',\n        'bat',\n        'cmd',\n        'com',\n        'pif',\n        'scr',\n        'vbs',\n        'js',\n        'jar',\n        'php',\n        'asp',\n        'jsp'\n    ];\n    // Check file size\n    if (file.size > maxSize) {\n        throw new Error('File too large. Maximum size is 25MB');\n    }\n    // Check file type\n    if (!allowedTypes.includes(file.type)) {\n        throw new Error('File type not allowed');\n    }\n    // Check file extension\n    const extension = file.name.split('.').pop()?.toLowerCase();\n    if (dangerousExtensions.includes(extension || '')) {\n        throw new Error('File extension not allowed for security reasons');\n    }\n    return true;\n};\n// Authentication token validation\nconst validateAuthToken = (token)=>{\n    if (!token || token.length < 10) {\n        return false;\n    }\n    // Check for suspicious patterns\n    const suspiciousPatterns = [\n        /[<>]/g,\n        /javascript:/gi,\n        /data:/gi,\n        /vbscript:/gi\n    ];\n    return !suspiciousPatterns.some((pattern)=>pattern.test(token));\n};\n// IP address validation and blocking\nconst blockedIPs = new Set();\nconst suspiciousActivity = new Map();\nconst checkIPSecurity = (ip)=>{\n    // Check if IP is blocked\n    if (blockedIPs.has(ip)) {\n        return false;\n    }\n    // Track suspicious activity\n    const attempts = suspiciousActivity.get(ip) || 0;\n    if (attempts > 50) {\n        blockedIPs.add(ip);\n        return false;\n    }\n    return true;\n};\nconst reportSuspiciousActivity = (ip)=>{\n    const current = suspiciousActivity.get(ip) || 0;\n    suspiciousActivity.set(ip, current + 1);\n};\n// Password security validation\nconst validatePasswordStrength = (password)=>{\n    const minLength = 8;\n    const hasUpperCase = /[A-Z]/.test(password);\n    const hasLowerCase = /[a-z]/.test(password);\n    const hasNumbers = /\\d/.test(password);\n    const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;\n};\n// Session security\nconst generateSecureSessionId = ()=>{\n    return (__webpack_require__(/*! crypto */ \"crypto\").randomBytes)(32).toString('hex');\n};\nconst validateSessionSecurity = (sessionData)=>{\n    // Check session expiration\n    if (sessionData.expiresAt && new Date() > new Date(sessionData.expiresAt)) {\n        return false;\n    }\n    // Check session integrity\n    if (!sessionData.userId || !sessionData.createdAt) {\n        return false;\n    }\n    return true;\n};\n// Error handling that doesn't leak sensitive information\nconst createSecureError = (message, statusCode = 400)=>{\n    // Don't expose internal errors in production\n    const isProduction = \"development\" === 'production';\n    const safeMessage = isProduction ? 'An error occurred' : message;\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: safeMessage\n    }, {\n        status: statusCode\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/middleware/security.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();