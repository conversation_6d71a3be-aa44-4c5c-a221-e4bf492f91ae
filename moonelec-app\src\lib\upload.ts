import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Security configuration
const SECURITY_CONFIG = {
  maxFileSize: 25 * 1024 * 1024, // 25MB
  allowedMimeTypes: [
    // Images
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // Documents
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    // Text files
    'text/plain', 'text/csv', 'text/xml', 'application/json',
    // Archives (limited)
    'application/zip'
  ],
  dangerousExtensions: ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'jsp', 'sh'],
  suspiciousPatterns: [
    Buffer.from('MZ'), // PE executable header
    Buffer.from('#!/'), // Script shebang
  ]
};

// Security validation functions
const validateFileSize = (file: File): void => {
  if (file.size > SECURITY_CONFIG.maxFileSize) {
    throw new Error(`File too large. Maximum size is ${SECURITY_CONFIG.maxFileSize / (1024 * 1024)}MB`);
  }
};

const validateFileType = (file: File): void => {
  if (!SECURITY_CONFIG.allowedMimeTypes.includes(file.type)) {
    throw new Error('File type not allowed. Allowed types: images, PDF, Office documents, text files');
  }
};

const validateFileExtension = (fileName: string): void => {
  const extension = path.extname(fileName).toLowerCase().substring(1);
  if (SECURITY_CONFIG.dangerousExtensions.includes(extension)) {
    throw new Error('File extension not allowed for security reasons');
  }
};

const scanForMalware = (buffer: Buffer): void => {
  for (const pattern of SECURITY_CONFIG.suspiciousPatterns) {
    if (buffer.indexOf(pattern) === 0) {
      throw new Error('File contains suspicious content and cannot be uploaded');
    }
  }
};

// Ensure upload directory exists
const createUploadDir = (dir: string) => {
  const uploadDir = path.join(process.cwd(), 'public', dir);
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  return uploadDir;
};

// Save a file to the public directory with security validation
export const saveFile = async (
  file: File,
  directory: string = 'uploads'
): Promise<string> => {
  // Security validations
  validateFileSize(file);
  validateFileType(file);
  validateFileExtension(file.name);

  const uploadDir = createUploadDir(directory);

  // Convert file to buffer for malware scanning
  const buffer = Buffer.from(await file.arrayBuffer());
  scanForMalware(buffer);

  // Generate a unique filename
  const fileExtension = path.extname(file.name);
  const fileName = `${uuidv4()}${fileExtension}`;
  const filePath = path.join(uploadDir, fileName);

  // Write file to disk
  fs.writeFileSync(filePath, buffer);

  // Return the public URL
  return `/${directory}/${fileName}`;
};

// Save a base64 image to the public directory
export const saveBase64Image = (
  base64Data: string,
  directory: string = 'uploads'
): string => {
  // Create directory if it doesn't exist
  const uploadDir = createUploadDir(directory);

  // Extract the file extension from the base64 data
  const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,/);
  if (!matches || matches.length !== 2) {
    throw new Error('Invalid base64 image format');
  }

  const fileExtension = matches[1];
  const base64Image = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');

  // Generate a unique filename
  const fileName = `${uuidv4()}.${fileExtension}`;
  const filePath = path.join(uploadDir, fileName);

  // Write file to disk
  fs.writeFileSync(filePath, base64Image, 'base64');

  // Return the public URL
  return `/${directory}/${fileName}`;
};

// Delete a file from the public directory
export const deleteFile = (fileUrl: string): boolean => {
  try {
    // Extract the file path from the URL
    const filePath = path.join(process.cwd(), 'public', fileUrl);

    // Check if file exists
    if (fs.existsSync(filePath)) {
      // Delete the file
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};
