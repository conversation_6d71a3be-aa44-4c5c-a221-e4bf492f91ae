import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Ensure upload directory exists
const createUploadDir = (dir: string) => {
  const uploadDir = path.join(process.cwd(), 'public', dir);
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  return uploadDir;
};

// Save a file to the public directory
export const saveFile = async (
  file: File,
  directory: string = 'uploads'
): Promise<string> => {
  const uploadDir = createUploadDir(directory);
  
  // Generate a unique filename
  const fileExtension = path.extname(file.name);
  const fileName = `${uuidv4()}${fileExtension}`;
  const filePath = path.join(uploadDir, fileName);
  
  // Convert file to buffer
  const buffer = Buffer.from(await file.arrayBuffer());
  
  // Write file to disk
  fs.writeFileSync(filePath, buffer);
  
  // Return the public URL
  return `/${directory}/${fileName}`;
};

// Save a base64 image to the public directory
export const saveBase64Image = (
  base64Data: string,
  directory: string = 'uploads'
): string => {
  // Create directory if it doesn't exist
  const uploadDir = createUploadDir(directory);
  
  // Extract the file extension from the base64 data
  const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,/);
  if (!matches || matches.length !== 2) {
    throw new Error('Invalid base64 image format');
  }
  
  const fileExtension = matches[1];
  const base64Image = base64Data.replace(/^data:image\/[a-zA-Z]+;base64,/, '');
  
  // Generate a unique filename
  const fileName = `${uuidv4()}.${fileExtension}`;
  const filePath = path.join(uploadDir, fileName);
  
  // Write file to disk
  fs.writeFileSync(filePath, base64Image, 'base64');
  
  // Return the public URL
  return `/${directory}/${fileName}`;
};

// Delete a file from the public directory
export const deleteFile = (fileUrl: string): boolean => {
  try {
    // Extract the file path from the URL
    const filePath = path.join(process.cwd(), 'public', fileUrl);
    
    // Check if file exists
    if (fs.existsSync(filePath)) {
      // Delete the file
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};
