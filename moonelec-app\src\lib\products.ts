import { prisma } from './prisma';

// Get all products with optional filtering
export async function getProducts(options?: {
  categoryId?: string;
  brandId?: string;
  search?: string;
  skip?: number;
  take?: number;
}) {
  const { categoryId, brandId, search, skip = 0, take = 50 } = options || {};

  const where = {
    ...(categoryId ? { categoryId } : {}),
    ...(brandId ? { brandId } : {}),
    ...(search
      ? {
          OR: [
            { name: { contains: search } },
            { reference: { contains: search } },
            { description: { contains: search } },
          ],
        }
      : {}),
  };

  const [rawProducts, total] = await Promise.all([
    prisma.product.findMany({
      where,
      include: {
        category: true,
        brand: true,
        productimage: {
          orderBy: {
            order: 'asc',
          },
        },
      },
      skip,
      take,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.product.count({ where }),
  ]);

  // Parse characteristics for all products
  const products = rawProducts.map(product => {
    let parsedCharacteristics = {};
    try {
      parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};
    } catch (error) {
      console.error('Error parsing characteristics for product', product.id, ':', error);
      parsedCharacteristics = {};
    }

    return {
      ...product,
      characteristics: parsedCharacteristics,
    };
  });

  return { products, total };
}

// Get a single product by ID
export async function getProductById(id: string) {
  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      category: true,
      brand: true,
      productimage: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!product) return null;

  // Parse characteristics JSON string back to object
  let parsedCharacteristics = {};
  try {
    parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};
  } catch (error) {
    console.error('Error parsing characteristics for product', id, ':', error);
    parsedCharacteristics = {};
  }

  return {
    ...product,
    characteristics: parsedCharacteristics,
  };
}

// Get a single product by reference
export async function getProductByReference(reference: string) {
  const product = await prisma.product.findUnique({
    where: { reference },
    include: {
      category: true,
      brand: true,
      productimage: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!product) return null;

  // Parse characteristics JSON string back to object
  let parsedCharacteristics = {};
  try {
    parsedCharacteristics = product.characteristics ? JSON.parse(product.characteristics) : {};
  } catch (error) {
    console.error('Error parsing characteristics for product', reference, ':', error);
    parsedCharacteristics = {};
  }

  return {
    ...product,
    characteristics: parsedCharacteristics,
  };
}

// Create a new product
export async function createProduct(data: {
  reference: string;
  name: string;
  description: string;
  characteristics: Record<string, any>;
  mainImage?: string;
  categoryId?: string;
  brandId?: string;
  images?: { url: string; alt?: string; order?: number }[];
}) {
  const { images, ...productData } = data;

  return prisma.$transaction(async (tx) => {
    // Check if reference already exists
    const existingProduct = await tx.product.findUnique({
      where: { reference: productData.reference },
    });

    if (existingProduct) {
      throw new Error(`Un produit avec la référence ${productData.reference} existe déjà`);
    }

    // Create the product
    const product = await tx.product.create({
      data: {
        id: crypto.randomUUID(),
        reference: productData.reference,
        name: productData.name,
        description: productData.description,
        characteristics: JSON.stringify(productData.characteristics),
        mainImage: productData.mainImage,
        categoryId: productData.categoryId,
        brandId: productData.brandId,
        updatedAt: new Date(),
      },
    });

    // Add images if provided
    if (images && images.length > 0) {
      await tx.productimage.createMany({
        data: images.map((image, index) => ({
          id: crypto.randomUUID(),
          url: image.url,
          alt: image.alt || product.name,
          order: image.order || index,
          productId: product.id,
        })),
      });
    }

    return product;
  });
}

// Update an existing product
export async function updateProduct(
  id: string,
  data: {
    reference?: string;
    name?: string;
    description?: string;
    characteristics?: Record<string, any>;
    mainImage?: string;
    categoryId?: string | null;
    brandId?: string | null;
  }
) {
  // If reference is being updated, check if it already exists
  if (data.reference) {
    const existingProduct = await prisma.product.findFirst({
      where: {
        reference: data.reference,
        id: { not: id },
      },
    });

    if (existingProduct) {
      throw new Error(`Un produit avec la référence ${data.reference} existe déjà`);
    }
  }

  const updatedProduct = await prisma.product.update({
    where: { id },
    data: {
      ...data,
      characteristics: data.characteristics ? JSON.stringify(data.characteristics) : undefined,
      updatedAt: new Date(),
    },
    include: {
      category: true,
      brand: true,
      productimage: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  // Parse characteristics JSON string back to object
  let parsedCharacteristics = {};
  try {
    parsedCharacteristics = updatedProduct.characteristics ? JSON.parse(updatedProduct.characteristics) : {};
  } catch (error) {
    console.error('Error parsing characteristics for updated product', id, ':', error);
    parsedCharacteristics = {};
  }

  return {
    ...updatedProduct,
    characteristics: parsedCharacteristics,
  };
}

// Delete a product
export async function deleteProduct(id: string) {
  return prisma.$transaction(async (tx) => {
    // 1. Supprimer d'abord les références dans QuoteItem
    await tx.quoteItem.deleteMany({
      where: { productId: id },
    });

    // 2. Supprimer les références dans OrderItem
    await tx.orderItem.deleteMany({
      where: { productId: id },
    });

    // 3. Supprimer les images du produit
    await tx.productimage.deleteMany({
      where: { productId: id },
    });

    // 4. Enfin, supprimer le produit lui-même
    return tx.product.delete({
      where: { id },
    });
  });
}

// Add images to a product
export async function addProductImages(
  productId: string,
  images: { url: string; alt?: string; order?: number }[]
) {
  return prisma.productimage.createMany({
    data: images.map((image, index) => ({
      id: crypto.randomUUID(),
      url: image.url,
      alt: image.alt,
      order: image.order || index,
      productId,
    })),
  });
}

// Update a product image
export async function updateProductImage(
  id: string,
  data: {
    url?: string;
    alt?: string;
    order?: number;
  }
) {
  return prisma.productimage.update({
    where: { id },
    data,
  });
}

// Delete a product image
export async function deleteProductImage(id: string) {
  return prisma.productimage.delete({
    where: { id },
  });
}

// Delete all images for a product
export async function deleteAllProductImages(productId: string) {
  return prisma.productimage.deleteMany({
    where: { productId },
  });
}
