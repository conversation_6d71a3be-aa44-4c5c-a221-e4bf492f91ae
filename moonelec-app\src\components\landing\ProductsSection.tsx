'use client';

import { useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, useInView } from 'framer-motion';
import { FaArrowRight } from 'react-icons/fa';

// Sample product categories
const productCategories = [
  {
    id: 'lighting',
    name: 'É<PERSON>lair<PERSON>',
    image: 'https://images.unsplash.com/photo-1565814329452-e1efa11c5b89?q=80&w=2070&auto=format&fit=crop',
    description: 'Solutions d\'éclairage pour tous les espaces, des LED économiques aux systèmes intelligents.'
  },
  {
    id: 'wiring',
    name: 'Câblage',
    image: 'https://images.unsplash.com/photo-1601814933824-fd0b574dd592?q=80&w=2012&auto=format&fit=crop',
    description: 'Câbles et accessoires de haute qualité pour toutes les installations électriques.'
  },
  {
    id: 'automation',
    name: 'Domotique',
    image: 'https://images.unsplash.com/photo-1558002038-1055907df827?q=80&w=2070&auto=format&fit=crop',
    description: 'Systèmes intelligents pour contrôler et automatiser votre environnement.'
  },
  {
    id: 'security',
    name: 'Sécurité',
    image: 'https://images.unsplash.com/photo-1557318041-1ce374d55ebf?q=80&w=2080&auto=format&fit=crop',
    description: 'Équipements de sécurité électrique, alarmes et systèmes de surveillance.'
  },
  {
    id: 'industrial',
    name: 'Équipement Industriel',
    image: 'https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?q=80&w=2070&auto=format&fit=crop',
    description: 'Matériel robuste pour les environnements industriels exigeants.'
  },
  {
    id: 'renewable',
    name: 'Énergie Renouvelable',
    image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?q=80&w=2072&auto=format&fit=crop',
    description: 'Solutions pour l\'énergie solaire, éolienne et autres sources renouvelables.'
  }
];

export default function ProductsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.1 });
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  return (
    <section id="products" className="section bg-accent dark:bg-[#111]" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Nos <span className="text-primary">Produits</span>
          </h2>
          <div className="w-20 h-1 bg-secondary mx-auto mb-6"></div>
          <p className="text-text-secondary max-w-3xl mx-auto">
            Découvrez notre vaste gamme de produits électriques de haute qualité pour tous vos projets, des plus simples aux plus complexes.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {productCategories.map((category, index) => (
            <ProductCard 
              key={category.id}
              category={category}
              index={index}
              isInView={isInView}
              isActive={activeCategory === category.id}
              onMouseEnter={() => setActiveCategory(category.id)}
              onMouseLeave={() => setActiveCategory(null)}
            />
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center mt-16"
        >
          <Link href="/products" className="btn-primary inline-flex items-center gap-2">
            Voir tous nos produits <FaArrowRight />
          </Link>
        </motion.div>
      </div>
    </section>
  );
}

function ProductCard({ 
  category, 
  index,
  isInView,
  isActive,
  onMouseEnter,
  onMouseLeave
}: { 
  category: {
    id: string;
    name: string;
    image: string;
    description: string;
  };
  index: number;
  isInView: boolean;
  isActive: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}) {
  return (
    <motion.div 
      className="rounded-lg overflow-hidden shadow-lg group relative"
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
      whileHover={{ y: -5 }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="relative h-64 w-full overflow-hidden">
        <Image
          src={category.image}
          alt={category.name}
          fill
          style={{ objectFit: 'cover' }}
          className="transition-transform duration-500 group-hover:scale-110"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
      </div>
      
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
        <h3 className="text-xl font-semibold mb-2">{category.name}</h3>
        <motion.p 
          className="text-sm text-gray-200 mb-4"
          initial={{ opacity: 0, height: 0 }}
          animate={{ 
            opacity: isActive ? 1 : 0,
            height: isActive ? 'auto' : 0
          }}
          transition={{ duration: 0.3 }}
        >
          {category.description}
        </motion.p>
        <Link 
          href={`/products/${category.id}`} 
          className="inline-flex items-center text-sm font-medium text-white hover:text-primary transition-colors"
        >
          Découvrir <FaArrowRight className="ml-2" />
        </Link>
      </div>
    </motion.div>
  );
}
