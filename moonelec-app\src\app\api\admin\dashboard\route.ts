import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';

// GET /api/admin/dashboard - Get dashboard statistics
export async function GET(req: NextRequest) {
  try {
    // Vérifier l'authentification et les autorisations
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Vérifier si l'utilisateur est un administrateur
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Récupérer les statistiques du tableau de bord
    const [
      totalClients,
      totalOrders,
      totalProducts,
      totalCommercials,
      totalCategories,
      totalBrands,
      quotes
    ] = await Promise.all([
      // Nombre total de clients
      prisma.user.count({
        where: { role: 'CLIENT' }
      }),

      // Nombre total de commandes/devis
      prisma.quote.count(),

      // Nombre total de produits
      prisma.product.count(),

      // Nombre total de commerciaux
      prisma.user.count({
        where: { role: 'COMMERCIAL' }
      }),

      // Nombre total de catégories
      prisma.category.count(),

      // Nombre total de marques
      prisma.brand.count(),

      // Récupérer tous les devis avec leurs articles pour calculer le CA réel
      prisma.quote.findMany({
        include: {
          items: true
        }
      })
    ]);

    // Calculer le chiffre d'affaires réel à partir des devis
    const totalRevenue = quotes.reduce((sum, quote) => {
      const quoteTotal = quote.items.reduce((itemSum, item) => {
        return itemSum + (item.quantity * item.unitPrice);
      }, 0);
      return sum + quoteTotal;
    }, 0);

    return NextResponse.json({
      totalClients,
      totalOrders,
      totalProducts,
      totalRevenue: Math.round(totalRevenue),
      totalCommercials,
      totalSuppliers: totalBrands, // Using brands as suppliers for now
      totalCategories,
      totalBrands
    });
  } catch (error: any) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
