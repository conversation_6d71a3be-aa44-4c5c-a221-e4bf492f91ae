import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { RealTimeDataService, DataCache } from '@/lib/realtime-data';
import { checkIPSecurity, createSecureError } from '@/middleware/security';

// GET /api/admin/dashboard - Get dashboard statistics
export async function GET(req: NextRequest) {
  try {
    // Security check
    const clientIP = req.headers.get('x-forwarded-for') ||
                    req.headers.get('x-real-ip') ||
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Vérifier l'authentification et les autorisations
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Vérifier si l'utilisateur est un administrateur
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check cache first for performance
    const cacheKey = 'admin-dashboard-data';
    let dashboardData = DataCache.get(cacheKey);

    if (!dashboardData) {
      // Get real-time dashboard data
      dashboardData = await RealTimeDataService.getDashboardData();

      // Cache for 2 minutes
      DataCache.set(cacheKey, dashboardData, 2);
    }

    // Format response to match existing frontend expectations
    const response = {
      totalClients: dashboardData.users.usersByRole.CLIENT || 0,
      totalOrders: dashboardData.summary.totalQuotes,
      totalProducts: dashboardData.summary.totalProducts,
      totalRevenue: Math.round(dashboardData.revenue.totalRevenue),
      totalCommercials: dashboardData.users.usersByRole.COMMERCIAL || 0,
      totalSuppliers: await prisma.brand.count(), // Keep brands as suppliers
      totalCategories: dashboardData.summary.totalCategories,
      totalBrands: await prisma.brand.count(),

      // Additional real-time data
      revenueGrowth: dashboardData.revenue.growth,
      activeUsers: dashboardData.users.activeUsers,
      averageQuoteValue: Math.round(dashboardData.revenue.averageQuoteValue),
      topProducts: dashboardData.products.topProducts.slice(0, 5),
      salesTrends: dashboardData.trends.slice(-7), // Last 7 days

      // Performance metrics
      quotesThisMonth: dashboardData.revenue.quotesCount,
      lastUpdated: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
