import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { prisma } from '@/lib/prisma';
import { checkIPSecurity, createSecureError } from '@/middleware/security';

// GET /api/autocomplete/products - Smart product reference suggestions
export async function GET(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const field = url.searchParams.get('field') || 'reference'; // reference, name, description
    const limit = parseInt(url.searchParams.get('limit') || '10');

    if (!query || query.length < 2) {
      return NextResponse.json({
        suggestions: [],
        message: 'Query too short, minimum 2 characters required'
      });
    }

    console.log('🔍 Product autocomplete request:', {
      user: user.id,
      query,
      field,
      limit
    });

    let suggestions: any[] = [];

    try {
      switch (field) {
        case 'reference':
          suggestions = await getReferenceSuggestions(query, limit);
          break;
        case 'name':
          suggestions = await getNameSuggestions(query, limit);
          break;
        case 'description':
          suggestions = await getDescriptionSuggestions(query, limit);
          break;
        case 'all':
          suggestions = await getAllFieldSuggestions(query, limit);
          break;
        default:
          suggestions = await getReferenceSuggestions(query, limit);
      }

      console.log('✅ Found suggestions:', suggestions.length);

      return NextResponse.json({
        suggestions,
        query,
        field,
        count: suggestions.length,
        metadata: {
          searchType: 'smart-autocomplete',
          responseTime: Date.now()
        }
      });

    } catch (searchError: any) {
      console.error('❌ Search error:', searchError);
      return NextResponse.json({
        suggestions: [],
        error: 'Search failed',
        query,
        field
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Autocomplete API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      suggestions: []
    }, { status: 500 });
  }
}

// Get reference suggestions with smart matching
async function getReferenceSuggestions(query: string, limit: number) {
  const products = await prisma.product.findMany({
    where: {
      OR: [
        {
          reference: {
            contains: query,
            mode: 'insensitive'
          }
        },
        {
          reference: {
            startsWith: query,
            mode: 'insensitive'
          }
        }
      ]
    },
    select: {
      id: true,
      reference: true,
      name: true,
      description: true,
      price: true,
      category: {
        select: {
          name: true
        }
      },
      brand: {
        select: {
          name: true
        }
      }
    },
    take: limit,
    orderBy: [
      {
        reference: 'asc'
      },
      {
        name: 'asc'
      }
    ]
  });

  return products.map(product => ({
    type: 'reference',
    value: product.reference,
    label: `${product.reference} - ${product.name}`,
    product: {
      id: product.id,
      reference: product.reference,
      name: product.name,
      description: product.description,
      price: product.price,
      category: product.category?.name,
      brand: product.brand?.name
    },
    matchType: product.reference.toLowerCase().startsWith(query.toLowerCase()) ? 'prefix' : 'contains'
  }));
}

// Get name suggestions
async function getNameSuggestions(query: string, limit: number) {
  const products = await prisma.product.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    select: {
      id: true,
      reference: true,
      name: true,
      description: true,
      price: true,
      category: {
        select: {
          name: true
        }
      }
    },
    take: limit,
    orderBy: {
      name: 'asc'
    }
  });

  return products.map(product => ({
    type: 'name',
    value: product.name,
    label: `${product.name} (${product.reference})`,
    product: {
      id: product.id,
      reference: product.reference,
      name: product.name,
      description: product.description,
      price: product.price,
      category: product.category?.name
    },
    matchType: 'contains'
  }));
}

// Get description suggestions
async function getDescriptionSuggestions(query: string, limit: number) {
  const products = await prisma.product.findMany({
    where: {
      description: {
        contains: query,
        mode: 'insensitive'
      }
    },
    select: {
      id: true,
      reference: true,
      name: true,
      description: true,
      price: true
    },
    take: limit,
    orderBy: {
      name: 'asc'
    }
  });

  return products.map(product => ({
    type: 'description',
    value: product.description,
    label: `${product.name} - ${product.description?.substring(0, 50)}...`,
    product: {
      id: product.id,
      reference: product.reference,
      name: product.name,
      description: product.description,
      price: product.price
    },
    matchType: 'contains'
  }));
}

// Get suggestions from all fields
async function getAllFieldSuggestions(query: string, limit: number) {
  const products = await prisma.product.findMany({
    where: {
      OR: [
        {
          reference: {
            contains: query,
            mode: 'insensitive'
          }
        },
        {
          name: {
            contains: query,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: query,
            mode: 'insensitive'
          }
        }
      ]
    },
    select: {
      id: true,
      reference: true,
      name: true,
      description: true,
      price: true,
      category: {
        select: {
          name: true
        }
      },
      brand: {
        select: {
          name: true
        }
      }
    },
    take: limit,
    orderBy: [
      {
        reference: 'asc'
      },
      {
        name: 'asc'
      }
    ]
  });

  return products.map(product => {
    let matchField = 'reference';
    let matchType = 'contains';
    
    if (product.reference.toLowerCase().includes(query.toLowerCase())) {
      matchField = 'reference';
      matchType = product.reference.toLowerCase().startsWith(query.toLowerCase()) ? 'prefix' : 'contains';
    } else if (product.name.toLowerCase().includes(query.toLowerCase())) {
      matchField = 'name';
    } else if (product.description?.toLowerCase().includes(query.toLowerCase())) {
      matchField = 'description';
    }

    return {
      type: 'all',
      value: matchField === 'reference' ? product.reference : product.name,
      label: `${product.reference} - ${product.name}`,
      product: {
        id: product.id,
        reference: product.reference,
        name: product.name,
        description: product.description,
        price: product.price,
        category: product.category?.name,
        brand: product.brand?.name
      },
      matchField,
      matchType
    };
  });
}
