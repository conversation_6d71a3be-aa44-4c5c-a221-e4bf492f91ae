{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/upload.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\n// Ensure upload directory exists\nconst createUploadDir = (dir: string) => {\n  const uploadDir = path.join(process.cwd(), 'public', dir);\n  if (!fs.existsSync(uploadDir)) {\n    fs.mkdirSync(uploadDir, { recursive: true });\n  }\n  return uploadDir;\n};\n\n// Save a file to the public directory\nexport const saveFile = async (\n  file: File,\n  directory: string = 'uploads'\n): Promise<string> => {\n  const uploadDir = createUploadDir(directory);\n  \n  // Generate a unique filename\n  const fileExtension = path.extname(file.name);\n  const fileName = `${uuidv4()}${fileExtension}`;\n  const filePath = path.join(uploadDir, fileName);\n  \n  // Convert file to buffer\n  const buffer = Buffer.from(await file.arrayBuffer());\n  \n  // Write file to disk\n  fs.writeFileSync(filePath, buffer);\n  \n  // Return the public URL\n  return `/${directory}/${fileName}`;\n};\n\n// Save a base64 image to the public directory\nexport const saveBase64Image = (\n  base64Data: string,\n  directory: string = 'uploads'\n): string => {\n  // Create directory if it doesn't exist\n  const uploadDir = createUploadDir(directory);\n  \n  // Extract the file extension from the base64 data\n  const matches = base64Data.match(/^data:image\\/([a-zA-Z]+);base64,/);\n  if (!matches || matches.length !== 2) {\n    throw new Error('Invalid base64 image format');\n  }\n  \n  const fileExtension = matches[1];\n  const base64Image = base64Data.replace(/^data:image\\/[a-zA-Z]+;base64,/, '');\n  \n  // Generate a unique filename\n  const fileName = `${uuidv4()}.${fileExtension}`;\n  const filePath = path.join(uploadDir, fileName);\n  \n  // Write file to disk\n  fs.writeFileSync(filePath, base64Image, 'base64');\n  \n  // Return the public URL\n  return `/${directory}/${fileName}`;\n};\n\n// Delete a file from the public directory\nexport const deleteFile = (fileUrl: string): boolean => {\n  try {\n    // Extract the file path from the URL\n    const filePath = path.join(process.cwd(), 'public', fileUrl);\n    \n    // Check if file exists\n    if (fs.existsSync(filePath)) {\n      // Delete the file\n      fs.unlinkSync(filePath);\n      return true;\n    }\n    return false;\n  } catch (error) {\n    console.error('Error deleting file:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,iCAAiC;AACjC,MAAM,kBAAkB,CAAC;IACvB,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;IACrD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;QAC7B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW;YAAE,WAAW;QAAK;IAC5C;IACA,OAAO;AACT;AAGO,MAAM,WAAW,OACtB,MACA,YAAoB,SAAS;IAE7B,MAAM,YAAY,gBAAgB;IAElC,6BAA6B;IAC7B,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;IAC5C,MAAM,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,MAAM,eAAe;IAC9C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,yBAAyB;IACzB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;IAEjD,qBAAqB;IACrB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;IAE3B,wBAAwB;IACxB,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU;AACpC;AAGO,MAAM,kBAAkB,CAC7B,YACA,YAAoB,SAAS;IAE7B,uCAAuC;IACvC,MAAM,YAAY,gBAAgB;IAElC,kDAAkD;IAClD,MAAM,UAAU,WAAW,KAAK,CAAC;IACjC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,gBAAgB,OAAO,CAAC,EAAE;IAChC,MAAM,cAAc,WAAW,OAAO,CAAC,kCAAkC;IAEzE,6BAA6B;IAC7B,MAAM,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,IAAI,CAAC,EAAE,eAAe;IAC/C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,qBAAqB;IACrB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU,aAAa;IAExC,wBAAwB;IACxB,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU;AACpC;AAGO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,qCAAqC;QACrC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QAEpD,uBAAuB;QACvB,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC3B,kBAAkB;YAClB,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YACd,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { saveFile, saveBase64Image } from '@/lib/upload';\n\n// POST /api/upload - Upload a file\nexport async function POST(req: NextRequest) {\n  try {\n    const formData = await req.formData();\n    const file = formData.get('file') as File | null;\n    const base64 = formData.get('base64') as string | null;\n    const directory = formData.get('directory') as string || 'uploads';\n    \n    if (!file && !base64) {\n      return NextResponse.json(\n        { error: 'No file or base64 data provided' },\n        { status: 400 }\n      );\n    }\n    \n    let fileUrl: string;\n    \n    if (file) {\n      fileUrl = await saveFile(file, directory);\n    } else if (base64) {\n      fileUrl = saveBase64Image(base64, directory);\n    } else {\n      return NextResponse.json(\n        { error: 'Invalid file data' },\n        { status: 400 }\n      );\n    }\n    \n    return NextResponse.json({ url: fileUrl }, { status: 201 });\n  } catch (error: any) {\n    console.error('Error uploading file:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to upload file' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,QAAQ;QACnC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,SAAS,SAAS,GAAG,CAAC;QAC5B,MAAM,YAAY,SAAS,GAAG,CAAC,gBAA0B;QAEzD,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,IAAI,MAAM;YACR,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,MAAM;QACjC,OAAO,IAAI,QAAQ;YACjB,UAAU,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;QACpC,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,KAAK;QAAQ,GAAG;YAAE,QAAQ;QAAI;IAC3D,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAwB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}