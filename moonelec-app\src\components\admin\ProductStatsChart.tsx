'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaBox } from 'react-icons/fa';

interface CategoryData {
  id: string;
  name: string;
  count: number;
  percentage: number;
  color: string;
}

export default function ProductStatsChart() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Données simulées pour le graphique
  const categories: CategoryData[] = [
    { id: '1', name: 'Éclairage', count: 1250, percentage: 35, color: '#4F46E5' },
    { id: '2', name: 'Câblage', count: 850, percentage: 24, color: '#10B981' },
    { id: '3', name: 'Appareillage', count: 650, percentage: 18, color: '#F59E0B' },
    { id: '4', name: 'Domotique', count: 450, percentage: 13, color: '#EF4444' },
    { id: '5', name: 'Autres', count: 350, percentage: 10, color: '#8B5CF6' },
  ];
  
  if (!mounted) {
    return <div className="h-80 bg-gray-100 dark:bg-gray-700 animate-pulse rounded-lg"></div>;
  }
  
  return (
    <div className="h-80">
      <div className="flex h-full">
        {/* Graphique circulaire */}
        <div className="w-1/2 relative flex items-center justify-center">
          <svg width="160" height="160" viewBox="0 0 160 160" className="transform -rotate-90">
            {categories.map((category, index) => {
              // Calculer les angles pour chaque segment
              const previousPercentages = categories
                .slice(0, index)
                .reduce((sum, cat) => sum + cat.percentage, 0);
              const startAngle = (previousPercentages / 100) * 360;
              const endAngle = ((previousPercentages + category.percentage) / 100) * 360;
              
              // Convertir les angles en coordonnées pour le chemin SVG
              const startX = 80 + 70 * Math.cos((startAngle * Math.PI) / 180);
              const startY = 80 + 70 * Math.sin((startAngle * Math.PI) / 180);
              const endX = 80 + 70 * Math.cos((endAngle * Math.PI) / 180);
              const endY = 80 + 70 * Math.sin((endAngle * Math.PI) / 180);
              
              // Déterminer si l'arc doit être dessiné dans le sens des aiguilles d'une montre
              const largeArcFlag = category.percentage > 50 ? 1 : 0;
              
              // Créer le chemin SVG pour le segment
              const path = `
                M 80 80
                L ${startX} ${startY}
                A 70 70 0 ${largeArcFlag} 1 ${endX} ${endY}
                Z
              `;
              
              return (
                <motion.path
                  key={category.id}
                  d={path}
                  fill={category.color}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
              );
            })}
            <circle cx="80" cy="80" r="50" fill="white" className="dark:fill-gray-800" />
          </svg>
          
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-800 dark:text-white">
                {categories.reduce((sum, cat) => sum + cat.count, 0)}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Produits</p>
            </div>
          </div>
        </div>
        
        {/* Légende */}
        <div className="w-1/2 flex flex-col justify-center space-y-3">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              className="flex items-center"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: category.color }}
              ></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800 dark:text-white">
                  {category.name}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-800 dark:text-white">
                  {category.count}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {category.percentage}%
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
