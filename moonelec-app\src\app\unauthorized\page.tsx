'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaLock, FaSignInAlt, FaHome, FaArrowLeft } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';

export default function UnauthorizedPage() {
  const router = useRouter();
  const { isAuthenticated, user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
    router.push('/auth/signin');
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="py-4 px-6 bg-white dark:bg-gray-800 shadow-sm">
        <div className="container mx-auto flex justify-between items-center">
          <Link href="/" className="text-xl font-bold text-primary">
            Moonelec
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow flex items-center justify-center py-12 px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
        >
          <div className="bg-yellow-600 p-6 text-white text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaLock className="text-4xl" />
            </div>
            <h1 className="text-2xl font-bold">Accès non autorisé</h1>
          </div>
          
          <div className="p-6">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6">
              <p className="text-yellow-700 dark:text-yellow-300">
                Vous n'avez pas les autorisations nécessaires pour accéder à cette page.
                {isAuthenticated && user && (
                  <span> Votre compte ({user.role.toLowerCase()}) ne dispose pas des privilèges requis.</span>
                )}
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-6">
              <motion.button
                onClick={() => router.back()}
                className="flex items-center justify-center gap-2 py-2.5 px-4 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaArrowLeft />
                <span>Retour</span>
              </motion.button>
              
              <motion.button
                onClick={() => router.push('/')}
                className="flex items-center justify-center gap-2 py-2.5 px-4 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaHome />
                <span>Accueil</span>
              </motion.button>
            </div>
            
            {isAuthenticated && (
              <div className="mt-6">
                <motion.button
                  onClick={handleLogout}
                  className="w-full flex items-center justify-center gap-2 py-2.5 px-4 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FaSignInAlt />
                  <span>Se déconnecter</span>
                </motion.button>
              </div>
            )}
            
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Besoin d'aide? <Link href="/contact" className="text-primary hover:underline">Contactez-nous</Link>
              </p>
            </div>
          </div>
        </motion.div>
      </main>

      {/* Footer */}
      <footer className="py-4 px-6 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="container mx-auto text-center text-sm text-gray-500 dark:text-gray-400">
          <p>© {new Date().getFullYear()} Moonelec. Tous droits réservés.</p>
        </div>
      </footer>
    </div>
  );
}
