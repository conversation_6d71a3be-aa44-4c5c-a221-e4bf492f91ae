'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useCart } from '@/context/CartContext';
import { useAuth } from '@/hooks/useAuth';
import { FaFileAlt, FaSpinner } from 'react-icons/fa';

export default function CartPage() {
  const router = useRouter();
  const { isAuthenticated, redirectToLogin } = useAuth();
  const { items, notes, setNotes, clearCart } = useCart();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleRequestQuote = async () => {
    if (!isAuthenticated) {
      // Rediriger vers la page de connexion
      redirectToLogin();
      return;
    }

    if (items.length === 0) {
      alert('Votre panier est vide. Veuillez ajouter des produits avant de demander un devis.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Préparer les données pour la demande de devis
      const quoteData = {
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity
        })),
        notes: notes || undefined
      };

      console.log('Données du devis à envoyer:', quoteData);

      // Envoyer la demande de devis à l'API
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || 'Erreur lors de la création du devis');
      }

      console.log('Réponse de l\'API:', responseData);

      // Vider le panier après la demande réussie
      clearCart();

      // Marquer que l'utilisateur a demandé un devis
      sessionStorage.setItem('quoteRequested', 'true');

      // Rediriger vers la page de confirmation
      router.push('/cart/quote-requested');
    } catch (error: any) {
      console.error('Erreur lors de la demande de devis:', error);
      alert(`Une erreur est survenue lors de la demande de devis: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <h1 className="text-2xl font-bold mb-4">Votre Panier</h1>

      {items.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <p>Votre panier est vide</p>
          <Link href="/products">
            <span className="text-blue-500 hover:underline mt-4 inline-block">
              Découvrir nos produits
            </span>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Liste des articles */}
          <div className="md:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Articles ({items.length})</h2>

            <div className="space-y-4">
              {items.map((item) => (
                <div key={item.id} className="border-b pb-4 flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">{item.name}</h3>
                    <p className="text-sm text-gray-500">Quantité: {item.quantity}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <Link href="/products">
                <span className="text-blue-500 hover:underline inline-block">
                  Continuer vos achats
                </span>
              </Link>
            </div>
          </div>

          {/* Formulaire de demande de devis */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Demande de devis</h2>

            <div className="mb-4">
              <p className="text-gray-600 dark:text-gray-300 mb-2">
                Nombre d'articles: <span className="font-semibold">{items.length}</span>
              </p>
              <p className="text-gray-600 dark:text-gray-300">
                Quantité totale: <span className="font-semibold">
                  {items.reduce((sum, item) => sum + item.quantity, 0)}
                </span>
              </p>
            </div>

            <div className="mb-6">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes ou instructions spéciales
              </label>
              <textarea
                id="notes"
                rows={4}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ajoutez des notes ou des instructions spéciales pour votre devis..."
              ></textarea>
            </div>

            <button
              onClick={handleRequestQuote}
              disabled={isSubmitting}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Traitement en cours...
                </>
              ) : (
                <>
                  <FaFileAlt className="mr-2" />
                  Demander un devis
                </>
              )}
            </button>

            <p className="text-sm text-gray-500 dark:text-gray-400 mt-4 text-center">
              {isAuthenticated
                ? "Votre demande sera traitée par notre équipe dans les plus brefs délais."
                : "Vous devrez vous connecter pour finaliser votre demande de devis."}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
