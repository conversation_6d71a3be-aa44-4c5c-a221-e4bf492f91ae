{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n// Temporarily disable with<PERSON>uth to fix build error\n// import { withAuth } from 'next-auth/middleware';\n\n// CORS configuration for mobile app\nconst corsOptions = {\n  'Access-Control-Allow-Origin': '*', // In production, specify your mobile app's origin\n  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',\n  'Access-Control-Allow-Credentials': 'true',\n  'Access-Control-Max-Age': '86400', // 24 hours\n};\n\n// Security headers\nconst securityHeaders = {\n  'X-Content-Type-Options': 'nosniff',\n  'X-Frame-Options': 'DENY',\n  'X-XSS-Protection': '1; mode=block',\n  'Referrer-Policy': 'strict-origin-when-cross-origin',\n  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',\n};\n\nfunction corsMiddleware(request: NextRequest) {\n  // Handle preflight requests\n  if (request.method === 'OPTIONS') {\n    const response = new NextResponse(null, { status: 200 });\n    \n    // Add CORS headers\n    Object.entries(corsOptions).forEach(([key, value]) => {\n      response.headers.set(key, value);\n    });\n    \n    return response;\n  }\n\n  // Continue with the request\n  const response = NextResponse.next();\n\n  // Add CORS headers to all responses\n  Object.entries(corsOptions).forEach(([key, value]) => {\n    response.headers.set(key, value);\n  });\n\n  // Add security headers (except for API routes to avoid conflicts)\n  if (!request.nextUrl.pathname.startsWith('/api/')) {\n    Object.entries(securityHeaders).forEach(([key, value]) => {\n      response.headers.set(key, value);\n    });\n  }\n\n  return response;\n}\n\n// API routes that don't require authentication\nconst publicApiRoutes = [\n  '/api/auth/mobile',\n  '/api/mobile/test',\n  '/api/test',\n  '/api/health',\n];\n\n// API routes that require mobile authentication\nconst mobileApiRoutes = [\n  '/api/mobile/',\n  '/api/chat/',\n  '/api/upload',\n  '/api/realtime/',\n];\n\nfunction shouldBypassAuth(pathname: string): boolean {\n  return publicApiRoutes.some(route => pathname.startsWith(route));\n}\n\nfunction isMobileApiRoute(pathname: string): boolean {\n  return mobileApiRoutes.some(route => pathname.startsWith(route));\n}\n\nexport default function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  console.log('🔒 Middleware processing:', {\n    method: request.method,\n    pathname,\n    userAgent: request.headers.get('user-agent')?.substring(0, 50),\n    hasAuth: !!request.headers.get('authorization'),\n  });\n\n  // Apply CORS for all requests\n  const corsResponse = corsMiddleware(request);\n  if (corsResponse.status === 200 && request.method === 'OPTIONS') {\n    return corsResponse;\n  }\n\n  // Handle API routes\n  if (pathname.startsWith('/api/')) {\n    // Public API routes - no auth required\n    if (shouldBypassAuth(pathname)) {\n      console.log('🔓 Public API route, bypassing auth:', pathname);\n      return corsResponse;\n    }\n\n    // Mobile API routes - handle mobile auth differently\n    if (isMobileApiRoute(pathname)) {\n      console.log('📱 Mobile API route, allowing through:', pathname);\n      return corsResponse;\n    }\n\n    // Other API routes - let them handle their own auth\n    return corsResponse;\n  }\n\n  // For non-API routes, just return the CORS response for now\n  // TODO: Re-enable NextAuth middleware after fixing build issues\n  console.log('🔐 Middleware for non-API route:', pathname);\n  return corsResponse;\n}\n\n// Configure which routes this middleware runs on\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n};\n\n// Additional utility functions for debugging\nexport function logRequest(request: NextRequest) {\n  console.log('📝 Request details:', {\n    method: request.method,\n    url: request.url,\n    pathname: request.nextUrl.pathname,\n    headers: {\n      authorization: request.headers.get('authorization') ? 'Bearer [PRESENT]' : 'Not provided',\n      contentType: request.headers.get('content-type'),\n      userAgent: request.headers.get('user-agent')?.substring(0, 100),\n      origin: request.headers.get('origin'),\n      referer: request.headers.get('referer'),\n    },\n    ip: request.headers.get('x-forwarded-for') || \n        request.headers.get('x-real-ip') || \n        'unknown',\n  });\n}\n\n// Health check function\nexport function createHealthResponse() {\n  return NextResponse.json({\n    status: 'healthy',\n    timestamp: new Date().toISOString(),\n    middleware: 'active',\n    cors: 'enabled',\n    security: 'enabled',\n  });\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AACA,kDAAkD;AAClD,mDAAmD;AAEnD,oCAAoC;AACpC,MAAM,cAAc;IAClB,+BAA+B;IAC/B,gCAAgC;IAChC,gCAAgC;IAChC,oCAAoC;IACpC,0BAA0B;AAC5B;AAEA,mBAAmB;AACnB,MAAM,kBAAkB;IACtB,0BAA0B;IAC1B,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,sBAAsB;AACxB;AAEA,SAAS,eAAe,OAAoB;IAC1C,4BAA4B;IAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,MAAM,WAAW,IAAI,6LAAA,CAAA,eAAY,CAAC,MAAM;YAAE,QAAQ;QAAI;QAEtD,mBAAmB;QACnB,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;QAC5B;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,oCAAoC;IACpC,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IAEA,kEAAkE;IAClE,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QACjD,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACnD,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;QAC5B;IACF;IAEA,OAAO;AACT;AAEA,+CAA+C;AAC/C,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;CACD;AAED,gDAAgD;AAChD,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;CACD;AAED,SAAS,iBAAiB,QAAgB;IACxC,OAAO,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;AAC3D;AAEA,SAAS,iBAAiB,QAAgB;IACxC,OAAO,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;AAC3D;AAEe,SAAS,WAAW,OAAoB;IACrD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,QAAQ,GAAG,CAAC,6BAA6B;QACvC,QAAQ,QAAQ,MAAM;QACtB;QACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU,GAAG;QAC3D,SAAS,CAAC,CAAC,QAAQ,OAAO,CAAC,GAAG,CAAC;IACjC;IAEA,8BAA8B;IAC9B,MAAM,eAAe,eAAe;IACpC,IAAI,aAAa,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,WAAW;QAC/D,OAAO;IACT;IAEA,oBAAoB;IACpB,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,uCAAuC;QACvC,IAAI,iBAAiB,WAAW;YAC9B,QAAQ,GAAG,CAAC,wCAAwC;YACpD,OAAO;QACT;QAEA,qDAAqD;QACrD,IAAI,iBAAiB,WAAW;YAC9B,QAAQ,GAAG,CAAC,0CAA0C;YACtD,OAAO;QACT;QAEA,oDAAoD;QACpD,OAAO;IACT;IAEA,4DAA4D;IAC5D,gEAAgE;IAChE,QAAQ,GAAG,CAAC,oCAAoC;IAChD,OAAO;AACT;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH;AAGO,SAAS,WAAW,OAAoB;IAC7C,QAAQ,GAAG,CAAC,uBAAuB;QACjC,QAAQ,QAAQ,MAAM;QACtB,KAAK,QAAQ,GAAG;QAChB,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAClC,SAAS;YACP,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB,qBAAqB;YAC3E,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;YACjC,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU,GAAG;YAC3D,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;YAC5B,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC/B;QACA,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;IACN;AACF;AAGO,SAAS;IACd,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;AACF"}}]}