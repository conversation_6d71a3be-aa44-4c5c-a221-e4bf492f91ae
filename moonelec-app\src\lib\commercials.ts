import { prisma } from './prisma';
import { createCommercialUser, hashPassword } from './auth';
import { UserRole } from '@prisma/client';

// Obtenir tous les commerciaux avec pagination et recherche
export async function getCommercials(options?: {
  search?: string;
  skip?: number;
  take?: number;
}) {
  const { search, skip = 0, take = 50 } = options || {};

  const where = search
    ? {
        user: {
          OR: [
            { firstname: { contains: search } },
            { lastname: { contains: search } },
            { email: { contains: search } },
            { username: { contains: search } },
          ],
        },
      }
    : {};

  const [commercials, total] = await Promise.all([
    prisma.commercial.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            firstname: true,
            lastname: true,
            telephone: true,
            createdAt: true,
            updatedAt: true,
            role: true,
          },
        },
        commercialclient: {
          include: {
            client: {
              include: {
                user: {
                  select: {
                    firstname: true,
                    lastname: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
      skip,
      take,
      orderBy: {
        user: {
          lastname: 'asc',
        },
      },
    }),
    prisma.commercial.count({ where }),
  ]);

  return { commercials, total };
}

// Obtenir un commercial par son ID
export async function getCommercialById(id: string) {
  return prisma.commercial.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true,
          firstname: true,
          lastname: true,
          telephone: true,
          createdAt: true,
          updatedAt: true,
          role: true,
        },
      },
      commercialclient: {
        include: {
          client: {
            include: {
              user: {
                select: {
                  firstname: true,
                  lastname: true,
                  email: true,
                },
              },
            },
          },
        },
      },
    },
  });
}

// Créer un nouveau commercial
export async function createCommercial(data: {
  email: string;
  username: string;
  password: string;
  firstname: string;
  lastname: string;
  telephone?: string;
  profile_photo?: string;
}) {
  try {
    const result = await createCommercialUser(data);
    return result;
  } catch (error) {
    console.error('Error creating commercial:', error);
    throw error;
  }
}

// Mettre à jour un commercial existant
export async function updateCommercial(
  id: string,
  data: {
    email?: string;
    firstname?: string;
    lastname?: string;
    telephone?: string;
    profile_photo?: string;
  }
) {
  const { email, firstname, lastname, telephone, profile_photo } = data;

  // Trouver le commercial pour obtenir l'ID de l'utilisateur
  const commercial = await prisma.commercial.findUnique({
    where: { id },
    include: { user: true },
  });

  if (!commercial) {
    throw new Error('Commercial not found');
  }

  return prisma.$transaction(async (tx) => {
    // Mettre à jour les informations de l'utilisateur
    if (email || firstname || lastname || telephone) {
      await tx.user.update({
        where: { id: commercial.userId },
        data: {
          ...(email && { email }),
          ...(firstname && { firstname }),
          ...(lastname && { lastname }),
          ...(telephone && { telephone }),
        },
      });
    }

    // Mettre à jour les informations spécifiques au commercial
    if (profile_photo !== undefined) {
      await tx.commercial.update({
        where: { id },
        data: {
          profile_photo,
        },
      });
    }

    // Récupérer le commercial mis à jour
    return tx.commercial.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            firstname: true,
            lastname: true,
            telephone: true,
            createdAt: true,
            updatedAt: true,
            role: true,
          },
        },
      },
    });
  });
}

// Supprimer un commercial
export async function deleteCommercial(id: string) {
  // Trouver le commercial pour obtenir l'ID de l'utilisateur
  const commercial = await prisma.commercial.findUnique({
    where: { id },
    include: { user: true },
  });

  if (!commercial) {
    throw new Error('Commercial not found');
  }

  return prisma.$transaction(async (tx) => {
    // Supprimer d'abord les relations avec les clients
    await tx.commercialclient.deleteMany({
      where: { commercialId: id },
    });

    // Supprimer le commercial
    await tx.commercial.delete({
      where: { id },
    });

    // Supprimer l'utilisateur associé
    return tx.user.delete({
      where: { id: commercial.userId },
    });
  });
}

// Assigner un client à un commercial
export async function assignClientToCommercial(commercialId: string, clientId: string) {
  return prisma.commercialclient.create({
    data: {
      commercialId,
      clientId,
    },
  });
}

// Retirer un client d'un commercial
export async function removeClientFromCommercial(commercialId: string, clientId: string) {
  return prisma.commercialclient.delete({
    where: {
      commercialId_clientId: {
        commercialId,
        clientId,
      },
    },
  });
}

// Obtenir les clients d'un commercial
export async function getCommercialClients(commercialId: string) {
  const commercialClients = await prisma.commercialclient.findMany({
    where: { commercialId },
    include: {
      client: {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              username: true,
              firstname: true,
              lastname: true,
              telephone: true,
            },
          },
        },
      },
    },
  });

  return commercialClients.map((cc) => cc.client);
}

// Obtenir les statistiques d'un commercial
export async function getCommercialStats(commercialId: string) {
  // Nombre de clients
  const clientCount = await prisma.commercialclient.count({
    where: { commercialId },
  });

  // Nombre de commandes des clients de ce commercial
  const clients = await prisma.commercialclient.findMany({
    where: { commercialId },
    select: { clientId: true },
  });

  const clientIds = clients.map((c) => c.clientId);

  const orderCount = await prisma.order.count({
    where: {
      clientId: { in: clientIds },
    },
  });

  // Montant total des commandes
  const orders = await prisma.order.findMany({
    where: {
      clientId: { in: clientIds },
    },
    select: { totalAmount: true },
  });

  const totalAmount = orders.reduce((sum, order) => sum + order.totalAmount, 0);

  return {
    clientCount,
    orderCount,
    totalAmount,
  };
}
