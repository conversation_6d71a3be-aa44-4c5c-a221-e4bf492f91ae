import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getSalesReports } from '@/lib/salesReports';
import * as XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';

// GET /api/sales-reports/export - Export sales reports
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can export sales reports
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Only administrators can export sales reports' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const format = searchParams.get('format') || 'excel';

    // Parse filter parameters
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const commercialId = searchParams.get('commercialId') || undefined;
    const city = searchParams.get('city') || undefined;

    // Handle specific dates
    const specificDatesCount = parseInt(searchParams.get('specificDatesCount') || '0');
    let specificDates: Date[] | undefined = undefined;

    if (specificDatesCount > 0) {
      specificDates = [];
      for (let i = 0; i < specificDatesCount; i++) {
        const dateParam = searchParams.get(`specificDate${i}`);
        if (dateParam) {
          specificDates.push(new Date(dateParam));
        }
      }
    }

    const filter: any = {};

    if (specificDates && specificDates.length > 0) {
      filter.specificDates = specificDates;
    } else {
      if (startDateParam) {
        filter.startDate = new Date(startDateParam);
      }

      if (endDateParam) {
        filter.endDate = new Date(endDateParam);
      }
    }

    if (commercialId) {
      filter.commercialId = commercialId;
    }

    if (city) {
      filter.city = city;
    }

    // Get all reports (no pagination for export)
    const { reports } = await getSalesReports(filter, 0, 1000);

    // Format data for export
    const exportData = reports.map(report => ({
      'Commercial': `${report.commercial.user.firstname} ${report.commercial.user.lastname}`,
      'Email': report.commercial.user.email,
      'Need': report.need,
      'Article Reference': report.articleRef || '',
      'Visit Date': new Date(report.visitDate).toLocaleDateString('fr-FR'),
      'Denomination': report.denomination,
      'Name': report.name,
      'Purpose of Visit': report.visitPurpose,
      'Complaint': report.complaint || '',
      'City': report.city,
      'Submitted At': new Date(report.submittedAt).toLocaleDateString('fr-FR'),
      'Has Video': report.videoUrl ? 'Yes' : 'No',
      'Has Audio': report.audioUrl ? 'Yes' : 'No',
      'Has PDF': report.pdfUrl ? 'Yes' : 'No',
    }));

    // Generate export based on format
    if (format === 'excel') {
      // Create Excel workbook
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales Reports');

      // Generate buffer
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return new NextResponse(excelBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': 'attachment; filename="sales-reports.xlsx"',
        },
      });
    } else if (format === 'csv') {
      // Create CSV
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const csvOutput = XLSX.utils.sheet_to_csv(worksheet);

      return new NextResponse(csvOutput, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="sales-reports.csv"',
        },
      });
    } else if (format === 'pdf') {
      // Create a simple HTML version that can be printed as PDF
      const logoPath = path.join(process.cwd(), 'public', 'images', 'logo', 'logo-moonelec.png');
      const logoExists = fs.existsSync(logoPath);

      // Get admin name
      const adminName = session.user.name || `${session.user.firstname || ''} ${session.user.lastname || ''}`.trim() || session.user.email || 'Administrateur';

      // Build filter information
      let filterInfo = '';
      if (specificDates && specificDates.length > 0) {
        if (specificDates.length === 1) {
          filterInfo = `Date spécifique: ${specificDates[0].toLocaleDateString('fr-FR')}`;
        } else {
          filterInfo = `Dates spécifiques: ${specificDates.map(d => d.toLocaleDateString('fr-FR')).join(', ')}`;
        }
      } else if (startDateParam && endDateParam) {
        filterInfo = `Période: ${new Date(startDateParam).toLocaleDateString('fr-FR')} au ${new Date(endDateParam).toLocaleDateString('fr-FR')}`;
      } else if (startDateParam) {
        filterInfo = `À partir du: ${new Date(startDateParam).toLocaleDateString('fr-FR')}`;
      } else if (endDateParam) {
        filterInfo = `Jusqu'au: ${new Date(endDateParam).toLocaleDateString('fr-FR')}`;
      }

      if (commercialId) {
        const commercial = reports.length > 0 ? reports[0].commercial : null;
        if (commercial) {
          filterInfo += (filterInfo ? ', ' : '') + `Commercial: ${commercial.user.firstname} ${commercial.user.lastname}`;
        }
      }
      if (city) {
        filterInfo += (filterInfo ? ', ' : '') + `Ville: ${city}`;
      }

      // Create HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Rapports de Vente - Moonelec</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 12px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
            }
            .logo {
              max-width: 200px;
              margin-bottom: 20px;
            }
            .admin-name {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .export-date {
              font-size: 12px;
              margin-bottom: 20px;
            }
            .filter-info {
              font-size: 10px;
              margin-bottom: 20px;
              text-align: left;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              font-size: 8px;
            }
            th, td {
              border: 1px solid #ccc;
              padding: 4px;
              text-align: center;
              word-wrap: break-word;
            }
            th {
              background-color: #f0f0f0;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 10px;
            }
            @page {
              size: A4 landscape;
              margin: 15mm;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            ${logoExists ? `<img src="data:image/png;base64,${fs.readFileSync(logoPath, 'base64')}" class="logo" alt="Moonelec Logo">` : '<h1>MOONELEC</h1>'}
            <div class="admin-name">Rapport généré par: ${adminName}</div>
            <div class="export-date">Date d'exportation: ${new Date().toLocaleDateString('fr-FR')}</div>
            ${filterInfo ? `<div class="filter-info">Critères de filtrage: ${filterInfo}</div>` : ''}
          </div>

          <div class="no-print" style="margin-bottom: 20px; text-align: center;">
            <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
              Imprimer en PDF
            </button>
          </div>

          <table>
            <thead>
              <tr>
                <th style="width: 8%;">ID</th>
                <th style="width: 15%;">Besoin</th>
                <th style="width: 10%;">Référence Article</th>
                <th style="width: 12%;">Commentaire</th>
                <th style="width: 10%;">Date de Visite</th>
                <th style="width: 12%;">Dénomination</th>
                <th style="width: 10%;">Nom</th>
                <th style="width: 13%;">Objet de la Visite</th>
                <th style="width: 10%;">Réclamation</th>
                <th style="width: 8%;">Ville</th>
              </tr>
            </thead>
            <tbody>
              ${reports.map(report => `
                <tr>
                  <td>${report.id.substring(0, 8)}...</td>
                  <td>${report.need.length > 30 ? report.need.substring(0, 30) + '...' : report.need}</td>
                  <td>${report.articleRef || '-'}</td>
                  <td>${report.comment ? (report.comment.length > 25 ? report.comment.substring(0, 25) + '...' : report.comment) : '-'}</td>
                  <td>${new Date(report.visitDate).toLocaleDateString('fr-FR')}</td>
                  <td>${report.denomination.length > 25 ? report.denomination.substring(0, 25) + '...' : report.denomination}</td>
                  <td>${report.name.length > 20 ? report.name.substring(0, 20) + '...' : report.name}</td>
                  <td>${report.visitPurpose.length > 30 ? report.visitPurpose.substring(0, 30) + '...' : report.visitPurpose}</td>
                  <td>${report.complaint ? (report.complaint.length > 25 ? report.complaint.substring(0, 25) + '...' : report.complaint) : '-'}</td>
                  <td>${report.city}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            Total des rapports: ${reports.length}
          </div>
        </body>
        </html>
      `;

      return new NextResponse(htmlContent, {
        headers: {
          'Content-Type': 'text/html',
          'Content-Disposition': 'inline; filename=sales-reports.html',
        },
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid export format. Supported formats: excel, csv, pdf' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error exporting sales reports:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to export sales reports' },
      { status: 500 }
    );
  }
}
