'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaSave, FaArrowLeft, FaSpinner, FaImage, FaVideo, FaMicrophone, FaFilePdf, FaTrash } from 'react-icons/fa';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import { toast } from 'react-hot-toast';

interface SalesReport {
  id: string;
  commercialId: string;
  need: string;
  articleRef?: string;
  comment?: string;
  visitDate: string;
  denomination: string;
  images?: string;
  imagesArray?: string[];
  name: string;
  visitPurpose: string;
  complaint?: string;
  city: string;
  videoUrl?: string;
  audioUrl?: string;
  pdfUrl?: string;
  submittedAt: string;
}

export default function EditSalesReportPage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [report, setReport] = useState<SalesReport | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Form state
  const [formData, setFormData] = useState({
    need: '',
    articleRef: '',
    comment: '',
    visitDate: '',
    denomination: '',
    name: '',
    visitPurpose: '',
    complaint: '',
    city: '',
  });

  // Media state
  const [images, setImages] = useState<File[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>([]);
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [existingVideoUrl, setExistingVideoUrl] = useState<string>('');
  const [existingAudioUrl, setExistingAudioUrl] = useState<string>('');
  const [existingPdfUrl, setExistingPdfUrl] = useState<string>('');

  // File input refs
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);
  const audioInputRef = useRef<HTMLInputElement>(null);
  const pdfInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (params.id) {
      fetchReport();
    }
  }, [params.id]);

  const fetchReport = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/sales-reports/${params.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch report');
      }
      
      const data = await response.json();
      setReport(data);
      
      // Populate form data
      setFormData({
        need: data.need || '',
        articleRef: data.articleRef || '',
        comment: data.comment || '',
        visitDate: data.visitDate ? data.visitDate.split('T')[0] : '',
        denomination: data.denomination || '',
        name: data.name || '',
        visitPurpose: data.visitPurpose || '',
        complaint: data.complaint || '',
        city: data.city || '',
      });

      // Set existing media
      setExistingImages(data.imagesArray || []);
      setExistingVideoUrl(data.videoUrl || '');
      setExistingAudioUrl(data.audioUrl || '');
      setExistingPdfUrl(data.pdfUrl || '');
      
    } catch (error) {
      console.error('Error fetching report:', error);
      toast.error('Erreur lors du chargement du rapport');
      router.push('/commercial/reports');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      setImages(prev => [...prev, ...Array.from(files)]);
    }
  };

  const handleVideoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setVideoFile(file);
    }
  };

  const handleAudioUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAudioFile(file);
    }
  };

  const handlePdfUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setPdfFile(file);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = (index: number) => {
    setExistingImages(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async () => {
    if (images.length === 0 && !videoFile && !audioFile && !pdfFile) {
      return { 
        imageUrls: existingImages, 
        videoUrl: existingVideoUrl, 
        audioUrl: existingAudioUrl, 
        pdfUrl: existingPdfUrl 
      };
    }
    
    const formData = new FormData();
    
    // Add new images
    images.forEach((image, index) => {
      formData.append(`image${index}`, image);
    });
    
    // Add video
    if (videoFile) {
      formData.append('video', videoFile);
    }
    
    // Add audio
    if (audioFile) {
      formData.append('audio', audioFile);
    }
    
    // Add PDF
    if (pdfFile) {
      formData.append('pdf', pdfFile);
    }
    
    try {
      const response = await fetch('/api/sales-reports/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload files');
      }
      
      const data = await response.json();
      
      return {
        imageUrls: [...existingImages, ...(data.imageUrls || [])],
        videoUrl: videoFile ? data.videoUrl : existingVideoUrl,
        audioUrl: audioFile ? data.audioUrl : existingAudioUrl,
        pdfUrl: pdfFile ? data.pdfUrl : existingPdfUrl,
      };
    } catch (error) {
      console.error('Error uploading files:', error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || user.role !== 'COMMERCIAL') {
      toast.error('Vous devez être connecté en tant que commercial');
      return;
    }
    
    setIsSubmitting(true);
    setUploadProgress(10);
    
    try {
      // Upload files
      setUploadProgress(30);
      const { imageUrls, videoUrl, audioUrl, pdfUrl } = await uploadFiles();
      
      setUploadProgress(60);
      
      // Update report
      const response = await fetch(`/api/sales-reports/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          visitDate: new Date(formData.visitDate),
          images: imageUrls,
          videoUrl,
          audioUrl,
          pdfUrl,
        }),
      });
      
      setUploadProgress(90);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update report');
      }
      
      setUploadProgress(100);
      toast.success('Rapport modifié avec succès!');
      router.push('/commercial/reports');
      
    } catch (error: any) {
      console.error('Error updating report:', error);
      toast.error(error.message || 'Erreur lors de la modification du rapport');
    } finally {
      setIsSubmitting(false);
      setUploadProgress(0);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <FaSpinner className="animate-spin text-4xl text-primary" />
      </div>
    );
  }

  if (!report) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
            Rapport non trouvé
          </h2>
          <Link
            href="/commercial/reports"
            className="text-primary hover:text-primary-dark"
          >
            Retour aux rapports
          </Link>
        </div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['COMMERCIAL']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Link
              href="/commercial/reports"
              className="mr-4 p-2 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              <FaArrowLeft />
            </Link>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Modifier le rapport de visite
            </h1>
          </div>
        </div>

        {/* Progress Bar */}
        {isSubmitting && (
          <div className="mb-6">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Modification en cours... {uploadProgress}%
            </p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nom du client <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div>
              <label htmlFor="denomination" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Dénomination <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="denomination"
                name="denomination"
                value={formData.denomination}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
          </div>

          {/* Continue with rest of form... */}
          <div className="flex justify-end space-x-3">
            <Link
              href="/commercial/reports"
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Annuler
            </Link>
            
            <motion.button
              type="submit"
              disabled={isSubmitting}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center ${
                isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {isSubmitting ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Modification...
                </>
              ) : (
                <>
                  <FaSave className="mr-2" />
                  Modifier
                </>
              )}
            </motion.button>
          </div>
        </form>
      </div>
    </RouteGuard>
  );
}
