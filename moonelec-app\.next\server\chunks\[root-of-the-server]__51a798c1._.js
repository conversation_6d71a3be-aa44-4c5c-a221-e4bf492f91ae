module.exports = {

"[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[project]/src/lib/products.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addProductImages": (()=>addProductImages),
    "createProduct": (()=>createProduct),
    "deleteAllProductImages": (()=>deleteAllProductImages),
    "deleteProduct": (()=>deleteProduct),
    "deleteProductImage": (()=>deleteProductImage),
    "getProductById": (()=>getProductById),
    "getProductByReference": (()=>getProductByReference),
    "getProducts": (()=>getProducts),
    "updateProduct": (()=>updateProduct),
    "updateProductImage": (()=>updateProductImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
async function getProducts(options) {
    const { categoryId, brandId, search, skip = 0, take = 50 } = options || {};
    const where = {
        ...categoryId ? {
            categoryId
        } : {},
        ...brandId ? {
            brandId
        } : {},
        ...search ? {
            OR: [
                {
                    name: {
                        contains: search
                    }
                },
                {
                    reference: {
                        contains: search
                    }
                },
                {
                    description: {
                        contains: search
                    }
                }
            ]
        } : {}
    };
    const [products, total] = await Promise.all([
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findMany({
            where,
            include: {
                category: true,
                brand: true,
                productimage: {
                    orderBy: {
                        order: 'asc'
                    }
                }
            },
            skip,
            take,
            orderBy: {
                createdAt: 'desc'
            }
        }),
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.count({
            where
        })
    ]);
    return {
        products,
        total
    };
}
async function getProductById(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findUnique({
        where: {
            id
        },
        include: {
            category: true,
            brand: true,
            productimage: {
                orderBy: {
                    order: 'asc'
                }
            }
        }
    });
}
async function getProductByReference(reference) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findUnique({
        where: {
            reference
        },
        include: {
            category: true,
            brand: true,
            productimage: {
                orderBy: {
                    order: 'asc'
                }
            }
        }
    });
}
async function createProduct(data) {
    const { images, ...productData } = data;
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Check if reference already exists
        const existingProduct = await tx.product.findUnique({
            where: {
                reference: productData.reference
            }
        });
        if (existingProduct) {
            throw new Error(`Un produit avec la référence ${productData.reference} existe déjà`);
        }
        // Create the product
        const product = await tx.product.create({
            data: productData
        });
        // Add images if provided
        if (images && images.length > 0) {
            await tx.productImage.createMany({
                data: images.map((image, index)=>({
                        url: image.url,
                        alt: image.alt || product.name,
                        order: image.order || index,
                        productId: product.id
                    }))
            });
        }
        return product;
    });
}
async function updateProduct(id, data) {
    // If reference is being updated, check if it already exists
    if (data.reference) {
        const existingProduct = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findFirst({
            where: {
                reference: data.reference,
                id: {
                    not: id
                }
            }
        });
        if (existingProduct) {
            throw new Error(`Un produit avec la référence ${data.reference} existe déjà`);
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.update({
        where: {
            id
        },
        data,
        include: {
            category: true,
            brand: true,
            productimage: {
                orderBy: {
                    order: 'asc'
                }
            }
        }
    });
}
async function deleteProduct(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // 1. Supprimer d'abord les références dans QuoteItem
        await tx.quoteItem.deleteMany({
            where: {
                productId: id
            }
        });
        // 2. Supprimer les références dans OrderItem
        await tx.orderItem.deleteMany({
            where: {
                productId: id
            }
        });
        // 3. Supprimer les images du produit
        await tx.productimage.deleteMany({
            where: {
                productId: id
            }
        });
        // 4. Enfin, supprimer le produit lui-même
        return tx.product.delete({
            where: {
                id
            }
        });
    });
}
async function addProductImages(productId, images) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].productimage.createMany({
        data: images.map((image, index)=>({
                url: image.url,
                alt: image.alt,
                order: image.order || index,
                productId
            }))
    });
}
async function updateProductImage(id, data) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].productimage.update({
        where: {
            id
        },
        data
    });
}
async function deleteProductImage(id) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].productimage.delete({
        where: {
            id
        }
    });
}
async function deleteAllProductImages(productId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].productimage.deleteMany({
        where: {
            productId
        }
    });
}
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[project]/src/lib/mobile-auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMobileUserFromRequest": (()=>getMobileUserFromRequest),
    "isMobileRequest": (()=>isMobileRequest),
    "verifyMobileToken": (()=>verifyMobileToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
async function verifyMobileToken(request) {
    try {
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('🔐 No valid authorization header found');
            return null;
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        console.log('🔐 Mobile token received:', token.substring(0, 50) + '...');
        // Verify JWT token using JWT_SECRET (for mobile auth)
        const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';
        console.log('🔐 Using JWT secret:', ("TURBOPACK compile-time truthy", 1) ? 'Available' : ("TURBOPACK unreachable", undefined));
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, jwtSecret);
        console.log('🔐 Token decoded successfully:', {
            userId: decoded.userId,
            role: decoded.role
        });
        // Get fresh user data from database
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: decoded.userId
            },
            select: {
                id: true,
                username: true,
                email: true,
                firstname: true,
                lastname: true,
                role: true,
                isActive: true
            }
        });
        if (!user || !user.isActive) {
            console.log('🔐 User not found or inactive:', {
                found: !!user,
                active: user?.isActive
            });
            return null;
        }
        console.log('🔐 Mobile user authenticated successfully:', {
            id: user.id,
            role: user.role
        });
        return user;
    } catch (error) {
        console.error('Mobile token verification error:', error);
        return null;
    }
}
function isMobileRequest(request) {
    const userAgent = request.headers.get('user-agent') || '';
    const authHeader = request.headers.get('authorization');
    // Check if it's a mobile request with JWT token
    return authHeader?.startsWith('Bearer ') || userAgent.includes('Expo') || userAgent.includes('ReactNative');
}
async function getMobileUserFromRequest(request) {
    if (!isMobileRequest(request)) {
        return null;
    }
    return await verifyMobileToken(request);
}
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminUser": (()=>createAdminUser),
    "createClientUser": (()=>createClientUser),
    "createCommercialUser": (()=>createCommercialUser),
    "findUserByEmail": (()=>findUserByEmail),
    "findUserByUsername": (()=>findUserByUsername),
    "hashPassword": (()=>hashPassword),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
async function hashPassword(password) {
    const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(10);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, salt);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
async function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].CLIENT
            }
        });
        // Créer le profil client associé
        const client = await tx.client.create({
            data: {
                userId: user.id,
                company_name
            }
        });
        return {
            user,
            client
        };
    });
}
async function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].COMMERCIAL
            }
        });
        // Créer le profil commercial associé
        const commercial = await tx.commercial.create({
            data: {
                userId: user.id,
                profile_photo
            }
        });
        return {
            user,
            commercial
        };
    });
}
async function createAdminUser({ email, username, password, lastname, firstname, telephone }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].ADMIN
            }
        });
        // Créer le profil admin associé
        const admin = await tx.admin.create({
            data: {
                userId: user.id
            }
        });
        return {
            user,
            admin
        };
    });
}
async function findUserByUsername(username) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            username
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
async function findUserByEmail(email) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            email
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
}}),
"[project]/src/lib/auth-options.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'Credentials',
            credentials: {
                username: {
                    label: "Nom d'utilisateur",
                    type: 'text'
                },
                password: {
                    label: 'Mot de passe',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.username || !credentials?.password) {
                    return null;
                }
                try {
                    // Rechercher l'utilisateur par nom d'utilisateur
                    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findUserByUsername"])(credentials.username);
                    // Vérifier si l'utilisateur existe
                    if (!user) {
                        return null;
                    }
                    // Vérifier le mot de passe
                    const isPasswordValid = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyPassword"])(credentials.password, user.password);
                    if (!isPasswordValid) {
                        return null;
                    }
                    // Retourner les données de l'utilisateur sans le mot de passe
                    return {
                        id: user.id,
                        email: user.email,
                        username: user.username,
                        name: `${user.firstname} ${user.lastname}`,
                        firstname: user.firstname,
                        lastname: user.lastname,
                        role: user.role,
                        clientId: user.client?.id,
                        commercialId: user.commercial?.id,
                        adminId: user.admin?.id
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.username = user.username;
                token.role = user.role;
                token.firstname = user.firstname;
                token.lastname = user.lastname;
                token.clientId = user.clientId;
                token.commercialId = user.commercialId;
                token.adminId = user.adminId;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.id;
                session.user.username = token.username;
                session.user.role = token.role;
                session.user.firstname = token.firstname;
                session.user.lastname = token.lastname;
                session.user.clientId = token.clientId;
                session.user.commercialId = token.commercialId;
                session.user.adminId = token.adminId;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signOut: '/auth/signout',
        error: '/auth/error'
    },
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/app/api/products/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$products$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/products.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mobile-auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-options.ts [app-route] (ecmascript)");
;
;
;
;
;
async function GET(req) {
    try {
        // Check for mobile authentication
        const mobileUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMobileUserFromRequest"])(req);
        if (mobileUser) {
            console.log('📱 Mobile user authenticated:', mobileUser.username);
        }
        const searchParams = req.nextUrl.searchParams;
        const categoryId = searchParams.get('categoryId') || undefined;
        const brandId = searchParams.get('brandId') || undefined;
        const search = searchParams.get('search') || undefined;
        const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')) : undefined;
        const take = searchParams.has('take') ? parseInt(searchParams.get('take')) : undefined;
        const { products, total } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$products$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getProducts"])({
            categoryId,
            brandId,
            search,
            skip,
            take
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            products,
            total
        });
    } catch (error) {
        console.error('Error fetching products:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to fetch products'
        }, {
            status: 500
        });
    }
}
async function POST(req) {
    try {
        const body = await req.json();
        const { reference, name, description, characteristics, mainImage, categoryId, brandId, images } = body;
        // Validate required fields
        if (!reference || !name || !description) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Reference, name, and description are required'
            }, {
                status: 400
            });
        }
        // Create the product
        const product = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$products$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createProduct"])({
            reference,
            name,
            description,
            characteristics: characteristics || {},
            mainImage,
            categoryId: categoryId || undefined,
            brandId: brandId || undefined,
            images
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(product, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating product:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to create product'
        }, {
            status: 500
        });
    }
}
async function POST(req) {
    try {
        // Check for mobile authentication first
        const mobileUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMobileUserFromRequest"])(req);
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!mobileUser && !session) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Use mobile user data if available, otherwise use session
        const user = mobileUser || session?.user;
        if (!user || user.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized - Admin access required'
            }, {
                status: 403
            });
        }
        const body = await req.json();
        const { reference, name, description, characteristics, mainImage, categoryId, brandId, images } = body;
        // Validate required fields
        if (!reference || !name) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Reference and name are required'
            }, {
                status: 400
            });
        }
        // Create the product
        const product = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$products$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createProduct"])({
            reference,
            name,
            description,
            characteristics: characteristics || {},
            mainImage,
            categoryId,
            brandId,
            images: images || []
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(product, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating product:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to create product'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__51a798c1._.js.map