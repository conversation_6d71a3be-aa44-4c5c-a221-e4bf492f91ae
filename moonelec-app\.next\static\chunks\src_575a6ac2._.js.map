{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\n\nexport function useAuth() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading = status === 'loading';\n  const user = session?.user;\n\n  const isClient = isAuthenticated && user?.role === 'CLIENT';\n  const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';\n  const isAdmin = isAuthenticated && user?.role === 'ADMIN';\n\n  const login = async (username: string, password: string) => {\n    const result = await signIn('credentials', {\n      username,\n      password,\n      redirect: false,\n    });\n\n    return result;\n  };\n\n  const logout = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const redirectToLogin = () => {\n    router.push('/auth/signin');\n  };\n\n  const redirectToDashboard = () => {\n    if (isAdmin) {\n      router.push('/admin/quotes');\n    } else if (isCommercial) {\n      router.push('/commercial/quotes');\n    } else if (isClient) {\n      router.push('/account/quotes');\n    } else {\n      router.push('/');\n    }\n  };\n\n  return {\n    session,\n    status,\n    user,\n    isAuthenticated,\n    isLoading,\n    isClient,\n    isCommercial,\n    isAdmin,\n    login,\n    logout,\n    redirectToLogin,\n    redirectToDashboard,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,WAAW;IACnC,MAAM,YAAY,WAAW;IAC7B,MAAM,OAAO,SAAS;IAEtB,MAAM,WAAW,mBAAmB,MAAM,SAAS;IACnD,MAAM,eAAe,mBAAmB,MAAM,SAAS;IACvD,MAAM,UAAU,mBAAmB,MAAM,SAAS;IAElD,MAAM,QAAQ,OAAO,UAAkB;QACrC,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;YACzC;YACA;YACA,UAAU;QACZ;QAEA,OAAO;IACT;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,cAAc;YACvB,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,UAAU;YACnB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzDgB;;QACoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/AdminSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport Image from 'next/image';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  FaTachometerAlt,\n  FaUsers,\n  FaUserTie,\n  FaShoppingCart,\n  FaBox,\n  FaCog,\n  FaChartBar,\n  FaChevronLeft,\n  FaChevronRight,\n  FaTags,\n  FaCopyright,\n  FaClipboardList\n} from 'react-icons/fa';\n\nexport default function AdminSidebar() {\n  const pathname = usePathname();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  const menuItems = [\n    { icon: <FaTachometerAlt />, label: 'Tableau de bord', href: '/admin/dashboard' },\n    { icon: <FaUsers />, label: 'Clients', href: '/admin/clients' },\n    { icon: <FaUserTie />, label: 'Commerciaux', href: '/admin/commercials' },\n    { icon: <FaShoppingCart />, label: 'Devis', href: '/admin/quotes' },\n    { icon: <FaBox />, label: 'Produits', href: '/admin/products' },\n    { icon: <FaTags />, label: 'Catégories', href: '/admin/categories' },\n    { icon: <FaCopyright />, label: 'Marques', href: '/admin/brands' },\n    { icon: <FaClipboardList />, label: 'Rapports', href: '/admin/reports' },\n    { icon: <FaChartBar />, label: 'Statistiques', href: '/admin/statistics' },\n    { icon: <FaCog />, label: 'Paramètres', href: '/admin/settings' },\n  ];\n\n  const toggleSidebar = () => {\n    setIsCollapsed(!isCollapsed);\n  };\n\n  return (\n    <motion.div\n      className={`bg-[#0a1f2f] text-white ${isCollapsed ? 'w-20' : 'w-64'} h-screen flex flex-col transition-all duration-300 shadow-lg z-20`}\n      animate={{ width: isCollapsed ? 80 : 256 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Logo */}\n      <div className=\"p-4 flex justify-between items-center border-b border-gray-700\">\n        <AnimatePresence>\n          {!isCollapsed && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"flex items-center\"\n            >\n              <Image\n                src=\"/images/logo/logo-moonelec.png\"\n                alt=\"Moonelec Logo\"\n                width={150}\n                height={40}\n                className=\"h-10 w-auto\"\n              />\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {isCollapsed && (\n          <div className=\"mx-auto\">\n            <Image\n              src=\"/images/logo/logo-icon.png\"\n              alt=\"Moonelec Icon\"\n              width={32}\n              height={32}\n              className=\"h-8 w-auto\"\n            />\n          </div>\n        )}\n\n        <button\n          onClick={toggleSidebar}\n          className=\"text-gray-400 hover:text-white transition-colors p-1 rounded-full hover:bg-gray-700\"\n        >\n          {isCollapsed ? <FaChevronRight size={16} /> : <FaChevronLeft size={16} />}\n        </button>\n      </div>\n\n      {/* Menu Items */}\n      <nav className=\"flex-1 overflow-y-auto py-4\">\n        <ul className=\"space-y-2 px-2\">\n          {menuItems.map((item, index) => {\n            const isActive = pathname === item.href;\n\n            return (\n              <motion.li\n                key={index}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.3, delay: index * 0.05 }}\n              >\n                <Link\n                  href={item.href}\n                  className={`flex items-center p-3 rounded-lg transition-all ${\n                    isActive\n                      ? 'bg-primary text-white'\n                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                  }`}\n                >\n                  <span className=\"text-xl\">{item.icon}</span>\n                  <AnimatePresence>\n                    {!isCollapsed && (\n                      <motion.span\n                        initial={{ opacity: 0, width: 0 }}\n                        animate={{ opacity: 1, width: 'auto' }}\n                        exit={{ opacity: 0, width: 0 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"ml-3 whitespace-nowrap overflow-hidden\"\n                      >\n                        {item.label}\n                      </motion.span>\n                    )}\n                  </AnimatePresence>\n                </Link>\n              </motion.li>\n            );\n          })}\n        </ul>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-gray-700 text-center\">\n        <AnimatePresence>\n          {!isCollapsed && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"text-xs text-gray-400\"\n            >\n              © {new Date().getFullYear()} Moonelec\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAsBe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,YAAY;QAChB;YAAE,oBAAM,6LAAC,iJAAA,CAAA,kBAAe;;;;;YAAK,OAAO;YAAmB,MAAM;QAAmB;QAChF;YAAE,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;YAAK,OAAO;YAAW,MAAM;QAAiB;QAC9D;YAAE,oBAAM,6LAAC,iJAAA,CAAA,YAAS;;;;;YAAK,OAAO;YAAe,MAAM;QAAqB;QACxE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,iBAAc;;;;;YAAK,OAAO;YAAS,MAAM;QAAgB;QAClE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,QAAK;;;;;YAAK,OAAO;YAAY,MAAM;QAAkB;QAC9D;YAAE,oBAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;YAAK,OAAO;YAAc,MAAM;QAAoB;QACnE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,cAAW;;;;;YAAK,OAAO;YAAW,MAAM;QAAgB;QACjE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,kBAAe;;;;;YAAK,OAAO;YAAY,MAAM;QAAiB;QACvE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;YAAK,OAAO;YAAgB,MAAM;QAAoB;QACzE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,QAAK;;;;;YAAK,OAAO;YAAc,MAAM;QAAkB;KACjE;IAED,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,wBAAwB,EAAE,cAAc,SAAS,OAAO,kEAAkE,CAAC;QACvI,SAAS;YAAE,OAAO,cAAc,KAAK;QAAI;QACzC,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4LAAA,CAAA,kBAAe;kCACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;oBAMjB,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAKhB,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAET,4BAAc,6LAAC,iJAAA,CAAA,iBAAc;4BAAC,MAAM;;;;;iDAAS,6LAAC,iJAAA,CAAA,gBAAa;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKvE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,MAAM;wBACpB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BAER,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAK;sCAEjD,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,gDAAgD,EAC1D,WACI,0BACA,oDACJ;;kDAEF,6LAAC;wCAAK,WAAU;kDAAW,KAAK,IAAI;;;;;;kDACpC,6LAAC,4LAAA,CAAA,kBAAe;kDACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAO;4CACrC,MAAM;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAC7B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDAET,KAAK,KAAK;;;;;;;;;;;;;;;;;2BAvBd;;;;;oBA8BX;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;4BACX;4BACI,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAO1C;GAjIwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/admin/AdminHeader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Image from 'next/image';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaBell, FaSearch, FaMoon, FaSun, FaUserCircle, FaSignOutAlt, FaCog } from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useTheme } from '@/context/ThemeContext';\n\nexport default function AdminHeader() {\n  const router = useRouter();\n  const { user, logout } = useAuth();\n  const { actualTheme, toggleTheme } = useTheme();\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);\n  const profileRef = useRef<HTMLDivElement>(null);\n  const notificationsRef = useRef<HTMLDivElement>(null);\n\n  // Notifications simulées\n  const notifications = [\n    { id: 1, message: 'Nouvelle commande #12345', time: 'Il y a 5 minutes', isRead: false },\n    { id: 2, message: 'Nouveau client inscrit', time: 'Il y a 30 minutes', isRead: false },\n    { id: 3, message: 'Mise à jour du stock terminée', time: 'Il y a 2 heures', isRead: true },\n  ];\n\n  // Gérer le clic en dehors des menus déroulants\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {\n        setIsProfileOpen(false);\n      }\n      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {\n        setIsNotificationsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n\n\n  const handleLogout = async () => {\n    await logout();\n    router.push('/');\n  };\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"flex items-center justify-between px-4 py-3\">\n        {/* Search Bar */}\n        <div className=\"relative w-64 md:w-96\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <FaSearch className=\"text-gray-400\" />\n          </div>\n          <input\n            type=\"text\"\n            className=\"block w-full pl-10 pr-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary\"\n            placeholder=\"Rechercher...\"\n          />\n        </div>\n\n        {/* Right Side Actions */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Theme Toggle */}\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={toggleTheme}\n            className=\"p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n            aria-label={actualTheme === 'dark' ? 'Passer en mode clair' : 'Passer en mode sombre'}\n          >\n            {actualTheme === 'dark' ? <FaSun className=\"text-yellow-400\" /> : <FaMoon className=\"text-blue-600\" />}\n          </motion.button>\n\n          {/* Notifications */}\n          <div className=\"relative\" ref={notificationsRef}>\n            <motion.button\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}\n              className=\"p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 relative\"\n            >\n              <FaBell />\n              {notifications.some(n => !n.isRead) && (\n                <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n              )}\n            </motion.button>\n\n            <AnimatePresence>\n              {isNotificationsOpen && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50\"\n                >\n                  <div className=\"p-3 border-b border-gray-200 dark:border-gray-700\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white\">Notifications</h3>\n                  </div>\n                  <div className=\"max-h-96 overflow-y-auto\">\n                    {notifications.length > 0 ? (\n                      <ul>\n                        {notifications.map((notification) => (\n                          <li\n                            key={notification.id}\n                            className={`p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 ${\n                              !notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''\n                            }`}\n                          >\n                            <div className=\"flex justify-between\">\n                              <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                                {notification.message}\n                              </p>\n                              {!notification.isRead && (\n                                <span className=\"w-2 h-2 bg-blue-500 rounded-full\"></span>\n                              )}\n                            </div>\n                            <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                              {notification.time}\n                            </p>\n                          </li>\n                        ))}\n                      </ul>\n                    ) : (\n                      <div className=\"p-4 text-center text-gray-500 dark:text-gray-400\">\n                        Aucune notification\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"p-2 text-center border-t border-gray-200 dark:border-gray-700\">\n                    <button className=\"text-sm text-primary hover:underline\">\n                      Marquer tout comme lu\n                    </button>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative\" ref={profileRef}>\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-2 focus:outline-none\"\n            >\n              <div className=\"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden\">\n                {user?.adminId ? (\n                  <div className=\"w-full h-full flex items-center justify-center\">\n                    <FaUserCircle className=\"w-full h-full text-primary\" />\n                  </div>\n                ) : (\n                  <FaUserCircle className=\"w-full h-full text-gray-400\" />\n                )}\n              </div>\n              <span className=\"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {user?.firstname} {user?.lastname}\n              </span>\n            </button>\n\n            <AnimatePresence>\n              {isProfileOpen && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50\"\n                >\n                  <div className=\"p-3 border-b border-gray-200 dark:border-gray-700\">\n                    <p className=\"text-sm font-medium text-gray-800 dark:text-white\">\n                      {user?.firstname} {user?.lastname}\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">{user?.email}</p>\n                  </div>\n                  <ul>\n                    <li>\n                      <button\n                        onClick={() => router.push('/admin/profile')}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      >\n                        <FaUserCircle className=\"mr-2\" />\n                        Mon profil\n                      </button>\n                    </li>\n                    <li>\n                      <button\n                        onClick={() => router.push('/admin/settings')}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      >\n                        <FaCog className=\"mr-2\" />\n                        Paramètres\n                      </button>\n                    </li>\n                    <li className=\"border-t border-gray-200 dark:border-gray-700\">\n                      <button\n                        onClick={handleLogout}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20\"\n                      >\n                        <FaSignOutAlt className=\"mr-2\" />\n                        Déconnexion\n                      </button>\n                    </li>\n                  </ul>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAC5C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEhD,yBAAyB;IACzB,MAAM,gBAAgB;QACpB;YAAE,IAAI;YAAG,SAAS;YAA4B,MAAM;YAAoB,QAAQ;QAAM;QACtF;YAAE,IAAI;YAAG,SAAS;YAA0B,MAAM;YAAqB,QAAQ;QAAM;QACrF;YAAE,IAAI;YAAG,SAAS;YAAiC,MAAM;YAAmB,QAAQ;QAAK;KAC1F;IAED,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5E,iBAAiB;oBACnB;oBACA,IAAI,iBAAiB,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACxF,uBAAuB;oBACzB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG,EAAE;IAIL,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;4BACT,WAAU;4BACV,cAAY,gBAAgB,SAAS,yBAAyB;sCAE7D,gBAAgB,uBAAS,6LAAC,iJAAA,CAAA,QAAK;gCAAC,WAAU;;;;;qDAAuB,6LAAC,iJAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAItF,6LAAC;4BAAI,WAAU;4BAAW,KAAK;;8CAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,uBAAuB,CAAC;oCACvC,WAAU;;sDAEV,6LAAC,iJAAA,CAAA,SAAM;;;;;wCACN,cAAc,IAAI,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,mBAChC,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAIpB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,qCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAsD;;;;;;;;;;;0DAEtE,6LAAC;gDAAI,WAAU;0DACZ,cAAc,MAAM,GAAG,kBACtB,6LAAC;8DACE,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;4DAEC,WAAW,CAAC,0FAA0F,EACpG,CAAC,aAAa,MAAM,GAAG,mCAAmC,IAC1D;;8EAEF,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,aAAa,OAAO;;;;;;wEAEtB,CAAC,aAAa,MAAM,kBACnB,6LAAC;4EAAK,WAAU;;;;;;;;;;;;8EAGpB,6LAAC;oEAAE,WAAU;8EACV,aAAa,IAAI;;;;;;;2DAdf,aAAa,EAAE;;;;;;;;;yEAoB1B,6LAAC;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAKtE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUnE,6LAAC;4BAAI,WAAU;4BAAW,KAAK;;8CAC7B,6LAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACZ,MAAM,wBACL,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iJAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;qEAG1B,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAG5B,6LAAC;4CAAK,WAAU;;gDACb,MAAM;gDAAU;gDAAE,MAAM;;;;;;;;;;;;;8CAI7B,6LAAC,4LAAA,CAAA,kBAAe;8CACb,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DACV,MAAM;4DAAU;4DAAE,MAAM;;;;;;;kEAE3B,6LAAC;wDAAE,WAAU;kEAA4C,MAAM;;;;;;;;;;;;0DAEjE,6LAAC;;kEACC,6LAAC;kEACC,cAAA,6LAAC;4DACC,SAAS,IAAM,OAAO,IAAI,CAAC;4DAC3B,WAAU;;8EAEV,6LAAC,iJAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAS;;;;;;;;;;;;kEAIrC,6LAAC;kEACC,cAAA,6LAAC;4DACC,SAAS,IAAM,OAAO,IAAI,CAAC;4DAC3B,WAAU;;8EAEV,6LAAC,iJAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAS;;;;;;;;;;;;kEAI9B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,6LAAC,iJAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazD;GA9MwB;;QACP,qIAAA,CAAA,YAAS;QACC,0HAAA,CAAA,UAAO;QACK,kIAAA,CAAA,WAAQ;;;KAHvB", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/shared/LoadingAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface LoadingAnimationProps {\n  isLoading?: boolean;\n  onLoadingComplete?: () => void;\n}\n\nexport default function LoadingAnimation({\n  isLoading = true,\n  onLoadingComplete\n}: LoadingAnimationProps) {\n  const [loading, setLoading] = useState(isLoading);\n  const [fadeOut, setFadeOut] = useState(false);\n\n  useEffect(() => {\n    if (isLoading) {\n      // Réduire le délai initial à 300ms\n      const timer = setTimeout(() => {\n        setFadeOut(true);\n\n        // Attendre la fin de l'animation de fade out avant de cacher complètement\n        const hideTimer = setTimeout(() => {\n          setLoading(false);\n          if (onLoadingComplete) onLoadingComplete();\n        }, 300); // Durée de l'animation de fade out réduite\n\n        return () => clearTimeout(hideTimer);\n      }, 300); // <PERSON><PERSON>lai initial réduit\n\n      return () => clearTimeout(timer);\n    } else {\n      // Si isLoading est false dès le départ, cacher immédiatement\n      setLoading(false);\n    }\n  }, [isLoading, onLoadingComplete]);\n\n  if (!loading) {\n    return null;\n  }\n\n  return (\n    <div\n      className={`fixed inset-0 z-50 flex items-center justify-center bg-[#1a1a1a] transition-opacity duration-500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`}\n    >\n          <style jsx global>{`\n            .loading-svg #ISpzKj7McrRg {\n              opacity: 0;\n              animation: opacitypath 1s ease-in-out forwards;\n            }\n\n            .loading-svg #IMb5qOBkYi8y2 {\n              opacity: 0;\n              animation: opacitypath2 1.2s ease-in-out forwards;\n            }\n\n            .loading-svg #IMb5qOBkYi8y {\n              filter: drop-shadow(3px 3px 2px rgba(255, 255, 255, 0.7));\n              animation: animationshadow 2s ease-in-out infinite;\n            }\n\n            .loading-svg #eGByPCsADLe4 {\n              transform-origin: center;\n              animation: movementAnimation 1s ease-in-out forwards;\n            }\n\n            .loading-svg #rOMyCEsDLSe1 {\n              stroke-dasharray: 300;\n              stroke-dashoffset: 300;\n              animation: animationpath 1s ease-in-out forwards;\n            }\n\n            @keyframes animationpath {\n              0% {\n                stroke-dashoffset: 300;\n              }\n              33% {\n                stroke-dashoffset: 225;\n              }\n              66% {\n                stroke-dashoffset: 182;\n              }\n              99% {\n                stroke-dashoffset: 120;\n              }\n              100% {\n                stroke-dashoffset: 99;\n              }\n            }\n\n            @keyframes opacitypath {\n              80% {\n                opacity: 0;\n              }\n              100% {\n                opacity: 1;\n              }\n            }\n\n            @keyframes opacitypath2 {\n              80% {\n                opacity: 0;\n              }\n              100% {\n                opacity: 1;\n              }\n            }\n\n            @keyframes movementAnimation {\n              0% {\n                transform: translate(-115px, 58px);\n              }\n              33.33% {\n                transform: translate(-65px, 5px);\n              }\n              66.66% {\n                transform: translate(-34px, 31px);\n              }\n              99.99% {\n                transform: translate(25px, -23px);\n              }\n              100% {\n                transform: translate(25px, -23px);\n              }\n            }\n\n            @keyframes animationshadow {\n              0% {\n                filter: drop-shadow(-10px 0px 10px rgba(255, 255, 255, 0.7));\n              }\n              50% {\n                filter: drop-shadow(4px -8px 14px rgba(255, 255, 255, 0.7));\n              }\n              100% {\n                filter: drop-shadow(-10px 0px 10px rgba(255, 255, 255, 0.7));\n              }\n            }\n          `}</style>\n\n          <svg\n            className=\"loading-svg\"\n            id=\"eGByPCsADLe1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n            viewBox=\"0 0 264.58333 264.58333\"\n            width=\"120\"\n            height=\"120\"\n          >\n            <path\n              id=\"ISpzKj7McrRg\"\n              d=\"M122.48,78.945c-19.782763,0-38.755276,7.858672-52.743802,21.847198s-21.847198,32.961039-21.847198,52.743802s7.858672,38.755276,21.847198,52.743802s32.961039,21.847198,52.743802,21.847198s38.755276-7.858672,52.743802-21.847198s21.847198-32.961039,21.847198-52.743802c0-41.195472-33.395528-74.591-74.591-74.591Zm-14.202,4.7329c-20.059262,13.206865-32.143233,35.606447-32.165,59.623c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c4.775729-.058974,9.533518-.596562,14.202-1.6047-11.659545,7.704127-25.322088,11.820586-39.297,11.84-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.085181-33.925568,24.011087-63.115508,57.26-69.858v-.0003Z\"\n              transform=\"translate(25.238-21.244)\"\n              fill=\"#006db7\"\n            />\n            <g id=\"IMb5qOBkYi8y2\">\n              <path\n                id=\"IMb5qOBkYi8y\"\n                d=\"M108.28,83.678c-33.248913,6.742492-57.174819,35.932432-57.26,69.858c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c13.974912-.019414,27.637455-4.135873,39.297-11.84-4.668482,1.008138-9.426271,1.545726-14.202,1.6047-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.021767-24.016553,12.105738-46.416135,32.165-59.623v.0003Z\"\n                transform=\"translate(25.238-21.244)\"\n                fill=\"#fff\"\n              />\n            </g>\n            <path\n              id=\"eGByPCsADLe4\"\n              d=\"M131.51,121l40.383.30632.2758,43.221\"\n              transform=\"translate(-122.494724 62.610263)\"\n              fill=\"none\"\n              stroke=\"#ed1c24\"\n              strokeWidth=\"24.133\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n            />\n            <path\n              id=\"rOMyCEsDLSe1\"\n              d=\"M29.098,200.61l53.006-53.126l30.765,31.282l58.655-57.504\"\n              transform=\"translate(25.238-21.244)\"\n              fill=\"none\"\n              stroke=\"#ed1c24\"\n              strokeWidth=\"24.133\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeDasharray=\"480\"\n            />\n          </svg>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AASe,SAAS,iBAAiB,EACvC,YAAY,IAAI,EAChB,iBAAiB,EACK;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;gBACb,mCAAmC;gBACnC,MAAM,QAAQ;wDAAW;wBACvB,WAAW;wBAEX,0EAA0E;wBAC1E,MAAM,YAAY;0EAAW;gCAC3B,WAAW;gCACX,IAAI,mBAAmB;4BACzB;yEAAG,MAAM,2CAA2C;wBAEpD;gEAAO,IAAM,aAAa;;oBAC5B;uDAAG,MAAM,uBAAuB;gBAEhC;kDAAO,IAAM,aAAa;;YAC5B,OAAO;gBACL,6DAA6D;gBAC7D,WAAW;YACb;QACF;qCAAG;QAAC;QAAW;KAAkB;IAEjC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC;kDACY,CAAC,iGAAiG,EAAE,UAAU,cAAc,eAAe;;;;;;0BAgGlJ,6LAAC;gBAEC,IAAG;gBACH,OAAM;gBACN,YAAW;gBACX,SAAQ;gBACR,OAAM;gBACN,QAAO;0DANG;;kCAQV,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;;;;;;;kCAEP,6LAAC;wBAAE,IAAG;;kCACJ,cAAA,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,WAAU;4BACV,MAAK;;;;;;;;;;;;kCAGT,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;kCAEjB,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,iBAAgB;;;;;;;;;;;;;;;;;;;AAK9B;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/auth/RouteGuard.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { useEffect, ReactNode } from 'react';\nimport { usePathname } from 'next/navigation';\nimport LoadingAnimation from '@/components/shared/LoadingAnimation';\nimport { UserRole } from '@prisma/client';\n\ntype RouteGuardProps = {\n  children: ReactNode;\n  allowedRoles?: UserRole[];\n};\n\nexport default function RouteGuard({\n  children,\n  allowedRoles = [],\n}: RouteGuardProps) {\n  const { isAuthenticated, isLoading, user, redirectToLogin } = useAuth();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    // Si le chargement est terminé et que l'utilisateur n'est pas authentifié\n    if (!isLoading && !isAuthenticated) {\n      // Stocker le chemin actuel pour rediriger après la connexion\n      sessionStorage.setItem('redirectAfterLogin', pathname);\n      redirectToLogin();\n    }\n  }, [isLoading, isAuthenticated, pathname, redirectToLogin]);\n\n  useEffect(() => {\n    // Si l'utilisateur est authentifié mais n'a pas le rôle requis\n    if (\n      isAuthenticated &&\n      allowedRoles.length > 0 &&\n      user?.role &&\n      !allowedRoles.includes(user.role)\n    ) {\n      // Rediriger vers la page d'accueil ou une page d'erreur\n      window.location.href = '/unauthorized';\n    }\n  }, [isAuthenticated, allowedRoles, user]);\n\n  // Afficher un écran de chargement pendant la vérification\n  if (isLoading || !isAuthenticated) {\n    return <LoadingAnimation isLoading={true} onLoadingComplete={() => {}} />;\n  }\n\n  // Si l'utilisateur est authentifié et a le rôle requis (ou aucun rôle n'est requis)\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,WAAW,EACjC,QAAQ,EACR,eAAe,EAAE,EACD;;IAChB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACpE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,0EAA0E;YAC1E,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,6DAA6D;gBAC7D,eAAe,OAAO,CAAC,sBAAsB;gBAC7C;YACF;QACF;+BAAG;QAAC;QAAW;QAAiB;QAAU;KAAgB;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,+DAA+D;YAC/D,IACE,mBACA,aAAa,MAAM,GAAG,KACtB,MAAM,QACN,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAChC;gBACA,wDAAwD;gBACxD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;+BAAG;QAAC;QAAiB;QAAc;KAAK;IAExC,0DAA0D;IAC1D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBAAO,6LAAC,mJAAA,CAAA,UAAgB;YAAC,WAAW;YAAM,mBAAmB,KAAO;;;;;;IACtE;IAEA,oFAAoF;IACpF,qBAAO;kBAAG;;AACZ;GApCwB;;QAIwC,0HAAA,CAAA,UAAO;QACpD,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/chat/ChatWindow.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useSession } from 'next-auth/react';\n\ninterface ChatUser {\n  id: string;\n  userId: string;\n  name: string;\n  email: string;\n  role: 'ADMIN' | 'COMMERCIAL';\n  profilePhoto?: string;\n}\n\ninterface ChatMessage {\n  id: string;\n  content: string;\n  senderType: 'ADMIN' | 'COMMERCIAL';\n  senderId: string;\n  messageType: 'TEXT' | 'FILE' | 'IMAGE';\n  fileUrl?: string;\n  fileName?: string;\n  isRead: boolean;\n  createdAt: string;\n}\n\ninterface ChatConversation {\n  id: string;\n  admin: { user: { id: string; firstname: string; lastname: string } };\n  commercial: { user: { id: string; firstname: string; lastname: string } };\n  lastMessage?: string;\n  lastMessageAt?: string;\n}\n\nexport default function ChatWindow() {\n  const { data: session } = useSession();\n  const [isOpen, setIsOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState<'conversations' | 'users'>('conversations');\n  const [conversations, setConversations] = useState<ChatConversation[]>([]);\n  const [availableUsers, setAvailableUsers] = useState<ChatUser[]>([]);\n  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  // Load conversations and users on mount\n  useEffect(() => {\n    if (session?.user && isOpen) {\n      loadConversations();\n      loadAvailableUsers();\n    }\n  }, [session, isOpen]);\n\n  // Load messages when conversation is selected\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation);\n    }\n  }, [selectedConversation]);\n\n  const loadConversations = async () => {\n    try {\n      const response = await fetch('/api/chat/conversations');\n      if (response.ok) {\n        const data = await response.json();\n        setConversations(data.conversations);\n      }\n    } catch (error) {\n      console.error('Error loading conversations:', error);\n    }\n  };\n\n  const loadAvailableUsers = async () => {\n    try {\n      const response = await fetch('/api/chat/users');\n      if (response.ok) {\n        const data = await response.json();\n        setAvailableUsers(data.users);\n      }\n    } catch (error) {\n      console.error('Error loading users:', error);\n    }\n  };\n\n  const loadMessages = async (conversationId: string) => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/chat/messages?conversationId=${conversationId}`);\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages);\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !selectedConversation) return;\n\n    try {\n      const response = await fetch('/api/chat/messages', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          conversationId: selectedConversation,\n          content: newMessage,\n          messageType: 'TEXT',\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(prev => [...prev, data.message]);\n        setNewMessage('');\n        loadConversations(); // Refresh conversations to update last message\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !selectedConversation) return;\n\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('conversationId', selectedConversation);\n\n      const response = await fetch('/api/chat/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n\n        // Send a file message\n        const messageResponse = await fetch('/api/chat/messages', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({\n            conversationId: selectedConversation,\n            content: `📎 ${data.file.fileName}`,\n            messageType: file.type.startsWith('image/') ? 'IMAGE' : 'FILE',\n            fileUrl: data.file.fileUrl,\n            fileName: data.file.fileName,\n          }),\n        });\n\n        if (messageResponse.ok) {\n          const messageData = await messageResponse.json();\n          setMessages(prev => [...prev, messageData.message]);\n          loadConversations();\n        }\n      } else {\n        const errorData = await response.json();\n        alert(`Erreur d'upload: ${errorData.error}`);\n      }\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      alert('Erreur lors de l\\'upload du fichier');\n    } finally {\n      setIsUploading(false);\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    }\n  };\n\n  const triggerFileUpload = () => {\n    fileInputRef.current?.click();\n  };\n\n  const startConversation = async (userId: string) => {\n    try {\n      const response = await fetch('/api/chat/conversations', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ participantId: userId }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSelectedConversation(data.conversation.id);\n        setActiveTab('conversations');\n        loadConversations();\n      }\n    } catch (error) {\n      console.error('Error starting conversation:', error);\n    }\n  };\n\n  const formatTime = (dateString: string) => {\n    return new Date(dateString).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const getConversationName = (conversation: ChatConversation) => {\n    if (session?.user?.role === 'ADMIN') {\n      return `${conversation.commercial.user.firstname} ${conversation.commercial.user.lastname}`;\n    } else {\n      return `${conversation.admin.user.firstname} ${conversation.admin.user.lastname}`;\n    }\n  };\n\n  if (!session?.user || (session.user.role !== 'ADMIN' && session.user.role !== 'COMMERCIAL')) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Chat Toggle Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-50\"\n      >\n        <span className=\"text-2xl\">💬</span>\n      </button>\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"fixed bottom-24 right-6 w-96 h-[500px] bg-white border border-gray-200 rounded-lg shadow-xl z-50 flex flex-col\">\n          {/* Header */}\n          <div className=\"bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between\">\n            <h3 className=\"font-semibold\">💬 Chat Moonelec</h3>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"text-white hover:text-gray-200 text-xl\"\n            >\n              ✕\n            </button>\n          </div>\n\n          {/* Tabs */}\n          <div className=\"flex border-b\">\n            <button\n              onClick={() => setActiveTab('conversations')}\n              className={`flex-1 p-3 text-sm font-medium ${\n                activeTab === 'conversations'\n                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <span className=\"inline mr-2\">💬</span>\n              Conversations\n            </button>\n            <button\n              onClick={() => setActiveTab('users')}\n              className={`flex-1 p-3 text-sm font-medium ${\n                activeTab === 'users'\n                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <span className=\"inline mr-2\">👥</span>\n              Utilisateurs\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1 overflow-hidden\">\n            {activeTab === 'conversations' && !selectedConversation && (\n              <div className=\"p-4 h-full overflow-y-auto\">\n                {conversations.length === 0 ? (\n                  <div className=\"text-center text-gray-500 mt-8\">\n                    <div className=\"text-5xl mb-4 text-gray-300\">💬</div>\n                    <p>Aucune conversation</p>\n                    <p className=\"text-sm\">Commencez une nouvelle conversation</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    {conversations.map((conversation) => (\n                      <div\n                        key={conversation.id}\n                        onClick={() => setSelectedConversation(conversation.id)}\n                        className=\"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\"\n                      >\n                        <div className=\"font-medium text-sm\">\n                          {getConversationName(conversation)}\n                        </div>\n                        {conversation.lastMessage && (\n                          <div className=\"text-xs text-gray-500 mt-1 truncate\">\n                            {conversation.lastMessage}\n                          </div>\n                        )}\n                        {conversation.lastMessageAt && (\n                          <div className=\"text-xs text-gray-400 mt-1\">\n                            {formatTime(conversation.lastMessageAt)}\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'users' && (\n              <div className=\"p-4 h-full overflow-y-auto\">\n                <div className=\"space-y-2\">\n                  {availableUsers.map((user) => (\n                    <div\n                      key={user.id}\n                      onClick={() => startConversation(user.id)}\n                      className=\"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\"\n                    >\n                      <div className=\"font-medium text-sm\">{user.name}</div>\n                      <div className=\"text-xs text-gray-500\">{user.role}</div>\n                      <div className=\"text-xs text-gray-400\">{user.email}</div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {selectedConversation && (\n              <div className=\"h-full flex flex-col\">\n                {/* Messages */}\n                <div className=\"flex-1 p-4 overflow-y-auto\">\n                  {loading ? (\n                    <div className=\"text-center text-gray-500\">Chargement...</div>\n                  ) : (\n                    <div className=\"space-y-3\">\n                      {messages.map((message) => (\n                        <div\n                          key={message.id}\n                          className={`flex ${\n                            message.senderId === session?.user?.id ? 'justify-end' : 'justify-start'\n                          }`}\n                        >\n                          <div\n                            className={`max-w-xs px-3 py-2 rounded-lg text-sm ${\n                              message.senderId === session?.user?.id\n                                ? 'bg-blue-600 text-white'\n                                : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white'\n                            }`}\n                          >\n                            {message.messageType === 'IMAGE' && message.fileUrl ? (\n                              <div>\n                                <img\n                                  src={message.fileUrl}\n                                  alt={message.fileName || 'Image'}\n                                  className=\"max-w-full h-auto rounded mb-2\"\n                                  style={{ maxHeight: '200px' }}\n                                />\n                                <div>{message.content}</div>\n                              </div>\n                            ) : message.messageType === 'FILE' && message.fileUrl ? (\n                              <div>\n                                <a\n                                  href={message.fileUrl}\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  className={`inline-flex items-center space-x-2 p-2 rounded border ${\n                                    message.senderId === session?.user?.id\n                                      ? 'border-blue-300 bg-blue-500'\n                                      : 'border-gray-300 bg-gray-50 dark:bg-gray-600 dark:border-gray-500'\n                                  }`}\n                                >\n                                  <span>📎</span>\n                                  <span className=\"underline\">{message.fileName || 'Fichier'}</span>\n                                </a>\n                                <div className=\"mt-2\">{message.content}</div>\n                              </div>\n                            ) : (\n                              <div>{message.content}</div>\n                            )}\n                            <div\n                              className={`text-xs mt-1 ${\n                                message.senderId === session?.user?.id\n                                  ? 'text-blue-100'\n                                  : 'text-gray-500 dark:text-gray-400'\n                              }`}\n                            >\n                              {formatTime(message.createdAt)}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                      <div ref={messagesEndRef} />\n                    </div>\n                  )}\n                </div>\n\n                {/* Message Input */}\n                <div className=\"p-4 border-t\">\n                  <div className=\"flex space-x-2\">\n                    <input\n                      type=\"file\"\n                      ref={fileInputRef}\n                      onChange={handleFileUpload}\n                      className=\"hidden\"\n                      accept=\"image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt,.csv\"\n                    />\n                    <button\n                      onClick={triggerFileUpload}\n                      disabled={isUploading}\n                      className=\"bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors\"\n                      title=\"Joindre un fichier\"\n                    >\n                      {isUploading ? (\n                        <span className=\"text-lg\">⏳</span>\n                      ) : (\n                        <span className=\"text-lg\">📎</span>\n                      )}\n                    </button>\n                    <input\n                      type=\"text\"\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}\n                      placeholder=\"Tapez votre message...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    />\n                    <button\n                      onClick={sendMessage}\n                      disabled={!newMessage.trim()}\n                      className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors\"\n                    >\n                      <span className=\"text-lg\">📤</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAkCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;+BAAG;QAAC;KAAS;IAEb,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS,QAAQ,QAAQ;gBAC3B;gBACA;YACF;QACF;+BAAG;QAAC;QAAS;KAAO;IAEpB,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,sBAAsB;gBACxB,aAAa;YACf;QACF;+BAAG;QAAC;KAAqB;IAEzB,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB,KAAK,aAAa;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,kBAAkB,KAAK,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,kCAAkC,EAAE,gBAAgB;YAClF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,sBAAsB;QAEjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,CAAA,OAAQ;2BAAI;wBAAM,KAAK,OAAO;qBAAC;gBAC3C,cAAc;gBACd,qBAAqB,+CAA+C;YACtE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,sBAAsB;QAEpC,eAAe;QACf,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,kBAAkB;YAElC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,sBAAsB;gBACtB,MAAM,kBAAkB,MAAM,MAAM,sBAAsB;oBACxD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,gBAAgB;wBAChB,SAAS,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE;wBACnC,aAAa,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,UAAU;wBACxD,SAAS,KAAK,IAAI,CAAC,OAAO;wBAC1B,UAAU,KAAK,IAAI,CAAC,QAAQ;oBAC9B;gBACF;gBAEA,IAAI,gBAAgB,EAAE,EAAE;oBACtB,MAAM,cAAc,MAAM,gBAAgB,IAAI;oBAC9C,YAAY,CAAA,OAAQ;+BAAI;4BAAM,YAAY,OAAO;yBAAC;oBAClD;gBACF;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,CAAC,iBAAiB,EAAE,UAAU,KAAK,EAAE;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,eAAe;YACf,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,eAAe;gBAAO;YAC/C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,wBAAwB,KAAK,YAAY,CAAC,EAAE;gBAC5C,aAAa;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,MAAM,SAAS,SAAS;YACnC,OAAO,GAAG,aAAa,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC7F,OAAO;YACL,OAAO,GAAG,aAAa,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QACnF;IACF;IAEA,IAAI,CAAC,SAAS,QAAS,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAe;QAC3F,OAAO;IACT;IAEA,qBACE;;0BAEE,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;0BAEV,cAAA,6LAAC;oBAAK,WAAU;8BAAW;;;;;;;;;;;YAI5B,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,+BAA+B,EACzC,cAAc,kBACV,wDACA,qCACJ;;kDAEF,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAS;;;;;;;0CAGzC,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,+BAA+B,EACzC,cAAc,UACV,wDACA,qCACJ;;kDAEF,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAS;;;;;;;;;;;;;kCAM3C,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,mBAAmB,CAAC,sCACjC,6LAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;sDAAE;;;;;;sDACH,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;yDAGzB,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;4CAEC,SAAS,IAAM,wBAAwB,aAAa,EAAE;4CACtD,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACZ,oBAAoB;;;;;;gDAEtB,aAAa,WAAW,kBACvB,6LAAC;oDAAI,WAAU;8DACZ,aAAa,WAAW;;;;;;gDAG5B,aAAa,aAAa,kBACzB,6LAAC;oDAAI,WAAU;8DACZ,WAAW,aAAa,aAAa;;;;;;;2CAdrC,aAAa,EAAE;;;;;;;;;;;;;;;4BAwB/B,cAAc,yBACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC;4CAEC,SAAS,IAAM,kBAAkB,KAAK,EAAE;4CACxC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DAAuB,KAAK,IAAI;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;;2CAN7C,KAAK,EAAE;;;;;;;;;;;;;;;4BAarB,sCACC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,wBACC,6LAAC;4CAAI,WAAU;sDAA4B;;;;;iEAE3C,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wDAEC,WAAW,CAAC,KAAK,EACf,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAAK,gBAAgB,iBACzD;kEAEF,cAAA,6LAAC;4DACC,WAAW,CAAC,sCAAsC,EAChD,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAChC,2BACA,8DACJ;;gEAED,QAAQ,WAAW,KAAK,WAAW,QAAQ,OAAO,iBACjD,6LAAC;;sFACC,6LAAC;4EACC,KAAK,QAAQ,OAAO;4EACpB,KAAK,QAAQ,QAAQ,IAAI;4EACzB,WAAU;4EACV,OAAO;gFAAE,WAAW;4EAAQ;;;;;;sFAE9B,6LAAC;sFAAK,QAAQ,OAAO;;;;;;;;;;;2EAErB,QAAQ,WAAW,KAAK,UAAU,QAAQ,OAAO,iBACnD,6LAAC;;sFACC,6LAAC;4EACC,MAAM,QAAQ,OAAO;4EACrB,QAAO;4EACP,KAAI;4EACJ,WAAW,CAAC,sDAAsD,EAChE,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAChC,gCACA,oEACJ;;8FAEF,6LAAC;8FAAK;;;;;;8FACN,6LAAC;oFAAK,WAAU;8FAAa,QAAQ,QAAQ,IAAI;;;;;;;;;;;;sFAEnD,6LAAC;4EAAI,WAAU;sFAAQ,QAAQ,OAAO;;;;;;;;;;;yFAGxC,6LAAC;8EAAK,QAAQ,OAAO;;;;;;8EAEvB,6LAAC;oEACC,WAAW,CAAC,aAAa,EACvB,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAChC,kBACA,oCACJ;8EAED,WAAW,QAAQ,SAAS;;;;;;;;;;;;uDAjD5B,QAAQ,EAAE;;;;;8DAsDnB,6LAAC;oDAAI,KAAK;;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,KAAK;oDACL,UAAU;oDACV,WAAU;oDACV,QAAO;;;;;;8DAET,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;oDACV,OAAM;8DAEL,4BACC,6LAAC;wDAAK,WAAU;kEAAU;;;;;6EAE1B,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;8DAG9B,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;oDACxC,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDACC,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI;oDAC1B,WAAU;8DAEV,cAAA,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhD;GA5ZwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\nimport AdminSidebar from '@/components/admin/AdminSidebar';\nimport AdminHeader from '@/components/admin/AdminHeader';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport ChatWindow from '@/components/chat/ChatWindow';\nimport { motion } from 'framer-motion';\n\nexport default function AdminLayout({ children }: { children: ReactNode }) {\n  const router = useRouter();\n  const { isAdmin } = useAuth();\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"flex h-screen bg-gray-100 dark:bg-gray-900\">\n        {/* Sidebar */}\n        <AdminSidebar />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          {/* Header */}\n          <AdminHeader />\n\n          {/* Main Content Area */}\n          <motion.main\n            className=\"flex-1 overflow-y-auto p-4 md:p-6\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.3 }}\n          >\n            {children}\n          </motion.main>\n        </div>\n\n        {/* Chat Window */}\n        <ChatWindow />\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS,YAAY,EAAE,QAAQ,EAA2B;;IACvE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE1B,qBACE,6LAAC,2IAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,8IAAA,CAAA,UAAY;;;;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6IAAA,CAAA,UAAW;;;;;sCAGZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;;;;;;;;;;;;8BAKL,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;AAInB;GA/BwB;;QACP,qIAAA,CAAA,YAAS;QACJ,0HAAA,CAAA,UAAO;;;KAFL", "debugId": null}}]}