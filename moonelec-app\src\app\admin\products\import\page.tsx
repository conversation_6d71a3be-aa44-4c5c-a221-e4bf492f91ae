'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaUpload, FaFilePdf, FaCheck, FaTimes, FaSave, FaImage, Fa<PERSON><PERSON>ner, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import Link from 'next/link';
import ImageUpload from '@/components/ui/ImageUpload';
import MultiImageUpload from '@/components/ui/MultiImageUpload';

interface ExtractedData {
  reference: string;
  name?: string;
  description: string;
  characteristics: Record<string, string>;
}

interface FormData extends ExtractedData {
  categoryId: string;
  brandId: string;
  mainImage: string;
  images: { url: string; alt?: string; order?: number }[];
}

export default function ImportProductPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [extractedData, setExtractedData] = useState<ExtractedData | null>(null);
  const [formData, setFormData] = useState<FormData>({
    reference: '',
    name: '',
    description: '',
    characteristics: {},
    categoryId: '',
    brandId: '',
    mainImage: '',
    images: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [brands, setBrands] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Fetch categories and brands on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);

        // Fetch brands
        const brandsResponse = await fetch('/api/brands');
        if (!brandsResponse.ok) {
          throw new Error('Failed to fetch brands');
        }
        const brandsData = await brandsResponse.json();
        setBrands(brandsData.brands);
      } catch (err) {
        console.error('Error fetching data:', err);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchData();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    if (file.type !== 'application/pdf') {
      setErrors({ file: 'Veuillez sélectionner un fichier PDF' });
      return;
    }

    setSelectedFile(file);
    setErrors({});
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setErrors({ file: 'Veuillez sélectionner un fichier PDF' });
      return;
    }

    setIsUploading(true);
    setErrors({});
    setExtractedData(null); // Réinitialiser les données extraites
    setFormData({
      reference: '',
      name: '',
      description: '',
      characteristics: {},
      categoryId: '',
      brandId: '',
      mainImage: '',
      images: []
    });

    try {
      // Créer un FormData pour envoyer le fichier
      const formData = new FormData();
      formData.append('file', selectedFile);

      // Simuler le temps de téléchargement
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsProcessing(true);

      // Envoyer le fichier à l'API d'extraction de PDF avec Hugging Face
      const response = await fetch('/api/extract-hf', {
        method: 'POST',
        body: formData,
      });

      // Récupérer les données de la réponse
      const result = await response.json();

      // Vérifier s'il y a une erreur
      if (!response.ok) {
        throw new Error(result.error || 'Erreur lors de l\'extraction des données du PDF');
      }

      // Vérifier si l'extraction a échoué
      if (result.error || result.data === null) {
        setErrors({ upload: result.error || 'Impossible d\'extraire des données du PDF. Veuillez vérifier que le PDF contient les informations nécessaires.' });
        return;
      }

      // Récupérer les données extraites
      const extractedData: ExtractedData = {
        reference: result.data.reference || '',
        name: result.data.productName || '',
        description: result.data.description || '',
        characteristics: result.data.characteristics || {}
      };

      // Vérifier la source des données
      const source = result.data.source || 'unknown';
      console.log(`Source des données extraites: ${source}`);

      // Vérifier si les données extraites sont complètes
      const isComplete =
        extractedData.reference &&
        extractedData.name &&
        extractedData.description &&
        Object.keys(extractedData.characteristics).length > 0;

      // Si les données sont incomplètes, afficher un avertissement
      if (!isComplete) {
        setErrors({
          upload: 'Certaines données n\'ont pas pu être extraites du PDF. Veuillez compléter les champs manquants manuellement.'
        });
      }

      // Mettre à jour l'état avec les données extraites
      setExtractedData(extractedData);

      // Afficher un message de succès avec la source des données
      const successMessage = source === 'IA'
        ? 'Données extraites avec succès par l\'IA !'
        : source === 'fallback'
          ? 'Données générées automatiquement à partir du nom du fichier !'
          : source.includes('règles')
            ? 'Données extraites avec succès par analyse du nom de fichier !'
            : 'Données extraites avec succès !';

      console.log(successMessage);

      // Afficher un avertissement si un warning est présent dans la réponse
      if (result.warning) {
        console.warn(result.warning);
        setErrors({
          upload: result.warning
        });
      }

      setFormData({
        ...extractedData,
        categoryId: '',
        brandId: '',
        mainImage: '',
        images: []
      });
    } catch (error: any) {
      console.error('Erreur lors du traitement du PDF:', error);
      setErrors({ upload: error.message || 'Une erreur est survenue lors du traitement du PDF' });
    } finally {
      setIsUploading(false);
      setIsProcessing(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleMainImageChange = (imageUrl: string | null) => {
    setFormData(prev => ({ ...prev, mainImage: imageUrl || '' }));
  };

  const handleImagesChange = (images: { url: string; alt?: string; order?: number }[]) => {
    setFormData(prev => ({ ...prev, images }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.categoryId) {
      newErrors.categoryId = 'La catégorie est requise';
    }

    if (!formData.brandId) {
      newErrors.brandId = 'La marque est requise';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!extractedData || !validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create the product using the API
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reference: formData.reference,
          name: formData.name,
          description: formData.description,
          characteristics: formData.characteristics,
          mainImage: formData.mainImage || null,
          categoryId: formData.categoryId || null,
          brandId: formData.brandId || null,
          images: formData.images,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      // Redirect to the product list
      router.push('/admin/products');
    } catch (err: any) {
      console.error('Error creating product:', err);
      setErrors({ submit: err.message || 'Une erreur est survenue lors de la création du produit' });
      setIsSubmitting(false);
    }
  };

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Importer un produit depuis un PDF
          </h1>
          <Link href="/admin/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Retour à la liste
            </motion.button>
          </Link>
        </div>

        {/* Error Message */}
        {errors.submit && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center">
            <FaExclamationTriangle className="mr-2" />
            <span>{errors.submit}</span>
          </div>
        )}

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          {!extractedData ? (
            <div className="max-w-md mx-auto">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaFilePdf className="text-4xl text-primary" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                  Téléchargez un fichier PDF
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mt-2">
                  Notre système extraira automatiquement les informations du produit à partir du PDF.
                </p>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Fichier PDF
                  </label>
                  <div className="flex items-center justify-center w-full">
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <FaUpload className="w-8 h-8 mb-3 text-gray-400" />
                        <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                          <span className="font-semibold">Cliquez pour télécharger</span> ou glissez-déposez
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">PDF uniquement</p>
                      </div>
                      <input
                        type="file"
                        accept=".pdf"
                        className="hidden"
                        onChange={handleFileChange}
                      />
                    </label>
                  </div>
                  {errors.file && (
                    <p className="mt-1 text-sm text-red-500">{errors.file}</p>
                  )}
                </div>

                {selectedFile && (
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center">
                      <FaFilePdf className="text-red-500 mr-2" />
                      <span className="text-gray-700 dark:text-gray-300">{selectedFile.name}</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => setSelectedFile(null)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                )}

                <motion.button
                  type="button"
                  onClick={handleUpload}
                  disabled={!selectedFile || isUploading || isProcessing}
                  className={`w-full flex items-center justify-center gap-2 px-4 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors ${
                    (!selectedFile || isUploading || isProcessing) ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                  whileHover={!isUploading && !isProcessing ? { scale: 1.05 } : {}}
                  whileTap={!isUploading && !isProcessing ? { scale: 0.95 } : {}}
                >
                  {isUploading ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Téléchargement...
                    </>
                  ) : isProcessing ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Traitement du PDF...
                    </>
                  ) : (
                    <>
                      <FaUpload />
                      Télécharger et traiter
                    </>
                  )}
                </motion.button>

                {errors.upload && (
                  <div className="p-4 bg-red-50 text-red-700 rounded-lg">
                    <p className="font-semibold mb-2">Message :</p>
                    <p>{errors.upload}</p>
                    {typeof errors.upload === 'string' && errors.upload.includes('automatiquement') ? (
                      <p className="mt-2">Les données ont été générées à partir du nom du fichier. Vous pouvez les modifier si nécessaire.</p>
                    ) : (
                      <p className="mt-2">Si les données ne sont pas correctes, vous pouvez les modifier manuellement ou essayer avec un autre fichier PDF.</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <div className="flex items-center justify-center w-full p-4 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg">
                  <FaCheck className="mr-2" />
                  <span>Les données ont été extraites avec succès du PDF. Veuillez compléter les informations manquantes si nécessaire.</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* Reference */}
                <div>
                  <label htmlFor="reference" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Référence
                  </label>
                  <input
                    type="text"
                    id="reference"
                    name="reference"
                    value={formData.reference}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>

                {/* Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nom
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>

                {/* Category */}
                <div>
                  <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Catégorie <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="categoryId"
                    name="categoryId"
                    value={formData.categoryId}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      errors.categoryId ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  >
                    <option value="">Sélectionner une catégorie</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  {errors.categoryId && (
                    <p className="mt-1 text-sm text-red-500">{errors.categoryId}</p>
                  )}
                </div>

                {/* Brand */}
                <div>
                  <label htmlFor="brandId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Marque <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="brandId"
                    name="brandId"
                    value={formData.brandId}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border ${
                      errors.brandId ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
                  >
                    <option value="">Sélectionner une marque</option>
                    {brands.map((brand) => (
                      <option key={brand.id} value={brand.id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                  {errors.brandId && (
                    <p className="mt-1 text-sm text-red-500">{errors.brandId}</p>
                  )}
                </div>

                {/* Main Image */}
                <div>
                  <ImageUpload
                    initialImage={formData.mainImage}
                    onImageChange={handleMainImageChange}
                    directory="products"
                    label="Image principale"
                    aspectRatio="square"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                ></textarea>
              </div>

              {/* Characteristics */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Caractéristiques
                </label>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <table className="w-full">
                    <tbody>
                      {Object.entries(formData.characteristics).map(([key, value]) => (
                        <tr key={key} className="border-b border-gray-200 dark:border-gray-600">
                          <td className="py-2 px-4 font-medium text-gray-700 dark:text-gray-300">
                            {key}
                          </td>
                          <td className="py-2 px-4 text-gray-600 dark:text-gray-400">
                            {value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Product Images */}
              <div className="mb-6">
                <MultiImageUpload
                  initialImages={formData.images}
                  onImagesChange={handleImagesChange}
                  directory="products"
                  label="Images du produit"
                  maxImages={10}
                />
              </div>

              {/* Submit Button */}
              <div className="mt-8 flex justify-end">
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className={`flex items-center justify-center gap-2 px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors ${
                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isSubmitting ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2" />
                      Enregistrer le produit
                    </>
                  )}
                </motion.button>
              </div>
            </form>
          )}
        </div>
      </div>
    </RouteGuard>
  );
}
