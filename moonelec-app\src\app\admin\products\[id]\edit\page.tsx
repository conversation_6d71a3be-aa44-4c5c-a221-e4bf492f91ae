'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaSave, FaArrowLeft, Fa<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaPlus, FaTrash } from 'react-icons/fa';
import Link from 'next/link';
import RouteGuard from '@/components/auth/RouteGuard';
import ImageUpload from '@/components/ui/ImageUpload';
import MultiImageUpload from '@/components/ui/MultiImageUpload';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
  order: number;
}

interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  characteristics: Record<string, any>;
  mainImage: string | null;
  categoryId: string | null;
  brandId: string | null;
  productImages: ProductImage[];
}

interface Category {
  id: string;
  name: string;
}

interface Brand {
  id: string;
  name: string;
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    reference: '',
    name: '',
    description: '',
    mainImage: '',
    categoryId: '',
    brandId: '',
  });

  const [characteristics, setCharacteristics] = useState<{ key: string; value: string }[]>([]);
  const [productImages, setProductImages] = useState<{ url: string; alt?: string; order?: number }[]>([]);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch product
        const productResponse = await fetch(`/api/products/${productId}`);

        if (!productResponse.ok) {
          throw new Error('Failed to fetch product');
        }

        const productData = await productResponse.json();
        setProduct(productData);

        // Set form data
        setFormData({
          reference: productData.reference,
          name: productData.name,
          description: productData.description,
          mainImage: productData.mainImage || '',
          categoryId: productData.categoryId || '',
          brandId: productData.brandId || '',
        });

        // Set characteristics
        const chars = Object.entries(productData.characteristics || {}).map(([key, value]) => ({
          key,
          value: String(value),
        }));
        setCharacteristics(chars.length > 0 ? chars : [{ key: '', value: '' }]);

        // Set product images
        setProductImages(productData.productImages.map(img => ({
          url: img.url,
          alt: img.alt,
          order: img.order,
        })));

        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');

        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }

        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);

        // Fetch brands
        const brandsResponse = await fetch('/api/brands');

        if (!brandsResponse.ok) {
          throw new Error('Failed to fetch brands');
        }

        const brandsData = await brandsResponse.json();
        setBrands(brandsData.brands);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'An error occurred while fetching data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [productId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCharacteristicChange = (index: number, field: 'key' | 'value', value: string) => {
    setCharacteristics(prev => {
      const newCharacteristics = [...prev];
      newCharacteristics[index][field] = value;
      return newCharacteristics;
    });
  };

  const addCharacteristic = () => {
    setCharacteristics(prev => [...prev, { key: '', value: '' }]);
  };

  const removeCharacteristic = (index: number) => {
    setCharacteristics(prev => prev.filter((_, i) => i !== index));
  };

  const handleMainImageChange = (imageUrl: string | null) => {
    setFormData(prev => ({ ...prev, mainImage: imageUrl || '' }));
  };

  const handleProductImagesChange = (images: { url: string; alt?: string; order?: number }[]) => {
    setProductImages(images);
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.reference.trim()) {
      errors.reference = 'La référence est requise';
    }

    if (!formData.name.trim()) {
      errors.name = 'Le nom est requis';
    }

    if (!formData.description.trim()) {
      errors.description = 'La description est requise';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Convert characteristics array to object
      const characteristicsObject = characteristics.reduce((obj, { key, value }) => {
        if (key.trim()) {
          obj[key.trim()] = value.trim();
        }
        return obj;
      }, {} as Record<string, string>);

      const response = await fetch(`/api/products/${productId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reference: formData.reference,
          name: formData.name,
          description: formData.description,
          characteristics: characteristicsObject,
          mainImage: formData.mainImage || null,
          categoryId: formData.categoryId || null,
          brandId: formData.brandId || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update product');
      }

      // Update product images
      // First, delete all existing images
      await fetch(`/api/products/${productId}/images`, {
        method: 'DELETE',
      });

      // Then, add the new images
      if (productImages.length > 0) {
        await fetch(`/api/products/${productId}/images`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ images: productImages }),
        });
      }

      setSuccessMessage('Produit mis à jour avec succès');

      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/admin/products/${productId}`);
      }, 1500);
    } catch (err: any) {
      console.error('Error updating product:', err);
      setError(err.message || 'Une erreur est survenue lors de la mise à jour du produit');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <FaSpinner className="animate-spin text-4xl text-primary" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
          <div className="flex items-center text-red-600 dark:text-red-400 mb-4">
            <FaExclamationTriangle className="text-2xl mr-2" />
            <h2 className="text-xl font-semibold">Erreur</h2>
          </div>
          <p className="text-red-600 dark:text-red-400 mb-4">
            Le produit n'a pas pu être chargé
          </p>
          <Link href="/admin/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              Retour à la liste des produits
            </motion.button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex items-center mb-4 md:mb-0">
            <Link href={`/admin/products/${productId}`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="mr-4 p-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                <FaArrowLeft />
              </motion.button>
            </Link>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Modifier le produit
            </h1>
          </div>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md flex items-center">
            <FaSpinner className="animate-spin mr-2" />
            <span>{successMessage}</span>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md flex items-center">
            <FaExclamationTriangle className="mr-2" />
            <span>{error}</span>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Reference */}
            <div>
              <label htmlFor="reference" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Référence <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="reference"
                name="reference"
                value={formData.reference}
                onChange={handleInputChange}
                className={`w-full px-4 py-2 border ${
                  formErrors.reference ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {formErrors.reference && (
                <p className="mt-1 text-sm text-red-500">{formErrors.reference}</p>
              )}
            </div>

            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nom <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-4 py-2 border ${
                  formErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
              />
              {formErrors.name && (
                <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>
              )}
            </div>

            {/* Category */}
            <div>
              <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Catégorie
              </label>
              <select
                id="categoryId"
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Sélectionner une catégorie</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Brand */}
            <div>
              <label htmlFor="brandId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Marque
              </label>
              <select
                id="brandId"
                name="brandId"
                value={formData.brandId}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Sélectionner une marque</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Main Image */}
            <div>
              <ImageUpload
                initialImage={formData.mainImage}
                onImageChange={handleMainImageChange}
                directory="products"
                label="Image principale"
                aspectRatio="square"
              />
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={5}
              className={`w-full px-4 py-2 border ${
                formErrors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary`}
            ></textarea>
            {formErrors.description && (
              <p className="mt-1 text-sm text-red-500">{formErrors.description}</p>
            )}
          </div>

          {/* Characteristics */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Caractéristiques
              </label>
              <motion.button
                type="button"
                onClick={addCharacteristic}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-1 bg-primary text-white rounded-full"
              >
                <FaPlus />
              </motion.button>
            </div>

            {characteristics.map((characteristic, index) => (
              <div key={index} className="flex items-center gap-2 mb-2">
                <input
                  type="text"
                  value={characteristic.key}
                  onChange={(e) => handleCharacteristicChange(index, 'key', e.target.value)}
                  placeholder="Nom"
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <input
                  type="text"
                  value={characteristic.value}
                  onChange={(e) => handleCharacteristicChange(index, 'value', e.target.value)}
                  placeholder="Valeur"
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <motion.button
                  type="button"
                  onClick={() => removeCharacteristic(index)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 text-red-500 hover:text-red-700 dark:hover:text-red-300"
                >
                  <FaTrash />
                </motion.button>
              </div>
            ))}
          </div>

          {/* Product Images */}
          <div className="mb-6">
            <MultiImageUpload
              initialImages={productImages}
              onImagesChange={handleProductImagesChange}
              directory="products"
              label="Images du produit"
              maxImages={10}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <motion.button
              type="submit"
              disabled={isSubmitting}
              className={`px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center ${
                isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isSubmitting ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Enregistrement...
                </>
              ) : (
                <>
                  <FaSave className="mr-2" />
                  Enregistrer les modifications
                </>
              )}
            </motion.button>
          </div>
        </form>
      </div>
    </RouteGuard>
  );
}
