import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as SecureStore from 'expo-secure-store';
import { API_BASE_URL, getApiUrl } from '../config/api';
import { 
  Product, 
  Category, 
  Quote, 
  User,
  ProductsResponse,
  QuotesResponse 
} from '../types';

/**
 * Dedicated Web API Service for mobile app
 * This service handles all communication with the web app's API endpoints
 * with proper authentication and error handling
 */
class WebApiService {
  private api: AxiosInstance;

  constructor() {
    // Use dynamic API URL based on platform and environment
    const apiUrl = getApiUrl();
    console.log('🌐 WebAPI: Initializing with URL:', apiUrl);

    this.api = axios.create({
      baseURL: apiUrl,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add authentication
    this.api.interceptors.request.use(
      async (config) => {
        try {
          // Always try to get JWT token first (mobile auth)
          const authToken = await SecureStore.getItemAsync('authToken');
          if (authToken) {
            config.headers.Authorization = `Bearer ${authToken}`;
            console.log('🔐 WebAPI: Using JWT token for request to', config.url);
          } else {
            console.log('⚠️ WebAPI: No JWT token found for request to', config.url);
          }
        } catch (error) {
          console.error('🔐 WebAPI: Error getting auth token:', error);
        }
        return config;
      },
      (error) => {
        console.error('🔐 WebAPI: Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => {
        console.log(`✅ WebAPI: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
        return response;
      },
      async (error) => {
        const status = error.response?.status;
        const url = error.config?.url;
        console.error(`❌ WebAPI: ${error.config?.method?.toUpperCase()} ${url} - ${status}`, error.response?.data);
        
        if (status === 401) {
          console.log('🔐 WebAPI: Unauthorized - token may be expired');
          // Don't automatically logout, let the app handle it
        }
        
        return Promise.reject(error);
      }
    );
  }

  // Helper method to handle API responses
  private handleResponse<T>(response: AxiosResponse<T>): T {
    return response.data;
  }

  // Helper method to handle API errors
  private handleError(error: any): never {
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    } else if (error.message) {
      throw new Error(error.message);
    } else {
      throw new Error('Network error occurred');
    }
  }

  // ==================== PRODUCTS ====================
  
  async getProducts(params?: {
    categoryId?: string;
    brandId?: string;
    search?: string;
    skip?: number;
    take?: number;
  }): Promise<ProductsResponse> {
    try {
      console.log('📦 WebAPI: Fetching products with params:', params);
      const response = await this.api.get('/products', { params });
      return this.handleResponse(response);
    } catch (error) {
      console.error('📦 WebAPI: Error fetching products:', error);
      this.handleError(error);
    }
  }

  async getProduct(id: string): Promise<Product> {
    try {
      console.log('📦 WebAPI: Fetching product:', id);
      const response = await this.api.get(`/products/${id}`);
      return this.handleResponse(response);
    } catch (error) {
      console.error('📦 WebAPI: Error fetching product:', error);
      this.handleError(error);
    }
  }

  async createProduct(productData: {
    reference: string;
    name: string;
    description?: string;
    characteristics?: Record<string, string>;
    mainImage?: string;
    categoryId?: string;
    brandId?: string;
    images?: string[];
  }): Promise<Product> {
    try {
      console.log('📦 WebAPI: Creating product:', productData.name);
      const response = await this.api.post('/products', productData);
      return this.handleResponse(response);
    } catch (error) {
      console.error('📦 WebAPI: Error creating product:', error);
      this.handleError(error);
    }
  }

  async deleteProduct(id: string): Promise<void> {
    try {
      console.log('📦 WebAPI: Deleting product:', id);
      await this.api.delete(`/products/${id}`);
    } catch (error) {
      console.error('📦 WebAPI: Error deleting product:', error);
      this.handleError(error);
    }
  }

  // ==================== CATEGORIES ====================
  
  async getCategories(): Promise<Category[]> {
    try {
      console.log('📁 WebAPI: Fetching categories');
      const response = await this.api.get('/categories');
      const data = this.handleResponse(response);
      // Handle both formats: direct array or { categories: [...] }
      return Array.isArray(data) ? data : (data as any).categories || [];
    } catch (error) {
      console.error('📁 WebAPI: Error fetching categories:', error);
      this.handleError(error);
    }
  }

  async createCategory(categoryData: {
    name: string;
    description?: string;
    image?: string;
  }): Promise<Category> {
    try {
      console.log('📁 WebAPI: Creating category:', categoryData.name);
      const response = await this.api.post('/categories', categoryData);
      return this.handleResponse(response);
    } catch (error) {
      console.error('📁 WebAPI: Error creating category:', error);
      this.handleError(error);
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      console.log('📁 WebAPI: Deleting category:', id);
      await this.api.delete(`/categories/${id}`);
    } catch (error) {
      console.error('📁 WebAPI: Error deleting category:', error);
      this.handleError(error);
    }
  }

  // ==================== BRANDS ====================

  async getBrands(): Promise<any[]> {
    try {
      console.log('🏪 WebAPI: Fetching brands');
      const response = await this.api.get('/brands');
      const data = this.handleResponse(response);
      // Handle both formats: direct array or { brands: [...] }
      return Array.isArray(data) ? data : (data as any).brands || [];
    } catch (error) {
      console.error('🏪 WebAPI: Error fetching brands:', error);
      this.handleError(error);
    }
  }

  async createBrand(brandData: {
    name: string;
    description?: string;
    logo?: string;
  }): Promise<any> {
    try {
      console.log('🏪 WebAPI: Creating brand:', brandData.name);
      const response = await this.api.post('/brands', brandData);
      return this.handleResponse(response);
    } catch (error) {
      console.error('🏪 WebAPI: Error creating brand:', error);
      this.handleError(error);
    }
  }

  async deleteBrand(id: string): Promise<void> {
    try {
      console.log('🏪 WebAPI: Deleting brand:', id);
      await this.api.delete(`/brands/${id}`);
    } catch (error) {
      console.error('🏪 WebAPI: Error deleting brand:', error);
      this.handleError(error);
    }
  }

  // ==================== QUOTES ====================
  
  async getQuotes(params?: {
    search?: string;
    status?: string;
    page?: number;
    limit?: number;
    clientId?: string;
    dashboard?: boolean;
  }): Promise<QuotesResponse> {
    try {
      console.log('📋 WebAPI: Fetching quotes with params:', params);
      const response = await this.api.get('/quotes', { params });
      return this.handleResponse(response);
    } catch (error) {
      console.error('📋 WebAPI: Error fetching quotes:', error);
      this.handleError(error);
    }
  }

  async getQuote(id: string): Promise<Quote> {
    try {
      console.log('📋 WebAPI: Fetching quote:', id);
      const response = await this.api.get(`/quotes/${id}`);
      return this.handleResponse(response);
    } catch (error) {
      console.error('📋 WebAPI: Error fetching quote:', error);
      this.handleError(error);
    }
  }

  async createQuote(quoteData: {
    items: { productId: string; quantity: number }[];
    notes?: string;
    clientId?: string;
  }): Promise<{ quote: Quote }> {
    try {
      console.log('📋 WebAPI: Creating quote with', quoteData.items.length, 'items');
      const response = await this.api.post('/quotes', quoteData);
      return this.handleResponse(response);
    } catch (error) {
      console.error('📋 WebAPI: Error creating quote:', error);
      this.handleError(error);
    }
  }

  // ==================== DASHBOARD DATA ====================
  
  async getDashboardData(): Promise<{
    quotes: Quote[];
    products: Product[];
    categories: Category[];
    stats: any;
  }> {
    try {
      console.log('📊 WebAPI: Fetching dashboard data');
      
      // Fetch all data in parallel
      const [quotesResponse, productsResponse, categoriesResponse] = await Promise.all([
        this.getQuotes({ dashboard: true, limit: 10 }),
        this.getProducts({ take: 10 }),
        this.getCategories(),
      ]);

      return {
        quotes: quotesResponse.quotes || [],
        products: productsResponse.products || [],
        categories: categoriesResponse,
        stats: {
          totalQuotes: quotesResponse.total || 0,
          totalProducts: productsResponse.total || 0,
          totalCategories: categoriesResponse.length || 0,
        }
      };
    } catch (error) {
      console.error('📊 WebAPI: Error fetching dashboard data:', error);
      this.handleError(error);
    }
  }

  // ==================== AUTHENTICATION STATUS ====================
  
  async checkAuthStatus(): Promise<{ authenticated: boolean; user?: User }> {
    try {
      console.log('🔐 WebAPI: Checking auth status');
      const response = await this.api.get('/test');
      const data = this.handleResponse(response);
      
      return {
        authenticated: !!(data as any).mobileUser,
        user: (data as any).mobileUser
      };
    } catch (error) {
      console.error('🔐 WebAPI: Error checking auth status:', error);
      return { authenticated: false };
    }
  }

  // ==================== UTILITY METHODS ====================
  
  async testConnection(): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('🧪 WebAPI: Testing connection');
      const response = await this.api.get('/mobile/test');
      const data = this.handleResponse(response);

      return {
        success: true,
        message: 'Connection successful',
        data
      };
    } catch (error) {
      console.error('🧪 WebAPI: Connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection failed'
      };
    }
  }

  // Test mobile app functionality comprehensively
  async runComprehensiveTest(): Promise<{
    connectivity: boolean;
    authentication: boolean;
    apiEndpoints: Record<string, boolean>;
    fileUpload: boolean;
    errors: string[];
  }> {
    const results = {
      connectivity: false,
      authentication: false,
      apiEndpoints: {
        products: false,
        categories: false,
        quotes: false,
        chat: false,
      },
      fileUpload: false,
      errors: [] as string[],
    };

    try {
      // Test basic connectivity
      console.log('🧪 Testing basic connectivity...');
      const connectTest = await this.testConnection();
      results.connectivity = connectTest.success;

      if (connectTest.success) {
        results.authentication = connectTest.data?.authentication?.hasValidAuth || false;

        if (connectTest.data?.apiEndpoints) {
          results.apiEndpoints = { ...connectTest.data.apiEndpoints };
        }
      } else {
        results.errors.push(`Connectivity: ${connectTest.message}`);
      }

      // Test individual API endpoints
      try {
        console.log('🧪 Testing products endpoint...');
        await this.getProducts({ take: 1 });
        results.apiEndpoints.products = true;
      } catch (error) {
        results.errors.push(`Products API: ${error instanceof Error ? error.message : 'Failed'}`);
      }

      try {
        console.log('🧪 Testing categories endpoint...');
        await this.getCategories();
        results.apiEndpoints.categories = true;
      } catch (error) {
        results.errors.push(`Categories API: ${error instanceof Error ? error.message : 'Failed'}`);
      }

      try {
        console.log('🧪 Testing quotes endpoint...');
        await this.getQuotes({ limit: 1 });
        results.apiEndpoints.quotes = true;
      } catch (error) {
        results.errors.push(`Quotes API: ${error instanceof Error ? error.message : 'Failed'}`);
      }

      // Test file upload (if authenticated)
      if (results.authentication) {
        try {
          console.log('🧪 Testing file upload...');
          // Create a small test file
          const testFile = new Blob(['test'], { type: 'text/plain' });
          const formData = new FormData();
          formData.append('file', testFile, 'test.txt');

          const response = await this.api.put('/mobile/test', formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          });

          results.fileUpload = response.status === 200;
        } catch (error) {
          results.errors.push(`File Upload: ${error instanceof Error ? error.message : 'Failed'}`);
        }
      }

    } catch (error) {
      results.errors.push(`Comprehensive test: ${error instanceof Error ? error.message : 'Failed'}`);
    }

    console.log('🧪 Comprehensive test results:', results);
    return results;
  }
}

// Export singleton instance
export default new WebApiService();
