import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import LoadingAnimation from '../components/LoadingAnimation';
import { buildApiUrl } from '../config/api';
import ApiService from '../services/api';

interface TestResult {
  test: string;
  success: boolean;
  data?: any;
  error?: string;
  timestamp: string;
}

const TestConnectionScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [result, ...prev]);
  };

  const testBasicConnectivity = async () => {
    try {
      const response = await fetch(buildApiUrl('/test'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      addTestResult({
        test: 'Basic Connectivity',
        success: response.ok,
        data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      addTestResult({
        test: 'Basic Connectivity',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };

  const testAuthenticatedRequest = async () => {
    try {
      const token = await ApiService.getToken();
      
      if (!token) {
        addTestResult({
          test: 'Authenticated Request',
          success: false,
          error: 'No authentication token found',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const response = await fetch(buildApiUrl('/test'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();
      
      addTestResult({
        test: 'Authenticated Request',
        success: response.ok,
        data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      addTestResult({
        test: 'Authenticated Request',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };

  const testApiService = async () => {
    try {
      const categories = await ApiService.getCategories();
      
      addTestResult({
        test: 'ApiService Categories',
        success: true,
        data: { categoriesCount: categories.length, categories: categories.slice(0, 3) },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      addTestResult({
        test: 'ApiService Categories',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };

  const testQuotesEndpoint = async () => {
    try {
      const quotes = await ApiService.getQuotes({ limit: 5 });
      
      addTestResult({
        test: 'Quotes Endpoint',
        success: true,
        data: { quotesCount: quotes.quotes?.length || 0, total: quotes.total },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      addTestResult({
        test: 'Quotes Endpoint',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    try {
      await testBasicConnectivity();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await testAuthenticatedRequest();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await testApiService();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await testQuotesEndpoint();
    } catch (error) {
      console.error('Test suite error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const renderTestResult = (result: TestResult, index: number) => (
    <View key={index} style={[styles.resultCard, result.success ? styles.successCard : styles.errorCard]}>
      <View style={styles.resultHeader}>
        <Text style={styles.testName}>{result.test}</Text>
        <Ionicons 
          name={result.success ? 'checkmark-circle' : 'close-circle'} 
          size={24} 
          color={result.success ? '#27ae60' : '#e74c3c'} 
        />
      </View>
      
      <Text style={styles.timestamp}>
        {new Date(result.timestamp).toLocaleTimeString()}
      </Text>
      
      {result.error && (
        <Text style={styles.errorText}>❌ {result.error}</Text>
      )}
      
      {result.data && (
        <View style={styles.dataContainer}>
          <Text style={styles.dataLabel}>Response:</Text>
          <Text style={styles.dataText}>{JSON.stringify(result.data, null, 2)}</Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#006db7" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>🧪 Test de Connectivité</Text>
      </View>

      {/* User Info */}
      <View style={styles.userInfo}>
        <Text style={styles.userText}>
          👤 Utilisateur: {user?.firstname} {user?.lastname} ({user?.role})
        </Text>
        <Text style={styles.userText}>
          🔑 Token: {user ? 'Présent' : 'Absent'}
        </Text>
      </View>

      {/* Test Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={runAllTests}
          disabled={isLoading}
        >
          {isLoading ? (
            <LoadingAnimation size={20} color="#fff" />
          ) : (
            <>
              <Ionicons name="play" size={20} color="#fff" />
              <Text style={styles.buttonText}>Lancer tous les tests</Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={clearResults}
          disabled={isLoading}
        >
          <Ionicons name="trash" size={20} color="#666" />
          <Text style={[styles.buttonText, { color: '#666' }]}>Effacer</Text>
        </TouchableOpacity>
      </View>

      {/* Individual Tests */}
      <View style={styles.individualTests}>
        <Text style={styles.sectionTitle}>Tests individuels:</Text>
        <View style={styles.testButtons}>
          <TouchableOpacity style={styles.testButton} onPress={testBasicConnectivity}>
            <Text style={styles.testButtonText}>Connectivité</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.testButton} onPress={testAuthenticatedRequest}>
            <Text style={styles.testButtonText}>Auth</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.testButton} onPress={testApiService}>
            <Text style={styles.testButtonText}>Categories</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.testButton} onPress={testQuotesEndpoint}>
            <Text style={styles.testButtonText}>Quotes</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Results */}
      <ScrollView style={styles.resultsContainer} showsVerticalScrollIndicator={false}>
        {testResults.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="flask-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>Aucun test exécuté</Text>
            <Text style={styles.emptySubtext}>Lancez les tests pour voir les résultats</Text>
          </View>
        ) : (
          testResults.map(renderTestResult)
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  userInfo: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
  },
  userText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  controls: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: '#006db7',
  },
  secondaryButton: {
    backgroundColor: '#f0f0f0',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  individualTests: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  testButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  testButton: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  testButtonText: {
    color: '#006db7',
    fontSize: 12,
    fontWeight: '500',
  },
  resultsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  resultCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  successCard: {
    borderLeftColor: '#27ae60',
  },
  errorCard: {
    borderLeftColor: '#e74c3c',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  testName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#e74c3c',
    marginBottom: 8,
  },
  dataContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 4,
    padding: 8,
  },
  dataLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 4,
  },
  dataText: {
    fontSize: 11,
    color: '#333',
    fontFamily: 'monospace',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default TestConnectionScreen;
