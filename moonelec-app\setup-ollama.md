# 🤖 AI-Powered PDF Extraction Setup Guide

This guide covers the AI options available for PDF extraction in your app. **No external setup required!**

## ✅ **Current Status: Embedded AI Active**

**🎉 No installation required!** The app now includes AI models directly embedded using Transformers.js.

### 🤖 **What's Included:**

- ✅ **Embedded AI Models** - Built into the app
- ✅ **No External Dependencies** - Works offline
- ✅ **Automatic Model Download** - Downloads on first use
- ✅ **Enhanced Text Parsing** - Smart fallback system
- ✅ **Ready for Production** - Perfect for hosting

### 🚀 **How It Works:**

1. **Upload PDF** → App extracts text
2. **AI Processing** → Embedded model analyzes content
3. **Smart Fallback** → Enhanced parsing if AI fails
4. **Product Creation** → Structured data ready to use

## 🔧 **AI Service Priority**

The app automatically chooses the best available AI service:

1. **🔑 OpenAI** (if API key provided) - Highest quality
2. **🧠 Embedded AI** (default) - Built-in, no setup
3. **🦙 Ollama** (if installed) - Local, powerful
4. **🧪 Test Mode** (development) - Simple parsing

### **Current Configuration:**

```env
# Embedded AI is active by default (no configuration needed)
AI_TEST_MODE="false"
OPENAI_API_KEY=""  # Leave empty to use embedded AI
```

## 🎯 Available Models

### Recommended for PDF Extraction:
- **llama3.2:3b** (3GB) - Fast, good for extraction
- **llama3.2:1b** (1.3GB) - Fastest, basic extraction
- **llama3.1:8b** (4.7GB) - Better quality, slower

### Installation commands:
```bash
# Fast and efficient (recommended)
ollama pull llama3.2:3b

# Smallest and fastest
ollama pull llama3.2:1b

# Best quality (requires more RAM)
ollama pull llama3.1:8b
```

## 🔍 Testing PDF Extraction

1. **Start Ollama** (should start automatically)
2. **Verify the model is available**:
   ```bash
   ollama list
   ```
3. **Test in the web app**:
   - Go to Admin → Products
   - Click "Extract from PDF"
   - Upload a PDF file
   - The system will use Ollama automatically

## 🛠️ Troubleshooting

### Ollama not starting
```bash
# Windows (run as administrator)
ollama serve

# macOS/Linux
ollama serve
```

### Model not found
```bash
# Check available models
ollama list

# Pull the required model
ollama pull llama3.2:3b
```

### Connection issues
- Check if Ollama is running: `http://localhost:11434`
- Restart Ollama service
- Check firewall settings

### Memory issues
- Use smaller model: `llama3.2:1b`
- Close other applications
- Increase system RAM if possible

## 🆚 Ollama vs OpenAI

| Feature | Ollama (Free) | OpenAI (Paid) |
|---------|---------------|---------------|
| Cost | Free | $0.002/1K tokens |
| Privacy | Local, private | Cloud-based |
| Speed | Depends on hardware | Fast |
| Quality | Good | Excellent |
| Setup | Requires installation | API key only |
| Offline | Works offline | Requires internet |

## 🔄 Switching Between Services

The app automatically detects which service to use:

1. **If OpenAI API key is set** → Uses OpenAI
2. **If no OpenAI key** → Uses Ollama
3. **If Ollama not available** → Shows error with setup instructions

To force Ollama usage, leave `OPENAI_API_KEY` empty in `.env`:
```env
OPENAI_API_KEY=""
```

## 📊 Performance Tips

1. **Use SSD storage** for better model loading
2. **Allocate enough RAM** (4GB+ recommended)
3. **Close unnecessary applications** during extraction
4. **Use smaller models** for faster processing
5. **Keep Ollama running** to avoid startup delays

## 🎉 Benefits of Local AI

- ✅ **Free forever** - No API costs
- ✅ **Private** - Data never leaves your computer
- ✅ **Offline** - Works without internet
- ✅ **No rate limits** - Process as many PDFs as you want
- ✅ **Customizable** - Switch models based on needs

Happy extracting! 🚀
