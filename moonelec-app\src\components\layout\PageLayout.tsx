'use client';

import { ReactNode, useState, useEffect } from 'react';
import { NavbarWithCart } from '@/components/shared/Navbar';
import Footer from '@/components/shared/Footer';
import LoadingAnimation from '@/components/shared/LoadingAnimation';

interface PageLayoutProps {
  children: ReactNode;
  showLoading?: boolean;
}

export default function PageLayout({
  children,
  showLoading = true
}: PageLayoutProps) {
  const [isLoading, setIsLoading] = useState(showLoading);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (showLoading) {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 200); // Réduire le délai à 200ms

      return () => clearTimeout(timer);
    } else {
      // Si showLoading est false dès le départ, désactiver immédiatement le chargement
      setIsLoading(false);
    }
  }, [showLoading]);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <LoadingAnimation
        isLoading={isLoading}
        onLoadingComplete={() => setIsLoading(false)}
      />

      {!isLoading && (
        <>
          <NavbarWithCart />
          <main className="flex-grow">
            <div className="animate-fade-in">
              {children}
            </div>
          </main>
          <Footer />

          {/* Scroll to top button */}
          {isVisible && (
            <button
              onClick={scrollToTop}
              className="fixed bottom-6 right-6 p-3 bg-primary text-white rounded-full shadow-lg hover:bg-primary-dark focus:outline-none z-50 animate-fade-in"
              aria-label="Retour en haut"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
            </button>
          )}
        </>
      )}
    </div>
  );
}
