'use client';

import { ReactNode, useState, useEffect } from 'react';
import ModernHeader from '@/components/layout/ModernHeader';
import ModernFooter from '@/components/layout/ModernFooter';
import LoadingAnimation from '@/components/shared/LoadingAnimation';

interface PageLayoutProps {
  children: ReactNode;
  showLoading?: boolean;
}

export default function PageLayout({
  children,
  showLoading = true
}: PageLayoutProps) {
  const [isLoading, setIsLoading] = useState(showLoading);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (showLoading) {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 200); // Réduire le délai à 200ms

      return () => clearTimeout(timer);
    } else {
      // Si showLoading est false dès le départ, désactiver immédiatement le chargement
      setIsLoading(false);
    }
  }, [showLoading]);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <LoadingAnimation
        isLoading={isLoading}
        onLoadingComplete={() => setIsLoading(false)}
      />

      {!isLoading && (
        <>
          <ModernHeader />
          <main className="flex-grow">
            <div className="animate-fade-in">
              {children}
            </div>
          </main>
          <ModernFooter />
        </>
      )}
    </div>
  );
}
