'use client';

import { useAuth } from '@/hooks/useAuth';
import { useEffect, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import LoadingAnimation from '@/components/shared/LoadingAnimation';
import { UserRole } from '@prisma/client';

type RouteGuardProps = {
  children: ReactNode;
  allowedRoles?: UserRole[];
};

export default function RouteGuard({
  children,
  allowedRoles = [],
}: RouteGuardProps) {
  const { isAuthenticated, isLoading, user, redirectToLogin } = useAuth();
  const pathname = usePathname();

  useEffect(() => {
    // Si le chargement est terminé et que l'utilisateur n'est pas authentifié
    if (!isLoading && !isAuthenticated) {
      // Stocker le chemin actuel pour rediriger après la connexion
      sessionStorage.setItem('redirectAfterLogin', pathname);
      redirectToLogin();
    }
  }, [isLoading, isAuthenticated, pathname, redirectToLogin]);

  useEffect(() => {
    // Si l'utilisateur est authentifié mais n'a pas le rôle requis
    if (
      isAuthenticated &&
      allowedRoles.length > 0 &&
      user?.role &&
      !allowedRoles.includes(user.role)
    ) {
      // Rediriger vers la page d'accueil ou une page d'erreur
      window.location.href = '/unauthorized';
    }
  }, [isAuthenticated, allowedRoles, user]);

  // Afficher un écran de chargement pendant la vérification
  if (isLoading || !isAuthenticated) {
    return <LoadingAnimation isLoading={true} onLoadingComplete={() => {}} />;
  }

  // Si l'utilisateur est authentifié et a le rôle requis (ou aucun rôle n'est requis)
  return <>{children}</>;
}
