-- AlterTable
ALTER TABLE `notification` ADD COLUMN `salesReportId` VARCHAR(191) NULL,
    MODIFY `type` ENUM('QUOTE_REQUESTED', 'QUOTE_APPROVED', 'QUOTE_REJECTED', 'ORDER_PLACED', 'ORDER_SHIPPED', 'REPORT_REMINDER', 'REPORT_SUBMITTED') NOT NULL;

-- CreateTable
CREATE TABLE `salesreport` (
    `id` VARCHAR(191) NOT NULL,
    `commercialId` VARCHAR(191) NOT NULL,
    `need` TEXT NOT NULL,
    `articleRef` VARCHAR(191) NULL,
    `comment` TEXT NULL,
    `visitDate` DATETIME(3) NOT NULL,
    `denomination` VARCHAR(191) NOT NULL,
    `images` TEXT NULL,
    `name` VARCHAR(191) NOT NULL,
    `visitPurpose` TEXT NOT NULL,
    `complaint` TEXT NULL,
    `city` VARCHAR(191) NOT NULL,
    `videoUrl` VARCHAR(191) NULL,
    `audioUrl` VARCHAR(191) NULL,
    `pdfUrl` VARCHAR(191) NULL,
    `submittedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `lastReminder` DATETIME(3) NULL,
    `isCompleted` BOOLEAN NOT NULL DEFAULT false,

    INDEX `SalesReport_commercialId_fkey`(`commercialId`),
    INDEX `SalesReport_visitDate_idx`(`visitDate`),
    INDEX `SalesReport_submittedAt_idx`(`submittedAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Notification_salesReportId_fkey` ON `notification`(`salesReportId`);

-- AddForeignKey
ALTER TABLE `notification` ADD CONSTRAINT `Notification_salesReportId_fkey` FOREIGN KEY (`salesReportId`) REFERENCES `salesreport`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `salesreport` ADD CONSTRAINT `salesreport_commercialId_fkey` FOREIGN KEY (`commercialId`) REFERENCES `commercial`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
