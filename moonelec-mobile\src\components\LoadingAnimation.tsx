import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

interface LoadingAnimationProps {
  size?: number;
  color?: string;
}

export const LoadingAnimation: React.FC<LoadingAnimationProps> = ({ 
  size = 80, 
  color = '#006db7' 
}) => {
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0.8)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Rotation animation
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    );

    // Scale animation
    const scaleAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 0.8,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    // Pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );

    spinAnimation.start();
    scaleAnimation.start();
    pulseAnimation.start();

    return () => {
      spinAnimation.stop();
      scaleAnimation.stop();
      pulseAnimation.stop();
    };
  }, [spinValue, scaleValue, pulseValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      {/* Outer pulse ring */}
      <Animated.View
        style={[
          styles.pulseRing,
          {
            width: size * 2,
            height: size * 2,
            borderRadius: size,
            borderColor: color,
            opacity: pulseValue,
            transform: [{ scale: scaleValue }],
          },
        ]}
      />
      
      {/* Main spinning circle */}
      <Animated.View
        style={[
          styles.spinner,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderTopColor: color,
            borderRightColor: color,
            transform: [{ rotate: spin }],
          },
        ]}
      />
      
      {/* Inner dot */}
      <View
        style={[
          styles.innerDot,
          {
            width: size * 0.3,
            height: size * 0.3,
            borderRadius: size * 0.15,
            backgroundColor: color,
          },
        ]}
      />
      
      {/* Electric effect dots */}
      <Animated.View
        style={[
          styles.electricDot1,
          {
            width: 8,
            height: 8,
            backgroundColor: '#ed1c24',
            transform: [{ rotate: spin }, { translateX: size * 0.6 }],
          },
        ]}
      />
      <Animated.View
        style={[
          styles.electricDot2,
          {
            width: 6,
            height: 6,
            backgroundColor: '#ed1c24',
            transform: [
              { rotate: spin },
              { rotateZ: '120deg' },
              { translateX: size * 0.5 },
            ],
          },
        ]}
      />
      <Animated.View
        style={[
          styles.electricDot3,
          {
            width: 4,
            height: 4,
            backgroundColor: '#ed1c24',
            transform: [
              { rotate: spin },
              { rotateZ: '240deg' },
              { translateX: size * 0.4 },
            ],
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  pulseRing: {
    position: 'absolute',
    borderWidth: 2,
    borderStyle: 'solid',
  },
  spinner: {
    borderWidth: 4,
    borderBottomColor: 'transparent',
    borderLeftColor: 'transparent',
    borderStyle: 'solid',
    position: 'absolute',
  },
  innerDot: {
    position: 'absolute',
  },
  electricDot1: {
    position: 'absolute',
    borderRadius: 4,
  },
  electricDot2: {
    position: 'absolute',
    borderRadius: 3,
  },
  electricDot3: {
    position: 'absolute',
    borderRadius: 2,
  },
});

export default LoadingAnimation;
