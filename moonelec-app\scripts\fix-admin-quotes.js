import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting data cleanup...');

    // 1. R<PERSON>cupérer tous les devis avec createdByAdminId non null
    const quotes = await prisma.quote.findMany({
      where: {
        createdByAdminId: {
          not: null,
        },
      },
    });

    console.log(`Found ${quotes.length} quotes with createdByAdminId`);

    // 2. Récupérer tous les administrateurs
    const admins = await prisma.admin.findMany();

    if (admins.length === 0) {
      console.log('No admins found. Creating a default admin...');

      // Créer un administrateur par défaut si aucun n'existe
      const defaultAdmin = await prisma.admin.create({
        data: {
          user: {
            create: {
              username: 'admin',
              email: '<EMAIL>',
              password: '$2a$10$8r84Qrv2Ow2PtE/1hm2Z3eQH.GvBNcKA.9WoJvJ5q9YyQcNBOjUAy', // 123456
              firstname: 'Admin',
              lastname: '<PERSON>ele<PERSON>',
              role: 'ADMIN',
            },
          },
        },
        include: {
          user: true,
        },
      });

      console.log(`Created default admin with ID: ${defaultAdmin.id}`);

      // Mettre à jour tous les devis pour utiliser cet administrateur
      if (quotes.length > 0) {
        const updateResult = await prisma.quote.updateMany({
          where: {
            createdByAdminId: {
              not: null,
            },
          },
          data: {
            createdByAdminId: defaultAdmin.id,
          },
        });

        console.log(`Updated ${updateResult.count} quotes to use the default admin`);
      }
    } else {
      console.log(`Found ${admins.length} admins`);

      // Vérifier si les createdByAdminId correspondent à des administrateurs existants
      const adminIds = admins.map(admin => admin.id);

      // Compter les devis avec des IDs d'admin invalides
      const invalidQuotes = quotes.filter(quote => !adminIds.includes(quote.createdByAdminId));

      if (invalidQuotes.length > 0) {
        console.log(`Found ${invalidQuotes.length} quotes with invalid admin IDs`);

        // Mettre à jour ces devis pour utiliser le premier administrateur
        const firstAdmin = admins[0];

        const updateResult = await prisma.quote.updateMany({
          where: {
            id: {
              in: invalidQuotes.map(q => q.id),
            },
          },
          data: {
            createdByAdminId: firstAdmin.id,
          },
        });

        console.log(`Updated ${updateResult.count} quotes to use admin: ${firstAdmin.id}`);
      } else {
        console.log('All quotes have valid admin IDs');
      }
    }

    console.log('Data cleanup completed successfully');
  } catch (error) {
    console.error('Error during data cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
