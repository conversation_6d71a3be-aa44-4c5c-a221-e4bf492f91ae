import { NextRequest, NextResponse } from 'next/server';
import { createClientUser, findUserByEmail, findUserByUsername } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      email,
      username,
      password,
      lastname,
      firstname,
      telephone,
      company_name,
    } = body;

    // Vérifier que tous les champs requis sont présents
    if (!email || !username || !password || !lastname || !firstname) {
      return NextResponse.json(
        { error: 'Tous les champs requis doivent être remplis' },
        { status: 400 }
      );
    }

    // Vérifier si l'email est déjà utilisé
    const existingEmail = await findUserByEmail(email);
    if (existingEmail) {
      return NextResponse.json(
        { error: 'Cet email est déjà utilisé' },
        { status: 400 }
      );
    }

    // Vérifier si le nom d'utilisateur est déjà utilisé
    const existingUsername = await findUserByUsername(username);
    if (existingUsername) {
      return NextResponse.json(
        { error: "Ce nom d'utilisateur est déjà utilisé" },
        { status: 400 }
      );
    }

    // Créer le nouvel utilisateur client
    const newUser = await createClientUser({
      email,
      username,
      password,
      lastname,
      firstname,
      telephone,
      company_name,
    });

    // Retourner une réponse de succès sans le mot de passe
    return NextResponse.json(
      {
        message: 'Utilisateur créé avec succès',
        user: {
          id: newUser.user.id,
          email: newUser.user.email,
          username: newUser.user.username,
          lastname: newUser.user.lastname,
          firstname: newUser.user.firstname,
          role: newUser.user.role,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Erreur lors de l\'inscription:', error);
    return NextResponse.json(
      { error: 'Une erreur est survenue lors de l\'inscription' },
      { status: 500 }
    );
  }
}
