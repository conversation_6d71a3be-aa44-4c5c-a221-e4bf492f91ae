import { NextRequest, NextResponse } from 'next/server';
import { getCategories, createCategory, getCategoriesWithProductCount } from '@/lib/categories';

// GET /api/categories - Get all categories
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const search = searchParams.get('search') || undefined;
    const includeProducts = searchParams.get('includeProducts') === 'true';
    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;
    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;
    const withCount = searchParams.get('withCount') === 'true';

    // Utiliser les données de la base de données
    if (withCount) {
      const categories = await getCategoriesWithProductCount();
      return NextResponse.json({ categories, total: categories.length });
    } else {
      const { categories, total } = await getCategories({
        search,
        includeProducts,
        skip,
        take,
      });

      return NextResponse.json({ categories, total });
    }
  } catch (error: any) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create a new category
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, description, image } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Create the category
    const category = await createCategory({
      name,
      description,
      image,
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error: any) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create category' },
      { status: 500 }
    );
  }
}
