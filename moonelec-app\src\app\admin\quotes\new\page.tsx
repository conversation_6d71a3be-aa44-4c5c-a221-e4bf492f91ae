'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { <PERSON>aPlus, FaMinus, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaFileAlt, FaArrowLeft } from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';

interface Client {
  id: string;
  userId: string;
  company_name?: string;
  user: {
    firstname: string;
    lastname: string;
    email: string;
    telephone?: string;
  };
}

interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  mainImage: string | null;
  category?: {
    id: string;
    name: string;
  } | null;
  brand?: {
    id: string;
    name: string;
  } | null;
}

interface QuoteItem {
  productId: string;
  product: Product;
  quantity: number;
}

export default function NewQuotePage() {
  const router = useRouter();
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [quoteItems, setQuoteItems] = useState<QuoteItem[]>([]);
  const [notes, setNotes] = useState('');
  const [isForSelf, setIsForSelf] = useState(false);
  
  // Charger les clients et les produits
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Charger les clients
        const clientsResponse = await fetch('/api/clients');
        const clientsData = await clientsResponse.json();
        
        // Charger les produits
        const productsResponse = await fetch('/api/products?take=100');
        const productsData = await productsResponse.json();
        
        setClients(clientsData.clients);
        setProducts(productsData.products);
        setFilteredProducts(productsData.products);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Filtrer les produits en fonction du terme de recherche
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProducts(filtered);
    }
  }, [searchTerm, products]);
  
  // Ajouter un produit au devis
  const addProductToQuote = (product: Product) => {
    setQuoteItems(prevItems => {
      // Vérifier si le produit est déjà dans le devis
      const existingItemIndex = prevItems.findIndex(item => item.productId === product.id);
      
      if (existingItemIndex !== -1) {
        // Mettre à jour la quantité si le produit existe déjà
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += 1;
        return updatedItems;
      } else {
        // Ajouter un nouveau produit
        return [...prevItems, {
          productId: product.id,
          product,
          quantity: 1
        }];
      }
    });
  };
  
  // Mettre à jour la quantité d'un produit dans le devis
  const updateItemQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemFromQuote(productId);
      return;
    }
    
    setQuoteItems(prevItems => 
      prevItems.map(item => 
        item.productId === productId ? { ...item, quantity } : item
      )
    );
  };
  
  // Supprimer un produit du devis
  const removeItemFromQuote = (productId: string) => {
    setQuoteItems(prevItems => prevItems.filter(item => item.productId !== productId));
  };
  
  // Créer le devis
  const handleCreateQuote = async () => {
    // Vérifier si un client est sélectionné ou si c'est pour l'admin lui-même
    if (!selectedClientId && !isForSelf) {
      alert('Veuillez sélectionner un client ou cocher "Créer pour moi-même"');
      return;
    }
    
    // Vérifier si des produits sont ajoutés au devis
    if (quoteItems.length === 0) {
      alert('Veuillez ajouter au moins un produit au devis');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Préparer les données du devis
      const quoteData = {
        clientId: isForSelf ? user?.clientId : selectedClientId,
        items: quoteItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity
        })),
        notes
      };
      
      // Envoyer la demande de devis à l'API
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quoteData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erreur lors de la création du devis');
      }
      
      const data = await response.json();
      
      // Rediriger vers la page d'édition du devis
      router.push(`/admin/quotes/${data.quote.id}/edit`);
    } catch (error) {
      console.error('Erreur lors de la création du devis:', error);
      alert('Une erreur est survenue lors de la création du devis. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
            Nouveau Devis
          </h1>
          <Link href="/admin/quotes">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              <FaArrowLeft className="mr-2" />
              Retour à la liste
            </motion.button>
          </Link>
        </div>
        
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Formulaire de devis */}
            <div className="lg:col-span-2 space-y-6">
              {/* Sélection du client */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                  <FaUser className="mr-2 text-primary" />
                  Client
                </h2>
                
                <div className="mb-4">
                  <label className="flex items-center space-x-2 text-gray-700 dark:text-gray-300 mb-4">
                    <input
                      type="checkbox"
                      checked={isForSelf}
                      onChange={(e) => {
                        setIsForSelf(e.target.checked);
                        if (e.target.checked) {
                          setSelectedClientId('');
                        }
                      }}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <span>Créer un devis pour moi-même (test)</span>
                  </label>
                  
                  {!isForSelf && (
                    <div className="relative">
                      <select
                        value={selectedClientId}
                        onChange={(e) => setSelectedClientId(e.target.value)}
                        disabled={isForSelf}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                      >
                        <option value="">Sélectionner un client</option>
                        {clients.map((client) => (
                          <option key={client.id} value={client.id}>
                            {client.user.firstname} {client.user.lastname} {client.company_name ? `(${client.company_name})` : ''}
                          </option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Recherche de produits */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                  Ajouter des produits
                </h2>
                
                <div className="relative mb-4">
                  <input
                    type="text"
                    placeholder="Rechercher un produit par nom, référence ou description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                
                <div className="max-h-96 overflow-y-auto">
                  {filteredProducts.length === 0 ? (
                    <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                      Aucun produit trouvé
                    </p>
                  ) : (
                    <div className="divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredProducts.map((product) => (
                        <div key={product.id} className="py-3 flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 relative rounded overflow-hidden bg-gray-100 dark:bg-gray-700 mr-3 flex-shrink-0">
                              {product.mainImage ? (
                                <Image
                                  src={product.mainImage}
                                  alt={product.name}
                                  fill
                                  style={{ objectFit: 'cover' }}
                                />
                              ) : (
                                <div className="flex items-center justify-center h-full text-gray-400">
                                  N/A
                                </div>
                              )}
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-800 dark:text-white">
                                {product.name}
                              </h3>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                Réf: {product.reference}
                              </p>
                            </div>
                          </div>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => addProductToQuote(product)}
                            className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors text-sm"
                          >
                            <FaPlus />
                          </motion.button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* Résumé du devis */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 sticky top-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                  <FaFileAlt className="mr-2 text-primary" />
                  Résumé du devis
                </h2>
                
                {quoteItems.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    Aucun produit ajouté
                  </p>
                ) : (
                  <div className="mb-6">
                    <div className="max-h-80 overflow-y-auto mb-4">
                      <div className="divide-y divide-gray-200 dark:divide-gray-700">
                        {quoteItems.map((item) => (
                          <div key={item.productId} className="py-3">
                            <div className="flex justify-between mb-2">
                              <div className="pr-4">
                                <h3 className="text-sm font-medium text-gray-800 dark:text-white">
                                  {item.product.name}
                                </h3>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  Réf: {item.product.reference}
                                </p>
                              </div>
                              <button
                                onClick={() => removeItemFromQuote(item.productId)}
                                className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                              >
                                <FaTrash />
                              </button>
                            </div>
                            <div className="flex items-center">
                              <button
                                onClick={() => updateItemQuantity(item.productId, item.quantity - 1)}
                                className="p-1 bg-gray-100 dark:bg-gray-700 rounded-l-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                              >
                                <FaMinus size={12} />
                              </button>
                              <input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => updateItemQuantity(item.productId, parseInt(e.target.value) || 1)}
                                className="w-12 px-2 py-1 text-center border-y border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none"
                              />
                              <button
                                onClick={() => updateItemQuantity(item.productId, item.quantity + 1)}
                                className="p-1 bg-gray-100 dark:bg-gray-700 rounded-r-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                              >
                                <FaPlus size={12} />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Notes
                      </label>
                      <textarea
                        id="notes"
                        rows={3}
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                        placeholder="Ajouter des notes ou instructions spéciales..."
                      ></textarea>
                    </div>
                  </div>
                )}
                
                <motion.button
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                  onClick={handleCreateQuote}
                  disabled={isSubmitting || quoteItems.length === 0 || (!selectedClientId && !isForSelf)}
                  className={`w-full py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center ${
                    (isSubmitting || quoteItems.length === 0 || (!selectedClientId && !isForSelf)) ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Création en cours...
                    </>
                  ) : (
                    <>
                      <FaFileAlt className="mr-2" />
                      Créer le devis
                    </>
                  )}
                </motion.button>
              </div>
            </div>
          </div>
        )}
      </div>
    </RouteGuard>
  );
}
