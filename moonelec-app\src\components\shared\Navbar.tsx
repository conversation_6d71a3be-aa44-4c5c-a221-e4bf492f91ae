'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaBars, FaTimes, FaUser, FaShoppingCart, FaSun, FaMoon } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import { useCart } from '@/context/CartContext';
import { useTheme } from '@/context/ThemeContext';
import CartSlidePanel from '@/components/cart/CartSlidePanel';
import NotificationBell from '@/components/notifications/NotificationBell';

export default function Navbar() {
  const { user } = useAuth();
  const { itemCount, isCartOpen, openCart, closeCart } = useCart();
  const { actualTheme, toggleTheme } = useTheme();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <motion.header
      className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${
        isScrolled ? 'bg-white/90 dark:bg-[#0a0a0a]/90 backdrop-blur-md shadow-md py-2' : 'bg-transparent py-4'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link href="/" className="flex items-center">
          <Image
            src="/images/logo/logo-moonelec.png"
            alt="Moonelec Logo"
            width={180}
            height={60}
            className="mr-2"
            priority
          />
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <NavLink href="/about">À Propos</NavLink>
          <NavLink href="#services">Services</NavLink>
          <NavLink href="/products">Produits</NavLink>
          <NavLink href="#brands">Marques</NavLink>
          <NavLink href="#contact">Contact</NavLink>

          {/* Theme Toggle */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleTheme}
            className="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-label={actualTheme === 'dark' ? 'Passer en mode clair' : 'Passer en mode sombre'}
          >
            {actualTheme === 'dark' ? (
              <FaSun className="text-yellow-400 text-lg" />
            ) : (
              <FaMoon className="text-blue-600 text-lg" />
            )}
          </motion.button>

          {!user ? (
            <>
              <Link href="/auth/signin" className="btn-outline">
                Connexion
              </Link>
              <Link href="/auth/signup" className="btn-primary">
                S'inscrire
              </Link>
            </>
          ) : (
            <div className="flex items-center space-x-4">
              <Link href="/account" className="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors">
                <div className="relative w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden border border-gray-300 dark:border-gray-600">
                  {user.image ? (
                    <Image
                      src={user.image}
                      alt={user.firstname || user.username}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <span className="text-gray-500 dark:text-gray-400 text-sm font-medium">
                      {user.firstname ? user.firstname.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>
                <span className="font-medium">
                  {user.firstname || user.username}
                </span>
              </Link>

              {/* Notification Bell - Only shown for admins */}
              <NotificationBell />

              <button
                onClick={openCart}
                className="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors"
                aria-label="Ouvrir le panier"
              >
                <FaShoppingCart className="h-6 w-6 navbar-cart-icon" />
                {itemCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {itemCount > 99 ? '99+' : itemCount}
                  </span>
                )}
              </button>
            </div>
          )}
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-2xl focus:outline-none"
          onClick={toggleMobileMenu}
          aria-label={isMobileMenuOpen ? "Fermer le menu" : "Ouvrir le menu"}
        >
          {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>

      {/* Mobile Navigation */}
      <motion.div
        className={`md:hidden absolute top-full left-0 right-0 bg-white dark:bg-[#0a0a0a] shadow-lg`}
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: isMobileMenuOpen ? 'auto' : 0,
          opacity: isMobileMenuOpen ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        style={{ overflow: 'hidden' }}
      >
        <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
          <MobileNavLink href="/about" onClick={() => setIsMobileMenuOpen(false)}>
            À Propos
          </MobileNavLink>
          <MobileNavLink href="#services" onClick={() => setIsMobileMenuOpen(false)}>
            Services
          </MobileNavLink>
          <MobileNavLink href="/products" onClick={() => setIsMobileMenuOpen(false)}>
            Produits
          </MobileNavLink>
          <MobileNavLink href="#brands" onClick={() => setIsMobileMenuOpen(false)}>
            Marques
          </MobileNavLink>
          <MobileNavLink href="#contact" onClick={() => setIsMobileMenuOpen(false)}>
            Contact
          </MobileNavLink>

          {/* Mobile Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="flex items-center justify-between py-2 px-4 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors"
          >
            <span>Mode {actualTheme === 'dark' ? 'Clair' : 'Sombre'}</span>
            {actualTheme === 'dark' ? (
              <FaSun className="text-yellow-400 text-lg" />
            ) : (
              <FaMoon className="text-blue-600 text-lg" />
            )}
          </button>

          {!user ? (
            <div className="flex flex-col space-y-2 pt-2">
              <Link
                href="/auth/signin"
                className="btn-outline w-full text-center"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Connexion
              </Link>
              <Link
                href="/auth/signup"
                className="btn-primary w-full text-center"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                S'inscrire
              </Link>
            </div>
          ) : (
            <div className="flex flex-col space-y-2 pt-2 border-t border-gray-100 dark:border-gray-800">
              <div className="flex items-center py-2">
                <div className="relative w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden border border-gray-300 dark:border-gray-600 mr-3">
                  {user.image ? (
                    <Image
                      src={user.image}
                      alt={user.firstname || user.username}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <span className="text-gray-500 dark:text-gray-400 text-sm font-medium">
                      {user.firstname ? user.firstname.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>
                <span className="font-medium text-gray-800 dark:text-white">
                  {user.firstname || user.username}
                </span>
              </div>

              <Link
                href="/account"
                className="py-2 px-4 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Mon compte
              </Link>

              <button
                className="w-full py-2 px-4 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors flex items-center justify-between text-left"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  openCart();
                }}
              >
                <span>Mon panier</span>
                {itemCount > 0 && (
                  <span className="bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {itemCount > 99 ? '99+' : itemCount}
                  </span>
                )}
              </button>
            </div>
          )}
        </div>
      </motion.div>
    </motion.header>
  );
}

// Composant CartSlidePanel rendu séparément pour éviter les problèmes de syntaxe
function NavbarWithCart() {
  const { isCartOpen, closeCart } = useCart();

  return (
    <>
      <Navbar />
      <CartSlidePanel isOpen={isCartOpen} onClose={closeCart} />
    </>
  );
}

export { NavbarWithCart };

function NavLink({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <Link
      href={href}
      className="relative font-medium text-text-primary hover:text-primary transition-colors group"
    >
      {children}
      <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
    </Link>
  );
}

function MobileNavLink({
  href,
  children,
  onClick
}: {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
}) {
  return (
    <Link
      href={href}
      className="py-2 px-4 border-b border-gray-100 dark:border-gray-800 text-text-primary hover:text-primary transition-colors"
      onClick={onClick}
    >
      {children}
    </Link>
  );
}
