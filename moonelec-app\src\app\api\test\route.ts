import { NextRequest, NextResponse } from 'next/server';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';

// GET /api/test - Test endpoint for mobile connectivity
export async function GET(req: NextRequest) {
  try {
    console.log('🧪 Test API - Request received');
    console.log('🧪 Headers:', Object.fromEntries(req.headers.entries()));
    
    // Check for mobile authentication
    const mobileUser = await getMobileUserFromRequest(req);
    
    console.log('🧪 Mobile user:', mobileUser ? { id: mobileUser.id, role: mobileUser.role } : 'None');
    
    return NextResponse.json({
      success: true,
      message: 'API is working!',
      timestamp: new Date().toISOString(),
      mobileUser: mobileUser ? {
        id: mobileUser.id,
        username: mobileUser.username,
        role: mobileUser.role
      } : null,
      headers: {
        userAgent: req.headers.get('user-agent'),
        authorization: req.headers.get('authorization') ? 'Present' : 'Missing'
      }
    });
  } catch (error) {
    console.error('🧪 Test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// POST /api/test - Test endpoint for mobile authentication
export async function POST(req: NextRequest) {
  try {
    console.log('🧪 Test API POST - Request received');
    
    const body = await req.json();
    console.log('🧪 Request body:', body);
    
    // Check for mobile authentication
    const mobileUser = await getMobileUserFromRequest(req);
    
    return NextResponse.json({
      success: true,
      message: 'POST test successful!',
      receivedData: body,
      mobileUser: mobileUser ? {
        id: mobileUser.id,
        username: mobileUser.username,
        role: mobileUser.role
      } : null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('🧪 Test API POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
