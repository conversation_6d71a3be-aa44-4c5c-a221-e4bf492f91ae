'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaShoppingCart, FaFileInvoice, FaHistory, FaHeart, FaUser } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';

export default function ClientDashboard() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simuler un chargement de données
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 }
    }
  };

  // Données simulées pour le tableau de bord
  const dashboardData = {
    totalOrders: 12,
    pendingOrders: 2,
    completedOrders: 10,
    favoriteProducts: 5,
    lastLogin: '2023-07-15 14:30:00'
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['CLIENT']}>
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8"
        >
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Bienvenue, {user?.firstname} {user?.lastname}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Voici un aperçu de votre activité chez Moonelec
          </p>
        </motion.div>

        {/* Dashboard Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Commandes totales"
              value={dashboardData.totalOrders}
              icon={<FaShoppingCart className="text-blue-500" />}
              bgColor="bg-blue-50 dark:bg-blue-900/20"
              textColor="text-blue-700 dark:text-blue-300"
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Commandes en cours"
              value={dashboardData.pendingOrders}
              icon={<FaHistory className="text-yellow-500" />}
              bgColor="bg-yellow-50 dark:bg-yellow-900/20"
              textColor="text-yellow-700 dark:text-yellow-300"
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Commandes terminées"
              value={dashboardData.completedOrders}
              icon={<FaFileInvoice className="text-green-500" />}
              bgColor="bg-green-50 dark:bg-green-900/20"
              textColor="text-green-700 dark:text-green-300"
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Produits favoris"
              value={dashboardData.favoriteProducts}
              icon={<FaHeart className="text-red-500" />}
              bgColor="bg-red-50 dark:bg-red-900/20"
              textColor="text-red-700 dark:text-red-300"
            />
          </motion.div>
        </motion.div>

        {/* Recent Orders and Account Info */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="lg:col-span-2"
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FaShoppingCart className="mr-2" /> Commandes récentes
              </h2>
              
              {dashboardData.totalOrders > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          N° Commande
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Montant
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Statut
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          #ORD-12345
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          15/07/2023
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          1 250,00 MAD
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Livré
                          </span>
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          #ORD-12344
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          10/07/2023
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          2 750,00 MAD
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            En cours
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">
                    Vous n'avez pas encore passé de commande.
                  </p>
                  <button className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors">
                    Parcourir les produits
                  </button>
                </div>
              )}
              
              {dashboardData.totalOrders > 0 && (
                <div className="mt-4 text-right">
                  <button className="text-primary hover:underline text-sm font-medium">
                    Voir toutes les commandes
                  </button>
                </div>
              )}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FaUser className="mr-2" /> Informations du compte
              </h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom complet</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">{user?.firstname} {user?.lastname}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">{user?.email}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom d'utilisateur</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">{user?.username}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Dernière connexion</h3>
                  <p className="mt-1 text-gray-900 dark:text-white">{dashboardData.lastLogin}</p>
                </div>
              </div>
              
              <div className="mt-6">
                <button className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors">
                  Modifier le profil
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </RouteGuard>
  );
}

interface DashboardCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  bgColor: string;
  textColor: string;
}

function DashboardCard({ title, value, icon, bgColor, textColor }: DashboardCardProps) {
  return (
    <div className={`rounded-lg shadow-md overflow-hidden ${bgColor}`}>
      <div className="p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0 p-3 rounded-full bg-white dark:bg-gray-700 shadow-sm">
            {icon}
          </div>
          <div className="ml-5">
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {title}
            </p>
            <p className={`text-2xl font-semibold ${textColor}`}>
              {value}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
