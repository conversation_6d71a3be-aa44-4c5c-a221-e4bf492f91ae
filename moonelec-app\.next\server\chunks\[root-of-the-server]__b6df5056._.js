module.exports = {

"[project]/.next-internal/server/app/api/commercials/dashboard/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[project]/src/lib/mobile-auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMobileUserFromRequest": (()=>getMobileUserFromRequest),
    "isMobileRequest": (()=>isMobileRequest),
    "verifyMobileToken": (()=>verifyMobileToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
async function verifyMobileToken(request) {
    try {
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('🔐 No valid authorization header found');
            return null;
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        console.log('🔐 Mobile token received (length):', token.length);
        // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET
        let decoded;
        try {
            const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
            console.log('🔐 Trying JWT_SECRET for token verification');
            decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, jwtSecret);
            console.log('🔐 Token decoded successfully with JWT_SECRET:', {
                userId: decoded.userId,
                role: decoded.role
            });
        } catch (jwtError) {
            console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');
            try {
                const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';
                decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, nextAuthSecret);
                console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', {
                    userId: decoded.userId,
                    role: decoded.role
                });
            } catch (nextAuthError) {
                console.error('🔐 Both JWT secrets failed:', {
                    jwtError: jwtError.message,
                    nextAuthError: nextAuthError.message
                });
                throw new Error('Invalid token signature');
            }
        }
        // Get fresh user data from database
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: decoded.userId
            },
            select: {
                id: true,
                username: true,
                email: true,
                firstname: true,
                lastname: true,
                role: true
            }
        });
        if (!user) {
            console.log('🔐 User not found:', {
                found: !!user
            });
            return null;
        }
        console.log('🔐 Mobile user authenticated successfully:', {
            id: user.id,
            role: user.role
        });
        return user;
    } catch (error) {
        console.error('Mobile token verification error:', error);
        return null;
    }
}
function isMobileRequest(request) {
    const userAgent = request.headers.get('user-agent') || '';
    const authHeader = request.headers.get('authorization');
    // Check if it's a mobile request with JWT token
    return authHeader?.startsWith('Bearer ') || userAgent.includes('Expo') || userAgent.includes('ReactNative');
}
async function getMobileUserFromRequest(request) {
    // Always try to verify mobile token if Authorization header is present
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
        return await verifyMobileToken(request);
    }
    return null;
}
}}),
"[project]/src/app/api/commercials/dashboard/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mobile-auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        // Verify authentication
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyMobileToken"])(request);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Non autorisé'
            }, {
                status: 401
            });
        }
        // Check if user is a commercial
        if (user.role !== 'COMMERCIAL') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Accès refusé - Rôle commercial requis'
            }, {
                status: 403
            });
        }
        // Get current date for calculations
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        // Get clients assigned to this commercial
        const totalClients = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count({
            where: {
                role: 'CLIENT',
                commercialId: user.id
            }
        });
        // Get new clients this month
        const newClients = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count({
            where: {
                role: 'CLIENT',
                commercialId: user.id,
                createdAt: {
                    gte: startOfMonth
                }
            }
        });
        // Get quotes for this commercial's clients
        const quotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where: {
                user: {
                    commercialId: user.id
                }
            },
            include: {
                items: true
            }
        });
        // Calculate total sales (sum of all quotes)
        const totalSales = quotes.reduce((sum, quote)=>{
            const quoteTotal = quote.items.reduce((itemSum, item)=>{
                return itemSum + item.quantity * item.unitPrice;
            }, 0);
            return sum + quoteTotal;
        }, 0);
        // Calculate monthly sales (this month)
        const monthlyQuotes = quotes.filter((quote)=>quote.createdAt >= startOfMonth);
        const monthlySales = monthlyQuotes.reduce((sum, quote)=>{
            const quoteTotal = quote.items.reduce((itemSum, item)=>{
                return itemSum + item.quantity * item.unitPrice;
            }, 0);
            return sum + quoteTotal;
        }, 0);
        // Get pending quotes count
        const pendingOrders = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.count({
            where: {
                user: {
                    commercialId: user.id
                },
                status: 'PENDING'
            }
        });
        // Get recent clients with their last quote
        const recentClients = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
            where: {
                role: 'CLIENT',
                commercialId: user.id
            },
            include: {
                quotes: {
                    orderBy: {
                        createdAt: 'desc'
                    },
                    take: 1
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: 5
        });
        // Format recent clients data
        const formattedRecentClients = recentClients.map((client)=>({
                id: client.id,
                firstname: client.firstname,
                lastname: client.lastname,
                email: client.email,
                company: client.company,
                lastOrder: client.quotes[0] ? client.quotes[0].createdAt.toLocaleDateString('fr-FR') : null,
                status: client.quotes.length > 0 ? 'Actif' : 'Nouveau'
            }));
        // Get recent quotes
        const recentQuotes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].quote.findMany({
            where: {
                user: {
                    commercialId: user.id
                }
            },
            include: {
                user: true,
                items: true
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: 5
        });
        // Format recent quotes data
        const formattedRecentQuotes = recentQuotes.map((quote)=>{
            const total = quote.items.reduce((sum, item)=>{
                return sum + item.quantity * item.unitPrice;
            }, 0);
            return {
                id: quote.id,
                clientName: `${quote.user.firstname} ${quote.user.lastname}`,
                company: quote.user.company,
                total: total,
                status: quote.status,
                createdAt: quote.createdAt.toLocaleDateString('fr-FR')
            };
        });
        // Mock upcoming meetings (you can implement a meetings table later)
        const upcomingMeetings = 3;
        const dashboardData = {
            totalClients,
            newClients,
            totalSales,
            monthlySales,
            pendingOrders,
            upcomingMeetings,
            recentClients: formattedRecentClients,
            recentQuotes: formattedRecentQuotes
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(dashboardData);
    } catch (error) {
        console.error('Error fetching commercial dashboard data:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur lors de la récupération des données du tableau de bord'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b6df5056._.js.map