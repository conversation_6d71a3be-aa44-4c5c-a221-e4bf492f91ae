import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Category } from '../../types';
import ApiService from '../../services/api';

const CategoriesScreen: React.FC = () => {
  const navigation = useNavigation();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setIsLoading(true);
      const categoriesData = await ApiService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getSubcategoriesCount = (categoryId: string) => {
    return categories.filter(cat => cat.parentId === categoryId).length;
  };

  const getProductsCount = (categoryId: string) => {
    // This would need to be implemented in the API
    // For now, return a placeholder
    return Math.floor(Math.random() * 50) + 1;
  };

  const renderCategory = ({ item }: { item: Category }) => {
    const subcategoriesCount = getSubcategoriesCount(item.id);
    const productsCount = getProductsCount(item.id);
    const hasSubcategories = subcategoriesCount > 0;

    return (
      <TouchableOpacity
        style={styles.categoryCard}
        onPress={() => {
          if (hasSubcategories) {
            // Navigate to subcategories
            navigation.navigate('Categories', { parentId: item.id, parentName: item.name });
          } else {
            // Navigate to products in this category
            navigation.navigate('CategoryProducts', { 
              categoryId: item.id, 
              categoryName: item.name 
            });
          }
        }}
      >
        <View style={styles.categoryIcon}>
          <Ionicons 
            name={hasSubcategories ? "folder" : "grid"} 
            size={32} 
            color="#006db7" 
          />
        </View>
        
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{item.name}</Text>
          {item.description && (
            <Text style={styles.categoryDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
          
          <View style={styles.categoryStats}>
            {hasSubcategories ? (
              <View style={styles.statItem}>
                <Ionicons name="folder-outline" size={16} color="#666" />
                <Text style={styles.statText}>
                  {subcategoriesCount} sous-catégorie{subcategoriesCount > 1 ? 's' : ''}
                </Text>
              </View>
            ) : (
              <View style={styles.statItem}>
                <Ionicons name="cube-outline" size={16} color="#666" />
                <Text style={styles.statText}>
                  {productsCount} produit{productsCount > 1 ? 's' : ''}
                </Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.categoryAction}>
          <Ionicons name="chevron-forward" size={20} color="#666" />
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="folder-open-outline" size={64} color="#ccc" />
      <Text style={styles.emptyText}>Aucune catégorie trouvée</Text>
      <Text style={styles.emptySubtext}>
        {searchQuery ? 'Essayez un autre terme de recherche' : 'Les catégories apparaîtront ici'}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Search Header */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher une catégorie..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories List */}
      <FlatList
        data={filteredCategories.filter(cat => !cat.parentId)} // Only show root categories
        renderItem={renderCategory}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />

      {/* Stats Footer */}
      <View style={styles.footer}>
        <View style={styles.footerStat}>
          <Text style={styles.footerNumber}>{filteredCategories.length}</Text>
          <Text style={styles.footerLabel}>Catégories</Text>
        </View>
        <View style={styles.footerSeparator} />
        <View style={styles.footerStat}>
          <Text style={styles.footerNumber}>
            {filteredCategories.reduce((total, cat) => total + getProductsCount(cat.id), 0)}
          </Text>
          <Text style={styles.footerLabel}>Produits</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  clearButton: {
    marginLeft: 8,
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  categoryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  categoryStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  categoryAction: {
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingVertical: 16,
  },
  footerStat: {
    flex: 1,
    alignItems: 'center',
  },
  footerNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#006db7',
  },
  footerLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  footerSeparator: {
    width: 1,
    backgroundColor: '#eee',
    marginHorizontal: 16,
  },
});

export default CategoriesScreen;
