import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Quote, QuoteStatus, RootStackParamList, UserRole } from '../../types';
import { useAuth } from '../../contexts/AuthContext';
import ApiService from '../../services/api';

type QuoteDetailRouteProp = RouteProp<RootStackParamList, 'QuoteDetail'>;

const QuoteDetailScreen: React.FC = () => {
  const route = useRoute<QuoteDetailRouteProp>();
  const navigation = useNavigation();
  const { user } = useAuth();
  const { quoteId } = route.params;

  const [quote, setQuote] = useState<Quote | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadQuote();
  }, [quoteId]);

  const loadQuote = async () => {
    try {
      setIsLoading(true);
      const quoteData = await ApiService.getQuote(quoteId);
      setQuote(quoteData);
    } catch (error) {
      console.error('Error loading quote:', error);
      Alert.alert('Erreur', 'Impossible de charger le devis');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: QuoteStatus) => {
    switch (status) {
      case QuoteStatus.PENDING:
        return '#FF9800';
      case QuoteStatus.APPROVED:
        return '#4CAF50';
      case QuoteStatus.REJECTED:
        return '#F44336';
      case QuoteStatus.EXPIRED:
        return '#9E9E9E';
      default:
        return '#666';
    }
  };

  const getStatusText = (status: QuoteStatus) => {
    switch (status) {
      case QuoteStatus.PENDING:
        return 'En attente';
      case QuoteStatus.APPROVED:
        return 'Approuvé';
      case QuoteStatus.REJECTED:
        return 'Rejeté';
      case QuoteStatus.EXPIRED:
        return 'Expiré';
      default:
        return status;
    }
  };

  const calculateTotal = () => {
    if (!quote?.items) return 0;
    return quote.items.reduce((total, item) => total + (item.totalPrice || 0), 0);
  };

  const handleShare = async () => {
    if (!quote) return;

    try {
      const message = `Devis #${quote.id.slice(-8)}\n` +
                     `Client: ${quote.client?.user?.firstname} ${quote.client?.user?.lastname}\n` +
                     `Statut: ${getStatusText(quote.status)}\n` +
                     `Total: ${calculateTotal().toFixed(2)} €\n` +
                     `Valide jusqu'au: ${new Date(quote.validUntil).toLocaleDateString('fr-FR')}`;

      await Share.share({
        message,
        title: `Devis Moonelec #${quote.id.slice(-8)}`,
      });
    } catch (error) {
      console.error('Error sharing quote:', error);
    }
  };

  const handleExportPDF = () => {
    Alert.alert(
      'Export PDF',
      'Cette fonctionnalité sera bientôt disponible',
      [{ text: 'OK' }]
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#006db7" />
        <Text style={styles.loadingText}>Chargement du devis...</Text>
      </View>
    );
  }

  if (!quote) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color="#ccc" />
        <Text style={styles.errorText}>Devis non trouvé</Text>
      </View>
    );
  }

  const isExpired = new Date(quote.validUntil) < new Date();

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.quoteId}>Devis #{quote.id.slice(-8)}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(quote.status) }]}>
              <Text style={styles.statusText}>{getStatusText(quote.status)}</Text>
            </View>
          </View>

          <View style={styles.headerInfo}>
            <Text style={styles.createdDate}>
              Créé le {new Date(quote.createdAt).toLocaleDateString('fr-FR')}
            </Text>
            <Text style={[styles.validUntil, isExpired && styles.expired]}>
              {isExpired ? 'Expiré le' : 'Valide jusqu\'au'} {new Date(quote.validUntil).toLocaleDateString('fr-FR')}
            </Text>
          </View>
        </View>

        {/* Client Information */}
        {quote.client && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations client</Text>
            <View style={styles.clientInfo}>
              <View style={styles.clientRow}>
                <Ionicons name="person-outline" size={20} color="#666" />
                <Text style={styles.clientText}>
                  {quote.client.user?.firstname} {quote.client.user?.lastname}
                </Text>
              </View>
              {quote.client.user?.email && (
                <View style={styles.clientRow}>
                  <Ionicons name="mail-outline" size={20} color="#666" />
                  <Text style={styles.clientText}>{quote.client.user.email}</Text>
                </View>
              )}
              {quote.client.user?.telephone && (
                <View style={styles.clientRow}>
                  <Ionicons name="call-outline" size={20} color="#666" />
                  <Text style={styles.clientText}>{quote.client.user.telephone}</Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Quote Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Articles ({quote.items?.length || 0})</Text>
          {quote.items && quote.items.length > 0 ? (
            quote.items.map((item, index) => (
              <View key={index} style={styles.quoteItem}>
                <View style={styles.itemInfo}>
                  {item.product.mainImage ? (
                    <Image source={{ uri: item.product.mainImage }} style={styles.itemImage} />
                  ) : (
                    <View style={styles.itemImagePlaceholder}>
                      <Ionicons name="image-outline" size={24} color="#ccc" />
                    </View>
                  )}

                  <View style={styles.itemDetails}>
                    <Text style={styles.itemName} numberOfLines={2}>
                      {item.product.name}
                    </Text>
                    <Text style={styles.itemReference}>Réf: {item.product.reference}</Text>
                    <Text style={styles.itemQuantity}>Quantité: {item.quantity}</Text>
                  </View>
                </View>

                <View style={styles.itemPricing}>
                  <Text style={styles.unitPrice}>{item.unitPrice?.toFixed(2) || '0.00'} €</Text>
                  <Text style={styles.totalPrice}>{item.totalPrice?.toFixed(2) || '0.00'} €</Text>
                </View>
              </View>
            ))
          ) : (
            <View style={styles.emptyItems}>
              <Ionicons name="document-text-outline" size={48} color="#ccc" />
              <Text style={styles.emptyText}>Aucun article</Text>
            </View>
          )}
        </View>

        {/* Notes */}
        {quote.notes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <Text style={styles.notesText}>{quote.notes}</Text>
          </View>
        )}

        {/* Total */}
        <View style={styles.totalSection}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Total HT</Text>
            <Text style={styles.totalAmount}>{calculateTotal().toFixed(2)} €</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>TVA (20%)</Text>
            <Text style={styles.totalAmount}>{(calculateTotal() * 0.2).toFixed(2)} €</Text>
          </View>
          <View style={[styles.totalRow, styles.totalFinal]}>
            <Text style={styles.totalLabelFinal}>Total TTC</Text>
            <Text style={styles.totalAmountFinal}>{(calculateTotal() * 1.2).toFixed(2)} €</Text>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={20} color="#006db7" />
          <Text style={styles.actionButtonText}>Partager</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleExportPDF}>
          <Ionicons name="download-outline" size={20} color="#006db7" />
          <Text style={styles.actionButtonText}>PDF</Text>
        </TouchableOpacity>

        {user?.role === UserRole.ADMIN && quote.status === QuoteStatus.PENDING && (
          <TouchableOpacity style={styles.primaryButton}>
            <Ionicons name="checkmark" size={20} color="#fff" />
            <Text style={styles.primaryButtonText}>Approuver</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  quoteId: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerInfo: {
    gap: 4,
  },
  createdDate: {
    fontSize: 14,
    color: '#666',
  },
  validUntil: {
    fontSize: 14,
    color: '#666',
  },
  expired: {
    color: '#F44336',
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  clientInfo: {
    gap: 12,
  },
  clientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  clientText: {
    fontSize: 16,
    color: '#333',
  },
  quoteItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemInfo: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  itemImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  itemReference: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  itemQuantity: {
    fontSize: 14,
    color: '#006db7',
    fontWeight: '600',
  },
  itemPricing: {
    alignItems: 'flex-end',
  },
  unitPrice: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  totalPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  emptyItems: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  notesText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  totalSection: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  totalFinal: {
    borderTopWidth: 2,
    borderTopColor: '#006db7',
    marginTop: 8,
    paddingTop: 16,
  },
  totalLabel: {
    fontSize: 16,
    color: '#666',
  },
  totalAmount: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  totalLabelFinal: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalAmountFinal: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#006db7',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#006db7',
    backgroundColor: '#fff',
    gap: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#006db7',
  },
  primaryButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#006db7',
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
});

export default QuoteDetailScreen;
