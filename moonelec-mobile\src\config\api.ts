// API Configuration
// Update this URL to match your web app's API endpoint
export const API_BASE_URL = 'http://192.168.11.110:3000/api';

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  // Remove leading slash if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${API_BASE_URL}/${cleanEndpoint}`;
};

// Common API endpoints
export const API_ENDPOINTS = {
  // Auth
  AUTH_MOBILE: '/auth/mobile',
  AUTH_SESSION: '/auth/session',
  AUTH_SIGNOUT: '/auth/signout',

  // Products
  PRODUCTS: '/products',
  PRODUCT_BY_ID: (id: string) => `/products/${id}`,

  // Categories
  CATEGORIES: '/categories',
  CATEGORY_BY_ID: (id: string) => `/categories/${id}`,

  // Brands
  BRANDS: '/brands',

  // Quotes
  QUOTES: '/quotes',
  QUOTE_BY_ID: (id: string) => `/quotes/${id}`,

  // Sales Reports
  SALES_REPORTS: '/sales-reports',
  SALES_REPORT_BY_ID: (id: string) => `/sales-reports/${id}`,

  // Chat
  CHAT_CONVERSATIONS: '/chat/conversations',
  CHAT_MESSAGES: '/chat/messages',
  CHAT_USERS: '/chat/users',

  // Upload
  UPLOAD: '/upload',
} as const;
