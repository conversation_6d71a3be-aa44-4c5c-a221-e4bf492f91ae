// API Configuration
// Dynamic API URL configuration for different environments

// IMPORTANT: Update this IP address to match your computer's network IP
// Find your IP with: ipconfig (Windows) or ifconfig (Mac/Linux)
const DEVELOPMENT_IP = '***********'; // Replace with your actual IP from terminal output

// IMPORTANT: Update this port to match your Next.js server port
const DEVELOPMENT_PORT = '3000'; // Corrected to match actual server port

// Get the local IP address for development
const getLocalIP = () => {
  // For production, use your deployed API URL
  const PRODUCTION_URL = 'https://your-production-api.com/api';

  // Check if we're in development or production
  const isDevelopment = __DEV__;

  if (isDevelopment) {
    // For Android emulator, use ******** to access localhost
    // For iOS simulator, localhost works
    // For physical devices, use your computer's IP address
    return `http://${DEVELOPMENT_IP}:${DEVELOPMENT_PORT}/api`;
  } else {
    return PRODUCTION_URL;
  }
};

export const API_BASE_URL = getLocalIP();

// Alternative URLs for different scenarios
export const API_URLS = {
  // For Android emulator (maps to host machine's localhost)
  ANDROID_EMULATOR: `http://********:${DEVELOPMENT_PORT}/api`,

  // For iOS simulator (localhost works)
  IOS_SIMULATOR: `http://localhost:${DEVELOPMENT_PORT}/api`,

  // For physical devices (replace with your computer's IP)
  PHYSICAL_DEVICE: `http://${DEVELOPMENT_IP}:${DEVELOPMENT_PORT}/api`,

  // For production
  PRODUCTION: 'https://your-production-api.com/api',
};

// Function to get the appropriate API URL based on platform and environment
export const getApiUrl = (): string => {
  if (__DEV__) {
    // Development environment
    try {
      const Platform = require('react-native').Platform;

      if (Platform.OS === 'android') {
        // For Android emulator, use ******** to access host machine's localhost
        console.log('🤖 Android detected, using emulator URL');
        return API_URLS.ANDROID_EMULATOR;
      } else if (Platform.OS === 'ios') {
        // For iOS simulator, localhost works
        console.log('🍎 iOS detected, using simulator URL');
        return API_URLS.IOS_SIMULATOR;
      }

      console.log('📱 Unknown platform, using physical device URL');
      return API_URLS.PHYSICAL_DEVICE; // Default fallback
    } catch (error) {
      console.warn('Could not detect platform, using Android emulator URL as fallback');
      return API_URLS.ANDROID_EMULATOR; // Default to Android emulator for safety
    }
  } else {
    // Production environment
    return API_URLS.PRODUCTION;
  }
};

// Function to test multiple API URLs and return the working one
export const findWorkingApiUrl = async (): Promise<string> => {
  const urlsToTest = [
    API_URLS.ANDROID_EMULATOR,
    API_URLS.IOS_SIMULATOR,
    API_URLS.PHYSICAL_DEVICE,
  ];

  for (const url of urlsToTest) {
    try {
      console.log(`🧪 Testing API URL: ${url}`);
      const response = await fetch(`${url}/test`, {
        method: 'GET',
        timeout: 5000,
      });

      if (response.ok) {
        console.log(`✅ Working API URL found: ${url}`);
        return url;
      }
    } catch (error) {
      console.log(`❌ Failed to connect to: ${url}`);
    }
  }

  console.warn('⚠️ No working API URL found, using default');
  return getApiUrl();
};

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  // Remove leading slash if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${API_BASE_URL}/${cleanEndpoint}`;
};

// Common API endpoints
export const API_ENDPOINTS = {
  // Auth
  AUTH_MOBILE: '/auth/mobile',
  AUTH_SESSION: '/auth/session',
  AUTH_SIGNOUT: '/auth/signout',

  // Products
  PRODUCTS: '/products',
  PRODUCT_BY_ID: (id: string) => `/products/${id}`,

  // Categories
  CATEGORIES: '/categories',
  CATEGORY_BY_ID: (id: string) => `/categories/${id}`,

  // Brands
  BRANDS: '/brands',

  // Quotes
  QUOTES: '/quotes',
  QUOTE_BY_ID: (id: string) => `/quotes/${id}`,

  // Sales Reports
  SALES_REPORTS: '/sales-reports',
  SALES_REPORT_BY_ID: (id: string) => `/sales-reports/${id}`,

  // Chat
  CHAT_CONVERSATIONS: '/chat/conversations',
  CHAT_MESSAGES: '/chat/messages',
  CHAT_USERS: '/chat/users',

  // Upload
  UPLOAD: '/upload',
} as const;
