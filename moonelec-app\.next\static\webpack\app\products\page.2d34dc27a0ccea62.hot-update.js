"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/components/products/ModernProductsPage.tsx":
/*!********************************************************!*\
  !*** ./src/components/products/ModernProductsPage.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronDown,FaEye,FaFilter,FaHeart,FaList,FaSearch,FaShoppingCart,FaStar,FaTh,FaTimes!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ModernProductsPage(param) {\n    let { initialProducts, categories, brands, searchParams } = param;\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProducts);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        10000\n    ]);\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedBrands, setSelectedBrands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchParams.search || '');\n    const sortOptions = [\n        {\n            value: 'name',\n            label: 'Nom A-Z'\n        },\n        {\n            value: 'name-desc',\n            label: 'Nom Z-A'\n        },\n        {\n            value: 'price',\n            label: 'Prix croissant'\n        },\n        {\n            value: 'price-desc',\n            label: 'Prix décroissant'\n        },\n        {\n            value: 'newest',\n            label: 'Plus récents'\n        },\n        {\n            value: 'rating',\n            label: 'Mieux notés'\n        }\n    ];\n    const ProductCard = (param)=>{\n        let { product } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            layout: true,\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -20\n            },\n            className: \"group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-square bg-light-gray overflow-hidden\",\n                    children: [\n                        product.mainImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: product.mainImage,\n                            alt: product.name,\n                            fill: true,\n                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center bg-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-gray-300 rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 left-3 flex flex-col space-y-2\",\n                            children: [\n                                product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-electric-blue text-white text-xs font-semibold rounded\",\n                                    children: \"NOUVEAU\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                product.isOnSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-moonelec-red text-white text-xs font-semibold rounded\",\n                                    children: \"PROMO\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-moonelec-red rounded-full shadow-md transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/\".concat(product.id),\n                                    className: \"p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-electric-blue rounded-full shadow-md transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEye, {\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-x-0 bottom-0 p-4 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full btn btn-primary text-sm py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaShoppingCart, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Ajouter au Panier\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-xs text-gray-500 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gray-100 px-2 py-1 rounded\",\n                                    children: product.category.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: product.brand.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-charcoal mb-2 line-clamp-2 group-hover:text-moonelec-red transition-colors\",\n                            children: product.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mb-2\",\n                            children: [\n                                \"R\\xe9f: \",\n                                product.reference\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 9\n                        }, this),\n                        product.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 mb-3\",\n                            children: [\n                                [\n                                    ...Array(5)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaStar, {\n                                        className: \"text-xs \".concat(i < product.rating ? 'text-yellow-400' : 'text-gray-300')\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        product.rating,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-bold text-moonelec-red\",\n                                        children: [\n                                            product.price.toLocaleString('fr-FR'),\n                                            \" MAD\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 line-through\",\n                                        children: [\n                                            product.originalPrice.toLocaleString('fr-FR'),\n                                            \" MAD\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n            lineNumber: 79,\n            columnNumber: 5\n        }, this);\n    };\n    const ProductListItem = (param)=>{\n        let { product } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            layout: true,\n            initial: {\n                opacity: 0,\n                x: -20\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            },\n            exit: {\n                opacity: 0,\n                x: 20\n            },\n            className: \"group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-48 h-48 bg-light-gray flex-shrink-0\",\n                        children: product.mainImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: product.mainImage,\n                            alt: product.name,\n                            fill: true,\n                            className: \"object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center bg-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-gray-300 rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-gray-100 px-2 py-1 rounded\",\n                                                    children: product.category.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: product.brand.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-charcoal mb-2 group-hover:text-moonelec-red transition-colors\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mb-2\",\n                                            children: [\n                                                \"R\\xe9f: \",\n                                                product.reference\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1 mb-4\",\n                                            children: [\n                                                [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaStar, {\n                                                        className: \"text-sm \".concat(i < product.rating ? 'text-yellow-400' : 'text-gray-300')\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.rating,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold text-moonelec-red\",\n                                                    children: [\n                                                        product.price.toLocaleString('fr-FR'),\n                                                        \" MAD\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 line-through\",\n                                                    children: [\n                                                        product.originalPrice.toLocaleString('fr-FR'),\n                                                        \" MAD\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-2 border border-gray-200 hover:border-moonelec-red text-gray-600 hover:text-moonelec-red rounded-lg transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products/\".concat(product.id),\n                                                    className: \"p-2 border border-gray-200 hover:border-electric-blue text-gray-600 hover:text-electric-blue rounded-lg transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEye, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn btn-primary px-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaShoppingCart, {\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Ajouter\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n            lineNumber: 186,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-light-gray\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Rechercher des produits...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-12 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"appearance-none bg-white border border-gray-200 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-electric-blue\",\n                                                    children: sortOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: option.value,\n                                                            children: option.label\n                                                        }, option.value, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronDown, {\n                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewMode('grid'),\n                                                    className: \"p-2 \".concat(viewMode === 'grid' ? 'bg-electric-blue text-white' : 'bg-white text-gray-600 hover:bg-gray-50', \" transition-colors\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTh, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewMode('list'),\n                                                    className: \"p-2 \".concat(viewMode === 'list' ? 'bg-electric-blue text-white' : 'bg-white text-gray-600 hover:bg-gray-50', \" transition-colors\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowFilters(!showFilters),\n                                            className: \"btn btn-secondary flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaFilter, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Filtres\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        (selectedCategories.length > 0 || selectedBrands.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap gap-2\",\n                            children: [\n                                selectedCategories.map((categoryId)=>{\n                                    const category = categories.find((c)=>c.id === categoryId);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center space-x-2 bg-electric-blue/10 text-electric-blue px-3 py-1 rounded-full text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category === null || category === void 0 ? void 0 : category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedCategories((prev)=>prev.filter((id)=>id !== categoryId)),\n                                                className: \"hover:text-electric-blue-dark\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTimes, {\n                                                    className: \"text-xs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, categoryId, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 19\n                                    }, this);\n                                }),\n                                selectedBrands.map((brandId)=>{\n                                    const brand = brands.find((b)=>b.id === brandId);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center space-x-2 bg-moonelec-red/10 text-moonelec-red px-3 py-1 rounded-full text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: brand === null || brand === void 0 ? void 0 : brand.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedBrands((prev)=>prev.filter((id)=>id !== brandId)),\n                                                className: \"hover:text-moonelec-red-dark\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTimes, {\n                                                    className: \"text-xs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, brandId, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                            children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -300\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -300\n                                },\n                                className: \"w-80 bg-white rounded-xl shadow-lg p-6 h-fit sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-charcoal mb-6\",\n                                        children: \"Filtres\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-charcoal mb-3\",\n                                                children: \"Cat\\xe9gories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedCategories.includes(category.id),\n                                                                onChange: (e)=>{\n                                                                    if (e.target.checked) {\n                                                                        setSelectedCategories((prev)=>[\n                                                                                ...prev,\n                                                                                category.id\n                                                                            ]);\n                                                                    } else {\n                                                                        setSelectedCategories((prev)=>prev.filter((id)=>id !== category.id));\n                                                                    }\n                                                                },\n                                                                className: \"rounded border-gray-300 text-electric-blue focus:ring-electric-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, category.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-charcoal mb-3\",\n                                                children: \"Marques\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedBrands.includes(brand.id),\n                                                                onChange: (e)=>{\n                                                                    if (e.target.checked) {\n                                                                        setSelectedBrands((prev)=>[\n                                                                                ...prev,\n                                                                                brand.id\n                                                                            ]);\n                                                                    } else {\n                                                                        setSelectedBrands((prev)=>prev.filter((id)=>id !== brand.id));\n                                                                    }\n                                                                },\n                                                                className: \"rounded border-gray-300 text-moonelec-red focus:ring-moonelec-red\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: brand.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, brand.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSelectedCategories([]);\n                                            setSelectedBrands([]);\n                                            setPriceRange([\n                                                0,\n                                                10000\n                                            ]);\n                                        },\n                                        className: \"w-full btn btn-secondary text-sm\",\n                                        children: \"Effacer les Filtres\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        ...Array(12)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-4 animate-pulse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-200 rounded-lg mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 bg-gray-200 rounded mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-3 bg-gray-200 rounded mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-6',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                        children: products.map((product)=>viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                                product: product\n                                            }, product.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductListItem, {\n                                                product: product\n                                            }, product.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this),\n                                !isLoading && products.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                                className: \"text-gray-400 text-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-charcoal mb-2\",\n                                            children: \"Aucun produit trouv\\xe9\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Essayez de modifier vos crit\\xe8res de recherche\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernProductsPage, \"BwW6TbPeIH4b+5LWE7d7zrUiHFk=\");\n_c = ModernProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ModernProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/ModernProductsPage.tsx\n"));

/***/ })

});