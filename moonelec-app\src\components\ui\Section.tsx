'use client';

import { ReactNode, useRef, forwardRef, useState, useEffect } from 'react';

interface SectionProps {
  children: ReactNode;
  id?: string;
  className?: string;
  title?: string;
  subtitle?: string;
  accent?: boolean;
  titleHighlight?: string;
}

const Section = forwardRef<HTMLElement, SectionProps>(function Section(
  {
    children,
    id,
    className = '',
    title,
    subtitle,
    accent = false,
    titleHighlight
  },
  forwardedRef
) {
  const localRef = useRef(null);
  const ref = forwardedRef || localRef;
  const [isInView, setIsInView] = useState(false);

  // Utiliser IntersectionObserver au lieu de useInView
  useEffect(() => {
    if (ref && 'current' in ref && ref.current && typeof IntersectionObserver !== 'undefined') {
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.2 }
      );

      observer.observe(ref.current);
      return () => observer.disconnect();
    } else {
      // Fallback si IntersectionObserver n'est pas disponible
      setIsInView(true);
    }
  }, [ref]);

  const baseStyles = 'py-16 md:py-24';
  const accentStyles = accent ? 'bg-accent dark:bg-[#111]' : '';

  return (
    <section
      id={id}
      className={`${baseStyles} ${accentStyles} ${className}`}
      ref={ref}
    >
      <div className="container mx-auto px-4">
        {(title || subtitle) && (
          <div
            className={`text-center mb-16 transition-all duration-600 ${isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}
          >
            {title && (
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                {titleHighlight ? (
                  <>
                    {title.split(titleHighlight)[0]}
                    <span className="text-primary">{titleHighlight}</span>
                    {title.split(titleHighlight)[1]}
                  </>
                ) : (
                  title
                )}
              </h2>
            )}
            <div className="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            {subtitle && (
              <p className="text-text-secondary max-w-3xl mx-auto">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {children}
      </div>
    </section>
  );
});

export default Section;
