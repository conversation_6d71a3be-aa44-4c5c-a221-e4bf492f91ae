import { NextRequest, NextResponse } from 'next/server';
import { getBrandById, updateBrand, deleteBrand } from '@/lib/brands';

// GET /api/brands/[id] - Get a single brand by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const searchParams = req.nextUrl.searchParams;
    const includeProducts = searchParams.get('includeProducts') === 'true';

    const brand = await getBrandById(id, includeProducts);

    if (!brand) {
      return NextResponse.json(
        { error: 'Brand not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(brand);
  } catch (error: any) {
    console.error('Error fetching brand:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch brand' },
      { status: 500 }
    );
  }
}

// PATCH /api/brands/[id] - Update a brand
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await req.json();
    const { name, image } = body;

    // Validate that at least one field is provided
    if (!name && !image) {
      return NextResponse.json(
        { error: 'At least one field must be provided for update' },
        { status: 400 }
      );
    }

    // Update the brand
    const brand = await updateBrand(id, {
      name,
      image,
    });

    return NextResponse.json(brand);
  } catch (error: any) {
    console.error('Error updating brand:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update brand' },
      { status: 500 }
    );
  }
}

// DELETE /api/brands/[id] - Delete a brand
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    await deleteBrand(id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting brand:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete brand' },
      { status: 500 }
    );
  }
}
