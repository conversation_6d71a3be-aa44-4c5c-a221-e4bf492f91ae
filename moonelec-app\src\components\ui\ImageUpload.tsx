'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaUpload, FaImage, FaTrash, FaSpinner } from 'react-icons/fa';
import { useFileUpload } from '@/hooks/useFileUpload';

interface ImageUploadProps {
  initialImage?: string;
  onImageChange: (imageUrl: string | null) => void;
  directory?: string;
  className?: string;
  aspectRatio?: 'square' | '16/9' | '4/3' | 'auto';
  maxSizeMB?: number;
  label?: string;
  required?: boolean;
}

export default function ImageUpload({
  initialImage,
  onImageChange,
  directory = 'uploads',
  className = '',
  aspectRatio = 'square',
  maxSizeMB = 5,
  label = 'Image',
  required = false,
}: ImageUploadProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(initialImage || null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadFile, isUploading, progress } = useFileUpload();

  const aspectRatioClass = {
    square: 'aspect-square',
    '16/9': 'aspect-video',
    '4/3': 'aspect-[4/3]',
    'auto': '',
  }[aspectRatio];

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSizeMB) {
      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);
      return;
    }
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('Veuillez sélectionner un fichier image');
      return;
    }
    
    setError(null);
    
    try {
      const url = await uploadFile(file, {
        directory,
        onSuccess: (url) => {
          setImageUrl(url);
          onImageChange(url);
        },
        onError: (err) => {
          setError(err.message);
        },
      });
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleRemoveImage = () => {
    setImageUrl(null);
    onImageChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    
    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSizeMB) {
      setError(`La taille du fichier dépasse la limite de ${maxSizeMB} MB`);
      return;
    }
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('Veuillez sélectionner un fichier image');
      return;
    }
    
    setError(null);
    
    try {
      const url = await uploadFile(file, {
        directory,
        onSuccess: (url) => {
          setImageUrl(url);
          onImageChange(url);
        },
        onError: (err) => {
          setError(err.message);
        },
      });
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div
        className={`relative border-2 border-dashed rounded-lg ${
          error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
        } ${aspectRatioClass} overflow-hidden`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        {imageUrl ? (
          <div className="relative w-full h-full">
            <Image
              src={imageUrl}
              alt="Uploaded image"
              fill
              style={{ objectFit: 'cover' }}
              className="w-full h-full"
            />
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
              <motion.button
                type="button"
                onClick={handleRemoveImage}
                className="p-2 bg-red-600 text-white rounded-full"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <FaTrash />
              </motion.button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-6 h-full">
            {isUploading ? (
              <div className="text-center">
                <FaSpinner className="animate-spin text-3xl text-primary mx-auto mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Téléchargement en cours... {Math.round(progress)}%
                </p>
              </div>
            ) : (
              <>
                <FaImage className="text-3xl text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-2">
                  Glissez-déposez une image ici ou cliquez pour sélectionner
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500 text-center">
                  Formats acceptés: JPG, PNG, GIF, WebP (max {maxSizeMB} MB)
                </p>
              </>
            )}
          </div>
        )}
        
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={isUploading}
        />
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
}
