{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/products.ts"], "sourcesContent": ["import { prisma } from './prisma';\n\n// Get all products with optional filtering\nexport async function getProducts(options?: {\n  categoryId?: string;\n  brandId?: string;\n  search?: string;\n  skip?: number;\n  take?: number;\n}) {\n  const { categoryId, brandId, search, skip = 0, take = 50 } = options || {};\n\n  const where = {\n    ...(categoryId ? { categoryId } : {}),\n    ...(brandId ? { brandId } : {}),\n    ...(search\n      ? {\n          OR: [\n            { name: { contains: search } },\n            { reference: { contains: search } },\n            { description: { contains: search } },\n          ],\n        }\n      : {}),\n  };\n\n  const [products, total] = await Promise.all([\n    prisma.product.findMany({\n      where,\n      include: {\n        category: true,\n        brand: true,\n        productimage: {\n          orderBy: {\n            order: 'asc',\n          },\n        },\n      },\n      skip,\n      take,\n      orderBy: {\n        createdAt: 'desc',\n      },\n    }),\n    prisma.product.count({ where }),\n  ]);\n\n  return { products, total };\n}\n\n// Get a single product by ID\nexport async function getProductById(id: string) {\n  return prisma.product.findUnique({\n    where: { id },\n    include: {\n      category: true,\n      brand: true,\n      productimage: {\n        orderBy: {\n          order: 'asc',\n        },\n      },\n    },\n  });\n}\n\n// Get a single product by reference\nexport async function getProductByReference(reference: string) {\n  return prisma.product.findUnique({\n    where: { reference },\n    include: {\n      category: true,\n      brand: true,\n      productimage: {\n        orderBy: {\n          order: 'asc',\n        },\n      },\n    },\n  });\n}\n\n// Create a new product\nexport async function createProduct(data: {\n  reference: string;\n  name: string;\n  description: string;\n  characteristics: Record<string, any>;\n  mainImage?: string;\n  categoryId?: string;\n  brandId?: string;\n  images?: { url: string; alt?: string; order?: number }[];\n}) {\n  const { images, ...productData } = data;\n\n  return prisma.$transaction(async (tx) => {\n    // Check if reference already exists\n    const existingProduct = await tx.product.findUnique({\n      where: { reference: productData.reference },\n    });\n\n    if (existingProduct) {\n      throw new Error(`Un produit avec la référence ${productData.reference} existe déjà`);\n    }\n\n    // Create the product\n    const product = await tx.product.create({\n      data: {\n        id: crypto.randomUUID(),\n        reference: productData.reference,\n        name: productData.name,\n        description: productData.description,\n        characteristics: JSON.stringify(productData.characteristics),\n        mainImage: productData.mainImage,\n        categoryId: productData.categoryId,\n        brandId: productData.brandId,\n        updatedAt: new Date(),\n      },\n    });\n\n    // Add images if provided\n    if (images && images.length > 0) {\n      await tx.productimage.createMany({\n        data: images.map((image, index) => ({\n          id: crypto.randomUUID(),\n          url: image.url,\n          alt: image.alt || product.name,\n          order: image.order || index,\n          productId: product.id,\n        })),\n      });\n    }\n\n    return product;\n  });\n}\n\n// Update an existing product\nexport async function updateProduct(\n  id: string,\n  data: {\n    reference?: string;\n    name?: string;\n    description?: string;\n    characteristics?: Record<string, any>;\n    mainImage?: string;\n    categoryId?: string | null;\n    brandId?: string | null;\n  }\n) {\n  // If reference is being updated, check if it already exists\n  if (data.reference) {\n    const existingProduct = await prisma.product.findFirst({\n      where: {\n        reference: data.reference,\n        id: { not: id },\n      },\n    });\n\n    if (existingProduct) {\n      throw new Error(`Un produit avec la référence ${data.reference} existe déjà`);\n    }\n  }\n\n  return prisma.product.update({\n    where: { id },\n    data: {\n      ...data,\n      characteristics: data.characteristics ? JSON.stringify(data.characteristics) : undefined,\n      updatedAt: new Date(),\n    },\n    include: {\n      category: true,\n      brand: true,\n      productimage: {\n        orderBy: {\n          order: 'asc',\n        },\n      },\n    },\n  });\n}\n\n// Delete a product\nexport async function deleteProduct(id: string) {\n  return prisma.$transaction(async (tx) => {\n    // 1. Supprimer d'abord les références dans QuoteItem\n    await tx.quoteItem.deleteMany({\n      where: { productId: id },\n    });\n\n    // 2. Supprimer les références dans OrderItem\n    await tx.orderItem.deleteMany({\n      where: { productId: id },\n    });\n\n    // 3. Supprimer les images du produit\n    await tx.productimage.deleteMany({\n      where: { productId: id },\n    });\n\n    // 4. Enfin, supprimer le produit lui-même\n    return tx.product.delete({\n      where: { id },\n    });\n  });\n}\n\n// Add images to a product\nexport async function addProductImages(\n  productId: string,\n  images: { url: string; alt?: string; order?: number }[]\n) {\n  return prisma.productimage.createMany({\n    data: images.map((image, index) => ({\n      id: crypto.randomUUID(),\n      url: image.url,\n      alt: image.alt,\n      order: image.order || index,\n      productId,\n    })),\n  });\n}\n\n// Update a product image\nexport async function updateProductImage(\n  id: string,\n  data: {\n    url?: string;\n    alt?: string;\n    order?: number;\n  }\n) {\n  return prisma.productimage.update({\n    where: { id },\n    data,\n  });\n}\n\n// Delete a product image\nexport async function deleteProductImage(id: string) {\n  return prisma.productimage.delete({\n    where: { id },\n  });\n}\n\n// Delete all images for a product\nexport async function deleteAllProductImages(productId: string) {\n  return prisma.productimage.deleteMany({\n    where: { productId },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAGO,eAAe,YAAY,OAMjC;IACC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAEzE,MAAM,QAAQ;QACZ,GAAI,aAAa;YAAE;QAAW,IAAI,CAAC,CAAC;QACpC,GAAI,UAAU;YAAE;QAAQ,IAAI,CAAC,CAAC;QAC9B,GAAI,SACA;YACE,IAAI;gBACF;oBAAE,MAAM;wBAAE,UAAU;oBAAO;gBAAE;gBAC7B;oBAAE,WAAW;wBAAE,UAAU;oBAAO;gBAAE;gBAClC;oBAAE,aAAa;wBAAE,UAAU;oBAAO;gBAAE;aACrC;QACH,IACA,CAAC,CAAC;IACR;IAEA,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC1C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtB;YACA,SAAS;gBACP,UAAU;gBACV,OAAO;gBACP,cAAc;oBACZ,SAAS;wBACP,OAAO;oBACT;gBACF;YACF;YACA;YACA;YACA,SAAS;gBACP,WAAW;YACb;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE;QAAM;KAC9B;IAED,OAAO;QAAE;QAAU;IAAM;AAC3B;AAGO,eAAe,eAAe,EAAU;IAC7C,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC/B,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,UAAU;YACV,OAAO;YACP,cAAc;gBACZ,SAAS;oBACP,OAAO;gBACT;YACF;QACF;IACF;AACF;AAGO,eAAe,sBAAsB,SAAiB;IAC3D,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC/B,OAAO;YAAE;QAAU;QACnB,SAAS;YACP,UAAU;YACV,OAAO;YACP,cAAc;gBACZ,SAAS;oBACP,OAAO;gBACT;YACF;QACF;IACF;AACF;AAGO,eAAe,cAAc,IASnC;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,aAAa,GAAG;IAEnC,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,oCAAoC;QACpC,MAAM,kBAAkB,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;YAClD,OAAO;gBAAE,WAAW,YAAY,SAAS;YAAC;QAC5C;QAEA,IAAI,iBAAiB;YACnB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,YAAY,SAAS,CAAC,YAAY,CAAC;QACrF;QAEA,qBAAqB;QACrB,MAAM,UAAU,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ,IAAI,OAAO,UAAU;gBACrB,WAAW,YAAY,SAAS;gBAChC,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW;gBACpC,iBAAiB,KAAK,SAAS,CAAC,YAAY,eAAe;gBAC3D,WAAW,YAAY,SAAS;gBAChC,YAAY,YAAY,UAAU;gBAClC,SAAS,YAAY,OAAO;gBAC5B,WAAW,IAAI;YACjB;QACF;QAEA,yBAAyB;QACzB,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;YAC/B,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;gBAC/B,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;wBAClC,IAAI,OAAO,UAAU;wBACrB,KAAK,MAAM,GAAG;wBACd,KAAK,MAAM,GAAG,IAAI,QAAQ,IAAI;wBAC9B,OAAO,MAAM,KAAK,IAAI;wBACtB,WAAW,QAAQ,EAAE;oBACvB,CAAC;YACH;QACF;QAEA,OAAO;IACT;AACF;AAGO,eAAe,cACpB,EAAU,EACV,IAQC;IAED,4DAA4D;IAC5D,IAAI,KAAK,SAAS,EAAE;QAClB,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,WAAW,KAAK,SAAS;gBACzB,IAAI;oBAAE,KAAK;gBAAG;YAChB;QACF;QAEA,IAAI,iBAAiB;YACnB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK,SAAS,CAAC,YAAY,CAAC;QAC9E;IACF;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QAC3B,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,GAAG,IAAI;YACP,iBAAiB,KAAK,eAAe,GAAG,KAAK,SAAS,CAAC,KAAK,eAAe,IAAI;YAC/E,WAAW,IAAI;QACjB;QACA,SAAS;YACP,UAAU;YACV,OAAO;YACP,cAAc;gBACZ,SAAS;oBACP,OAAO;gBACT;YACF;QACF;IACF;AACF;AAGO,eAAe,cAAc,EAAU;IAC5C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,qDAAqD;QACrD,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;YAC5B,OAAO;gBAAE,WAAW;YAAG;QACzB;QAEA,6CAA6C;QAC7C,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;YAC5B,OAAO;gBAAE,WAAW;YAAG;QACzB;QAEA,qCAAqC;QACrC,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;YAC/B,OAAO;gBAAE,WAAW;YAAG;QACzB;QAEA,0CAA0C;QAC1C,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE;YAAG;QACd;IACF;AACF;AAGO,eAAe,iBACpB,SAAiB,EACjB,MAAuD;IAEvD,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;QACpC,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;gBAClC,IAAI,OAAO,UAAU;gBACrB,KAAK,MAAM,GAAG;gBACd,KAAK,MAAM,GAAG;gBACd,OAAO,MAAM,KAAK,IAAI;gBACtB;YACF,CAAC;IACH;AACF;AAGO,eAAe,mBACpB,EAAU,EACV,IAIC;IAED,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;QAChC,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAGO,eAAe,mBAAmB,EAAU;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;QAChC,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe,uBAAuB,SAAiB;IAC5D,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;QACpC,OAAO;YAAE;QAAU;IACrB;AACF", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      console.log('🔐 No valid authorization header found');\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n    console.log('🔐 Mobile token received (length):', token.length);\n    console.log('🔐 Mobile token full:', token);\n\n    // Try to verify JWT token with JWT_SECRET first, then fallback to NEXTAUTH_SECRET\n    let decoded: any;\n    try {\n      const jwtSecret = process.env.JWT_SECRET || 'fallback-secret';\n      console.log('🔐 Trying JWT_SECRET for token verification');\n      decoded = jwt.verify(token, jwtSecret) as any;\n      console.log('🔐 Token decoded successfully with JWT_SECRET:', { userId: decoded.userId, role: decoded.role });\n    } catch (jwtError) {\n      console.log('🔐 JWT_SECRET failed, trying NEXTAUTH_SECRET...');\n      try {\n        const nextAuthSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';\n        decoded = jwt.verify(token, nextAuthSecret) as any;\n        console.log('🔐 Token decoded successfully with NEXTAUTH_SECRET:', { userId: decoded.userId, role: decoded.role });\n      } catch (nextAuthError) {\n        console.error('🔐 Both JWT secrets failed:', { jwtError: jwtError.message, nextAuthError: nextAuthError.message });\n        throw new Error('Invalid token signature');\n      }\n    }\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n        isActive: true,\n      }\n    });\n\n    if (!user || !user.isActive) {\n      console.log('🔐 User not found or inactive:', { found: !!user, active: user?.isActive });\n      return null;\n    }\n\n    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n\n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') ||\n         userAgent.includes('Expo') ||\n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  // Always try to verify mobile token if Authorization header is present\n  const authHeader = request.headers.get('authorization');\n  if (authHeader?.startsWith('Bearer ')) {\n    return await verifyMobileToken(request);\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QACjE,QAAQ,GAAG,CAAC,sCAAsC,MAAM,MAAM;QAC9D,QAAQ,GAAG,CAAC,yBAAyB;QAErC,kFAAkF;QAClF,IAAI;QACJ,IAAI;YACF,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI;YAC5C,QAAQ,GAAG,CAAC;YACZ,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAC5B,QAAQ,GAAG,CAAC,kDAAkD;gBAAE,QAAQ,QAAQ,MAAM;gBAAE,MAAM,QAAQ,IAAI;YAAC;QAC7G,EAAE,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,eAAe,IAAI;gBACtD,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;gBAC5B,QAAQ,GAAG,CAAC,uDAAuD;oBAAE,QAAQ,QAAQ,MAAM;oBAAE,MAAM,QAAQ,IAAI;gBAAC;YAClH,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,+BAA+B;oBAAE,UAAU,SAAS,OAAO;oBAAE,eAAe,cAAc,OAAO;gBAAC;gBAChH,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,QAAQ,GAAG,CAAC,kCAAkC;gBAAE,OAAO,CAAC,CAAC;gBAAM,QAAQ,MAAM;YAAS;YACtF,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,8CAA8C;YAAE,IAAI,KAAK,EAAE;YAAE,MAAM,KAAK,IAAI;QAAC;QACzF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,uEAAuE;IACvE,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,YAAY,WAAW,YAAY;QACrC,OAAO,MAAM,kBAAkB;IACjC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { getProducts, createProduct } from '@/lib/products';\nimport { getMobileUserFromRequest } from '@/lib/mobile-auth';\nimport { authOptions } from '@/lib/auth-options';\n\n// GET /api/products - Get all products with optional filtering\nexport async function GET(req: NextRequest) {\n  try {\n    console.log('📦 Products API - Request received');\n\n    // Check for mobile authentication\n    const mobileUser = await getMobileUserFromRequest(req);\n    if (mobileUser) {\n      console.log('📱 Mobile user authenticated:', mobileUser.username, mobileUser.role);\n    } else {\n      console.log('📱 No mobile user found, allowing public access to products');\n    }\n\n    const searchParams = req.nextUrl.searchParams;\n    const categoryId = searchParams.get('categoryId') || undefined;\n    const brandId = searchParams.get('brandId') || undefined;\n    const search = searchParams.get('search') || undefined;\n    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;\n    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;\n\n    const { products, total } = await getProducts({\n      categoryId,\n      brandId,\n      search,\n      skip,\n      take,\n    });\n\n    return NextResponse.json({ products, total });\n  } catch (error: any) {\n    console.error('Error fetching products:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch products' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/products - Create a new product\nexport async function POST(req: NextRequest) {\n  try {\n    // Check for mobile authentication first\n    const mobileUser = await getMobileUserFromRequest(req);\n    const session = await getServerSession(authOptions);\n\n    if (!mobileUser && !session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Use mobile user data if available, otherwise use session\n    const user = mobileUser || session?.user;\n\n    if (!user || user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });\n    }\n\n    const body = await req.json();\n    const {\n      reference,\n      name,\n      description,\n      characteristics,\n      mainImage,\n      categoryId,\n      brandId,\n      images\n    } = body;\n\n    // Validate required fields\n    if (!reference || !name) {\n      return NextResponse.json(\n        { error: 'Reference and name are required' },\n        { status: 400 }\n      );\n    }\n\n    // Create the product\n    const product = await createProduct({\n      reference,\n      name,\n      description,\n      characteristics: characteristics || {},\n      mainImage,\n      categoryId,\n      brandId,\n      images: images || [],\n    });\n\n    return NextResponse.json(product, { status: 201 });\n  } catch (error: any) {\n    console.error('Error creating product:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to create product' },\n      { status: 500 }\n    );\n  }\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,iCAAiC,WAAW,QAAQ,EAAE,WAAW,IAAI;QACnF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY;QAC7C,MAAM,aAAa,aAAa,GAAG,CAAC,iBAAiB;QACrD,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAC/C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAE9E,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;YAC5C;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;YAAU;QAAM;IAC7C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA2B,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,wCAAwC;QACxC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,2DAA2D;QAC3D,MAAM,OAAO,cAAc,SAAS;QAEpC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuC,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EACJ,SAAS,EACT,IAAI,EACJ,WAAW,EACX,eAAe,EACf,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACP,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD,EAAE;YAClC;YACA;YACA;YACA,iBAAiB,mBAAmB,CAAC;YACrC;YACA;YACA;YACA,QAAQ,UAAU,EAAE;QACtB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YAAE,QAAQ;QAAI;IAClD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA2B,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}