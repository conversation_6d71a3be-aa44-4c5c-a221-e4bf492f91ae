import OpenAI from 'openai';

// Types for AI responses
export interface ExtractedProduct {
  productName: string;
  reference: string;
  description: string;
  characteristics: Record<string, string>;
}

// AI Service that supports OpenAI, Ollama, and embedded models
export class AIService {
  private openai?: OpenAI;
  private ollamaBaseUrl: string;
  private ollamaModel: string;
  private useEmbedded: boolean;
  private useOllama: boolean;
  private embeddedModel: any;

  constructor() {
    this.ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
    this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.2:3b';

    // Check if OpenAI is configured
    const openaiKey = process.env.OPENAI_API_KEY;
    const hasOpenAI = openaiKey && openaiKey !== '' && openaiKey !== 'your-openai-api-key-here';

    // Determine which AI service to use (priority: OpenAI > Embedded > Ollama)
    if (hasOpenAI) {
      this.useOllama = false;
      this.useEmbedded = false;
      this.openai = new OpenAI({
        apiKey: openaiKey,
      });
      console.log('🤖 AI Service initialized: OpenAI');
    } else {
      // Use embedded AI by default (no external dependencies)
      this.useOllama = false;
      this.useEmbedded = true;
      this.embeddedModel = null; // Will be loaded on first use
      console.log('🤖 AI Service initialized: Embedded AI (Transformers.js)');
    }
  }

  // Check if AI service is available
  async isAvailable(): Promise<{ available: boolean; service: string; error?: string }> {
    // Check for test mode
    if (process.env.NODE_ENV === 'development' && process.env.AI_TEST_MODE === 'true') {
      return { available: true, service: 'Test Mode (No AI)' };
    }

    // Check embedded AI first
    if (this.useEmbedded) {
      try {
        // Embedded AI is always available (no external dependencies)
        return { available: true, service: 'Embedded AI (Transformers.js)' };
      } catch (error) {
        return {
          available: false,
          service: 'Embedded AI',
          error: 'Failed to initialize embedded AI model'
        };
      }
    }

    if (this.useOllama) {
      try {
        const response = await fetch(`${this.ollamaBaseUrl}/api/tags`);
        if (response.ok) {
          const data = await response.json();
          const hasModel = data.models?.some((model: any) => model.name.includes(this.ollamaModel.split(':')[0]));

          if (!hasModel) {
            return {
              available: false,
              service: 'Ollama',
              error: `Model ${this.ollamaModel} not found. Please install it with: ollama pull ${this.ollamaModel}`
            };
          }

          return { available: true, service: 'Ollama' };
        } else {
          return {
            available: false,
            service: 'Ollama',
            error: 'Ollama server not running. Please start Ollama first.'
          };
        }
      } catch (error) {
        return {
          available: false,
          service: 'Ollama',
          error: 'Cannot connect to Ollama. Please install and start Ollama.'
        };
      }
    } else {
      try {
        await this.openai!.models.list();
        return { available: true, service: 'OpenAI' };
      } catch (error) {
        return {
          available: false,
          service: 'OpenAI',
          error: 'Invalid OpenAI API key'
        };
      }
    }
  }

  // Extract product data from text using AI
  async extractProductData(text: string): Promise<ExtractedProduct[]> {
    // Check for test mode
    if (process.env.NODE_ENV === 'development' && process.env.AI_TEST_MODE === 'true') {
      return this.extractWithTestMode(text);
    }

    if (this.useEmbedded) {
      return this.extractWithEmbeddedAI(text);
    }

    const prompt = this.createExtractionPrompt(text);

    if (this.useOllama) {
      return this.extractWithOllama(prompt);
    } else {
      return this.extractWithOpenAI(prompt);
    }
  }

  // Extract using test mode (no AI, simple text parsing)
  private async extractWithTestMode(text: string): Promise<ExtractedProduct[]> {
    console.log('🧪 Using Test Mode for extraction (no AI required)...');

    // Simple text parsing to extract basic product info
    const lines = text.split('\n').filter(line => line.trim().length > 5);
    const products: ExtractedProduct[] = [];

    // Look for product-like patterns
    for (let i = 0; i < Math.min(lines.length, 10); i++) {
      const line = lines[i].trim();

      if (line.length > 10 && line.length < 200) {
        // Try to extract a reference (numbers/letters pattern)
        const refMatch = line.match(/([A-Z0-9-]{3,15})/);
        const reference = refMatch ? refMatch[1] : `REF-${Date.now()}-${i}`;

        products.push({
          productName: line.substring(0, 50).trim(),
          reference: reference,
          description: `Produit extrait automatiquement: ${line.substring(0, 100)}`,
          characteristics: {
            'Source': 'Extraction automatique',
            'Ligne': `${i + 1}`,
            'Longueur': `${line.length} caractères`
          }
        });
      }
    }

    // If no products found, create a default one
    if (products.length === 0) {
      products.push({
        productName: 'Produit extrait du PDF',
        reference: `REF-${Date.now()}`,
        description: 'Produit extrait automatiquement du document PDF',
        characteristics: {
          'Source': 'Extraction automatique',
          'Mode': 'Test sans IA',
          'Taille du texte': `${text.length} caractères`
        }
      });
    }

    console.log(`✅ Test mode extracted ${products.length} products`);
    return products;
  }

  // Extract using embedded AI (Transformers.js)
  private async extractWithEmbeddedAI(text: string): Promise<ExtractedProduct[]> {
    try {
      console.log('🧠 Using Embedded AI for extraction...');

      // Dynamic import to avoid issues with server-side rendering
      const { pipeline } = await import('@xenova/transformers');

      // Initialize the model if not already loaded
      if (!this.embeddedModel) {
        console.log('📥 Loading embedded AI model (first time may take a moment)...');
        // Use a lightweight text generation model
        this.embeddedModel = await pipeline('text-generation', 'Xenova/distilgpt2');
        console.log('✅ Embedded AI model loaded successfully');
      }

      // Create a structured prompt for product extraction
      const prompt = `Extract product information from this text. Format as JSON:
Product Name: [name]
Reference: [ref]
Description: [desc]

Text: ${text.substring(0, 500)}...

JSON:`;

      // Generate response using the embedded model
      const result = await this.embeddedModel(prompt, {
        max_new_tokens: 200,
        temperature: 0.3,
        do_sample: true,
      });

      const generatedText = result[0]?.generated_text || '';
      console.log('🧠 Embedded AI response:', generatedText.substring(0, 200));

      // Parse the response and extract product information
      return await this.parseEmbeddedAIResponse(generatedText, text);

    } catch (error) {
      console.error('❌ Embedded AI extraction error:', error);

      // Fallback to enhanced text parsing if AI fails
      console.log('🔄 Falling back to enhanced text parsing...');
      return this.extractWithEnhancedParsing(text);
    }
  }

  // Enhanced text parsing as fallback
  private async extractWithEnhancedParsing(text: string): Promise<ExtractedProduct[]> {
    console.log('🔍 Using enhanced text parsing...');

    const lines = text.split('\n').filter(line => line.trim().length > 3);
    const products: ExtractedProduct[] = [];

    // Look for product patterns with better heuristics
    for (let i = 0; i < Math.min(lines.length, 20); i++) {
      const line = lines[i].trim();

      // Skip very short or very long lines
      if (line.length < 5 || line.length > 300) continue;

      // Look for lines that might be product names (contain letters and possibly numbers)
      if (/[A-Za-z].*[A-Za-z]/.test(line) && !/^(page|chapter|section|\d+\s*$)/i.test(line)) {
        // Extract potential reference numbers
        const refMatches = line.match(/([A-Z0-9]{2,}[-_]?[A-Z0-9]{2,}|[A-Z]{2,}\d+|\d+[A-Z]{2,})/g);
        const reference = refMatches ? refMatches[0] : `REF-${Date.now()}-${i}`;

        // Clean product name
        let productName = line.replace(/^\d+\.?\s*/, '').trim(); // Remove leading numbers
        productName = productName.substring(0, 80).trim(); // Limit length

        // Look for characteristics in nearby lines
        const characteristics: Record<string, string> = {};

        // Check next few lines for specifications
        for (let j = i + 1; j < Math.min(i + 4, lines.length); j++) {
          const nextLine = lines[j].trim();
          if (nextLine.length > 0 && nextLine.length < 200) {
            // Look for key-value patterns
            const kvMatch = nextLine.match(/([^:]+):\s*(.+)/);
            if (kvMatch) {
              characteristics[kvMatch[1].trim()] = kvMatch[2].trim();
            } else if (nextLine.includes('mm') || nextLine.includes('cm') || nextLine.includes('kg')) {
              characteristics['Dimensions/Poids'] = nextLine;
            }
          }
        }

        products.push({
          productName,
          reference,
          description: `Produit extrait: ${productName}`,
          characteristics: {
            ...characteristics,
            'Source': 'Extraction automatique améliorée',
            'Position': `Ligne ${i + 1}`,
          }
        });

        // Limit to 5 products to avoid overwhelming
        if (products.length >= 5) break;
      }
    }

    // If no products found, create a default one
    if (products.length === 0) {
      const firstMeaningfulLine = lines.find(line => line.length > 10 && line.length < 200) || 'Produit du PDF';
      products.push({
        productName: firstMeaningfulLine.substring(0, 50),
        reference: `REF-${Date.now()}`,
        description: 'Produit extrait automatiquement du document PDF',
        characteristics: {
          'Source': 'Extraction automatique',
          'Taille du texte': `${text.length} caractères`,
          'Nombre de lignes': `${lines.length}`,
        }
      });
    }

    console.log(`✅ Enhanced parsing extracted ${products.length} products`);
    return products;
  }

  // Parse embedded AI response
  private async parseEmbeddedAIResponse(generatedText: string, originalText: string): Promise<ExtractedProduct[]> {
    try {
      // Try to extract JSON from the generated text
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return [{
          productName: parsed.name || parsed.productName || 'Produit IA',
          reference: parsed.reference || parsed.ref || `REF-AI-${Date.now()}`,
          description: parsed.description || parsed.desc || 'Extrait par IA embarquée',
          characteristics: parsed.characteristics || {}
        }];
      }
    } catch (error) {
      console.log('Failed to parse AI JSON, using text analysis');
    }

    // Fallback to enhanced parsing
    return await this.extractWithEnhancedParsing(originalText);
  }

  // Create the extraction prompt
  private createExtractionPrompt(text: string): string {
    return `Analyze the following product catalog text and extract product information. For each product found, provide ONLY the following information in JSON format:

{
  "products": [
    {
      "productName": "exact product name",
      "reference": "product reference/model number",
      "description": "brief product description",
      "characteristics": {
        "key1": "value1",
        "key2": "value2"
      }
    }
  ]
}

Rules:
- Extract only clear, factual information
- Use the exact product names as written
- Include technical specifications in characteristics
- If no clear reference is found, use the product name
- Return valid JSON only, no additional text

Text to analyze:
${text}`;
  }

  // Extract using Ollama (local Llama model)
  private async extractWithOllama(prompt: string): Promise<ExtractedProduct[]> {
    try {
      console.log('🦙 Using Ollama for extraction...');

      const response = await fetch(`${this.ollamaBaseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.ollamaModel,
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.1,
            top_p: 0.9,
            max_tokens: 2000,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.response;

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('❌ Ollama extraction error:', error);
      throw new Error(`Ollama extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Extract using OpenAI
  private async extractWithOpenAI(prompt: string): Promise<ExtractedProduct[]> {
    try {
      console.log('🤖 Using OpenAI for extraction...');

      const completion = await this.openai!.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a product data extraction specialist. Extract product information and return only valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
      });

      const content = completion.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('❌ OpenAI extraction error:', error);
      throw new Error(`OpenAI extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Parse AI response and extract products
  private parseAIResponse(content: string): ExtractedProduct[] {
    try {
      // Clean the response to extract JSON
      let jsonStr = content.trim();

      // Find JSON block if wrapped in markdown
      const jsonMatch = jsonStr.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
      if (jsonMatch) {
        jsonStr = jsonMatch[1];
      }

      // Try to find JSON object
      const startIndex = jsonStr.indexOf('{');
      const lastIndex = jsonStr.lastIndexOf('}');

      if (startIndex !== -1 && lastIndex !== -1) {
        jsonStr = jsonStr.substring(startIndex, lastIndex + 1);
      }

      const parsed = JSON.parse(jsonStr);
      const products = parsed.products || [parsed];

      return products.map((product: any) => ({
        productName: product.productName || product.name || 'Produit sans nom',
        reference: product.reference || product.ref || product.productName || 'REF-AUTO',
        description: product.description || 'Description non disponible',
        characteristics: product.characteristics || product.specs || {}
      }));

    } catch (error) {
      console.error('❌ Failed to parse AI response:', error);
      console.log('Raw response:', content);

      // Fallback: try to extract basic info with regex
      return this.fallbackExtraction(content);
    }
  }

  // Fallback extraction using simple text parsing
  private fallbackExtraction(text: string): ExtractedProduct[] {
    console.log('🔄 Using fallback extraction...');

    // Simple regex patterns to find product-like information
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    const products: ExtractedProduct[] = [];

    for (const line of lines) {
      if (line.length > 10 && line.length < 200) {
        products.push({
          productName: line.trim(),
          reference: `REF-${Date.now()}-${products.length}`,
          description: 'Extrait automatiquement du document',
          characteristics: {}
        });
      }
    }

    return products.slice(0, 5); // Limit to 5 products
  }
}

// Export singleton instance
export const aiService = new AIService();
