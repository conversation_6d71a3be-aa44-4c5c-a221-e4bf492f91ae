import OpenAI from 'openai';

// Types for AI responses
export interface ExtractedProduct {
  productName: string;
  reference: string;
  description: string;
  characteristics: Record<string, string>;
}

// AI Service that supports both OpenAI and Ollama
export class AIService {
  private openai?: OpenAI;
  private ollamaBaseUrl: string;
  private ollamaModel: string;
  private useOllama: boolean;

  constructor() {
    this.ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
    this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.2:3b';

    // Check if OpenAI is configured
    const openaiKey = process.env.OPENAI_API_KEY;
    this.useOllama = !openaiKey || openaiKey === '' || openaiKey === 'your-openai-api-key-here';

    if (!this.useOllama) {
      this.openai = new OpenAI({
        apiKey: openaiKey,
      });
    }

    console.log(`🤖 AI Service initialized: ${this.useOllama ? 'Ollama' : 'OpenAI'}`);
  }

  // Check if AI service is available
  async isAvailable(): Promise<{ available: boolean; service: string; error?: string }> {
    // Check for test mode
    if (process.env.NODE_ENV === 'development' && process.env.AI_TEST_MODE === 'true') {
      return { available: true, service: 'Test Mode (No AI)' };
    }

    if (this.useOllama) {
      try {
        const response = await fetch(`${this.ollamaBaseUrl}/api/tags`);
        if (response.ok) {
          const data = await response.json();
          const hasModel = data.models?.some((model: any) => model.name.includes(this.ollamaModel.split(':')[0]));

          if (!hasModel) {
            return {
              available: false,
              service: 'Ollama',
              error: `Model ${this.ollamaModel} not found. Please install it with: ollama pull ${this.ollamaModel}`
            };
          }

          return { available: true, service: 'Ollama' };
        } else {
          return {
            available: false,
            service: 'Ollama',
            error: 'Ollama server not running. Please start Ollama first.'
          };
        }
      } catch (error) {
        return {
          available: false,
          service: 'Ollama',
          error: 'Cannot connect to Ollama. Please install and start Ollama.'
        };
      }
    } else {
      try {
        await this.openai!.models.list();
        return { available: true, service: 'OpenAI' };
      } catch (error) {
        return {
          available: false,
          service: 'OpenAI',
          error: 'Invalid OpenAI API key'
        };
      }
    }
  }

  // Extract product data from text using AI
  async extractProductData(text: string): Promise<ExtractedProduct[]> {
    // Check for test mode
    if (process.env.NODE_ENV === 'development' && process.env.AI_TEST_MODE === 'true') {
      return this.extractWithTestMode(text);
    }

    const prompt = this.createExtractionPrompt(text);

    if (this.useOllama) {
      return this.extractWithOllama(prompt);
    } else {
      return this.extractWithOpenAI(prompt);
    }
  }

  // Extract using test mode (no AI, simple text parsing)
  private async extractWithTestMode(text: string): Promise<ExtractedProduct[]> {
    console.log('🧪 Using Test Mode for extraction (no AI required)...');

    // Simple text parsing to extract basic product info
    const lines = text.split('\n').filter(line => line.trim().length > 5);
    const products: ExtractedProduct[] = [];

    // Look for product-like patterns
    for (let i = 0; i < Math.min(lines.length, 10); i++) {
      const line = lines[i].trim();

      if (line.length > 10 && line.length < 200) {
        // Try to extract a reference (numbers/letters pattern)
        const refMatch = line.match(/([A-Z0-9-]{3,15})/);
        const reference = refMatch ? refMatch[1] : `REF-${Date.now()}-${i}`;

        products.push({
          productName: line.substring(0, 50).trim(),
          reference: reference,
          description: `Produit extrait automatiquement: ${line.substring(0, 100)}`,
          characteristics: {
            'Source': 'Extraction automatique',
            'Ligne': `${i + 1}`,
            'Longueur': `${line.length} caractères`
          }
        });
      }
    }

    // If no products found, create a default one
    if (products.length === 0) {
      products.push({
        productName: 'Produit extrait du PDF',
        reference: `REF-${Date.now()}`,
        description: 'Produit extrait automatiquement du document PDF',
        characteristics: {
          'Source': 'Extraction automatique',
          'Mode': 'Test sans IA',
          'Taille du texte': `${text.length} caractères`
        }
      });
    }

    console.log(`✅ Test mode extracted ${products.length} products`);
    return products;
  }

  // Create the extraction prompt
  private createExtractionPrompt(text: string): string {
    return `Analyze the following product catalog text and extract product information. For each product found, provide ONLY the following information in JSON format:

{
  "products": [
    {
      "productName": "exact product name",
      "reference": "product reference/model number",
      "description": "brief product description",
      "characteristics": {
        "key1": "value1",
        "key2": "value2"
      }
    }
  ]
}

Rules:
- Extract only clear, factual information
- Use the exact product names as written
- Include technical specifications in characteristics
- If no clear reference is found, use the product name
- Return valid JSON only, no additional text

Text to analyze:
${text}`;
  }

  // Extract using Ollama (local Llama model)
  private async extractWithOllama(prompt: string): Promise<ExtractedProduct[]> {
    try {
      console.log('🦙 Using Ollama for extraction...');

      const response = await fetch(`${this.ollamaBaseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.ollamaModel,
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.1,
            top_p: 0.9,
            max_tokens: 2000,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.response;

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('❌ Ollama extraction error:', error);
      throw new Error(`Ollama extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Extract using OpenAI
  private async extractWithOpenAI(prompt: string): Promise<ExtractedProduct[]> {
    try {
      console.log('🤖 Using OpenAI for extraction...');

      const completion = await this.openai!.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a product data extraction specialist. Extract product information and return only valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
      });

      const content = completion.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('❌ OpenAI extraction error:', error);
      throw new Error(`OpenAI extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Parse AI response and extract products
  private parseAIResponse(content: string): ExtractedProduct[] {
    try {
      // Clean the response to extract JSON
      let jsonStr = content.trim();

      // Find JSON block if wrapped in markdown
      const jsonMatch = jsonStr.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
      if (jsonMatch) {
        jsonStr = jsonMatch[1];
      }

      // Try to find JSON object
      const startIndex = jsonStr.indexOf('{');
      const lastIndex = jsonStr.lastIndexOf('}');

      if (startIndex !== -1 && lastIndex !== -1) {
        jsonStr = jsonStr.substring(startIndex, lastIndex + 1);
      }

      const parsed = JSON.parse(jsonStr);
      const products = parsed.products || [parsed];

      return products.map((product: any) => ({
        productName: product.productName || product.name || 'Produit sans nom',
        reference: product.reference || product.ref || product.productName || 'REF-AUTO',
        description: product.description || 'Description non disponible',
        characteristics: product.characteristics || product.specs || {}
      }));

    } catch (error) {
      console.error('❌ Failed to parse AI response:', error);
      console.log('Raw response:', content);

      // Fallback: try to extract basic info with regex
      return this.fallbackExtraction(content);
    }
  }

  // Fallback extraction using simple text parsing
  private fallbackExtraction(text: string): ExtractedProduct[] {
    console.log('🔄 Using fallback extraction...');

    // Simple regex patterns to find product-like information
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    const products: ExtractedProduct[] = [];

    for (const line of lines) {
      if (line.length > 10 && line.length < 200) {
        products.push({
          productName: line.trim(),
          reference: `REF-${Date.now()}-${products.length}`,
          description: 'Extrait automatiquement du document',
          characteristics: {}
        });
      }
    }

    return products.slice(0, 5); // Limit to 5 products
  }
}

// Export singleton instance
export const aiService = new AIService();
