import { NextRequest, NextResponse } from 'next/server';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { prisma } from '@/lib/prisma';

// GET /api/mobile/test - Comprehensive mobile app connectivity test
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Mobile test endpoint called');
    
    // Test mobile authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);
    
    // Test database connectivity
    let dbTest = false;
    let userCount = 0;
    try {
      userCount = await prisma.user.count();
      dbTest = true;
    } catch (dbError) {
      console.error('Database test failed:', dbError);
    }

    // Test API endpoints availability
    const apiTests = {
      products: false,
      categories: false,
      quotes: false,
      chat: false,
    };

    try {
      await prisma.product.count();
      apiTests.products = true;
    } catch (error) {
      console.error('Products test failed:', error);
    }

    try {
      await prisma.category.count();
      apiTests.categories = true;
    } catch (error) {
      console.error('Categories test failed:', error);
    }

    try {
      await prisma.quote.count();
      apiTests.quotes = true;
    } catch (error) {
      console.error('Quotes test failed:', error);
    }

    try {
      await prisma.chatconversation.count();
      apiTests.chat = true;
    } catch (error) {
      console.error('Chat test failed:', error);
    }

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      server: {
        status: 'online',
        environment: process.env.NODE_ENV || 'unknown',
      },
      authentication: {
        mobileUser: mobileUser ? {
          id: mobileUser.id,
          role: mobileUser.role,
          username: mobileUser.username
        } : null,
        sessionUser: session?.user ? {
          id: session.user.id,
          role: session.user.role,
          email: session.user.email
        } : null,
        hasValidAuth: !!(mobileUser || session?.user)
      },
      database: {
        connected: dbTest,
        userCount: userCount,
      },
      apiEndpoints: apiTests,
      headers: {
        userAgent: request.headers.get('user-agent'),
        authorization: request.headers.get('authorization') ? 'Bearer [PRESENT]' : 'Not provided',
        contentType: request.headers.get('content-type'),
      },
      network: {
        ip: request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown',
        host: request.headers.get('host'),
      }
    };

    console.log('🧪 Mobile test response:', {
      hasAuth: response.authentication.hasValidAuth,
      dbConnected: response.database.connected,
      apiTests: response.apiEndpoints
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error('Mobile test endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Test failed',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

// POST /api/mobile/test - Test mobile app data submission
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Mobile POST test endpoint called');
    
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);
    
    if (!mobileUser && !session) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required for POST test',
        timestamp: new Date().toISOString(),
      }, { status: 401 });
    }

    const body = await request.json();
    
    return NextResponse.json({
      success: true,
      message: 'POST test successful',
      receivedData: body,
      user: mobileUser || session?.user,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Mobile POST test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'POST test failed',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

// PUT /api/mobile/test - Test file upload functionality
export async function PUT(request: NextRequest) {
  try {
    console.log('🧪 Mobile file upload test endpoint called');
    
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);
    
    if (!mobileUser && !session) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required for file upload test',
        timestamp: new Date().toISOString(),
      }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided for upload test',
        timestamp: new Date().toISOString(),
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'File upload test successful',
      file: {
        name: file.name,
        size: file.size,
        type: file.type,
      },
      user: mobileUser || session?.user,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Mobile file upload test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'File upload test failed',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

// DELETE /api/mobile/test - Test cleanup
export async function DELETE(request: NextRequest) {
  try {
    console.log('🧪 Mobile DELETE test endpoint called');
    
    return NextResponse.json({
      success: true,
      message: 'DELETE test successful',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Mobile DELETE test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'DELETE test failed',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
