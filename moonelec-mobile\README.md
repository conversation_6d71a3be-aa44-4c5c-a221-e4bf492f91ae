# Moonelec Mobile App

Application mobile React Native pour Moonelec - Distribution de Matériel Électrique.

## Description

Cette application mobile est conçue pour fonctionner avec l'API existante de l'application web Moonelec. Elle permet aux clients, commerciaux et administrateurs d'accéder aux fonctionnalités principales depuis leurs appareils mobiles.

## Fonctionnalités

### Pour tous les utilisateurs
- **Authentification** : Connexion sécurisée avec nom d'utilisateur/mot de passe
- **Catalogue produits** : Navigation et recherche dans le catalogue
- **Navigation par catégories** : Organisation hiérarchique des produits
- **Gestion des devis** : Consultation, création et suivi des devis détaillés
- **Partage et export** : Partage de devis et rapports

### Pour les commerciaux
- **Rapports de visite** : Création et gestion des rapports de visite
- **Pièces jointes multimédia** : Photos, vidéos, audio et documents
- **Géolocalisation** : Suivi des visites par ville

### Pour les administrateurs
- **Tableau de bord** : Vue d'ensemble des activités
- **Gestion complète** : Accès à toutes les fonctionnalités

## Architecture

### Structure du projet
```
src/
├── components/          # Composants réutilisables
├── contexts/           # Contextes React (Auth, etc.)
├── navigation/         # Configuration de navigation
├── screens/           # Écrans de l'application
│   ├── auth/          # Écrans d'authentification
│   ├── products/      # Écrans produits
│   ├── quotes/        # Écrans devis
│   └── sales-reports/ # Écrans rapports de visite
├── services/          # Services API
├── types/            # Types TypeScript
└── utils/            # Utilitaires
```

### Technologies utilisées
- **React Native** avec Expo
- **TypeScript** pour la sécurité des types
- **React Navigation** pour la navigation
- **React Native Paper** pour les composants UI
- **Axios** pour les appels API
- **Expo SecureStore** pour le stockage sécurisé

## Configuration

### Prérequis
- Node.js 18+
- Expo CLI
- Application web Moonelec en cours d'exécution

### Installation
```bash
cd moonelec-mobile
npm install
```

### Configuration de l'API
Modifiez l'URL de base dans `src/services/api.ts` :
```typescript
const BASE_URL = 'http://YOUR_API_URL:3000/api';
```

Pour le développement local, utilisez votre adresse IP locale :
```typescript
const BASE_URL = 'http://*************:3000/api';
```

**Important**: Assurez-vous que votre application web Moonelec est en cours d'exécution et accessible depuis votre appareil mobile.

### Lancement
```bash
# Démarrer le serveur de développement
npm start

# Lancer sur Android
npm run android

# Lancer sur iOS
npm run ios

# Lancer sur le web
npm run web
```

## Intégration avec l'API existante

L'application mobile utilise les mêmes endpoints que l'application web :

### Authentification
- `POST /api/auth/callback/credentials` - Connexion
- `GET /api/auth/session` - Vérification de session
- `POST /api/auth/signout` - Déconnexion

### Produits
- `GET /api/products` - Liste des produits
- `GET /api/products/:id` - Détail d'un produit

### Devis
- `GET /api/quotes` - Liste des devis
- `GET /api/quotes/:id` - Détail d'un devis
- `POST /api/quotes` - Création d'un devis

### Rapports de visite (Commerciaux)
- `GET /api/sales-reports` - Liste des rapports
- `POST /api/sales-reports` - Création d'un rapport
- `POST /api/upload` - Upload de fichiers

### Catégories et marques
- `GET /api/categories` - Liste des catégories
- `GET /api/brands` - Liste des marques

## Sécurité

- **Authentification** : Utilise le système NextAuth existant
- **Stockage sécurisé** : Les tokens sont stockés avec Expo SecureStore
- **Validation** : Validation côté client et serveur
- **HTTPS** : Recommandé pour la production

## Développement

### Ajout de nouvelles fonctionnalités
1. Créer les types dans `src/types/`
2. Ajouter les appels API dans `src/services/api.ts`
3. Créer les écrans dans `src/screens/`
4. Mettre à jour la navigation dans `src/navigation/`

### Tests
```bash
# Lancer les tests
npm test
```

### Build de production
```bash
# Build pour Android
expo build:android

# Build pour iOS
expo build:ios
```

## Déploiement

### Android
1. Générer un APK ou AAB avec Expo
2. Publier sur Google Play Store

### iOS
1. Générer un IPA avec Expo
2. Publier sur Apple App Store

### Configuration requise
- Certificats de signature
- Profils de provisioning (iOS)
- Clés de signature (Android)

## Support

Pour toute question ou problème :
1. Vérifiez que l'API web est accessible
2. Consultez les logs de l'application
3. Contactez l'équipe de développement

## Roadmap

### Fonctionnalités à venir
- [ ] Mode hors ligne
- [ ] Notifications push
- [ ] Synchronisation en arrière-plan
- [ ] Géolocalisation avancée
- [ ] Signature électronique
- [ ] Export PDF des devis
- [ ] Chat en temps réel

### Améliorations techniques
- [ ] Tests automatisés
- [ ] CI/CD
- [ ] Monitoring des performances
- [ ] Analytics d'utilisation
