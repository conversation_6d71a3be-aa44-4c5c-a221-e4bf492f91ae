"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/ModernHeader.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ModernHeader.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaCheckCircle,FaChevronDown,FaGlobe,FaSearch,FaShieldAlt,FaShippingFast,FaShoppingCart,FaTimes,FaUser!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ShopMegaMenu = (param)=>{\n    let { isOpen, onClose } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: -10\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -10\n            },\n            transition: {\n                duration: 0.2\n            },\n            className: \"absolute top-full left-0 w-full bg-white shadow-xl border-t border-gray-100 z-50\",\n            onMouseLeave: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Cat\\xe9gories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=eclairage\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"\\xc9clairage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=cables\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"C\\xe2bles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=prises\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Prises & Interrupteurs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?category=protection\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Protection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Marques\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=schneider\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Schneider Electric\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=legrand\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Legrand\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=abb\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"ABB\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products?brand=siemens\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Siemens\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-4\",\n                                    children: \"Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/residential\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"R\\xe9sidentiel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/commercial\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Commercial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/industrial\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Industriel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/solutions/smart-home\",\n                                                className: \"text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                children: \"Maison Intelligente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-light-gray p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-charcoal mb-2\",\n                                    children: \"Produit Vedette\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-white rounded-md mb-3 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"Nouveau syst\\xe8me d'\\xe9clairage LED intelligent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/featured\",\n                                    className: \"btn btn-primary text-sm py-2 px-4\",\n                                    children: \"D\\xe9couvrir\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 39,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n            lineNumber: 31,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ShopMegaMenu;\nconst FeatureBar = ()=>{\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShippingFast, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 96,\n                columnNumber: 13\n            }, undefined),\n            title: \"Livraison Gratuite\",\n            description: \"Sur toutes commandes de plus de 500 MAD\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaCheckCircle, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 101,\n                columnNumber: 13\n            }, undefined),\n            title: \"Garantie 30 Jours\",\n            description: \"Garantie de remboursement de 30 jours\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaGlobe, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 106,\n                columnNumber: 13\n            }, undefined),\n            title: \"Livraison Nationale\",\n            description: \"Livraison dans tout le Maroc\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShieldAlt, {\n                className: \"text-2xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 111,\n                columnNumber: 13\n            }, undefined),\n            title: \"Paiement Sécurisé\",\n            description: \"100% sécurisé et protégé\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-t border-gray-100 py-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-center space-x-4 group cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 group-hover:text-moonelec-red transition-colors duration-300\",\n                                children: feature.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-charcoal text-sm\",\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = FeatureBar;\nfunction ModernHeader() {\n    _s();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isShopMenuOpen, setIsShopMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { user, signOut } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"ModernHeader.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"ModernHeader.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernHeader.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ModernHeader.useEffect\"];\n        }\n    }[\"ModernHeader.useEffect\"], []);\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(\"/products?search=\".concat(encodeURIComponent(searchQuery.trim())));\n            setSearchQuery('');\n        }\n    };\n    const navigation = [\n        {\n            name: 'Accueil',\n            href: '/'\n        },\n        {\n            name: 'À Propos',\n            href: '/about'\n        },\n        {\n            name: 'Boutique',\n            href: '/products',\n            hasDropdown: true,\n            onMouseEnter: ()=>setIsShopMenuOpen(true),\n            onMouseLeave: ()=>setIsShopMenuOpen(false)\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-40 transition-all duration-300 \".concat(isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-white'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-moonelec-red rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"M\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-charcoal font-heading\",\n                                            children: \"Moonelec\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: item.onMouseEnter,\n                                            onMouseLeave: item.onMouseLeave,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"flex items-center space-x-1 font-medium transition-colors duration-200 \".concat(pathname === item.href ? 'text-moonelec-red' : 'text-charcoal hover:text-moonelec-red'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.hasDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaChevronDown, {\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSearch,\n                                            className: \"hidden md:flex\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Rechercher des produits...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"w-64 pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSearch, {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(user.role.toLowerCase(), \"/dashboard\"),\n                                                    className: \"p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>signOut(),\n                                                    className: \"text-sm text-gray-600 hover:text-moonelec-red transition-colors\",\n                                                    children: \"D\\xe9connexion\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaUser, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/cart\",\n                                            className: \"relative p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShoppingCart, {\n                                                    className: \"text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-moonelec-red text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                            className: \"lg:hidden p-2 text-gray-600 hover:text-moonelec-red transition-colors\",\n                                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTimes, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaBars, {\n                                                className: \"text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 71\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopMegaMenu, {\n                        isOpen: isShopMenuOpen,\n                        onClose: ()=>setIsShopMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                        children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"lg:hidden bg-white border-t border-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearch,\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Rechercher...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaCheckCircle_FaChevronDown_FaGlobe_FaSearch_FaShieldAlt_FaShippingFast_FaShoppingCart_FaTimes_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSearch, {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"block py-2 font-medium transition-colors \".concat(pathname === item.href ? 'text-moonelec-red' : 'text-charcoal hover:text-moonelec-red'),\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: item.name\n                                            }, item.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureBar, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\layout\\\\ModernHeader.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ModernHeader, \"QMPdWmtEOIcEQfKEgdl9NvgxJoA=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c2 = ModernHeader;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ShopMegaMenu\");\n$RefreshReg$(_c1, \"FeatureBar\");\n$RefreshReg$(_c2, \"ModernHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/ModernHeader.tsx\n"));

/***/ })

});