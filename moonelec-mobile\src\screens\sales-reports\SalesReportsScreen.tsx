import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SalesReport } from '../../types';
import ApiService from '../../services/api';

const SalesReportsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [reports, setReports] = useState<SalesReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    try {
      setIsLoading(true);
      const response = await ApiService.getSalesReports({ take: 50 });
      setReports(response.reports);
    } catch (error) {
      console.error('Error loading sales reports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReports();
    setRefreshing(false);
  };

  const renderReport = ({ item }: { item: SalesReport }) => (
    <TouchableOpacity
      style={styles.reportCard}
      onPress={() => navigation.navigate('SalesReportDetail', { reportId: item.id })}
    >
      <View style={styles.reportHeader}>
        <Text style={styles.reportClient}>{item.denomination}</Text>
        <Text style={styles.reportDate}>
          {new Date(item.visitDate).toLocaleDateString('fr-FR')}
        </Text>
      </View>

      <View style={styles.reportInfo}>
        <Text style={styles.reportContact}>{item.name}</Text>
        <Text style={styles.reportCity}>{item.city}</Text>
        <Text style={styles.reportPurpose} numberOfLines={2}>
          {item.visitPurpose}
        </Text>
      </View>

      <View style={styles.reportFooter}>
        <View style={styles.attachments}>
          {item.images.length > 0 && (
            <View style={styles.attachmentBadge}>
              <Ionicons name="image" size={16} color="#4CAF50" />
              <Text style={styles.attachmentCount}>{item.images.length}</Text>
            </View>
          )}
          {item.videoUrl && (
            <View style={styles.attachmentBadge}>
              <Ionicons name="videocam" size={16} color="#2196F3" />
            </View>
          )}
          {item.audioUrl && (
            <View style={styles.attachmentBadge}>
              <Ionicons name="mic" size={16} color="#FF9800" />
            </View>
          )}
        </View>
        <Ionicons name="chevron-forward" size={20} color="#666" />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Rapports de Visite</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreateSalesReport')}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={reports}
        renderItem={renderReport}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#006db7',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  reportCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  reportClient: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  reportDate: {
    fontSize: 14,
    color: '#666',
  },
  reportInfo: {
    marginBottom: 12,
  },
  reportContact: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  reportCity: {
    fontSize: 14,
    color: '#006db7',
    fontWeight: '600',
    marginBottom: 4,
  },
  reportPurpose: {
    fontSize: 14,
    color: '#333',
  },
  reportFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  attachments: {
    flexDirection: 'row',
  },
  attachmentBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  attachmentCount: {
    fontSize: 12,
    marginLeft: 4,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});

export default SalesReportsScreen;
