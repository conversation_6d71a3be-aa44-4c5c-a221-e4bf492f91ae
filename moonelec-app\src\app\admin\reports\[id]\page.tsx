'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { 
  FaArrowLeft, 
  FaUser, 
  FaCalendarAlt, 
  FaMapMarkerAlt, 
  FaTag, 
  FaComment, 
  FaBuilding,
  FaFileAlt,
  FaVideo,
  FaFileAudio,
  FaFilePdf,
  FaDownload,
  FaSpinner
} from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';

interface SalesReport {
  id: string;
  commercialId: string;
  need: string;
  articleRef?: string;
  comment?: string;
  visitDate: string;
  denomination: string;
  images?: string;
  imagesArray?: string[];
  name: string;
  visitPurpose: string;
  complaint?: string;
  city: string;
  videoUrl?: string;
  audioUrl?: string;
  pdfUrl?: string;
  submittedAt: string;
  commercial: {
    user: {
      firstname: string;
      lastname: string;
      email: string;
      telephone?: string;
    }
  }
}

export default function ReportDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [report, setReport] = useState<SalesReport | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Fetch report details
  const fetchReport = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/sales-reports/${params.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch report');
      }
      
      const data = await response.json();
      setReport(data.report);
      
      // Set the first image as selected if available
      if (data.report.imagesArray && data.report.imagesArray.length > 0) {
        setSelectedImage(data.report.imagesArray[0]);
      }
    } catch (error) {
      console.error('Error fetching report:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch report on initial load
  useEffect(() => {
    if (user) {
      fetchReport();
    }
  }, [user, params.id]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <FaSpinner className="animate-spin text-4xl text-primary" />
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
            Report Not Found
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            The report you are looking for does not exist or you do not have permission to view it.
          </p>
          <button
            onClick={() => router.push('/admin/reports')}
            className="flex items-center text-primary hover:text-primary-dark"
          >
            <FaArrowLeft className="mr-2" />
            Back to Reports
          </button>
        </div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['ADMIN']}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => router.push('/admin/reports')}
            className="mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
          >
            <FaArrowLeft className="text-xl" />
          </button>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Sales Report Details
          </h1>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Report Details */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              {/* Commercial Info */}
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white mr-4">
                  <FaUser className="text-xl" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                    {report.commercial.user.firstname} {report.commercial.user.lastname}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300">
                    {report.commercial.user.email}
                  </p>
                  {report.commercial.user.telephone && (
                    <p className="text-gray-600 dark:text-gray-300">
                      {report.commercial.user.telephone}
                    </p>
                  )}
                </div>
              </div>
              
              {/* Report Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex items-start">
                  <FaCalendarAlt className="text-gray-500 dark:text-gray-400 mt-1 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Visit Date</p>
                    <p className="text-gray-800 dark:text-white">
                      {new Date(report.visitDate).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <FaMapMarkerAlt className="text-gray-500 dark:text-gray-400 mt-1 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">City</p>
                    <p className="text-gray-800 dark:text-white">{report.city}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <FaTag className="text-gray-500 dark:text-gray-400 mt-1 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Article Reference</p>
                    <p className="text-gray-800 dark:text-white">{report.articleRef || 'N/A'}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <FaBuilding className="text-gray-500 dark:text-gray-400 mt-1 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Denomination</p>
                    <p className="text-gray-800 dark:text-white">{report.denomination}</p>
                  </div>
                </div>
              </div>
              
              {/* Need */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">Need</h3>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                  <p className="text-gray-800 dark:text-white whitespace-pre-wrap">{report.need}</p>
                </div>
              </div>
              
              {/* Purpose of Visit */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">Purpose of Visit</h3>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                  <p className="text-gray-800 dark:text-white whitespace-pre-wrap">{report.visitPurpose}</p>
                </div>
              </div>
              
              {/* Comment */}
              {report.comment && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">Comment</h3>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                    <p className="text-gray-800 dark:text-white whitespace-pre-wrap">{report.comment}</p>
                  </div>
                </div>
              )}
              
              {/* Complaint */}
              {report.complaint && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">Complaint</h3>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                    <p className="text-gray-800 dark:text-white whitespace-pre-wrap">{report.complaint}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Right Column - Media */}
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Attachments</h3>
              
              {/* Images */}
              {report.imagesArray && report.imagesArray.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Images</h4>
                  
                  {/* Main Image */}
                  <div className="relative aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden mb-2">
                    {selectedImage ? (
                      <Image
                        src={selectedImage}
                        alt="Report image"
                        fill
                        style={{ objectFit: 'contain' }}
                        className="w-full h-full"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <span className="text-gray-400">No image selected</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Image Thumbnails */}
                  <div className="grid grid-cols-4 gap-2">
                    {report.imagesArray.map((image, index) => (
                      <div
                        key={index}
                        className={`relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 cursor-pointer ${
                          selectedImage === image ? 'ring-2 ring-primary' : ''
                        }`}
                        onClick={() => setSelectedImage(image)}
                      >
                        <Image
                          src={image}
                          alt={`Report image ${index + 1}`}
                          fill
                          style={{ objectFit: 'cover' }}
                          className="w-full h-full"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Other Media */}
              <div className="space-y-4">
                {/* Video */}
                {report.videoUrl && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div className="flex items-center">
                      <FaVideo className="text-blue-500 mr-2" />
                      <span className="text-gray-800 dark:text-white">Video Recording</span>
                    </div>
                    <a
                      href={report.videoUrl}
                      download
                      className="text-primary hover:text-primary-dark"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FaDownload />
                    </a>
                  </div>
                )}
                
                {/* Audio */}
                {report.audioUrl && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div className="flex items-center">
                      <FaFileAudio className="text-green-500 mr-2" />
                      <span className="text-gray-800 dark:text-white">Audio Recording</span>
                    </div>
                    <a
                      href={report.audioUrl}
                      download
                      className="text-primary hover:text-primary-dark"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FaDownload />
                    </a>
                  </div>
                )}
                
                {/* PDF */}
                {report.pdfUrl && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div className="flex items-center">
                      <FaFilePdf className="text-red-500 mr-2" />
                      <span className="text-gray-800 dark:text-white">PDF Document</span>
                    </div>
                    <a
                      href={report.pdfUrl}
                      download
                      className="text-primary hover:text-primary-dark"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FaDownload />
                    </a>
                  </div>
                )}
                
                {!report.videoUrl && !report.audioUrl && !report.pdfUrl && (!report.imagesArray || report.imagesArray.length === 0) && (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    No attachments available
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </RouteGuard>
  );
}
