"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/components/products/ModernProductsPage.tsx":
/*!********************************************************!*\
  !*** ./src/components/products/ModernProductsPage.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronDown,FaEye,FaFilter,FaHeart,FaList,FaSearch,FaShoppingCart,FaStar,FaTimes!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ModernProductsPage(param) {\n    let { initialProducts, categories, brands, searchParams } = param;\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProducts);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        10000\n    ]);\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedBrands, setSelectedBrands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchParams.search || '');\n    const sortOptions = [\n        {\n            value: 'name',\n            label: 'Nom A-Z'\n        },\n        {\n            value: 'name-desc',\n            label: 'Nom Z-A'\n        },\n        {\n            value: 'price',\n            label: 'Prix croissant'\n        },\n        {\n            value: 'price-desc',\n            label: 'Prix décroissant'\n        },\n        {\n            value: 'newest',\n            label: 'Plus récents'\n        },\n        {\n            value: 'rating',\n            label: 'Mieux notés'\n        }\n    ];\n    const ProductCard = (param)=>{\n        let { product } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            layout: true,\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: -20\n            },\n            className: \"group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-square bg-light-gray overflow-hidden\",\n                    children: [\n                        product.mainImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: product.mainImage,\n                            alt: product.name,\n                            fill: true,\n                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center bg-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-gray-300 rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 left-3 flex flex-col space-y-2\",\n                            children: [\n                                product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-electric-blue text-white text-xs font-semibold rounded\",\n                                    children: \"NOUVEAU\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                product.isOnSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-moonelec-red text-white text-xs font-semibold rounded\",\n                                    children: \"PROMO\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-moonelec-red rounded-full shadow-md transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/\".concat(product.id),\n                                    className: \"p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-electric-blue rounded-full shadow-md transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEye, {\n                                        className: \"text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-x-0 bottom-0 p-4 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full btn btn-primary text-sm py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaShoppingCart, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Ajouter au Panier\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-xs text-gray-500 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gray-100 px-2 py-1 rounded\",\n                                    children: product.category.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: product.brand.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-charcoal mb-2 line-clamp-2 group-hover:text-moonelec-red transition-colors\",\n                            children: product.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mb-2\",\n                            children: [\n                                \"R\\xe9f: \",\n                                product.reference\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 9\n                        }, this),\n                        product.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 mb-3\",\n                            children: [\n                                [\n                                    ...Array(5)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaStar, {\n                                        className: \"text-xs \".concat(i < product.rating ? 'text-yellow-400' : 'text-gray-300')\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-1\",\n                                    children: [\n                                        \"(\",\n                                        product.rating,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-bold text-moonelec-red\",\n                                        children: [\n                                            product.price.toLocaleString('fr-FR'),\n                                            \" MAD\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 line-through\",\n                                        children: [\n                                            product.originalPrice.toLocaleString('fr-FR'),\n                                            \" MAD\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n            lineNumber: 79,\n            columnNumber: 5\n        }, this);\n    };\n    const ProductListItem = (param)=>{\n        let { product } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            layout: true,\n            initial: {\n                opacity: 0,\n                x: -20\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            },\n            exit: {\n                opacity: 0,\n                x: 20\n            },\n            className: \"group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-48 h-48 bg-light-gray flex-shrink-0\",\n                        children: product.mainImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: product.mainImage,\n                            alt: product.name,\n                            fill: true,\n                            className: \"object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center bg-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-gray-300 rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-gray-100 px-2 py-1 rounded\",\n                                                    children: product.category.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: product.brand.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-charcoal mb-2 group-hover:text-moonelec-red transition-colors\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mb-2\",\n                                            children: [\n                                                \"R\\xe9f: \",\n                                                product.reference\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1 mb-4\",\n                                            children: [\n                                                [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaStar, {\n                                                        className: \"text-sm \".concat(i < product.rating ? 'text-yellow-400' : 'text-gray-300')\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.rating,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold text-moonelec-red\",\n                                                    children: [\n                                                        product.price.toLocaleString('fr-FR'),\n                                                        \" MAD\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 line-through\",\n                                                    children: [\n                                                        product.originalPrice.toLocaleString('fr-FR'),\n                                                        \" MAD\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-2 border border-gray-200 hover:border-moonelec-red text-gray-600 hover:text-moonelec-red rounded-lg transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/products/\".concat(product.id),\n                                                    className: \"p-2 border border-gray-200 hover:border-electric-blue text-gray-600 hover:text-electric-blue rounded-lg transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEye, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn btn-primary px-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaShoppingCart, {\n                                                            className: \"mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Ajouter\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n            lineNumber: 186,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-light-gray\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Rechercher des produits...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-12 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"appearance-none bg-white border border-gray-200 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-electric-blue\",\n                                                    children: sortOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: option.value,\n                                                            children: option.label\n                                                        }, option.value, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronDown, {\n                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex border border-gray-200 rounded-lg overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewMode('grid'),\n                                                    className: \"p-2 \".concat(viewMode === 'grid' ? 'bg-electric-blue text-white' : 'bg-white text-gray-600 hover:bg-gray-50', \" transition-colors\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaGrid3X3, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewMode('list'),\n                                                    className: \"p-2 \".concat(viewMode === 'list' ? 'bg-electric-blue text-white' : 'bg-white text-gray-600 hover:bg-gray-50', \" transition-colors\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowFilters(!showFilters),\n                                            className: \"btn btn-secondary flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaFilter, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Filtres\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        (selectedCategories.length > 0 || selectedBrands.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap gap-2\",\n                            children: [\n                                selectedCategories.map((categoryId)=>{\n                                    const category = categories.find((c)=>c.id === categoryId);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center space-x-2 bg-electric-blue/10 text-electric-blue px-3 py-1 rounded-full text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category === null || category === void 0 ? void 0 : category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedCategories((prev)=>prev.filter((id)=>id !== categoryId)),\n                                                className: \"hover:text-electric-blue-dark\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTimes, {\n                                                    className: \"text-xs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, categoryId, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 19\n                                    }, this);\n                                }),\n                                selectedBrands.map((brandId)=>{\n                                    const brand = brands.find((b)=>b.id === brandId);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center space-x-2 bg-moonelec-red/10 text-moonelec-red px-3 py-1 rounded-full text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: brand === null || brand === void 0 ? void 0 : brand.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedBrands((prev)=>prev.filter((id)=>id !== brandId)),\n                                                className: \"hover:text-moonelec-red-dark\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTimes, {\n                                                    className: \"text-xs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, brandId, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                            children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -300\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -300\n                                },\n                                className: \"w-80 bg-white rounded-xl shadow-lg p-6 h-fit sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-charcoal mb-6\",\n                                        children: \"Filtres\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-charcoal mb-3\",\n                                                children: \"Cat\\xe9gories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedCategories.includes(category.id),\n                                                                onChange: (e)=>{\n                                                                    if (e.target.checked) {\n                                                                        setSelectedCategories((prev)=>[\n                                                                                ...prev,\n                                                                                category.id\n                                                                            ]);\n                                                                    } else {\n                                                                        setSelectedCategories((prev)=>prev.filter((id)=>id !== category.id));\n                                                                    }\n                                                                },\n                                                                className: \"rounded border-gray-300 text-electric-blue focus:ring-electric-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, category.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-charcoal mb-3\",\n                                                children: \"Marques\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedBrands.includes(brand.id),\n                                                                onChange: (e)=>{\n                                                                    if (e.target.checked) {\n                                                                        setSelectedBrands((prev)=>[\n                                                                                ...prev,\n                                                                                brand.id\n                                                                            ]);\n                                                                    } else {\n                                                                        setSelectedBrands((prev)=>prev.filter((id)=>id !== brand.id));\n                                                                    }\n                                                                },\n                                                                className: \"rounded border-gray-300 text-moonelec-red focus:ring-moonelec-red\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700\",\n                                                                children: brand.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, brand.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSelectedCategories([]);\n                                            setSelectedBrands([]);\n                                            setPriceRange([\n                                                0,\n                                                10000\n                                            ]);\n                                        },\n                                        className: \"w-full btn btn-secondary text-sm\",\n                                        children: \"Effacer les Filtres\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        ...Array(12)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-4 animate-pulse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-200 rounded-lg mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 bg-gray-200 rounded mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-3 bg-gray-200 rounded mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-6',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                        children: products.map((product)=>viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCard, {\n                                                product: product\n                                            }, product.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductListItem, {\n                                                product: product\n                                            }, product.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this),\n                                !isLoading && products.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaEye_FaFilter_FaHeart_FaList_FaSearch_FaShoppingCart_FaStar_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                                className: \"text-gray-400 text-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-charcoal mb-2\",\n                                            children: \"Aucun produit trouv\\xe9\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Essayez de modifier vos crit\\xe8res de recherche\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\products\\\\ModernProductsPage.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernProductsPage, \"BwW6TbPeIH4b+5LWE7d7zrUiHFk=\");\n_c = ModernProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ModernProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/ModernProductsPage.tsx\n"));

/***/ })

});