import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { 
  getUnreadNotificationsForAdmin, 
  markNotificationAsRead, 
  markAllNotificationsAsRead 
} from '@/lib/notifications';

// GET /api/notifications - Récupérer les notifications non lues
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Vérifier que l'utilisateur est un administrateur
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Only administrators can access notifications' },
        { status: 403 }
      );
    }

    const adminId = session.user.adminId;

    if (!adminId) {
      return NextResponse.json(
        { error: 'Admin ID not found in session' },
        { status: 400 }
      );
    }

    const notifications = await getUnreadNotificationsForAdmin(adminId);

    return NextResponse.json({ notifications });
  } catch (error: any) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

// PATCH /api/notifications - Marquer une notification comme lue
export async function PATCH(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Vérifier que l'utilisateur est un administrateur
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Only administrators can update notifications' },
        { status: 403 }
      );
    }

    const adminId = session.user.adminId;

    if (!adminId) {
      return NextResponse.json(
        { error: 'Admin ID not found in session' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const { notificationId, markAll } = body;

    if (markAll) {
      // Marquer toutes les notifications comme lues
      const result = await markAllNotificationsAsRead(adminId);
      return NextResponse.json({ success: true, count: result.count });
    } else if (notificationId) {
      // Marquer une notification spécifique comme lue
      const notification = await markNotificationAsRead(notificationId);
      return NextResponse.json({ success: true, notification });
    } else {
      return NextResponse.json(
        { error: 'notificationId or markAll is required' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update notification' },
      { status: 500 }
    );
  }
}
