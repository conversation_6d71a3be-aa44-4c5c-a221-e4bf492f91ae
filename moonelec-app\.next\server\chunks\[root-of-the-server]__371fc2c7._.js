module.exports = {

"[project]/.next-internal/server/app/api/extract-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = global.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
// En développement, nous attachons le client à l'objet global pour éviter
// de créer de nouvelles instances à chaque rechargement du serveur
if ("TURBOPACK compile-time truthy", 1) {
    global.prisma = prisma;
}
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminUser": (()=>createAdminUser),
    "createClientUser": (()=>createClientUser),
    "createCommercialUser": (()=>createCommercialUser),
    "findUserByEmail": (()=>findUserByEmail),
    "findUserByUsername": (()=>findUserByUsername),
    "hashPassword": (()=>hashPassword),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
async function hashPassword(password) {
    const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(10);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, salt);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
async function createClientUser({ email, username, password, lastname, firstname, telephone, company_name }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].CLIENT
            }
        });
        // Créer le profil client associé
        const client = await tx.client.create({
            data: {
                userId: user.id,
                company_name
            }
        });
        return {
            user,
            client
        };
    });
}
async function createCommercialUser({ email, username, password, lastname, firstname, telephone, profile_photo }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].COMMERCIAL
            }
        });
        // Créer le profil commercial associé
        const commercial = await tx.commercial.create({
            data: {
                userId: user.id,
                profile_photo
            }
        });
        return {
            user,
            commercial
        };
    });
}
async function createAdminUser({ email, username, password, lastname, firstname, telephone }) {
    const hashedPassword = await hashPassword(password);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
        // Créer l'utilisateur de base
        const user = await tx.user.create({
            data: {
                email,
                username,
                password: hashedPassword,
                lastname,
                firstname,
                telephone,
                role: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["UserRole"].ADMIN
            }
        });
        // Créer le profil admin associé
        const admin = await tx.admin.create({
            data: {
                userId: user.id
            }
        });
        return {
            user,
            admin
        };
    });
}
async function findUserByUsername(username) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            username
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
async function findUserByEmail(email) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
        where: {
            email
        },
        include: {
            client: true,
            commercial: true,
            admin: true
        }
    });
}
}}),
"[project]/src/lib/auth-options.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'Credentials',
            credentials: {
                username: {
                    label: "Nom d'utilisateur",
                    type: 'text'
                },
                password: {
                    label: 'Mot de passe',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.username || !credentials?.password) {
                    return null;
                }
                try {
                    // Rechercher l'utilisateur par nom d'utilisateur
                    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["findUserByUsername"])(credentials.username);
                    // Vérifier si l'utilisateur existe
                    if (!user) {
                        return null;
                    }
                    // Vérifier le mot de passe
                    const isPasswordValid = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyPassword"])(credentials.password, user.password);
                    if (!isPasswordValid) {
                        return null;
                    }
                    // Retourner les données de l'utilisateur sans le mot de passe
                    return {
                        id: user.id,
                        email: user.email,
                        username: user.username,
                        name: `${user.firstname} ${user.lastname}`,
                        firstname: user.firstname,
                        lastname: user.lastname,
                        role: user.role,
                        clientId: user.client?.id,
                        commercialId: user.commercial?.id,
                        adminId: user.admin?.id
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.username = user.username;
                token.role = user.role;
                token.firstname = user.firstname;
                token.lastname = user.lastname;
                token.clientId = user.clientId;
                token.commercialId = user.commercialId;
                token.adminId = user.adminId;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.id;
                session.user.username = token.username;
                session.user.role = token.role;
                session.user.firstname = token.firstname;
                session.user.lastname = token.lastname;
                session.user.clientId = token.clientId;
                session.user.commercialId = token.commercialId;
                session.user.adminId = token.adminId;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signOut: '/auth/signout',
        error: '/auth/error'
    },
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:stream/web [external] (node:stream/web, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream/web", () => require("node:stream/web"));

module.exports = mod;
}}),
"[project]/src/lib/ai-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIService": (()=>AIService),
    "aiService": (()=>aiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
;
class AIService {
    openai;
    ollamaBaseUrl;
    ollamaModel;
    useOllama;
    constructor(){
        this.ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
        this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.2:3b';
        // Check if OpenAI is configured
        const openaiKey = process.env.OPENAI_API_KEY;
        this.useOllama = !openaiKey || openaiKey === '' || openaiKey === 'your-openai-api-key-here';
        if (!this.useOllama) {
            this.openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]({
                apiKey: openaiKey
            });
        }
        console.log(`🤖 AI Service initialized: ${this.useOllama ? 'Ollama' : 'OpenAI'}`);
    }
    // Check if AI service is available
    async isAvailable() {
        if (this.useOllama) {
            try {
                const response = await fetch(`${this.ollamaBaseUrl}/api/tags`);
                if (response.ok) {
                    const data = await response.json();
                    const hasModel = data.models?.some((model)=>model.name.includes(this.ollamaModel.split(':')[0]));
                    if (!hasModel) {
                        return {
                            available: false,
                            service: 'Ollama',
                            error: `Model ${this.ollamaModel} not found. Please install it with: ollama pull ${this.ollamaModel}`
                        };
                    }
                    return {
                        available: true,
                        service: 'Ollama'
                    };
                } else {
                    return {
                        available: false,
                        service: 'Ollama',
                        error: 'Ollama server not running. Please start Ollama first.'
                    };
                }
            } catch (error) {
                return {
                    available: false,
                    service: 'Ollama',
                    error: 'Cannot connect to Ollama. Please install and start Ollama.'
                };
            }
        } else {
            try {
                await this.openai.models.list();
                return {
                    available: true,
                    service: 'OpenAI'
                };
            } catch (error) {
                return {
                    available: false,
                    service: 'OpenAI',
                    error: 'Invalid OpenAI API key'
                };
            }
        }
    }
    // Extract product data from text using AI
    async extractProductData(text) {
        const prompt = this.createExtractionPrompt(text);
        if (this.useOllama) {
            return this.extractWithOllama(prompt);
        } else {
            return this.extractWithOpenAI(prompt);
        }
    }
    // Create the extraction prompt
    createExtractionPrompt(text) {
        return `Analyze the following product catalog text and extract product information. For each product found, provide ONLY the following information in JSON format:

{
  "products": [
    {
      "productName": "exact product name",
      "reference": "product reference/model number",
      "description": "brief product description",
      "characteristics": {
        "key1": "value1",
        "key2": "value2"
      }
    }
  ]
}

Rules:
- Extract only clear, factual information
- Use the exact product names as written
- Include technical specifications in characteristics
- If no clear reference is found, use the product name
- Return valid JSON only, no additional text

Text to analyze:
${text}`;
    }
    // Extract using Ollama (local Llama model)
    async extractWithOllama(prompt) {
        try {
            console.log('🦙 Using Ollama for extraction...');
            const response = await fetch(`${this.ollamaBaseUrl}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.ollamaModel,
                    prompt: prompt,
                    stream: false,
                    options: {
                        temperature: 0.1,
                        top_p: 0.9,
                        max_tokens: 2000
                    }
                })
            });
            if (!response.ok) {
                throw new Error(`Ollama API error: ${response.status}`);
            }
            const data = await response.json();
            const content = data.response;
            return this.parseAIResponse(content);
        } catch (error) {
            console.error('❌ Ollama extraction error:', error);
            throw new Error(`Ollama extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Extract using OpenAI
    async extractWithOpenAI(prompt) {
        try {
            console.log('🤖 Using OpenAI for extraction...');
            const completion = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a product data extraction specialist. Extract product information and return only valid JSON.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2000
            });
            const content = completion.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No response from OpenAI');
            }
            return this.parseAIResponse(content);
        } catch (error) {
            console.error('❌ OpenAI extraction error:', error);
            throw new Error(`OpenAI extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Parse AI response and extract products
    parseAIResponse(content) {
        try {
            // Clean the response to extract JSON
            let jsonStr = content.trim();
            // Find JSON block if wrapped in markdown
            const jsonMatch = jsonStr.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
            if (jsonMatch) {
                jsonStr = jsonMatch[1];
            }
            // Try to find JSON object
            const startIndex = jsonStr.indexOf('{');
            const lastIndex = jsonStr.lastIndexOf('}');
            if (startIndex !== -1 && lastIndex !== -1) {
                jsonStr = jsonStr.substring(startIndex, lastIndex + 1);
            }
            const parsed = JSON.parse(jsonStr);
            const products = parsed.products || [
                parsed
            ];
            return products.map((product)=>({
                    productName: product.productName || product.name || 'Produit sans nom',
                    reference: product.reference || product.ref || product.productName || 'REF-AUTO',
                    description: product.description || 'Description non disponible',
                    characteristics: product.characteristics || product.specs || {}
                }));
        } catch (error) {
            console.error('❌ Failed to parse AI response:', error);
            console.log('Raw response:', content);
            // Fallback: try to extract basic info with regex
            return this.fallbackExtraction(content);
        }
    }
    // Fallback extraction using simple text parsing
    fallbackExtraction(text) {
        console.log('🔄 Using fallback extraction...');
        // Simple regex patterns to find product-like information
        const lines = text.split('\n').filter((line)=>line.trim().length > 0);
        const products = [];
        for (const line of lines){
            if (line.length > 10 && line.length < 200) {
                products.push({
                    productName: line.trim(),
                    reference: `REF-${Date.now()}-${products.length}`,
                    description: 'Extrait automatiquement du document',
                    characteristics: {}
                });
            }
        }
        return products.slice(0, 5); // Limit to 5 products
    }
}
const aiService = new AIService();
}}),
"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-options.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-service.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(req) {
    try {
        // Vérification de l'authentification
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user || session.user.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Check if AI service is available
        const aiStatus = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiService"].isAvailable();
        if (!aiStatus.available) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `AI service not available: ${aiStatus.error}`,
                service: aiStatus.service,
                configured: false
            }, {
                status: 503
            });
        }
        console.log(`🤖 Using AI service: ${aiStatus.service}`);
        // Récupération du fichier PDF
        const formData = await req.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No PDF file provided'
            }, {
                status: 400
            });
        }
        // Vérification du type de fichier
        if (file.type !== 'application/pdf') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be a PDF'
            }, {
                status: 400
            });
        }
        // Vérification de la taille du fichier (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File size must be less than 10MB'
            }, {
                status: 400
            });
        }
        console.log('📄 Processing PDF:', file.name, 'Size:', file.size);
        // Conversion du fichier en buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Extraction du texte du PDF
        console.log('🔍 Extracting text from PDF...');
        let extractedText = '';
        try {
            // Use pdfjs-dist for PDF parsing
            const pdfjsLib = await __turbopack_context__.r("[project]/node_modules/pdfjs-dist/build/pdf.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            // Load the PDF document
            const loadingTask = pdfjsLib.getDocument({
                data: buffer,
                useSystemFonts: true
            });
            const pdfDocument = await loadingTask.promise;
            // Extract text from each page
            for(let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++){
                const page = await pdfDocument.getPage(pageNum);
                const textContent = await page.getTextContent();
                const pageText = textContent.items.map((item)=>item.str).join(' ');
                extractedText += pageText + '\n';
            }
        } catch (pdfError) {
            console.error('❌ PDF parsing error:', pdfError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to parse PDF file. Please ensure it contains readable text.'
            }, {
                status: 400
            });
        }
        if (!extractedText || extractedText.trim().length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No text could be extracted from the PDF'
            }, {
                status: 400
            });
        }
        console.log('📝 Extracted text length:', extractedText.length);
        // Appel au service IA (Ollama ou OpenAI)
        console.log('🤖 Calling AI service for extraction...');
        let extractedProducts;
        try {
            extractedProducts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiService"].extractProductData(extractedText);
        } catch (aiError) {
            console.error('❌ AI Service Error:', aiError);
            const errorMessage = aiError instanceof Error ? aiError.message : 'Unknown AI error';
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `AI extraction failed: ${errorMessage}`
            }, {
                status: 500
            });
        }
        if (!extractedProducts || extractedProducts.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No products could be extracted from the PDF'
            }, {
                status: 400
            });
        }
        // Use the first extracted product (already parsed by AI service)
        const extractedData = extractedProducts[0];
        console.log('🤖 AI extracted product:', extractedData.productName);
        // Validation des données extraites
        if (!extractedData.productName || !extractedData.description) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Insufficient product data extracted'
            }, {
                status: 400
            });
        }
        console.log('✅ Successfully extracted product data:', extractedData.productName);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: extractedData,
            metadata: {
                fileName: file.name,
                fileSize: file.size,
                textLength: extractedText.length,
                extractedAt: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('❌ PDF extraction error:', error);
        if (error instanceof Error) {
            // Erreurs spécifiques d'OpenAI
            if (error.message.includes('API key')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'OpenAI API configuration error'
                }, {
                    status: 500
                });
            }
            // Erreurs de parsing PDF
            if (error.message.includes('pdf')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Failed to parse PDF file'
                }, {
                    status: 400
                });
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error during PDF extraction'
        }, {
            status: 500
        });
    }
}
async function GET() {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user || session.user.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        // Check AI service availability
        const aiStatus = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiService"].isAvailable();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            available: aiStatus.available,
            service: aiStatus.service,
            error: aiStatus.error,
            supportedFormats: [
                'application/pdf'
            ],
            maxFileSize: '10MB',
            features: [
                'Product name extraction',
                'Reference number extraction',
                'Technical characteristics extraction',
                'Category and brand detection',
                'Free local AI with Ollama/Llama',
                'OpenAI fallback support'
            ]
        });
    } catch (error) {
        console.error('Error getting extraction info:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__371fc2c7._.js.map