{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\n\nexport function useAuth() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading = status === 'loading';\n  const user = session?.user;\n\n  const isClient = isAuthenticated && user?.role === 'CLIENT';\n  const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';\n  const isAdmin = isAuthenticated && user?.role === 'ADMIN';\n\n  const login = async (username: string, password: string) => {\n    const result = await signIn('credentials', {\n      username,\n      password,\n      redirect: false,\n    });\n\n    return result;\n  };\n\n  const logout = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const redirectToLogin = () => {\n    router.push('/auth/signin');\n  };\n\n  const redirectToDashboard = () => {\n    if (isAdmin) {\n      router.push('/admin/quotes');\n    } else if (isCommercial) {\n      router.push('/commercial/quotes');\n    } else if (isClient) {\n      router.push('/account/quotes');\n    } else {\n      router.push('/');\n    }\n  };\n\n  return {\n    session,\n    status,\n    user,\n    isAuthenticated,\n    isLoading,\n    isClient,\n    isCommercial,\n    isAdmin,\n    login,\n    logout,\n    redirectToLogin,\n    redirectToDashboard,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,WAAW;IACnC,MAAM,YAAY,WAAW;IAC7B,MAAM,OAAO,SAAS;IAEtB,MAAM,WAAW,mBAAmB,MAAM,SAAS;IACnD,MAAM,eAAe,mBAAmB,MAAM,SAAS;IACvD,MAAM,UAAU,mBAAmB,MAAM,SAAS;IAElD,MAAM,QAAQ,OAAO,UAAkB;QACrC,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;YACzC;YACA;YACA,UAAU;QACZ;QAEA,OAAO;IACT;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,cAAc;YACvB,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,UAAU;YACnB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzDgB;;QACoB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/shared/LoadingAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface LoadingAnimationProps {\n  isLoading?: boolean;\n  onLoadingComplete?: () => void;\n}\n\nexport default function LoadingAnimation({\n  isLoading = true,\n  onLoadingComplete\n}: LoadingAnimationProps) {\n  const [loading, setLoading] = useState(isLoading);\n  const [fadeOut, setFadeOut] = useState(false);\n\n  useEffect(() => {\n    if (isLoading) {\n      // Réduire le délai initial à 300ms\n      const timer = setTimeout(() => {\n        setFadeOut(true);\n\n        // Attendre la fin de l'animation de fade out avant de cacher complètement\n        const hideTimer = setTimeout(() => {\n          setLoading(false);\n          if (onLoadingComplete) onLoadingComplete();\n        }, 300); // Durée de l'animation de fade out réduite\n\n        return () => clearTimeout(hideTimer);\n      }, 300); // <PERSON><PERSON>lai initial réduit\n\n      return () => clearTimeout(timer);\n    } else {\n      // Si isLoading est false dès le départ, cacher immédiatement\n      setLoading(false);\n    }\n  }, [isLoading, onLoadingComplete]);\n\n  if (!loading) {\n    return null;\n  }\n\n  return (\n    <div\n      className={`fixed inset-0 z-50 flex items-center justify-center bg-[#1a1a1a] transition-opacity duration-500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`}\n    >\n          <style jsx global>{`\n            .loading-svg #ISpzKj7McrRg {\n              opacity: 0;\n              animation: opacitypath 1s ease-in-out forwards;\n            }\n\n            .loading-svg #IMb5qOBkYi8y2 {\n              opacity: 0;\n              animation: opacitypath2 1.2s ease-in-out forwards;\n            }\n\n            .loading-svg #IMb5qOBkYi8y {\n              filter: drop-shadow(3px 3px 2px rgba(255, 255, 255, 0.7));\n              animation: animationshadow 2s ease-in-out infinite;\n            }\n\n            .loading-svg #eGByPCsADLe4 {\n              transform-origin: center;\n              animation: movementAnimation 1s ease-in-out forwards;\n            }\n\n            .loading-svg #rOMyCEsDLSe1 {\n              stroke-dasharray: 300;\n              stroke-dashoffset: 300;\n              animation: animationpath 1s ease-in-out forwards;\n            }\n\n            @keyframes animationpath {\n              0% {\n                stroke-dashoffset: 300;\n              }\n              33% {\n                stroke-dashoffset: 225;\n              }\n              66% {\n                stroke-dashoffset: 182;\n              }\n              99% {\n                stroke-dashoffset: 120;\n              }\n              100% {\n                stroke-dashoffset: 99;\n              }\n            }\n\n            @keyframes opacitypath {\n              80% {\n                opacity: 0;\n              }\n              100% {\n                opacity: 1;\n              }\n            }\n\n            @keyframes opacitypath2 {\n              80% {\n                opacity: 0;\n              }\n              100% {\n                opacity: 1;\n              }\n            }\n\n            @keyframes movementAnimation {\n              0% {\n                transform: translate(-115px, 58px);\n              }\n              33.33% {\n                transform: translate(-65px, 5px);\n              }\n              66.66% {\n                transform: translate(-34px, 31px);\n              }\n              99.99% {\n                transform: translate(25px, -23px);\n              }\n              100% {\n                transform: translate(25px, -23px);\n              }\n            }\n\n            @keyframes animationshadow {\n              0% {\n                filter: drop-shadow(-10px 0px 10px rgba(255, 255, 255, 0.7));\n              }\n              50% {\n                filter: drop-shadow(4px -8px 14px rgba(255, 255, 255, 0.7));\n              }\n              100% {\n                filter: drop-shadow(-10px 0px 10px rgba(255, 255, 255, 0.7));\n              }\n            }\n          `}</style>\n\n          <svg\n            className=\"loading-svg\"\n            id=\"eGByPCsADLe1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n            viewBox=\"0 0 264.58333 264.58333\"\n            width=\"120\"\n            height=\"120\"\n          >\n            <path\n              id=\"ISpzKj7McrRg\"\n              d=\"M122.48,78.945c-19.782763,0-38.755276,7.858672-52.743802,21.847198s-21.847198,32.961039-21.847198,52.743802s7.858672,38.755276,21.847198,52.743802s32.961039,21.847198,52.743802,21.847198s38.755276-7.858672,52.743802-21.847198s21.847198-32.961039,21.847198-52.743802c0-41.195472-33.395528-74.591-74.591-74.591Zm-14.202,4.7329c-20.059262,13.206865-32.143233,35.606447-32.165,59.623c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c4.775729-.058974,9.533518-.596562,14.202-1.6047-11.659545,7.704127-25.322088,11.820586-39.297,11.84-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.085181-33.925568,24.011087-63.115508,57.26-69.858v-.0003Z\"\n              transform=\"translate(25.238-21.244)\"\n              fill=\"#006db7\"\n            />\n            <g id=\"IMb5qOBkYi8y2\">\n              <path\n                id=\"IMb5qOBkYi8y\"\n                d=\"M108.28,83.678c-33.248913,6.742492-57.174819,35.932432-57.26,69.858c0,18.953166,7.529116,37.13006,20.931028,50.531972s31.578806,20.931028,50.531972,20.931028c13.974912-.019414,27.637455-4.135873,39.297-11.84-4.668482,1.008138-9.426271,1.545726-14.202,1.6047-18.953166,0-37.13006-7.529116-50.531972-20.931028s-20.931028-31.578806-20.931028-50.531972c.021767-24.016553,12.105738-46.416135,32.165-59.623v.0003Z\"\n                transform=\"translate(25.238-21.244)\"\n                fill=\"#fff\"\n              />\n            </g>\n            <path\n              id=\"eGByPCsADLe4\"\n              d=\"M131.51,121l40.383.30632.2758,43.221\"\n              transform=\"translate(-122.494724 62.610263)\"\n              fill=\"none\"\n              stroke=\"#ed1c24\"\n              strokeWidth=\"24.133\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n            />\n            <path\n              id=\"rOMyCEsDLSe1\"\n              d=\"M29.098,200.61l53.006-53.126l30.765,31.282l58.655-57.504\"\n              transform=\"translate(25.238-21.244)\"\n              fill=\"none\"\n              stroke=\"#ed1c24\"\n              strokeWidth=\"24.133\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeDasharray=\"480\"\n            />\n          </svg>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AASe,SAAS,iBAAiB,EACvC,YAAY,IAAI,EAChB,iBAAiB,EACK;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;gBACb,mCAAmC;gBACnC,MAAM,QAAQ;wDAAW;wBACvB,WAAW;wBAEX,0EAA0E;wBAC1E,MAAM,YAAY;0EAAW;gCAC3B,WAAW;gCACX,IAAI,mBAAmB;4BACzB;yEAAG,MAAM,2CAA2C;wBAEpD;gEAAO,IAAM,aAAa;;oBAC5B;uDAAG,MAAM,uBAAuB;gBAEhC;kDAAO,IAAM,aAAa;;YAC5B,OAAO;gBACL,6DAA6D;gBAC7D,WAAW;YACb;QACF;qCAAG;QAAC;QAAW;KAAkB;IAEjC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC;kDACY,CAAC,iGAAiG,EAAE,UAAU,cAAc,eAAe;;;;;;0BAgGlJ,6LAAC;gBAEC,IAAG;gBACH,OAAM;gBACN,YAAW;gBACX,SAAQ;gBACR,OAAM;gBACN,QAAO;0DANG;;kCAQV,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;;;;;;;kCAEP,6LAAC;wBAAE,IAAG;;kCACJ,cAAA,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,WAAU;4BACV,MAAK;;;;;;;;;;;;kCAGT,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;kCAEjB,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,iBAAgB;;;;;;;;;;;;;;;;;;;AAK9B;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/auth/RouteGuard.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { useEffect, ReactNode } from 'react';\nimport { usePathname } from 'next/navigation';\nimport LoadingAnimation from '@/components/shared/LoadingAnimation';\nimport { UserRole } from '@prisma/client';\n\ntype RouteGuardProps = {\n  children: ReactNode;\n  allowedRoles?: UserRole[];\n};\n\nexport default function RouteGuard({\n  children,\n  allowedRoles = [],\n}: RouteGuardProps) {\n  const { isAuthenticated, isLoading, user, redirectToLogin } = useAuth();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    // Si le chargement est terminé et que l'utilisateur n'est pas authentifié\n    if (!isLoading && !isAuthenticated) {\n      // Stocker le chemin actuel pour rediriger après la connexion\n      sessionStorage.setItem('redirectAfterLogin', pathname);\n      redirectToLogin();\n    }\n  }, [isLoading, isAuthenticated, pathname, redirectToLogin]);\n\n  useEffect(() => {\n    // Si l'utilisateur est authentifié mais n'a pas le rôle requis\n    if (\n      isAuthenticated &&\n      allowedRoles.length > 0 &&\n      user?.role &&\n      !allowedRoles.includes(user.role)\n    ) {\n      // Rediriger vers la page d'accueil ou une page d'erreur\n      window.location.href = '/unauthorized';\n    }\n  }, [isAuthenticated, allowedRoles, user]);\n\n  // Afficher un écran de chargement pendant la vérification\n  if (isLoading || !isAuthenticated) {\n    return <LoadingAnimation isLoading={true} onLoadingComplete={() => {}} />;\n  }\n\n  // Si l'utilisateur est authentifié et a le rôle requis (ou aucun rôle n'est requis)\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,WAAW,EACjC,QAAQ,EACR,eAAe,EAAE,EACD;;IAChB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACpE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,0EAA0E;YAC1E,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,6DAA6D;gBAC7D,eAAe,OAAO,CAAC,sBAAsB;gBAC7C;YACF;QACF;+BAAG;QAAC;QAAW;QAAiB;QAAU;KAAgB;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,+DAA+D;YAC/D,IACE,mBACA,aAAa,MAAM,GAAG,KACtB,MAAM,QACN,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAChC;gBACA,wDAAwD;gBACxD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;+BAAG;QAAC;QAAiB;QAAc;KAAK;IAExC,0DAA0D;IAC1D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBAAO,6LAAC,mJAAA,CAAA,UAAgB;YAAC,WAAW;YAAM,mBAAmB,KAAO;;;;;;IACtE;IAEA,oFAAoF;IACpF,qBAAO;kBAAG;;AACZ;GApCwB;;QAIwC,0HAAA,CAAA,UAAO;QACpD,qIAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/components/chat/ChatWindow.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useSession } from 'next-auth/react';\n\ninterface ChatUser {\n  id: string;\n  userId: string;\n  name: string;\n  email: string;\n  role: 'ADMIN' | 'COMMERCIAL';\n  profilePhoto?: string;\n}\n\ninterface ChatMessage {\n  id: string;\n  content: string;\n  senderType: 'ADMIN' | 'COMMERCIAL';\n  senderId: string;\n  messageType: 'TEXT' | 'FILE' | 'IMAGE';\n  fileUrl?: string;\n  fileName?: string;\n  isRead: boolean;\n  createdAt: string;\n}\n\ninterface ChatConversation {\n  id: string;\n  admin: { user: { id: string; firstname: string; lastname: string } };\n  commercial: { user: { id: string; firstname: string; lastname: string } };\n  lastMessage?: string;\n  lastMessageAt?: string;\n}\n\nexport default function ChatWindow() {\n  const { data: session } = useSession();\n  const [isOpen, setIsOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState<'conversations' | 'users'>('conversations');\n  const [conversations, setConversations] = useState<ChatConversation[]>([]);\n  const [availableUsers, setAvailableUsers] = useState<ChatUser[]>([]);\n  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  // Load conversations and users on mount\n  useEffect(() => {\n    if (session?.user && isOpen) {\n      loadConversations();\n      loadAvailableUsers();\n    }\n  }, [session, isOpen]);\n\n  // Load messages when conversation is selected\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation);\n    }\n  }, [selectedConversation]);\n\n  const loadConversations = async () => {\n    try {\n      const response = await fetch('/api/chat/conversations');\n      if (response.ok) {\n        const data = await response.json();\n        setConversations(data.conversations);\n      }\n    } catch (error) {\n      console.error('Error loading conversations:', error);\n    }\n  };\n\n  const loadAvailableUsers = async () => {\n    try {\n      const response = await fetch('/api/chat/users');\n      if (response.ok) {\n        const data = await response.json();\n        setAvailableUsers(data.users);\n      }\n    } catch (error) {\n      console.error('Error loading users:', error);\n    }\n  };\n\n  const loadMessages = async (conversationId: string) => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/chat/messages?conversationId=${conversationId}`);\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages);\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !selectedConversation) return;\n\n    try {\n      const response = await fetch('/api/chat/messages', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          conversationId: selectedConversation,\n          content: newMessage,\n          messageType: 'TEXT',\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(prev => [...prev, data.message]);\n        setNewMessage('');\n        loadConversations(); // Refresh conversations to update last message\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const startConversation = async (userId: string) => {\n    try {\n      const response = await fetch('/api/chat/conversations', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ participantId: userId }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSelectedConversation(data.conversation.id);\n        setActiveTab('conversations');\n        loadConversations();\n      }\n    } catch (error) {\n      console.error('Error starting conversation:', error);\n    }\n  };\n\n  const formatTime = (dateString: string) => {\n    return new Date(dateString).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const getConversationName = (conversation: ChatConversation) => {\n    if (session?.user?.role === 'ADMIN') {\n      return `${conversation.commercial.user.firstname} ${conversation.commercial.user.lastname}`;\n    } else {\n      return `${conversation.admin.user.firstname} ${conversation.admin.user.lastname}`;\n    }\n  };\n\n  if (!session?.user || (session.user.role !== 'ADMIN' && session.user.role !== 'COMMERCIAL')) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Chat Toggle Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-50\"\n      >\n        <span className=\"text-2xl\">💬</span>\n      </button>\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"fixed bottom-24 right-6 w-96 h-[500px] bg-white border border-gray-200 rounded-lg shadow-xl z-50 flex flex-col\">\n          {/* Header */}\n          <div className=\"bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between\">\n            <h3 className=\"font-semibold\">💬 Chat Moonelec</h3>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"text-white hover:text-gray-200 text-xl\"\n            >\n              ✕\n            </button>\n          </div>\n\n          {/* Tabs */}\n          <div className=\"flex border-b\">\n            <button\n              onClick={() => setActiveTab('conversations')}\n              className={`flex-1 p-3 text-sm font-medium ${\n                activeTab === 'conversations'\n                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <span className=\"inline mr-2\">💬</span>\n              Conversations\n            </button>\n            <button\n              onClick={() => setActiveTab('users')}\n              className={`flex-1 p-3 text-sm font-medium ${\n                activeTab === 'users'\n                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <span className=\"inline mr-2\">👥</span>\n              Utilisateurs\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1 overflow-hidden\">\n            {activeTab === 'conversations' && !selectedConversation && (\n              <div className=\"p-4 h-full overflow-y-auto\">\n                {conversations.length === 0 ? (\n                  <div className=\"text-center text-gray-500 mt-8\">\n                    <div className=\"text-5xl mb-4 text-gray-300\">💬</div>\n                    <p>Aucune conversation</p>\n                    <p className=\"text-sm\">Commencez une nouvelle conversation</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    {conversations.map((conversation) => (\n                      <div\n                        key={conversation.id}\n                        onClick={() => setSelectedConversation(conversation.id)}\n                        className=\"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\"\n                      >\n                        <div className=\"font-medium text-sm\">\n                          {getConversationName(conversation)}\n                        </div>\n                        {conversation.lastMessage && (\n                          <div className=\"text-xs text-gray-500 mt-1 truncate\">\n                            {conversation.lastMessage}\n                          </div>\n                        )}\n                        {conversation.lastMessageAt && (\n                          <div className=\"text-xs text-gray-400 mt-1\">\n                            {formatTime(conversation.lastMessageAt)}\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'users' && (\n              <div className=\"p-4 h-full overflow-y-auto\">\n                <div className=\"space-y-2\">\n                  {availableUsers.map((user) => (\n                    <div\n                      key={user.id}\n                      onClick={() => startConversation(user.id)}\n                      className=\"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors\"\n                    >\n                      <div className=\"font-medium text-sm\">{user.name}</div>\n                      <div className=\"text-xs text-gray-500\">{user.role}</div>\n                      <div className=\"text-xs text-gray-400\">{user.email}</div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {selectedConversation && (\n              <div className=\"h-full flex flex-col\">\n                {/* Messages */}\n                <div className=\"flex-1 p-4 overflow-y-auto\">\n                  {loading ? (\n                    <div className=\"text-center text-gray-500\">Chargement...</div>\n                  ) : (\n                    <div className=\"space-y-3\">\n                      {messages.map((message) => (\n                        <div\n                          key={message.id}\n                          className={`flex ${\n                            message.senderId === session?.user?.id ? 'justify-end' : 'justify-start'\n                          }`}\n                        >\n                          <div\n                            className={`max-w-xs px-3 py-2 rounded-lg text-sm ${\n                              message.senderId === session?.user?.id\n                                ? 'bg-blue-600 text-white'\n                                : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white'\n                            }`}\n                          >\n                            <div>{message.content}</div>\n                            <div\n                              className={`text-xs mt-1 ${\n                                message.senderId === session?.user?.id\n                                  ? 'text-blue-100'\n                                  : 'text-gray-500 dark:text-gray-400'\n                              }`}\n                            >\n                              {formatTime(message.createdAt)}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                      <div ref={messagesEndRef} />\n                    </div>\n                  )}\n                </div>\n\n                {/* Message Input */}\n                <div className=\"p-4 border-t\">\n                  <div className=\"flex space-x-2\">\n                    <input\n                      type=\"text\"\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}\n                      placeholder=\"Tapez votre message...\"\n                      className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    />\n                    <button\n                      onClick={sendMessage}\n                      disabled={!newMessage.trim()}\n                      className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors\"\n                    >\n                      <span className=\"text-lg\">📤</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAkCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;+BAAG;QAAC;KAAS;IAEb,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS,QAAQ,QAAQ;gBAC3B;gBACA;YACF;QACF;+BAAG;QAAC;QAAS;KAAO;IAEpB,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,sBAAsB;gBACxB,aAAa;YACf;QACF;+BAAG;QAAC;KAAqB;IAEzB,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB,KAAK,aAAa;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,kBAAkB,KAAK,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,kCAAkC,EAAE,gBAAgB;YAClF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,sBAAsB;QAEjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,CAAA,OAAQ;2BAAI;wBAAM,KAAK,OAAO;qBAAC;gBAC3C,cAAc;gBACd,qBAAqB,+CAA+C;YACtE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,eAAe;gBAAO;YAC/C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,wBAAwB,KAAK,YAAY,CAAC,EAAE;gBAC5C,aAAa;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,MAAM,SAAS,SAAS;YACnC,OAAO,GAAG,aAAa,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC7F,OAAO;YACL,OAAO,GAAG,aAAa,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QACnF;IACF;IAEA,IAAI,CAAC,SAAS,QAAS,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAe;QAC3F,OAAO;IACT;IAEA,qBACE;;0BAEE,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;0BAEV,cAAA,6LAAC;oBAAK,WAAU;8BAAW;;;;;;;;;;;YAI5B,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,+BAA+B,EACzC,cAAc,kBACV,wDACA,qCACJ;;kDAEF,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAS;;;;;;;0CAGzC,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,+BAA+B,EACzC,cAAc,UACV,wDACA,qCACJ;;kDAEF,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAS;;;;;;;;;;;;;kCAM3C,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,mBAAmB,CAAC,sCACjC,6LAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;sDAAE;;;;;;sDACH,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;yDAGzB,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;4CAEC,SAAS,IAAM,wBAAwB,aAAa,EAAE;4CACtD,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACZ,oBAAoB;;;;;;gDAEtB,aAAa,WAAW,kBACvB,6LAAC;oDAAI,WAAU;8DACZ,aAAa,WAAW;;;;;;gDAG5B,aAAa,aAAa,kBACzB,6LAAC;oDAAI,WAAU;8DACZ,WAAW,aAAa,aAAa;;;;;;;2CAdrC,aAAa,EAAE;;;;;;;;;;;;;;;4BAwB/B,cAAc,yBACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC;4CAEC,SAAS,IAAM,kBAAkB,KAAK,EAAE;4CACxC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DAAuB,KAAK,IAAI;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;;2CAN7C,KAAK,EAAE;;;;;;;;;;;;;;;4BAarB,sCACC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,wBACC,6LAAC;4CAAI,WAAU;sDAA4B;;;;;iEAE3C,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wDAEC,WAAW,CAAC,KAAK,EACf,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAAK,gBAAgB,iBACzD;kEAEF,cAAA,6LAAC;4DACC,WAAW,CAAC,sCAAsC,EAChD,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAChC,2BACA,8DACJ;;8EAEF,6LAAC;8EAAK,QAAQ,OAAO;;;;;;8EACrB,6LAAC;oEACC,WAAW,CAAC,aAAa,EACvB,QAAQ,QAAQ,KAAK,SAAS,MAAM,KAChC,kBACA,oCACJ;8EAED,WAAW,QAAQ,SAAS;;;;;;;;;;;;uDApB5B,QAAQ,EAAE;;;;;8DAyBnB,6LAAC;oDAAI,KAAK;;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;oDACxC,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDACC,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI;oDAC1B,WAAU;8DAEV,cAAA,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhD;GAnTwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/commercial/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport {\n  FaTachometerAlt,\n  FaUsers,\n  FaShoppingCart,\n  FaCalendarAlt,\n  FaChartLine,\n  FaUser,\n  FaSignOutAlt,\n  FaBars,\n  FaTimes,\n  FaBell\n} from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport ChatWindow from '@/components/chat/ChatWindow';\n\nexport default function CommercialLayout({ children }: { children: ReactNode }) {\n  const router = useRouter();\n  const { user, logout } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const menuItems = [\n    { icon: <FaTachometerAlt />, label: 'Tableau de bord', href: '/commercial/dashboard' },\n    { icon: <FaUsers />, label: 'Clients', href: '/commercial/clients' },\n    { icon: <FaShoppingCart />, label: 'Commandes', href: '/commercial/orders' },\n    { icon: <FaCalendarAlt />, label: 'Rendez-vous', href: '/commercial/meetings' },\n    { icon: <FaChartLine />, label: 'Performances', href: '/commercial/performance' },\n    { icon: <FaUser />, label: 'Profil', href: '/commercial/profile' },\n  ];\n\n  const handleLogout = async () => {\n    await logout();\n    router.push('/');\n  };\n\n  return (\n    <RouteGuard allowedRoles={['COMMERCIAL']}>\n      <div className=\"min-h-screen bg-gray-100 dark:bg-gray-900\">\n        {/* Header */}\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0 flex items-center\">\n                  <Link href=\"/\">\n                    <Image\n                      src=\"/images/logo/logo-moonelec.png\"\n                      alt=\"Moonelec Logo\"\n                      width={150}\n                      height={40}\n                      className=\"h-10 w-auto\"\n                    />\n                  </Link>\n                </div>\n              </div>\n\n              <div className=\"hidden md:ml-6 md:flex md:items-center md:space-x-4\">\n                {menuItems.map((item, index) => {\n                  const isActive = typeof window !== 'undefined' && window.location.pathname === item.href;\n\n                  return (\n                    <Link\n                      key={index}\n                      href={item.href}\n                      className={`px-3 py-2 rounded-md text-sm font-medium ${\n                        isActive\n                          ? 'bg-primary text-white'\n                          : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'\n                      }`}\n                    >\n                      <span className=\"flex items-center\">\n                        <span className=\"mr-2\">{item.icon}</span>\n                        {item.label}\n                      </span>\n                    </Link>\n                  );\n                })}\n              </div>\n\n              <div className=\"flex items-center\">\n                <button className=\"p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 relative\">\n                  <FaBell />\n                  <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n                </button>\n\n                <div className=\"ml-3 relative\">\n                  <div className=\"flex items-center\">\n                    <button\n                      onClick={() => router.push('/commercial/profile')}\n                      className=\"flex items-center space-x-2 focus:outline-none\"\n                    >\n                      <div className=\"w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden\">\n                        {user?.commercialId ? (\n                          <Image\n                            src=\"/images/commercial/profile-photo.jpg\"\n                            alt=\"Commercial Avatar\"\n                            width={32}\n                            height={32}\n                            className=\"w-full h-full object-cover\"\n                          />\n                        ) : (\n                          <FaUser className=\"w-full h-full text-gray-400 p-1\" />\n                        )}\n                      </div>\n                      <span className=\"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        {user?.firstname}\n                      </span>\n                    </button>\n                  </div>\n                </div>\n\n                <button\n                  onClick={handleLogout}\n                  className=\"ml-4 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hidden md:block\"\n                >\n                  <FaSignOutAlt />\n                </button>\n\n                <div className=\"ml-4 md:hidden\">\n                  <button\n                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                    className=\"p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none\"\n                  >\n                    {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile menu */}\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"md:hidden\"\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n                {menuItems.map((item, index) => {\n                  const isActive = typeof window !== 'undefined' && window.location.pathname === item.href;\n\n                  return (\n                    <Link\n                      key={index}\n                      href={item.href}\n                      className={`block px-3 py-2 rounded-md text-base font-medium ${\n                        isActive\n                          ? 'bg-primary text-white'\n                          : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'\n                      }`}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      <span className=\"flex items-center\">\n                        <span className=\"mr-2\">{item.icon}</span>\n                        {item.label}\n                      </span>\n                    </Link>\n                  );\n                })}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"w-full text-left block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700\"\n                >\n                  <span className=\"flex items-center\">\n                    <span className=\"mr-2\"><FaSignOutAlt /></span>\n                    Déconnexion\n                  </span>\n                </button>\n              </div>\n            </motion.div>\n          )}\n        </header>\n\n        {/* Main Content */}\n        <main className=\"py-6\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n\n        {/* Chat Window */}\n        <ChatWindow />\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAYA;AACA;AACA;;;AArBA;;;;;;;;;;AAuBe,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;;IAC5E,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,YAAY;QAChB;YAAE,oBAAM,6LAAC,iJAAA,CAAA,kBAAe;;;;;YAAK,OAAO;YAAmB,MAAM;QAAwB;QACrF;YAAE,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;YAAK,OAAO;YAAW,MAAM;QAAsB;QACnE;YAAE,oBAAM,6LAAC,iJAAA,CAAA,iBAAc;;;;;YAAK,OAAO;YAAa,MAAM;QAAqB;QAC3E;YAAE,oBAAM,6LAAC,iJAAA,CAAA,gBAAa;;;;;YAAK,OAAO;YAAe,MAAM;QAAuB;QAC9E;YAAE,oBAAM,6LAAC,iJAAA,CAAA,cAAW;;;;;YAAK,OAAO;YAAgB,MAAM;QAA0B;QAChF;YAAE,oBAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;YAAK,OAAO;YAAU,MAAM;QAAsB;KAClE;IAED,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,2IAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAa;kBACtC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,MAAM;4CACpB,MAAM,WAAW,aAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI;4CAExF,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,yCAAyC,EACnD,WACI,0BACA,6EACJ;0DAEF,cAAA,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DAAK,WAAU;sEAAQ,KAAK,IAAI;;;;;;wDAChC,KAAK,KAAK;;;;;;;+CAVR;;;;;wCAcX;;;;;;kDAGF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC,iJAAA,CAAA,SAAM;;;;;kEACP,6LAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGlB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS,IAAM,OAAO,IAAI,CAAC;wDAC3B,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACZ,MAAM,6BACL,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAI;oEACJ,KAAI;oEACJ,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;yFAGZ,6LAAC,iJAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAGtB,6LAAC;gEAAK,WAAU;0EACb,MAAM;;;;;;;;;;;;;;;;;;;;;;0DAMf,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,iJAAA,CAAA,eAAY;;;;;;;;;;0DAGf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS,IAAM,oBAAoB,CAAC;oDACpC,WAAU;8DAET,iCAAmB,6LAAC,iJAAA,CAAA,UAAO;;;;6EAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQlD,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,UAAU,GAAG,CAAC,CAAC,MAAM;wCACpB,MAAM,WAAW,aAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAAI;wCAExF,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,iDAAiD,EAC3D,WACI,0BACA,6EACJ;4CACF,SAAS,IAAM,oBAAoB;sDAEnC,cAAA,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;kEAAQ,KAAK,IAAI;;;;;;oDAChC,KAAK,KAAK;;;;;;;2CAXR;;;;;oCAeX;kDAEA,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAU;8DAAO,cAAA,6LAAC,iJAAA,CAAA,eAAY;;;;;;;;;;gDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU1D,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;8BAKL,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;AAInB;GA3KwB;;QACP,qIAAA,CAAA,YAAS;QACC,0HAAA,CAAA,UAAO;;;KAFV", "debugId": null}}]}