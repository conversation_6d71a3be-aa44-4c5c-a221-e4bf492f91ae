'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaArrowLeft, FaTag, Fa<PERSON><PERSON>right, FaSpinner, FaExclamationTriangle, FaShoppingCart, FaInfoCircle, FaCheck, FaLightbulb } from 'react-icons/fa';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import PageLayout from '@/components/layout/PageLayout';
import { useCart } from '@/context/CartContext';
import AddToCartAnimation from '@/components/cart/AddToCartAnimation';

interface Product {
  id: string;
  reference: string;
  name: string;
  description: string;
  characteristics: Record<string, any>;
  mainImage: string | null;
  category: {
    id: string;
    name: string;
  } | null;
  brand: {
    id: string;
    name: string;
  } | null;
  productimage: {
    id: string;
    url: string;
    alt: string;
    order: number;
  }[];
}

export default function ProductDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { addItem } = useCart();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationStartPosition, setAnimationStartPosition] = useState<{ x: number; y: number } | null>(null);
  const [animationEndPosition, setAnimationEndPosition] = useState<{ x: number; y: number } | null>(null);
  const addToCartButtonRef = useRef<HTMLButtonElement>(null);

  // Fonction pour ajouter le produit au panier avec animation
  const addToCart = (quantity: number = 1) => {
    if (product) {
      // Calculer les positions pour l'animation
      const buttonElement = addToCartButtonRef.current;
      const cartIcon = document.querySelector('.navbar-cart-icon') as HTMLElement;

      if (buttonElement && cartIcon) {
        const buttonRect = buttonElement.getBoundingClientRect();
        const cartRect = cartIcon.getBoundingClientRect();

        setAnimationStartPosition({
          x: buttonRect.left + buttonRect.width / 2,
          y: buttonRect.top + buttonRect.height / 2
        });

        setAnimationEndPosition({
          x: cartRect.left + cartRect.width / 2,
          y: cartRect.top + cartRect.height / 2
        });

        // Démarrer l'animation
        setIsAnimating(true);

        // Ajouter au panier après un court délai pour que l'animation soit visible
        setTimeout(() => {
          addItem(product, quantity);
        }, 300);
      } else {
        // Fallback si les éléments ne sont pas trouvés
        addItem(product, quantity);
      }
    }
  };

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/products/${id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch product');
        }

        const data = await response.json();
        setProduct(data);
        setSelectedImage(data.mainImage || (data.productimage.length > 0 ? data.productimage[0].url : null));
      } catch (err: any) {
        console.error('Error fetching product:', err);
        setError(err.message || 'An error occurred while fetching the product');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id]);

  // Organiser les caractéristiques en groupes
  const characteristicsGroups: Record<string, Record<string, any>> = {};
  if (product && product.characteristics) {
    Object.entries(product.characteristics).forEach(([key, value]) => {
      const groupMatch = key.match(/^([^_]+)_(.+)$/);
      if (groupMatch) {
        const [, group, subKey] = groupMatch;
        if (!characteristicsGroups[group]) {
          characteristicsGroups[group] = {};
        }
        characteristicsGroups[group][subKey] = value;
      } else {
        if (!characteristicsGroups['general']) {
          characteristicsGroups['general'] = {};
        }
        characteristicsGroups['general'][key] = value;
      }
    });
  }

  // Extraire les caractéristiques principales pour l'affichage en haut
  const mainFeatures = product ? Object.entries(product.characteristics || {}).slice(0, 4) : [];

  return (
    <PageLayout showLoading={false}>
      {isLoading ? (
        <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[60vh]">
          <div className="relative w-20 h-20">
            <div className="absolute top-0 left-0 w-full h-full border-8 border-gray-200 dark:border-gray-700 rounded-full"></div>
            <div className="absolute top-0 left-0 w-full h-full border-8 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
          </div>
          <p className="mt-4 text-gray-500 dark:text-gray-400 text-lg">Chargement du produit...</p>
        </div>
      ) : error || !product ? (
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-6 rounded-xl border border-red-100 dark:border-red-900/30 flex items-center mb-6 shadow-sm">
            <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mr-4">
              <FaExclamationTriangle className="text-red-500 dark:text-red-300 text-xl" />
            </div>
            <div>
              <h3 className="font-semibold text-red-800 dark:text-red-200 text-lg mb-1">Produit non trouvé</h3>
              <p>{error || 'Le produit que vous recherchez n\'existe pas ou a été supprimé.'}</p>
            </div>
          </div>
          <Link href="/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center px-5 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium"
            >
              <FaArrowLeft className="mr-2" />
              Retour aux produits
            </motion.button>
          </Link>
        </div>
      ) : (
          <>
            {/* Hero Section with Breadcrumb */}
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 py-8 mb-8 border-b border-gray-200 dark:border-gray-800">
              <div className="container mx-auto px-4">
                {/* Breadcrumb */}
                <nav className="mb-4 text-sm">
                  <ol className="flex items-center space-x-2">
                    <li>
                      <Link href="/" className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light transition-colors">
                        Accueil
                      </Link>
                    </li>
                    <li className="text-gray-500 dark:text-gray-400">/</li>
                    <li>
                      <Link href="/products" className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light transition-colors">
                        Produits
                      </Link>
                    </li>
                    <li className="text-gray-500 dark:text-gray-400">/</li>
                    <li className="text-gray-800 dark:text-white font-medium truncate max-w-[200px]">
                      {product.name}
                    </li>
                  </ol>
                </nav>

                <div className="flex items-center justify-between">
                  <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white">
                    Détails du produit
                  </h1>

                  <Link href="/products">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700 shadow-sm"
                    >
                      <FaArrowLeft className="mr-2" />
                      Retour aux produits
                    </motion.button>
                  </Link>
                </div>
              </div>
            </div>

            <div className="container mx-auto px-4 pb-16">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Product Images */}
                  <div className="p-6 md:p-8">
                    <div className="relative h-96 w-full bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden mb-6 border border-gray-100 dark:border-gray-700">
                      {selectedImage ? (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                          className="h-full w-full"
                        >
                          <Image
                            src={selectedImage}
                            alt={product.name}
                            fill
                            style={{ objectFit: 'contain' }}
                            className="p-6"
                          />
                        </motion.div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-gray-400">
                          Aucune image disponible
                        </div>
                      )}

                      {/* Brand logo overlay */}
                      {product.brand && product.brand.name && (
                        <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 rounded-lg p-2 shadow-md border border-gray-100 dark:border-gray-700">
                          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Marque</div>
                          <div className="font-semibold text-gray-800 dark:text-white">{product.brand.name}</div>
                        </div>
                      )}
                    </div>

                    {/* Thumbnails */}
                    {(product.productimage.length > 0 || product.mainImage) && (
                      <div className="flex flex-wrap gap-3 justify-center">
                        {product.mainImage && (
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setSelectedImage(product.mainImage)}
                            className={`relative h-20 w-20 rounded-lg overflow-hidden flex-shrink-0 border-2 ${
                              selectedImage === product.mainImage
                                ? 'border-primary shadow-md'
                                : 'border-gray-200 dark:border-gray-700'
                            } transition-all duration-200`}
                          >
                            <Image
                              src={product.mainImage}
                              alt={`${product.name} - Image principale`}
                              fill
                              style={{ objectFit: 'cover' }}
                            />
                          </motion.button>
                        )}

                        {product.productimage.map((image) => (
                          <motion.button
                            key={image.id}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setSelectedImage(image.url)}
                            className={`relative h-20 w-20 rounded-lg overflow-hidden flex-shrink-0 border-2 ${
                              selectedImage === image.url
                                ? 'border-primary shadow-md'
                                : 'border-gray-200 dark:border-gray-700'
                            } transition-all duration-200`}
                          >
                            <Image
                              src={image.url}
                              alt={image.alt || `${product.name} - Image ${image.order}`}
                              fill
                              style={{ objectFit: 'cover' }}
                            />
                          </motion.button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="p-6 md:p-8 bg-gray-50 dark:bg-gray-800/50 border-t lg:border-t-0 lg:border-l border-gray-100 dark:border-gray-700">
                    <div className="flex flex-wrap gap-3 mb-4">
                      {product.category && (
                        <span className="px-3 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300 rounded-full text-sm flex items-center font-medium">
                          <FaTag className="mr-1" />
                          {product.category.name}
                        </span>
                      )}

                      <span className="px-3 py-1 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-300 rounded-full text-sm flex items-center font-medium">
                        <FaCheck className="mr-1" />
                        En stock
                      </span>
                    </div>

                    <h1 className="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white mb-3">
                      {product.name}
                    </h1>

                    <p className="text-gray-500 dark:text-gray-400 mb-6 flex items-center">
                      <span className="font-medium mr-2">Référence:</span>
                      <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-gray-700 dark:text-gray-300 text-sm">{product.reference}</span>
                    </p>

                    <div className="mb-8">
                      <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-3 flex items-center">
                        <FaInfoCircle className="mr-2 text-primary" />
                        Description
                      </h2>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {product.description}
                      </p>
                    </div>

                    {/* Main Features */}
                    {mainFeatures.length > 0 && (
                      <div className="mb-8">
                        <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-3 flex items-center">
                          <FaLightbulb className="mr-2 text-yellow-500" />
                          Caractéristiques principales
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {mainFeatures.map(([key, value]) => (
                            <div key={key} className="flex items-start">
                              <div className="w-2 h-2 rounded-full bg-primary mt-1.5 mr-2"></div>
                              <div>
                                <span className="font-medium text-gray-700 dark:text-gray-300 capitalize">{key.replace(/_/g, ' ')}: </span>
                                <span className="text-gray-600 dark:text-gray-400">{String(value)}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                      <div className="flex flex-col md:flex-row gap-4">
                        <motion.button
                          ref={addToCartButtonRef}
                          whileHover={{ scale: 1.03 }}
                          whileTap={{ scale: 0.97 }}
                          className="flex-1 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium flex items-center justify-center"
                          onClick={() => addToCart(1)}
                        >
                          <FaShoppingCart className="mr-2" />
                          Ajouter au panier
                        </motion.button>

                        <Link href="/contact" className="flex-1">
                          <motion.button
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            className="w-full px-6 py-3 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors font-medium border border-blue-200 dark:border-blue-900/30"
                          >
                            Demander un devis
                          </motion.button>
                        </Link>
                      </div>

                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-4 text-center">
                        Pour connaître le prix et la disponibilité exacte, veuillez nous contacter.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Characteristics Tabs */}
                {Object.keys(characteristicsGroups).length > 0 && (
                  <div className="border-t border-gray-200 dark:border-gray-700 p-6 md:p-8">
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-6 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      Spécifications techniques
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      {Object.entries(characteristicsGroups).map(([group, characteristics]) => (
                        <motion.div
                          key={group}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-3 capitalize flex items-center">
                            <div className="w-4 h-4 rounded-full bg-primary mr-2"></div>
                            {group === 'general' ? 'Caractéristiques générales' : group.replace(/_/g, ' ')}
                          </h3>

                          <div className="bg-white dark:bg-gray-700 rounded-xl overflow-hidden shadow-sm border border-gray-100 dark:border-gray-600">
                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                              <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                                {Object.entries(characteristics).map(([key, value]) => (
                                  <tr key={key} className="hover:bg-gray-50 dark:hover:bg-gray-600/50 transition-colors">
                                    <td className="px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                                      {key.replace(/_/g, ' ')}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                                      {typeof value === 'boolean'
                                        ? value ? 'Oui' : 'Non'
                                        : String(value)}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Related Products Section */}
              <div className="mt-16">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Produits similaires
                </h2>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {/* Placeholder for related products */}
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-100 dark:border-gray-700 h-64 flex items-center justify-center">
                      <p className="text-gray-400 dark:text-gray-500">Produit similaire {i}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
      )}

      {/* Animation d'ajout au panier */}
      <AddToCartAnimation
        isAnimating={isAnimating}
        productImage={product?.mainImage || null}
        productName={product?.name || ''}
        startPosition={animationStartPosition}
        endPosition={animationEndPosition}
        onAnimationComplete={() => setIsAnimating(false)}
      />
    </PageLayout>
  );
}
