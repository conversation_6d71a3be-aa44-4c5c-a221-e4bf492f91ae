'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function DashboardChart() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Données simulées pour le graphique
  const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
  const currentYear = [
    150000, 180000, 210000, 240000, 270000, 300000, 330000, 360000, 390000, 420000, 450000, 480000
  ];
  const previousYear = [
    120000, 140000, 160000, 180000, 200000, 220000, 240000, 260000, 280000, 300000, 320000, 340000
  ];
  
  // Trouver la valeur maximale pour l'échelle
  const maxValue = Math.max(...currentYear, ...previousYear);
  
  // Calculer la hauteur des barres en pourcentage
  const getBarHeight = (value: number) => {
    return (value / maxValue) * 100;
  };
  
  if (!mounted) {
    return <div className="h-80 bg-gray-100 dark:bg-gray-700 animate-pulse rounded-lg"></div>;
  }
  
  return (
    <div className="h-80">
      <div className="flex items-center justify-between mb-6">
        <div className="flex space-x-4">
          <div className="flex items-center">
            <span className="w-3 h-3 bg-primary rounded-full mr-2"></span>
            <span className="text-sm text-gray-600 dark:text-gray-300">2023</span>
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full mr-2"></span>
            <span className="text-sm text-gray-600 dark:text-gray-300">2022</span>
          </div>
        </div>
        
        <select className="bg-gray-100 dark:bg-gray-700 border-none rounded-md text-sm text-gray-600 dark:text-gray-300 py-1 px-3 focus:outline-none focus:ring-2 focus:ring-primary">
          <option value="year">Annuel</option>
          <option value="month">Mensuel</option>
          <option value="week">Hebdomadaire</option>
        </select>
      </div>
      
      <div className="relative h-64">
        {/* Axe Y */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400">
          {[0, 25, 50, 75, 100].reverse().map((value, index) => (
            <div key={index} className="flex items-center">
              <span className="mr-2">{(maxValue * value / 100).toLocaleString('fr-FR')} MAD</span>
              <div className="w-full border-b border-gray-200 dark:border-gray-700"></div>
            </div>
          ))}
        </div>
        
        {/* Graphique */}
        <div className="ml-24 h-full flex items-end">
          <div className="flex-1 flex justify-around items-end h-full">
            {months.map((month, index) => (
              <div key={index} className="flex flex-col items-center group">
                <div className="relative w-12 flex justify-center">
                  {/* Barre de l'année précédente */}
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${getBarHeight(previousYear[index])}%` }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                    className="absolute bottom-0 w-4 bg-gray-300 dark:bg-gray-600 rounded-t-sm"
                  ></motion.div>
                  
                  {/* Barre de l'année courante */}
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${getBarHeight(currentYear[index])}%` }}
                    transition={{ duration: 0.5, delay: index * 0.05 + 0.2 }}
                    className="absolute bottom-0 w-4 bg-primary rounded-t-sm ml-5"
                  ></motion.div>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-full mb-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-800 text-white text-xs rounded py-1 px-2 pointer-events-none">
                    <div>2022: {previousYear[index].toLocaleString('fr-FR')} MAD</div>
                    <div>2023: {currentYear[index].toLocaleString('fr-FR')} MAD</div>
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">{month}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
