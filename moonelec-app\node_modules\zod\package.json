{"name": "zod", "version": "3.25.49", "type": "module", "author": "<PERSON> <<EMAIL>>", "description": "TypeScript-first schema declaration and validation library with static type inference", "files": ["dist", "v3", "v4", "v4-mini"], "funding": "https://github.com/sponsors/colinhacks", "homepage": "https://zod.dev", "keywords": ["typescript", "schema", "validation", "type", "inference"], "license": "MIT", "sideEffects": false, "main": "./dist/cjs/index.js", "types": "./dist/types/index.d.ts", "module": "./dist/esm/index.js", "exports": {".": {"import": {"@zod/source": "./src/index.ts", "types": "./dist/types/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"@zod/source": "./src/index.ts", "types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.js"}}, "./v3": {"import": {"@zod/source": "./src/index.ts", "types": "./dist/types/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"@zod/source": "./src/index.ts", "types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.js"}}, "./v4": {"import": {"@zod/source": "./src/v4/index.ts", "types": "./dist/types/v4/index.d.ts", "default": "./dist/esm/v4/index.js"}, "require": {"@zod/source": "./src/v4/index.ts", "types": "./dist/types/v4/index.d.ts", "default": "./dist/cjs/v4/index.js"}}, "./v4-mini": {"@zod/source": "./src/v4/mini/index.ts", "types": "./dist/types/v4/mini/index.d.ts", "import": "./dist/esm/v4/mini/index.js", "require": "./dist/cjs/v4/mini/index.js"}, "./v4/mini": {"@zod/source": "./src/v4/mini/index.ts", "types": "./dist/types/v4/mini/index.d.ts", "import": "./dist/esm/v4/mini/index.js", "require": "./dist/cjs/v4/mini/index.js"}, "./v4/core": {"@zod/source": "./src/v4/core/index.ts", "types": "./dist/types/v4/core/index.d.ts", "import": "./dist/esm/v4/core/index.js", "require": "./dist/cjs/v4/core/index.js"}, "./v4/locales": {"@zod/source": "./src/v4/locales/index.ts", "types": "./dist/types/v4/locales/index.d.ts", "import": "./dist/esm/v4/locales/index.js", "require": "./dist/cjs/v4/locales/index.js"}, "./v4/locales/*": {"@zod/source": "./src/v4/locales/*", "types": "./dist/types/v4/locales/*", "import": "./dist/esm/v4/locales/*", "require": "./dist/cjs/v4/locales/*"}, "./package.json": "./package.json"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./v3": "./src/v3/index.ts", "./v4": "./src/v4/index.ts", "./v4-mini": "./src/v4/mini/index.ts", "./v4/core": "./src/v4/core/index.ts", "./v4/locales": "./src/v4/locales/index.ts", "./v4/locales/*": "./src/v4/locales/*"}, "sourceDialects": ["@zod/source"], "exclude": ["./src/**/*.test.ts"]}, "repository": {"type": "git", "url": "git+https://github.com/colinhacks/zod.git"}, "bugs": {"url": "https://github.com/colinhacks/zod/issues"}, "support": {"backing": {"npm-funding": true}}, "scripts": {"clean": "rm -rf dist", "build": "tsx ./build.mts", "postbuild": "pnpm biome check --write .", "test:watch": "pnpm vitest", "test": "pnpm vitest run", "bump:beta": "pnpm version \"v$(pnpm pkg get version | jq -r)-beta.$(date +%Y%m%dT%H%M%S)\"", "pub:beta": "pnpm bump:beta && pnpm publish --tag next --publish-branch v4 --no-git-checks --dry-run"}}