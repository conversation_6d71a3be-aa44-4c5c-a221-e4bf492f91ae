import { prisma } from './prisma';
import { QuoteStatus, NotificationType } from '@prisma/client';
import { generateQuoteNumber } from './utils';
import { createNotificationForAllAdmins } from './notifications';

// Obtenir tous les devis avec pagination et filtrage
export async function getQuotes(options?: {
  clientId?: string;
  status?: QuoteStatus;
  search?: string;
  skip?: number;
  take?: number;
}) {
  const { clientId, status, search, skip = 0, take = 50 } = options || {};

  const where = {
    ...(clientId ? { clientId } : {}),
    ...(status ? { status } : {}),
    ...(search
      ? {
          OR: [
            { quoteNumber: { contains: search } },
            { client: { user: { firstname: { contains: search } } } },
            { client: { user: { lastname: { contains: search } } } },
            { client: { company_name: { contains: search } } },
          ],
        }
      : {}),
  };

  const [quotes, total] = await Promise.all([
    prisma.quote.findMany({
      where,
      include: {
        client: {
          include: {
            user: {
              select: {
                firstname: true,
                lastname: true,
                email: true,
                telephone: true,
              },
            },
          },
        },
        quoteitem: {
          include: {
            product: true,
          },
        },
      },
      skip,
      take,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.quote.count({ where }),
  ]);

  return { quotes, total };
}

// Obtenir un devis par son ID
export async function getQuoteById(id: string) {
  return prisma.quote.findUnique({
    where: { id },
    include: {
      client: {
        include: {
          user: {
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
              telephone: true,
              role: true,
            },
          },
        },
      },
      quoteitem: {
        include: {
          product: {
            include: {
              category: true,
              brand: true,
              productimage: true,
            },
          },
        },
      },
    },
  });
}

// Créer un nouveau devis
export async function createQuote(data: {
  clientId: string;
  notes?: string;
  validUntil?: Date;
  items: {
    productId: string;
    quantity: number;
  }[];
  createdByAdminId?: string; // ID de l'admin qui crée le devis (si applicable)
}) {
  const { clientId, notes, validUntil, items, createdByAdminId } = data;

  // Générer un numéro de devis unique
  const quoteNumber = await generateQuoteNumber();

  return prisma.$transaction(async (tx) => {
    // Créer le devis
    const quote = await tx.quote.create({
      data: {
        quoteNumber,
        clientId,
        status: QuoteStatus.DRAFT,
        notes,
        validUntil,
        createdByAdminId,
      },
    });

    // Ajouter les produits au devis
    for (const item of items) {
      await tx.quoteItem.create({
        data: {
          quoteId: quote.id,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: 0, // Le prix sera défini par l'administrateur
        },
      });
    }

    // Récupérer les informations du client pour la notification
    const client = await tx.client.findUnique({
      where: { id: clientId },
      include: {
        user: {
          select: {
            firstname: true,
            lastname: true,
          },
        },
      },
    });

    // Créer une notification pour tous les administrateurs
    let notificationMessage = '';

    if (createdByAdminId) {
      // Si le devis a été créé par un administrateur
      const admin = await tx.admin.findUnique({
        where: { id: createdByAdminId },
        include: {
          user: {
            select: {
              firstname: true,
              lastname: true,
            },
          },
        },
      });

      if (admin && admin.user) {
        notificationMessage = `L'administrateur ${admin.user.firstname} ${admin.user.lastname} a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;
      } else {
        notificationMessage = `Un administrateur a créé un devis pour ${client?.user.firstname} ${client?.user.lastname}`;
      }
    } else {
      // Si le devis a été créé par un client
      notificationMessage = `${client?.user.firstname} ${client?.user.lastname} a demandé un devis`;
    }

    // Créer la notification en dehors de la transaction pour éviter les problèmes
    try {
      await createNotificationForAllAdmins(
        NotificationType.QUOTE_REQUESTED,
        notificationMessage,
        quote.id
      );
    } catch (error) {
      console.error('Erreur lors de la création des notifications:', error);
      // Ne pas bloquer la création du devis si la notification échoue
    }

    return quote;
  });
}

// Mettre à jour un devis existant
export async function updateQuote(
  id: string,
  data: {
    status?: QuoteStatus;
    notes?: string;
    validUntil?: Date | null;
    totalAmount?: number;
    pdfUrl?: string;
    items?: {
      id?: string; // Si l'item existe déjà
      productId: string;
      quantity: number;
      unitPrice?: number;
    }[];
  }
) {
  const { status, notes, validUntil, totalAmount, pdfUrl, items } = data;

  return prisma.$transaction(async (tx) => {
    // Préparer les données à mettre à jour
    const updateData: any = {};

    if (status !== undefined) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;
    if (totalAmount !== undefined) updateData.totalAmount = totalAmount;
    if (pdfUrl !== undefined) updateData.pdfUrl = pdfUrl;

    // Traiter la date de validité
    if (validUntil !== undefined) {
      if (validUntil === null) {
        updateData.validUntil = null;
      } else if (typeof validUntil === 'string') {
        // Si c'est une chaîne de caractères, s'assurer qu'elle est au format ISO
        if (validUntil.includes('T')) {
          // Déjà au format ISO complet
          updateData.validUntil = new Date(validUntil);
        } else {
          // Ajouter l'heure (minuit) pour compléter le format ISO
          updateData.validUntil = new Date(`${validUntil}T00:00:00.000Z`);
        }
      } else if (validUntil instanceof Date) {
        updateData.validUntil = validUntil;
      }
    }

    // Mettre à jour le devis
    const quote = await tx.quote.update({
      where: { id },
      data: updateData,
    });

    // Mettre à jour les produits du devis si fournis
    if (items && items.length > 0) {
      // Récupérer les items existants
      const existingItems = await tx.quoteItem.findMany({
        where: { quoteId: id },
      });

      // Créer un map des items existants pour un accès rapide
      const existingItemsMap = new Map(
        existingItems.map(item => [item.productId, item])
      );

      // Traiter chaque item
      for (const item of items) {
        const existingItem = item.id
          ? existingItems.find(i => i.id === item.id)
          : existingItemsMap.get(item.productId);

        if (existingItem) {
          // Mettre à jour l'item existant
          await tx.quoteItem.update({
            where: { id: existingItem.id },
            data: {
              quantity: item.quantity,
              ...(item.unitPrice !== undefined && { unitPrice: item.unitPrice }),
            },
          });
        } else {
          // Créer un nouvel item
          await tx.quoteItem.create({
            data: {
              quoteId: id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice || 0,
            },
          });
        }
      }

      // Supprimer les items qui ne sont plus dans la liste
      const newProductIds = items.map(item => item.productId);
      const itemsToDelete = existingItems.filter(
        item => !newProductIds.includes(item.productId)
      );

      for (const item of itemsToDelete) {
        await tx.quoteItem.delete({
          where: { id: item.id },
        });
      }
    }

    return quote;
  });
}

// Supprimer un devis
export async function deleteQuote(id: string) {
  return prisma.$transaction(async (tx) => {
    // Supprimer d'abord les items du devis
    await tx.quoteItem.deleteMany({
      where: { quoteId: id },
    });

    // Supprimer le devis
    return tx.quote.delete({
      where: { id },
    });
  });
}

// Convertir un devis en commande
export async function convertQuoteToOrder(id: string) {
  const quote = await getQuoteById(id);

  if (!quote) {
    throw new Error('Quote not found');
  }

  if (quote.status !== QuoteStatus.APPROVED) {
    throw new Error('Only approved quotes can be converted to orders');
  }

  // Logique pour créer une commande à partir du devis
  // À implémenter selon les besoins
}

// Obtenir les devis d'un client
export async function getClientQuotes(clientId: string, options?: {
  status?: QuoteStatus;
  skip?: number;
  take?: number;
}) {
  const { status, skip = 0, take = 50 } = options || {};

  const where = {
    clientId,
    ...(status ? { status } : {}),
  };

  const [quotes, total] = await Promise.all([
    prisma.quote.findMany({
      where,
      include: {
        quoteitem: {
          include: {
            product: true,
          },
        },
      },
      skip,
      take,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.quote.count({ where }),
  ]);

  return { quotes, total };
}
