{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/admin/clients/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { \n  FaArrowLeft, \n  FaEdit, \n  FaTrash, \n  Fa<PERSON><PERSON><PERSON>,\n  Fa<PERSON>ser,\n  FaEnvelope,\n  FaPhone,\n  FaMapMarkerAlt,\n  FaBuilding,\n  FaCalendarAlt,\n  FaGlobe\n} from 'react-icons/fa';\nimport { useAuth } from '@/hooks/useAuth';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport Link from 'next/link';\n\ninterface Client {\n  id: string;\n  user: {\n    id: string;\n    firstname: string;\n    lastname: string;\n    email: string;\n    telephone?: string;\n    createdAt: string;\n    updatedAt: string;\n  };\n  company?: string;\n  address?: string;\n  city?: string;\n  postalCode?: string;\n  country?: string;\n  createdAt: string;\n}\n\nexport default function ClientDetailPage({ params }: { params: { id: string } }) {\n  const router = useRouter();\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [client, setClient] = useState<Client | null>(null);\n\n  // Fetch client details\n  const fetchClient = async () => {\n    setIsLoading(true);\n    \n    try {\n      const response = await fetch(`/api/admin/clients/${params.id}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch client');\n      }\n      \n      const data = await response.json();\n      setClient(data.client);\n    } catch (error) {\n      console.error('Error fetching client:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle delete client\n  const handleDeleteClient = async () => {\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce client ? Cette action est irréversible.')) {\n      return;\n    }\n    \n    try {\n      const response = await fetch(`/api/admin/clients/${params.id}`, {\n        method: 'DELETE',\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to delete client');\n      }\n      \n      // Redirect to clients list\n      router.push('/admin/clients?success=deleted');\n    } catch (error) {\n      console.error('Error deleting client:', error);\n      alert('Erreur lors de la suppression du client');\n    }\n  };\n\n  // Fetch client on initial load\n  useEffect(() => {\n    if (user) {\n      fetchClient();\n    }\n  }, [user, params.id]);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen\">\n        <FaSpinner className=\"animate-spin text-4xl text-primary\" />\n      </div>\n    );\n  }\n\n  if (!client) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white mb-4\">\n            Client Non Trouvé\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n            Le client que vous recherchez n'existe pas ou vous n'avez pas les permissions pour le voir.\n          </p>\n          <button\n            onClick={() => router.push('/admin/clients')}\n            className=\"flex items-center text-primary hover:text-primary-dark\"\n          >\n            <FaArrowLeft className=\"mr-2\" />\n            Retour à la liste des clients\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <RouteGuard allowedRoles={['ADMIN']}>\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <button\n              onClick={() => router.push('/admin/clients')}\n              className=\"mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white\"\n            >\n              <FaArrowLeft className=\"text-xl\" />\n            </button>\n            <h1 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n              Détails du Client\n            </h1>\n          </div>\n          \n          <div className=\"flex gap-3\">\n            <Link href={`/admin/clients/${client.id}/edit`}>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors\"\n              >\n                <FaEdit />\n                <span>Modifier</span>\n              </motion.button>\n            </Link>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={handleDeleteClient}\n              className=\"flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n            >\n              <FaTrash />\n              <span>Supprimer</span>\n            </motion.button>\n          </div>\n        </div>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Main Information */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n              {/* Client Header */}\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white mr-4\">\n                  <FaUser className=\"text-2xl\" />\n                </div>\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n                    {client.user.firstname} {client.user.lastname}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300\">\n                    ID: {client.id}\n                  </p>\n                </div>\n              </div>\n              \n              {/* Personal Information */}\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n                  Informations Personnelles\n                </h3>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"flex items-start\">\n                    <FaEnvelope className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                    <div>\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400\">Email</p>\n                      <p className=\"text-gray-800 dark:text-white\">{client.user.email}</p>\n                    </div>\n                  </div>\n                  \n                  {client.user.telephone && (\n                    <div className=\"flex items-start\">\n                      <FaPhone className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                      <div>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">Téléphone</p>\n                        <p className=\"text-gray-800 dark:text-white\">{client.user.telephone}</p>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n              \n              {/* Company Information */}\n              {(client.company || client.address || client.city) && (\n                <div className=\"mb-8\">\n                  <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n                    Informations de l'Entreprise\n                  </h3>\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    {client.company && (\n                      <div className=\"flex items-start\">\n                        <FaBuilding className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                        <div>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">Entreprise</p>\n                          <p className=\"text-gray-800 dark:text-white\">{client.company}</p>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {client.address && (\n                      <div className=\"flex items-start md:col-span-2\">\n                        <FaMapMarkerAlt className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                        <div>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">Adresse</p>\n                          <p className=\"text-gray-800 dark:text-white\">{client.address}</p>\n                          {(client.city || client.postalCode) && (\n                            <p className=\"text-gray-800 dark:text-white\">\n                              {client.postalCode && `${client.postalCode} `}\n                              {client.city}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n                    )}\n                    \n                    {client.country && (\n                      <div className=\"flex items-start\">\n                        <FaGlobe className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                        <div>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">Pays</p>\n                          <p className=\"text-gray-800 dark:text-white\">{client.country}</p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          \n          {/* Sidebar Information */}\n          <div>\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n                Informations du Compte\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-start\">\n                  <FaCalendarAlt className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                  <div>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">Date d'inscription</p>\n                    <p className=\"text-gray-800 dark:text-white\">\n                      {new Date(client.user.createdAt).toLocaleDateString('fr-FR', {\n                        year: 'numeric',\n                        month: 'long',\n                        day: 'numeric',\n                      })}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start\">\n                  <FaCalendarAlt className=\"text-gray-500 dark:text-gray-400 mt-1 mr-3\" />\n                  <div>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">Dernière mise à jour</p>\n                    <p className=\"text-gray-800 dark:text-white\">\n                      {new Date(client.user.updatedAt).toLocaleDateString('fr-FR', {\n                        year: 'numeric',\n                        month: 'long',\n                        day: 'numeric',\n                      })}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"pt-4 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">Statut</span>\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n                      Actif\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Quick Actions */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mt-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n                Actions Rapides\n              </h3>\n              \n              <div className=\"space-y-3\">\n                <Link href={`/admin/clients/${client.id}/edit`}>\n                  <button className=\"w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\">\n                    <FaEdit />\n                    <span>Modifier le Client</span>\n                  </button>\n                </Link>\n                \n                <button\n                  onClick={handleDeleteClient}\n                  className=\"w-full flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n                >\n                  <FaTrash />\n                  <span>Supprimer le Client</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAaA;AACA;AACA;;;AApBA;;;;;;;;AAyCe,SAAS,iBAAiB,EAAE,MAAM,EAA8B;;IAC7E,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpD,uBAAuB;IACvB,MAAM,cAAc;QAClB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE;YAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,KAAK,MAAM;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ,kFAAkF;YAC7F;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC9D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;qCAAG;QAAC;QAAM,OAAO,EAAE;KAAC;IAEpB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwD;;;;;;kCAGtE,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;IAM1C;IAEA,qBACE,6LAAC,2IAAA,CAAA,UAAU;QAAC,cAAc;YAAC;SAAQ;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;8CAEV,cAAA,6LAAC,iJAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;;;;;;;sCAKnE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;8CAC5C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,6LAAC,iJAAA,CAAA,SAAM;;;;;0DACP,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iJAAA,CAAA,UAAO;;;;;sDACR,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAKZ,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;4DACX,OAAO,IAAI,CAAC,SAAS;4DAAC;4DAAE,OAAO,IAAI,CAAC,QAAQ;;;;;;;kEAE/C,6LAAC;wDAAE,WAAU;;4DAAmC;4DACzC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;kDAMpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAIzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAiC,OAAO,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;oDAIlE,OAAO,IAAI,CAAC,SAAS,kBACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAiC,OAAO,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ5E,CAAC,OAAO,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,mBAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAIzE,6LAAC;gDAAI,WAAU;;oDACZ,OAAO,OAAO,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAiC,OAAO,OAAO;;;;;;;;;;;;;;;;;;oDAKjE,OAAO,OAAO,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAiC,OAAO,OAAO;;;;;;oEAC3D,CAAC,OAAO,IAAI,IAAI,OAAO,UAAU,mBAChC,6LAAC;wEAAE,WAAU;;4EACV,OAAO,UAAU,IAAI,GAAG,OAAO,UAAU,CAAC,CAAC,CAAC;4EAC5C,OAAO,IAAI;;;;;;;;;;;;;;;;;;;oDAOrB,OAAO,OAAO,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAiC,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW5E,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAIzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,OAAO,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,SAAS;wEAC3D,MAAM;wEACN,OAAO;wEACP,KAAK;oEACP;;;;;;;;;;;;;;;;;;8DAKN,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,OAAO,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,SAAS;wEAC3D,MAAM;wEACN,OAAO;wEACP,KAAK;oEACP;;;;;;;;;;;;;;;;;;8DAKN,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EAA4I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASpK,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAIzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;8DAC5C,cAAA,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,iJAAA,CAAA,SAAM;;;;;0EACP,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAIV,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,iJAAA,CAAA,UAAO;;;;;sEACR,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GAzSwB;;QACP,qIAAA,CAAA,YAAS;QACP,0HAAA,CAAA,UAAO;;;KAFF", "debugId": null}}]}