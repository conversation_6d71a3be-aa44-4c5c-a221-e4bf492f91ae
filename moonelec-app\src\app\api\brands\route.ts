import { NextRequest, NextResponse } from 'next/server';
import { getBrands, createBrand, getBrandsWithProductCount } from '@/lib/brands';

// GET /api/brands - Get all brands
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const search = searchParams.get('search') || undefined;
    const includeProducts = searchParams.get('includeProducts') === 'true';
    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;
    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;
    const withCount = searchParams.get('withCount') === 'true';

    // Utiliser les données de la base de données
    if (withCount) {
      const brands = await getBrandsWithProductCount();
      return NextResponse.json({ brands, total: brands.length });
    } else {
      const { brands, total } = await getBrands({
        search,
        includeProducts,
        skip,
        take,
      });

      return NextResponse.json({ brands, total });
    }
  } catch (error: any) {
    console.error('Error fetching brands:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch brands' },
      { status: 500 }
    );
  }
}

// POST /api/brands - Create a new brand
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, image } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Create the brand
    const brand = await createBrand({
      name,
      image,
    });

    return NextResponse.json(brand, { status: 201 });
  } catch (error: any) {
    console.error('Error creating brand:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create brand' },
      { status: 500 }
    );
  }
}
