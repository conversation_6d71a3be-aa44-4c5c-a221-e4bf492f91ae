{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/categories.ts"], "sourcesContent": ["import { prisma } from './prisma';\n\n// Get all categories\nexport async function getCategories(options?: {\n  search?: string;\n  includeProducts?: boolean;\n  skip?: number;\n  take?: number;\n}) {\n  const { search, includeProducts = false, skip = 0, take = 50 } = options || {};\n\n  const where = search\n    ? {\n        OR: [\n          { name: { contains: search } },\n          { description: { contains: search } },\n        ],\n      }\n    : {};\n\n  const [categories, total] = await Promise.all([\n    prisma.category.findMany({\n      where,\n      include: {\n        product: includeProducts,\n      },\n      skip,\n      take,\n      orderBy: {\n        name: 'asc',\n      },\n    }),\n    prisma.category.count({ where }),\n  ]);\n\n  return { categories, total };\n}\n\n// Get a single category by ID\nexport async function getCategoryById(id: string, includeProducts: boolean = false) {\n  return prisma.category.findUnique({\n    where: { id },\n    include: {\n      product: includeProducts,\n    },\n  });\n}\n\n// Create a new category\nexport async function createCategory(data: {\n  name: string;\n  description?: string;\n  image?: string;\n}) {\n  // Check if a category with the same name already exists\n  const existingCategory = await prisma.category.findFirst({\n    where: {\n      name: data.name,\n    },\n  });\n\n  if (existingCategory) {\n    throw new Error(`Une catégorie avec le nom ${data.name} existe déjà`);\n  }\n\n  return prisma.category.create({\n    data,\n  });\n}\n\n// Update an existing category\nexport async function updateCategory(\n  id: string,\n  data: {\n    name?: string;\n    description?: string;\n    image?: string;\n  }\n) {\n  // If name is being updated, check if it already exists\n  if (data.name) {\n    const existingCategory = await prisma.category.findFirst({\n      where: {\n        name: data.name,\n        id: { not: id },\n      },\n    });\n\n    if (existingCategory) {\n      throw new Error(`Une catégorie avec le nom ${data.name} existe déjà`);\n    }\n  }\n\n  return prisma.category.update({\n    where: { id },\n    data,\n  });\n}\n\n// Delete a category\nexport async function deleteCategory(id: string) {\n  // First, update all products in this category to have null categoryId\n  await prisma.product.updateMany({\n    where: { categoryId: id },\n    data: { categoryId: null },\n  });\n\n  // Then delete the category\n  return prisma.category.delete({\n    where: { id },\n  });\n}\n\n// Get category with product count\nexport async function getCategoriesWithProductCount() {\n  const categories = await prisma.category.findMany({\n    orderBy: {\n      name: 'asc',\n    },\n  });\n\n  const categoriesWithCount = await Promise.all(\n    categories.map(async (category) => {\n      const count = await prisma.product.count({\n        where: { categoryId: category.id },\n      });\n      return {\n        ...category,\n        productCount: count,\n      };\n    })\n  );\n\n  return categoriesWithCount;\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,eAAe,cAAc,OAKnC;IACC,MAAM,EAAE,MAAM,EAAE,kBAAkB,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAE7E,MAAM,QAAQ,SACV;QACE,IAAI;YACF;gBAAE,MAAM;oBAAE,UAAU;gBAAO;YAAE;YAC7B;gBAAE,aAAa;oBAAE,UAAU;gBAAO;YAAE;SACrC;IACH,IACA,CAAC;IAEL,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC5C,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACvB;YACA,SAAS;gBACP,SAAS;YACX;YACA;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE;QAAM;KAC/B;IAED,OAAO;QAAE;QAAY;IAAM;AAC7B;AAGO,eAAe,gBAAgB,EAAU,EAAE,kBAA2B,KAAK;IAChF,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAChC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,SAAS;QACX;IACF;AACF;AAGO,eAAe,eAAe,IAIpC;IACC,wDAAwD;IACxD,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QACvD,OAAO;YACL,MAAM,KAAK,IAAI;QACjB;IACF;IAEA,IAAI,kBAAkB;QACpB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;IACtE;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B;IACF;AACF;AAGO,eAAe,eACpB,EAAU,EACV,IAIC;IAED,uDAAuD;IACvD,IAAI,KAAK,IAAI,EAAE;QACb,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACvD,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,IAAI;oBAAE,KAAK;gBAAG;YAChB;QACF;QAEA,IAAI,kBAAkB;YACpB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;QACtE;IACF;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAGO,eAAe,eAAe,EAAU;IAC7C,sEAAsE;IACtE,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YAAE,YAAY;QAAG;QACxB,MAAM;YAAE,YAAY;QAAK;IAC3B;IAEA,2BAA2B;IAC3B,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChD,SAAS;YACP,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,WAAW,GAAG,CAAC,OAAO;QACpB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACvC,OAAO;gBAAE,YAAY,SAAS,EAAE;YAAC;QACnC;QACA,OAAO;YACL,GAAG,QAAQ;YACX,cAAc;QAChB;IACF;IAGF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      console.log('🔐 No valid authorization header found');\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n    console.log('🔐 Mobile token received:', token.substring(0, 50) + '...');\n\n    // Verify JWT token using JWT_SECRET (for mobile auth)\n    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret';\n    console.log('🔐 Using JWT secret:', jwtSecret ? 'Available' : 'Missing');\n\n    const decoded = jwt.verify(token, jwtSecret) as any;\n    console.log('🔐 Token decoded successfully:', { userId: decoded.userId, role: decoded.role });\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n        isActive: true,\n      }\n    });\n\n    if (!user || !user.isActive) {\n      console.log('🔐 User not found or inactive:', { found: !!user, active: user?.isActive });\n      return null;\n    }\n\n    console.log('🔐 Mobile user authenticated successfully:', { id: user.id, role: user.role });\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n\n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') ||\n         userAgent.includes('Expo') ||\n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  if (!isMobileRequest(request)) {\n    return null;\n  }\n\n  return await verifyMobileToken(request);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QACjE,QAAQ,GAAG,CAAC,6BAA6B,MAAM,SAAS,CAAC,GAAG,MAAM;QAElE,sDAAsD;QACtD,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC3E,QAAQ,GAAG,CAAC,wBAAwB,uCAAY;QAEhD,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,QAAQ,GAAG,CAAC,kCAAkC;YAAE,QAAQ,QAAQ,MAAM;YAAE,MAAM,QAAQ,IAAI;QAAC;QAE3F,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,QAAQ,GAAG,CAAC,kCAAkC;gBAAE,OAAO,CAAC,CAAC;gBAAM,QAAQ,MAAM;YAAS;YACtF,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,8CAA8C;YAAE,IAAI,KAAK,EAAE;YAAE,MAAM,KAAK,IAAI;QAAC;QACzF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,IAAI,CAAC,gBAAgB,UAAU;QAC7B,OAAO;IACT;IAEA,OAAO,MAAM,kBAAkB;AACjC", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { prisma } from './prisma';\nimport { UserRole } from '@prisma/client';\n\n// Fonction pour hacher un mot de passe\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await bcrypt.genSalt(10);\n  return bcrypt.hash(password, salt);\n}\n\n// Fonction pour vérifier un mot de passe\nexport async function verifyPassword(\n  password: string,\n  hashedPassword: string\n): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Fonction pour créer un utilisateur client\nexport async function createClientUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  company_name,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  company_name?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.CLIENT,\n      },\n    });\n\n    // C<PERSON>er le profil client associé\n    const client = await tx.client.create({\n      data: {\n        userId: user.id,\n        company_name,\n      },\n    });\n\n    return { user, client };\n  });\n}\n\n// Fonction pour créer un utilisateur commercial\nexport async function createCommercialUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n  profile_photo,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n  profile_photo?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.COMMERCIAL,\n      },\n    });\n\n    // Créer le profil commercial associé\n    const commercial = await tx.commercial.create({\n      data: {\n        userId: user.id,\n        profile_photo,\n      },\n    });\n\n    return { user, commercial };\n  });\n}\n\n// Fonction pour créer un utilisateur administrateur\nexport async function createAdminUser({\n  email,\n  username,\n  password,\n  lastname,\n  firstname,\n  telephone,\n}: {\n  email: string;\n  username: string;\n  password: string;\n  lastname: string;\n  firstname: string;\n  telephone?: string;\n}) {\n  const hashedPassword = await hashPassword(password);\n\n  return prisma.$transaction(async (tx) => {\n    // Créer l'utilisateur de base\n    const user = await tx.user.create({\n      data: {\n        email,\n        username,\n        password: hashedPassword,\n        lastname,\n        firstname,\n        telephone,\n        role: UserRole.ADMIN,\n      },\n    });\n\n    // Créer le profil admin associé\n    const admin = await tx.admin.create({\n      data: {\n        userId: user.id,\n      },\n    });\n\n    return { user, admin };\n  });\n}\n\n// Fonction pour trouver un utilisateur par son nom d'utilisateur\nexport async function findUserByUsername(username: string) {\n  return prisma.user.findUnique({\n    where: { username },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n\n// Fonction pour trouver un utilisateur par son email\nexport async function findUserByEmail(email: string) {\n  return prisma.user.findUnique({\n    where: { email },\n    include: {\n      client: true,\n      commercial: true,\n      admin: true,\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;IAClC,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eACpB,QAAgB,EAChB,cAAsB;IAEtB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,eAAe,iBAAiB,EACrC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EASb;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,MAAM;YACvB;QACF;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAO;IACxB;AACF;AAGO,eAAe,qBAAqB,EACzC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EASd;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,UAAU;YAC3B;QACF;QAEA,qCAAqC;QACrC,MAAM,aAAa,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAM;QAAW;IAC5B;AACF;AAGO,eAAe,gBAAgB,EACpC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EAQV;IACC,MAAM,iBAAiB,MAAM,aAAa;IAE1C,OAAO,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;QAChC,8BAA8B;QAC9B,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA;gBACA,MAAM,6HAAA,CAAA,WAAQ,CAAC,KAAK;YACtB;QACF;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAS;QAClB,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF;AAGO,eAAe,gBAAgB,KAAa;IACjD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE;QAAM;QACf,SAAS;YACP,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/auth-options.ts"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { findUserByUsername, verifyPassword } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'Credentials',\n      credentials: {\n        username: { label: \"Nom d'utilisateur\", type: 'text' },\n        password: { label: 'Mot de passe', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.username || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Rechercher l'utilisateur par nom d'utilisateur\n          const user = await findUserByUsername(credentials.username);\n\n          // Vérifier si l'utilisateur existe\n          if (!user) {\n            return null;\n          }\n\n          // Vérifier le mot de passe\n          const isPasswordValid = await verifyPassword(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Retourner les données de l'utilisateur sans le mot de passe\n          return {\n            id: user.id,\n            email: user.email,\n            username: user.username,\n            name: `${user.firstname} ${user.lastname}`,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            role: user.role,\n            clientId: user.client?.id,\n            commercialId: user.commercial?.id,\n            adminId: user.admin?.id,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.username = user.username;\n        token.role = user.role;\n        token.firstname = user.firstname;\n        token.lastname = user.lastname;\n        token.clientId = user.clientId;\n        token.commercialId = user.commercialId;\n        token.adminId = user.adminId;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.id as string;\n        session.user.username = token.username as string;\n        session.user.role = token.role as string;\n        session.user.firstname = token.firstname as string;\n        session.user.lastname = token.lastname as string;\n        session.user.clientId = token.clientId as string | undefined;\n        session.user.commercialId = token.commercialId as string | undefined;\n        session.user.adminId = token.adminId as string | undefined;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 jours\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,UAAU;oBAAE,OAAO;oBAAqB,MAAM;gBAAO;gBACrD,UAAU;oBAAE,OAAO;oBAAgB,MAAM;gBAAW;YACtD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,IAAI;oBACF,iDAAiD;oBACjD,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,QAAQ;oBAE1D,mCAAmC;oBACnC,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,2BAA2B;oBAC3B,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EACzC,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,MAAM,EAAE;wBACvB,cAAc,KAAK,UAAU,EAAE;wBAC/B,SAAS,KAAK,KAAK,EAAE;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO;YAC9B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YACtC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/categories/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { getCategories, createCategory, getCategoriesWithProductCount } from '@/lib/categories';\nimport { getMobileUserFromRequest } from '@/lib/mobile-auth';\nimport { authOptions } from '@/lib/auth-options';\n\n// GET /api/categories - Get all categories\nexport async function GET(req: NextRequest) {\n  try {\n    // Check for mobile authentication\n    const mobileUser = await getMobileUserFromRequest(req);\n    if (mobileUser) {\n      console.log('📱 Mobile user authenticated for categories:', mobileUser.username);\n    }\n\n    const searchParams = req.nextUrl.searchParams;\n    const search = searchParams.get('search') || undefined;\n    const includeProducts = searchParams.get('includeProducts') === 'true';\n    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;\n    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;\n    const withCount = searchParams.get('withCount') === 'true';\n\n    // Utiliser les données de la base de données\n    if (withCount) {\n      const categories = await getCategoriesWithProductCount();\n      return NextResponse.json({ categories, total: categories.length });\n    } else {\n      const { categories, total } = await getCategories({\n        search,\n        includeProducts,\n        skip,\n        take,\n      });\n\n      return NextResponse.json({ categories, total });\n    }\n  } catch (error: any) {\n    console.error('Error fetching categories:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch categories' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/categories - Create a new category\nexport async function POST(req: NextRequest) {\n  try {\n    // Check for mobile authentication first\n    const mobileUser = await getMobileUserFromRequest(req);\n    const session = await getServerSession(authOptions);\n\n    if (!mobileUser && !session) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    // Use mobile user data if available, otherwise use session\n    const user = mobileUser || session?.user;\n\n    if (!user || user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });\n    }\n\n    const body = await req.json();\n    const { name, description, image } = body;\n\n    // Validate required fields\n    if (!name) {\n      return NextResponse.json(\n        { error: 'Name is required' },\n        { status: 400 }\n      );\n    }\n\n    // Create the category\n    const category = await createCategory({\n      name,\n      description,\n      image,\n    });\n\n    return NextResponse.json(category, { status: 201 });\n  } catch (error: any) {\n    console.error('Error creating category:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to create category' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,kCAAkC;QAClC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,gDAAgD,WAAW,QAAQ;QACjF;QAEA,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,kBAAkB,aAAa,GAAG,CAAC,uBAAuB;QAChE,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QAEpD,6CAA6C;QAC7C,IAAI,WAAW;YACb,MAAM,aAAa,MAAM,CAAA,GAAA,0HAAA,CAAA,gCAA6B,AAAD;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;gBAAY,OAAO,WAAW,MAAM;YAAC;QAClE,OAAO;YACL,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;gBAChD;gBACA;gBACA;gBACA;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;gBAAY;YAAM;QAC/C;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA6B,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,wCAAwC;QACxC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,+HAAA,CAAA,cAAW;QAElD,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,2DAA2D;QAC3D,MAAM,OAAO,cAAc,SAAS;QAEpC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuC,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;QAErC,2BAA2B;QAC3B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE;YACpC;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA4B,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}