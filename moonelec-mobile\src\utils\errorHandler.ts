import { Alert } from 'react-native';
import Toast from 'react-native-toast-message';

// French error messages mapping
export const ERROR_MESSAGES = {
  // Authentication Errors
  UNAUTHORIZED: 'Vous devez être connecté pour accéder à cette fonctionnalité.',
  INVALID_CREDENTIALS: 'Email ou mot de passe incorrect. Veuillez réessayer.',
  SESSION_EXPIRED: 'Votre session a expiré. Veuillez vous reconnecter.',
  ACCESS_DENIED: 'Vous n\'avez pas les permissions nécessaires pour cette action.',
  ACCOUNT_LOCKED: 'Votre compte a été temporairement verrouillé. Contactez l\'administrateur.',
  
  // Validation Errors
  REQUIRED_FIELD: 'Ce champ est obligatoire.',
  INVALID_EMAIL: 'Veuillez saisir une adresse email valide.',
  INVALID_PHONE: 'Veuillez saisir un numéro de téléphone valide.',
  PASSWORD_TOO_SHORT: 'Le mot de passe doit contenir au moins 8 caractères.',
  PASSWORD_MISMATCH: 'Les mots de passe ne correspondent pas.',
  INVALID_DATE: 'Veuillez sélectionner une date valide.',
  INVALID_FORMAT: 'Le format de données n\'est pas valide.',
  
  // File Upload Errors
  FILE_TOO_LARGE: 'Le fichier est trop volumineux. Taille maximale autorisée : 25 MB.',
  INVALID_FILE_TYPE: 'Type de fichier non autorisé. Formats acceptés : PDF, JPG, PNG, DOCX.',
  UPLOAD_FAILED: 'Échec du téléchargement du fichier. Veuillez réessayer.',
  NO_FILE_SELECTED: 'Veuillez sélectionner un fichier à télécharger.',
  CORRUPTED_FILE: 'Le fichier semble être corrompu. Veuillez en sélectionner un autre.',
  
  // Database Errors
  NOT_FOUND: 'L\'élément demandé n\'a pas été trouvé.',
  ALREADY_EXISTS: 'Cet élément existe déjà dans le système.',
  DELETE_FAILED: 'Impossible de supprimer cet élément. Il peut être utilisé ailleurs.',
  UPDATE_FAILED: 'Échec de la mise à jour. Veuillez réessayer.',
  CREATE_FAILED: 'Échec de la création. Vérifiez les informations saisies.',
  
  // Network Errors
  NETWORK_ERROR: 'Problème de connexion réseau. Vérifiez votre connexion internet.',
  SERVER_ERROR: 'Erreur du serveur. Veuillez réessayer dans quelques instants.',
  TIMEOUT: 'La requête a pris trop de temps. Veuillez réessayer.',
  CONNECTION_LOST: 'Connexion perdue. Reconnexion en cours...',
  
  // Business Logic Errors
  INSUFFICIENT_STOCK: 'Stock insuffisant pour cette quantité.',
  QUOTE_EXPIRED: 'Ce devis a expiré et ne peut plus être modifié.',
  INVALID_QUANTITY: 'La quantité doit être un nombre positif.',
  PRICE_CHANGED: 'Le prix de ce produit a changé. Veuillez actualiser votre panier.',
  ORDER_CANCELLED: 'Cette commande a été annulée et ne peut plus être modifiée.',
  
  // PDF Extraction Errors
  PDF_EXTRACTION_FAILED: 'Échec de l\'extraction des données du PDF. Vérifiez que le fichier est lisible.',
  PDF_NO_TEXT: 'Aucun texte détectable dans ce PDF. Essayez avec un autre fichier.',
  AI_MODEL_ERROR: 'Erreur du modèle d\'IA. Veuillez réessayer ou utiliser l\'extraction manuelle.',
  PDF_CORRUPTED: 'Le fichier PDF semble être corrompu ou protégé par mot de passe.',
  
  // Chat Errors
  MESSAGE_SEND_FAILED: 'Échec de l\'envoi du message. Veuillez réessayer.',
  CHAT_UPLOAD_FAILED: 'Échec du téléchargement du fichier dans le chat.',
  CONVERSATION_NOT_FOUND: 'Cette conversation n\'existe plus ou vous n\'y avez pas accès.',
  
  // Sales Report Errors
  REPORT_SAVE_FAILED: 'Échec de la sauvegarde du rapport de visite.',
  REPORT_DELETE_FAILED: 'Impossible de supprimer ce rapport. Vous ne pouvez supprimer que vos propres rapports.',
  REPORT_ALREADY_SUBMITTED: 'Ce rapport a déjà été soumis et ne peut plus être modifié.',
  MISSING_REQUIRED_INFO: 'Veuillez remplir tous les champs obligatoires du rapport.',
  
  // Mobile Specific Errors
  CAMERA_PERMISSION: 'Permission d\'accès à la caméra requise pour prendre des photos.',
  STORAGE_PERMISSION: 'Permission d\'accès au stockage requise pour sauvegarder les fichiers.',
  LOCATION_PERMISSION: 'Permission d\'accès à la localisation requise pour cette fonctionnalité.',
  MICROPHONE_PERMISSION: 'Permission d\'accès au microphone requise pour l\'enregistrement audio.',
  
  // Generic Errors
  UNKNOWN_ERROR: 'Une erreur inattendue s\'est produite. Veuillez réessayer.',
  MAINTENANCE_MODE: 'Le système est en maintenance. Veuillez réessayer plus tard.',
  FEATURE_DISABLED: 'Cette fonctionnalité est temporairement désactivée.',
  RATE_LIMIT_EXCEEDED: 'Trop de tentatives. Veuillez attendre avant de réessayer.',
};

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Connexion réussie ! Bienvenue.',
  LOGOUT_SUCCESS: 'Déconnexion réussie. À bientôt !',
  SAVE_SUCCESS: 'Enregistrement réussi.',
  UPDATE_SUCCESS: 'Mise à jour réussie.',
  DELETE_SUCCESS: 'Suppression réussie.',
  UPLOAD_SUCCESS: 'Fichier téléchargé avec succès.',
  MESSAGE_SENT: 'Message envoyé avec succès.',
  REPORT_SUBMITTED: 'Rapport de visite soumis avec succès.',
  PDF_EXTRACTED: 'Données extraites du PDF avec succès.',
  QUOTE_CREATED: 'Devis créé avec succès.',
  ORDER_PLACED: 'Commande passée avec succès.',
  PHOTO_TAKEN: 'Photo prise avec succès.',
  FILE_DOWNLOADED: 'Fichier téléchargé avec succès.',
};

// Warning messages
export const WARNING_MESSAGES = {
  UNSAVED_CHANGES: 'Vous avez des modifications non sauvegardées. Voulez-vous continuer ?',
  DELETE_CONFIRMATION: 'Êtes-vous sûr de vouloir supprimer cet élément ?',
  LOGOUT_CONFIRMATION: 'Êtes-vous sûr de vouloir vous déconnecter ?',
  LOW_STOCK: 'Attention : Stock faible pour ce produit.',
  QUOTE_EXPIRING: 'Ce devis expire bientôt.',
  LARGE_FILE: 'Ce fichier est volumineux. Le téléchargement peut prendre du temps.',
  OFFLINE_MODE: 'Vous êtes hors ligne. Certaines fonctionnalités peuvent être limitées.',
};

// Error type detection
export function getErrorType(error: any): string {
  if (!error) return 'UNKNOWN_ERROR';
  
  // Check for specific error codes
  if (error.status || error.code) {
    const code = error.status || error.code;
    switch (code) {
      case 401: return 'UNAUTHORIZED';
      case 403: return 'ACCESS_DENIED';
      case 404: return 'NOT_FOUND';
      case 409: return 'ALREADY_EXISTS';
      case 413: return 'FILE_TOO_LARGE';
      case 422: return 'INVALID_FORMAT';
      case 429: return 'RATE_LIMIT_EXCEEDED';
      case 500: return 'SERVER_ERROR';
      case 503: return 'MAINTENANCE_MODE';
      case 504: return 'TIMEOUT';
      default: break;
    }
  }
  
  // Check for specific error messages
  const message = error.message || error.error || '';
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
    return 'NETWORK_ERROR';
  }
  if (lowerMessage.includes('timeout')) {
    return 'TIMEOUT';
  }
  if (lowerMessage.includes('file') && lowerMessage.includes('size')) {
    return 'FILE_TOO_LARGE';
  }
  if (lowerMessage.includes('unauthorized') || lowerMessage.includes('token')) {
    return 'UNAUTHORIZED';
  }
  if (lowerMessage.includes('validation') || lowerMessage.includes('required')) {
    return 'INVALID_FORMAT';
  }
  if (lowerMessage.includes('pdf')) {
    return 'PDF_EXTRACTION_FAILED';
  }
  if (lowerMessage.includes('upload')) {
    return 'UPLOAD_FAILED';
  }
  if (lowerMessage.includes('permission')) {
    return 'CAMERA_PERMISSION';
  }
  
  return 'UNKNOWN_ERROR';
}

// Enhanced error handler with context
export function handleError(error: any, context?: string, customMessage?: string, showAlert = false) {
  console.error(`Error in ${context || 'application'}:`, error);
  
  const errorType = getErrorType(error);
  const message = customMessage || ERROR_MESSAGES[errorType as keyof typeof ERROR_MESSAGES] || ERROR_MESSAGES.UNKNOWN_ERROR;
  
  // Add context to message if provided
  const contextualMessage = context 
    ? `${context}: ${message}`
    : message;
  
  if (showAlert) {
    // Show native alert for critical errors
    Alert.alert(
      'Erreur',
      contextualMessage,
      [{ text: 'OK', style: 'default' }],
      { cancelable: true }
    );
  } else {
    // Show toast notification
    Toast.show({
      type: 'error',
      text1: 'Erreur',
      text2: contextualMessage,
      visibilityTime: 5000,
      position: 'top',
      topOffset: 60,
    });
  }
  
  return {
    type: errorType,
    message: contextualMessage,
    originalError: error,
  };
}

// Success handler
export function handleSuccess(messageKey: keyof typeof SUCCESS_MESSAGES, customMessage?: string) {
  const message = customMessage || SUCCESS_MESSAGES[messageKey];
  
  Toast.show({
    type: 'success',
    text1: 'Succès',
    text2: message,
    visibilityTime: 3000,
    position: 'top',
    topOffset: 60,
  });
}

// Warning handler
export function handleWarning(messageKey: keyof typeof WARNING_MESSAGES, customMessage?: string, showAlert = false) {
  const message = customMessage || WARNING_MESSAGES[messageKey];
  
  if (showAlert) {
    Alert.alert(
      'Attention',
      message,
      [
        { text: 'Annuler', style: 'cancel' },
        { text: 'Continuer', style: 'destructive' }
      ],
      { cancelable: true }
    );
  } else {
    Toast.show({
      type: 'info',
      text1: 'Attention',
      text2: message,
      visibilityTime: 4000,
      position: 'top',
      topOffset: 60,
    });
  }
}

// Loading handler
export function handleLoading(message: string = 'Chargement en cours...') {
  Toast.show({
    type: 'info',
    text1: 'Chargement',
    text2: message,
    visibilityTime: 0, // Don't auto-hide
    position: 'top',
    topOffset: 60,
  });
}

// Dismiss loading
export function dismissLoading() {
  Toast.hide();
}

// API error handler specifically for fetch requests
export async function handleApiError(response: Response, context?: string) {
  let errorData;
  
  try {
    errorData = await response.json();
  } catch {
    errorData = { message: 'Erreur de communication avec le serveur' };
  }
  
  const error = {
    status: response.status,
    message: errorData.message || errorData.error || 'Erreur inconnue',
    details: errorData.details,
  };
  
  return handleError(error, context);
}

// Confirmation dialog
export function showConfirmation(
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
) {
  Alert.alert(
    title,
    message,
    [
      {
        text: 'Annuler',
        style: 'cancel',
        onPress: onCancel,
      },
      {
        text: 'Confirmer',
        style: 'destructive',
        onPress: onConfirm,
      },
    ],
    { cancelable: true }
  );
}

export default {
  handleError,
  handleSuccess,
  handleWarning,
  handleLoading,
  dismissLoading,
  handleApiError,
  showConfirmation,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  WARNING_MESSAGES,
};
