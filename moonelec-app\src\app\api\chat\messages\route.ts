import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';

// GET /api/chat/messages?conversationId=xxx - Get messages for a conversation
export async function GET(req: NextRequest) {
  try {
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const searchParams = req.nextUrl.searchParams;
    const conversationId = searchParams.get('conversationId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (!conversationId) {
      return NextResponse.json({ error: 'Conversation ID is required' }, { status: 400 });
    }

    // Verify user has access to this conversation
    const conversation = await prisma.chatconversation.findUnique({
      where: { id: conversationId },
      include: {
        admin: { include: { user: true } },
        commercial: { include: { user: true } }
      }
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Check if user is part of this conversation
    const isParticipant = 
      conversation.admin.user.id === user.id || 
      conversation.commercial.user.id === user.id;

    if (!isParticipant) {
      return NextResponse.json({ error: 'Access denied to this conversation' }, { status: 403 });
    }

    // Get messages with pagination
    const messages = await prisma.chatmessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Mark messages as read for the current user
    const userType = user.role === 'ADMIN' ? 'ADMIN' : 'COMMERCIAL';
    await prisma.chatmessage.updateMany({
      where: {
        conversationId,
        senderType: { not: userType },
        isRead: false,
      },
      data: { isRead: true }
    });

    return NextResponse.json({ 
      messages: messages.reverse(), // Return in chronological order
      conversation 
    });

  } catch (error) {
    console.error('Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/chat/messages - Send a new message
export async function POST(req: NextRequest) {
  try {
    const mobileUser = await getMobileUserFromRequest(req);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'COMMERCIAL')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { conversationId, content, messageType = 'TEXT', fileUrl, fileName } = await req.json();

    if (!conversationId || !content) {
      return NextResponse.json({ 
        error: 'Conversation ID and content are required' 
      }, { status: 400 });
    }

    // Verify user has access to this conversation
    const conversation = await prisma.chatconversation.findUnique({
      where: { id: conversationId },
      include: {
        admin: { include: { user: true } },
        commercial: { include: { user: true } }
      }
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Check if user is part of this conversation
    const isParticipant = 
      conversation.admin.user.id === user.id || 
      conversation.commercial.user.id === user.id;

    if (!isParticipant) {
      return NextResponse.json({ error: 'Access denied to this conversation' }, { status: 403 });
    }

    const senderType = user.role === 'ADMIN' ? 'ADMIN' : 'COMMERCIAL';
    const senderId = user.id;

    // Create the message
    const message = await prisma.chatmessage.create({
      data: {
        conversationId,
        senderId,
        senderType,
        content,
        messageType,
        fileUrl,
        fileName,
      }
    });

    // Update conversation's last message info
    await prisma.chatconversation.update({
      where: { id: conversationId },
      data: {
        lastMessage: content,
        lastMessageAt: new Date(),
        updatedAt: new Date(),
      }
    });

    return NextResponse.json({ message }, { status: 201 });

  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
