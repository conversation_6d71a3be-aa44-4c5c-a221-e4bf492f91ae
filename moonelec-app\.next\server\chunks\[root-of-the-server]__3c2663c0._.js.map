{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/categories.ts"], "sourcesContent": ["import { prisma } from './prisma';\n\n// Get all categories\nexport async function getCategories(options?: {\n  search?: string;\n  includeProducts?: boolean;\n  skip?: number;\n  take?: number;\n}) {\n  const { search, includeProducts = false, skip = 0, take = 50 } = options || {};\n\n  const where = search\n    ? {\n        OR: [\n          { name: { contains: search } },\n          { description: { contains: search } },\n        ],\n      }\n    : {};\n\n  const [categories, total] = await Promise.all([\n    prisma.category.findMany({\n      where,\n      include: {\n        product: includeProducts,\n      },\n      skip,\n      take,\n      orderBy: {\n        name: 'asc',\n      },\n    }),\n    prisma.category.count({ where }),\n  ]);\n\n  return { categories, total };\n}\n\n// Get a single category by ID\nexport async function getCategoryById(id: string, includeProducts: boolean = false) {\n  return prisma.category.findUnique({\n    where: { id },\n    include: {\n      product: includeProducts,\n    },\n  });\n}\n\n// Create a new category\nexport async function createCategory(data: {\n  name: string;\n  description?: string;\n  image?: string;\n}) {\n  // Check if a category with the same name already exists\n  const existingCategory = await prisma.category.findFirst({\n    where: {\n      name: data.name,\n    },\n  });\n\n  if (existingCategory) {\n    throw new Error(`Une catégorie avec le nom ${data.name} existe déjà`);\n  }\n\n  return prisma.category.create({\n    data,\n  });\n}\n\n// Update an existing category\nexport async function updateCategory(\n  id: string,\n  data: {\n    name?: string;\n    description?: string;\n    image?: string;\n  }\n) {\n  // If name is being updated, check if it already exists\n  if (data.name) {\n    const existingCategory = await prisma.category.findFirst({\n      where: {\n        name: data.name,\n        id: { not: id },\n      },\n    });\n\n    if (existingCategory) {\n      throw new Error(`Une catégorie avec le nom ${data.name} existe déjà`);\n    }\n  }\n\n  return prisma.category.update({\n    where: { id },\n    data,\n  });\n}\n\n// Delete a category\nexport async function deleteCategory(id: string) {\n  // First, update all products in this category to have null categoryId\n  await prisma.product.updateMany({\n    where: { categoryId: id },\n    data: { categoryId: null },\n  });\n\n  // Then delete the category\n  return prisma.category.delete({\n    where: { id },\n  });\n}\n\n// Get category with product count\nexport async function getCategoriesWithProductCount() {\n  const categories = await prisma.category.findMany({\n    orderBy: {\n      name: 'asc',\n    },\n  });\n\n  const categoriesWithCount = await Promise.all(\n    categories.map(async (category) => {\n      const count = await prisma.product.count({\n        where: { categoryId: category.id },\n      });\n      return {\n        ...category,\n        productCount: count,\n      };\n    })\n  );\n\n  return categoriesWithCount;\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,eAAe,cAAc,OAKnC;IACC,MAAM,EAAE,MAAM,EAAE,kBAAkB,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAE7E,MAAM,QAAQ,SACV;QACE,IAAI;YACF;gBAAE,MAAM;oBAAE,UAAU;gBAAO;YAAE;YAC7B;gBAAE,aAAa;oBAAE,UAAU;gBAAO;YAAE;SACrC;IACH,IACA,CAAC;IAEL,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC5C,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACvB;YACA,SAAS;gBACP,SAAS;YACX;YACA;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE;QAAM;KAC/B;IAED,OAAO;QAAE;QAAY;IAAM;AAC7B;AAGO,eAAe,gBAAgB,EAAU,EAAE,kBAA2B,KAAK;IAChF,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAChC,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,SAAS;QACX;IACF;AACF;AAGO,eAAe,eAAe,IAIpC;IACC,wDAAwD;IACxD,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QACvD,OAAO;YACL,MAAM,KAAK,IAAI;QACjB;IACF;IAEA,IAAI,kBAAkB;QACpB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;IACtE;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B;IACF;AACF;AAGO,eAAe,eACpB,EAAU,EACV,IAIC;IAED,uDAAuD;IACvD,IAAI,KAAK,IAAI,EAAE;QACb,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACvD,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,IAAI;oBAAE,KAAK;gBAAG;YAChB;QACF;QAEA,IAAI,kBAAkB;YACpB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;QACtE;IACF;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B,OAAO;YAAE;QAAG;QACZ;IACF;AACF;AAGO,eAAe,eAAe,EAAU;IAC7C,sEAAsE;IACtE,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YAAE,YAAY;QAAG;QACxB,MAAM;YAAE,YAAY;QAAK;IAC3B;IAEA,2BAA2B;IAC3B,OAAO,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChD,SAAS;YACP,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,WAAW,GAAG,CAAC,OAAO;QACpB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACvC,OAAO;gBAAE,YAAY,SAAS,EAAE;YAAC;QACnC;QACA,OAAO;YACL,GAAG,QAAQ;YACX,cAAc;QAChB;IACF;IAGF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/mobile-auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport interface MobileUser {\n  id: string;\n  username: string;\n  email: string;\n  firstname: string;\n  lastname: string;\n  role: string;\n  isActive: boolean;\n}\n\nexport async function verifyMobileToken(request: NextRequest): Promise<MobileUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization');\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n\n    // Verify JWT token\n    const decoded = jwt.verify(\n      token,\n      process.env.NEXTAUTH_SECRET || 'fallback-secret'\n    ) as any;\n\n    // Get fresh user data from database\n    const user = await prisma.user.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        username: true,\n        email: true,\n        firstname: true,\n        lastname: true,\n        role: true,\n        isActive: true,\n      }\n    });\n\n    if (!user || !user.isActive) {\n      return null;\n    }\n\n    return user;\n  } catch (error) {\n    console.error('Mobile token verification error:', error);\n    return null;\n  }\n}\n\nexport function isMobileRequest(request: NextRequest): boolean {\n  const userAgent = request.headers.get('user-agent') || '';\n  const authHeader = request.headers.get('authorization');\n  \n  // Check if it's a mobile request with JWT token\n  return authHeader?.startsWith('Bearer ') || \n         userAgent.includes('Expo') || \n         userAgent.includes('ReactNative');\n}\n\nexport async function getMobileUserFromRequest(request: NextRequest): Promise<MobileUser | null> {\n  if (!isMobileRequest(request)) {\n    return null;\n  }\n  \n  return await verifyMobileToken(request);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAYO,eAAe,kBAAkB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QAEjE,mBAAmB;QACnB,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CACxB,OACA,QAAQ,GAAG,CAAC,eAAe,IAAI;QAGjC,oCAAoC;QACpC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACvD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,gDAAgD;IAChD,OAAO,YAAY,WAAW,cACvB,UAAU,QAAQ,CAAC,WACnB,UAAU,QAAQ,CAAC;AAC5B;AAEO,eAAe,yBAAyB,OAAoB;IACjE,IAAI,CAAC,gBAAgB,UAAU;QAC7B,OAAO;IACT;IAEA,OAAO,MAAM,kBAAkB;AACjC", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/categories/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getCategories, createCategory, getCategoriesWithProductCount } from '@/lib/categories';\nimport { getMobileUserFromRequest } from '@/lib/mobile-auth';\n\n// GET /api/categories - Get all categories\nexport async function GET(req: NextRequest) {\n  try {\n    // Check for mobile authentication\n    const mobileUser = await getMobileUserFromRequest(req);\n    if (mobileUser) {\n      console.log('📱 Mobile user authenticated for categories:', mobileUser.username);\n    }\n\n    const searchParams = req.nextUrl.searchParams;\n    const search = searchParams.get('search') || undefined;\n    const includeProducts = searchParams.get('includeProducts') === 'true';\n    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;\n    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;\n    const withCount = searchParams.get('withCount') === 'true';\n\n    // Utiliser les données de la base de données\n    if (withCount) {\n      const categories = await getCategoriesWithProductCount();\n      return NextResponse.json({ categories, total: categories.length });\n    } else {\n      const { categories, total } = await getCategories({\n        search,\n        includeProducts,\n        skip,\n        take,\n      });\n\n      return NextResponse.json({ categories, total });\n    }\n  } catch (error: any) {\n    console.error('Error fetching categories:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch categories' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/categories - Create a new category\nexport async function POST(req: NextRequest) {\n  try {\n    const body = await req.json();\n    const { name, description, image } = body;\n\n    // Validate required fields\n    if (!name) {\n      return NextResponse.json(\n        { error: 'Name is required' },\n        { status: 400 }\n      );\n    }\n\n    // Create the category\n    const category = await createCategory({\n      name,\n      description,\n      image,\n    });\n\n    return NextResponse.json(category, { status: 201 });\n  } catch (error: any) {\n    console.error('Error creating category:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to create category' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,kCAAkC;QAClC,MAAM,aAAa,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;QAClD,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,gDAAgD,WAAW,QAAQ;QACjF;QAEA,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,kBAAkB,aAAa,GAAG,CAAC,uBAAuB;QAChE,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QAEpD,6CAA6C;QAC7C,IAAI,WAAW;YACb,MAAM,aAAa,MAAM,CAAA,GAAA,0HAAA,CAAA,gCAA6B,AAAD;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;gBAAY,OAAO,WAAW,MAAM;YAAC;QAClE,OAAO;YACL,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;gBAChD;gBACA;gBACA;gBACA;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;gBAAY;YAAM;QAC/C;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA6B,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;QAErC,2BAA2B;QAC3B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE;YACpC;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAA4B,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}