'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { FaFileAlt, FaEye, FaCheck, FaTimes, FaSpinner, FaDownload } from 'react-icons/fa';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';
import { QuoteStatus } from '@prisma/client';
import { formatDate } from '@/lib/utils';
import PageLayout from '@/components/layout/PageLayout';

interface Quote {
  id: string;
  quoteNumber: string;
  status: QuoteStatus;
  createdAt: string;
  updatedAt: string;
  totalAmount: number | null;
  validUntil: string | null;
  pdfUrl: string | null;
  quoteItems: {
    id: string;
    quantity: number;
    unitPrice: number | null;
    product: {
      id: string;
      name: string;
      reference: string;
    };
  }[];
}

export default function ClientQuotesPage() {
  const router = useRouter();
  const { user } = useAuth();

  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<QuoteStatus | ''>('');

  // Charger les devis du client
  useEffect(() => {
    const fetchQuotes = async () => {
      if (!user?.clientId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Construire l'URL avec les paramètres de filtrage
        let url = `/api/quotes?clientId=${user.clientId}`;

        if (statusFilter) {
          url += `&status=${statusFilter}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Erreur lors du chargement des devis');
        }

        const data = await response.json();
        setQuotes(data.quotes);
      } catch (error) {
        console.error('Error fetching quotes:', error);
        setError('Impossible de charger vos devis. Veuillez réessayer plus tard.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuotes();
  }, [user, statusFilter]);

  // Fonction pour obtenir la couleur en fonction du statut
  const getStatusColor = (status: QuoteStatus) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Fonction pour obtenir le libellé en français du statut
  const getStatusLabel = (status: QuoteStatus) => {
    switch (status) {
      case 'DRAFT':
        return 'Brouillon';
      case 'PENDING':
        return 'En attente';
      case 'APPROVED':
        return 'Approuvé';
      case 'REJECTED':
        return 'Refusé';
      case 'EXPIRED':
        return 'Expiré';
      default:
        return status;
    }
  };

  // Fonction pour accepter un devis
  const handleAcceptQuote = async (id: string) => {
    if (!confirm('Êtes-vous sûr de vouloir accepter ce devis ?')) {
      return;
    }

    try {
      console.log('Acceptation du devis:', id);

      const response = await fetch(`/api/quotes/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'APPROVED' }),
      });

      const responseData = await response.json();
      console.log('Réponse du serveur:', responseData);

      if (!response.ok) {
        throw new Error(`Erreur lors de l'acceptation du devis: ${responseData.error || response.statusText}`);
      }

      // Mettre à jour le statut du devis dans la liste
      setQuotes(quotes.map(quote =>
        quote.id === id ? { ...quote, status: 'APPROVED' as QuoteStatus } : quote
      ));

      alert('Devis accepté avec succès!');
    } catch (error: any) {
      console.error('Error accepting quote:', error);
      alert(`Impossible d'accepter le devis: ${error.message}`);
    }
  };

  // Fonction pour refuser un devis
  const handleRejectQuote = async (id: string) => {
    if (!confirm('Êtes-vous sûr de vouloir refuser ce devis ?')) {
      return;
    }

    try {
      console.log('Refus du devis:', id);

      const response = await fetch(`/api/quotes/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'REJECTED' }),
      });

      const responseData = await response.json();
      console.log('Réponse du serveur:', responseData);

      if (!response.ok) {
        throw new Error(`Erreur lors du refus du devis: ${responseData.error || response.statusText}`);
      }

      // Mettre à jour le statut du devis dans la liste
      setQuotes(quotes.map(quote =>
        quote.id === id ? { ...quote, status: 'REJECTED' as QuoteStatus } : quote
      ));

      alert('Devis refusé avec succès!');
    } catch (error: any) {
      console.error('Error rejecting quote:', error);
      alert(`Impossible de refuser le devis: ${error.message}`);
    }
  };

  return (
    <PageLayout>
      <RouteGuard allowedRoles={['CLIENT', 'ADMIN']}>
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
              Mes Devis
            </h1>
            <div className="flex items-center space-x-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as QuoteStatus | '')}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Tous les statuts</option>
                <option value="DRAFT">Brouillon</option>
                <option value="PENDING">En attente</option>
                <option value="APPROVED">Approuvé</option>
                <option value="REJECTED">Refusé</option>
                <option value="EXPIRED">Expiré</option>
              </select>
              <Link href="/products">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                >
                  Parcourir les produits
                </motion.button>
              </Link>
            </div>
          </div>

          {/* Liste des devis */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <FaSpinner className="animate-spin text-primary text-2xl" />
              </div>
            ) : error ? (
              <div className="p-6 text-center text-red-500 dark:text-red-400">
                {error}
              </div>
            ) : quotes.length === 0 ? (
              <div className="p-8 text-center">
                <div className="flex justify-center mb-4">
                  <FaFileAlt className="text-5xl text-gray-400" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                  Aucun devis trouvé
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Vous n'avez pas encore de devis. Parcourez nos produits et ajoutez-les à votre panier pour demander un devis.
                </p>
                <Link href="/products">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                  >
                    Découvrir nos produits
                  </motion.button>
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Numéro
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Produits
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Montant
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {quotes.map((quote) => (
                      <tr key={quote.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {quote.quoteNumber}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {formatDate(quote.createdAt)}
                          </div>
                          {quote.validUntil && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Valide jusqu'au {formatDate(quote.validUntil)}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {quote.quoteItems.length} produit(s)
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs">
                            {quote.quoteItems.slice(0, 2).map(item => item.product.name).join(', ')}
                            {quote.quoteItems.length > 2 && '...'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(quote.status)}`}>
                            {getStatusLabel(quote.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {quote.totalAmount ? `${quote.totalAmount.toLocaleString('fr-MA')} MAD` : '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <Link href={`/account/quotes/${quote.id}`}>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                title="Voir"
                              >
                                <FaEye />
                              </motion.button>
                            </Link>

                            {quote.pdfUrl && (
                              <a href={quote.pdfUrl} target="_blank" rel="noopener noreferrer">
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                  title="Télécharger PDF"
                                >
                                  <FaDownload />
                                </motion.button>
                              </a>
                            )}

                            {quote.status === 'PENDING' && (
                              <>
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  onClick={() => handleAcceptQuote(quote.id)}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                  title="Accepter"
                                >
                                  <FaCheck />
                                </motion.button>

                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  onClick={() => handleRejectQuote(quote.id)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  title="Refuser"
                                >
                                  <FaTimes />
                                </motion.button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </RouteGuard>
    </PageLayout>
  );
}
