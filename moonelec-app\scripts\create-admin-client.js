import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    // Récupérer l'utilisateur admin
    const adminUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
      include: {
        admin: true,
      },
    });

    if (!adminUser) {
      console.log('L\'administrateur n\'existe pas.');
      return;
    }

    // Vérifier si l'administrateur a déjà un client associé
    const existingClient = await prisma.client.findFirst({
      where: {
        userId: adminUser.id,
      },
    });

    if (existingClient) {
      console.log('L\'administrateur a déjà un client associé:', existingClient);
      return;
    }

    // Créer un client pour l'administrateur
    const client = await prisma.client.create({
      data: {
        userId: adminUser.id,
        company_name: 'Moonelec Admin Test',
      },
    });

    console.log('Client créé pour l\'administrateur:', client);
  } catch (error) {
    console.error('Erreur lors de la création du client pour l\'administrateur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
