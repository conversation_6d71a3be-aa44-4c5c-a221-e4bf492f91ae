{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Déclaration pour éviter les erreurs TypeScript\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Création d'un client Prisma singleton\nexport const prisma = global.prisma || new PrismaClient();\n\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (process.env.NODE_ENV !== 'production') {\n  global.prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,SAAS,OAAO,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,0EAA0E;AAC1E,mEAAmE;AACnE,wCAA2C;IACzC,OAAO,MAAM,GAAG;AAClB", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/lib/brands.ts"], "sourcesContent": ["import { prisma } from './prisma';\n\n// Get all brands\nexport async function getBrands(options?: {\n  search?: string;\n  includeProducts?: boolean;\n  skip?: number;\n  take?: number;\n}) {\n  const { search, includeProducts = false, skip = 0, take = 50 } = options || {};\n\n  const where = search\n    ? {\n        OR: [\n          { name: { contains: search } },\n        ],\n      }\n    : {};\n\n  const [brands, total] = await Promise.all([\n    prisma.brand.findMany({\n      where,\n      include: {\n        product: includeProducts,\n      },\n      skip,\n      take,\n      orderBy: {\n        name: 'asc',\n      },\n    }),\n    prisma.brand.count({ where }),\n  ]);\n\n  return { brands, total };\n}\n\n// Get a single brand by ID\nexport async function getBrandById(id: string, includeProducts: boolean = false) {\n  return prisma.brand.findUnique({\n    where: { id },\n    include: {\n      product: includeProducts,\n    },\n  });\n}\n\n// Create a new brand\nexport async function createBrand(data: {\n  name: string;\n  image?: string;\n}) {\n  // Check if a brand with the same name already exists\n  const existingBrand = await prisma.brand.findFirst({\n    where: {\n      name: data.name,\n    },\n  });\n\n  if (existingBrand) {\n    throw new Error(`Une marque avec le nom ${data.name} existe déjà`);\n  }\n\n  return prisma.brand.create({\n    data: {\n      id: crypto.randomUUID(),\n      name: data.name,\n      image: data.image,\n      updatedAt: new Date(),\n    },\n  });\n}\n\n// Update an existing brand\nexport async function updateBrand(\n  id: string,\n  data: {\n    name?: string;\n    image?: string;\n  }\n) {\n  // If name is being updated, check if it already exists\n  if (data.name) {\n    const existingBrand = await prisma.brand.findFirst({\n      where: {\n        name: data.name,\n        id: { not: id },\n      },\n    });\n\n    if (existingBrand) {\n      throw new Error(`Une marque avec le nom ${data.name} existe déjà`);\n    }\n  }\n\n  return prisma.brand.update({\n    where: { id },\n    data: {\n      ...data,\n      updatedAt: new Date(),\n    },\n  });\n}\n\n// Delete a brand\nexport async function deleteBrand(id: string) {\n  // First, update all products in this brand to have null brandId\n  await prisma.product.updateMany({\n    where: { brandId: id },\n    data: { brandId: null },\n  });\n\n  // Then delete the brand\n  return prisma.brand.delete({\n    where: { id },\n  });\n}\n\n// Get brands with product count\nexport async function getBrandsWithProductCount() {\n  const brands = await prisma.brand.findMany({\n    orderBy: {\n      name: 'asc',\n    },\n  });\n\n  const brandsWithCount = await Promise.all(\n    brands.map(async (brand) => {\n      const count = await prisma.product.count({\n        where: { brandId: brand.id },\n      });\n      return {\n        ...brand,\n        productCount: count,\n      };\n    })\n  );\n\n  return brandsWithCount;\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,eAAe,UAAU,OAK/B;IACC,MAAM,EAAE,MAAM,EAAE,kBAAkB,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,WAAW,CAAC;IAE7E,MAAM,QAAQ,SACV;QACE,IAAI;YACF;gBAAE,MAAM;oBAAE,UAAU;gBAAO;YAAE;SAC9B;IACH,IACA,CAAC;IAEL,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QACxC,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpB;YACA,SAAS;gBACP,SAAS;YACX;YACA;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE;QAAM;KAC5B;IAED,OAAO;QAAE;QAAQ;IAAM;AACzB;AAGO,eAAe,aAAa,EAAU,EAAE,kBAA2B,KAAK;IAC7E,OAAO,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;QAC7B,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,SAAS;QACX;IACF;AACF;AAGO,eAAe,YAAY,IAGjC;IACC,qDAAqD;IACrD,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;QACjD,OAAO;YACL,MAAM,KAAK,IAAI;QACjB;IACF;IAEA,IAAI,eAAe;QACjB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;IACnE;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;QACzB,MAAM;YACJ,IAAI,OAAO,UAAU;YACrB,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,WAAW,IAAI;QACjB;IACF;AACF;AAGO,eAAe,YACpB,EAAU,EACV,IAGC;IAED,uDAAuD;IACvD,IAAI,KAAK,IAAI,EAAE;QACb,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACjD,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,IAAI;oBAAE,KAAK;gBAAG;YAChB;QACF;QAEA,IAAI,eAAe;YACjB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;QACnE;IACF;IAEA,OAAO,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;QACzB,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,GAAG,IAAI;YACP,WAAW,IAAI;QACjB;IACF;AACF;AAGO,eAAe,YAAY,EAAU;IAC1C,gEAAgE;IAChE,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9B,OAAO;YAAE,SAAS;QAAG;QACrB,MAAM;YAAE,SAAS;QAAK;IACxB;IAEA,wBAAwB;IACxB,OAAO,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;QACzB,OAAO;YAAE;QAAG;IACd;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACzC,SAAS;YACP,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CACvC,OAAO,GAAG,CAAC,OAAO;QAChB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACvC,OAAO;gBAAE,SAAS,MAAM,EAAE;YAAC;QAC7B;QACA,OAAO;YACL,GAAG,KAAK;YACR,cAAc;QAChB;IACF;IAGF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/MoonelecApp/moonelec-app/src/app/api/brands/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getBrands, createBrand, getBrandsWithProductCount } from '@/lib/brands';\n\n// GET /api/brands - Get all brands\nexport async function GET(req: NextRequest) {\n  try {\n    const searchParams = req.nextUrl.searchParams;\n    const search = searchParams.get('search') || undefined;\n    const includeProducts = searchParams.get('includeProducts') === 'true';\n    const skip = searchParams.has('skip') ? parseInt(searchParams.get('skip')!) : undefined;\n    const take = searchParams.has('take') ? parseInt(searchParams.get('take')!) : undefined;\n    const withCount = searchParams.get('withCount') === 'true';\n\n    // Utiliser les données de la base de données\n    if (withCount) {\n      const brands = await getBrandsWithProductCount();\n      return NextResponse.json({ brands, total: brands.length });\n    } else {\n      const { brands, total } = await getBrands({\n        search,\n        includeProducts,\n        skip,\n        take,\n      });\n\n      return NextResponse.json({ brands, total });\n    }\n  } catch (error: any) {\n    console.error('Error fetching brands:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to fetch brands' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/brands - Create a new brand\nexport async function POST(req: NextRequest) {\n  try {\n    const body = await req.json();\n    const { name, image } = body;\n\n    // Validate required fields\n    if (!name) {\n      return NextResponse.json(\n        { error: 'Name is required' },\n        { status: 400 }\n      );\n    }\n\n    // Create the brand\n    const brand = await createBrand({\n      name,\n      image,\n    });\n\n    return NextResponse.json(brand, { status: 201 });\n  } catch (error: any) {\n    console.error('Error creating brand:', error);\n    return NextResponse.json(\n      { error: error.message || 'Failed to create brand' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,kBAAkB,aAAa,GAAG,CAAC,uBAAuB;QAChE,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,OAAO,aAAa,GAAG,CAAC,UAAU,SAAS,aAAa,GAAG,CAAC,WAAY;QAC9E,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QAEpD,6CAA6C;QAC7C,IAAI,WAAW;YACb,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;gBAAQ,OAAO,OAAO,MAAM;YAAC;QAC1D,OAAO;YACL,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE;gBACxC;gBACA;gBACA;gBACA;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;gBAAQ;YAAM;QAC3C;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAyB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;QAExB,2BAA2B;QAC3B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;YAC9B;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO;YAAE,QAAQ;QAAI;IAChD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAyB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}