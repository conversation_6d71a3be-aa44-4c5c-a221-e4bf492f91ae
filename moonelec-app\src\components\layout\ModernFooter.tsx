'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  FaFacebook, 
  FaTwitter, 
  FaInstagram, 
  FaLinkedin,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaArrowUp,
  FaHeart
} from 'react-icons/fa';

export default function ModernFooter() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const footerLinks = {
    company: [
      { name: 'À Propos', href: '/about' },
      { name: 'Notre Histoire', href: '/about#history' },
      { name: 'Équi<PERSON>', href: '/about#team' },
      { name: '<PERSON><PERSON><PERSON>', href: '/careers' },
      { name: 'Actualités', href: '/news' }
    ],
    products: [
      { name: 'Éclairage LED', href: '/products?category=eclairage' },
      { name: 'Prises & Interrupteurs', href: '/products?category=prises' },
      { name: 'Protection Électrique', href: '/products?category=protection' },
      { name: 'Câbles', href: '/products?category=cables' },
      { name: 'Domotique', href: '/products?category=domotique' }
    ],
    services: [
      { name: 'Installation', href: '/services/installation' },
      { name: 'Maintenance', href: '/services/maintenance' },
      { name: 'Conseil Technique', href: '/services/consulting' },
      { name: 'Formation', href: '/services/training' },
      { name: 'Support 24/7', href: '/support' }
    ],
    support: [
      { name: 'Centre d\'Aide', href: '/help' },
      { name: 'Contact', href: '/contact' },
      { name: 'Garanties', href: '/warranty' },
      { name: 'Retours', href: '/returns' },
      { name: 'FAQ', href: '/faq' }
    ]
  };

  const socialLinks = [
    { name: 'Facebook', icon: <FaFacebook />, href: 'https://facebook.com/moonelec', color: 'hover:text-blue-600' },
    { name: 'Twitter', icon: <FaTwitter />, href: 'https://twitter.com/moonelec', color: 'hover:text-blue-400' },
    { name: 'Instagram', icon: <FaInstagram />, href: 'https://instagram.com/moonelec', color: 'hover:text-pink-600' },
    { name: 'LinkedIn', icon: <FaLinkedin />, href: 'https://linkedin.com/company/moonelec', color: 'hover:text-blue-700' }
  ];

  return (
    <footer className="bg-charcoal text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-6 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              {/* Logo */}
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-moonelec-red rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-2xl">M</span>
                </div>
                <span className="text-3xl font-bold font-heading">Moonelec</span>
              </div>

              <p className="text-gray-300 leading-relaxed mb-6">
                Votre partenaire de confiance pour tous vos besoins électriques au Maroc. 
                Nous offrons des solutions innovantes et de qualité supérieure depuis plus de 15 ans.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <FaPhone className="text-moonelec-red" />
                  <span className="text-gray-300">+212 5 22 XX XX XX</span>
                </div>
                <div className="flex items-center space-x-3">
                  <FaEnvelope className="text-moonelec-red" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <FaMapMarkerAlt className="text-moonelec-red" />
                  <span className="text-gray-300">Casablanca, Maroc</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Company Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-6 text-white">Entreprise</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-moonelec-red transition-colors duration-300"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Products Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-6 text-white">Produits</h3>
            <ul className="space-y-3">
              {footerLinks.products.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-electric-blue transition-colors duration-300"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Services Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-6 text-white">Services</h3>
            <ul className="space-y-3">
              {footerLinks.services.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-electric-blue transition-colors duration-300"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Support Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h3 className="text-lg font-semibold mb-6 text-white">Support</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-moonelec-red transition-colors duration-300"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Newsletter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-12 pt-8 border-t border-gray-700"
        >
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="text-2xl font-bold mb-4">Restez Informé</h3>
            <p className="text-gray-300 mb-6">
              Abonnez-vous à notre newsletter pour recevoir les dernières actualités et offres spéciales.
            </p>
            <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Votre adresse email"
                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-moonelec-red focus:border-transparent"
              />
              <button
                type="submit"
                className="btn btn-primary px-6 py-3 whitespace-nowrap"
              >
                S'abonner
              </button>
            </form>
          </div>
        </motion.div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-700 bg-gray-900">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-gray-400 text-sm">
              <p className="flex items-center">
                © 2024 Moonelec. Tous droits réservés. Fait avec 
                <FaHeart className="text-moonelec-red mx-1" />
                au Maroc.
              </p>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  className={`text-gray-400 ${social.color} transition-colors duration-300 text-xl`}
                  aria-label={social.name}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-4 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Confidentialité
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Conditions
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll to Top Button */}
      <motion.button
        onClick={scrollToTop}
        className="fixed bottom-8 right-8 p-3 bg-moonelec-red hover:bg-moonelec-red-dark text-white rounded-full shadow-lg transition-all duration-300 z-50"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        <FaArrowUp className="text-lg" />
      </motion.button>
    </footer>
  );
}
