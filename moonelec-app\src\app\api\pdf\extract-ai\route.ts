import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getMobileUserFromRequest } from '@/lib/mobile-auth';
import PDFExtractionService from '@/lib/pdf-extraction-ai';
import { checkIPSecurity, createSecureError, validateFileUpload } from '@/middleware/security';

// POST /api/pdf/extract-ai - Enhanced PDF extraction with AI and attribution
export async function POST(request: NextRequest) {
  try {
    // Security check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    if (!checkIPSecurity(clientIP)) {
      return createSecureError('Access denied', 403);
    }

    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = mobileUser || session?.user;
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    console.log('🤖 AI PDF extraction request from:', { 
      id: user.id, 
      role: user.role, 
      name: `${user.firstname} ${user.lastname}` 
    });

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const companyLogo = formData.get('companyLogo') as string | null;

    if (!file) {
      return NextResponse.json({ error: 'No PDF file provided' }, { status: 400 });
    }

    // Validate file
    try {
      validateFileUpload(file);
    } catch (validationError: any) {
      return NextResponse.json({ 
        error: validationError.message || 'Invalid file' 
      }, { status: 400 });
    }

    // Check if it's a PDF
    if (file.type !== 'application/pdf') {
      return NextResponse.json({ 
        error: 'Only PDF files are supported' 
      }, { status: 400 });
    }

    // Convert file to buffer
    const pdfBuffer = Buffer.from(await file.arrayBuffer());

    console.log('📄 Processing PDF:', {
      fileName: file.name,
      fileSize: file.size,
      extractorName: `${user.firstname} ${user.lastname}`
    });

    try {
      // Extract data with AI
      const extractionResult = await PDFExtractionService.extractProductDataFromPDF(
        pdfBuffer,
        `${user.firstname} ${user.lastname}`,
        companyLogo || undefined
      );

      // Generate formatted report
      const report = PDFExtractionService.generateExtractionReport(
        extractionResult,
        file.name
      );

      console.log('✅ AI extraction completed:', {
        confidence: extractionResult.attribution.confidence,
        productName: extractionResult.extractedData.productName,
        reference: extractionResult.extractedData.reference
      });

      return NextResponse.json({
        success: true,
        extraction: {
          ...extractionResult,
          formattedReport: report
        },
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          processingTime: Date.now(),
          extractorInfo: {
            name: `${user.firstname} ${user.lastname}`,
            role: user.role,
            extractionDate: new Date().toISOString()
          }
        }
      });

    } catch (extractionError: any) {
      console.error('❌ AI extraction failed:', extractionError);
      
      return NextResponse.json({
        error: 'Failed to extract data from PDF',
        details: extractionError.message,
        fallback: {
          message: 'AI extraction failed, please try manual extraction',
          supportedFormats: ['PDF with clear text', 'Structured documents']
        }
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ PDF extraction API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Failed to process PDF extraction request'
    }, { status: 500 });
  }
}

// GET /api/pdf/extract-ai - Get extraction capabilities info
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const mobileUser = await getMobileUserFromRequest(request);
    const session = await getServerSession(authOptions);

    if (!mobileUser && !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json({
      service: 'Moonelec AI PDF Extraction',
      capabilities: {
        aiModel: 'Xenova/distilbert-base-uncased',
        supportedFormats: ['PDF'],
        maxFileSize: '25MB',
        extractionFields: [
          'productName',
          'reference', 
          'description',
          'characteristics'
        ],
        features: [
          'AI-powered text extraction',
          'Automatic product identification',
          'Confidence scoring',
          'Attribution tracking',
          'Company branding support'
        ]
      },
      attribution: {
        required: true,
        includes: [
          'Extractor name',
          'Extraction date',
          'AI model used',
          'Confidence score',
          'Company logo (optional)'
        ]
      },
      usage: {
        endpoint: '/api/pdf/extract-ai',
        method: 'POST',
        contentType: 'multipart/form-data',
        parameters: {
          file: 'PDF file (required)',
          companyLogo: 'Company logo URL (optional)'
        }
      }
    });
  } catch (error) {
    console.error('Error getting extraction info:', error);
    return NextResponse.json({
      error: 'Failed to get extraction capabilities'
    }, { status: 500 });
  }
}
