import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, Image, Text } from 'react-native';

interface AnimatedLogoProps {
  size?: number;
  showText?: boolean;
  animationType?: 'bounce' | 'fade' | 'scale' | 'slide';
}

export const AnimatedLogo: React.FC<AnimatedLogoProps> = ({ 
  size = 120, 
  showText = true,
  animationType = 'bounce'
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0.5)).current;
  const rotateValue = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animations = [];

    switch (animationType) {
      case 'bounce':
        animations.push(
          Animated.spring(scaleValue, {
            toValue: 1,
            tension: 100,
            friction: 3,
            useNativeDriver: true,
          })
        );
        break;
      
      case 'fade':
        animations.push(
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          })
        );
        break;
      
      case 'scale':
        animations.push(
          Animated.sequence([
            Animated.timing(scaleValue, {
              toValue: 1.2,
              duration: 500,
              useNativeDriver: true,
            }),
            Animated.timing(scaleValue, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
          ])
        );
        break;
      
      case 'slide':
        animations.push(
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          })
        );
        break;
    }

    // Text animation
    if (showText) {
      animations.push(
        Animated.delay(500),
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        })
      );
    }

    // Subtle rotation animation
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 10000,
        useNativeDriver: true,
      })
    );

    Animated.parallel(animations).start();
    rotateAnimation.start();

    return () => {
      rotateAnimation.stop();
    };
  }, [animationType, showText]);

  const translateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [50, 0],
  });

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            transform: [
              { scale: scaleValue },
              { translateY: animationType === 'slide' ? translateY : 0 },
              { rotate: rotate },
            ],
            opacity: animationType === 'fade' ? opacity : 1,
          },
        ]}
      >
        {/* Logo principal */}
        <View style={[styles.logoCircle, { width: size, height: size }]}>
          <View style={[styles.innerCircle, { width: size * 0.7, height: size * 0.7 }]}>
            <Text style={[styles.logoText, { fontSize: size * 0.15 }]}>M</Text>
          </View>
          
          {/* Éléments électriques décoratifs */}
          <View style={[styles.electricElement1, { width: size * 0.1, height: size * 0.1 }]} />
          <View style={[styles.electricElement2, { width: size * 0.08, height: size * 0.08 }]} />
          <View style={[styles.electricElement3, { width: size * 0.06, height: size * 0.06 }]} />
        </View>
      </Animated.View>

      {showText && (
        <Animated.View style={[styles.textContainer, { opacity: textOpacity }]}>
          <Text style={styles.brandText}>MOONELEC</Text>
          <Text style={styles.taglineText}>Composants Électriques</Text>
        </Animated.View>
      )}
    </View>
  );
};

// Composant Logo simple pour les headers
export const SimpleLogo: React.FC<{ size?: number }> = ({ size = 40 }) => {
  return (
    <View style={[styles.logoCircle, { width: size, height: size }]}>
      <View style={[styles.innerCircle, { width: size * 0.7, height: size * 0.7 }]}>
        <Text style={[styles.logoText, { fontSize: size * 0.15 }]}>M</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  logoCircle: {
    backgroundColor: '#006db7',
    borderRadius: 1000,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    position: 'relative',
  },
  innerCircle: {
    backgroundColor: '#fff',
    borderRadius: 1000,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    color: '#006db7',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  electricElement1: {
    position: 'absolute',
    backgroundColor: '#ed1c24',
    borderRadius: 1000,
    top: '15%',
    right: '15%',
  },
  electricElement2: {
    position: 'absolute',
    backgroundColor: '#ed1c24',
    borderRadius: 1000,
    bottom: '20%',
    left: '20%',
  },
  electricElement3: {
    position: 'absolute',
    backgroundColor: '#ed1c24',
    borderRadius: 1000,
    top: '50%',
    right: '10%',
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  brandText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#006db7',
    letterSpacing: 2,
    textAlign: 'center',
  },
  taglineText: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default AnimatedLogo;
