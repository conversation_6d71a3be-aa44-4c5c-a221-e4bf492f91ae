/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/signup/page";
exports.ids = ["app/auth/signup/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signup/page.tsx */ \"(rsc)/./src/app/auth/signup/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'signup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/signup/page\",\n        pathname: \"/auth/signup\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signup/page.tsx */ \"(rsc)/./src/app/auth/signup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FzdXMlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVjdHMlNUMlNUNNb29uZWxlY0FwcCU1QyU1Q21vb25lbGVjLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNzaWdudXAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBc3VzXFxcXERlc2t0b3BcXFxcUHJvamVjdHNcXFxcTW9vbmVsZWNBcHBcXFxcbW9vbmVsZWMtYXBwXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxzaWdudXBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ToastProvider.tsx */ \"(rsc)/./src/components/ui/ToastProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(rsc)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/CartContext.tsx */ \"(rsc)/./src/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ThemeContext.tsx */ \"(rsc)/./src/context/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\app\\auth\\signup\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5b27a5b3657b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1YjI3YTViMzY1N2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/CartContext */ \"(rsc)/./src/context/CartContext.tsx\");\n/* harmony import */ var _context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/ThemeContext */ \"(rsc)/./src/context/ThemeContext.tsx\");\n/* harmony import */ var _components_ui_ToastProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ToastProvider */ \"(rsc)/./src/components/ui/ToastProvider.tsx\");\n/* harmony import */ var _lib_startupTasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/startupTasks */ \"(rsc)/./src/lib/startupTasks.ts\");\n\n\n\n\n\n\n\n// Run startup tasks (only on server)\nif (true) {\n    (0,_lib_startupTasks__WEBPACK_IMPORTED_MODULE_6__.runStartupTasks)();\n}\nconst metadata = {\n    title: \"Moonelec - Distribution de Matériel Électrique\",\n    description: \"Moonelec, spécialiste en distribution de matériel électrique depuis 1990. Plus de 300 000 références produits dans les secteurs résidentiel, tertiaire et industriel.\",\n    manifest: \"/manifest.json\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300 font-sans\",\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_CartContext__WEBPACK_IMPORTED_MODULE_3__.CartProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ToastProvider__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/ToastProvider.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ToastProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\ToastProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\components\\ui\\ToastProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\AuthContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\AuthContext.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./src/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ CartProvider),
/* harmony export */   useCart: () => (/* binding */ useCart)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CartProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\CartContext.tsx",
"CartProvider",
);const useCart = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\CartContext.tsx",
"useCart",
);

/***/ }),

/***/ "(rsc)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Projects\\MoonelecApp\\moonelec-app\\src\\context\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Création d'un client Prisma singleton\nconst prisma = global.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n// En développement, nous attachons le client à l'objet global pour éviter\n// de créer de nouvelles instances à chaque rechargement du serveur\nif (true) {\n    global.prisma = prisma;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFPOUMsd0NBQXdDO0FBQ2pDLE1BQU1DLFNBQVNDLE9BQU9ELE1BQU0sSUFBSSxJQUFJRCx3REFBWUEsR0FBRztBQUUxRCwwRUFBMEU7QUFDMUUsbUVBQW1FO0FBQ25FLElBQUlHLElBQXFDLEVBQUU7SUFDekNELE9BQU9ELE1BQU0sR0FBR0E7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBEw6ljbGFyYXRpb24gcG91ciDDqXZpdGVyIGxlcyBlcnJldXJzIFR5cGVTY3JpcHRcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDcsOpYXRpb24gZCd1biBjbGllbnQgUHJpc21hIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbC5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpO1xuXG4vLyBFbiBkw6l2ZWxvcHBlbWVudCwgbm91cyBhdHRhY2hvbnMgbGUgY2xpZW50IMOgIGwnb2JqZXQgZ2xvYmFsIHBvdXIgw6l2aXRlclxuLy8gZGUgY3LDqWVyIGRlIG5vdXZlbGxlcyBpbnN0YW5jZXMgw6AgY2hhcXVlIHJlY2hhcmdlbWVudCBkdSBzZXJ2ZXVyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBnbG9iYWwucHJpc21hID0gcHJpc21hO1xufVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbCIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/reportReminders.ts":
/*!************************************!*\
  !*** ./src/lib/reportReminders.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIncompleteReports: () => (/* binding */ checkIncompleteReports),\n/* harmony export */   scheduleReportReminders: () => (/* binding */ scheduleReportReminders)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n// Check for incomplete reports and send reminders\nasync function checkIncompleteReports() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    // Get all commercials\n    const commercials = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.commercial.findMany({\n        include: {\n            user: true\n        }\n    });\n    const results = [];\n    for (const commercial of commercials){\n        // Check if the commercial has submitted a report today\n        const report = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findFirst({\n            where: {\n                commercialId: commercial.id,\n                submittedAt: {\n                    gte: today\n                },\n                isCompleted: true\n            }\n        });\n        if (!report) {\n            // Commercial hasn't submitted a report today\n            // Check if we need to send a reminder (every 2 hours)\n            const lastReminder = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.findFirst({\n                where: {\n                    commercialId: commercial.id,\n                    lastReminder: {\n                        not: null\n                    },\n                    isCompleted: false\n                },\n                orderBy: {\n                    lastReminder: 'desc'\n                }\n            });\n            const twoHoursAgo = new Date();\n            twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);\n            if (!lastReminder || lastReminder.lastReminder && lastReminder.lastReminder < twoHoursAgo) {\n                // Create or update a reminder report\n                const reportId = lastReminder ? lastReminder.id : (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const reminderReport = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.salesreport.upsert({\n                    where: {\n                        id: reportId\n                    },\n                    update: {\n                        lastReminder: new Date()\n                    },\n                    create: {\n                        id: reportId,\n                        commercialId: commercial.id,\n                        need: '',\n                        visitDate: today,\n                        denomination: '',\n                        name: '',\n                        visitPurpose: '',\n                        city: '',\n                        lastReminder: new Date(),\n                        isCompleted: false\n                    }\n                });\n                // Create notifications for admins\n                const admins = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.admin.findMany();\n                for (const admin of admins){\n                    await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n                        data: {\n                            id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                            type: 'REPORT_REMINDER',\n                            message: `${commercial.user.firstname} ${commercial.user.lastname} has not submitted their daily report yet.`,\n                            adminId: admin.id,\n                            salesReportId: reminderReport.id,\n                            isRead: false\n                        }\n                    });\n                }\n                results.push({\n                    commercial,\n                    reminderReport,\n                    needsReminder: true\n                });\n            } else {\n                results.push({\n                    commercial,\n                    needsReminder: false\n                });\n            }\n        } else {\n            results.push({\n                commercial,\n                report,\n                needsReminder: false\n            });\n        }\n    }\n    return results;\n}\n// Schedule the check to run every 2 hours\nfunction scheduleReportReminders() {\n    // Only run in production to avoid multiple instances in development\n    if (false) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/reportReminders.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/startupTasks.ts":
/*!*********************************!*\
  !*** ./src/lib/startupTasks.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runStartupTasks: () => (/* binding */ runStartupTasks)\n/* harmony export */ });\n/* harmony import */ var _reportReminders__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reportReminders */ \"(rsc)/./src/lib/reportReminders.ts\");\n\n// Run startup tasks\nfunction runStartupTasks() {\n    // Schedule report reminders\n    (0,_reportReminders__WEBPACK_IMPORTED_MODULE_0__.scheduleReportReminders)();\n    console.log('Startup tasks completed');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0YXJ0dXBUYXNrcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0RDtBQUU1RCxvQkFBb0I7QUFDYixTQUFTQztJQUNkLDRCQUE0QjtJQUM1QkQseUVBQXVCQTtJQUV2QkUsUUFBUUMsR0FBRyxDQUFDO0FBQ2QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcbGliXFxzdGFydHVwVGFza3MudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2NoZWR1bGVSZXBvcnRSZW1pbmRlcnMgfSBmcm9tICcuL3JlcG9ydFJlbWluZGVycyc7XG5cbi8vIFJ1biBzdGFydHVwIHRhc2tzXG5leHBvcnQgZnVuY3Rpb24gcnVuU3RhcnR1cFRhc2tzKCkge1xuICAvLyBTY2hlZHVsZSByZXBvcnQgcmVtaW5kZXJzXG4gIHNjaGVkdWxlUmVwb3J0UmVtaW5kZXJzKCk7XG4gIFxuICBjb25zb2xlLmxvZygnU3RhcnR1cCB0YXNrcyBjb21wbGV0ZWQnKTtcbn1cbiJdLCJuYW1lcyI6WyJzY2hlZHVsZVJlcG9ydFJlbWluZGVycyIsInJ1blN0YXJ0dXBUYXNrcyIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/startupTasks.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signup/page.tsx */ \"(ssr)/./src/app/auth/signup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FzdXMlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVjdHMlNUMlNUNNb29uZWxlY0FwcCU1QyU1Q21vb25lbGVjLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNzaWdudXAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQW9JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBc3VzXFxcXERlc2t0b3BcXFxcUHJvamVjdHNcXFxcTW9vbmVsZWNBcHBcXFxcbW9vbmVsZWMtYXBwXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxzaWdudXBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/ToastProvider.tsx */ \"(ssr)/./src/components/ui/ToastProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/CartContext.tsx */ \"(ssr)/./src/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/ThemeContext.tsx */ \"(ssr)/./src/context/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CToastProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CProjects%5C%5CMoonelecApp%5C%5Cmoonelec-app%5C%5Csrc%5C%5Ccontext%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* harmony import */ var _components_auth_SignUpForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/SignUpForm */ \"(ssr)/./src/components/auth/SignUpForm.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SignUpPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isLoading, redirectToDashboard } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignUpPage.useEffect\": ()=>{\n            // Si l'utilisateur est déjà authentifié, le rediriger vers son tableau de bord\n            if (!isLoading && isAuthenticated) {\n                redirectToDashboard();\n            }\n        }\n    }[\"SignUpPage.useEffect\"], [\n        isLoading,\n        isAuthenticated,\n        redirectToDashboard\n    ]);\n    // Ne pas afficher la page si l'utilisateur est déjà authentifié\n    if (isLoading || isAuthenticated) {\n        return null;\n    }\n    const benefits = [\n        \"Accès à plus de 300 000 références produits\",\n        \"Suivi de commandes en temps réel\",\n        \"Devis personnalisés pour vos projets\",\n        \"Support technique dédié\",\n        \"Offres exclusives réservées aux membres\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col md:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block md:w-1/2 relative bg-secondary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-secondary to-secondary/70\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col justify-center items-center text-white p-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold mb-6 text-center\",\n                                children: \"Rejoignez la Communaut\\xe9 Moonelec\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-center mb-8\",\n                                children: \"Cr\\xe9ez votre compte pour acc\\xe9der \\xe0 nos services exclusifs et d\\xe9couvrir notre vaste gamme de produits \\xe9lectriques de qualit\\xe9.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 w-full max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"bg-white/10 backdrop-blur-sm p-6 border-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-4 text-xl\",\n                                            children: \"Avantages de l'inscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mr-2\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 51,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: benefit\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 52,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full md:w-1/2 flex flex-col justify-center items-center p-8 md:p-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 flex justify-center md:justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                width: 180,\n                                height: 60\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_SignUpForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/signup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/SignUpForm.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/SignUpForm.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaEnvelope,FaEye,FaEyeSlash,FaFacebook,FaGoogle,FaLock,FaPhone,FaUser!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction SignUpForm() {\n    const [formState, setFormState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        phone: '',\n        company: '',\n        password: '',\n        confirmPassword: '',\n        agreeTerms: false\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormState((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? checked : value\n            }));\n        // Clear password error when user types\n        if (name === 'password' || name === 'confirmPassword') {\n            setPasswordError('');\n        }\n    };\n    const validateStep1 = ()=>{\n        if (!formState.firstName || !formState.lastName || !formState.email) {\n            return false;\n        }\n        return true;\n    };\n    const validateStep2 = ()=>{\n        if (!formState.password || !formState.confirmPassword || !formState.agreeTerms) {\n            return false;\n        }\n        if (formState.password !== formState.confirmPassword) {\n            setPasswordError('Les mots de passe ne correspondent pas');\n            return false;\n        }\n        if (formState.password.length < 8) {\n            setPasswordError('Le mot de passe doit contenir au moins 8 caractères');\n            return false;\n        }\n        return true;\n    };\n    const handleNextStep = ()=>{\n        if (validateStep1()) {\n            setStep(2);\n        }\n    };\n    const handlePrevStep = ()=>{\n        setStep(1);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateStep2()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Appel à l'API pour créer un compte\n            const response = await fetch('/api/auth/signup', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    firstname: formState.firstName,\n                    lastname: formState.lastName,\n                    email: formState.email,\n                    username: formState.email.split('@')[0],\n                    password: formState.password,\n                    telephone: formState.phone || undefined,\n                    company_name: formState.company || undefined\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Une erreur est survenue lors de l\\'inscription');\n            }\n            // Redirection vers la page de connexion avec un message de succès\n            window.location.href = '/auth/signin?success=account-created';\n        } catch (error) {\n            console.error('Erreur lors de l\\'inscription:', error);\n            setPasswordError(error.message || 'Une erreur est survenue lors de l\\'inscription');\n            setIsSubmitting(false);\n        }\n    };\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const toggleConfirmPasswordVisibility = ()=>{\n        setShowConfirmPassword(!showConfirmPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-md mx-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"bg-white dark:bg-[#1a1a1a] rounded-lg shadow-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"Cr\\xe9er un compte\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary\",\n                                    children: \"Rejoignez Moonelec pour acc\\xe9der \\xe0 nos services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'}`,\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-16 h-1 ${step >= 2 ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-700'}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'}`,\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.1\n                                            },\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"firstName\",\n                                                            className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                            children: \"Pr\\xe9nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaUser, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"firstName\",\n                                                                    name: \"firstName\",\n                                                                    value: formState.firstName,\n                                                                    onChange: handleChange,\n                                                                    required: true,\n                                                                    className: \"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                                    placeholder: \"Jean\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"lastName\",\n                                                            className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"lastName\",\n                                                            name: \"lastName\",\n                                                            value: formState.lastName,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                            placeholder: \"Dupont\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaEnvelope, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            value: formState.email,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"phone\",\n                                                    className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                    children: \"T\\xe9l\\xe9phone (optionnel)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaPhone, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            id: \"phone\",\n                                                            name: \"phone\",\n                                                            value: formState.phone,\n                                                            onChange: handleChange,\n                                                            className: \"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                            placeholder: \"+33 1 23 45 67 89\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"company\",\n                                                    className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                    children: \"Entreprise (optionnel)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaBuilding, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"company\",\n                                                            name: \"company\",\n                                                            value: formState.company,\n                                                            onChange: handleChange,\n                                                            className: \"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                            placeholder: \"Nom de votre entreprise\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            type: \"button\",\n                                            onClick: handleNextStep,\n                                            disabled: !validateStep1(),\n                                            className: `btn-primary w-full py-3 ${!validateStep1() ? 'opacity-70 cursor-not-allowed' : ''}`,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.5\n                                            },\n                                            whileHover: validateStep1() ? {\n                                                scale: 1.02\n                                            } : {},\n                                            whileTap: validateStep1() ? {\n                                                scale: 0.98\n                                            } : {},\n                                            children: \"Continuer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                    children: \"Mot de passe\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaLock, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            id: \"password\",\n                                                            name: \"password\",\n                                                            value: formState.password,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                            placeholder: \"••••••••\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: togglePasswordVisibility,\n                                                            className: \"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaEyeSlash, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 39\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaEye, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 56\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"Le mot de passe doit contenir au moins 8 caract\\xe8res\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"confirmPassword\",\n                                                    className: \"block text-sm font-medium text-text-secondary mb-1\",\n                                                    children: \"Confirmer le mot de passe\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaLock, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                                            id: \"confirmPassword\",\n                                                            name: \"confirmPassword\",\n                                                            value: formState.confirmPassword,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-[#111] dark:text-white\",\n                                                            placeholder: \"••••••••\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: toggleConfirmPasswordVisibility,\n                                                            className: \"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaEyeSlash, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 46\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaEye, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 63\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: passwordError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.3\n                                            },\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    id: \"agreeTerms\",\n                                                    name: \"agreeTerms\",\n                                                    checked: formState.agreeTerms,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: \"w-4 h-4 mt-1 text-primary border-gray-300 rounded focus:ring-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"agreeTerms\",\n                                                    className: \"ml-2 text-sm text-text-secondary\",\n                                                    children: [\n                                                        \"J'accepte les\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/terms\",\n                                                            className: \"text-primary hover:underline\",\n                                                            children: \"conditions d'utilisation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        ' ',\n                                                        \"et la\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/privacy\",\n                                                            className: \"text-primary hover:underline\",\n                                                            children: \"politique de confidentialit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                    type: \"button\",\n                                                    onClick: handlePrevStep,\n                                                    className: \"btn-outline w-1/3 py-3\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 10\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3,\n                                                        delay: 0.4\n                                                    },\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: \"Retour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting || !validateStep2(),\n                                                    className: `btn-primary w-2/3 py-3 ${isSubmitting || !validateStep2() ? 'opacity-70 cursor-not-allowed' : ''}`,\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 10\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3,\n                                                        delay: 0.5\n                                                    },\n                                                    whileHover: validateStep2() && !isSubmitting ? {\n                                                        scale: 1.02\n                                                    } : {},\n                                                    whileTap: validateStep2() && !isSubmitting ? {\n                                                        scale: 0.98\n                                                    } : {},\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Inscription en cours...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 23\n                                                    }, this) : 'S\\'inscrire'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3,\n                                delay: 0.6\n                            },\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-300 dark:border-gray-700 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bg-white dark:bg-[#1a1a1a] px-4 text-sm text-gray-500\",\n                                            children: \"ou\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"flex items-center justify-center gap-2 py-2.5 px-4 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-[#222] transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaGoogle, {\n                                                    className: \"text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Google\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"flex items-center justify-center gap-2 py-2.5 px-4 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-[#222] transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaEnvelope_FaEye_FaEyeSlash_FaFacebook_FaGoogle_FaLock_FaPhone_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaFacebook, {\n                                                    className: \"text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Facebook\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        delay: 0.7\n                    },\n                    className: \"bg-gray-50 dark:bg-[#111] py-4 px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary\",\n                        children: [\n                            \"Vous avez d\\xe9j\\xe0 un compte?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/auth/signin\",\n                                className: \"text-primary font-medium hover:underline\",\n                                children: \"Se connecter\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/SignUpForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Card)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Card({ children, className = '', animate = false, delay = 0, onClick, hoverEffect = true }) {\n    const baseStyles = 'bg-white dark:bg-[#1a1a1a] rounded-lg shadow-md overflow-hidden';\n    if (animate) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            className: `${baseStyles} ${className}`,\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            whileInView: {\n                opacity: 1,\n                y: 0\n            },\n            viewport: {\n                once: true,\n                margin: '-50px'\n            },\n            transition: {\n                duration: 0.5,\n                delay\n            },\n            onClick: onClick,\n            whileHover: hoverEffect ? {\n                y: -5,\n                transition: {\n                    duration: 0.2\n                }\n            } : {},\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${baseStyles} ${className} ${hoverEffect ? 'transition-transform hover:-translate-y-1 duration-200' : ''}`,\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Logo.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Logo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Logo({ width = 180, height = 60, className = '', linkClassName = '', href = '/' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: href,\n        className: `flex items-center ${linkClassName}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            src: \"/images/logo/logo-moonelec.png\",\n            alt: \"Moonelec Logo\",\n            width: width,\n            height: height,\n            className: className,\n            priority: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\Logo.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2dvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRStCO0FBQ0Y7QUFVZCxTQUFTRSxLQUFLLEVBQzNCQyxRQUFRLEdBQUcsRUFDWEMsU0FBUyxFQUFFLEVBQ1hDLFlBQVksRUFBRSxFQUNkQyxnQkFBZ0IsRUFBRSxFQUNsQkMsT0FBTyxHQUFHLEVBQ0E7SUFDVixxQkFDRSw4REFBQ04sa0RBQUlBO1FBQUNNLE1BQU1BO1FBQU1GLFdBQVcsQ0FBQyxrQkFBa0IsRUFBRUMsZUFBZTtrQkFDL0QsNEVBQUNOLGtEQUFLQTtZQUNKUSxLQUFJO1lBQ0pDLEtBQUk7WUFDSk4sT0FBT0E7WUFDUEMsUUFBUUE7WUFDUkMsV0FBV0E7WUFDWEssUUFBUTs7Ozs7Ozs7Ozs7QUFJaEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcUHJvamVjdHNcXE1vb25lbGVjQXBwXFxtb29uZWxlYy1hcHBcXHNyY1xcY29tcG9uZW50c1xcdWlcXExvZ28udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcblxuaW50ZXJmYWNlIExvZ29Qcm9wcyB7XG4gIHdpZHRoPzogbnVtYmVyO1xuICBoZWlnaHQ/OiBudW1iZXI7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgbGlua0NsYXNzTmFtZT86IHN0cmluZztcbiAgaHJlZj86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9nbyh7XG4gIHdpZHRoID0gMTgwLFxuICBoZWlnaHQgPSA2MCxcbiAgY2xhc3NOYW1lID0gJycsXG4gIGxpbmtDbGFzc05hbWUgPSAnJyxcbiAgaHJlZiA9ICcvJ1xufTogTG9nb1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPExpbmsgaHJlZj17aHJlZn0gY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgJHtsaW5rQ2xhc3NOYW1lfWB9PlxuICAgICAgPEltYWdlXG4gICAgICAgIHNyYz1cIi9pbWFnZXMvbG9nby9sb2dvLW1vb25lbGVjLnBuZ1wiXG4gICAgICAgIGFsdD1cIk1vb25lbGVjIExvZ29cIlxuICAgICAgICB3aWR0aD17d2lkdGh9XG4gICAgICAgIGhlaWdodD17aGVpZ2h0fVxuICAgICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZX1cbiAgICAgICAgcHJpb3JpdHlcbiAgICAgIC8+XG4gICAgPC9MaW5rPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkltYWdlIiwiTGluayIsIkxvZ28iLCJ3aWR0aCIsImhlaWdodCIsImNsYXNzTmFtZSIsImxpbmtDbGFzc05hbWUiLCJocmVmIiwic3JjIiwiYWx0IiwicHJpb3JpdHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ToastProvider.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/ToastProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n            duration: 3000,\n            style: {\n                background: '#363636',\n                color: '#fff'\n            },\n            success: {\n                style: {\n                    background: '#22c55e'\n                }\n            },\n            error: {\n                style: {\n                    background: '#ef4444'\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\components\\\\ui\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Ub2FzdFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUUwQztBQUUzQixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0Qsb0RBQU9BO1FBQ05FLFVBQVM7UUFDVEMsY0FBYztZQUNaQyxVQUFVO1lBQ1ZDLE9BQU87Z0JBQ0xDLFlBQVk7Z0JBQ1pDLE9BQU87WUFDVDtZQUNBQyxTQUFTO2dCQUNQSCxPQUFPO29CQUNMQyxZQUFZO2dCQUNkO1lBQ0Y7WUFDQUcsT0FBTztnQkFDTEosT0FBTztvQkFDTEMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7Ozs7OztBQUdOIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxUb2FzdFByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUb2FzdFByb3ZpZGVyKCkge1xuICByZXR1cm4gKFxuICAgIDxUb2FzdGVyXG4gICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgZHVyYXRpb246IDMwMDAsXG4gICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJyMzNjM2MzYnLFxuICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgIH0sXG4gICAgICAgIHN1Y2Nlc3M6IHtcbiAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJyMyMmM1NWUnLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZWY0NDQ0JyxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfX1cbiAgICAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRvYXN0ZXIiLCJUb2FzdFByb3ZpZGVyIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwic3VjY2VzcyIsImVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dC9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtEO0FBR25DLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUEyQjtJQUN4RSxxQkFBTyw4REFBQ0YsNERBQWVBO2tCQUFFRTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFzdXNcXERlc2t0b3BcXFByb2plY3RzXFxNb29uZWxlY0FwcFxcbW9vbmVsZWMtYXBwXFxzcmNcXGNvbnRleHRcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiA8U2Vzc2lvblByb3ZpZGVyPntjaGlsZHJlbn08L1Nlc3Npb25Qcm92aWRlcj47XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./src/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CartProvider({ children }) {\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCartOpen, setIsCartOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Charger le panier depuis le localStorage au montage du composant\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            const storedCart = localStorage.getItem('cart');\n            const storedNotes = localStorage.getItem('cartNotes');\n            if (storedCart) {\n                try {\n                    setItems(JSON.parse(storedCart));\n                } catch (error) {\n                    console.error('Error parsing cart from localStorage:', error);\n                    setItems([]);\n                }\n            }\n            if (storedNotes) {\n                setNotes(storedNotes);\n            }\n            setIsInitialized(true);\n        }\n    }[\"CartProvider.useEffect\"], []);\n    // Sauvegarder le panier dans le localStorage à chaque modification\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CartProvider.useEffect\": ()=>{\n            if (isInitialized) {\n                localStorage.setItem('cart', JSON.stringify(items));\n                localStorage.setItem('cartNotes', notes);\n            }\n        }\n    }[\"CartProvider.useEffect\"], [\n        items,\n        notes,\n        isInitialized\n    ]);\n    // Ajouter un produit au panier\n    const addItem = (product, quantity)=>{\n        setItems((prevItems)=>{\n            // Vérifier si le produit est déjà dans le panier\n            const existingItemIndex = prevItems.findIndex((item)=>item.productId === product.id);\n            if (existingItemIndex !== -1) {\n                // Mettre à jour la quantité si le produit existe déjà\n                const updatedItems = [\n                    ...prevItems\n                ];\n                updatedItems[existingItemIndex].quantity += quantity;\n                return updatedItems;\n            } else {\n                // Ajouter un nouveau produit au panier\n                return [\n                    ...prevItems,\n                    {\n                        id: `${product.id}_${Date.now()}`,\n                        productId: product.id,\n                        name: product.name,\n                        reference: product.reference,\n                        description: product.description,\n                        image: product.mainImage,\n                        quantity,\n                        category: product.category,\n                        brand: product.brand\n                    }\n                ];\n            }\n        });\n    };\n    // Mettre à jour la quantité d'un produit dans le panier\n    const updateItemQuantity = (itemId, quantity)=>{\n        if (quantity <= 0) {\n            removeItem(itemId);\n            return;\n        }\n        setItems((prevItems)=>prevItems.map((item)=>item.id === itemId ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    // Supprimer un produit du panier\n    const removeItem = (itemId)=>{\n        setItems((prevItems)=>prevItems.filter((item)=>item.id !== itemId));\n    };\n    // Vider le panier\n    const clearCart = ()=>{\n        setItems([]);\n        setNotes('');\n    };\n    // Nombre total d'articles dans le panier\n    const itemCount = items.reduce((total, item)=>total + item.quantity, 0);\n    // Fonctions pour gérer l'ouverture et la fermeture du panneau coulissant\n    const openCart = ()=>setIsCartOpen(true);\n    const closeCart = ()=>setIsCartOpen(false);\n    const toggleCart = ()=>setIsCartOpen((prev)=>!prev);\n    // Modifier la fonction addItem pour ouvrir automatiquement le panneau\n    const handleAddItem = (product, quantity)=>{\n        addItem(product, quantity);\n        openCart(); // Ouvrir le panneau lorsqu'un produit est ajouté\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            items,\n            notes,\n            addItem: handleAddItem,\n            updateItemQuantity,\n            removeItem,\n            clearCart,\n            setNotes,\n            itemCount,\n            isCartOpen,\n            openCart,\n            closeCart,\n            toggleCart\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\CartContext.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\nfunction useCart() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/ThemeContext.tsx":
/*!**************************************!*\
  !*** ./src/context/ThemeContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [actualTheme, setActualTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setTheme(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update actual theme based on theme setting and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const updateActualTheme = {\n                \"ThemeProvider.useEffect.updateActualTheme\": ()=>{\n                    let newActualTheme;\n                    if (theme === 'system') {\n                        newActualTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    } else {\n                        newActualTheme = theme;\n                    }\n                    setActualTheme(newActualTheme);\n                    // Update document class for Tailwind dark mode\n                    const root = document.documentElement;\n                    if (newActualTheme === 'dark') {\n                        root.classList.add('dark');\n                    } else {\n                        root.classList.remove('dark');\n                    }\n                    // Update data attribute for custom CSS targeting\n                    root.setAttribute('data-theme', newActualTheme);\n                }\n            }[\"ThemeProvider.useEffect.updateActualTheme\"];\n            updateActualTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateActualTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const handleSetTheme = (newTheme)=>{\n        setTheme(newTheme);\n        if (mounted) {\n            localStorage.setItem('theme', newTheme);\n        }\n    };\n    const toggleTheme = ()=>{\n        const newTheme = actualTheme === 'light' ? 'dark' : 'light';\n        handleSetTheme(newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            actualTheme,\n            setTheme: handleSetTheme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\MoonelecApp\\\\moonelec-app\\\\src\\\\context\\\\ThemeContext.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nfunction useAuth() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const isAuthenticated = status === 'authenticated';\n    const isLoading = status === 'loading';\n    const user = session?.user;\n    const isClient = isAuthenticated && user?.role === 'CLIENT';\n    const isCommercial = isAuthenticated && user?.role === 'COMMERCIAL';\n    const isAdmin = isAuthenticated && user?.role === 'ADMIN';\n    const login = async (username, password)=>{\n        const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signIn)('credentials', {\n            username,\n            password,\n            redirect: false\n        });\n        return result;\n    };\n    const logout = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signOut)({\n            redirect: false\n        });\n        router.push('/');\n    };\n    const redirectToLogin = ()=>{\n        router.push('/auth/signin');\n    };\n    const redirectToDashboard = ()=>{\n        if (isAdmin) {\n            router.push('/admin/quotes');\n        } else if (isCommercial) {\n            router.push('/commercial/quotes');\n        } else if (isClient) {\n            router.push('/account/quotes');\n        } else {\n            router.push('/');\n        }\n    };\n    return {\n        session,\n        status,\n        user,\n        isAuthenticated,\n        isLoading,\n        isClient,\n        isCommercial,\n        isAdmin,\n        login,\n        logout,\n        redirectToLogin,\n        redirectToDashboard\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-icons","vendor-chunks/react-hot-toast","vendor-chunks/uuid","vendor-chunks/goober","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAsus%5CDesktop%5CProjects%5CMoonelecApp%5Cmoonelec-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();