@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600&display=swap');
@import "tailwindcss";
@import "./animations.css";

:root {
  /* Moonelec Brand Colors */
  --moonelec-red: #E10600;
  --moonelec-red-dark: #B30500;
  --moonelec-red-light: #FF1A0D;
  --charcoal: #1A1A1A;
  --electric-blue: #0072CE;
  --electric-blue-dark: #005BA3;
  --light-gray: #F5F8FA;
  --medium-gray: #E5E7EB;
  --dark-gray: #6B7280;

  /* Legacy compatibility */
  --background: #ffffff;
  --foreground: #1A1A1A;
  --primary: #E10600;
  --primary-light: #FF1A0D;
  --primary-dark: #B30500;
  --secondary: #0072CE;
  --secondary-light: #1A8CFF;
  --secondary-dark: #005BA3;
  --accent: #F5F8FA;
  --accent-dark: #E5E7EB;
  --text-primary: #1A1A1A;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;
  --border: #E5E7EB;
  --border-light: #F3F4F6;
  --card-bg: #ffffff;
  --card-border: #E5E7EB;
  --input-bg: #ffffff;
  --input-border: #D1D5DB;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);

  /* Typography */
  --font-heading: 'Montserrat', sans-serif;
  --font-body: 'Inter', sans-serif;

  /* Spacing (24px base grid) */
  --spacing-xs: 6px;
  --spacing-sm: 12px;
  --spacing-md: 24px;
  --spacing-lg: 48px;
  --spacing-xl: 72px;
  --spacing-2xl: 96px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Animation Durations */
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

[data-theme="dark"] {
  /* Dark mode colors */
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #0088e6;
  --primary-light: #33a3ff;
  --primary-dark: #0066b3;
  --secondary: #ff4d54;
  --secondary-light: #ff7a80;
  --secondary-dark: #e6434a;
  --accent: #1a1a1a;
  --accent-dark: #2a2a2a;
  --text-primary: #f5f5f5;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --border: #333333;
  --border-light: #404040;
  --card-bg: #1a1a1a;
  --card-border: #333333;
  --input-bg: #1a1a1a;
  --input-border: #404040;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-lg: rgba(0, 0, 0, 0.5);
}

@theme {
  /* Moonelec Brand Colors */
  --color-moonelec-red: #E10600;
  --color-moonelec-red-dark: #B30500;
  --color-moonelec-red-light: #FF1A0D;
  --color-charcoal: #1A1A1A;
  --color-electric-blue: #0072CE;
  --color-electric-blue-dark: #005BA3;
  --color-light-gray: #F5F8FA;
  --color-medium-gray: #E5E7EB;
  --color-dark-gray: #6B7280;

  /* Legacy compatibility */
  --color-primary: #E10600;
  --color-primary-light: #FF1A0D;
  --color-primary-dark: #B30500;
  --color-secondary: #0072CE;
  --color-secondary-light: #1A8CFF;
  --color-secondary-dark: #005BA3;

  /* Typography */
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Montserrat', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Base layer styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: var(--font-body);
    scroll-behavior: smooth;
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: var(--font-body);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Moonelec Typography System */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    color: var(--charcoal);
    margin: 0;
  }

  h1 {
    font-size: 3.5rem;
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
  }

  h3 {
    font-size: 2rem;
    font-weight: 600;
  }

  h4 {
    font-size: 1.5rem;
    font-weight: 600;
  }

  h5 {
    font-size: 1.25rem;
    font-weight: 600;
  }

  h6 {
    font-size: 1.125rem;
    font-weight: 600;
  }

  /* Responsive Typography */
  @media (max-width: 768px) {
    h1 {
      font-size: 2.5rem;
    }

    h2 {
      font-size: 2rem;
    }

    h3 {
      font-size: 1.75rem;
    }
  }

  /* Code elements use monospace fonts */
  code, pre, kbd, samp {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }

  /* Remove default margins */
  p, ul, ol, dl, blockquote, figure {
    margin: 0;
  }

  /* Improve link accessibility */
  a {
    color: var(--moonelec-red);
    text-decoration: none;
    transition: color var(--duration-fast) ease;
  }

  a:hover {
    color: var(--moonelec-red-dark);
  }

  a:focus {
    outline: 2px solid var(--electric-blue);
    outline-offset: 2px;
  }
}

/* Component layer styles */
@layer components {
  /* Moonelec Button System */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-family: var(--font-body);
    font-weight: 500;
    font-size: 1rem;
    line-height: 1.5;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    min-height: 44px;
    position: relative;
    overflow: hidden;
  }

  .btn:focus {
    outline: 2px solid var(--electric-blue);
    outline-offset: 2px;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Primary Button - Moonelec Red */
  .btn-primary {
    background-color: var(--moonelec-red);
    color: white;
    box-shadow: var(--shadow-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--moonelec-red-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
  }

  .btn-primary:active {
    transform: translateY(-1px);
  }

  /* Secondary Button */
  .btn-secondary {
    background-color: transparent;
    color: var(--charcoal);
    border: 2px solid var(--medium-gray);
  }

  .btn-secondary:hover:not(:disabled) {
    border-color: var(--moonelec-red);
    color: var(--moonelec-red);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  /* Electric Blue Button */
  .btn-electric {
    background-color: var(--electric-blue);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-electric:hover:not(:disabled) {
    background-color: var(--electric-blue-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
  }

  /* Utility classes for consistent theming */
  .bg-card {
    background-color: var(--card-bg);
  }

  .border-card {
    border-color: var(--card-border);
  }

  .text-primary {
    color: var(--text-primary);
  }

  .text-secondary {
    color: var(--text-secondary);
  }

  .text-muted {
    color: var(--text-muted);
  }

  .border-default {
    border-color: var(--border);
  }

  .border-light {
    border-color: var(--border-light);
  }

  .shadow-card {
    box-shadow: 0 1px 3px var(--shadow);
  }

  .shadow-card-lg {
    box-shadow: 0 4px 6px var(--shadow-lg);
  }

  /* Button styles */
  .btn-primary {
    @apply text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: var(--primary);
  }

  .btn-primary:hover {
    background-color: var(--primary-dark);
  }

  .btn-secondary {
    @apply text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: var(--secondary);
  }

  .btn-secondary:hover {
    background-color: var(--secondary-dark);
  }

  .btn-outline {
    @apply border-2 font-medium py-2 px-4 rounded-lg transition-all duration-200;
    border-color: var(--primary);
    color: var(--primary);
  }

  .btn-outline:hover {
    background-color: var(--primary);
    color: white;
  }

  /* Input styles */
  .input-default {
    @apply w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200;
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
  }

  .input-default:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 109, 183, 0.2);
  }

  [data-theme="dark"] .input-default:focus {
    box-shadow: 0 0 0 2px rgba(0, 136, 230, 0.3);
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: 0 1px 3px var(--shadow);
  }

  .card-hover {
    transition: all 0.2s ease-in-out;
  }

  .card-hover:hover {
    box-shadow: 0 4px 6px var(--shadow-lg);
    transform: translateY(-1px);
  }

  /* Typography utilities */
  .font-display {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .font-body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .font-mono {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }

  /* Enhanced text styles */
  .text-gradient {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--accent);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.3s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.3s ease-out;
}

/* Theme transition */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-in-out forwards;
}

.animate-slide-left {
  animation: slideInLeft 0.8s ease-in-out forwards;
}

.animate-slide-right {
  animation: slideInRight 0.8s ease-in-out forwards;
}

/* Additional utility styles */
@layer utilities {
  .section {
    padding: 4rem 1rem;
  }

  @media (min-width: 768px) {
    .section {
      padding: 6rem 2rem;
    }
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }
}
