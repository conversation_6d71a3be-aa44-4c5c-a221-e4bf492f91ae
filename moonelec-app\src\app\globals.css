@import "tailwindcss";
@import "./animations.css";

:root {
  /* Light mode colors */
  --background: #ffffff;
  --foreground: #171717;
  --primary: #006db7;
  --primary-light: #0088e6;
  --primary-dark: #004d85;
  --secondary: #ed1c24;
  --secondary-light: #ff4d54;
  --secondary-dark: #c41e3a;
  --accent: #f5f5f5;
  --accent-dark: #e5e5e5;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --border: #e5e5e5;
  --border-light: #f0f0f0;
  --card-bg: #ffffff;
  --card-border: #e5e5e5;
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] {
  /* Dark mode colors */
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #0088e6;
  --primary-light: #33a3ff;
  --primary-dark: #0066b3;
  --secondary: #ff4d54;
  --secondary-light: #ff7a80;
  --secondary-dark: #e6434a;
  --accent: #1a1a1a;
  --accent-dark: #2a2a2a;
  --text-primary: #f5f5f5;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --border: #333333;
  --border-light: #404040;
  --card-bg: #1a1a1a;
  --card-border: #333333;
  --input-bg: #1a1a1a;
  --input-border: #404040;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-lg: rgba(0, 0, 0, 0.5);
}

@theme {
  --color-primary: #006db7;
  --color-primary-light: #0088e6;
  --color-primary-dark: #004d85;
  --color-secondary: #ed1c24;
  --color-secondary-light: #ff4d54;
  --color-secondary-dark: #c41e3a;
  --font-sans: var(--font-inter);
  --font-display: var(--font-poppins);
  --font-mono: var(--font-jetbrains-mono);
}

/* Base layer styles */
@layer base {
  html {
    font-family: var(--font-inter), system-ui, sans-serif;
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: var(--font-inter), system-ui, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Headings use Poppins for better visual hierarchy */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-weight: 600;
  }

  /* Code elements use JetBrains Mono */
  code, pre, kbd, samp {
    font-family: var(--font-jetbrains-mono), monospace;
  }
}

/* Component layer styles */
@layer components {
  /* Utility classes for consistent theming */
  .bg-card {
    background-color: var(--card-bg);
  }

  .border-card {
    border-color: var(--card-border);
  }

  .text-primary {
    color: var(--text-primary);
  }

  .text-secondary {
    color: var(--text-secondary);
  }

  .text-muted {
    color: var(--text-muted);
  }

  .border-default {
    border-color: var(--border);
  }

  .border-light {
    border-color: var(--border-light);
  }

  .shadow-card {
    box-shadow: 0 1px 3px var(--shadow);
  }

  .shadow-card-lg {
    box-shadow: 0 4px 6px var(--shadow-lg);
  }

  /* Button styles */
  .btn-primary {
    @apply text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: var(--primary);
  }

  .btn-primary:hover {
    background-color: var(--primary-dark);
  }

  .btn-secondary {
    @apply text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: var(--secondary);
  }

  .btn-secondary:hover {
    background-color: var(--secondary-dark);
  }

  .btn-outline {
    @apply border-2 font-medium py-2 px-4 rounded-lg transition-all duration-200;
    border-color: var(--primary);
    color: var(--primary);
  }

  .btn-outline:hover {
    background-color: var(--primary);
    color: white;
  }

  /* Input styles */
  .input-default {
    @apply w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200;
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
  }

  .input-default:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 109, 183, 0.2);
  }

  [data-theme="dark"] .input-default:focus {
    box-shadow: 0 0 0 2px rgba(0, 136, 230, 0.3);
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: 0 1px 3px var(--shadow);
  }

  .card-hover {
    transition: all 0.2s ease-in-out;
  }

  .card-hover:hover {
    box-shadow: 0 4px 6px var(--shadow-lg);
    transform: translateY(-1px);
  }

  /* Typography utilities */
  .font-display {
    font-family: var(--font-poppins), system-ui, sans-serif;
  }

  .font-body {
    font-family: var(--font-inter), system-ui, sans-serif;
  }

  .font-mono {
    font-family: var(--font-jetbrains-mono), monospace;
  }

  /* Enhanced text styles */
  .text-gradient {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--accent);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.3s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.3s ease-out;
}

/* Theme transition */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-in-out forwards;
}

.animate-slide-left {
  animation: slideInLeft 0.8s ease-in-out forwards;
}

.animate-slide-right {
  animation: slideInRight 0.8s ease-in-out forwards;
}

/* Additional utility styles */
@layer utilities {
  .section {
    padding: 4rem 1rem;
  }

  @media (min-width: 768px) {
    .section {
      padding: 6rem 2rem;
    }
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }
}
