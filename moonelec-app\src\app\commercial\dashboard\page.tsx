'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaUsers, FaShoppingCart, FaChartLine, FaCalendarAlt, FaUser } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import RouteGuard from '@/components/auth/RouteGuard';

export default function CommercialDashboard() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simuler un chargement de données
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 }
    }
  };

  // Données simulées pour le tableau de bord
  const dashboardData = {
    totalClients: 48,
    newClients: 5,
    totalSales: 750000,
    monthlySales: 120000,
    pendingOrders: 12,
    upcomingMeetings: 3
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <RouteGuard allowedRoles={['COMMERCIAL']}>
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8"
        >
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Bienvenue, {user?.firstname} {user?.lastname}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Voici un aperçu de votre activité commerciale chez Moonelec
          </p>
        </motion.div>

        {/* Dashboard Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Clients totaux"
              value={dashboardData.totalClients}
              icon={<FaUsers className="text-blue-500" />}
              bgColor="bg-blue-50 dark:bg-blue-900/20"
              textColor="text-blue-700 dark:text-blue-300"
              subtext={`+${dashboardData.newClients} ce mois-ci`}
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Ventes totales"
              value={`${(dashboardData.totalSales / 1000).toFixed(0)}k MAD`}
              icon={<FaChartLine className="text-green-500" />}
              bgColor="bg-green-50 dark:bg-green-900/20"
              textColor="text-green-700 dark:text-green-300"
              subtext={`${(dashboardData.monthlySales / 1000).toFixed(0)}k MAD ce mois-ci`}
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Commandes en attente"
              value={dashboardData.pendingOrders}
              icon={<FaShoppingCart className="text-yellow-500" />}
              bgColor="bg-yellow-50 dark:bg-yellow-900/20"
              textColor="text-yellow-700 dark:text-yellow-300"
              subtext="Nécessitent votre attention"
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <DashboardCard
              title="Rendez-vous à venir"
              value={dashboardData.upcomingMeetings}
              icon={<FaCalendarAlt className="text-purple-500" />}
              bgColor="bg-purple-50 dark:bg-purple-900/20"
              textColor="text-purple-700 dark:text-purple-300"
              subtext="Cette semaine"
            />
          </motion.div>
        </motion.div>

        {/* Clients and Upcoming Meetings */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="lg:col-span-2"
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FaUsers className="mr-2" /> Clients récents
              </h2>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Client
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Entreprise
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Dernière commande
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Statut
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <FaUser className="text-gray-500 dark:text-gray-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              Ahmed Benani
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        Société ABC
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        15/07/2023
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          Actif
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <FaUser className="text-gray-500 dark:text-gray-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              Fatima Zahra
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        Entreprise XYZ
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        10/07/2023
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          En attente
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <div className="mt-4 text-right">
                <button className="text-primary hover:underline text-sm font-medium">
                  Voir tous les clients
                </button>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FaCalendarAlt className="mr-2" /> Rendez-vous à venir
              </h2>
              
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        Société ABC
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Ahmed Benani
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        18 Juil
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        10:00
                      </p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                    Présentation des nouveaux produits
                  </p>
                </div>
                
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        Entreprise XYZ
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Fatima Zahra
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        20 Juil
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        14:30
                      </p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                    Suivi de commande
                  </p>
                </div>
              </div>
              
              <div className="mt-6">
                <button className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors">
                  Planifier un rendez-vous
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </RouteGuard>
  );
}

interface DashboardCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
  textColor: string;
  subtext?: string;
}

function DashboardCard({ title, value, icon, bgColor, textColor, subtext }: DashboardCardProps) {
  return (
    <div className={`rounded-lg shadow-md overflow-hidden ${bgColor}`}>
      <div className="p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0 p-3 rounded-full bg-white dark:bg-gray-700 shadow-sm">
            {icon}
          </div>
          <div className="ml-5">
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {title}
            </p>
            <p className={`text-2xl font-semibold ${textColor}`}>
              {value}
            </p>
            {subtext && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {subtext}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
