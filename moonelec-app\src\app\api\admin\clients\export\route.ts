import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import * as XLSX from 'xlsx';
import PDFDocument from 'pdfkit';

// GET /api/admin/clients/export - Export clients data
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = req.nextUrl.searchParams;
    const format = searchParams.get('format') as 'excel' | 'csv' | 'pdf' || 'excel';
    const search = searchParams.get('search') || '';
    const city = searchParams.get('city') || '';
    const company = searchParams.get('company') || '';

    // Build where clause (same as in the main route)
    const where: any = {
      user: {
        role: 'CLIENT',
      },
    };

    if (search) {
      where.OR = [
        {
          user: {
            firstname: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            lastname: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          company: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (city) {
      where.city = {
        contains: city,
        mode: 'insensitive',
      };
    }

    if (company) {
      where.company = {
        contains: company,
        mode: 'insensitive',
      };
    }

    // Get all clients (no pagination for export)
    const clients = await prisma.client.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            firstname: true,
            lastname: true,
            email: true,
            telephone: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Prepare data for export
    const exportData = clients.map(client => ({
      'ID': client.id,
      'Prénom': client.user.firstname,
      'Nom': client.user.lastname,
      'Email': client.user.email,
      'Téléphone': client.user.telephone || '',
      'Entreprise': client.company || '',
      'Adresse': client.address || '',
      'Ville': client.city || '',
      'Code Postal': client.postalCode || '',
      'Pays': client.country || '',
      'Date d\'inscription': new Date(client.user.createdAt).toLocaleDateString('fr-FR'),
    }));

    if (format === 'excel') {
      // Create Excel file
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      
      // Set column widths
      const colWidths = [
        { wch: 10 }, // ID
        { wch: 15 }, // Prénom
        { wch: 15 }, // Nom
        { wch: 25 }, // Email
        { wch: 15 }, // Téléphone
        { wch: 20 }, // Entreprise
        { wch: 30 }, // Adresse
        { wch: 15 }, // Ville
        { wch: 10 }, // Code Postal
        { wch: 15 }, // Pays
        { wch: 15 }, // Date d'inscription
      ];
      worksheet['!cols'] = colWidths;
      
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');
      
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': 'attachment; filename=clients.xlsx',
        },
      });
    } else if (format === 'csv') {
      // Create CSV file
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');
      
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'csv' });
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=clients.csv',
        },
      });
    } else if (format === 'pdf') {
      // Create PDF file
      const doc = new PDFDocument({ margin: 50 });
      const chunks: Buffer[] = [];
      
      doc.on('data', (chunk) => chunks.push(chunk));
      
      // Add title
      doc.fontSize(20).text('Liste des Clients', { align: 'center' });
      doc.moveDown();
      
      // Add export date
      doc.fontSize(12).text(`Exporté le: ${new Date().toLocaleDateString('fr-FR')}`, { align: 'right' });
      doc.moveDown();
      
      // Add filter information if any
      if (search || city || company) {
        doc.fontSize(12).text('Filtres appliqués:');
        if (search) doc.text(`Recherche: ${search}`);
        if (city) doc.text(`Ville: ${city}`);
        if (company) doc.text(`Entreprise: ${company}`);
        doc.moveDown();
      }
      
      // Add table headers
      const tableTop = doc.y;
      const itemHeight = 20;
      
      doc.fontSize(10);
      doc.text('Nom', 50, tableTop, { width: 80 });
      doc.text('Email', 130, tableTop, { width: 120 });
      doc.text('Téléphone', 250, tableTop, { width: 80 });
      doc.text('Entreprise', 330, tableTop, { width: 100 });
      doc.text('Ville', 430, tableTop, { width: 80 });
      doc.text('Date', 510, tableTop, { width: 60 });
      
      // Add line under headers
      doc.moveTo(50, tableTop + 15)
         .lineTo(570, tableTop + 15)
         .stroke();
      
      // Add data rows
      let currentY = tableTop + itemHeight;
      
      clients.forEach((client, index) => {
        if (currentY > 750) { // Start new page if needed
          doc.addPage();
          currentY = 50;
        }
        
        const fullName = `${client.user.firstname} ${client.user.lastname}`;
        
        doc.text(fullName, 50, currentY, { width: 80 });
        doc.text(client.user.email, 130, currentY, { width: 120 });
        doc.text(client.user.telephone || '', 250, currentY, { width: 80 });
        doc.text(client.company || '', 330, currentY, { width: 100 });
        doc.text(client.city || '', 430, currentY, { width: 80 });
        doc.text(new Date(client.user.createdAt).toLocaleDateString('fr-FR'), 510, currentY, { width: 60 });
        
        currentY += itemHeight;
      });
      
      // Add footer
      doc.fontSize(8).text(
        `Total: ${clients.length} clients`,
        50,
        doc.page.height - 50,
        { align: 'center' }
      );
      
      doc.end();
      
      return new Promise<NextResponse>((resolve) => {
        doc.on('end', () => {
          const buffer = Buffer.concat(chunks);
          resolve(new NextResponse(buffer, {
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': 'attachment; filename=clients.pdf',
            },
          }));
        });
      });
    }

    return NextResponse.json({ message: 'Format non supporté' }, { status: 400 });
  } catch (error) {
    console.error('Error exporting clients:', error);
    return NextResponse.json(
      { message: 'Erreur lors de l\'exportation' },
      { status: 500 }
    );
  }
}
