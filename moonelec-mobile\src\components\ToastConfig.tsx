import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseToast, ErrorToast, InfoToast } from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';

// Custom toast configuration with French styling
export const toastConfig = {
  // Success toast
  success: (props: any) => (
    <BaseToast
      {...props}
      style={styles.successToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="checkmark-circle" size={24} color="#10b981" />
        </View>
      )}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // Error toast
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={styles.errorToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="close-circle" size={24} color="#ef4444" />
        </View>
      )}
      text1NumberOfLines={1}
      text2NumberOfLines={3}
    />
  ),

  // Info/Warning toast
  info: (props: any) => (
    <InfoToast
      {...props}
      style={styles.infoToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="information-circle" size={24} color="#3b82f6" />
        </View>
      )}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // Warning toast
  warning: (props: any) => (
    <BaseToast
      {...props}
      style={styles.warningToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      renderLeadingIcon={() => (
        <View style={styles.iconContainer}>
          <Ionicons name="warning" size={24} color="#f59e0b" />
        </View>
      )}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),

  // Loading toast
  loading: (props: any) => (
    <View style={styles.loadingToast}>
      <View style={styles.loadingContent}>
        <View style={styles.iconContainer}>
          <Ionicons name="hourglass" size={24} color="#6b7280" />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.loadingText1}>{props.text1 || 'Chargement'}</Text>
          <Text style={styles.loadingText2}>{props.text2 || 'Veuillez patienter...'}</Text>
        </View>
      </View>
    </View>
  ),

  // Custom French error toast
  frenchError: ({ text1, text2, ...props }: any) => (
    <View style={styles.customErrorToast}>
      <View style={styles.customErrorContent}>
        <View style={styles.errorIconContainer}>
          <Ionicons name="alert-circle" size={28} color="#ffffff" />
        </View>
        <View style={styles.customTextContainer}>
          <Text style={styles.customErrorTitle}>
            {text1 || 'Erreur'}
          </Text>
          <Text style={styles.customErrorMessage}>
            {text2 || 'Une erreur s\'est produite'}
          </Text>
        </View>
      </View>
    </View>
  ),

  // Custom French success toast
  frenchSuccess: ({ text1, text2, ...props }: any) => (
    <View style={styles.customSuccessToast}>
      <View style={styles.customSuccessContent}>
        <View style={styles.successIconContainer}>
          <Ionicons name="checkmark-circle" size={28} color="#ffffff" />
        </View>
        <View style={styles.customTextContainer}>
          <Text style={styles.customSuccessTitle}>
            {text1 || 'Succès'}
          </Text>
          <Text style={styles.customSuccessMessage}>
            {text2 || 'Opération réussie'}
          </Text>
        </View>
      </View>
    </View>
  ),

  // Custom French warning toast
  frenchWarning: ({ text1, text2, ...props }: any) => (
    <View style={styles.customWarningToast}>
      <View style={styles.customWarningContent}>
        <View style={styles.warningIconContainer}>
          <Ionicons name="warning" size={28} color="#ffffff" />
        </View>
        <View style={styles.customTextContainer}>
          <Text style={styles.customWarningTitle}>
            {text1 || 'Attention'}
          </Text>
          <Text style={styles.customWarningMessage}>
            {text2 || 'Veuillez faire attention'}
          </Text>
        </View>
      </View>
    </View>
  ),
};

const styles = StyleSheet.create({
  // Base styles
  contentContainer: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 15,
    marginRight: 10,
  },
  textContainer: {
    flex: 1,
    paddingRight: 15,
  },

  // Success toast
  successToast: {
    borderLeftColor: '#10b981',
    borderLeftWidth: 5,
    backgroundColor: '#f0fdf4',
    borderRadius: 8,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Error toast
  errorToast: {
    borderLeftColor: '#ef4444',
    borderLeftWidth: 5,
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Info toast
  infoToast: {
    borderLeftColor: '#3b82f6',
    borderLeftWidth: 5,
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Warning toast
  warningToast: {
    borderLeftColor: '#f59e0b',
    borderLeftWidth: 5,
    backgroundColor: '#fffbeb',
    borderRadius: 8,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Loading toast
  loadingToast: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    marginHorizontal: 20,
    paddingVertical: 15,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  // Text styles
  text1: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  text2: {
    fontSize: 14,
    fontWeight: '400',
    color: '#4b5563',
    lineHeight: 18,
  },
  loadingText1: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  loadingText2: {
    fontSize: 14,
    fontWeight: '400',
    color: '#6b7280',
  },

  // Custom toast styles
  customErrorToast: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    marginHorizontal: 20,
    paddingVertical: 16,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  customErrorContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorIconContainer: {
    marginRight: 12,
  },
  customTextContainer: {
    flex: 1,
  },
  customErrorTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 2,
  },
  customErrorMessage: {
    fontSize: 14,
    fontWeight: '400',
    color: '#fecaca',
    lineHeight: 18,
  },

  // Custom success toast
  customSuccessToast: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    marginHorizontal: 20,
    paddingVertical: 16,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  customSuccessContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  successIconContainer: {
    marginRight: 12,
  },
  customSuccessTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 2,
  },
  customSuccessMessage: {
    fontSize: 14,
    fontWeight: '400',
    color: '#a7f3d0',
    lineHeight: 18,
  },

  // Custom warning toast
  customWarningToast: {
    backgroundColor: '#f59e0b',
    borderRadius: 12,
    marginHorizontal: 20,
    paddingVertical: 16,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  customWarningContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  warningIconContainer: {
    marginRight: 12,
  },
  customWarningTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 2,
  },
  customWarningMessage: {
    fontSize: 14,
    fontWeight: '400',
    color: '#fde68a',
    lineHeight: 18,
  },
});

export default toastConfig;
