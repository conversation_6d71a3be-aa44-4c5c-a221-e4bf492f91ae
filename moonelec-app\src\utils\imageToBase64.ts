import { createCanvas, loadImage } from 'canvas';

/**
 * Convertit une image en chaîne base64
 * @param imagePath Chemin de l'image (relatif au dossier public)
 * @returns Promise avec la chaîne base64 de l'image
 */
export async function imageToBase64(imagePath: string): Promise<string> {
  try {
    // Charger l'image depuis le chemin public
    const image = await loadImage(`${process.cwd()}/public${imagePath}`);
    
    // Créer un canvas avec les dimensions de l'image
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    
    // Dessiner l'image sur le canvas
    ctx.drawImage(image, 0, 0);
    
    // Convertir le canvas en base64
    const base64 = canvas.toDataURL('image/png');
    
    return base64;
  } catch (error) {
    console.error('Erreur lors de la conversion de l\'image en base64:', error);
    throw error;
  }
}
